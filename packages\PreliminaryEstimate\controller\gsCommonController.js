const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");


class GsCommonController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取菜单栏数据
     * @param arg
     * @return {Promise<void>}
     */
    async getMenuData(arg){
        let menuData =await  this.service.PreliminaryEstimate.gsCommonService.getMenuData(arg);
        return ResponseData.success(menuData);
    }

    /**
     * 获取外层菜单栏数据
     * @param arg
     * @return {Promise<void>}
     */
    getMenuList(arg){
        return this.service.PreliminaryEstimate.gsCommonService.getMenuList(arg);
    }

    /**
     *   表格列设置查询
     * @param arg
     * @returns {*}
     */
    async getTableList(arg){
        let  result= await this.service.PreliminaryEstimate.gsCommonService.getTableList(arg)
        return ResponseData.success(result);
    }

    /**
     *   表格列设置保存
     * @param arg
     * @returns {*}
     */
    async saveTableList(arg){
        let objMap = await  this.service.PreliminaryEstimate.gsCommonService.saveTableList(arg);
        return ResponseData.success(objMap);
    }

    /**
     * 工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async getProjectSetting(arg){
        let result = await this.service.PreliminaryEstimate.gsCommonService.getProjectSetting(arg);
        return ResponseData.success(result);
    }

    /**
     * 保存工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async saveProjectSetting(arg){
        await this.service.PreliminaryEstimate.gsCommonService.saveProjectSetting(arg);
        return ResponseData.success();
    }

    /**
     * 判断项目是否有变更
     * @param arg
     * @return {Promise<string|null|*>}
     */
    async diffProject(arg) {
        return await this.service.PreliminaryEstimate.gsCommonService.diffProject(arg);
    }

    /**
     * 所有页签缓存
     * @param arg
     * @returns {*}
     */
    async getTableSettingCache(arg) {
        let result = await this.service.PreliminaryEstimate.gsCommonService.getTableSettingCache(arg)
        return ResponseData.success(result);
    }

    /**
     * 设置所有页签缓存
     * @param arg
     * @returns {*}
     */
    async setTableSettingCache(arg) {
        await this.service.PreliminaryEstimate.gsCommonService.setTableSettingCache(arg)
        return ResponseData.success(true);
    }

    /**
     * 设置定额操作缓存
     * @param arg
     * @returns {*}
     */
    async setDeSettingCache(arg) {
        await this.service.PreliminaryEstimate.gsCommonService.setDeSettingCache(arg)
        return ResponseData.success(true);
    }

    /**
     * 定额操作缓存
     * @param arg
     * @returns {*}
     */
    async getDeSettingCache(arg) {
        let result = await this.service.PreliminaryEstimate.gsCommonService.getDeSettingCache(arg)
        return ResponseData.success(result);
    }

    /**
     * 获取精度设置
     * @param arg
     * @returns {*}
     */
    async getDefaultPrecisionSetting(arg){
        let {constructId} = arg;
        let result = await this.service.PreliminaryEstimate.gsCommonService.getDefaultPrecisionSetting(constructId);
        return ResponseData.success(result.PRECISION_SETTING_UI);
    }

    /**
     * 获取精度设置
     * @param arg
     * @returns {*}
     */
    async getPrecisionSetting(arg){
        let {constructId, isAll} = arg;
        let result = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        if (isAll === true) {
            return ResponseData.success(result);
        }
        return ResponseData.success(result.PRECISION_SETTING_UI);
    }

    /**
     * 精度设置
     * @param arg
     * @returns {*}
     */
    async setPrecisionSetting(arg){
        let {constructId, precisionSetting} = arg;
        await this.service.PreliminaryEstimate.gsCommonService.setPrecisionSetting(constructId, precisionSetting);
        return ResponseData.success();
    }

    /**
     *  定额补差显示设置
     * @param arg
     * @returns {*}
     */
    async setDePriceSpread(arg){
        let {constructId, display} = arg;
        await this.service.PreliminaryEstimate.gsCommonService.setDePriceSpread(constructId, display);
        return ResponseData.success();
    }


}

GsCommonController.toString = () => '[class GsCommonController]';
module.exports = GsCommonController;
