const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const {qdCalculatorBaseFn, QDRules} = require("./QDCalculatorCodes");
const DeTypeConstants = require("../../../constants/DeTypeConstants");
const ResourceKindConstants = require("../../../constants/ResourceKindConstants");
const WildcardMap = require("../../../core/container/WildcardMap");
const LogUtil = require("../../../core/tools/logUtil");
const {ObjectUtil} = require("../../../../../common/ObjectUtil");
const {NumberUtil} = require("../../../utils/NumberUtil");
const EE = require('../../../../../core/ee');
const DeUtils = require('../../utils/DeUtils');

/**
 * 清单计算器
 */
class QDCalculator extends CalculateEngine{
    static SPLITOR = "_";
    constructId;
    deRowId;
    unitId;
    ctx;
    relateQDIds;
    precision; //精度设置
    digitPropertyMap = new Map();
    data; //动态计算时的数据
    constructor(constructId, unitId,deRowId,ctx) {
        super(ctx);
        this.ctx = ctx;
        this.constructId = constructId;
        this.unitId = unitId;
        this.deRowId = deRowId;
        this.relateQDIds = [];
        this.data = [];
        this.initDigitPropertyMap();
    }
    static  qdMap = [
        "RSum",
        "CSum",
        "JSum",
        "SSum",
        "ZSum",
        "rTotalSum",
        "cTotalSum",
        "jTotalSum",
        "sTotalSum",
        "zTotalSum",
        "RDSum",
        "CDSum",
        "JDSum",
        "ZDSum",
        "SDSum",
        "rdTotalSum",
        "cdTotalSum",
        "jdTotalSum",
        "zdTotalSum",
        "sdTotalSum",
        "price",
        "totalNumber",
        "baseJournalPrice",
        "baseJournalTotalNumber",
    ];
    initDigitPropertyMap()
    {
        this.digitPropertyMap.set("resQty",5);
        this.digitPropertyMap.set("quantity",5);
        this.digitPropertyMap.set("totalNumber",5);
        this.digitPropertyMap.set("baseJournalTotalNumber",5);
        this.digitPropertyMap.set("rTotalSum",5);
        this.digitPropertyMap.set("cTotalSum",5);
        this.digitPropertyMap.set("jTotalSum",5);
        this.digitPropertyMap.set("sTotalSum",5);
        this.digitPropertyMap.set("zTotalSum",5);
        this.digitPropertyMap.set("rdTotalSum",5);
        this.digitPropertyMap.set("cdTotalSum",5);
        this.digitPropertyMap.set("jdTotalSum",5);
        this.digitPropertyMap.set("zdTotalSum",5);
        this.digitPropertyMap.set("sdTotalSum",5);
    }
    convertValue(value,param) {
        // let paramArray = param.split(QDCalculator.SPLITOR);
        // let digits = this.digitPropertyMap.get(paramArray[0]);
        // if(ObjectUtil.isEmpty(digits)) {
        //     digits = 2;
        // }
        // return NumberUtil.numberScale(value, digits);
        return value;
    }

    static getInstance({constructId, unitId,deRowId},ctx){
        return new QDCalculator(constructId,unitId,deRowId,ctx);
    }

    render()
    {
        let qd = this.ctx.deMap.getNodeById(this.deRowId);
        if(ObjectUtils.isNotEmpty(qd) && qd.type != DeTypeConstants.DE_TYPE_DEFAULT && qd.type === DeTypeConstants.DE_TYPE_DELIST)
        {
            for (let key of QDCalculator.qdMap) {
                let digits = DeUtils.getDePrecision(key, this.precision);
                if(ObjectUtil.isEmpty(digits)) {
                    digits = 2;
                }
                let columnKey = key + "_" + qd.sequenceNbr;
                qd[key] = NumberUtil.numberScale(this.parser(columnKey),digits);
                this.instanceMap[columnKey] = qd[key];
                LogUtil.renderLogger("QDCalculator :" + qd.sequenceNbr + "-----------key :" + key + "-----------value :" + qd[key]);
            }
        }
        qd.updateDate = Date.now();
    }

    async prepare() {
        const {service} = EE.app;
        this.precision = service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(this.constructId);
    }

    async analyze() {
        await this.prepare();
        this.preload(qdCalculatorBaseFn);
        this.buildRules();
        await this.render()
    }

    getValue({type,kind,column})
    {
        let currentDe = this.ctx.deMap.getNodeById(this.deRowId);
        let value;
        switch (type) {
            case `parent`:{
                if (typeof column == 'function') {
                    value = column({ de: currentDe});
                } else {
                    value = currentDe[column];
                }
                let digital = DeUtils.getDePrecision(column, this.precision);
                if(ObjectUtil.isNotEmpty(digital)) {
                    value = NumberUtil.numberFormat(value, digital);
                }
                break;
            }

            default:{
                value = {type,kind,column};
                break;
            }
        }
        return value;
    }
    buildRules() {
        let rules = {};
        let currentDe = this.ctx.deMap.getNodeById(this.deRowId);
        this.data.push(currentDe);
        if(ObjectUtils.isNotEmpty(currentDe) && currentDe.type === DeTypeConstants.DE_TYPE_DELIST) {
            //03定额有可能有人材机
            this.buildChildDeRcjRules(rules, currentDe);
            this.loadRules(rules);
        }
    }
    buildChildDeRcjRules(rules,currentDe) {

        // let priceRules = "0";//单价
        // let baseJournalPriceRules = "0";//基数单价
        let rSum = "0";//人 基数 单价
        let rTotalSum = "0";//人 基数 合价
        let cSum = "0";//材 基数 单价
        let cTotalSum = "0";//材 基数 合价
        let jSum = "0";//机 基数 单价
        let jTotalSum = "0";//机 基数 合价
        let sSum = "0";//机 基数 单价
        let sTotalSum = "0";//机 基数 合价
        let zSum = "0";//机 基数 单价
        let zTotalSum = "0";//机 基数 合价
        let rdSum = "0";//人 定额价 合价
        let cdSum = "0";//材 定额价 合价
        let jdSum = "0";//机 定额价 合价
        let zdSum = "0";//机 定额价 合价
        let sdSum = "0";//机 定额价 合价

        let rdTotalSum = "0";//人 基数 合价
        let cdTotalSum = "0";//材 基数 合价
        let jdTotalSum = "0";//机 基数 合价
        let zdTotalSum = "0";//机 基数 合价
        let sdTotalSum = "0";//机 基数 合价

        let subResources = this.ctx.resourceMap.getValues(WildcardMap.generateKey(this.unitId,this.deRowId) + WildcardMap.WILDCARD);
        for(let subResource of subResources){ 
            this.data.push(subResource);
            rules[this.getTotalNumberKey(subResource.sequenceNbr)] = QDRules['totalNumber'].mathFormula;
            rules[this.getMarketPriceKey(subResource.sequenceNbr)] = QDRules['marketPrice'].mathFormula;
            rules[this.getDePriceKey(subResource.sequenceNbr)] = QDRules['dePrice'].mathFormula;
            // rules[this.getPriceKey(subResource.sequenceNbr)] = QDRules['price'].mathFormula;
            rules[this.getResQtyKey(subResource.sequenceNbr)] = QDRules['resQty'].mathFormula;
            if(subResource.kind != ResourceKindConstants.INT_TYPE_ZC && subResource.kind != ResourceKindConstants.INT_TYPE_SB){
                // priceRules += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                // baseJournalPriceRules += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
            }
            if (subResource.kind == ResourceKindConstants.INT_TYPE_ZC){
                zSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                zTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                zdTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
                zdSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
            // break;
            }
            if (subResource.kind == ResourceKindConstants.INT_TYPE_SB){
                sSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                sTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                sdTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
                sdSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);

            // break;
            }
            if (subResource.kind == ResourceKindConstants.INT_TYPE_R){
                rSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                rTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                rdTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
                rdSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
            // break;
            }//无语
            if (subResource.kind == ResourceKindConstants.INT_TYPE_C || subResource.kind == 6 || subResource.kind == 7 || subResource.kind == 8 || subResource.kind == 9 || subResource.kind == 10) {
                cSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                cTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                cdTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
                cdSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
            // break;
            }
            if (subResource.kind == ResourceKindConstants.INT_TYPE_J) {
                jSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                jTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getMarketPriceKey(subResource.sequenceNbr);
                jdTotalSum += "+" + this.getTotalNumberKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
                jdSum += "+" + this.getResQtyKey(subResource.sequenceNbr) + "*" + this.getDePriceKey(subResource.sequenceNbr);
            // break;
            }
        }
        for (let subDe of currentDe?.children)
        {
            if(subDe.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtils.isNotEmpty(subDe.calculateMethod)){
                continue;
            }
            this.data.push(subDe);
            rules[this.getQuantity(subDe.sequenceNbr)] = QDRules['quantity'].mathFormula;
            rules[this.getMarketPriceKey(subDe.sequenceNbr)] = QDRules['marketPrice'].mathFormula;
            rules[this.getPriceKey(subDe.sequenceNbr)] = QDRules['price'].mathFormula;
            rules[this.getBaseJournalPriceKey(subDe.sequenceNbr)] = QDRules['baseJournalPrice'].mathFormula;
            rules[this.getResQtyKey(subDe.sequenceNbr)] = QDRules['resQty'].mathFormula;
            if((subDe.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || subDe.deResourceKind == ResourceKindConstants.INT_TYPE_SB) 
                && (subDe.type === DeTypeConstants.DE_TYPE_RESOURCE || subDe.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)){
                    //子定额位主材或设备不计算单价及汇总人材机
                 rules[this.getSSum(subDe.sequenceNbr)] = QDRules['SSum'].mathFormula;
                 rules[this.getZSum(subDe.sequenceNbr)] = QDRules['ZSum'].mathFormula;
                 sSum += "+" + this.getSSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 zSum += "+" + this.getZSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 zdSum += "+" + this.getSSum(subDe.sequenceNbr)+"*"+this.getDePriceKey(subDe.sequenceNbr);
                 sdSum += "+" + this.getZSum(subDe.sequenceNbr)+"*"+this.getDePriceKey(subDe.sequenceNbr);
                 rules[this.getSTotalSum(subDe.sequenceNbr)] = QDRules['sTotalSum'].mathFormula;
                 rules[this.getZTotalSum(subDe.sequenceNbr)] = QDRules['zTotalSum'].mathFormula;
                 rules[this.getSdTotalSum(subDe.sequenceNbr)] = QDRules['sdTotalSum'].mathFormula;
                 rules[this.getZdTotalSum(subDe.sequenceNbr)] = QDRules['zdTotalSum'].mathFormula;
                 sTotalSum += "+" + this.getSTotalSum(subDe.sequenceNbr);
                 zTotalSum += "+" + this.getZTotalSum(subDe.sequenceNbr);
                 sdTotalSum += "+" + this.getSdTotalSum(subDe.sequenceNbr);
                 zdTotalSum += "+" + this.getZdTotalSum(subDe.sequenceNbr);
            }else{

                // priceRules += "+" + this.getResQtyKey(subDe.sequenceNbr) + "*" + this.getPriceKey(subDe.sequenceNbr);
                // baseJournalPriceRules += "+" + this.getResQtyKey(subDe.sequenceNbr) + "*" + this.getBaseJournalPriceKey(subDe.sequenceNbr);

                 rules[this.getRSum(subDe.sequenceNbr)] = QDRules['RSum'].mathFormula;
                 rules[this.getJSum(subDe.sequenceNbr)] = QDRules['JSum'].mathFormula;
                 rules[this.getCSum(subDe.sequenceNbr)] = QDRules['CSum'].mathFormula;
                 rules[this.getSSum(subDe.sequenceNbr)] = QDRules['SSum'].mathFormula;
                 rules[this.getZSum(subDe.sequenceNbr)] = QDRules['ZSum'].mathFormula;
                 
                 rules[this.getRdSum(subDe.sequenceNbr)] = QDRules['RDSum'].mathFormula;
                 rules[this.getJdSum(subDe.sequenceNbr)] = QDRules['JDSum'].mathFormula;
                 rules[this.getCdSum(subDe.sequenceNbr)] = QDRules['CDSum'].mathFormula;
                 rules[this.getSdSum(subDe.sequenceNbr)] = QDRules['SDSum'].mathFormula;
                 rules[this.getZdSum(subDe.sequenceNbr)] = QDRules['ZDSum'].mathFormula;

                 rSum += "+" + this.getRSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 cSum += "+" + this.getCSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 jSum += "+" + this.getJSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 sSum += "+" + this.getSSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 zSum += "+" + this.getZSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);

                 rdSum += "+" + this.getRdSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 cdSum += "+" + this.getCdSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 jdSum += "+" + this.getJdSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 sdSum += "+" + this.getSdSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);
                 zdSum += "+" + this.getZdSum(subDe.sequenceNbr)+"*"+this.getResQtyKey(subDe.sequenceNbr);

                 rules[this.getRTotalSum(subDe.sequenceNbr)] = QDRules['rTotalSum'].mathFormula;
                 rules[this.getCTotalSum(subDe.sequenceNbr)] = QDRules['cTotalSum'].mathFormula;
                 rules[this.getJTotalSum(subDe.sequenceNbr)] = QDRules['jTotalSum'].mathFormula;
                 rules[this.getSTotalSum(subDe.sequenceNbr)] = QDRules['sTotalSum'].mathFormula;
                 rules[this.getZTotalSum(subDe.sequenceNbr)] = QDRules['zTotalSum'].mathFormula;

                 rules[this.getRdTotalSum(subDe.sequenceNbr)] = QDRules['rdTotalSum'].mathFormula;
                 rules[this.getCdTotalSum(subDe.sequenceNbr)] = QDRules['cdTotalSum'].mathFormula;
                 rules[this.getJdTotalSum(subDe.sequenceNbr)] = QDRules['jdTotalSum'].mathFormula;
                 rules[this.getZdTotalSum(subDe.sequenceNbr)] = QDRules['zdTotalSum'].mathFormula;
                 rules[this.getSdTotalSum(subDe.sequenceNbr)] = QDRules['sdTotalSum'].mathFormula;

                 rTotalSum += "+" + this.getRTotalSum(subDe.sequenceNbr);
                 cTotalSum += "+" + this.getCTotalSum(subDe.sequenceNbr);
                 jTotalSum += "+" + this.getJTotalSum(subDe.sequenceNbr);
                 sTotalSum += "+" + this.getSTotalSum(subDe.sequenceNbr);
                 zTotalSum += "+" + this.getZTotalSum(subDe.sequenceNbr);

                 rdTotalSum += "+" + this.getRdTotalSum(subDe.sequenceNbr);
                 cdTotalSum += "+" + this.getCdTotalSum(subDe.sequenceNbr);
                 jdTotalSum += "+" + this.getJdTotalSum(subDe.sequenceNbr);
                 sdTotalSum += "+" + this.getSdTotalSum(subDe.sequenceNbr);
                 zdTotalSum += "+" + this.getZdTotalSum(subDe.sequenceNbr);
             }

        }
        //单价计算公式==合价/工程量
        rules[this.getRSum(currentDe.sequenceNbr)]  = rSum;//当前定额下人的基数汇总  
        rules[this.getCSum(currentDe.sequenceNbr)]  = cSum;//当前定额下材的基数汇总
        rules[this.getJSum(currentDe.sequenceNbr)]  = jSum;//当前定额下机的基数汇总  
        rules[this.getSSum(currentDe.sequenceNbr)]  = sSum;//当前定额下材的基数汇总
        rules[this.getZSum(currentDe.sequenceNbr)]  = zSum;//当前定额下机的基数汇总

        rules[this.getRTotalSum(currentDe.sequenceNbr)]  = rTotalSum;//当前定额下人的基数汇总
        rules[this.getCTotalSum(currentDe.sequenceNbr)]  = cTotalSum;//当前定额下材的基数汇总
        rules[this.getJTotalSum(currentDe.sequenceNbr)]  = jTotalSum;//当前定额下机的基数汇总
        rules[this.getSTotalSum(currentDe.sequenceNbr)]  = sTotalSum;//当前定额下材的基数汇总
        rules[this.getZTotalSum(currentDe.sequenceNbr)]  = zTotalSum;//当前定额下机的基数汇总

        rules[this.getRdSum(currentDe.sequenceNbr)]  = rdSum;//当前定额下人的基数 定额价汇总
        rules[this.getCdSum(currentDe.sequenceNbr)]  = cdSum;//当前定额下材的基数 定额价汇总
        rules[this.getJdSum(currentDe.sequenceNbr)]  = jdSum;//当前定额下机的基数 定额价汇总
        rules[this.getSdSum(currentDe.sequenceNbr)]  = sdSum;//当前定额下机的基数 定额价汇总
        rules[this.getZdSum(currentDe.sequenceNbr)]  = zdSum;//当前定额下机的基数 定额价汇总

        rules[this.getRdTotalSum(currentDe.sequenceNbr)]  = rdTotalSum;//当前定额下人的基数 定额价汇总
        rules[this.getCdTotalSum(currentDe.sequenceNbr)]  = cdTotalSum;//当前定额下材的基数 定额价汇总
        rules[this.getJdTotalSum(currentDe.sequenceNbr)]  = jdTotalSum;//当前定额下机的基数 定额价汇总
        rules[this.getSdTotalSum(currentDe.sequenceNbr)]  = sdTotalSum;//当前定额下机的基数 定额价汇总
        rules[this.getZdTotalSum(currentDe.sequenceNbr)]  = zdTotalSum;//当前定额下机的基数 定额价汇总
        //单价 如果有过单价调整 则直接取当前单价
        // if(ObjectUtils.isNotEmpty(currentDe.resourceTZ))
        // {
        //     rules[this.getPriceKey(currentDe.sequenceNbr)] = QDRules['price'].mathFormula;
        // }
        // else
        // {
            rules[this.getPriceKey(currentDe.sequenceNbr)] = this.getRSum(currentDe.sequenceNbr) +'+'+this.getCSum(currentDe.sequenceNbr)+'+'+this.getJSum(currentDe.sequenceNbr);
            rules[this.getBaseJournalPriceKey(currentDe.sequenceNbr)] = this.getRdSum(currentDe.sequenceNbr) +'+'+this.getCdSum(currentDe.sequenceNbr)+'+'+this.getJdSum(currentDe.sequenceNbr);
        // }
        rules[this.getQuantity(currentDe.sequenceNbr)] = QDRules['quantity'].mathFormula;

        //合价(当前定额的工程量 * 当前定额的单价)
        rules[this.getTotalNumberKey(currentDe.sequenceNbr)] = this.getPriceKey(currentDe.sequenceNbr)+"*"+this.getQuantity(currentDe.sequenceNbr);
        rules[this.getBaseJournalTotalNumberKey(currentDe.sequenceNbr)] = this.getBaseJournalPriceKey(currentDe.sequenceNbr)+"*"+this.getQuantity(currentDe.sequenceNbr);
    }

    getRuntimeValue({type,kind,column},param) {
        let value = 0;
        let key = param.split(QDCalculator.SPLITOR)[1];
        let item = this.data.find(item => item.sequenceNbr === key);
        switch (type) {
            case `item`: {
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
            }
        }
        let columnKey = param.split(QDCalculator.SPLITOR)[0];
        let digital = DeUtils.getDePrecision(columnKey, this.precision);
        if(ObjectUtil.isNotEmpty(digital)) {
            value = NumberUtil.numberFormat(value, digital);
        }
        return value;
    }
    getRdTotalSum = (sequenceNbr) => {
        return "rdTotalSum_" + sequenceNbr;
    }
    getCdTotalSum = (sequenceNbr) => {
        return "cdTotalSum_" + sequenceNbr;
    }
    getJdTotalSum = (sequenceNbr) => {
        return "jdTotalSum_" + sequenceNbr;
    }

    getDePriceKey = (sequenceNbr) => {
        return "dePrice_" + sequenceNbr;
    }
    getRTotalSum = (sequenceNbr) => {
        return "rTotalSum_" + sequenceNbr;
    }
    getCTotalSum = (sequenceNbr) => {
        return "cTotalSum_" + sequenceNbr;
    }
    getJTotalSum = (sequenceNbr) => {
        return "jTotalSum_" + sequenceNbr;
    }
    getSTotalSum = (sequenceNbr) => {
        return "sTotalSum_" + sequenceNbr;
    }
    getZTotalSum = (sequenceNbr) => {
        return "zTotalSum_" + sequenceNbr;
    }
    getRSum = (sequenceNbr) => {
        return "RSum_" + sequenceNbr;
    }
    getJSum = (sequenceNbr) => {
        return "JSum_" + sequenceNbr;
    }
    getCSum = (sequenceNbr) => {
        return "CSum_" + sequenceNbr;
    }
    getSSum = (sequenceNbr) => {
        return "SSum_" + sequenceNbr;
    }
    getZSum = (sequenceNbr) => {
        return "ZSum_" + sequenceNbr;
    }
    getResQtyKey = (sequenceNbr) => {
        return "resQty_" + sequenceNbr;
    }

    getTotalNumberKey = (sequenceNbr) => {
        return "totalNumber_" + sequenceNbr;
    }

    getPriceKey = (sequenceNbr) => {
        return "price_" + sequenceNbr;
    }

    getQuantity = (sequenceNbr) => {
        return "quantity_" + sequenceNbr;
    }

    getMarketPriceKey = (sequenceNbr) => {
        return "marketPrice_" + sequenceNbr;
    }
    getBaseJournalPriceKey = (sequenceNbr) => {
        return "baseJournalPrice_" + sequenceNbr;
    }
    getBaseJournalTotalNumberKey = (sequenceNbr) => {
        return "baseJournalTotalNumber_" + sequenceNbr;
    }
    getSdTotalSum = (sequenceNbr) => {
        return "sdTotalSum_" + sequenceNbr;
    }
    getZdTotalSum = (sequenceNbr) => {
        return "zdTotalSum_" + sequenceNbr;
    }
    getRdSum = (sequenceNbr) => {
        return "RDSum_" + sequenceNbr;
    }
    getCdSum = (sequenceNbr) => {
        return "CDSum_" + sequenceNbr;
    }
    getJdSum = (sequenceNbr) => {
        return "JDSum_" + sequenceNbr;
    }
    getSdSum = (sequenceNbr) => {
        return "SDSum_" + sequenceNbr;
    }
    getZdSum = (sequenceNbr) => {
        return "ZDSum_" + sequenceNbr;
    }
}
module.exports = {QDCalculator}
