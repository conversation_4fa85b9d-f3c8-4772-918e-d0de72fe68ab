class CostDeMatchConstants {

  static NON_COST_DE = 0;
  static AWF_DE = 1; //安文费
  static ZJCS_DE = 2;//总价措施
  static CG_DE = 3;//超高
  static DS_CY_DE = 4;//地上垂运
  static DX_CY_DE = 6;//地下垂运
  static AZ_DE = 5;//安装费

  static FXTJ_CG = 11;//房修土建-超高
  static FXTJ_CZYS = 12;//房修土建-垂直运输
  static FXTJ_ZXXJX = 13;//房修土建-中小型机械
  static FXTJ_GCSDF = 14;//房修土建-工程水电费

  
  static GJMQ_DE_CG = 21;//古建明清-超高
  static GJMQ_DE_CZYS = 22;//古建明清-垂直运输
  static GJMQ_DE_ZXXJX = 23;//古建明清-中小型机械

  //
  static FGJZ_DE_ZXXJX = 33;//仿古建筑-中小型机械

  // 垂直运输费地上、地下 工程量表达式
  static DSZSGR = 'DSZSGR';
  static DXZSGR = 'DXZSGR';
  // 超高费工程量表达式
  static CGRGHJ = 'CGGRHJ';

  static UNIT_RENGONG = '工日';

  static SPECIAL_RCJ = ['QTCLFBFB', '34000001-2', 'J00004', 'J00031', 'J00031', 'C11384', 'C00007', 'C000200', 'C11408', 'C11388', 'J00006', 'J00008'];

  // 措施人材机 RGFBFB1/ RGFBFB2/ RGFBFB3/ CLFBFB1/ CLFBFB2/ CLFBFB3/ JXFBFB1/ JXFBFB2/ JXFBFB3
  static CS_RCJ = ['RGFBFB1', 'RGFBFB2', 'RGFBFB3', 'CLFBFB1', 'CLFBFB2', 'CLFBFB3', 'JXFBFB1', 'JXFBFB2', 'JXFBFB3'];


  // 泵车
  static BSHNTL_BC = 'BSHNTL_BC';

  // 输送泵
  static BSHNTL_40 = 'BSHNTL_40';
  static BSHNTL_60 = 'BSHNTL_60';
  static BSHNTL_80 = 'BSHNTL_80';
  static BSHNTL_100 = 'BSHNTL_100';
  static BSHNTL_120 = 'BSHNTL_120';
  static BSHNTL_140 = 'BSHNTL_140';
  static BSHNTL_160 = 'BSHNTL_160';
  static BSHNTL_180 = 'BSHNTL_180';
  static BSHNTL_200 = 'BSHNTL_200';

  static BSHNTL_LIST = [
    CostDeMatchConstants.BSHNTL_BC,
    CostDeMatchConstants.BSHNTL_40,
    CostDeMatchConstants.BSHNTL_60,
    CostDeMatchConstants.BSHNTL_80,
    CostDeMatchConstants.BSHNTL_100,
    CostDeMatchConstants.BSHNTL_120,
    CostDeMatchConstants.BSHNTL_140,
    CostDeMatchConstants.BSHNTL_160,
    CostDeMatchConstants.BSHNTL_180,
    CostDeMatchConstants.BSHNTL_200
  ];

  // 安装工程
  static DEK_ANZHUANG_2022 = '2022-AZGC-DEK';

  // 房屋修缮
  static DEK_ANZHUANG_2022_FWXS_AZ = '2023-FWXS-DEX-AZ';
  static DEK_ANZHUANG_2022_FWXS_TJ = '2023-FWXS-DEX-TJ';

  // 对应分部
  static DYFBFX = 1;
  // 指定措施
  static ZJCS = 2;
  // 指定分部
  static FBFX = 3;


  static TITLE_WITH_MARJOR_PROJECT = '随主工程';


  // 人材机 其他机械 总价措施记取使用
  static OTHER_JX = ['J00004', 'J00031', 'J00006', 'J00008'];

  /**
   * 初始化默认值
   */
  static calculateBaseInitValue = 'RGF_DEJ+JXF_DEJ';


  /**
   * 计取地区(其他)
   */
  static calculateBaseAreaOther = 'other';
  static calculateBaseAreaOtherName = '(其他地区)';
  /**
   * 计取地区(张承)
   */
  static calculateBaseAreaZhangCheng = 'zhangcheng';
  static calculateBaseAreaZhangChengName = '(张承地区)';


  /**
   * 总价措施计取判断
   */
  static dlqlwxgc = '道路桥梁维修工程';
  static pssswxyhgc = '排水设施维修养护工程';
  static ldwxgc = '路灯维修工程';

  /**
   * 总价措施计取基数枚举值
   */
  static RGFDEJ_JXFDEJ = 'RGF_DEJ+JXF_DEJ';
  static RGFDEJ_JXFDEJ_LIST = ['RGF_DEJ', 'JXF_DEJ'];
  static RGFSCJ_JXFSCJ = 'RGF+JXF';
  static RGFSCJ_JXFSCJ_LIST = ['RGF', 'JXF'];


  // 古建明清-超高费
  static GJMQ_CG = 520;
  // 古建明清-垂直运输费
  static GJMQ_CZYS = 530;
  // 古建明清-中小型机械使用费
  static GJMQ_ZXXJX = 510;
  // 仿古-中小型机械使用费
  static FGJZ_ZXXJX = 410;

  // 超高费
  static CG = 310;
  // 垂直运输费
  static CZYS = 320;
  // 中小型机械使用费
  static ZXXJX = 330;
  // 工程水电费
  static GCSDF = 340;

  static LIBRARY_CODE_22 = '2023-FWXS-DEX-TJ';
  static LIBRARY_CODE_12 = '2013-FWXS-DEX-TJ';
  
  static LIBRARY_CODE_GJMQ_25 = '2024-GJXSGC-DEG';
  static CLASS_LEVEL2_ARR_GJMQ_25 = ['第一章', '第二章', '第三章', '第四章', '第五章', '第六章', '第七章', '第八章', '第九章', '第十章',
    '第十一章', '第十二章'];
  static LIBRARY_CODE_FGJZ_25 = '2024-FGJZ-DEG';
  static CLASS_LEVEL2_ARR_FGJZ_25 = ['第一章', '第二章', '第三章', '第四章', '第五章', '第六章', '第七章', '第八章', '第九章', '第十章',
    '第十一章', '第十二章', '第十三章', '第十四章', '第十五章', '第十六章', '第十七章'];


  static CLASS_LEVEL2_ARR_22 = ['第一章', '第二章', '第三章', '第四章', '第五章', '第六章', '第七章', '第八章', '第九章', '第十章',
    '第十一章', '第十二章', '第十三章', '第十四章', '第十五章', '第十六章', '第十七章'];

  static CLASS_LEVEL2_ARR_12 = ['第一章', '第二章', '第三章', '第四章', '第五章', '第六章', '第七章', '第八章', '第九章', '第十章',
    '第十一章', '第十二章', '第十三章', '第十四章', '第十五章', '第十七章', '第十八章', '第十九章'];
}

CostDeMatchConstants.toString = () => 'CostDeMatchConstants';
module.exports = CostDeMatchConstants;