const { ObjectUtils } = require('../../utils/ObjectUtils');
const ResourceKindConstants = require("../../constants/ResourceKindConstants");
const DeTypeConstants = require("../../constants/DeTypeConstants");
const CommonConstants = require("../../constants/CommonConstants");

class DeTypeCheckUtil {
  checkAllDeType(deRow,ctx){
    this._updateChildDeType(deRow,ctx);
    this.updateParentDeType(deRow,ctx);
  }
  _updateChildDeType(deRow,ctx){
    
    if(ObjectUtils.isNotEmpty(deRow.children)){
      for(let child of deRow?.children){
        this.checkAndUpdateDeType(child,ctx, false);
        this._updateChildDeType(child,ctx);
      }
    }
    
    this.checkAndUpdateDeType(deRow,ctx, false);
  }
  checkAndUpdateDeType(deRow,ctx, isTraceTop=true){
        
      // 自我检查 子级完整度及消耗量变化
      let isRcj = true;
      let childs = ctx.resourceMap.findByValueProperty('deRowId',deRow.sequenceNbr);
      //过滤主材
      if(ObjectUtils.isNotEmpty(childs) && childs.length>0){
        childs = childs.filter(item=>{
          return item.value.kind != ResourceKindConstants.INT_TYPE_ZC 
          && item.value.kind != ResourceKindConstants.INT_TYPE_SB
          && item.value.isDeCompensation !== CommonConstants.COMMON_YES;
        });
      }

      if(deRow.type === DeTypeConstants.DE_TYPE_DELIST){
        childs = childs.concat(deRow.children);
        isRcj = false;
      }
      let checkChildOk = this._checkChildWholeAndResQtyChange(childs,deRow.initChildCodes,isRcj);
      
      if(!checkChildOk && 
        (deRow.displayType === DeTypeConstants.DE_TYPE_JIE_LABEL 
          || deRow.displayType === DeTypeConstants.DE_TYPE_DE_LABEL)){
            if(deRow.resQty !== deRow.initResQty){
              isTraceTop?this.updateParentDeType(deRow,ctx):null;
            }
            return;
      }
      if(checkChildOk && 
        (deRow.displayType === DeTypeConstants.DE_TYPE_JIEHUAN_LABEL 
          || deRow.displayType === DeTypeConstants.DE_TYPE_HUAN_LABEL)){
          return;
      }
      
      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE 
        || deRow.type === DeTypeConstants.DE_TYPE_USER_DE
        || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
        || this._changeDisplayType(deRow)){
          isTraceTop?this.updateParentDeType(deRow,ctx):null;
      }
  }
  updateParentDeType(deRow,ctx){
        
    //向上检查
    let parent = ctx.deMap.getNodeById(deRow.parentId);
    if(ObjectUtils.isEmpty(parent)){
      return ;
    }
    if(parent.type === DeTypeConstants.DE_TYPE_DELIST){
       let rcjAddCheck = false;
      // 自我检查  包含了人材机调整
      let childs = ctx.resourceMap.findByValueProperty('deRowId',parent.sequenceNbr);
      if(ObjectUtils.isNotEmpty(childs) && childs.length>0){//处理父级
        let nonTcRcjs = childs.filter(item=>item.value.isDeCompensation !== CommonConstants.COMMON_YES);
        if(ObjectUtils.isNotEmpty(nonTcRcjs) && nonTcRcjs.length>0){
          rcjAddCheck = true;
        }
      }
      if(rcjAddCheck && 
        (parent.displayType === DeTypeConstants.DE_TYPE_JIEHUAN_LABEL 
          || parent.displayType === DeTypeConstants.DE_TYPE_HUAN_LABEL)){
          return;
      }
      //处理本身      
      let checkChildOk = this._checkChildWholeAndResQtyChange(parent.children,parent.initChildCodes,false);
      if(checkChildOk && 
        (parent.displayType === DeTypeConstants.DE_TYPE_JIEHUAN_LABEL 
          || parent.displayType === DeTypeConstants.DE_TYPE_HUAN_LABEL)){
          return;
      }
      
      if(!checkChildOk && !rcjAddCheck &&
        (parent.displayType === DeTypeConstants.DE_TYPE_JIE_LABEL 
          || parent.displayType === DeTypeConstants.DE_TYPE_DE_LABEL)){
            return;
      }
      //处理父级
      if(this._changeDisplayType(parent)){
        this.updateParentDeType(parent, ctx);
      }
    }

  }
  _checkChildWholeAndResQtyChange(childs, initChildCodes,isRcj){
    let checkChildOk = false;
    if(ObjectUtils.isEmpty(childs)){
      childs = [];
    }

    if(ObjectUtils.isEmpty(initChildCodes)){
      initChildCodes = [];
    }
    let pbIds = new Set();
    if(isRcj){
      let pbs = [];
      for(let item of childs){
        if(item.value.kind != ResourceKindConstants.INT_TYPE_ZC 
          && item.value.kind != ResourceKindConstants.INT_TYPE_SB
          && item.value.isDeCompensation != CommonConstants.COMMON_YES){
            pbs.push(item.value);
            if(ObjectUtils.isNotEmpty(item.value.pbs)){
              for(let pbs of item.value.pbs){
                pbIds.add(pbs.sequenceNbr);
              }
            }
        }
      }
      childs = pbs;
    }
    if(childs.length !== initChildCodes.length){
      checkChildOk = true;
    }else{
      let codeMap  = new Map();
      for(let itemCode of initChildCodes){
        let ccode = this._fixCode(itemCode);
        if(codeMap.has(ccode)){
          let ccount = codeMap.get(ccode);
          codeMap.set(ccode,ccount++);
        }else{
          codeMap.set(ccode,1);
        }
      }
      initChildCodes.forEach(itemCodes=>{
        let itemCodeArrays = itemCodes.split(",")
        let itemCode = itemCodeArrays[0];
        let items = childs.filter(itemChild=> this._fixCode(itemCode) ===  this._fixCode(isRcj?itemChild.materialCode:itemChild.deCode));
        
        if(ObjectUtils.isEmpty(items)){
          checkChildOk = true;
        }        
        let item = items[0];
        if(items.length > 1){
          let ccount = codeMap.get(this._fixCode(itemCode));
          if(ccount != items.length){
            checkChildOk = true;
          }
        }
        if(!checkChildOk && isRcj && ObjectUtils.isNotEmpty(item.pbs)){
          if((itemCodeArrays.length-1) != item.pbs.length){
            checkChildOk = true;
          }else{
            for(let i=1;i<itemCodeArrays.length;i++){
              let itemCodeAtt = itemCodeArrays[i];
              let itemAtt = item.pbs.find(itemPbs =>  this._fixCode(itemCodeAtt) === this._fixCode(itemPbs.materialCode));
              if(ObjectUtils.isEmpty(itemAtt)){
                checkChildOk = true;
              }else{
                //pbs 没有changeResQty
                // let compareResQty = itemAtt.isTempRemove === CommonConstants.COMMON_YES?itemAtt.changeResQty:itemAtt.resQty;
                let compareResQty = itemAtt.resQty;
                if(compareResQty != itemAtt.originalQty){
                  checkChildOk = true;          
                }

              }
            }
          }
        }
        if(!checkChildOk){
          if(isRcj){
            //存在字符串与数字差异，此处两个=判断
            if(item.isTempRemove === CommonConstants.COMMON_NO && item.resQty != item.originalQty){
              checkChildOk = true;          
            }
            let compareResQty = pbIds.has(item.sequenceNbr)?item.resQty:item.changeResQty;
            if(item.isTempRemove === CommonConstants.COMMON_YES &&  compareResQty!= item.originalQty){
              checkChildOk = true;          
            }
          }else{
            //存在字符串与数字差异，此处两个=判断
            if(item.resQty != item.initResQty){
              checkChildOk = true;          
            }
            if(item.displayType === DeTypeConstants.DE_TYPE_HUAN_LABEL
              ||item.displayType === DeTypeConstants.DE_TYPE_JIEHUAN_LABEL){
              checkChildOk = true;          
            }
          }
        }
      })
    }
    return checkChildOk;
  }
  _changeDisplayType(de){
    let change = false;
    switch (de.displayType) {
      case '定':
          de.displayType = '换';
          change = true;
          break;
      case '借':
          de.displayType = '借换';
          change = true;
          break;
      case '借换':
          de.displayType = '借';
          change = true;
          break;
      case '换':
          de.displayType = '定';
          change = true;
          break;
      default:
          return change;
    }
    return change;
  }

  _fixCode(code){
    if(code.indexOf('#')){
      return code.split('#')[0];
    }
    return code;
  }
}
module.exports = {
  DeTypeCheckUtil: new DeTypeCheckUtil()
}