const {cLexer:lexer } = require('chain-lexer');
const Decimal = require('decimal.js');
const {ObjectUtils} = require('../../utils/ObjectUtils');



 class AnalyzeCore{
   static TOKEN_TYPE_ID = "Identifier";
//    static varCustom = ['aa','bb','cc','dd','ee','ff','gg','hh','ii','jj','kk','ll','mm','nn','oo','pp','qq','rr','ss','tt','uu','vv','ww','xx','yy','zz']
   
   static operatorPriority = {
         '+':2,'-':2,'*':3,'/':3,'%':3,'^':4,'(':1,')':1
   }

   /**
    * 公式解析
    * @param calculateBase
    * @returns {any[]}
    */
   static renderParams(calculateBase) {
        let paramsSet=new Set();
        lexer.start(calculateBase);
        let parsedTokens = lexer.DFA.result.tokens;
        parsedTokens.forEach((token) => {
            if(token.type == AnalyzeCore.TOKEN_TYPE_ID){
                paramsSet.add(token.value);
            }
        });
        return Array.from(paramsSet);
    }
    static renderFunction(params,calculateBase){
        // console.log(params,calculateBase);
        let result = this.createFormulaRunner(calculateBase);
        return result;
        // return new Function("{"+params.join(",")+"}",`return eval(${calculateBase});`);
    }
    
    static createFormulaRunner(calculateBase){
        const tokens = this.tokenize(calculateBase);
        const postfix = this.infixToPostfix(tokens);

        return (variables) =>{
            const stack = [];
            for(let i=0; i < postfix.length;i++){
                let token = postfix[i];
                if(this.isOperator(token)){
                    const right = stack.pop();
                    const left = stack.pop();
                    stack.push(this.createOperation(left,right,token));
                }else{
                    stack.push(this.getOperand(token,variables));
                }
            }
            return stack.pop().toDecimal().toNumber();
        }
    }

    static tokenize(formula){
        return formula.match(/(\d+\.?\d*|\w+)|([+\-*/%^()])/g).filter(x=>x.trim());
    }
    static infixToPostfix(tokens){
        const output=[];
        const stack = [];
        for(const token of tokens){
            if(token === '('){
                stack.push(token);
            }else if(token === ')'){
                while(stack.length && stack[stack.length - 1] !== '('){
                    output.push(stack.pop());
                }
                stack.pop();
            }else if(this.isOperator(token)){
                while(stack.length && AnalyzeCore.operatorPriority[stack[stack.length-1]]>=AnalyzeCore.operatorPriority[token]){
                    
                    output.push(stack.pop());
                }
                stack.push(token);
            }else{
                output.push(token);
            }
        }
        return output.concat(stack.reverse());
    }

    static createOperation(left,right,operator){
        const methodMap = {
            "+":'add','-':'sub','*':'mul','/':'div','%':'mod','^':'pow'
        };
        return {
            toDecimal:()=>left.toDecimal()[methodMap[operator]](right.toDecimal())
        };
    }

    static getOperand(token,variables){
        return {
            toDecimal: ()=>{
                //未定义为0，不知道妥不妥
                if(variables[token] == undefined || ObjectUtils.isNumber(token) || ObjectUtils.isNumberStr(token)){
                    return new Decimal(token);
                }
                return new Decimal(String(variables[token]));
            }
        };
    }
    static isOperator(token){
        return '+-*/%^()'.includes(token);
    }
}
module.exports = {AnalyzeCore}
