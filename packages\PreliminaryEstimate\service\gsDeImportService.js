const {Service} = require('../../../core');
const ProjectDomain = require("../domains/ProjectDomain");
const {ObjectUtils} = require("../utils/ObjectUtils");
const DeDomain = require("../domains/DeDomain");
const projectLevelConstant = require("../constants/ProjectLevelConstant");
const StandardDeModel = require("../domains/deProcessor/models/StandardDeModel");
const {Snowflake} = require("../utils/Snowflake");
const DeTypeConstants = require("../constants/DeTypeConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const LabelConstants = require("../constants/LabelConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const WildcardMap = require("../core/container/WildcardMap");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const BranchProjectDisplayConstant = require("../constants/BranchProjectDisplayConstant");
const RcjCommonConstants = require("../constants/RcjCommonConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const DeQualityUtils = require("../domains/utils/DeQualityUtils");
const DeUtils = require("../domains/utils/DeUtils");
const { de } = require('../../../electron/enum/BranchProjectLevelConstant');


/**
 * 费率 service
 * @class
 */
class GsDeImportService extends Service {

    constructor(ctx) {
        super(ctx);
    }

  

    async importYgsDe(args) {
        let {constructId, singleId, unitId, fbTree, importConstructId, importSingleId, importUnitId} = args;

        //校验导入文件是否有数据，选择整个单位无数据/没勾选数据，无数据导入
        let selectFbZfb = [];
        let selectFb = [];
        let deDomain = ProjectDomain.getDomain(importConstructId).getDeDomain();
        await this.calFbTreeList(fbTree, selectFbZfb, selectFb);
        let importDeTree = deDomain.getDeTree(item => item.unitId === importUnitId);
        if ((ObjectUtils.isEmpty(fbTree) && importDeTree.length <= 0) || (ObjectUtils.isNotEmpty(fbTree) && ObjectUtils.isEmpty(selectFbZfb))) {
            return ResponseData.success(true);
        }
        let maxLevel = 0;
        let importAllUnit = false;
        if (ObjectUtils.isEmpty(fbTree)) {
            importAllUnit = true;
            let importUnitRoot = deDomain.getRoot(importUnitId);
            maxLevel = DeUtils.calculateLevel(importUnitRoot,true,false);
        }else{
            for(let zfbId of selectFbZfb){
                let zfbDe = deDomain.getDeById(zfbId);
                let curmaxLevel = DeUtils.calculateLevel(zfbDe,true,false);
                maxLevel = maxLevel>curmaxLevel?maxLevel:curmaxLevel;
            }
        }
        if (maxLevel >= 4) {
            return ResponseData.fail("导入后分部超过四级，请调整导入分部");
        }

        //当前项目预算书数据
        let unitRoot = ProjectDomain.getDomain(constructId).getDeDomain().getRoot(unitId);
        maxLevel = DeUtils.calculateLevel(unitRoot,true,false,true);
        if (maxLevel >= 4) {
            return ResponseData.fail("导入后分部超过四级，请调整当前项目分部");
        }
        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let constructMajorType = unit.constructMajorType;   //主单位专业
        let drFbList = unitRoot.children.filter(o=>ObjectUtils.isNotEmpty(o.importYgsDeDRGCFB));

        let deTree = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId);
        let deMaxIndex = ObjectUtils.isEmpty(deTree[deTree.length - 1].index) ? 0 : deTree[deTree.length - 1].index;
        let isImportYgsDe = deTree.filter(p => ObjectUtils.isNotEmpty(p.importYgsDeYYGCFB) && p.importYgsDeYYGCFB);   //如果已经导入过的话，则不在结构原有项目
        //导入单位人材机所有数据
        let nowRcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        if (ObjectUtils.isEmpty(isImportYgsDe)) {
            let fbList = deTree.filter(o => o.type === DeTypeConstants.DE_TYPE_FB);
            //如果没有分部，则新建原有工程分部，把定额parentId修改及链表修改
            let findOne = deTree.find(o => o.sequenceNbr === unit.defaultDeId);
            ProjectDomain.getDomain(constructId).ctx.deMap.deleteParentNodeChild(findOne.sequenceNbr);
            let fbId = Snowflake.nextId();
            let newModel = new StandardDeModel(constructId, unitId, fbId, unit.defaultDeId, DeTypeConstants.DE_TYPE_FB);
            newModel.deName = "原有工程分部";
            newModel.importYgsDeYYGCFB = true;
            DeDomain.filter4DeTree(await ProjectDomain.getDomain(constructId).getDeDomain().createDeRow(newModel, 0));
            let fbDeList = [];

            if(ObjectUtils.isEmpty(drFbList)){
                fbDeList = deTree.filter(o => o.parentId === unit.defaultDeId);
            } else {
                fbDeList = deTree.filter(o => o.parentId === unit.defaultDeId);
            }

            let updateQuantity = false;
            if (ObjectUtils.isNotEmpty(fbDeList)) {
                newModel.displaySign = BranchProjectDisplayConstant.open;
                ProjectDomain.getDomain(constructId).getDeDomain().updateDe(newModel);
                for (const p of fbDeList) {
                    if (p.type === DeTypeConstants.DE_TYPE_FB) {
                        p.type = DeTypeConstants.DE_TYPE_ZFB;
                    }
                    p.parentId = fbId;
                    p.index = p.index + 1;
                    ProjectDomain.getDomain(constructId).getDeDomain().updateDe(p);

                    if (ObjectUtils.isNotEmpty(p.importYgsDeDRGCFB)) {
                        //导入分部在外层
                        let parentDe = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(unit.defaultDeId);
                        ProjectDomain.getDomain(constructId).ctx.deMap.addParentNodeChild(p, parentDe);
                    } else {
                        ProjectDomain.getDomain(constructId).ctx.deMap.addParentNodeChild(p, newModel);
                    }
                    if (p.type === DeTypeConstants.DE_TYPE_DELIST && !updateQuantity) {
                        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                        await deDomain.updateQuantity(constructId, unitId, p.sequenceNbr, p.originalQuantity);
                        updateQuantity = true;
                    }
                }
            }
        }



        let deTreeNew = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId);
        let importConversion = await ProjectDomain.getDomain(importConstructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let importUnitConversion = importConversion[importUnitId];     //导入项目的标准换算信息
        let importUnit = ProjectDomain.getDomain(importConstructId).getProjectById(importUnitId);
        let importConstructMajorType = importUnit.constructMajorType;   //导入单位专业
        let majorTypeBoolean = importConstructMajorType === constructMajorType;
        let importFbList = importDeTree.filter(o => o.type === DeTypeConstants.DE_TYPE_FB);
        //如果没有分部，则新建导入工程分部，把定额parentId修改及链表修改
        let importFbId = Snowflake.nextId();
        let fbList = deTreeNew.filter(o=>o.type === DeTypeConstants.DE_TYPE_FB);
        let deMaxIndex1 = ObjectUtils.isEmpty(fbList[fbList.length - 1].index) ? 0 : fbList[fbList.length - 1].index;
        let importNewModel = new StandardDeModel(constructId, unitId, importFbId, unit.defaultDeId, DeTypeConstants.DE_TYPE_FB);
        importNewModel.deName = "导入工程分部"
        DeDomain.filter4DeTree(await ProjectDomain.getDomain(constructId).getDeDomain().createDeRow(importNewModel, ++deMaxIndex1));
        importNewModel.importYgsDeDRGCFB = true;
        ProjectDomain.getDomain(constructId).getDeDomain().updateDe(importNewModel);
        let importFbDeList = [];
        if (ObjectUtils.isEmpty(importFbList)) {
            // importFbDeList = importDeTree.filter(o => o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_USER_DE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
            importFbDeList = importDeTree.filter(o => o.parentId === importUnit.defaultDeId);
        } else {
            // importFbDeList = importDeTree.filter(o => o.type === DeTypeConstants.DE_TYPE_FB);
            importFbDeList = importDeTree.filter(o => o.parentId === importUnit.defaultDeId);
        }

        if (ObjectUtils.isNotEmpty(importFbDeList)) {
            importNewModel.displaySign = BranchProjectDisplayConstant.open;
            ProjectDomain.getDomain(constructId).getDeDomain().updateDe(importNewModel);
        }

        let quantitiesMap = ProjectDomain.getDomain(importConstructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let importUnitQuantiesMap = quantitiesMap.get(importUnitId);

        //导入单位人材机所有数据
        let importRcjList = ProjectDomain.getDomain(importConstructId).getResourceDomain().getResource(WildcardMap.generateKey(importUnitId) + WildcardMap.WILDCARD);
        for (const p of importFbDeList) {
            if (p.type === DeTypeConstants.DE_TYPE_FB) {
                if ((ObjectUtils.isNotEmpty(fbTree) && !selectFbZfb.includes(p.sequenceNbr))) {
                    //分部没勾选则不同步、子分部已经作为分部的child已经做了处理
                    continue;
                }
            }
            if (p.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE) {
                continue;
            }

            let deConversion = importUnitConversion[p.sequenceNbr];      //导入定额的标准换算
            let importNode = ProjectDomain.getDomain(importConstructId).ctx.deMap.getNodeById(p.sequenceNbr);
            let importNodeCopy = ConvertUtil.deepCopy(importNode);
            if (p.type === DeTypeConstants.DE_TYPE_FB) {
                importNodeCopy.type = DeTypeConstants.DE_TYPE_ZFB;
            }
            importNodeCopy.parentId = importFbId;
            importNodeCopy.index = p.index + 1;
            importNodeCopy.sequenceNbr = Snowflake.nextId();
            importNodeCopy.deRowId = importNodeCopy.sequenceNbr;
            importNodeCopy.constructId = constructId;
            importNodeCopy.unitId = unitId;

            if (p.type === DeTypeConstants.DE_TYPE_DELIST && !majorTypeBoolean) {
                await this.calMajorTypeBoolean(importNodeCopy);     //计算专业不同时定额类型
            }
            if (p.type === DeTypeConstants.DE_TYPE_DELIST) {
                importNodeCopy.calculateMethod = undefined;
            }

            this._fillArrow(importNodeCopy);
            ProjectDomain.getDomain(constructId).getDeDomain().createDeRow(importNodeCopy);
            // ProjectDomain.getDomain(constructId).ctx.deMap.addParentNodeChild(importNodeCopy, importNewModel);
            ProjectDomain.getDomain(constructId).getDeDomain().updateDe(importNodeCopy);


            if (importNodeCopy.type === DeTypeConstants.DE_TYPE_USER_RESOURCE) {
                //处理补充人材机编码
                // await this.calBCRCJCode(constructId, unitId, importNodeCopy);

                importNodeCopy.deRowId = importNodeCopy.sequenceNbr;
                importNodeCopy.deId = importNodeCopy.sequenceNbr;
                // await ProjectDomain.getDomain(constructId).getDeDomain().appendUserResource(constructId, unitId, importNodeCopy.deRowId, importNodeCopy);
                // await ProjectDomain.getDomain(constructId).getDeDomain().extendQuantity(constructId, unitId, importNodeCopy.deRowId);
            }

            //处理人材机
            if(importNodeCopy.type===DeTypeConstants.DE_TYPE_USER_DE || importNodeCopy.type === DeTypeConstants.DE_TYPE_DELIST || importNodeCopy.type === DeTypeConstants.DE_TYPE_DE || importNodeCopy.type === DeTypeConstants.DE_TYPE_RESOURCE || importNodeCopy.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
                await this.calRcjData(importNodeCopy.constructId, importNodeCopy.unitId, p.sequenceNbr, importNodeCopy.sequenceNbr, importRcjList, nowRcjList);
            }
            //处理标准换算
            if (importNodeCopy.type === DeTypeConstants.DE_TYPE_DELIST || importNodeCopy.type === DeTypeConstants.DE_TYPE_DE) {
                await this.calDeConversion(constructId, unitId, importNodeCopy.sequenceNbr, deConversion);
            }
            //处理工程量为GCGM的数据           
            let tokens = DeQualityUtils.evalQualityTokens(importNodeCopy.originalQuantity);
            if(ObjectUtil.isNotEmpty(tokens) && tokens.length > 0){
                DeQualityUtils.addNotifyQuantityMap(importNodeCopy,ProjectDomain.getDomain(constructId).functionDataMap);
                await ProjectDomain.getDomain(constructId).getDeDomain().updateQuantity(constructId, unitId, importNodeCopy.sequenceNbr, importNodeCopy.originalQuantity);
            }

            //处理定额工程量明细
            let deMap = importUnitQuantiesMap?.get(p.sequenceNbr);
            if (ObjectUtils.isNotEmpty(deMap)) {
                await this.calGCLMX(constructId, unitId, importNodeCopy.sequenceNbr, deMap);
            }

            if (p.type === DeTypeConstants.DE_TYPE_DELIST || p.type === DeTypeConstants.DE_TYPE_DE || p.type === DeTypeConstants.DE_TYPE_USER_RESOURCE || p.type === DeTypeConstants.DE_TYPE_RESOURCE || p.type === DeTypeConstants.DE_TYPE_USER_DE) {
                if (ObjectUtils.isNotEmpty(importNodeCopy.originalQuantity) && importNodeCopy.originalQuantity != 0) {
                    await ProjectDomain.getDomain(constructId).getDeDomain().updateQuantity(constructId, unitId, importNodeCopy.sequenceNbr, importNodeCopy.originalQuantity);
                }
            }

            await this.updateCopyDeNodeParentId(importNodeCopy, majorTypeBoolean, selectFbZfb, importRcjList, nowRcjList, importUnitConversion, importUnitQuantiesMap);
        }
        console.log("计算完成");
        return ResponseData.success(true);
    }


    async queryUnitGcgm(constructId, unitId) {
        let gcgm = 0;
        //当前单位工程规模
        let params = {};
        params.constructId = constructId;
        params.unitId = unitId;
        params.type = "13";
        params.levelType = 3;
        let gctzList = await this.service.PreliminaryEstimate.gsOverviewService.getList(params);
        if (ObjectUtils.isNotEmpty(gctzList)) {
            let find = gctzList.find(o => o.name === "工程规模");
            if (ObjectUtils.isNotEmpty(find) && ObjectUtils.isNotEmpty(find.context)) {
                gcgm = Number(find.context);
            }
        }
        return gcgm;
    }


    async calGCLMX(constructId, unitId, deId, oldDeMap) {
        let oldDeMapCopy = ConvertUtil.deepCopy(oldDeMap);
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId);
        let nowDeMap = unitQuantiesMap?.get(deId);
        if (ObjectUtils.isEmpty(nowDeMap)) {
            oldDeMapCopy.constructId = constructId;
            oldDeMapCopy.unitId = unitId;
            oldDeMapCopy.quotaListId = deId;
            if (ObjectUtils.isNotEmpty(oldDeMapCopy.quantities)) {
                oldDeMapCopy.quantities.forEach(o => {
                    o.quotaListId = deId;
                });
            }
            unitQuantiesMap.set(deId, oldDeMapCopy);
        }
    }


    async calBCRCJCode(constructId, unitId, rcj) {
        // let regex = new RegExp(`/...\d{3}$/`);
        // if (!regex.test(rcj.materialCode)) {
        //     return;
        // }
        //
        // let args = {};
        // args.constructId = constructId;
        // args.unitId = unitId;
        // args.prefix = rcj.materialCode.slice(0, -3);
        // let newVar = await this.service.PreliminaryEstimate.gsRcjService.getDefaultCode(args);
        // if (ObjectUtils.isNotEmpty(newVar)) {
        //     rcj.materialCode = args.prefix + newVar;
        // }
    }


    async addRcjUserList(constructId, unitId, rcj) {
        // let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        // let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
        //
        // // 放入用戶rcj
        // if (ObjectUtils.isEmpty(rcjUserList)) {
        //     rcjUserList = [];
        // }
        // rcjUserList.push(rcj);
        // businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, rcjUserList);
    }



    async calDeConversion(constructId, unitId, deId, deConversion) {
        if(ObjectUtils.isNotEmpty(deConversion)){
            let deConversionCopy = ConvertUtil.deepCopy(deConversion);
            deConversionCopy.constructId = constructId;
            deConversionCopy.unitId = unitId;
            deConversionCopy.deId = deId;
            deConversionCopy.sequenceNbr = deId;
            let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
            let unitConversion = conversion[unitId];

            // if(ObjectUtils.isNotEmpty(deConversionMapCopy.conversionList)){
            //     deConversionMapCopy.conversionList.forEach(o=>{
            //         o.deId = deId;
            //     })
            // }
            // if(ObjectUtils.isNotEmpty(deConversionMapCopy.originConversionList)){
            //     deConversionMapCopy.originConversionList.forEach(o=>{
            //         o.deId = deId;
            //     })
            // }
            if (ObjectUtils.isEmpty(unitConversion)) {
                let unitConversionNew = {};
                unitConversionNew[deId] = deConversionCopy;
                conversion[unitId] = unitConversionNew;
            } else {
                unitConversion[deId] =  deConversionCopy;
            }
        }
    }

    /**
   * 处理箭头
   * @param newNode
   * @param parentNode
   * @private
   */
  _fillArrow(newNode) {
    newNode.displaySign = ObjectUtils.isEmpty(newNode.children) ? BranchProjectDisplayConstant.noSign : BranchProjectDisplayConstant.open;
    //处理父节点箭头
    // if (ObjectUtil.isNotEmpty(parentNode)) {
    //   parentNode.displaySign = BranchProjectDisplayConstant.open;
    //   this.updateDe(parentNode);
    // }
  }


    async importExcelDe(args) {
        let {constructId, singleId, unitId, deList} = args;


    }

    async updateCopyDeNodeParentId(importNodeCopy, majorTypeBoolean, selectFbZfb, importRcjList, nowRcjList, importUnitConversion, importUnitQuantiesMap) {
        if (ObjectUtils.isNotEmpty(importNodeCopy.children)) {
            let deepCopy = ConvertUtil.deepCopy(importNodeCopy);
            for (let i = 0; i < deepCopy.children.length; i++) {
                let item = deepCopy.children[i];
                if (i === 0) {
                    ProjectDomain.getDomain(importNodeCopy.constructId).ctx.deMap.deleteParentNodeChild(importNodeCopy.sequenceNbr);
                }


                if (item.type === DeTypeConstants.DE_TYPE_FB || item.type === DeTypeConstants.DE_TYPE_ZFB) {
                    if (ObjectUtils.isNotEmpty(selectFbZfb) && !selectFbZfb.includes(item.sequenceNbr)) {
                        //没勾选则不同步
                        continue;
                    }
                }
                if (item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE) {
                    continue;
                }

                let oldDeId = ConvertUtil.deepCopy(item.sequenceNbr);
                item.sequenceNbr = Snowflake.nextId();
                item.deRowId = item.sequenceNbr;
                item.parentId = deepCopy.sequenceNbr;
                item.constructId = deepCopy.constructId;
                item.unitId = deepCopy.unitId;
                if (!majorTypeBoolean) {
                    await this.calMajorTypeBoolean(item);
                }
                if (item.type === DeTypeConstants.DE_TYPE_DELIST) {
                    item.calculateMethod = undefined;
                }

                this._fillArrow(item);
                ProjectDomain.getDomain(importNodeCopy.constructId).getDeDomain().createDeRow(item);
                // ProjectDomain.getDomain(importNodeCopy.constructId).ctx.deMap.addParentNodeChild(item, importNodeCopy);
                ProjectDomain.getDomain(importNodeCopy.constructId).getDeDomain().updateDe(item);

                //处理人材机
                if (item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_DELIST || item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE) {
                    await this.calRcjData(item.constructId, item.unitId, oldDeId, item.sequenceNbr, importRcjList, nowRcjList);
                }
                if (item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE) {
                    //处理补充人材机编码
                    // await this.calBCRCJCode(item.constructId, item.unitId, item);

                    item.deRowId = item.sequenceNbr;
                    item.deId = item.sequenceNbr;
                    // await ProjectDomain.getDomain(item.constructId).getDeDomain().appendUserResource(item.constructId, item.unitId, item.deRowId, item);
                    // await ProjectDomain.getDomain(item.constructId).getDeDomain().extendQuantity(item.constructId, item.unitId, item.deRowId);
                }

                //处理标准换算
                if (item.type === DeTypeConstants.DE_TYPE_DELIST || item.type === DeTypeConstants.DE_TYPE_DE) {
                    let deConversion = importUnitConversion[oldDeId];      //导入定额的标准换算
                    await this.calDeConversion(item.constructId, item.unitId, item.sequenceNbr, deConversion);
                }

                //处理定额工程量明细
                let deMap = importUnitQuantiesMap?.get(oldDeId);
                if (ObjectUtils.isNotEmpty(deMap)) {
                    await this.calGCLMX(item.constructId, item.unitId, item.sequenceNbr, deMap);
                }
                //处理工程量为GCGM的数据           
                let tokens = DeQualityUtils.evalQualityTokens(item.originalQuantity);
                if (ObjectUtil.isNotEmpty(tokens) && tokens.length > 0) {
                    DeQualityUtils.addNotifyQuantityMap(item, ProjectDomain.getDomain(item.constructId).functionDataMap);
                    await ProjectDomain.getDomain(item.constructId).getDeDomain().updateQuantity(item.constructId, item.unitId, item.sequenceNbr, item.originalQuantity);
                }

                //更新工程量，重新计算价钱
                if (item.type === DeTypeConstants.DE_TYPE_DELIST || item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_DE) {
                    if (i === deepCopy.children.length - 1) {
                        let deDomain = ProjectDomain.getDomain(item.constructId).getDeDomain();
                        if (ObjectUtils.isNotEmpty(importNodeCopy.originalQuantity) && importNodeCopy.originalQuantity != 0) {
                            // await deDomain.updateQuantity(item.constructId, item.unitId, item.sequenceNbr, item.originalQuantity);
                            let args = {};
                            args.constructId = item.constructId;
                            args.unitId = item.unitId;
                            args.deRowId = item.sequenceNbr;
                            deDomain.notify(args, false);
                        }
                    }
                }

                if (ObjectUtils.isNotEmpty(item.children)) {
                    await this.updateCopyDeNodeParentId(item, majorTypeBoolean, selectFbZfb, importRcjList, nowRcjList, importUnitConversion, importUnitQuantiesMap);
                }
            }
        }
    }


    async calRcjData(constructId, unitId, oldDeId, newDeId, importRcjList, nowRcjList) {
        //组装人定额材机数据
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        let deRcjList = importRcjList.filter(o => o.deRowId === oldDeId);
        if (ObjectUtils.isNotEmpty(deRcjList)) {
            for (const o of deRcjList) {
                let deRcjCopy = ConvertUtil.deepCopy(o);
                deRcjCopy.sequenceNbr = Snowflake.nextId();
                deRcjCopy.constructId = constructId;
                deRcjCopy.unitId = unitId;
                deRcjCopy.deRowId = newDeId;
                deRcjCopy.deId = newDeId;
                deRcjCopy.parentId = newDeId;
                if (ObjectUtils.isNotEmpty(deRcjCopy.pbs)) {
                    deRcjCopy.pbs.forEach(m => {
                        m.parentId = deRcjCopy.sequenceNbr;
                    });

                    let nowRcj = nowRcjList.find(p => p.materialCode === deRcjCopy.materialCode && p.materialName === deRcjCopy.materialName
                        && p.specification === deRcjCopy.specification && p.unit === deRcjCopy.unit && p.dePrice === deRcjCopy.dePrice);
                    if (ObjectUtils.isNotEmpty(nowRcj) && nowRcj.markSum != deRcjCopy.markSum) {
                        deRcjCopy.markSum = nowRcj.markSum;
                    }
                }


                //导入人材机数据
                let filter = rcjList.filter(o => o.materialCode === deRcjCopy.materialCode && o.type === deRcjCopy.type && o.materialName === deRcjCopy.materialName &&
                    o.specification === deRcjCopy.specification && o.unit === deRcjCopy.unit && o.dePrice === deRcjCopy.dePrice);
                if (ObjectUtils.isNotEmpty(filter)) {
                    deRcjCopy.marketPrice = filter[0].marketPrice;  //修改人材机市场价
                    ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deRcjCopy.deRowId, deRcjCopy);
                } else {
                    ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deRcjCopy.deRowId, deRcjCopy);
                }
                await ProjectDomain.getDomain(constructId).getResourceDomain().notify(deRcjCopy);

                if (deRcjCopy.materialCode.includes("补充")) {
                    //处理补充人材机编码
                    // await this.calBCRCJCode(constructId, unitId, deRcjCopy);
                    // await this.addRcjUserList(constructId, unitId, deRcjCopy);
                    continue;
                }

                //计算人材机的编码
                if (ObjectUtils.isNotEmpty(deRcjCopy.rcjId)) {
                    let constructRcjArray = new Array();
                    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
                    if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                        constructProjectRcjs.forEach(item => constructRcjArray.push(item));
                        if (ObjectUtils.isNotEmpty(deRcjCopy.pbs)) {
                            let businessMap = ProjectDomain.getDomain(deRcjCopy.constructId).functionDataMap;
                            let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
                            if (ObjectUtils.isEmpty(objMap)) {
                                businessMap.set(FunctionTypeConstants.RCJ_MEMORY, new Map());
                                objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
                            }
                            let unitMemory = objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + deRcjCopy.constructId + FunctionTypeConstants.SEPARATOR + deRcjCopy.unitId);

                            //处理子级数据
                            for (const p of deRcjCopy.pbs) {
                                if (ObjectUtils.isNotEmpty(unitMemory)) {
                                    let existRcj = await this.service.PreliminaryEstimate.gsRcjCollectService.findAlikeRcj(unitMemory, p);
                                    if (ObjectUtils.isNotEmpty(existRcj)) {
                                        p.materialCode = existRcj.materialCode;
                                    } else {
                                        if (await this.service.PreliminaryEstimate.gsRcjCollectService.estimateChangeMaterialCode(p)) {

                                        } else {
                                            let maxNum = await this.service.PreliminaryEstimate.gsRcjCollectService.getMaxNumber(unitMemory, p);
                                            p.materialCode.replace( /#\d+/g, '') + '#' + maxNum;
                                        }
                                        await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(p, true, constructRcjArray);

                                        // if (p.materialCode.includes('#')) {
                                        //     p.materialCode = p.materialCode.replace(/#\d+/g, '');
                                        // } else {
                                        //     await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(p, true, constructRcjArray);
                                        // }
                                    }
                                } else {
                                    if (p.materialCode.includes('#')) {
                                        p.materialCode = p.materialCode.replace(/#\d+/g, '');
                                    } else {
                                        await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(p, true, constructRcjArray);
                                    }
                                }
                            }
                            // await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructRcjArray, deRcjCopy, true);
                        }


                        if (ObjectUtils.isNotEmpty(deRcjCopy.pbs)) {
                            //处理父级数据
                            if (deRcjCopy.materialCode.includes('#')) {
                                deRcjCopy.materialCode = deRcjCopy.materialCode.replace(/#\d+/g, '');
                            }
                            await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructRcjArray, deRcjCopy, true);
                            if (!deRcjCopy.materialCode.includes('#')) {
                                await this.service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructRcjArray, deRcjCopy, true);
                            }
                        } else {
                            //处理父级数据
                            if (deRcjCopy.materialCode.includes('#')) {
                                deRcjCopy.materialCode = deRcjCopy.materialCode.replace(/#\d+/g, '');
                            }
                            await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(deRcjCopy, true, constructRcjArray);
                            // if (!deRcjCopy.materialCode.includes('#')) {
                            //     await this.service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(deRcjCopy, true, constructRcjArray);
                            // }
                        }
                    }
                }
            }
        }
    }

    async calMajorTypeBoolean(de) {
        switch (de.displayType) {
            case '定':
                de.displayType = '借';
                break;
            case '换':
                de.displayType = '借换';
                break;
            case '借':
                de.displayType = '定';
                break
            case '借换':
                de.displayType = '换';
                break
            default:
        }
    }


    async calFbTreeList(fbTree, selectFbZfb, selectFb) {
        if (ObjectUtils.isNotEmpty(fbTree)) {
            for (const o of fbTree) {
                if (o.select) {
                    selectFbZfb.push(o.sequenceNbr);
                    if (o.type === DeTypeConstants.DE_TYPE_FB) {
                        selectFb.push(o.sequenceNbr);
                    }
                }
                if (ObjectUtils.isNotEmpty(o.childrenList)) {
                    await this.calFbTreeList(o.childrenList, selectFbZfb, selectFb);
                }
            }
        }
    }


}

GsDeImportService.toString = () => '[class GsDeImportService]';
module.exports = GsDeImportService;
