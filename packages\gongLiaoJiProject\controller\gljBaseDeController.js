const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require('../utils/ObjectUtils');
const ProjectDomain = require("../domains/ProjectDomain");

/**
 * 定额 Controller
 */
class GljBaseDeController extends Controller {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 根据定额库编码查询定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async listDeByLibraryCode(args) {
        let {libraryCode} = args
        const result =  await this.service.gongLiaoJiProject.gljBaseDeService.listDeByLibraryCode(libraryCode);
        return ResponseData.success(result);
    }

    async getDeAndRcj(args){
        let {deId} = args
        const result =  await this.service.gongLiaoJiProject.gljBaseDeService.getDeAndRcj(deId);
        return ResponseData.success(result);
    }

    /**
     * 获取定额分类目录树
     * @param libraryCode 定额册code
     */
    async queryDeListTree(args){
        let {libraryCode} = args
        if (ObjectUtils.isEmpty(libraryCode)) {
            return ResponseData.fail('libraryCode为空');
        }
        const result = await this.service.gongLiaoJiProject.gljBaseDeService.listTreeByLibraryCode(libraryCode);
        return ResponseData.success(result);
    }


    /**
     * 根据条件查询定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryListByParam(args) {
        const result = await this.service.gongLiaoJiProject.gljBaseDeService.queryListByParam(args);
        return ResponseData.success(result);
    }

    /**
     * 查询某条定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryDeById(args) {
        let {standardId, constructId, sequenceNbr} = args
        if (ObjectUtils.isEmpty(standardId) && ObjectUtils.isEmpty(sequenceNbr)) {
            return ResponseData.success(null);
        }
        let result = await this.service.gongLiaoJiProject.gljBaseDeService.queryDeById(args);
        if (ObjectUtils.isEmpty(result) && ObjectUtils.isNotEmpty(sequenceNbr)) {
            let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
            result = deDomain.getDeById(sequenceNbr);
        }
        return ResponseData.success(result);
    }

    /**
     * 查询某条定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getDeAndRcjByDeCode(args) {
        let {constructId, unitId, deCode} = args;
        if (ObjectUtils.isEmpty(deCode)) {
            return ResponseData.success(null);
        }
        const result = await this.service.gongLiaoJiProject.gljBaseDeService.getDeAndRcjByDeCode(constructId, unitId, deCode);
        return ResponseData.success(result);
    }

    /**
     * 模糊查定额
     * @param qdDeParam 定额编码、名称、分类名
     * @see ../params/QdDeParam
     */
    async likeDeByCodeOrName(qdDeParam) {
        if (ObjectUtils.isEmpty(qdDeParam.page)){
            qdDeParam.page = 1;
        }
        if (ObjectUtils.isEmpty(qdDeParam.limit)){
            qdDeParam.limit = 20;
        }
        const result = await this.service.gongLiaoJiProject.gljBaseDeService.queryDeByBdCodeAndName(qdDeParam);

        return ResponseData.success(result);
    }

    /**
     * 子目是否关联
     * @param args
     * @returns {Promise<void>}
     */
    async zmIsRelation(args) {
        let {deId} = args
        let deRelationList = await this.service.gongLiaoJiProject.gljBaseDeRelationService.getZmRelationByDeIdF(deId);
        if (ObjectUtils.isNotEmpty(deRelationList)) {
            return ResponseData.success(true);
        }
        return ResponseData.success(false);
    }

    /**
     * 获取关联定额
     * @param args
     * @returns {Promise<void>}
     */
    async zmDeList(args) {
        let {deId} = args
        let result = await this.service.gongLiaoJiProject.gljBaseDeRelationService.zmDeList(deId);
        return ResponseData.success(result);
    }

    /**
     * 根据人材机 查询所有定额
     * @param params
     * @returns {Promise<ResponseData>}
     */
    async queryBaseDeByRcj(params) {
        const result = await this.service.gongLiaoJiProject.gljBaseDeService.queryBaseDeByRcj(params);
        return ResponseData.success(result);
    }


}

GljBaseDeController.toString = () => '[class GljBaseDeController]';
module.exports = GljBaseDeController;