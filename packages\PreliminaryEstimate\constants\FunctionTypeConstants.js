

class FunctionTypeConstants {

    /**
     * 工程项目 - 建设其他费 key
     * @type {string}
     */
    static PROJECT_JSQTF = "PROJECT_JSQTF";

    /**
     * 工程项目 - 建设其他费费用代码 key
     * @type {string}
     */
    static PROJECT_JSQTF_CODE = "PROJECT_JSQTF_CODE";

    /**
     * 工程项目 - 建设其他费费用计算器 key
     * @type {string}
     */
    static PROJECT_JSQTF_JSQ = "PROJECT_JSQTF_JSQ";

    /**
     * 工程项目 - 概算汇总 key
     * @type {string}
     */
    static PROJECT_GS_SUMMARY = "PROJECT_GS_SUMMARY";

    /**
     * 工程项目 - 概算费用代码
     * @type {string}
     */
    static PROJECT_GS_CODE = "PROJECT_GS_CODE";

    /**
     *  概算格列设置
     * @type {string}
     */
    static PROJECT_GS_TABLELIST = "PROJECT_GS_TABLELIST-";

    /**
     *  工程项目 - 概算调整 key
     * @type {string}
     */
    static PROJECT_GS_ADJUST = "PROJECT_GS_ADJUST";

    /**
     * 单位工程 - 费用汇总 key
     * @type {string}
     */
    static UNIT_COST_SUMMARY = "UNIT_COST_SUMMARY";

    /**
     * 单位工程 - 费用代码 key
     * @type {string}
     */
    static UNIT_COST_CODE = "UNIT_COST_CODE";

    /**
     * 单位工程 - 费用汇总合计 key
     * @type {string}
     */
    static UNIT_COST_SUMMARY_TOTAL = "UNIT_COST_SUMMARY_TOTAL";

    /**
     * 工程项目 - 取费表
     * @type {string}
     */
    static PROJECT_QFB = "PROJECT_QFB";

    /**
     * 单位工程 - 取费表
     * @type {string}
     */
    static UNIT_QFB = "UNIT_QFB";

    /**
     * 单位工程 - 工程量明细 Quantities
     * @type {string}
     */
     static UNIT_QUANTITIES = "UNIT_QUANTITIES";

    /**
     * 单位工程 - 标准换算 CONVERSION
     * @type {string}
     */
     static UNIT_CONVERSION = "UNIT_CONVERSION";

    /**
     * 工程项目 - 费用查看
     * @type {string}
     */
    static PROJECT_FYCK = "PROJECT_FYCK";

    /**
     * 工程项目 - 费用查看
     * @type {string}
     */
    static SINGLE_FYCK = "SINGLE_FYCK";

    /**
     * 单位工程 - 费用查看
     * @type {string}
     */
    static UNIT_FYCK = "UNIT_FYCK";

    /**
     *    人材机汇总 key
     * @type {string}
     */
    static RCJ_COLLECT = "RCJ_COLLECT";
    /**
     *    人材机汇总 单位菜单 key
     * @type {string}
     */
    static UNIT_MENU = "UNIT_MENU-";
    /**
     *    人材机汇总 工程项目菜单 key
     * @type {string}
     */
    static PROJECT_MENU = "PROJECT_MENU";

    static SINGLE_MENU = "SINGLE_MENU";

    /**
     *  分隔符
     * @type {string}
     */
    static SEPARATOR = "-";

    /**
     *   工程项目数据排序
     * @type {string}
     */
    static PROJECT_DATA_SORT = "PROJECT_DATA_SORT";
    /**
     *   单项数据排序
     * @type {string}
     */
    static SINGLE_DATA_SORT = "SINGLE_DATA_SORT-";
    /**
     *   单位数据排序
     * @type {string}
     */
    static UNIT_DATA_SORT = "UNIT_DATA_SORT-";

    /**
     *  单位工程颜色
     */
    static UNIT_COLOR = "UNIT_COLOR";

    /**
     *  工程基本信息
     */
    static JBXX_KEY = "PROJECT_JBXX";
    /**
     *  工程基本信息-基本
     */
    static JBXX_KEY_TYPE_11 = "11";
    /**
     *  工程基本信息-编制说明
     */
    static JBXX_KEY_TYPE_12 = "12";
    /**
     *  工程基本信息-特征
     */
    static JBXX_KEY_TYPE_13 = "13";

    /**
     *  设备购置费信息
     */
    static SBGZF_KEY = "PROJECT_SBGZF";
    /**
     *  设备购置费信息-国内
     */
    static SBGZF_KEY_TYPE_GN = "sbgzf00";
    /**
     *  设备购置费信息-国外
     */
    static SBGZF_KEY_TYPE_GW = "sbgzf01";
    /**
     *  设备购置费信息-汇总
     */
    static SBGZF_KEY_TYPE_HZ = "sbgzf02";
    /**
     *  设备购置费信息-汇总金额
     */
    static SBGZF_KEY_TYPE_HZ_SBF = "SBF";

    /**
     *  设备购置费信息-设备进口计算器
     */
    static SBGZF_KEY_TYPE_HZ_JSQ = "JSQ";
    
    /**
     *  单位独立费
     */
    static UNIT_DLF_KEY = "UNIT_DLF";

    /**
     *    人材机汇总 载价 单位
     * @type {string}
     */
    static UNIT_LOAD_PRICE = "UNIT_LOAD_PRICE-";

    /**
     *    人材机汇总 载价 单项
     * @type {string}
     */
    static SINGLE_LOAD_PRICE = "SINGLE_LOAD_PRICE-";

    /**
     *  表格列设置
     */
    static UNIT_BGLSZ = "UNIT_BGLSZ";

    /**
     *    人材机汇总 载价 工程项目
     * @type {string}
     */
    static PROJECT_LOAD_PRICE = "PROJECT_LOAD_PRICE";
    /**
     * 用户定额
     * @type {string}
     */
    static PROJECT_USER_DE = "PROJECT_USER_DE";
    /**
     *   用戶人材機
     * @type {string}
     */
    static PROJECT_USER_RCJ = "PROJECT_USER_RCJ";

    /**
     * 安装费用缓存
     * @type {string}
     */
    static PROJECT_AZ_CACHE = "PROJECT_USER_AZ_CACHE";

    /**
     * 安装费用缓存-预算定额
     * @type {string}
     */
    static PROJECT_AZ_CACHE_YS = "PROJECT_USER_AZ_CACHE_YS";

    /**
     *  工程项目临时数据
     * @type {string}
     */
    static TEMPORARY_DATA = "TEMPORARY_DATA";

    /**
     *    人材机表格列设置
     * @type {string}
     */
    static RCJ_TABLELIST = "RCJ_TABLELIST-";

    /**
     *    预算书表格列设置
     * @type {string}
     */
    static YSH_TABLELIST = "YSH_TABLELIST-";
    /**
         *    预算书表格列设置
         * @type {string}
         */
    static YSH_GCL_EXP_NOTIFY = "YSH_GCL_EXP_NOTIFY";
    /**
     * 单位独立费
     */
    static DLF_TABLELIST = "DLF_TABLELIST-";

    /**
     * 单位三材
     */
    static SC_TABLELIST = "SC_TABLELIST-";

    /**
     *    预算书整理子目配置
     * @type {string}
     */
    static YSH_TABLELIST_ARRANGE = "YSH_TABLELIST-ARRANGE-";

    /**
     * 工程项目设置
     * @type {string}
     */
    static PROJECT_SETTING = "PROJECT_SETTING";

    /**
     * 人材机缓存
     * @type {string}
     */
    static RCJ_MEMORY = "RCJ_MEMORY";
    /**
     * 单位 - 人材机缓存
     * @type {string}
     */
    static UNIT_RCJ_MEMORY = "UNIT_RCJ_MEMORY-";
    /**
     *  定额主材设备
     * @type {string}
     */
    static UNIT_DE_MAINMATERIAL = "UNIT_DE_MAINMATERIAL-";
    /**
     * 精度设置
     */
    static PROJECT_PRECISION_SETTING = 'PROJECT_PRECISION_SETTING';

    /**
     * 定额费用代码
     */
    static DE_COST_CODE = 'DE_COST_CODE';

    /**
     * 定额操作缓存
     */
    static DE_SETTING_CACHE = "DE_SETTING_CACHE";

    /**
     * 所有页签缓存
     */
    static TABLE_SETTING_CACHE = "TABLE_SETTING_CACHE";

    /**
     * 人材机汇总-主要材料设置
     */
    static MAIN_MATERIAL_SETTING = "MAIN_MATERIAL_SETTING-";

    /**
     * 定额价差显示设置
     */
    static DE_PRICE_SPREAD = "DE_PRICE_SPREAD";
}
FunctionTypeConstants.toString = () => 'FunctionTypeConstants';
module.exports = FunctionTypeConstants;