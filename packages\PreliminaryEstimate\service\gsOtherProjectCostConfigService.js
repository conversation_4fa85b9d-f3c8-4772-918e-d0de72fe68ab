const {Service} = require('../../../core');
const gsGcsj = require('../jsonData/gs_jsqtf_config/gs_gcsj.json');
const gsGcjl = require('../jsonData/gs_jsqtf_config/gs_gcjl.json');
const gsHjyx = require('../jsonData/gs_jsqtf_config/gs_hjyx.json');
const gsJsxm = require('../jsonData/gs_jsqtf_config/gs_jsxm.json');
const gsShfx = require('../jsonData/gs_jsqtf_config/gs_shfx.json');
const gsZjzx = require('../jsonData/gs_jsqtf_config/gs_zjzx.json');
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const OtherProjectCostCategoryEnum = require("../enums/OtherProjectCostCategoryEnum");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../utils/NumberUtil");

/**
 * 建设其他费-计算器配置    地质灾害危险性评估费、水土保持补偿费涉及联动，在前端写了
 */
class GsOtherProjectCostConfigService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取计算器中的金额下拉数据
     * @param args
     * @returns {*[]}
     */
    async getMoney(args) {
        let {constructId, calculatorBasis} = args;

        let calculationBaseList;
        for (let enumKey in OtherProjectCostCategoryEnum) {
            if (OtherProjectCostCategoryEnum[enumKey].code === calculatorBasis) {
                let vaule = OtherProjectCostCategoryEnum[enumKey].value;
                if (vaule === 0) {
                    calculationBaseList = await this.getCalculateBase4(constructId);
                    break;
                }
                if (vaule === 1) {
                    calculationBaseList = await this.getCalculateBase1(constructId);
                    break;
                }
                if (vaule === 2) {
                    calculationBaseList = null;
                    break;
                }
            }
        }
        return calculationBaseList;
    }

    /**
     * 获取计算基数
     * @returns {any[]}
     * @param constructId
     */
    async getCalculateBase4(constructId) {

        let calculationBaseMap = new Map();
        // 获取当前工程项目下所有的单位
        let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
        let jagcf = 0;
        // 获取该项目的所有单位工程
        for (let i in unitProjects) {
            let unitProject = unitProjects[i];
            // 建筑工程费 = ∑各建筑单位工程造价合价 + ∑各安装单位工程造价合价
            jagcf = NumberUtil.add(jagcf, unitProject.projectCost);
        }
        calculationBaseMap.set("建安工程费", jagcf);

        // 建设其他费.联合试运转费
        let jsqtLhsyzf;
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        if (ObjectUtils.isNotEmpty(otherProjectCosts)) {
            let otherProjectCost = otherProjectCosts.find(item => item.name === "联合试运转费");
            jsqtLhsyzf = ObjectUtils.isEmpty(otherProjectCost) ? 0 : parseFloat(otherProjectCost.amount);
        } else {
            jsqtLhsyzf = 0;
        }
        calculationBaseMap.set("联合试运转费", jsqtLhsyzf);

        // 设备购置费 = ∑设备购置费汇总合计
        let equipmentCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY)
            .get(this.service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ));
        let sbgzf = equipmentCosts[0].price;
        calculationBaseMap.set("设备购置费", sbgzf);

        // 整个项目的总工程造价值
        let unitSbf = 0;
        for (let j = 0; j < unitProjects.length; j++) {
            let unitProject = unitProjects[j];
            // 获取该单位的费用汇总
            let param = {
                constructId: constructId,
                singleId: unitProject.parentId,
                unitId: unitProject.sequenceNbr
            }
            let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);
            unitSbf = NumberUtil.add(unitSbf, unitCostSummaryPriceMap.get("设备费"));
        }
        calculationBaseMap.set("设备费", unitSbf);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let jsqJe = precision.OTHER_PROJECT_COST.jsqJe;

        // calculationBaseMap转成数组
        let calculationBaseList = [];
        calculationBaseMap.forEach((value, key) => {
            let calculationBase = {};
            calculationBase.name = key;
            calculationBase.money = NumberUtil.numberScale(NumberUtil.divide(value, 10000), jsqJe);
            calculationBaseList.push(calculationBase);
        });
        return calculationBaseList;
    }

    /**
     * 获取计算基数
     * @returns {any[]}
     * @param constructId
     */
    async getCalculateBase1(constructId) {

        // 获取当前工程项目下所有的单位
        let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
        let jagcf = 0;
        // 获取该项目的所有单位工程
        for (let i in unitProjects) {
            let unitProject = unitProjects[i];
            // 建筑工程费 = ∑各建筑单位工程造价合价 + ∑各安装单位工程造价合价
            jagcf = NumberUtil.add(jagcf, unitProject.projectCost);
        }

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let jsqJe = precision.OTHER_PROJECT_COST.jsqJe;

        let calculationBase = {};
        calculationBase.name = "建安工程费";
        calculationBase.money = NumberUtil.numberScale(NumberUtil.divide(jagcf, 10000), jsqJe);

        let calculationBaseList = [];
        calculationBaseList.push(calculationBase);

        return calculationBaseList;
    }


    /**
     * 获取工程设计-专业调整系数、工程复杂调整系数、附加调整系数、其他调整系数
     */
    getGcsj(args) {
        // let menuName = args.menuName;
        // switch (menuName) {
        //     case "专业调整系数":
        //         return gsGcsj.专业调整系数;
        //     case "工程复杂调整系数":
        //         return gsGcsj.工程复杂调整系数;
        //     case "附加调整系数":
        //         return gsGcsj.附加调整系数;
        //     case "其他调整系数":
        //         return gsGcsj.其他调整系数;
        //     default:
        //         return null;
        // }
        return gsGcsj;
    }


    /**
     * 获取工程监理-专业调整系数、复杂调整系数、高程调整系数
     */
    getGcjl(args) {
        // let menuName = args.menuName;
        // switch (menuName) {
        //     case "专业调整系数":
        //         return gsGcjl.专业调整系数;
        //     case "复杂调整系数":
        //         return gsGcjl.复杂调整系数;
        //     case "高程调整系数":
        //         return gsGcjl.高程调整系数;
        //     default:
        //         return null;
        // }
        return gsGcjl;
    }

    /**
     * 获取环境影响-行业调整系数、环境敏感调整系数
     */
    getHjyx(args) {
        // let menuName = args.menuName;
        // switch (menuName) {
        //     case "专业调整系数":
        //         return gsHjyx.行业调整系数;
        //     case "复杂调整系数":
        //         return gsHjyx.环境敏感调整系数;
        //     default:
        //         return null;
        // }
        return gsHjyx;
    }


    /**
     * 获取建设项目-行业调整系数、工程复杂调整系数
     */
    getJsxm(args) {
        // let menuName = args.menuName;
        // switch (menuName) {
        //     case "专业调整系数":
        //         return gsJsxm.行业调整系数;
        //     case "复杂调整系数":
        //         return gsJsxm.工程复杂调整系数;
        //     default:
        //         return null;
        // }
        return gsJsxm;
    }

    /**
     * 获取社会风险稳定-专业调整系数、社会稳定风险敏感程度调整系数、区域范围调整系数
     */
    getShfx(args) {
        // let menuName = args.menuName;
        // switch (menuName) {
        //     case "专业调整系数":
        //         return gsShfx.行业调整系数;
        //     case "社会稳定风险敏感程度调整系数":
        //         return gsShfx.社会稳定风险敏感程度调整系数;
        //     case "区域范围调整系数":
        //         return gsShfx.区域范围调整系数;
        //     default:
        //         return null;
        // }
        return gsShfx;
    }

    /**
     * 获取造价咨询-专业调整系数、钢筋工程量精细计量方式、工程复杂调整系数
     */
    getZjzx(args) {
        // let menuName = args.menuName;
        // switch (menuName) {
        //     case "专业调整系数":
        //         return gsZjzx.专业调整系数;
        //     case "钢筋工程量精细计量方式":
        //         return gsZjzx.钢筋工程量精细计量方式;
        //     case "工程复杂调整系数":
        //         return gsZjzx.工程复杂调整系数;
        //     default:
        //         return null;
        // }
        return gsZjzx;
    }

}

GsOtherProjectCostConfigService.toString = () => '[class GsOtherProjectCostConfigService]';
module.exports = GsOtherProjectCostConfigService;