const {Service} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {GsConversionModel} = require("../models/GsConversionModel");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");

/**
 * 标准换算 service
 * @class
 */
class GsConversionService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 标准换算
     * @param projectConversionMap
     */
    transConversion(projectConversionMap) {
        projectConversionMap = ObjectUtils.convertObjectToMap(projectConversionMap);
        for (let [key, unitConversionMap] of projectConversionMap) {
            unitConversionMap = ObjectUtils.convertObjectToMap(unitConversionMap);
            for (let [key, gsConversionModel] of unitConversionMap) {
                gsConversionModel = ObjectUtils.copyProp(gsConversionModel, new GsConversionModel());
                gsConversionModel.conversionList = ObjectUtils.convertMapToArrayAndObject(gsConversionModel.conversionList)
                gsConversionModel.originConversionList = ObjectUtils.convertMapToArrayAndObject(gsConversionModel.originConversionList)
                gsConversionModel.defaultConcersions = ObjectUtils.convertMapToArrayAndObject(gsConversionModel.defaultConcersions)
                gsConversionModel.originDefaultConcersions = ObjectUtils.convertMapToArrayAndObject(gsConversionModel.originDefaultConcersions)
                gsConversionModel.conversionInfo = !gsConversionModel.conversionInfo? [] : ObjectUtils.convertMapToArrayAndObject(gsConversionModel.conversionInfo)
                gsConversionModel.redArray = !gsConversionModel.redArray? [] : ObjectUtils.convertMapToArrayAndString(gsConversionModel.redArray)
                gsConversionModel.blackArray = !gsConversionModel.blackArray? [] : ObjectUtils.convertMapToArrayAndString(gsConversionModel.blackArray)
                gsConversionModel.nameSuffixHistory = !gsConversionModel.nameSuffixHistory? [] : ObjectUtils.convertMapToArrayAndString(gsConversionModel.nameSuffixHistory)
                gsConversionModel.codeSuffixHistory = !gsConversionModel.codeSuffixHistory? [] : ObjectUtils.convertMapToArrayAndString(gsConversionModel.codeSuffixHistory)
                unitConversionMap.set(key, gsConversionModel);
            }
            projectConversionMap.set(key, unitConversionMap);
        }
        return projectConversionMap;
    }

    /**
     * 清空标准换算
     * @param standardDeId
     * @param fbFxDeId
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async cleanRules(standardDeId, fbFxDeId, constructId, singleId, unitId) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        let gsConversionModel = unitConversion[fbFxDeId];
        gsConversionModel.conversionList = ConvertUtil.deepCopy(gsConversionModel.originConversionList);
        gsConversionModel.defaultConcersions = ConvertUtil.deepCopy(gsConversionModel.originDefaultConcersions);

        let deRow = await this.service.PreliminaryEstimate.gsRuleDetailFullService.replaceDe(constructId, unitId, fbFxDeId, false);
        // await this.service.PreliminaryEstimate.gsConversionDeService.conversionRule(
        //     constructId,
        //     singleId,
        //     unitId,
        //     fbFxDeId,
        //     gsConversionModel.conversionList
        // );
    }

    /**
     * 更新 人材机消耗量
     * @param rcj
     * @param resQty
     * @returns {Promise<void>}
     */
    async updateRcjResQty(rcj, resQty) {
        // 修改人材机消耗量
        let params = {
            constructId: rcj.constructId,
            singleId: rcj.singleId,
            unitId: rcj.unitId,
            deId: rcj.deId,
            rcjDetailId: rcj.sequenceNbr,
            constructRcj: {resQty},
        }
        await this.service.PreliminaryEstimate.gsRcjService.updateRcjDetail(params);
    }

}

GsConversionService.toString = () => '[class GsConversionService]';
module.exports = GsConversionService;
