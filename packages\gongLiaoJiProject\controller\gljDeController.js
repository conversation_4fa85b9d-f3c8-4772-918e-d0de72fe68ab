const { Controller } = require('../../../core');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const ProjectDomain = require('../domains/ProjectDomain');
const { Snowflake } = require('../utils/Snowflake');
const DeDomain = require('../domains/DeDomain');
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const BaseDomain = require('../domains/core/BaseDomain');
const { ResponseData } = require('../../../electron/utils/ResponseData');
const StandardDeModel = require('../domains/deProcessor/models/StandardDeModel');
const DeTypeConstants = require("../constants/DeTypeConstants");
const CommonConstants = require("../constants/CommonConstants");
const ProjectLevelConstant = require("../constants/ProjectLevelConstant");
const BranchProjectDisplayConstant = require("../constants/BranchProjectDisplayConstant");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const PropertyUtil = require("../domains/utils/PropertyUtil");
const {ConvertUtil} = require("../utils/ConvertUtils")
const WildcardMap = require('../core/container/WildcardMap');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const DeCommonConstants = require("../constants/DeCommonConstants");
const CostDeMatchConstants = require("../constants/CostDeMatchConstants");
class GljDeController extends Controller {

  constructor(ctx) {
    super(ctx);
  }

  /**
   *
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async createDe(args)
  {
    let {de,prevDeRowId, deRow} = args;
    let index;
    let prevDeRow;
    if(ObjectUtil.isNotEmpty(prevDeRowId))
    {
        prevDeRow = ProjectDomain.getDomain(de.constructId).deDomain.getDeById(prevDeRowId);
        if (prevDeRowId !== de.parentId) {
            if(ObjectUtil.isEmpty(prevDeRow)){
              index = 0
            }else{
              index = prevDeRow.index + 1;
            }
        } else {
            index = 0
        }
    }

    let newModel = new StandardDeModel(de.constructId,de.unitId,Snowflake.nextId(),de.parentId,de.type);
    let deModel = await ProjectDomain.getDomain(de.constructId).deDomain.createDeRow(newModel,index,true);

    await this.service.gongLiaoJiProject.gljDeService.moveDeToFb(deModel, prevDeRowId, deRow);
    return  ResponseData.success(DeDomain.filter4DeTree(deModel));
  }
  /**
   * 创建 用户定额人材机
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async appendUserResource(args)
  {
      let {constructId, unitId, prevDeRowId,userResource} = args;
      let index;
      let prevDeRow;
      let parentId;
      if(ObjectUtil.isNotEmpty(prevDeRowId))
      {
        prevDeRow = ProjectDomain.getDomain(constructId)
        .deDomain
        .getDeById(prevDeRowId)        
        if(ObjectUtil.isEmpty(prevDeRow)){
          return ResponseData.fail("未选中定额行");
        }

        if (
          [
            DeTypeConstants.DE_TYPE_DEFAULT,
            DeTypeConstants.DE_TYPE_FB,
            DeTypeConstants.DE_TYPE_ZFB,
          ].includes(prevDeRow.type)
        ) {
          parentId = prevDeRowId
        } else {
          index = prevDeRow.index + 1
          parentId = prevDeRow.parentId
        }
      }
      else
      {
          parentId = userResource.parentId;
      }
      let deRowModel = new StandardDeModel(constructId,unitId,Snowflake.nextId(),parentId,DeTypeConstants.DE_TYPE_USER_RESOURCE);
      await ProjectDomain.getDomain(constructId).deDomain.createDeRow(deRowModel,index);
      userResource.deRowId = deRowModel.sequenceNbr;
      userResource.deId = deRowModel.sequenceNbr;
      await ProjectDomain.getDomain(constructId).deDomain.appendUserResource(constructId, unitId, userResource.deRowId, userResource);
      await ProjectDomain.getDomain(constructId).deDomain.extendQuantity(constructId, unitId, userResource.deRowId)
      return ResponseData.success(true);
  }

    /**
     * 创建用户定额
     * @param args
     */
    async appendUserDeNextRow(args)
    {
        let {constructId, unitId,prevDeRowId,userDe} = args;
        let index;
        let prevDeRow;
        let parentId;
        if(ObjectUtil.isNotEmpty(prevDeRowId))
        {
          prevDeRow = ProjectDomain.getDomain(constructId)
          .deDomain
          .getDeById(prevDeRowId)
          if(ObjectUtil.isEmpty(prevDeRow)){
            return ResponseData.fail("未选中定额行");
          }

          if (
            [
              DeTypeConstants.DE_TYPE_DEFAULT,
              DeTypeConstants.DE_TYPE_FB,
              DeTypeConstants.DE_TYPE_ZFB,
            ].includes(prevDeRow.type)
          ) {
            parentId = prevDeRowId
          } else {
            index = prevDeRow.index + 1
            parentId = prevDeRow.parentId
          }
        }

        let deRowModel = new StandardDeModel(constructId,unitId,Snowflake.nextId(),parentId,DeTypeConstants.DE_TYPE_USER_DE);
        await ProjectDomain.getDomain(constructId).deDomain.createDeRow(deRowModel,index);
        await ProjectDomain.getDomain(constructId).deDomain.appendUserDe(constructId,unitId,deRowModel.sequenceNbr,userDe);
        return ResponseData.success(true);
    }

  /**
   * 批量插入定额
   * @param {*} args 
   * @returns 
   */
  async batchCreateDeRowAppendBaseDe(args){
    let {deRow,baseDeIds} = args;
    let index;
    let prevDeRow;
    let deDomain = ProjectDomain.getDomain(deRow.constructId).deDomain;
    let isFirstEmpty = false;
    if(ObjectUtil.isNotEmpty(deRow.prevRowId))
    {
        prevDeRow = deDomain.getDeById(deRow.prevRowId);
        if(ObjectUtil.isEmpty(prevDeRow)){
          return ResponseData.fail("未选中定额行");
        }
        if(prevDeRow.type === DeTypeConstants.DE_TYPE_EMPTY){
          isFirstEmpty = true;
        }
        index = prevDeRow.index + 1;
    }
    let results = [];
    for(let baseDeId of baseDeIds){
      
      if(isFirstEmpty){
        //如果是第一个定额，且前面是空行，则不需要插入空行
        let curDeRow = await deDomain.appendBaseDe(deRow.constructId, deRow.unitId, baseDeId, prevDeRow.sequenceNbr,true,true);
        await deDomain.extendQuantity(deRow.constructId, deRow.unitId, curDeRow.sequenceNbr);
        isFirstEmpty = false;
        results.push(curDeRow);
        continue;
      }
      let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
      await deDomain.createDeRow(deRowModel,index);
      let result = await deDomain.appendBaseDe(deRowModel.constructId,deRowModel.unitId,baseDeId,deRowModel.sequenceNbr,true,true)
      results.push(result);
      index ++;
    }
    return  ResponseData.success(results);
  }

  /**
   * 批量插入人材机
   * @param {*} args 
   * @returns 
   */
  async batchCreateDeRowAppendBaseResource(args){
    let {deRow,resourceIds} = args;
    let index;
    let prevDeRow;
    let deDomain = ProjectDomain.getDomain(deRow.constructId).deDomain;
    
    //插入明细区数据
    if(deRow.type === DeTypeConstants.SUB_DE_TYPE_DE){
      prevDeRow = deDomain.getDeById(deRow.prevRowId);
      if(ObjectUtil.isEmpty(prevDeRow)){
        return ResponseData.fail("未选中定额行");
      }
      let results = [];
      for(let resourceId of resourceIds){
        let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: resourceId});
        if (ObjectUtil.isNotEmpty(baseRCJ) && ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
            return ResponseData.fail("错误，存在循环引用！");
        }
        let result = await this.service.gongLiaoJiProject.gljRcjService.addRcjData(deRow.prevRowId, baseRCJ, deRow.constructId, null, deRow.unitId,deRow.prevRowId,null, {});
        results.push(result);
      }
      return ResponseData.success([prevDeRow]);
    }
    let isFirstEmpty = false;
    if(ObjectUtil.isNotEmpty(deRow.prevRowId))
    {
        prevDeRow = deDomain.getDeById(deRow.prevRowId);
        if(ObjectUtil.isEmpty(prevDeRow)){
          return ResponseData.fail("未选中定额行");
        }
        if(prevDeRow.type === DeTypeConstants.DE_TYPE_EMPTY){
          isFirstEmpty = true;
        }

        index = prevDeRow.index + 1;
    }
    let results = [];
    for(let resourceId of resourceIds){
      let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: resourceId});
      if (ObjectUtil.isNotEmpty(baseRCJ) && ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
          return ResponseData.fail("错误，存在循环引用！");
      }

      if(isFirstEmpty){
        //如果是第一个人材机，且前面是空行，则不需要插入空行
        let curDeRow = await deDomain.appendDeResource(deRow.constructId,deRow.unitId,resourceId, prevDeRow.sequenceNbr);
        await deDomain.extendQuantity(deRow.constructId, deRow.unitId, curDeRow.sequenceNbr);
        results.push(curDeRow);
        isFirstEmpty = false;
        continue;
      }

      let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
      await deDomain.createDeRow(deRowModel,index);
      let result = await deDomain.appendDeResource(deRowModel.constructId,deRowModel.unitId,resourceId,deRowModel.sequenceNbr);
      results.push(result);
      index ++;
    }
    return  ResponseData.success(results);
  }
  /**
   * 插入标准定额
   * @returns {Promise<ResponseData>}
   * @param args
   */
  async createDeRowAppendBaseDe(args)
  {
    let {deRow,baseDeId} = args;
    let index;
    let prevDeRow;
    let deDomain = ProjectDomain.getDomain(deRow.constructId).deDomain;
    if(ObjectUtil.isNotEmpty(deRow.prevRowId))
    {
        prevDeRow = deDomain.getDeById(deRow.prevRowId);
        if(ObjectUtil.isEmpty(prevDeRow)){
          return ResponseData.fail("未选中定额行");
        }

        index = prevDeRow.index + 1;
    }

    let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
    await deDomain.createDeRow(deRowModel,index);
    let result = await deDomain.appendBaseDe(deRowModel.constructId,deRowModel.unitId,baseDeId,deRowModel.sequenceNbr,true,true)
    return  ResponseData.success(DeDomain.filter4DeTree(result));
  }


  /**
   * 插入标准人材机
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async createDeRowAppendBaseResource(args)
  {
    let {deRow,resourceId} = args;
    //插入明细区数据
    if(deRow.type === DeTypeConstants.SUB_DE_TYPE_DE){
       let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: resourceId});
      if (ObjectUtil.isNotEmpty(baseRCJ) && ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
          return ResponseData.fail("错误，存在循环引用！");
      }
      let result = await this.service.gongLiaoJiProject.gljRcjService.addRcjData(deRow.prevRowId, baseRCJ, deRow.constructId, null, deRow.unitId,deRow.prevRowId,null, {});
      if (ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.code)) {
        return result
      }
      return ResponseData.success(result.deRowId);

    }
    let index;
    let prevDeRow;
    if(ObjectUtil.isNotEmpty(deRow.prevRowId))
    {
        prevDeRow = ProjectDomain.getDomain(deRow.constructId).deDomain.getDeById(deRow.prevRowId);
        
        if(ObjectUtil.isEmpty(prevDeRow)){
          return ResponseData.fail("未选中定额行");
        }

        index = prevDeRow.index + 1;
    }

      let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: resourceId});
      if (ObjectUtil.isNotEmpty(baseRCJ) && ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
          return ResponseData.fail("错误，存在循环引用！");
      }

    let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
    await ProjectDomain.getDomain(deRow.constructId).deDomain.createDeRow(deRowModel,index);
    return  ResponseData.success(DeDomain.filter4DeTree(await ProjectDomain.getDomain(deRowModel.constructId).deDomain.appendDeResource(deRowModel.constructId,deRowModel.unitId,resourceId,deRowModel.sequenceNbr)));
  }

  /**
   * 更新取费专业
   * @param args
   * @returns {Promise<ResponseData>}
   */
 async updateChargingDiscipline(args)
  {
    let {constructId,unitId,deRowId,costFileCode,costMajorName} = args;
    return ResponseData.success(DeDomain.filter4DeTree(await ProjectDomain.getDomain(constructId).deDomain.updateChargingDiscipline(constructId,unitId,deRowId,costFileCode,costMajorName)));
  }

  /**
   * 预算书中定额替换
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async appendBaseDe2De(args) {
    let { constructId, unitId, deStandardId, deRowId ,isDe} = args;
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;

    //替换查询原来定额的章节
    let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRowId);
    let oldclasslevel02 = de.classlevel02;

    let deRow ;
    if(ObjectUtil.isEmpty(isDe) || isDe === CommonConstants.COMMON_YES)
    {
      deRow = await deDomain.appendBaseDe(constructId, unitId, deStandardId, deRowId,true,true);
    }
    else
    {
        let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: deStandardId});
        if (ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
            return ResponseData.fail("错误，存在循环引用！");
        }
      deRow = await deDomain.appendDeResource(constructId, unitId, deStandardId, deRowId);
    }

    await deDomain.extendQuantity(constructId, unitId, deRow.sequenceNbr);
    return ResponseData.success(deRow);
  }

  /**
   * 创建用户定额
   * @param args
   */
  async appendUserDe(args)
  {
    let {constructId,unitId,userDe,deRowId} = args;
    let deRow = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRowId);
    userDe.deRowId = deRowId;
    await ProjectDomain.getDomain(constructId).deDomain.appendUserDe(constructId,unitId,deRowId,userDe);
    await ProjectDomain.getDomain(constructId).deDomain.extendQuantity(constructId, unitId, deRow.sequenceNbr);
    return ResponseData.success(true);
  }

  /**
   * 获取所有定额
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllDe(args) {
    let { constructId, unitId} = args;
    let deList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
    return ResponseData.success(deList);
  }

  /**
   * 获取定额级人材机
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllDeLevelRcj(args) {
    let { constructId, unitId} = args;
    let deList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_RESOURCE);
    return ResponseData.success(deList);
  }

    /**
     * 通过单位工程获取定额
     * @param args
     * @returns {ResponseData}
     */
  getDeTree4Unit(args)
  {
    let {constructId,unitId} = args;
    return  ResponseData.success(ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId));
  }


  /**
   * 整理子目
   * types=[1,2,3,4] 1 为按取费专业， 2 按章  3 按节  4 删除自定义
   * @param {*} args 
   */
  async arrangeDe(args){
    let {constructId,unitId,types} = args;
    if(ObjectUtil.isEmpty(constructId) || ObjectUtil.isEmpty(unitId) || ObjectUtil.isEmpty(types) || !Array.isArray(types)){
      return  ResponseData.fail("传参错误")
    }
    let result = await ProjectDomain.getDomain(constructId).deDomain.arrangeDe(unitId, types);
    if(result === 200){
      //按照费用记取 整理子目 规则变化
      // let jiquFlag = await this.service.gongLiaoJiProject.gljAZservice.checkJiquMethod(constructId, unitId);
      // if(jiquFlag === 1){
        // await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeRepeat(constructId, unitId);
        await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
          unitId: unitId,
          singleId: null,
          constructId: constructId
        });
      // }
      return ResponseData.success(result);
    }
    return  ResponseData.success(result);
  }
  //有按照记取定额整理子目后的处理
  async arrangeAZDe(args){
    let {constructId,unitId,type} = args;
    if(type === 1){ 
      await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeRepeat(constructId, unitId);
    }
    if(type === 0){
      let anZhDes = ProjectDomain.getDomain(constructId).deDomain.getDes(item=>item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtil.isEmpty(item.calculateMethod));
      for(let de of anZhDes){
        await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(de.sequenceNbr);
      }
      await this.service.gongLiaoJiProject.gljAZservice.updateCaches(constructId, unitId);
    }

    return ResponseData.success();
  }
  /**
   * 获取子目整理配置
   * @param {*} args 
   * @returns 
   */
  async getArrangeConfig(args){
    let {constructId,unitId} = args;
    //保存整理类型
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    if(ObjectUtil.isEmpty(deDomain)){
      return ResponseData.fail("参数有误");
    }
    let objMap = deDomain.functionDataMap.get(FunctionTypeConstants.YSH_TABLELIST);
    if(ObjectUtil.isEmpty(objMap)){
      return ResponseData.success();
    }
    let types = objMap.get(FunctionTypeConstants.YSH_TABLELIST_ARRANGE+unitId);
    if(ObjectUtil.isEmpty(types)){
      return ResponseData.success();
    }
    return ResponseData.success(types);
  }
  /**
   * 子目排序
   * @param {*} args 
   */
  async sortDe(args){
    let {constructId,unitId} = args;
    let projectDomain = ProjectDomain.getDomain(constructId);
    let deDomain = projectDomain.deDomain;
    let unitProject  = projectDomain.getProjectById(unitId);
    await deDomain.sortDe(unitId,unitProject.constructMajorType);
    return ResponseData.success();
  }

    /**
     * 通用更新定额
     * @param args
     * @returns {ResponseData}
     */
  async updateDe(args)
  {
    let {deRow} = args;
    // let newModel = new StandardDeModel(deRow.constructId,deRow.unitId,deRow.sequenceNbr,deRow.parentId,deRow.type);
    // copyProperties(deRow,newModel,["children","index"]);

    let deDomain = ProjectDomain.getDomain(deRow.constructId).deDomain;
    await deDomain.updateDe(deRow,true,true);
    //顶顶顶顶顶顶顶顶顶顶
    return ResponseData.success();
  }

   _getChildDes(deDomain,de,childList)
    {
      let curDeRow = deDomain.getDe(item=>item.sequenceNbr=== de.sequenceNbr&&item.unitId=== de.unitId);
      if(curDeRow.children && curDeRow.children.length > 0){
        let firstChild = curDeRow.children[0];
        if(firstChild.type === DeTypeConstants.DE_TYPE_FB 
          || firstChild.type === DeTypeConstants.DE_TYPE_ZFB){
            for(let child of curDeRow.children){
              this._getChildDes(deDomain, child, childList);
            }
          }else{
            childList.push(...curDeRow.children);
          }
      }
    }

  /**
   *
   * @param args
   * @returns {ResponseData}
   */
  async getDeDepth(args)
  {
    let {constructId,unitId,deRowId,types,isShowAnnotations,posId} = args;
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    if (ObjectUtil.isNotEmpty(isShowAnnotations)) {
        if (isShowAnnotations === false) {
            //如果隐藏所有批注，将所有批注改为fasle
            deDomain.updateDeIsShowAnnotations(unitId, isShowAnnotations);
        }
    }

    if(ObjectUtil.isNotEmpty(types))
    {
      types = types.split(",");
    }
    let deLists = deDomain.getDeTreeDepth(constructId,unitId,deRowId,types,posId)
    //重置序号
    if(ObjectUtil.isNotEmpty(deLists)){
      let index = 1;
      let sets = new Set();
      //处理主材或设备
      let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
      deLists.forEach(element => {
        if(element.type === DeTypeConstants.DE_TYPE_DEFAULT
          || element.type === DeTypeConstants.DE_TYPE_FB
          || element.type === DeTypeConstants.DE_TYPE_ZFB
        ){
          sets.add(element.sequenceNbr);
        }
        if(element.type === DeTypeConstants.SUB_DE_TYPE_DE){
          element.quantity = NumberUtil.numberFormat(element.quantity,precision.DETAIL.RCJ.totalNumber);
        }
      });

      let type04CloseList = [];

      deLists.forEach(element => {
        //数据单位为%/元处理单位
        if(ObjectUtils.isNotEmpty(element.isCostDe)
          && element.isCostDe !== CostDeMatchConstants.NON_COST_DE 
          && (element.unit == '%'||element.unit == '元')) {
            element.unit = DeTypeConstants.DE_UNIT_LABEL;
            deDomain.updateUnit(constructId, unitId, element.sequenceNbr,  DeTypeConstants.DE_UNIT_LABEL);
        }
        // 人材机下拉修改类型
        if (ObjectUtils.isNotEmpty(element.deResourceKind)) {
          this.service.gongLiaoJiProject.gljBaseRcjService.typeListByKind(element);
        }
        //对隐藏数据进行数据统计
        if(element.displaySign === BranchProjectDisplayConstant.close 
          && (element.type === DeTypeConstants.DE_TYPE_FB || element.type === DeTypeConstants.DE_TYPE_ZFB)){
          let childList = [];
          this._getChildDes(deDomain,element, childList);
          childList.forEach(child => {index++;});
        }

        if(element.type !== DeTypeConstants.DE_TYPE_DEFAULT
          && element.type !== DeTypeConstants.DE_TYPE_FB
          && element.type !== DeTypeConstants.DE_TYPE_ZFB
          && sets.has(element.parentId)
        ){
          element.dispNo = index;
          index++;
        }

          if (ObjectUtil.isEmpty(element.isShowAnnotations)) {
              element.isShowAnnotations = false;
          }

          if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && (ObjectUtil.isEmpty(element.displaySign) || element.displaySign === BranchProjectDisplayConstant.noSign)) {
              //如果定额无箭头，则展开
              let filter = deLists.filter(o => o.parentId === element.sequenceNbr);
              if (ObjectUtil.isNotEmpty(filter)) {
                  element.displaySign = BranchProjectDisplayConstant.open;
              }
          }

          if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && element.displaySign === BranchProjectDisplayConstant.close) {
              //如果定额收起，则不展示05类型
              type04CloseList.push(element.deRowId);
          }

          //人材机定额，费用人材机单位显示元
          if(element.isFyrcj == 0){
              element.unit = "元";
          }
      });

      if(ObjectUtil.isNotEmpty(type04CloseList)){
          deLists = deLists.filter(p=>ObjectUtil.isEmpty(p.parentId) || !type04CloseList.includes(p.parentId));
      }
    }
    // 分部升降级
    await this.service.gongLiaoJiProject.gljDeService.fbUpAndDown(constructId, unitId, deLists);
    return ResponseData.success(deLists);
  }

    /**
     * 获取所有定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getDeAllDepth(args) {
        let {constructId, unitId, deRowId} = args;
        let deLists = await this.service.gongLiaoJiProject.gljDeService.getDeAllDepth(constructId, unitId, deRowId);
        //处理主材或设备
        if(ObjectUtil.isNotEmpty(deLists)){
          let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
          deLists.forEach(element => {
            //数据单位为%/元处理单位
            if(ObjectUtils.isNotEmpty(element.isCostDe)
              && element.isCostDe !== CostDeMatchConstants.NON_COST_DE 
              && (element.unit == '%'||element.unit == '元')) {
                element.unit = DeTypeConstants.DE_UNIT_LABEL;
            }
            if(element.type === DeTypeConstants.SUB_DE_TYPE_DE){
              // element.resQty = NumberUtil.numberFormat(element.resQty,precision.DETAIL.RCJ.resQty);
              element.quantity = NumberUtil.numberFormat(element.quantity,precision.DETAIL.RCJ.totalNumber);
            }
          });
        }
        return ResponseData.success(deLists);
    }
  /**
   *删除定额行
   * @param args
   * @returns {ResponseData}
   */
  async removeDeRow(args)
  {
      let {constructId, deRowId, isDelAll, isRemoveRelationDe} = args;
      let newVar;
      if (isDelAll === false) {
          newVar = await this.service.gongLiaoJiProject.gljDeService.delFb(constructId, deRowId);
      } else {
          newVar = await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(deRowId);
      }
      if (isRemoveRelationDe === true) {
          let relationDeList = await ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.fDeId === deRowId && item.isRelationDe === true && String(item.quantityExpression).includes('GCL') && !String(item.quantityExpression).includes('GCLMXHJ'));
          for (let relationDe of relationDeList) {
              await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(relationDe.sequenceNbr);
          }
      }
      return ResponseData.success(newVar);
  }
  /**
   * 临时删除定额行
   * @param args
   * @returns {ResponseData}
   */
  async tempRemoveDeRow(args)
  {
    let {constructId,idList,deRowId,deList} = args;
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    let zcsbRcjs = [];
    //临时删除人材机还是定额，用是否传了定额id来处理
    if(ObjectUtil.isEmpty(deRowId)){
      for(let de of deList){
        if(de.type == DeTypeConstants.SUB_DE_TYPE_DE){ //不存在与定额，假设为人材机
          zcsbRcjs.push(de);
          continue;
        }
        await deDomain.tempRemoveDeRow(de.id,true);
      }
    }else{
      for(let id of idList){
        await this.service.gongLiaoJiProject.gljRcjService.tempRemoveRcjRow(constructId,deRowId,id);
      }
    }
    //处理虚拟定额/主材设备
    if(ObjectUtils.isNotEmpty(zcsbRcjs)){

      for(let rcj of zcsbRcjs){
        //存在父级无需处理主材设备
        let de = deList.find(item=>item.id == rcj.parentId);
        if(ObjectUtil.isNotEmpty(de)){
            continue;
        }
        await this.service.gongLiaoJiProject.gljRcjService.tempRemoveRcjRow(constructId,rcj.parentId,rcj.id);
      }
    }
    return ResponseData.success();
  }
   /**
   * 取消临时删除定额行
   * @param args
   * @returns {ResponseData}
   */
   async cancelTempRemoveDeRow(args)
   {
     let {constructId,idList,deRowId,deList} = args;
     let deDomain = ProjectDomain.getDomain(constructId).deDomain;
     let zcsbRcjs = [];
     //临时删除人材机还是定额，用是否传了定额id来处理
    if(ObjectUtil.isEmpty(deRowId)){
      for(let de of deList){
        if(de.type == DeTypeConstants.SUB_DE_TYPE_DE){ //不存在与定额，假设为人材机
          zcsbRcjs.push(de);
          continue;
        }
        await deDomain.cancelTempRemoveDeRow(de.id);
      }
    }else{
      for(let id of idList){
        await this.service.gongLiaoJiProject.gljRcjService.cancelTempRemoveRcjRow(constructId,deRowId,id);
      }

    }
    //处理虚拟定额/主材设备
    if(ObjectUtils.isNotEmpty(zcsbRcjs)){

      for(let rcj of zcsbRcjs){
        //存在父级无需处理主材设备
        let de = deList.find(item=>item.id == rcj.parentId);
        if(ObjectUtil.isNotEmpty(de)){
            continue;
        }
        await this.service.gongLiaoJiProject.gljRcjService.cancelTempRemoveRcjRow(constructId,rcj.parentId,rcj.id);
      }
    }
     return ResponseData.success();
   }
   /**
   * 取消临时删除定额行
   * @param args
   * @returns {ResponseData}
   */
   async batchCancelTempRemoveDeRow(args)
   {
     let {constructId,unitId} = args; 
     let unitProjects = [];
     if(ObjectUtil.isNotEmpty(unitId)){
        let unitProject = await ProjectDomain.getDomain(constructId).getProjectById(unitId);
        unitProjects.push(unitProject);
     }else{
        unitProjects = await ProjectDomain.getDomain(constructId).getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
     }
     let allData = await ProjectDomain.getDomain(constructId).deDomain.batchCancelTempRemoveDeRow(constructId,unitProjects)
     let checkOperate = true; //
     if(ObjectUtil.isNotEmpty(allData.de)){
      for(let id of allData.de){
        checkOperate = false;
        await ProjectDomain.getDomain(constructId).deDomain.cancelTempRemoveDeRow(id); 
      }
     }
     if(ObjectUtil.isNotEmpty(allData.rcj)){
      for(let rcjDetail of allData.rcj){
        checkOperate = false;
        await this.service.gongLiaoJiProject.gljRcjService.cancelTempRemoveRcjRow(constructId,rcjDetail.deRowId,rcjDetail.sequenceNbr);
      } 
     }
     if(checkOperate){
      return  ResponseData.fail("未找到临时删除子目");
     }
     return ResponseData.success();
   }
   /**
   * 批量删除临时删除定额行
   * @param args
   * @returns {ResponseData}
   */
   async realTempRemoveDeRow(args)
   {
    //因为临时删除已经处理了，避免重复通知
     let {constructId,unitId} = args; 
     let unitProjects = [];
     if(ObjectUtil.isNotEmpty(unitId)){
        let unitProject = await ProjectDomain.getDomain(constructId).getProjectById(unitId);
        unitProjects.push(unitProject);
     }else{
        unitProjects = await ProjectDomain.getDomain(constructId).getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
     }
     let allData = await ProjectDomain.getDomain(constructId).deDomain.realTempRemoveDeRow(constructId,unitProjects);
     let checkOperate = true; //

     if(ObjectUtil.isNotEmpty(allData.de)){
      for(let deRowId of allData.de){
        checkOperate = false;
        await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(deRowId,false)
      }
     }
     if(ObjectUtil.isNotEmpty(allData.rcj)){
      for(let rcjDetail of allData.rcj){
        checkOperate = false;
        await this.service.gongLiaoJiProject.gljRcjService.deleteRcjByCodeData(rcjDetail.deRowId,constructId,rcjDetail.unitId,rcjDetail.sequenceNbr,false,{});
      } 
     }
     if(checkOperate){
      return  ResponseData.fail("未找到临时删除子目");
     }
     return ResponseData.success();
   }
    /**
     * 初始化定额行
     * @param args
     * @returns {ResponseData}
     */
  initDefaultDE(args)
  {
    let {constructId,unitId} = args;
    return ResponseData.success(ProjectDomain.getDomain(constructId).deDomain.initDefaultDE(constructId,unitId));
  }


    /**
     * 通过编码插入定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
 async queryDeAndAppendDe(args)
  {
    let {constructId,unitId,deCode,deRowId} = args;
    
    if(ObjectUtil.isNotEmpty(deCode)){
      deCode = deCode.toUpperCase();
    }
    let res = await ProjectDomain.getDomain(constructId).deDomain.getDesAndAppendDe(constructId, unitId, deCode, deRowId);
    return ResponseData.success(res);
  }
  /**
     * 通过编码查询03定额是否存在
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async checkAndQueryDe(args)
  {
    let {constructId,unitId,deCode} = args;
    
    if(ObjectUtil.isNotEmpty(deCode)){
      deCode = deCode.toUpperCase();
      if(deCode.indexOf('#')){
        deCode = deCode.split('#')[0];
      }
    }
    let projectDomain = ProjectDomain.getDomain(constructId);
    let deBaseDomain = projectDomain.getDeDomain();
    let data = await deBaseDomain.checkAndQueryDe(constructId, unitId, deCode,projectDomain.functionDataMap);

    if(ObjectUtil.isEmpty(data.local)&&(ObjectUtil.isEmpty(data.db)||(ObjectUtil.isNotEmpty(data.db)&&data.db.length<=1))){
      return  ResponseData.fail();
    }
    if(data.local.length === 1 && ObjectUtil.isEmpty(data.db)){
      return  ResponseData.fail();
    }
    //判断如果//编码-名称-单位-单价  一样不返回
    if(data.local.length === 1 && data.db.length === 1){
      let localDe = data.local[0];
      let dbDe = data.db[0];
      if(localDe.deCode === dbDe.deCode && localDe.deName === dbDe.deName && localDe.displayType == dbDe.displayType && localDe.unit === dbDe.unit
        && localDe.price === dbDe.price
      ){
        return  ResponseData.fail();
      }
    }
    return ResponseData.success(data);
  }
 /**
   * 增加de颜色
   * @param arg
   * @return {Promise<void>}
   */
 async setDeColor(args){
  let {constructId,deRowId,color} = args;
  if(ObjectUtil.isEmpty(deRowId)){
    return ResponseData.fail("未选择定额行");
  }
  for(let deId of deRowId){
    let deRow = ProjectDomain.getDomain(constructId).deDomain.getDe(item=>item.sequenceNbr===deId);
    if(ObjectUtil.isEmpty(deRow)){
        deRow = ProjectDomain.getDomain(constructId).csxmDomain.getDe(item=>item.sequenceNbr===deId);
    }
    if(ObjectUtil.isEmpty(deRow)){
        return ResponseData.fail("找不到定额行");
    }
    deRow.color = color;
  }
  return ResponseData.success();
}

  /**
   * 编码重复选择定额处理
   */
  async selectDe(args){
    let { constructId, unitId, deStandardId, deRowId, deRow} = args;
    if(ObjectUtil.isEmpty(deStandardId) || ObjectUtil.isEmpty(deRowId)){
      return ResponseData.fail("请选择定额");
    }
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    let deRowModel = null;

    if(ObjectUtil.isNotEmpty(deRowId) && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)){
      
      let deRowCode = deRow.deCode;
      let baseRCJDetail = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:deRow.standardId});

      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE &&ObjectUtil.isNotEmpty(baseRCJDetail)){
        deRowModel = await deDomain.appendDeResource(constructId, unitId, deRow.standardId, deRowId);
      }else{
        let userDeBases = deDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
        if(ObjectUtil.isNotEmpty(userDeBases)) {

          let resultUs = userDeBases.filter(item => DeTypeCheckUtil._fixCode(item.materialCode) === DeTypeCheckUtil._fixCode(deRowCode) && item.unitId === unitId);
          if (ObjectUtil.isNotEmpty(resultUs)) {
            if(resultUs.length > 1){
              resultUs.sort((a,b)=>b.updateDate-a.updateDate);
            }
            let result = resultUs[0];
            let newResult = ConvertUtil.deepCopy(result);
            newResult.unitId=unitId;
            newResult.deId=deRowId;
            newResult.deRowId=deRowId;
            newResult.sequenceNbr=null;
            newResult.parentId = null;
            deRowModel = await deDomain.appendUserResource(constructId, unitId, deRowId, newResult, false);
          }
        }
        if(ObjectUtil.isEmpty(deRowModel)){
          //查询所有定额的数据
          let de = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item=>item.sequenceNbr === deStandardId);
          if(ObjectUtil.isNotEmpty(de)){
            deRowModel = await deDomain.appendBaseDeByLocal(constructId, unitId, de, deRowId,false);
          }
        }
      }
      
      if(deRowCode.indexOf('#') > -1){
        
        let unitAllMemory = await this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId, unitId);
        
        if(ObjectUtil.isNotEmpty(unitAllMemory)){
          let querySequnceNbr = deRow.sequenceNbr;
          let resultCache = null;
          if(querySequnceNbr.indexOf('__')>-1){
            let querySequnceNbrs = querySequnceNbr.split('__');
            let resultCaches = unitAllMemory.filter(item=>item.sequenceNbr === querySequnceNbrs[0]);
            resultCache = resultCaches[querySequnceNbrs[1]];
          }else{
            resultCache = unitAllMemory.find(item=>item.materialCode === deRowCode);
          }
          let rcjDeKey = WildcardMap.generateKey(unitId, deRowModel.sequenceNbr) + WildcardMap.WILDCARD;
          let rcjs =  deDomain.ctx.resourceMap.getValues(rcjDeKey);
          let deRcj = rcjs.find(item=>item.isDeResource === CommonConstants.COMMON_YES);
          PropertyUtil.copyProperties(resultCache,deRcj,["sequenceNbr","deRowId","deId","parentId","resQty","originalQty"]);
          deRcj.isDeResource = CommonConstants.COMMON_YES;
          deRowModel.deCode = deRcj.materialCode;
          deRowModel.deName = deRcj.materialName;
          deRowModel.unit = deRcj.unit;
          deRowModel.deResourceKind = deRcj.kind;
          deRowModel.specification = deRcj.specification;
          deRowModel.ifLockStandardPrice = deRcj.ifLockStandardPrice;
          deRowModel.markSum = deRcj.markSum;
          deDomain.notify(deRowModel,false);
        }
      } 
      
    }else{
      //替换查询原来定额的章节
      let de = deDomain.getDe(item=>item.sequenceNbr === deStandardId);
      //尝试从用户定额缓存中获取
      if(ObjectUtil.isEmpty(de)){
        let userDes = deDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
        if(ObjectUtil.isNotEmpty(userDes)){
          let result = userDes.find(item=>item.sequenceNbr === deStandardId && item.unitId === unitId);
          if(ObjectUtil.isNotEmpty(result)){
            await deDomain.appendUserDe(constructId, unitId, deRowId, result,false);
            deRowModel = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRowId);
          }
        }
      }
      if(ObjectUtil.isEmpty(deRowModel)){
        
        //尝试从其他地方查询定额
        if(ObjectUtil.isEmpty(de))
        {
            de = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item=>item.sequenceNbr === deStandardId);
        }
        if(ObjectUtil.isEmpty(de))
        {
          deRowModel = await deDomain.appendBaseDe(constructId, unitId, deStandardId, deRowId,true,true);
        }else{
          deRowModel = await deDomain.appendBaseDeByLocal(constructId, unitId, de, deRowId,false);
        }
      }
    }
    await deDomain.extendQuantity(constructId, unitId, deRowModel.sequenceNbr);
    return ResponseData.success(DeDomain.filter4DeTree(deRowModel));
  }
  _
  /**
   * 替换功能过滤 定额数据
   * @param {*} args 
   */
  async selectReplaceDe(args){
    let {constructId,unitId,deCode,deName,operate} = args;
    if(ObjectUtil.isNotEmpty(deCode)){
      deCode = deCode.toUpperCase();
    }
    let projectDomain =ProjectDomain.getDomain(constructId);
    let deDomain = projectDomain.deDomain;
    //按范围查询所有数据
    let deList = [];
    let unitIds = [];
    if(ObjectUtil.isEmpty(unitId)){
      let projects = projectDomain.getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
      for(let project of projects){
        unitIds.push(project.sequenceNbr);
      }
    }else{
      unitIds.push(unitId);
    }
    for(let unitIdItem of unitIds){
      
      let deAllList = deDomain.getDeAllTreeDepth(constructId,unitIdItem,null,null);
      let deFbList = deDomain.getFbTreeDepth(constructId,unitIdItem,null,[DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
      for(let de of deAllList){
        if(![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(de.type)){
          let fbItem = deFbList.find(item=>item.sequenceNbr === de.parentId);
          if(ObjectUtil.isNotEmpty(fbItem)){
            de.unitId = unitIdItem//  防止unitId不一致的情况，？？？
            deList.push(de);
          }
        }
      }
    }

    //分别按照名称及编码过滤数据，，operate 默认为 and
    if(ObjectUtil.isNotEmpty(deCode)){
      if(ObjectUtil.isNotEmpty(deName)){
        if(operate === 'or'){
          deList = deList.filter(item=>item.deCode === deCode || (ObjectUtil.isNotEmpty(item.deName) && item.deName.indexOf(deName)>-1));
        }else{
          deList = deList.filter(item=>item.deCode === deCode && ObjectUtil.isNotEmpty(item.deName) && item.deName.indexOf(deName)>-1);
        }
      }else{
        deList = deList.filter(item=>item.deCode === deCode);
      }
    }else{
      if(ObjectUtil.isNotEmpty(deName)){
        deList = deList.filter(item=> ObjectUtil.isNotEmpty(item.deName) && item.deName.indexOf(deName)>-1);
      }
    }

    //虚拟两个父类定额，一 单位 二 分部
    
    let parentDeMap = new Map();

    deList.forEach(item=>{
      let itemKey = item.unitId +"-"+ item.parentId;
      if(!parentDeMap.has(itemKey)){
        parentDeMap.set(itemKey,item);
      }
    })
    let unitSet = new Set();
    let addDeList = []
    //重置Map中的对象
    for(let [key,value] of parentDeMap.entries()){
      //虚拟化一个父级定额  id 为 item的parentid  parentIdw为unitId
      let parentDe = new StandardDeModel(value.constructId,value.unitId,value.parentId,value.unitId,DeTypeConstants.DE_TYPE_DEFAULT);
      let root = deDomain.getRoot(value.unitId);
      let parentDeName = "";
      if(root.sequenceNbr === value.parentId){
        parentDeName  = root.name;
        parentDe.index = (value.index+"").padStart(6, "0");
      }else{
        let parentList = [];//铁定有父级，所有此处这样处理也没有问题        
        deDomain.newFindParents(value,parentList,[DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB])
        let fbIndex  = "";
        for(let i = parentList.length-1;i>=0;i--){
          if(parentList[i].type === DeTypeConstants.DE_TYPE_DEFAULT){
            parentDeName += (parentList[i].name + "\\");
          }else{
            let tempDeName = parentList[i].deName;
            if(ObjectUtil.isEmpty(tempDeName)){
              tempDeName = "";
            }
            parentDeName += (tempDeName + "\\");
          }
          let tempIndex=parentList[i].index;
          if(ObjectUtil.isEmpty(tempIndex)){
            tempIndex = "0";
          }
          fbIndex += ((tempIndex+"").padStart(6, "0")+"-");
        }
        parentDe.index = fbIndex + "-" + (value.index+"").padStart(6, "0");
      }
      parentDe.type = DeTypeConstants.DE_TYPE_ZFB
      parentDe.deName = parentDeName;
      addDeList.push(parentDe);
      unitSet.add(value.unitId);      
    }

    for(let unitIdItem of unitSet){
      let parentList = [];
      let curProject = projectDomain.getProjectById(unitIdItem);
      projectDomain.findParents(curProject,parentList,[ProjectTypeConstants.PROJECT_TYPE_SINGLE]);
      let parentDe = new StandardDeModel(constructId,unitIdItem,unitIdItem,0,DeTypeConstants.DE_TYPE_DEFAULT);
      if(ObjectUtil.isNotEmpty(parentList)){
        let parentDeName = parentList.map(item=>item.name).join("\\");
        parentDe.deName = parentDeName + "\\" + curProject.name;
        parentDe.index = parentList.map(item=>(item.index+"").padStart(6, "0")).join("-")+ "-" +(curProject.index+"").padStart(6, "0");
      }else{
        parentDe.deName = curProject.name;
        parentDe.index = (curProject.index+"").padStart(6, "0")
      }
      parentDe.type = DeTypeConstants.DE_TYPE_FB
      addDeList.push(parentDe);
    }
    addDeList.sort((a,b)=>{
      if(a.type > b.type){
        return 1;
      }
      if(a.type < b.type){
        return -1;
      }
      if(a.index > b.index){
        return 1;
      }
      if(a.index < b.index){
        return -1;
      }
      return 0;
    });
    //为啥
    for(let it of  addDeList){
      console.log(it.deName +"-" +it.index)
    }
    let parentList = PropertyUtil.shallowCopyAndFilterProperties(addDeList, BaseDomain.avoidProperty).map(DeDomain.filter4DeTree)
    let resultList = [...parentList,...deList];
    if(ObjectUtil.isNotEmpty(resultList)){
      let dispNoIndex = 1;
      let rootDes = resultList.filter(item=>item.type ===  DeTypeConstants.DE_TYPE_FB);
      this._resetDispNo(rootDes, dispNoIndex, resultList);
    }
    return ResponseData.success(resultList);
  }

  _resetDispNo(rootDes, index, resultList) {  
    // 遍历当前层级的所有节点  
    for (let i = 0; i < rootDes.length; i++) {  
        rootDes[i].dispNo = index; // 给当前节点添加序号  
        index ++;
        let childs = resultList.filter(item => item.parentId === rootDes[i].sequenceNbr );
        if (ObjectUtil.isNotEmpty(childs)) {
            //排序
            childs.sort((a,b)=>a.index-b.index);
            index = this._resetDispNo(childs, index, resultList); // 传入当前节点的ID作为子节点的parentId  
        }  
    }  
    return index;
} 

  async replaceDe(args){
    let {constructId,deRowId,replaceDes} = args;
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    let deRow = deDomain.getDeById(deRowId);
    if(ObjectUtil.isNotEmpty(deRow)){
      //组合复制数据
      let localDeCopy = ConvertUtil.deepCopy(deRow);//不要污染原始数据
      //人材机处理
      let rcjList = deDomain.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,localDeCopy.sequenceNbr);
      localDeCopy.rcjList = rcjList;
      //编制换算处理
      let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
      let unitConversionMap = conversionMap.get(localDeCopy.unitId);  
      localDeCopy.conversion = unitConversionMap?.get(localDeCopy.sequenceNbr);
      let relaceFlag = true;
      //循环批量处理替换数据
      for(let replaceDe of replaceDes){
        //不存在的数据过滤掉
        let replaceDeRow = deDomain.getDeById(replaceDe.sequenceNbr);
        if(ObjectUtil.isEmpty(replaceDeRow)){
          continue; 
        }
        relaceFlag = false;
        let curUnitConversionMap = unitConversionMap;
        if(replaceDe.unitId !== localDeCopy.unitId){
          curUnitConversionMap = conversionMap.get(replaceDe.unitId); 
        }
        await deDomain.appendDeByUnitDe(localDeCopy,replaceDeRow.unitId,replaceDeRow.sequenceNbr,curUnitConversionMap,true);
      }
      if(relaceFlag){
        return ResponseData.fail("替换定额已被移除，请关闭弹窗后重新选择");
      }
    }else{
      return ResponseData.fail("替换定额已被移除,请关闭弹窗后重新选择");
    }
    return ResponseData.success(); 
  }
    /**
     * 通过编码插入安装计取定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryDeAndAppendDeAz(args) {
        let {constructId, unitId, deCode, deRowId} = args;
        
        if(ObjectUtil.isNotEmpty(deCode)){
          deCode = deCode.toUpperCase();
        }
        return ResponseData.success(await ProjectDomain.getDomain(constructId).deDomain.getDesAndAppendDeAz(constructId, unitId, deCode, deRowId));
    }

    async moveUpAndDown(args){
      let{constructId,deRowIds,type} = args;
      await ProjectDomain.getDomain(constructId).deDomain.moveUpAndDown(deRowIds,type);
      return ResponseData.success();
     }
    /**
     * 二次弹框选择安装编码插入
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryDeAndAppendDeAzInsert(args) {
        let {constructId, unitId, deRowId, resourceDeRowId} = args;
        return ResponseData.success(await ProjectDomain.getDomain(constructId).deDomain.getDeAndAppendDeAzInsert(constructId, unitId, deRowId, resourceDeRowId));
    }

    /**
     * 更新定额合计
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async updateTotal(args)
  {
    let {constructId, unitId, deId, totalNumber} = args;
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    return ResponseData.success(await deDomain.updatePriceORResQtyOrTotalNumber(constructId, unitId, null,null, totalNumber));
  }

    /**
     * 更新定额消耗量
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async updateResQty(args)
  {
    let {constructId, unitId, deRowId, resQty} = args;
    try{
      eval(resQty)
    }catch(error){
      return ResponseData.fail("消耗量非数字类型");
    }
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    return ResponseData.success(await deDomain.updateResQty(constructId, unitId, deRowId,resQty));
  }

    /**
     * 更新定额工程量
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async updateQuantity(args) {
    try {
      let newVar = await this.service.gongLiaoJiProject.gljDeService.updateQuantity(args);

      return newVar;//返回值更改
      
    } catch (error) {
      return ResponseData.fail(error.message);
    }
  }
  
    /**
     * 更新定额单价
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async updatePrice(args) {
    let {constructId, unitId, deRowId, price,rSum,cSum,jSum} = args;
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    let deRow = deDomain.getDeById(deRowId);
    if(ObjectUtils.isEmpty(deRow)){
      return ResponseData.fail("定额不存在");
    }
    try{
      //单价修改按比列分配逻辑
      if(ObjectUtil.isNotEmpty(rSum) || ObjectUtil.isNotEmpty(cSum) || ObjectUtil.isNotEmpty(jSum)){
        await deDomain.updatePriceWithRCJ(constructId, unitId, deRowId,price,rSum,cSum,jSum);
      }else{
        //校验是否有人材机数据
        // if([DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_USER_DE].includes(deRow.type)){
        //   let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
        //   let rcjLists =  deDomain.ctx.resourceMap.getValues(rcjDeKey);
        //   let rcjModifiable = false;
        //   for(let item of rcjLists){
        //     if(item.isFyrcj == 0){
        //       continue;
        //     }
        //     if(item.materialCode=== DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
        //             || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
        //             || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ){ 
        //       continue;
        //     }
        //     rcjModifiable = true;
        //   }
        //   if(!rcjModifiable){
        //     return new ResponseData(500,"T001", null, 500);
        //   }
        // }

        await deDomain.updatePrice(constructId, unitId, deRowId, price);
      }

      //联动计取安装费
      await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "update");
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
    }catch(error){
      return ResponseData.fail(error.message);      
    }
    return ResponseData.success();
  }

  /**
   * 更改定额上的人材机单价
   * @param {*} args 
   * @returns 
   */
  async updateRCJPrice(args) {
    let {constructId, unitId, deRowId, price,type} = args;
    
    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    try{
      let deRow = deDomain.getDeById(deRowId);
      if(ObjectUtil.isEmpty(deRow) || deRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE
         || deRow.type === DeTypeConstants.DE_TYPE_DEFAULT
         || deRow.type === DeTypeConstants.DE_TYPE_FB
         || deRow.type === DeTypeConstants.DE_TYPE_ZFB
         || deRow.type === DeTypeConstants.DE_TYPE_EMPTY){
        return ResponseData.fail("此类型定额不允许修改单价");
      }

      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
        await deDomain.updatePrice(constructId, unitId, deRowId, price);
      }else{
        await deDomain.updateRCJPrice(constructId, unitId, deRow, price, type);
      }
      //联动计取安装费
      await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "update");
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
    }catch(error){
      return ResponseData.fail(error.message);      
    }
    return ResponseData.success();  
  }

    /**
     * 更新定额单位
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async updateUnit(args) {
        let {constructId, unitId, deRowId, unit} = args;
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        return ResponseData.success(await deDomain.updateUnit(constructId, unitId, deRowId, unit));
  }

  /**
   * 粘贴数据
   * @param {*} args 
   * @returns 
   */
  async pasteDe(args){
    let {constructId,unitId,oUnitId,prevDeRowId,idList,type} = args;
    let prevDeRow;   
    if(ObjectUtil.isEmpty(idList)){
      return ResponseData.fail("参数错误");
    }

    let deDomain = ProjectDomain.getDomain(constructId).deDomain;
    if(ObjectUtil.isNotEmpty(prevDeRowId))
    {
      prevDeRow = deDomain.getDeById(prevDeRowId);
    }
    if(ObjectUtil.isEmpty(prevDeRow)){
      return ResponseData.fail("未选择行");
    }
    let checkDeRows = [];
    let maxLevel = 0;
    for(let id of idList){
      let deRow = deDomain.getDeById(id);
      if(ObjectUtil.isNotEmpty(deRow)){
        let curLevel = this._calculateLevel(deRow,true,false);//本身检查分部层级
        maxLevel = maxLevel<curLevel?curLevel:maxLevel;
        checkDeRows.push(deRow.sequenceNbr);
      }
    }
    if(ObjectUtil.isEmpty(checkDeRows) || checkDeRows.length === 0){
      return ResponseData.fail("主材设备数据无法粘贴或原数据已删除");
    }
    //计算层级
    let allLevel = 0
    if(type === "child" || type === "childNoCircle"){
      allLevel = this._calculateLevel(prevDeRow) + maxLevel;
    }else{
      allLevel = this._calculateLevel(prevDeRow) + maxLevel -1;
    }
    if(allLevel>4){
      return ResponseData.fail("粘贴操作将导致分部的层级超过4级，请重新选择位置！");      
    }
    
    await deDomain.pasteDe(unitId, oUnitId, checkDeRows,prevDeRow,type, false);
    
    return ResponseData.success();
  }
  
  _calculateLevel(deRow,checkChild=false,checkParent = true){
    let count = 0;
    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.type)){
      count++
      if(checkChild){
        let max = 0;
        for(let child of deRow.children){
          let childLevel = this._calculateLevel(child,checkChild,false);
          max = max>childLevel?max:childLevel;
        }
        count += max;
      }
    }
    if(checkParent && ObjectUtil.isNotEmpty(deRow.parent)){
      count+=this._calculateLevel(deRow.parent);
    }
    return count;
  }


    /**
     * 展开
     */
    queryDeChildType(args) {
        let {constructId, singleId, unitId, deRowId} = args;
        let resultType = new Set();
        let deLists = ProjectDomain.getDomain(constructId).deDomain.getDeTreeDepth(constructId,unitId,deRowId,null)
        let deChildList = deLists.filter(o=>o.parentId === deRowId);
        if(ObjectUtil.isNotEmpty(deChildList)){
            deChildList.forEach(o=>{
                resultType.add(o.type);
            })
        }
        let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRowId);
        if(ObjectUtil.isNotEmpty(de) && ObjectUtil.isNotEmpty(de.children)){
            de.children.forEach(o=>{
                resultType.add(o.type);
            })
        }
        const myList = [...resultType];
        return ResponseData.success(myList);
    }


    /**
     * 展开
     */
    open(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(pointLine.sequenceNbr);
        de.displaySign = BranchProjectDisplayConstant.open;
        ProjectDomain.getDomain(constructId).deDomain.updateDe(de);
        return ResponseData.success(true);
    }

    /**
     * 折叠
     */
    close(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(pointLine.sequenceNbr);
        de.displaySign = BranchProjectDisplayConstant.close;
        ProjectDomain.getDomain(constructId).deDomain.updateDe(de);
        return ResponseData.success(true);
    }


    /**
     * 批量删除定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteDe(args) {
        let {constructId, unitId, deRowIdList} = args;
        for (const o of deRowIdList) {
            await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(o);
        }
        return ResponseData.success(true);
    }


    /**
     * 批量删除定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteDeLine(args) {
        let {constructId, unitId, deRowIdList, isDelZmDe} = args;
        for (let deRowId of deRowIdList) {
            let pointLine = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRowId)
            if (ObjectUtil.isEmpty(pointLine) || pointLine.type === DeTypeConstants.DE_TYPE_DEFAULT) {
                continue
            }
            let params = {
                constructId,
                deRowId,
                isDelAll: true,
                isRemoveRelationDe: true
            }
            this.removeDeRow(params)
        }
        return ResponseData.success(true);
    }

    /**
     * 批量删除定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteDes(args) {
        let {constructId, unitId, deRowIdList, isDelZmDe} = args;
        for (let deRowId of deRowIdList) {
            let pointLine = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRowId)
            if (ObjectUtil.isEmpty(pointLine) || pointLine.type === DeTypeConstants.DE_TYPE_DEFAULT || pointLine.type === DeTypeConstants.DE_TYPE_FB
                || pointLine.type === DeTypeConstants.DE_TYPE_ZFB || pointLine.type === DeTypeConstants.DE_TYPE_DELIST) {
                continue
            }
            let params = {
                constructId,
                deRowId,
                isDelAll: true,
                isRemoveRelationDe: true
            }
            this.removeDeRow(params)
        }
        return ResponseData.success(true);
    }

    /**
     * 批量删除工程量为0的数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteQuantityZeroDe(args) {
        let {constructId, unitId, applyConstruct} = args;
        if (applyConstruct) {
            let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
            let unitList = projectTree.filter(o => o.type === ProjectLevelConstant.unit);
            let originalQuantityNoZeroList = [];
            if (ObjectUtil.isNotEmpty(unitList)) {
                for (let unit of unitList) {
                    let deTree = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unit.sequenceNbr);
                    if (ObjectUtil.isNotEmpty(deTree)) {
                        let originalQuantityZeroList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_DE
                        || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                        || o.type === DeTypeConstants.DE_TYPE_USER_DE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE) && (ObjectUtil.isEmpty(o.originalQuantity) || o.originalQuantity == 0 || o.originalQuantity == 0.00));
                        if (ObjectUtil.isNotEmpty(originalQuantityZeroList)) {
                            originalQuantityZeroList.forEach(o=>{
                                originalQuantityNoZeroList.push(o);
                            })
                        }
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(originalQuantityNoZeroList)) {
                for (const o of originalQuantityNoZeroList) {
                    await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(o.deRowId);
                }
            } else {
                return ResponseData.fail("未找到工程量为0子目！");
            }
        } else {
            let deTree = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
            if (ObjectUtil.isNotEmpty(deTree)) {
                let originalQuantityZeroList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_DE
                        || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                        || o.type === DeTypeConstants.DE_TYPE_USER_DE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE) && (ObjectUtil.isEmpty(o.originalQuantity) || o.originalQuantity == 0 || o.originalQuantity == 0.00));
                if (ObjectUtil.isNotEmpty(originalQuantityZeroList)) {
                    for (const o of originalQuantityZeroList) {
                      if(o.isTempRemove === CommonConstants.COMMON_YES){
                        continue;
                      }
                        await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(o.deRowId);
                    }
                } else {
                    return ResponseData.fail("未找到工程量为0子目！");
                }
            }
        }

        return ResponseData.success(true);
    }

    /**
     * 批量删除该单位所有
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteAll(args) {
        let {constructId, unitId} = args;
        let deTree = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
        if (ObjectUtil.isNotEmpty(deTree)) {
            let originalQuantityZeroList = deTree.filter(o => o.type !== DeTypeConstants.DE_TYPE_DEFAULT);
            if (ObjectUtil.isNotEmpty(originalQuantityZeroList)) {
                for (const o of originalQuantityZeroList) {
                    await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(o.deRowId);
                }
            }
        }
        return ResponseData.success(true);
    }

    /**
     * 定额删除所有批注
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteAllDeAnnotations(args) {
        let {constructId, unitId, applyConstruct} = args;
        if (applyConstruct) {
            let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
            let unitList = projectTree.filter(o => o.type === ProjectLevelConstant.unit);
            if (ObjectUtil.isNotEmpty(unitList)) {
                for (let unit of unitList) {
                    let deTree = ProjectDomain.getDomain(constructId).deDomain.getDeTreeDepth(constructId, unit.sequenceNbr, null, null);
                    if (ObjectUtil.isNotEmpty(deTree)) {
                        let annotationsList = deTree.filter(o => ObjectUtil.isNotEmpty(o.annotations));
                        if (ObjectUtil.isNotEmpty(annotationsList)) {
                            for (const o of annotationsList) {
                                o.annotations = undefined;
                                ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                            }
                        }
                    }
                }
            }
        } else {
            let deTree = ProjectDomain.getDomain(constructId).deDomain.getDeTreeDepth(constructId, unitId, null, null);
            if (ObjectUtil.isNotEmpty(deTree)) {
                let annotationsList = deTree.filter(o => ObjectUtil.isNotEmpty(o.annotations));
                if (ObjectUtil.isNotEmpty(annotationsList)) {
                    for (const o of annotationsList) {
                        o.annotations = undefined;
                        ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                    }
                }
            }
        }
    }


    /**
     * 展开至那级集合查询
     */
    async openLevelCheckList(args) {
        let {constructId, singleId, unitId} = args;
        let newVar = await this.service.gongLiaoJiProject.gljDeService.openLevelCheckList(constructId, unitId);
        return ResponseData.success(newVar);
    }

    /**
     * 展开所有、一级分部、二级分部、三级分部、四级分部、子目、主材/设备
     */
    async openLevel(args) {
        let {constructId, singleId, unitId, type} = args;

        let newVar = await this.service.gongLiaoJiProject.gljDeService.openLevel(constructId, unitId, type);
        return ResponseData.success(true);
    }

    /**
     * 查找
     */
    async search(args) {
      try{
        let result = await this.service.gongLiaoJiProject.gljDeService.search(args);
        return ResponseData.success(result);
      }catch(error){
        return ResponseData.fail("内部错误");
      }
    }

    /**
     * 过滤
     */
    async filterDe(args) {
        let result = await this.service.gongLiaoJiProject.gljDeService.filterDe(args);
        return ResponseData.success(result);
    }

    /**
     * 根据定额id 向上展开父级
     * @param args
     * @returns {Promise<void>}
     */
    async openParentDe(args) {
        await this.service.gongLiaoJiProject.gljDeService.openParentDe(args);
        return ResponseData.success(true);
    }

  /**
   *   获取定额主材设备 /未计价材料
   * @param args
   * @returns {Promise<void>}
   */
    async getMainMaterialAndEquipment(args ){
      let  result  =await this.service.gongLiaoJiProject.gljDeService.getMainMaterialAndEquipment(args);
      return ResponseData.success(result);
    }

  /**
   *  修改主材设备
   * @param args
   * @returns {Promise<void>}
   */
  async updateMainMaterialAndEquipment(args ){
    await this.service.gongLiaoJiProject.gljDeService.updateMainMaterialAndEquipment(args);
    return ResponseData.success(true);
  }

  /**
   * 获取补充编码序列
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getDefaultCode(args) {
    let { constructId, unitId, prefix } = args;
    const result = await this.service.gongLiaoJiProject.gljDeService.getDefaultCode(constructId, unitId, prefix);
    return ResponseData.success(result);
  }

  /**
     * 分部分项分部数据升降级
     * @param args
     * @param operateAction   up 升级  down降级
     * @returns {Promise<ResponseData >}
     */
    async fbDataUpAndDownController(args) {
        //分部数据升级降级
        let {unitId, constructId, singleId, selectId, operateAction, type} = args;
        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        await this.service.gongLiaoJiProject.gljDeService.fbDataUpAndDown(operateAction, selectId, unit, type, constructId, singleId, unitId);
        // // 填充操作菜单
        // let deList = ProjectDomain.getDomain(unitId).deDomain.getDeTree(item => item.unitId === unitId);
        // let itemBillProject = deList.find(top => top.kind == DeTypeConstants.DE_TYPE_DEFAULT);
        // let nodesByKind = deList.getNodeById(itemBillProject.sequenceNbr);
        // // OptionMenuHandler.setOptionMenu(nodesByKind);
        // // this.service.baseBranchProjectOptionService._fillOptionMenu(itemBillProjects,0,itemBillProjects.length-1);
        // //重新查询下挂清单定额数据的dispNo
        return ResponseData.success(true);
    }

    /**
     * 获取定额说明信息
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getDeContent(args) {
        let {constructId, unitId, deId} = args;
        let deContentMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DE_CONTENT);
        if (ObjectUtils.isEmpty(deContentMap)){
            return ResponseData.success({});
        }
        let unitDeContentMap = deContentMap.get(unitId)
        let deMap = unitDeContentMap?.get(deId);
        if (ObjectUtils.isNotEmpty(deMap)){
            return ResponseData.success(deMap);
        }
        return ResponseData.success({});
    }

    /**
     * 插入子目定额
     * @param args
     * @returns {Promise<void>}
     */
    async insertZmDe(args) {
        let {deRow, zmDeList, zmVariableRuleList} = args
        let fQuantity = zmVariableRuleList?.find(item => item.variableCode === 'GCL').resultValue;
        // 修改父级工程量
        let params = {
            constructId: deRow.constructId,
            unitId: deRow.unitId,
            deId: deRow.fDeId,
            quantity: fQuantity,
        }
        await this.service.gongLiaoJiProject.gljDeService.updateQuantity(params);
        let result = {deList: [], unPricedDeList: [], conversionList: []}
        if (ObjectUtil.isNotEmpty(zmDeList)) {
            // 记录zmVariableRuleList
            let quantitiesMap = ProjectDomain.getDomain(deRow.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
            let unitQuantiesMap = quantitiesMap.get(deRow.unitId);
            let pointLine = unitQuantiesMap.get(deRow.fDeId);
            pointLine.zmVariableRuleList = zmVariableRuleList
            zmDeList.reverse(); // 倒序后插入
            for (let zmDe of zmDeList) {
                if (ObjectUtil.isEmpty(zmDe.deIdZ)) {
                    continue
                }
                let index;
                let prevDeRow;
                if(ObjectUtil.isNotEmpty(deRow.prevRowId)) {
                    prevDeRow = ProjectDomain.getDomain(deRow.constructId).deDomain.getDeById(deRow.prevRowId);
                    index = prevDeRow.index + 1;
                }
                let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
                await ProjectDomain.getDomain(deRow.constructId).deDomain.createDeRow(deRowModel,index);
                let relationDe = await ProjectDomain.getDomain(deRowModel.constructId).deDomain.appendBaseDe(deRowModel.constructId,deRowModel.unitId,zmDe.deIdZ,deRowModel.sequenceNbr,true,true)
                relationDe.isRelationDe = true
                relationDe.fDeId = deRow.fDeId
                // 修改子级消耗量
                let paramsZ = {
                    constructId: deRow.constructId,
                    unitId: deRow.unitId,
                    deId: relationDe.sequenceNbr,
                    quantity: zmDe.quantity,
                }
                await this.service.gongLiaoJiProject.gljDeService.updateQuantity(paramsZ);
                //只保存费用代码给GCL
                let newExpression = zmDe.quantityExpression;
                zmVariableRuleList.forEach(rule => {
                    if (rule.variableCode !== "GCL") {
                        // 转义正则表达式特殊字符并添加单词边界
                        const escapedCode = rule.variableCode.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const regex = new RegExp(`\\b${escapedCode}\\b`, 'g');
                        newExpression = newExpression.replace(regex, rule.resultValue);
                    }
                });
                relationDe.quantityExpression = newExpression

                //更新定额费用代码
                let zmPriceCodes = [
                    {code: 'GCL',price: ObjectUtils.isNotEmpty(fQuantity) ? fQuantity: 0}
                ]
                await this.service.gongLiaoJiProject.gljDeService.setDeCostCode(deRow.constructId, deRow.unitId, relationDe.sequenceNbr, zmPriceCodes);

                let de = ConvertUtil.deepCopy(relationDe);
                result.deList.push(de);
                //未计价材料
                let unPriced = await this.service.gongLiaoJiProject.gljDeService.getMainMaterialAndEquipment({
                    constructId: deRow.constructId,
                    unitId: deRow.unitId,
                    deStandardId: zmDe.deIdZ
                });
                if (ObjectUtils.isNotEmpty(unPriced)) {
                    de.unPriced = unPriced
                    let rcjList = await this.service.gongLiaoJiProject.gljProjectCommonService.getAllRcjByDeId(deRow.constructId, deRow.unitId, de.sequenceNbr);
                    for (let item of unPriced) {
                        item.rcjDetailId = rcjList.find(item2 => item2.materialCode === item.materialCode).sequenceNbr
                    }
                    result.unPricedDeList.push(de);
                }
                //标准换算
                let conversion = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvertList(deRow.constructId, deRow.unitId, de.sequenceNbr);
                if (ObjectUtils.isNotEmpty(conversion)) {
                    result.conversionList.push(conversion);
                }
            }
        }
        return ResponseData.success(result);
    }

    /**
     * 子目规则计算
     * @param args
     * @returns {Promise<void>}
     */
    async zmCalculateVariable(args) {
        let {constructId, zmVariableRuleList, standardId} = args;
        let result = await this.service.gongLiaoJiProject.gljBaseDeRelationService.zmDeListCalculate(constructId, standardId, zmVariableRuleList);
        return ResponseData.success(result);
    }

    /**
     * 子目规则计算
     * @param args
     * @returns {Promise<void>}
     */
    async zmCalculateQuantity(args) {
        let {constructId, zmVariableRuleList, zmDe} = args;
        let result = await this.service.gongLiaoJiProject.gljBaseDeRelationService.zmCalculateQuantity(constructId, zmVariableRuleList, zmDe);
        return ResponseData.success(result);
    }

    /**
     * 定额是否关联子目
     * @param args
     * @returns {Promise<void>}
     */
    async existRelationDe(args) {
        let {constructId, deRowIdList} = args
        if (ObjectUtil.isNotEmpty(deRowIdList)) {
            for (let deRowId of deRowIdList) {
                let relationDeList = await this.service.gongLiaoJiProject.gljDeService.existRelationDe({constructId, deRowId});
                if (ObjectUtils.isNotEmpty(relationDeList)) {
                    return ResponseData.success(true);
                }
            }
        }
        return ResponseData.success(false);
    }

  /**
   * 定额 类型刷新
   * @param args
   * @returns {Promise<void>}
   */
  async checkDeType(args) {
    let {constructId, unitId, deRowId} = args;
    let deRow =ProjectDomain.getDomain(constructId).ctx.allDeMap.getAllNodes().find(item => item.sequenceNbr === deRowId);
    DeTypeCheckUtil.checkAndUpdateDeType(deRow,ProjectDomain.getDomain(constructId).ctx, true,'all');
    return ResponseData.success();
  }

}
GljDeController.toString = () => 'gljDeController';
module.exports = GljDeController;
