const {Service} = require("../../../core");
const gljFyhz = require("../jsonData/glj_fyhz.json");
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../utils/NumberUtil");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const FreeRateModel = require("../domains/projectProcessor/models/FreeRateModel");
const ConstructMajorTypeEnum = require("../enums/ConstructMajorTypeEnum");
const OtherProjectCostOptionMenuConstants = require("../constants/OtherProjectCostOptionMenuConstants");
const xeUtils = require("xe-utils");
const FileOperatorType = require("../constants/FileOperatorType");
const UtilsPs = require("../../../core/ps");
const fs = require("fs");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {BrowserWindow, dialog} = require('electron');
const {ResponseData} = require("../../../common/ResponseData");
const {GljUnitCostSummary} = require("../models/GljUnitCostSummary");
const {GljUnitAwfSummary} = require("../models/GljUnitAwfSummary");
const {GljUnitCostSummaryTotal} = require("../models/GljUnitCostSummaryTotal");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {getConnection, getRepository, getManager} = require('typeorm');
const {FileUtils} = require("../utils/FileUtils.js");
const XLSX = require('xlsx');
const TaxCalculationMethodEnum = require("../enums/TaxCalculationMethodEnum");
const YGLJOperator = require('../core/tools/fileOperator/YGLJOperator');
const CommonConstants = require('../constants/CommonConstants');
const { PricingFileWriteUtils } = require('../../../electron/utils/PricingFileWriteUtils');
const WildcardMap = require('../core/container/WildcardMap');
const FileOperator = require("../core/tools/fileOperator/FileOperator");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const AppContext = require("../core/container/APPContext");
const DeTypeConstants = require("../constants/DeTypeConstants");
const CostDeMatchConstants = require("../constants/CostDeMatchConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");

/**
 * 单位费用汇总  service
 */
class GljUnitCostSummaryService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取默认费用汇总
     * @returns {any[]}
     */
    async defaultUnitCostSummary(args) {
        let {constructId, singleId, unitId, qfMajorType} = args;

        // 获取单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        let qfRate;
        // 获取单位层级取费表数据，为了多专业修改费率后，进行单专业同专业汇总时，获取已有的费率
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isNotEmpty(unitQfbMap)) {
            for (let [key, value] of unitQfbMap) {
                if (key.includes(unitProject.qfMajorType) && unitProject.isSingleMajorFlag === true) {
                    qfRate = value;
                }
            }
        }

        // 非已有费率时，获取默认费率
        if (ObjectUtils.isEmpty(qfRate)) {
            // 获取取费表费率
            let rates = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId
            });
            // 添加单位时，初始化费用汇总的费率
            if (ObjectUtils.isEmpty(qfMajorType) && ObjectUtils.isNotEmpty(rates)) {
                qfRate = rates[0];
                // 多专业汇总时，初始化费用汇总的费率
            } else if (qfMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL) {
                if (unitProject.isSingleMajorFlag === true) {
                    qfRate = rates.find(item => item.qfCode === unitProject.qfMajorType);
                } else {
                    let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
                    if (unitCostSummaryMap.size === 0) {
                        qfRate = rates[0];
                    }
                }
            } else {
                qfRate = rates.find(item => item.qfCode === qfMajorType);
            }
        }

        let unitCostSummaryArray = [];
        // 当前专业汇总行
        let obj = new GljUnitCostSummary();
        if (ObjectUtils.isNotEmpty(qfRate)) {
            obj.name = qfRate.freeProfession;  //名称
            obj.calculateFormula = qfRate.qfCode;  //计算基数
            obj.instructions = qfRate.freeProfession;  //基数说明
            obj.price = 0;
            obj.permission = [];
            obj.isSummaryLine = true;
            obj.whetherPrint = 1;
            obj.isUpdateRate = false;
            unitCostSummaryArray.push(obj);
        }

        let sort = 1;
        for (let i in gljFyhz) {
            sort++;
            let obj = new GljUnitCostSummary();
            ConvertUtil.setDstBySrc(gljFyhz[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            obj.orderNum = sort;
            obj.whetherPrint = 1;
            obj.unitId = unitId;
            obj.calculateMoney = 0;
            obj.price = 0;
            obj.isSummaryLine = false;
            obj.isUpdateRate = false;
            obj.adopted = false;  // 是否被引用
            if (unitProject.isSingleMajorFlag || (qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL && !unitProject.isSingleMajorFlag)) {
                obj["permission"] = OtherProjectCostOptionMenuConstants.pageItem;
            } else {
                obj["permission"] = OtherProjectCostOptionMenuConstants.Item;
            }
            // 初始化费率
            if (ObjectUtils.isNotEmpty(qfRate) && ObjectUtils.isNotEmpty(obj.category)
                && ((qfMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL && unitProject.isSingleMajorFlag === true) ||
                    (unitProject.isSingleMajorFlag === false || ObjectUtils.isEmpty(unitProject.isSingleMajorFlag)) && qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL)) {
                switch (obj.category) {
                    case "企业管理费":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.manageFeeRate) ? qfRate.manageFeeRate : 0;
                        break;
                    case "利润":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.profitRate) ? qfRate.profitRate : 0;
                        break;
                    case "税金":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.taxRate) ? qfRate.taxRate : 0;
                        break;
                    case "规费":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.gfRate) ? qfRate.gfRate : 0;
                        break;
                    case "安全文明施工费":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.anwenRate) ? qfRate.anwenRate : 0;
                        break;
                    default:
                        break;
                }
            } else {
                switch (obj.name) {
                    case "企业管理费":
                        obj.calculateFormula = "GLF";
                        obj.instructions = "企业管理费";
                        break;
                    case "利润":
                        obj.calculateFormula = "LR";
                        obj.instructions = "利润";
                        break;
                    case "税金":
                        obj.calculateFormula = "SJ";
                        obj.instructions = "税金";
                        break;
                    case "安全生产、文明施工费":
                        obj.calculateFormula = "AQWMSGF";
                        obj.instructions = "安全生产、文明施工费";
                        break;
                    default:
                        break;
                }
            }
            unitCostSummaryArray.push(obj);
        }
        return unitCostSummaryArray;
    }

    /**
     * 获取默认费用汇总
     * @returns {any[]}
     */
    async defaultUnitCostSummary2(args) {
        let {constructId, singleId, unitId, qfMajorType} = args;

        // 获取单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        let qfRate;
        // 获取单位层级取费表数据，为了多专业修改费率后，进行单专业同专业汇总时，获取已有的费率
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isNotEmpty(unitQfbMap)) {
            for (let [key, value] of unitQfbMap) {
                if (key.includes(unitProject.qfPartMajorType) && unitProject.isPartSingleMajorFlag === true) {
                    qfRate = value;
                }
            }
        }

        // 非已有费率时，获取默认费率
        if (ObjectUtils.isEmpty(qfRate)) {
            // 获取取费表费率
            let rates = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId
            });
            // 添加单位时，初始化费用汇总的费率
            if (ObjectUtils.isEmpty(qfMajorType) && ObjectUtils.isNotEmpty(rates)) {
                qfRate = rates[0];
                // 多专业汇总时，初始化费用汇总的费率
            } else if (qfMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL) {
                if (unitProject.isPartSingleMajorFlag === true) {
                    qfRate = rates.find(item => item.qfCode === unitProject.qfPartMajorType);
                } else {
                    let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
                    if (unitCostSummaryMap.size === 0) {
                        qfRate = rates[0];
                    }
                }
            } else {
                qfRate = rates.find(item => item.qfCode === qfMajorType);
            }
        }

        let unitCostSummaryArray = [];
        // 当前专业汇总行
        let obj = new GljUnitCostSummary();
        if (ObjectUtils.isNotEmpty(qfRate)) {
            obj.name = qfRate.freeProfession;  //名称
            obj.calculateFormula = qfRate.qfCode;  //计算基数
            obj.instructions = qfRate.freeProfession;  //基数说明
            obj.price = 0;
            obj.permission = [];
            obj.isSummaryLine = true;
            obj.whetherPrint = 1;
            obj.isUpdateRate = false;
            unitCostSummaryArray.push(obj);
        }

        let sort = 1;
        for (let i in gljFyhz) {
            sort++;
            let obj = new GljUnitCostSummary();
            ConvertUtil.setDstBySrc(gljFyhz[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            obj.orderNum = sort;
            obj.whetherPrint = 1;
            obj.unitId = unitId;
            obj.calculateMoney = 0;
            obj.price = 0;
            obj.isSummaryLine = false;
            obj.isUpdateRate = false;
            obj.adopted = false;  // 是否被引用
            if (unitProject.isPartSingleMajorFlag || (qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL && !unitProject.isPartSingleMajorFlag)) {
                obj["permission"] = OtherProjectCostOptionMenuConstants.pageItem;
            } else {
                obj["permission"] = OtherProjectCostOptionMenuConstants.Item;
            }
            // 初始化费率
            if (ObjectUtils.isNotEmpty(qfRate) && ObjectUtils.isNotEmpty(obj.category)
                && ((qfMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL && unitProject.isPartSingleMajorFlag === true) ||
                    (unitProject.isPartSingleMajorFlag === false || ObjectUtils.isEmpty(unitProject.isPartSingleMajorFlag)) && qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL)) {
                switch (obj.category) {
                    case "企业管理费":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.manageFeeRate) ? qfRate.manageFeeRate : 0;
                        break;
                    case "利润":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.profitRate) ? qfRate.profitRate : 0;
                        break;
                    case "税金":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.taxRate) ? qfRate.taxRate : 0;
                        break;
                    case "规费":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.gfRate) ? qfRate.gfRate : 0;
                        break;
                    case "安全文明施工费":
                        obj.rate = ObjectUtils.isNotEmpty(qfRate.anwenRate) ? qfRate.anwenRate : 0;
                        break;
                    default:
                        break;
                }
            } else {
                switch (obj.name) {
                    case "企业管理费":
                        obj.calculateFormula = "GLF";
                        obj.instructions = "企业管理费";
                        break;
                    case "利润":
                        obj.calculateFormula = "LR";
                        obj.instructions = "利润";
                        break;
                    case "税金":
                        obj.calculateFormula = "SJ";
                        obj.instructions = "税金";
                        break;
                    case "安全生产、文明施工费":
                        obj.calculateFormula = "AQWMSGF";
                        obj.instructions = "安全生产、文明施工费";
                        break;
                    default:
                        break;
                }
            }
            unitCostSummaryArray.push(obj);
        }
        return unitCostSummaryArray;
    }


    /**
     * 计算费用汇总
     * @param constructId
     * @param unitId
     * @param unitCostCodePriceArray
     * @param unitCostSummaryArray
     * @param qfMajorType  取费专业
     * @returns {*}
     */
    async countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, qfMajorType) {
        // 计算费用汇总条目
        await this.countUnitCostSummaryItem(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray);
        // 更新费用汇总
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType, unitCostSummaryArray);

        // 更新单位的工程造价、工程规模
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        if (ObjectUtils.isNotEmpty(unitCostCodePriceArray)) {
            unitProject.average = unitCostCodePriceArray.find(item => item.code === "GCGM").price;   // 工程规模
        }
        if (ObjectUtils.isNotEmpty(unitCostSummaryArray) && unitProject.qfMajorTypeMoneyMap) {
            let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));
            // 单专业
            if (unitProject.isSingleMajorFlag) {
                qfMajorTypeMoneyMap.set(unitProject.qfMajorType, unitCostSummaryArray.find(item => item.category === "工程造价").price);
            }
            // 多专业
            if (qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL && !unitProject.isSingleMajorFlag) {
                qfMajorTypeMoneyMap.set(qfMajorType, unitCostSummaryArray.find(item => item.category === "工程造价").price);
            }
            unitProject.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
            const sum = Array.from(qfMajorTypeMoneyMap.values()).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
            unitProject.projectCost = sum;
            unitProject.unitCost = NumberUtil.divide(sum, unitProject.average);  // 单方造价
            ProjectDomain.getDomain(constructId).updateProject(unitProject);
        }
        return unitCostSummaryArray;
    }

    /**
     * 计算费用汇总条目
     * @param constructId
     * @param unitId
     * @param unitCostCodePriceArray  费用代码
     * @param unitCostSummaryArray  费用汇总
     */
    async countUnitCostSummaryItem(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray) {
        //费用代码<费用代码,price>
        let priceMap = new Map();
        //计算基数 <费用汇总费用代号,calculateFormula>
        let codeFormulaMap = new Map();
        //费用汇总费率
        let codeRateMap = new Map();

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        //费用代码
        for (let i = 0; i < unitCostCodePriceArray?.length; i++) {
            let unitCostCodePrice = unitCostCodePriceArray[i];
            let code = ObjectUtils.isEmpty(unitCostCodePrice.code) ? unitCostCodePrice.code : unitCostCodePrice.code.toLowerCase();
            priceMap.set(code, unitCostCodePrice.price)
        }
        if (ObjectUtils.isNotEmpty(unitCostSummaryArray)) {
            //费用汇总
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                let code = ObjectUtils.isEmpty(unitCostSummary.code) ? unitCostSummary.code : unitCostSummary.code.toLowerCase();
                if (ObjectUtils.isNotEmpty(unitCostSummary.calculateFormula)) {
                    codeFormulaMap.set(code, unitCostSummary.calculateFormula.toLowerCase());
                }

                if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                    let rate = unitCostSummary.rate;
                    if (unitCostSummary.isUpdateRate === true) {
                        codeRateMap.set(code, NumberUtil.numberScale(rate, freeRate));
                    } else {
                        codeRateMap.set(code,  NumberUtil.numberScale(rate, freeRate));
                    }
                    // codeRateMap.set(code, rate);
                } else {
                    // unitCostSummary.rate = 100;
                    codeRateMap.set(code, 100);
                }
            }

            // 获取单位的工程造价
            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            // 当前专业汇总行
            let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));

            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                if (ObjectUtils.isEmpty(unitCostSummary.calculateFormula)) {
                    continue;
                }
                if (ObjectUtils.isNotEmpty(unitCostSummary.isSummaryLine) && unitCostSummary.isSummaryLine === true) {
                    if (qfMajorTypeMoneyMap.has(unitCostSummary.calculateFormula)) {
                        unitCostSummary.price = qfMajorTypeMoneyMap.get(unitCostSummary.calculateFormula);
                    }
                } else {
                    //计算基数
                    let calculateFormula = unitCostSummary.calculateFormula.toLowerCase();
                    // 分解字符串成表达式和变量名
                    const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
                    //存放替换后的计算公式
                    let afterCalculateFormula = calculateFormula;
                    if (afterCalculateFormula === "/") {
                        continue;
                    }
                    //递归计算费用汇总
                    afterCalculateFormula = await this.recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je);

                    unitCostSummary.calculateMoney = eval(afterCalculateFormula);
                    let result;
                    if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                        let rate = NumberUtil.numberScale(unitCostSummary.rate, freeRate);
                        unitCostSummary.rate = rate;
                        if (unitCostSummary.isUpdateRate === true) {
                            result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * rate / 100;
                        } else {
                            result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * rate / 100;
                        }
                    } else {
                        result = ObjectUtils.isEmpty(afterCalculateFormula) ? 0 : NumberUtil.numberScale(eval(afterCalculateFormula), je);
                    }
                    unitCostSummary.price = NumberUtil.numberScale(result, je);

                    priceMap.set(ObjectUtils.isEmpty(unitCostSummary.code) ? unitCostSummary.code : unitCostSummary.code.toLowerCase(), unitCostSummary.price);
                }
            }
        }
    }

    /**
     * 递归计算费用汇总
     * @param calculateFormula 计算基数
     * @param afterCalculateFormula  存放替换后的计算公式
     * @param codeFormulaMap 计算基数 <费用汇总费用代号,calculateFormula>
     * @param priceMap 费用代码<费用代码,price>
     * @param variablesToReplace 拆分计算基数后的数组
     * @param codeRateMap  费率 <费用汇总费用代号,rate>
     * @returns {*}
     */
    async recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je) {
        for (let variable of variablesToReplace) {
            if (priceMap.has(variable) && ObjectUtils.isNotEmpty(afterCalculateFormula)) {
                if (priceMap.get(variable) < 0) {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + NumberUtil.numberScale(priceMap.get(variable), je) + ')');
                } else {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, NumberUtil.numberScale(priceMap.get(variable), je));
                }
            } else {
                if (isNaN(Number(variable))) {
                    if (codeFormulaMap.has(variable) && ObjectUtils.isNotEmpty(codeFormulaMap.get(variable)) && ObjectUtils.isNotEmpty(afterCalculateFormula)) {
                        let variablesToReplace1 = codeFormulaMap.get(variable).replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                        //说明当前行引用了费用代号 此处需要加上费率
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + variable + ')/100*' + codeRateMap.get(variable));
                        afterCalculateFormula = afterCalculateFormula.replace(variable, codeFormulaMap.get(variable));
                        afterCalculateFormula = await this.recursionSummary(codeFormulaMap.get(variable), afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace1, codeRateMap, je);
                    } else {
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '0'); // 比如E
                    }
                }
            }
        }
        return afterCalculateFormula;
    }

    /**
     * 导出局部费用汇总
     * @param args
     */
    async exportUnitPartCostSummary(args) {
        let {constructId, unitId, qfMajorType, constructMajorType, rowList} = args;

        // 存放局部汇总文件的路径
        const exportDir = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\glj\\局部汇总';
        FileUtils.ensurePathExists(exportDir)
        let count = FileUtils.countDirectories(exportDir);
        let fileName = '局部费用汇总'
        if (ObjectUtils.isNotEmpty(constructMajorType)) {
            let qfMajor = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisTypeOne(constructMajorType);
            fileName = '局部' + qfMajor?.qfMajorName
        }

        let options = {
            title: '保存文件',
            defaultPath: exportDir + '\\' + fileName + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: ['xlsx']} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.endsWith('xlsx')) {
                filePath += 'xlsx';
            }

            let i = 1;
            // 提取需要的字段
            let filteredData = rowList.map(item => ({
                '序号': i++,
                '费用名称': item.name,
                '取费说明': item.instructions,
                '费率(%)': item.rate,
                '费用金额(元)': item.price
            }));
            if (ObjectUtils.isEmpty(constructMajorType)) {
                i = 1;
                filteredData = rowList.map(item => {
                    if (item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZGC
                        || item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_ZSGC
                        || item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZGC
                        || item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_SZGC
                        || item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_YLLH
                        || item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXJZ
                        || item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXAZ) {
                        item.instructions = "直接费+企业管理费+规费+利润+价款调整+独立费+安全生产、文明施工费+税金"
                    }
                    if (item.name === "工程造价") {
                        item.instructions = "专业造价总合计"
                    }
                    let headers = {
                        '序号': i++,
                        '名称': item.name,
                        '金额': item.price,
                        '备注': item.description,
                    }
                    return headers;
                });
            }

            // 转换数据为工作表
            const worksheet = XLSX.utils.json_to_sheet(filteredData);

            // 创建工作簿并添加工作表
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
            // 写入Excel文件
            XLSX.writeFile(workbook, filePath);
            return ResponseData.success(filePath);
        }
    }

    /**
     * 局部多专业汇总-多专业汇总后的总金额合计
     * @param args
     * @returns {null}
     */
    async getPartCostSummaryMajorsTotal(args) {
        let {constructId, singleId, unitId, isPartSingleMajorFlag, qfPartMajorType} = args;
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 获取该单位下的存放map<取费专业，取费费用汇总工程造价>
        let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));

        let majorList = await this.getCostSummaryMajorByDeList(args);
        const properties = new Set();
        majorList.forEach(item => {
            Object.keys(item).forEach(key => properties.add(key));
        });
        let allMajorCode = Array.from(properties);

        // // 获取取费专业
        // let qfList = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();

        // 获取取费表列表数据  根据取费表中的专业顺序进行排序  todo
        let qfList = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList({
            constructId:constructId,
            unitId:unitId
        })
        qfList = qfList.sort((a, b) => a.sortNo - b.sortNo);


        let costSummaryTotals = [];
        let dispNo = 1;
        // 未补充专业，不显示
        for (let i in qfList) {
            let qfModel = qfList[i];
            // 获取定额中所有的专业
            if (allMajorCode.includes(qfModel.qfCode)) {
                let costSummary = new GljUnitCostSummaryTotal();
                costSummary.dispNo = dispNo++;
                costSummary.code = qfModel.qfCode;
                // costSummary.name = qfModel.qfName;
                costSummary.name = qfModel.freeProfession;

                let param = {
                    constructId,
                    singleId,
                    unitId,
                }
                if (isPartSingleMajorFlag === true) {
                    param.qfMajorType = "TOTAL";
                }else {
                    param.qfMajorType = qfModel.qfCode
                }
                let costSummary2 = await this.getUnitCostSummaryList(param);
                // let param = {constructId, unitId, deLists, csxmDeList, qfMajorType: qfModel.qfCode, isCostSummary: true}
                // let costSummary2 = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice2(param)
                costSummary.price = costSummary2?.find(item => item.name === '工程造价')?.price;
                if (costSummary.price !== 0 && costSummary.price !== "0") {
                    costSummaryTotals.push(costSummary);
                }
            }
        }
        let costSummaryTotal = new GljUnitCostSummary();
        costSummaryTotal.dispNo = costSummaryTotals.length + 1;
        costSummaryTotal.name = "工程造价";
        costSummaryTotal.price = costSummaryTotals.reduce((sum, obj) => {
            let price = NumberUtil.numberScale(obj.price, precision.COST_SUMMARY.je) || 0;
            return NumberUtil.accAdd(sum, price);
        }, 0);
        costSummaryTotals.push(costSummaryTotal);
        return costSummaryTotals;
    }

    /**
     * 获取费用汇总
     * @param args
     * @returns {*}
     */
    async getUnitCostSummaryList(args) {
        let {constructId, singleId, unitId, qfMajorType} = args;
        let unitCostSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

        if (ObjectUtils.isNotEmpty(unitCostSummaryArray)) {
            // 过滤汇总行
            if (qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL) {
                unitCostSummaryArray = unitCostSummaryArray.filter(item => item.isSummaryLine === false);
            }
            // 复制 添加是否被引用标识
            let unitCostSummarys = ConvertUtil.deepCopy(unitCostSummaryArray);
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                //判断需要删除的费用汇总中的费用代号是否被引用
                let deleteItem = unitCostSummaryArray[i];
                if (ObjectUtils.isNotEmpty(deleteItem)) {
                    for (let i = 0; i < unitCostSummarys.length; i++) {
                        if (!ObjectUtils.isEmpty(unitCostSummarys[i].calculateFormula)) {
                            let codeList = unitCostSummarys[i].calculateFormula.split(/[+\-*/]/);
                            if (!ObjectUtils.isEmpty(codeList) && !ObjectUtils.isEmpty(deleteItem.code)) {
                                if (this.stringInArray(codeList, deleteItem.code)) {
                                    deleteItem.adopted = true;  // 该行已被引用，不可删除
                                }
                            }
                        }
                    }
                }
            }
            // 添加序号
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                unitCostSummaryArray[i].sortIndex = i + 1;
            }
        }
        return unitCostSummaryArray;
    }

    /**
     * 新增粘贴费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async addCostSummary(args) {
        let {constructId, singleId, unitId, qfMajorType, unitCostSummary} = args;
        // 添加元素位置
        let lineNumber = args.lineNumber;

        // 获取费用汇总
        let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

        // 新增行
        let addUnitCostSummary = [];
        unitCostSummary.sequenceNbr = Snowflake.nextId();
        unitCostSummary.whetherPrint = 1;
        unitCostSummary.isSummaryLine = false;
        unitCostSummary.isUpdateRate = false;
        unitCostSummary["permission"] = OtherProjectCostOptionMenuConstants.Item;
        unitCostSummary.adopted = false; // 是否被引用
        // 添加新增行
        addUnitCostSummary.push(unitCostSummary);
        unitCostSummarys.splice(lineNumber, 0, ...addUnitCostSummary);

        // 跨单位复制
        if (ObjectUtils.isNotEmpty(unitCostSummary.calculateFormula)) {
            //费用代码
            let unitCostCodePrices = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

            // 计算费用汇总
            await this.countUnitCostSummaryItem(constructId, unitCostCodePrices, unitCostSummarys);
        }
        // 更新费用汇总
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType, unitCostSummarys);
        return unitCostSummarys;
    }

    /**
     * 删除费用汇总(需要判断)
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteCostSummary(args) {
        let {constructId, singleId, unitId, qfMajorType, sequenceNbr} = args;

        // 获取费用汇总
        let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);
        // 非汇总页，过滤汇总行
        if (qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL) {
            unitCostSummarys = unitCostSummarys.filter(item => item.isSummaryLine === false);
        }
        //判断需要删除的费用汇总中的费用代号是否被引用
        let deleteItem = unitCostSummarys.find(item => item.sequenceNbr === sequenceNbr);
        if (ObjectUtils.isNotEmpty(deleteItem)) {
            for (let i = 0; i < unitCostSummarys.length; i++) {
                if (!ObjectUtils.isEmpty(unitCostSummarys[i].calculateFormula)) {
                    let codeList = unitCostSummarys[i].calculateFormula.split(/[+\-*/]/);
                    if (!ObjectUtils.isEmpty(codeList) && !ObjectUtils.isEmpty(deleteItem.code)) {
                        if (this.stringInArray(codeList, deleteItem.code)) {
                            // return ResponseData.fail('该行已被引用，不可删除');
                            const regex = new RegExp(`(?<![a-zA-Z0-9_])${deleteItem.code}(?![a-zA-Z0-9_])`, 'g');
                            unitCostSummarys[i].calculateFormula = unitCostSummarys[i].calculateFormula.replace(regex, "※");
                            // unitCostSummarys[i].calculateFormula = unitCostSummarys[i].calculateFormula.replaceAll(deleteItem.code, "※");
                            unitCostSummarys[i].instructions = unitCostSummarys[i].instructions.replaceAll(deleteItem.name, "0");
                            // unitCostSummarys[i].calculateMoney = NumberUtil.subtract(unitCostSummarys[i].calculateMoney, deleteItem.calculateMoney);
                            // unitCostSummarys[i].price = NumberUtil.subtract(unitCostSummarys[i].price, deleteItem.price);
                        }
                    }
                }
            }
            // 删除该行数据
            let unitCostSummaryArray = unitCostSummarys.filter(item => item.sequenceNbr !== sequenceNbr);

            // 获取费用代码
            let unitCostCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

            let unitCostSummaryList = await this.countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, qfMajorType);
            // 更新费用汇总
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
                .set(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType, unitCostSummaryList);
            return ResponseData.success(unitCostSummaryList);
        } else {
            return ResponseData.fail('该行不存在');
        }
    }

    /**
     * 保存修改费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async saveCostSummary(args) {
        let {constructId, singleId, unitId, qfMajorType} = args;
        let param = args.unitCostSummary;

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;
        let je = precision.COST_SUMMARY.je;

        if (ObjectUtils.isEmpty(param)) {
            return ResponseData.fail('参数错误');
        }
        let bussinessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        // 获取费用代码
        let unitCostCodePriceArray = bussinessMap.get(FunctionTypeConstants.UNIT_COST_CODE).get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);
        for (let i = 0; i < unitCostCodePriceArray.length; i++) {
            if (param.code === unitCostCodePriceArray[i].code) {
                return ResponseData.fail('当前费用代号与费用代码重复，请修改');
            }
        }
        // 获取费用汇总
        let unitCostSummaryArray = bussinessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);
        for (let i = 0; i < unitCostSummaryArray.length; i++) {
            if (param.sequenceNbr !== unitCostSummaryArray[i].sequenceNbr && ObjectUtils.isNotEmpty(param.code) && param.isSummaryLine === false
                && ObjectUtils.isNotEmpty(unitCostSummaryArray[i].code) && param.code.toLowerCase() === unitCostSummaryArray[i].code.toLowerCase()) {
                return ResponseData.fail('当前费用代码已被使用');
            }
            if (unitCostSummaryArray[i].category === param.category && unitCostSummaryArray[i].sequenceNbr !== param.sequenceNbr
                && ObjectUtils.isNotEmpty(unitCostSummaryArray[i].category)) {
                return ResponseData.fail('该费用已存在');
            }
        }

        let copyCode = param.code;
        let copyName = param.name;
        if (ObjectUtils.isEmpty(param.code)) {
            param.code = "※";
            param.name = "0";
        }
        let codePriceMap = new Map();  //1费用代号、代码存放<费用代号、代码,price>
        let codeFormulaMap = new Map();  //计算基数
        let codeInstructionsMap = new Map();  //基数说明、费用名称<费用代号、代码,instructions>
        let codeRateMap = new Map();
        let codeNameMap = new Map();
        let currentId = param.sequenceNbr;
        let priceChangeArray = [];
        if (ObjectUtils.isNotEmpty(param.sequenceNbr)) {
            // 修改
            let unitCostSummary = unitCostSummaryArray.find(item => item.sequenceNbr === param.sequenceNbr);
            if (unitCostSummary == null) {
                return ResponseData.fail('参数错误');
            }

            await this.getAllCodeFormulaPriceMap(constructId, unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, unitCostSummaryArray);

            let lowerParamCalculateFormula = ObjectUtils.isNotEmpty(param.calculateFormula) ? param.calculateFormula.toLowerCase() : param.calculateFormula;  // 转小写
            if (ObjectUtils.isNotEmpty(lowerParamCalculateFormula)) {
                let removeChar = '※';
                let codeList = lowerParamCalculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => item !== removeChar);
                if (ObjectUtils.isNotEmpty(codeList) && param.isSummaryLine === false) {
                    for (let i = 0; i < codeList.length; i++) {
                        // 判断公式中引用的费用代码是不是引用的列表不存在的
                        let keys = [...codeNameMap.keys()].map(function (item) {
                            if (ObjectUtils.isNotEmpty(item)) {
                                return item.toLowerCase();
                            }
                        });
                        if (!keys.includes(codeList[i]) && isNaN(Number(codeList[i]))) {
                            return ResponseData.fail("费用代码输入有误！");
                        }
                    }
                }
            }

            //判断code
            if (ObjectUtils.isNotEmpty(unitCostSummary.code) && param.code !== unitCostSummary.code) {
                // 如果修改了code  那么就要去看这个老的code是不有地方引用了他  如果有引用  那么不能修改这个code  后面产品又说改了code其他引用了code的地方也要同步变更...
                await this.inspectionCode(param.code, unitCostSummary.code, codeFormulaMap);
            }
            //判断name
            if (ObjectUtils.isNotEmpty(unitCostSummary.name) && param.name !== unitCostSummary.name && ObjectUtils.isNotEmpty(copyCode)) {
                // 如果修改了name  那么就要去看这个老的name是不有地方引用了他  如果有引用  那么不能修改这个name  后面产品又说改了name其他引用了name的地方也要同步变更...
                await this.inspectionName(param.name, unitCostSummary.name, codeInstructionsMap);
            }

            //判断 计算基数和费率修改
            if ((param.calculateFormula !== unitCostSummary.calculateFormula || (param.rate !== unitCostSummary.rate && ObjectUtils.isNotEmpty(param.calculateFormula)))
                && param.isSummaryLine === false) {
                // 如果参数传来的计算公式或者费率不一样  那么说明修改了计算公式或者费率   就需要对计算公式进行验证并计算结果
                let responseData = await this.handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap);
                if (!ObjectUtils.isEmpty(responseData)) {
                    return ResponseData.fail(responseData.message);
                }

                // 把本次计算的结果存入map  留待后续计算使用
                codePriceMap.set(param.code, NumberUtil.numberScale(param.price, je));
                codeFormulaMap.set(param.code, param.calculateFormula);
                codeInstructionsMap.set(param.code, param.instructions);
                // 如果公式进行了修改  那么需要对引用了这个条公式对应的code的所有公式重新计算，并且对扩散影响的所有公式都需要重新计算
                await this.handleCodePriceChange(param.code, codePriceMap, codeFormulaMap, codeRateMap);
            }
            // 根据上面这步的计算 得出有哪些数据需要更新
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let item = unitCostSummaryArray[i];
                let price = codePriceMap.get(item.code);
                let formula = codeFormulaMap.get(item.code);
                let instructions = codeInstructionsMap.get(item.code);

                let updateUnitCostSummary = new GljUnitCostSummary();
                updateUnitCostSummary.sequenceNbr = item.sequenceNbr;
                let flag = false;
                if (ObjectUtils.isNotEmpty(price) && price !== item.price) {
                    updateUnitCostSummary.price = price;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(formula) && formula !== item.calculateFormula) {
                    updateUnitCostSummary.calculateFormula = formula;
                    flag = true;
                } else {
                    updateUnitCostSummary.price = 0;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(instructions) && instructions !== item.instructions) {
                    updateUnitCostSummary.instructions = instructions;
                    flag = true;
                }
                if (flag === true) {
                    priceChangeArray.push(updateUnitCostSummary);
                }
            }

        } else {
            // 新增
            let find = unitCostSummaryArray.find(item => item.code === param.code);
            if (ObjectUtils.isNotEmpty(find)) {
                return ResponseData.fail('当前code已存在');
            }
            // 获取统一的map数据
            await this.getAllCodeFormulaPriceMap(constructId, unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, unitCostSummaryArray);
            // 处理公式校验和计算
            await this.handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap);
        }

        // 获取单位，并修改费用汇总的汇总行数据
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        if (unitProject.isSingleMajorFlag === true) {
            let qfMajorName = unitProject.qfMajorType;
            // 针对不同工程专业，取费表数据更新
            await this.updateUnitFreeRate(constructId, singleId, unitId, qfMajorName, param);
        } else {
            if (qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL) {
                // 针对不同工程专业，取费表数据更新
                await this.updateUnitFreeRate(constructId, singleId, unitId, qfMajorType, param);
            }
        }

        if (ObjectUtils.isNotEmpty(param.code) && param.code === '※') {
            args.unitCostSummary.code = copyCode;
            args.unitCostSummary.name = copyName;
        }
        // 进行数据更新
        await this.setUnitCostSummaryData(args, unitCostSummaryArray, priceChangeArray);
        //调用计算费用汇总
        await this.countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, qfMajorType);

        if (((unitProject.isSingleMajorFlag === false || ObjectUtils.isEmpty(unitProject.isSingleMajorFlag)) && qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL) ||
            unitProject.isSingleMajorFlag === true) {
            // 当前专业汇总行
            unitCostSummaryArray[0].price = unitCostSummaryArray.find(item => item.category === "工程造价").price;
        }

        // 更新费用汇总
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType, unitCostSummaryArray)
        return ResponseData.success(unitCostSummaryArray);
    }

    /**
     * 判断 target中是否包含 arr
     * @param arr
     * @param target
     * @returns {boolean}
     */
    stringInArray(arr, target) {
        for (let item of arr) {
            if (item === target) {
                return true;
            }
        }
        return false;
    }

    /**
     * 修改code - 修改计算基数
     * @param newCode
     * @param oldCode
     * @param codeFormulaMap
     */
    async inspectionCode(newCode, oldCode, codeFormulaMap) {
        let lowerOldCode = oldCode.toLowerCase();
        for (let [key, value] of codeFormulaMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let codeList = value.toLowerCase().split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(codeList) && this.stringInArray(codeList, lowerOldCode)) {
                    let reg = new RegExp("\\b" + lowerOldCode + "\\b", "gi")
                    let replace = value.replaceAll(reg, newCode);
                    codeFormulaMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 修改name - 修改基数说明
     * @param newName
     * @param oldName
     * @param codeInstructionsMap
     */
    async inspectionName(newName, oldName, codeInstructionsMap) {
        for (let [key, value] of codeInstructionsMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let nameList = value.toLowerCase().split(/[+\-*/]/);
                if (ObjectUtils.isNotEmpty(nameList) && this.stringInArray(nameList, oldName)) {
                    // 使用正则表达式 \b 来匹配独立的 A
                    // let regex = new RegExp(`\\b${oldName}\\b`, 'g');
                    // let replace = value.replace(regex, newName);

                    // 计算基数中引用的费用代号被删除后，变成※，基数说明列中对应的需要变成0
                    let replace = value.replaceAll(oldName, newName);
                    codeInstructionsMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 获取单位工程的所有已有的费用代码基数和对应的价格
     * @param constructId
     * @param unitCostCodePriceArray
     * @param currentId
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeInstructionsMap
     * @param codeRateMap
     * @param codeNameMap
     * @param unitCostSummaryArray
     */
    async getAllCodeFormulaPriceMap(constructId, unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, unitCostSummaryArray) {

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        if (ObjectUtils.isNotEmpty(unitCostCodePriceArray)) {
            for (let i = 0; i < unitCostCodePriceArray.length; i++) {
                let unitCostCodePrice = unitCostCodePriceArray[i];
                let code = unitCostCodePrice.code;
                codePriceMap.set(code, NumberUtil.numberScale(unitCostCodePrice.price, je));
                codeFormulaMap.set(code, unitCostCodePrice.code);
                codeInstructionsMap.set(code, unitCostCodePrice.name);
                codeNameMap.set(code, unitCostCodePrice.name);
            }
        }
        if (ObjectUtils.isNotEmpty(unitCostSummaryArray)) {
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                let code = unitCostSummary.code;
                if (!ObjectUtils.isEmpty(currentId) && currentId === unitCostSummary.sequenceNbr) {
                    continue;
                }
                codePriceMap.set(code, NumberUtil.numberScale(unitCostSummary.price, je));
                if (ObjectUtils.isNotEmpty(code)) {
                    codeFormulaMap.set(code, unitCostSummary.calculateFormula);
                    codeInstructionsMap.set(code, unitCostSummary.instructions);
                }
                codeNameMap.set(code, unitCostSummary.name);
                if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                    if (unitCostSummary.isUpdateRate === true) {
                        codeRateMap.set(code, NumberUtil.numberScale(unitCostSummary.rate, freeRate));
                    } else {
                        codeRateMap.set(code, unitCostSummary.rate);
                    }
                }
            }
        }
    }

    /**
     * 费用代码修改 - 计算基数修改
     * @param calculateFormula
     * @param codePriceMap
     * @returns {any}
     */
    async doCalculator(calculateFormula, codePriceMap) {
        if (ObjectUtils.isEmpty(calculateFormula)) {
            return calculateFormula;
        }
        // 分解字符串成表达式和变量名
        const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
        //存放替换后的计算公式
        let afterCalculateFormula = calculateFormula;

        // 创建一个新的 Map 来存储小写的键和对应的值
        const keysLowercase = new Map();
        // 使用 forEach 遍历原始的 Map
        codePriceMap.forEach((value, key) => {
            // 将键转换为小写
            const lowerCaseKey = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            // 将新的键值对添加到新的 Map 中
            keysLowercase.set(lowerCaseKey, value);
        });

        //替换费用代码和费用代号
        for (let variable of variablesToReplace) {
            let variableLowerCase = variable.toLowerCase(); // 转小写
            if (keysLowercase.has(variableLowerCase)) {
                afterCalculateFormula = afterCalculateFormula.replace(variable, keysLowercase.get(variableLowerCase));
            }
        }

        let flag = await this.isValidExpression(afterCalculateFormula);
        if (!flag) {
            throw new Error("表达式有误，请重新编辑！");
        }
        // 匹配表达式中的※为0，进行检验
        afterCalculateFormula = afterCalculateFormula.replaceAll("※", "0");
        return eval(afterCalculateFormula);
    }

    /**
     * 效验取费基数：A+B+V+
     * @param expression
     * @returns {boolean}
     */
    async isValidExpression(expression) {
        // 匹配表达式中的※为0，进行检验
        expression = expression.replaceAll("※", "0");
        // 匹配四则运算表达式的正则表达式
        const regex = /^[\d\+\-\*\/\(\)\.]+$/;
        // 检查表达式是否匹配正则表达式
        if (!regex.test(expression)) {
            return false;
        }

        try {
            // 使用 eval() 函数计算表达式的值
            eval(expression);
            return true;
        } catch (e) {
            // 如果表达式有语法错误，eval() 函数会抛出异常
            return false;
        }
    }

    /**
     * 计算基数修改 - 基数说明修改
     * @param formula
     * @param codeNameMap
     * @returns {string|*}
     */
    async getFormulaInstructions(formula, codeNameMap) {
        if (ObjectUtils.isEmpty(formula)) {
            return formula;
        }
        // codeNameMap的key转小写
        let codeNameMapLowerCase = new Map();
        for (let [key, value] of codeNameMap) {
            let keyNew = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            codeNameMapLowerCase.set(keyNew, value);
        }
        // 把公式进行分割
        formula = formula.toLowerCase();
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (!codeList.length) {
            return "";
        }
        for (let i = 0; i < codeList.length; i++) {
            let code = codeList[i];
            let instruction = codeNameMapLowerCase.get(code);
            if (ObjectUtils.isNotEmpty(instruction) && instruction.trim()) {
                formula = formula.replace(code, instruction);
            } else {
                formula = formula.replace(code, code);
            }
        }
        return formula;
    }

    /**
     * 处理公式修改逻辑
     * @param constructId
     * @param param
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeNameMap
     */
    async handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap) {

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        // 对本次新增或者修改进行正确性检测
        let set = new Set();
        // set.add(param.code);
        try {
            await this.doInspection(set, param, codeFormulaMap, param.code);
            // 如果没有抛出异常  说明检查通过了
            let res = await this.doCalculator(param.calculateFormula, codePriceMap);
            param.calculateMoney = Math.round(res);
            if (param.rate) {
                if (param.isUpdateRate === true) {
                    res = NumberUtil.numberScale(parseFloat(res * param.rate / 100), freeRate);
                } else {
                    res = parseFloat((res * param.rate / 100));
                }
            }
            param.price = Math.round(res * 100) / 100;
            param.instructions = await this.getFormulaInstructions(param.calculateFormula, codeNameMap)
        } catch (e) {
            return ResponseData.fail(e.message);
        }
    }

    /**
     * 获取所有引用code
     * @param param
     * @param calculateFormula
     * @param codeFormulaMap
     * @param set
     */
    async getCodes(param, calculateFormula, codeFormulaMap, set) {
        if (codeFormulaMap.has(calculateFormula) && ObjectUtils.isNotEmpty(codeFormulaMap.get(calculateFormula))) {
            // 获取公式calculateFormula中的code
            let codeList = codeFormulaMap.get(calculateFormula).replace(/[+\-*/]/g, ',').split(',');
            if (ObjectUtils.isNotEmpty(codeList)) {
                if (codeList.length === 1 && codeList[0] === calculateFormula) {
                    return;
                }
                for (let i = 0; i < codeList.length; i++) {
                    let code = codeList[i];
                    if (code !== "※") {
                        set.add(code);
                    }
                    await this.getCodes(param, code, codeFormulaMap, set);
                }
            }
        } else {
            let codeList = calculateFormula.replace(/[+\-*/]/g, ',').split(',');
            for (let i = 0; i < codeList.length; i++) {
                let code = codeList[i];
                if (codeFormulaMap.has(code) && ObjectUtils.isNotEmpty(codeFormulaMap.get(code))) {
                    let calculateFormula2 = codeFormulaMap.get(code);
                    if (calculateFormula2.includes(param.code)) {
                        set.add(calculateFormula2);
                    }
                }
            }
        }
    }

    /**
     * 对公式的正确性进行检查 检查是否有错误引用或者循环引用
     * ps：
     * 这个有个简单的做法  就是从修改的公式code开始  把每一层的每一个元素都分解到一个二叉树里面
     * 如果这个二叉树的任何一条从顶层到底层的分支中出现重复的元素  那就说明这个公式存在循环引用   但是这样做的话错误提示不明确
     * @param codes
     * @param param
     * @param codeFormulaMap
     * @param code
     */
    async doInspection(codes, param, codeFormulaMap, code) {
        let formula = param.calculateFormula;
        if (ObjectUtils.isEmpty(formula)) {
            return;
        }
        if (formula === code) {
            throw new Error("公式存在循环引用，请检查并修改");
        }
        // 获取应用取费基数下所有的子code
        await this.getCodes(param, formula, codeFormulaMap, codes);
        if (codes.has(code)) {
            throw new Error("公式存在循环引用，请检查并修改");
        }
        // 根据 加减乘除 分割计算公式
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (codeList.length === 0) {
            throw new Error("运算公式格式错误，请检查并修改");
        }
    }

    /**
     * 计算费率
     * @param code
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeRateMap
     */
    async handleCodePriceChange(code, codePriceMap, codeFormulaMap, codeRateMap) {
        for (let [key, value] of codeFormulaMap.entries()) {
            if (ObjectUtils.isNotEmpty(value) && ObjectUtils.isNotEmpty(value) && isNaN(Number(value))) {
                // 对公式进行分解
                let codeList = value.split(/[\+\-\*\/\(\)]+/);
                if (codeList.length > 1) {
                    if (codeList.includes(code)) {
                        let res = await this.doCalculator(value, codePriceMap);
                        let rate = codeRateMap.get(key);
                        if (rate) {
                            res = parseFloat((res * rate / 100));
                            codePriceMap.set(key, Math.round(res * 100) / 100)
                        } else {
                            codePriceMap.set(key, res);
                        }
                        await this.handleCodePriceChange(key, codePriceMap, codeFormulaMap, codeRateMap);
                    }
                }
            }
        }
    }

    /**
     * 设置数据
     * @param args
     * @param unitCostSummaryArray
     * @param priceChangeArray
     */
    async setUnitCostSummaryData(args, unitCostSummaryArray, priceChangeArray) {
        //新增的数据
        let newUnitCostSummary = args.unitCostSummary;

        if (ObjectUtils.isEmpty(newUnitCostSummary.sequenceNbr)) {
            // // 新增
            // newUnitCostSummary.sequenceNbr = Snowflake.nextId();
            // for (let i = unitCostSummaryArray.length - 1; i >= 0; i--) {
            //     const item = unitCostSummaryArray[i];
            //     if (item.sortNum >= newUnitCostSummary.sortNum) {
            //         item.sortNum += 1;
            //     }
            // }
            unitCostSummaryArray.push(newUnitCostSummary);
        } else {
            //修改
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let element = unitCostSummaryArray[i];
                if (element.sequenceNbr === newUnitCostSummary.sequenceNbr) {
                    unitCostSummaryArray[i] = newUnitCostSummary;
                }
            }
        }
        //更新修改后的金额
        if (!ObjectUtils.isEmpty(priceChangeArray)) {
            for (let i = 0; i < priceChangeArray.length; i++) {
                let item = priceChangeArray[i];
                for (let j = 0; j < unitCostSummaryArray.length; j++) {
                    let element = unitCostSummaryArray[j];
                    if (element.sequenceNbr === item.sequenceNbr) {
                        if (!ObjectUtils.isEmpty(item.calculateMoney)) {
                            unitCostSummaryArray[j].calculateMoney = item.calculateMoney;
                        }
                        if (!ObjectUtils.isEmpty(item.price)) {
                            unitCostSummaryArray[j].price = item.price;
                        }
                        if (!ObjectUtils.isEmpty(item.calculateFormula)) {
                            unitCostSummaryArray[j].calculateFormula = item.calculateFormula;
                        }
                        if (!ObjectUtils.isEmpty(item.instructions)) {
                            unitCostSummaryArray[j].instructions = item.instructions;
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取工程专业下拉框
     * @param args
     * @returns {(((searchElement: unknown, fromIndex?: number) => number) | {(...items: ConcatArray<unknown>): unknown[], (...items: unknown[]): unknown[]} | {(callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown): unknown, (callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown, initialValue: unknown): unknown, <U>(callbackfn: (previousValue: U, currentValue: unknown, currentIndex: number, array: unknown[]) => U, initialValue: U): U} | {<S extends unknown>(predicate: (value: unknown, index: number, array: unknown[]) => value is S, thisArg?: any): S[], (predicate: (value: unknown, index: number, array: unknown[]) => unknown, thisArg?: any): unknown[]} | (() => unknown) | ((predicate: (value: unknown, index: number, array: unknown[]) => unknown, thisArg?: any) => boolean) | number | ((...items: unknown[]) => number) | (() => unknown[]) | (() => string) | ((start?: number, end?: number) => unknown[]) | (<U>(callbackfn: (value: unknown, index: number, array: unknown[]) => U, thisArg?: any) => U[]) | {(callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown): unknown, (callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown, initialValue: unknown): unknown, <U>(callbackfn: (previousValue: U, currentValue: unknown, currentIndex: number, array: unknown[]) => U, initialValue: U): U} | {<S extends unknown>(predicate: (value: unknown, index: number, array: unknown[]) => value is S, thisArg?: any): this is S[], (predicate: (value: unknown, index: number, array: unknown[]) => unknown, thisArg?: any): boolean} | ((compareFn?: (a: unknown, b: unknown) => number) => ObjectConstructor) | ((separator?: string) => string) | ((callbackfn: (value: unknown, index: number, array: unknown[]) => void, thisArg?: any) => void) | {(start: number, deleteCount?: number): unknown[], (start: number, deleteCount: number, ...items: unknown[]): unknown[]})[] | any[]}
     */
    async getConstructMajorTypeEnum(args) {
        return Object.values(ConstructMajorTypeEnum);
    }

    /**
     * 获取费用汇总左侧树
     *
     * @param args
     * @returns {{}}
     */
    async getCostSummaryMajorMenuList(args) {
        const {constructId, singleId, unitId} = args;

        // 获取费用汇总
        let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
        // 当前单位的工程专业
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 获取取费专业
        let qfList = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();
        qfList = qfList.filter(item => item.qfCode !== CommonConstants.SYSTEM_SZGC);
        let qfCodeList = qfList.map(item => item.qfCode);

        let qfCodeNameMap = qfList.reduce((acc, item) => {
            acc.set(item.qfCode, item.qfName);
            return acc;
        }, new Map());

        let constructMajorTypeList = [];
        // 单专业汇总
        if (unitProject.isSingleMajorFlag) {
            // // 遍历费用汇总的模板
            // unitCostSummaryMap.forEach((value, key) => {
            //     let majorObj = {};
            //     // 获取定额中所有的专业
            //     qfCodeList.forEach(qfCode => {
            //         if (key.includes(unitId)) {
            //             if (key.includes(qfCode)) {
            //                 majorObj[qfCode] = qfCodeNameMap.get(qfCode);
            //                 constructMajorTypeList.push(majorObj);
            //             }
            //         }
            //     })
            // });

            // 多专业汇总
        } else {
            // 预算书下定额
            let yssTypeArray = ['0', '01', '02', '03'];
            let budgetBookDes = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && !yssTypeArray.includes(item.type));

            // 措施项目下定额
            let csxmTypeArray = ['0', '01', '02'];
            let csxmDes = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && !csxmTypeArray.includes(item.type));

            // 独立费
            let independentCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
                .get(await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unitId));

            // 遍历费用汇总的模板
            unitCostSummaryMap.forEach((value, key) => {
                let majorObj = {};
                // 获取该单位的费用汇总key
                if (key.includes(unitId)) {
                    // 获取定额中所有的专业
                    qfCodeList.forEach(qfCode => {
                        if (key.includes(qfCode)) {
                            let budgetBookTypes;
                            if (ObjectUtils.isNotEmpty(budgetBookDes)) {
                                // 获取预算书下的专业类型
                                budgetBookTypes = [...new Set(budgetBookDes.filter(item => ObjectUtils.isNotEmpty(item.costFileCode)).map(item => item.costFileCode))];
                            }
                            let csxmTypes;
                            if (ObjectUtils.isNotEmpty(csxmDes)) {
                                // 获取措施项目下的专业类型
                                csxmTypes = [...new Set(csxmDes.filter(item => ObjectUtils.isNotEmpty(item.costFileCode)).map(item => item.costFileCode))];
                            }
                            let independentCostTypes;
                            if (ObjectUtils.isNotEmpty(independentCosts)) {
                                // 获取独立费下的专业类型
                                independentCostTypes = [...new Set(independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode)).map(item => item.costMajorCode))];
                            }
                            if (ObjectUtils.isNotEmpty(budgetBookTypes) || ObjectUtils.isNotEmpty(csxmTypes) || ObjectUtils.isNotEmpty(independentCostTypes)) {
                                majorObj[qfCode] = qfCodeNameMap.get(qfCode);
                                constructMajorTypeList.push(majorObj);
                            }
                        }
                    });
                }
            });
        }
        if (ObjectUtils.isNotEmpty(constructMajorTypeList)) {
            let treeList = {};
            treeList.itemList = constructMajorTypeList;
            return treeList;
        }
    }

    /**
     * 多专业汇总-多专业汇总补充工程专业
     * @param args
     */
    async supplyCostSummaryMajors(args) {
        let {constructId, singleId, unitId, qfMajorType, isSingleMajorFlag} = args;

        // 当前单位的工程专业
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let qfMajorTypeMoneyMap = new Map();
        // 修改单位，记录多专业或单专业汇总的标识
        unitProject.isSingleMajorFlag = isSingleMajorFlag;
        unitProject.jzgcProjectCost = 0;  // 建筑工程造价
        unitProject.zsgcProjectCost = 0;  // 装饰工程造价
        unitProject.azgcProjectCost = 0;  // 安装工程造价
        unitProject.szgcProjectCost = 0;  // 市政工程造价
        unitProject.yllhProjectCost = 0;  // 园林绿化工程

        // isSingleMajorFlag = true 单专业
        if (isSingleMajorFlag) {

            unitProject.qfMajorType = qfMajorType;
            qfMajorTypeMoneyMap.set(qfMajorType, 0);
            unitProject.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
            ProjectDomain.getDomain(constructId).updateProject(unitProject);

            // 删除原来的费用代码
            let codeMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            let newCodeMap = new Map();
            for (let [key, value] of codeMap.entries()) {
                if (!key.includes(unitId)) {
                    newCodeMap.set(key, value);
                }
            }
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_COST_CODE, newCodeMap);

            // 对专业类型进行费用代码，并对费用汇总填写数据
            let unitCostCodePrices = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.defaultUnitCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                qfMajorType: UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL
            });
            // 更新或添加费用代码
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .set(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL, unitCostCodePrices);

            // 删除原来的费用汇总
            let codeSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
            let newCodeSummaryMap = new Map();
            for (let [key, value] of codeSummaryMap.entries()) {
                if (!key.includes(unitId)) {
                    newCodeSummaryMap.set(key, value);
                }
            }
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);

            // 添加补充的专业的取费表记录，并获取当前专业费率，包括：取费表未初始化的专业费率
            await this.service.gongLiaoJiProject.gljFreeRateService.addUnitFreeRateCostSummary(constructId, unitId, qfMajorType);
            // 同步更新取费文件
            await this.service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorDelFeeRate(constructId, unitId, unitProject.constructMajorType, qfMajorType);

            // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
            let unitCostSummarys = await this.defaultUnitCostSummary({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                qfMajorType: UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL
            });

            // 更新或添加费用汇总
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
                .set(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL, unitCostSummarys);

            // 对专业类型进行费用代码，并对费用汇总填写数据
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                qfMajorType: UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL
            });

            // isSingleMajorFlag = false 多专业
        } else {

            // 对专业类型进行费用代码，并对费用汇总填写数据
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                qfMajorType: qfMajorType
            });

            // 同步更新取费文件
            await this.service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorDelFeeRate(constructId, unitId, unitProject.constructMajorType, qfMajorType);

            // // 1. 预算书下定额
            // let typeArray = ['0', '01', '02', '03'];
            // let budgetBookDes = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && !typeArray.includes(item.type));
            //
            // // 2.获取措施项目定额
            // let csxmDes = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && !['0', '01', '02', '03'].includes(item.type));
            //
            // // 3. 独立费
            // let independentCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            //     .get(this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unitId));
            //
            // let budgetBookTypes,csxmTypes,independentCostTypes;
            // if (ObjectUtils.isNotEmpty(budgetBookDes)) {
            //     // 获取预算书下的专业类型
            //     budgetBookTypes = budgetBookDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
            // }
            // if(ObjectUtils.isNotEmpty(csxmDes)){
            //     // 获取措施项目下的专业类型
            //     csxmTypes = csxmDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
            // }
            // if (ObjectUtils.isNotEmpty(independentCosts)) {
            //     // 获取独立费下的专业类型
            //     independentCostTypes = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode)).map(item => item.costMajorCode);
            // }
            // // 专业名称数组合并并去重: 如果预算书、独立费有一个有不同专业类型，就可点击弹窗，就有几个子节点
            // let constructMajorTypeSet = new Set();
            // // constructMajorTypeSet.add(unitProject.qfMajorType);
            // if (ObjectUtils.isNotEmpty(budgetBookTypes)) {
            //     constructMajorTypeSet = new Set([...constructMajorTypeSet, ...budgetBookTypes]);
            // }
            // if (ObjectUtils.isNotEmpty(csxmTypes)) {
            //     constructMajorTypeSet = new Set([...constructMajorTypeSet, ...csxmTypes]);
            // }
            // if (ObjectUtils.isNotEmpty(independentCostTypes)) {
            //     constructMajorTypeSet = new Set([...constructMajorTypeSet, ...independentCostTypes]);
            // }
            //
            // if (ObjectUtils.isNotEmpty(constructMajorTypeSet)) {
            //     // 删除原来的费用代码
            //     let codeMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            //     let newCodeMap = new Map();
            //     for (let [key, value] of codeMap.entries()) {
            //         if (!key.includes(unitId)) {
            //             newCodeMap.set(key, value);
            //         }
            //     }
            //     ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_COST_CODE, newCodeMap);
            //
            //     // 删除原来的费用汇总
            //     let codeSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
            //     let newCodeSummaryMap = new Map();
            //     for (let [key, value] of codeSummaryMap.entries()) {
            //         if (!key.includes(unitId)) {
            //             newCodeSummaryMap.set(key, value);
            //         }
            //     }
            //     ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);
            //
            //
            //     // 添加多专业费用类型字段
            //     constructMajorTypeSet.forEach(majorName => {
            //         // newCodeMap.set(unitId + FunctionTypeConstants.SEPARATOR + majorName, null);
            //         // newCodeSummaryMap.set(unitId + FunctionTypeConstants.SEPARATOR + majorName, null);
            //         qfMajorTypeMoneyMap.set(majorName, 0);
            //     });
            //     unitProject.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
            //     ProjectDomain.getDomain(constructId).updateProject(unitProject);
            //
            //     for (const majorName of constructMajorTypeSet) {
            //
            //         // 对专业类型进行费用代码，并对费用汇总填写数据
            //         let unitCostCodePrices = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.defaultUnitCostCodePrice({
            //             constructId: constructId,
            //             singleId: singleId,
            //             unitId: unitId,
            //             qfMajorType: majorName
            //         });
            //         // 更新或添加费用代码
            //         ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
            //             .set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePrices);
            //
            //         // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
            //         let unitCostSummarys = await this.defaultUnitCostSummary({
            //             constructId: constructId,
            //             singleId: singleId,
            //             unitId: unitId,
            //             qfMajorType: majorName
            //         });
            //
            //         // 更新或添加费用汇总
            //         ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            //             .set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostSummarys);
            //
            //         // 对专业类型进行费用代码，并对费用汇总填写数据
            //         let unitCostCodePriceArray = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
            //             constructId: constructId,
            //             singleId: singleId,
            //             unitId: unitId,
            //             qfMajorType: majorName
            //         });
            //         // 更新或添加费用代码
            //         ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
            //             .set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePriceArray);
            //    }

        }
        return ResponseData.success();
        // }
    }


    /**
     * 汇总费用代码价格
     * @param args
     * @returns {Promise<Map<any, any>>}
     */
    async getUnitCostCode(args) {
        let {constructId, singleId, unitId} = args;
        // 获取默认map
        let unitCostCodeMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 创建一个新的Map对象来存储安装与建筑费用相加的值
        let unitCostCodePriceMap = new Map();
        if (ObjectUtils.isNotEmpty(unitProject) && unitProject.isSingleMajorFlag === true) {
            unitCostCodePriceMap = unitCostCodeMap.get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL).reduce((acc, item) => {
                acc.set(item.code, item.price);
                return acc;
            }, new Map());
        } else {
            if (ObjectUtils.isNotEmpty(unitCostCodeMap)) {
                unitCostCodeMap.forEach((value, key) => {
                    if (key.includes(unitId) && !key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL)) {
                        value.forEach(item => {
                            if (unitCostCodePriceMap.has(item.code) && !(item.code === 'GCSF' || item.code === 'GCDF' || item.code === 'GCSDF' || item.code === 'JZMJ')) {
                                unitCostCodePriceMap.set(item.code, NumberUtil.add(unitCostCodePriceMap.get(item.code), item.price));
                            } else {
                                unitCostCodePriceMap.set(item.code, item.price);
                            }
                        });
                    }
                })
            }
        }
        return unitCostCodePriceMap;
    }

    /**
     * 汇总费用汇总价格
     * @param args
     * @returns {Promise<Map<any, any>>}
     */
    async getUnitCostSummary(args) {
        let {constructId, singleId, unitId} = args;
        // 获取默认map
        let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;

        // 创建一个新的Map对象来存储安装与建筑费用相加的值
        let unitCostSummaryPriceMap = new Map();
        if (ObjectUtils.isNotEmpty(unitProject) && unitProject.isSingleMajorFlag === true) {
            unitCostSummaryPriceMap = unitCostSummaryMap.get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL).reduce((acc, item) => {
                acc.set(item.category, NumberUtil.numberScale(item.price, je));
                return acc;
            }, new Map());
        } else {
            if (ObjectUtils.isNotEmpty(unitCostSummaryMap)) {
                unitCostSummaryMap.forEach((value, key) => {
                    if (key.includes(unitId) && !key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL)) {
                        value.forEach(item => {
                            if (unitCostSummaryPriceMap.has(item.category)) {
                                unitCostSummaryPriceMap.set(item.category, NumberUtil.numberScale(NumberUtil.add(unitCostSummaryPriceMap.get(item.category), item.price, je)));
                            } else {
                                unitCostSummaryPriceMap.set(item.category, NumberUtil.numberScale(item.price, je));
                            }
                        });
                    }
                })
            }
        }
        return unitCostSummaryPriceMap;
    }


    /**
     *  费用汇总更新费率后，修改取费表费率
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qfMajorType
     * @param param
     * @returns {Promise<void>}
     */
    async updateUnitFreeRate(constructId, singleId, unitId, qfMajorType, param) {
        // 针对不同工程专业，取费表数据更新
        let freeRateModel = new FreeRateModel();
        freeRateModel.constructId = constructId;
        freeRateModel.unitId = unitId;
        freeRateModel.qfCode = qfMajorType;
        freeRateModel.libraryCode = qfMajorType;
        if (ObjectUtils.isNotEmpty(param.category)) {
            let rate = ObjectUtils.isNotEmpty(param.rate) ? param.rate : 100;
            switch (param.category) {
                case "企业管理费":
                    freeRateModel.manageFeeRate = parseFloat(rate);
                    break;
                case "利润":
                    freeRateModel.profitRate = parseFloat(rate);
                    break;
                case "税金":
                    freeRateModel.taxRate = parseFloat(rate);
                    break;
                case "规费":
                    freeRateModel.gfRate = parseFloat(rate);
                    break;
                case "安全文明施工费":
                    freeRateModel.anwenRate = parseFloat(rate);
                    break;
                default:
                    break;
            }
        }
        await this.service.gongLiaoJiProject.gljFreeRateService.updateUnitFree(freeRateModel);
    }

    /**
     * 取费表修改费率后，修改费用汇总费率，并计算费用汇总
     * @param constructId
     * @param singleId
     * @param unitId
     * @param unitFreeRate
     */
    async updateUnitCostSummaryRate(constructId, singleId, unitId, unitFreeRate) {
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let qfMajorName;
        if (unitProject.isSingleMajorFlag === true && unitFreeRate.qfCode === unitProject.qfMajorType) {
            qfMajorName = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL;
        }

        if (unitProject.isSingleMajorFlag === false || ObjectUtils.isEmpty(unitProject.isSingleMajorFlag)) {
            qfMajorName = unitFreeRate.qfCode;
        }

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        if (ObjectUtils.isNotEmpty(unitFreeRate) && ObjectUtils.isNotEmpty(qfMajorName)) {
            // 获取当前工程专业费用汇总
            let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
                .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorName);
            if (ObjectUtils.isNotEmpty(unitCostSummarys)) {
                // 遍历费用汇总更新费率
                for (let i = 0; i < unitCostSummarys.length; i++) {
                    let unitCostSummary = unitCostSummarys[i];
                    if (ObjectUtils.isNotEmpty(unitCostSummary.category)) {
                        switch (unitCostSummary.category) {
                            case "企业管理费":
                                if (unitFreeRate.manageFeeRateUpdate === true) {
                                    unitCostSummary.isUpdateRate = true;
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.manageFeeRate) ? NumberUtil.numberScale(unitFreeRate.manageFeeRate, freeRate) : 0;
                                } else {
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.manageFeeRate) ? unitFreeRate.manageFeeRate : 0;
                                }
                                break;
                            case "利润":
                                if (unitFreeRate.profitRateUpdate === true) {
                                    unitCostSummary.isUpdateRate = true;
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.profitRate) ? NumberUtil.numberScale(unitFreeRate.profitRate, freeRate) : 0;
                                } else {
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.profitRate) ? unitFreeRate.profitRate : 0;
                                }
                                break;
                            case "税金":
                                if (unitFreeRate.taxRateUpdate === true) {
                                    unitCostSummary.isUpdateRate = true;
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.taxRate) ? NumberUtil.numberScale(unitFreeRate.taxRate, freeRate) : 0;
                                } else {
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.taxRate) ? unitFreeRate.taxRate : 0;
                                }
                                break;
                            case "规费":
                                unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.gfRate) ? unitFreeRate.gfRate : 0;
                                break;
                            case "安全文明施工费":
                                if (unitFreeRate.anwenRateUpdate === true) {
                                    unitCostSummary.isUpdateRate = true;
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.anwenRate) ? NumberUtil.numberScale(unitFreeRate.anwenRate, freeRate) : 0;
                                } else {
                                    unitCostSummary.isUpdateRate = false;
                                    unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.anwenRate) ? unitFreeRate.anwenRate : 0;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
                // 获取当前工程专业费用代码
                let unitCostCodePrices = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                    .get(unitId + FunctionTypeConstants.SEPARATOR + unitFreeRate.qfCode);
                // 重新计算费用汇总
                let unitCostSummaryArray = await this.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummarys, unitFreeRate.qfCode);
                // 更新费用汇总
                ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
                    .set(unitId + FunctionTypeConstants.SEPARATOR + unitFreeRate.qfCode, unitCostSummaryArray);
            }
        }
    }


    /**
     * 导入费用汇总
     * @param args
     */
    async importUnitCostSummary(args) {
        let {constructId, unitId, qfMajorType} = args;

        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

        // 获取费用汇总模版存放路径  D:\IdeaProjects\gaiSuan\pricing-cs\build\extraResources\excelTemplate\glj\费用汇总
        let fyhzTemplatePath;
        //判断计税
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            //简易计税
            fyhzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\glj\\费用汇总\\简易计税';
        } else {
            fyhzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\glj\\费用汇总\\一般计税';
        }

        let options = {
            properties: ['openFile'],
            defaultPath: fyhzTemplatePath, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_FYHZ]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            return ResponseData.fail('未选中任何文件');
        }

        // 获取单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 费用汇总的路径
        const qtfFilePath = result[0];
        try {
            const data = fs.readFileSync(qtfFilePath, 'utf8');
            // 使用JSON.parse()方法将JSON字符串转换为JavaScript数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段
            const unitCostSummaryArray = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.calculateFormula = line.calculateFormula;
                obj.instructions = line.instructions;
                obj.category = line.category;
                obj.rate = line.rate;
                obj.permission = line.permission;
                obj.remark = line.remark;
                obj.sequenceNbr = line.sequenceNbr;
                obj.whetherPrint = line.whetherPrint;
                obj.isSummaryLine = line.isSummaryLine;
                unitCostSummaryArray.push(obj);

                if (unitProject.isSingleMajorFlag) {
                    // 费用汇总更新费率后，修改取费表费率
                    this.updateUnitFreeRate(constructId, null, unitId, unitProject.qfMajorType, line);
                } else {
                    this.updateUnitFreeRate(constructId, null, unitId, qfMajorType, line);
                }
            });

            // 获取费用代码
            let unitCostCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

            //调用计算费用汇总
            let unitCostSummarys = await this.countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, qfMajorType);

            // // 获取费用汇总行
            // unitCostSummarys = unitCostSummarys.filter(item => item.isSummaryLine === false);

            if (unitProject.isSingleMajorFlag || (qfMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL && !unitProject.isSingleMajorFlag)) {

                // 通过定额的取费专业，查询当前取费专业的qfCode
                let baseFeeFileProject2022 = await this.service.gongLiaoJiProject.gljFreeRateService.getFeeFilesByQfCode(qfMajorType);

                // 当前专业汇总行
                let obj = new GljUnitCostSummary();
                if (ObjectUtils.isNotEmpty(baseFeeFileProject2022)) {
                    obj.name = baseFeeFileProject2022.qfName;  //名称
                    obj.calculateFormula = baseFeeFileProject2022.qfCode;  //计算基数
                    obj.instructions = baseFeeFileProject2022.qfName;  //基数说明
                    obj.price = 0;
                    obj.isSummaryLine = true;
                    obj.whetherPrint = 1;
                    unitCostSummarys.unshift(obj);
                }
            }
            // 添加汇总行，并重新计算汇总页的费用汇总计算
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePriceTotal({
                constructId: constructId,
                singleId: unitProject.parentId,
                unitId: unitId,
                unitCostSummarys: null
            });
            return ResponseData.success(unitCostSummarys);
        } catch (err) {
            return ResponseData.fail('导入失败');
        }
    }


    /**
     * 导出费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitCostSummary(args) {
        let {constructId, unitId, qfMajorType} = args;
        // 获取工程项目的费用汇总
        let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

        // 指定导出的列名
        const columns = ["dispNo", "code", "name", "calculateFormula", "instructions", "category", "rate", "permission", "remark",
            "whetherPrint", "sequenceNbr", "isSummaryLine"];

        // 获取费用汇总行
        unitCostSummarys = unitCostSummarys.filter(item => item.isSummaryLine === false);
        // 根据指定的列名来重组数据，确保导出的JSON只包含这些列
        const formattedData = unitCostSummarys.map(item => {
            return columns.reduce((acc, col) => {
                acc[col] = item[col];
                return acc;
            }, {});
        });

        // 将数组转换为JSON字符串   const jsonData = JSON.stringify(formattedData, null, 2);
        const jsonData = JSON.stringify(formattedData); // 第三个参数是缩进量，使输出更易读

        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

        // 存放费用汇总文件的路径
        let fyhzTemplatePath;
        //判断计税
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            //简易计税
            fyhzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\glj\\费用汇总\\简易计税';
        } else {
            fyhzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\glj\\费用汇总\\一般计税';
        }
        let count = this.countDirectories(fyhzTemplatePath);
        let options = {
            title: '保存文件',
            defaultPath: fyhzTemplatePath + '\\费用汇总模板' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_FYHZ]} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(FileOperatorType.File_TYPE_FYHZ)) {
                filePath += FileOperatorType.File_TYPE_FYHZ;
            }
            // 写入文件
            fs.writeFile(filePath, jsonData, (err) => {
                if (err) {
                    ResponseData.fail('写入文件时发生错误');
                } else {
                    ResponseData.success('数据已成功导出到');
                }
            });
            return ResponseData.success(filePath);
        }
    }

    /**
     * 获取当前文件夹路径下文件个数
     * @param dirPath
     * @returns {number}
     */
    countDirectories(dirPath) {
        let count = 1;
        let numbers = [];
        fs.readdirSync(dirPath).forEach((item) => {
            if (item.match(/\d+/g) !== null) {
                numbers.push(item.match(/\d+/g)[0]);
            }
        });
        if (ObjectUtils.isNotEmpty(numbers)) {
            count = Math.max(...numbers) + 1;
        }
        return count;
    }

    /**
     * 获取费用汇总应用范围
     * @param args
     * @returns {*}
     */
    async scopeOfApplicationsCostSummary(args) {
        let {constructId} = args;
        // 获取该工程项目
        let constructProjectTree = {};
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        if (ObjectUtils.isNotEmpty(constructProject)) {
            constructProjectTree.sequenceNbr = constructProject.sequenceNbr;
            constructProjectTree.name = constructProject.name;
            constructProjectTree.type = constructProject.type;
            // 添加是否被勾选标识
            constructProjectTree["ifCheck"] = false;
            constructProjectTree.parentId = constructProject.parentId;

            // 获取取费专业
            let qfList = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();
            qfList = qfList.filter(item => item.qfCode !== CommonConstants.SYSTEM_SZGC)

            let qfCodeList = qfList.map(item => item.qfCode);
            // 取费专业code,name的map集合
            let qfCodeNameMap = qfList.reduce((acc, item) => {
                acc.set(item.qfCode, item.qfName);
                return acc;
            }, new Map());

            // 添加节点属性
            await this.addPropertiesToTree(constructProject, constructProject.sequenceNbr, constructProjectTree, constructId, qfCodeList, qfCodeNameMap);
        }
        let constructProjectList = xeUtils.toTreeArray(new Array(constructProjectTree));
        constructProjectList.forEach(item => {
            item.children = [];
        });
        return constructProjectList;
    }


    /**
     * 添加节点属性
     * @param constructProject
     * @param parentId
     * @param constructProjectTree
     * @param constructId
     * @param qfCodeList
     * @param qfCodeNameMap
     */
    async addPropertiesToTree(constructProject, parentId, constructProjectTree, constructId, qfCodeList, qfCodeNameMap) {
        let singleProjectArray = [];
        if (ObjectUtils.isNotEmpty(constructProject.children)) {
            for (const node of constructProject.children) {
                let child = {};
                child.sequenceNbr = node.sequenceNbr;
                child.name = node.name;
                child.parentId = parentId;
                child.type = node.type;
                if (node.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    child.qfMajorType = node.qfMajorType;
                    let costSummaryMajorMenu = await this.getCostSummaryMajorMenuList({
                        constructId: constructId,
                        singleId: null,
                        unitId: node.sequenceNbr,
                    });
                    if (ObjectUtils.isNotEmpty(costSummaryMajorMenu)) {
                        let itemList = costSummaryMajorMenu.itemList;
                        if (ObjectUtils.isNotEmpty(itemList)) {
                            itemList.forEach(item => {
                                item.sequenceNbr = Snowflake.nextId();
                                item.ifCheck = false;
                                item.parentId = node.sequenceNbr;
                                item.type = ProjectTypeConstants.PROJECT_TYPE_UNIT_MAJOR;

                                qfCodeList.forEach(qfCode => {
                                    if (ObjectUtils.isNotEmpty(item[qfCode]) && item[qfCode] === qfCodeNameMap.get(qfCode)) {
                                        item.qfMajorType = qfCode;
                                        item.name = qfCodeNameMap.get(qfCode);
                                    }
                                });
                            });
                            child.children = itemList;
                        }
                    }
                }
                // 添加是否被勾选标识
                child["ifCheck"] = false;
                singleProjectArray.push(child);
                constructProjectTree.children = singleProjectArray;
                if (node) {
                    await this.addPropertiesToTree(node, node.sequenceNbr, child, constructId, qfCodeList, qfCodeNameMap);
                }
            }
        }
    }


    /**
     * 批量应用费用汇总
     * @param args
     */
    async batchApplicationsCostSummary(args) {
        let {constructProjectList, constructId, unitId, qfMajorType} = args;
        if (ObjectUtils.isEmpty(constructProjectList)) {
            return ResponseData.fail('请选择应用范围');
        }
        // 遍历工程单位的树，获取被勾选的单位下的工程专业List
        let constructMajorList = constructProjectList.filter(item => {
            return (item.ifCheck === true && item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT_MAJOR)
                || (item.ifCheck === true && item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && ObjectUtils.isEmpty(item.children))
        });

        // 获取费用汇总
        let costSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
        // 通过当前指定的单位、工程专业，获取工程专业模板
        let unitCostSummary = costSummaryArray.get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

        // 获取费用代码
        let costCodeCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
        // 遍历所有单位的费用汇总模板，补充专业后的所有模板
        if (ObjectUtils.isNotEmpty(constructMajorList)) {
            for (let i = 0; i < constructMajorList.length; i++) {
                let unitMajorTypeProject = constructMajorList[i];
                let unitId;
                if (unitMajorTypeProject.type === ProjectTypeConstants.PROJECT_TYPE_UNIT_MAJOR) {
                    console.log("qfMajorType----。" + unitMajorTypeProject.qfMajorType + "unitId--->" + unitMajorTypeProject.parentId);
                    unitId = unitMajorTypeProject.parentId;
                }
                if (unitMajorTypeProject.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    unitId = unitMajorTypeProject.sequenceNbr;
                }

                // 获取费用代码
                let unitCostCodePrices = costCodeCodePriceArray.get(unitId + FunctionTypeConstants.SEPARATOR + unitMajorTypeProject.qfMajorType);

                // 复制，防止修改原值
                let unitCostSummaryCopy = ConvertUtil.deepCopy(unitCostSummary);
                unitCostSummaryCopy.forEach(item => {
                    item.price = 0.00;
                });
                // 重新计算费用汇总
                await this.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummaryCopy, unitMajorTypeProject.qfMajorType);
            }
        }
        return ResponseData.success();
    }


    /**
     * 获取安文费明细汇总
     * @param args
     * @returns {Promise<*[]>}
     */
    async getAWFSummary(args) {
        let {constructId, singleId, unitId} = args;

        let awfRateMap = new Map();
        let awfPriceMap = new Map();
        let awfCalculateMoneyMap = new Map();
        let awfCalculateFormulaMap = new Map();
        let awfInstructionsMap = new Map();
        // 获取费用汇总
        let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);

        // 获取取费专业
        let qfList = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();
        qfList = qfList.filter(item => item.qfCode !== CommonConstants.SYSTEM_SZGC);

        let qfCodeList = qfList.map(item => item.qfCode);
        // 取费专业code,name的map集合
        let qfCodeNameMap = qfList.reduce((acc, item) => {
            acc.set(item.qfCode, item.qfName);
            return acc;
        }, new Map());

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let awf = precision.COST_SUMMARY.awf;
        let freeRate = precision.COST_SUMMARY.AWMX.freeRate;

        // isSingleMajorFlag = true 单专业
        let constructMajorTypeSet = new Set();
        if (ObjectUtils.isNotEmpty(unitCostSummaryMap)) {
            // 当前单位的工程专业
            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

            if (unitProject.isSingleMajorFlag) {
                constructMajorTypeSet.add(unitProject.qfMajorType);

                // 获取安全文明施工费费用汇总
                let unitCostSummaryArray = unitCostSummaryMap.get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL);
                let awf = unitCostSummaryArray.find(item => item.category === "安全文明施工费");
                if (awf.isUpdateRate === true) {
                    awfRateMap.set(unitProject.qfMajorType, NumberUtil.numberScale(awf.rate, freeRate));
                } else {
                    awfRateMap.set(unitProject.qfMajorType, awf.rate);
                }
                awfPriceMap.set(unitProject.qfMajorType, awf.price);
                awfCalculateMoneyMap.set(unitProject.qfMajorType, awf.calculateMoney);
                awfCalculateFormulaMap.set(unitProject.qfMajorType, awf.calculateFormula);
                awfInstructionsMap.set(unitProject.qfMajorType, awf.instructions);

                // isSingleMajorFlag = false 多专业
            } else {
                unitCostSummaryMap.forEach((value, key) => {
                    if (key.includes(unitId)) {
                        // 获取定额中所有的专业
                        qfCodeList.forEach(qfCode => {
                            if (key.includes(qfCode)) {
                                constructMajorTypeSet.add(qfCode);
                                // 获取安全文明施工费费用汇总
                                let awf = value.find(item => item.category === "安全文明施工费");
                                if (awf.isUpdateRate === true) {
                                    awfRateMap.set(qfCode, NumberUtil.numberScale(awf.rate, freeRate));
                                } else {
                                    awfRateMap.set(qfCode, awf.rate);
                                }
                                awfPriceMap.set(qfCode, awf.price);
                                awfCalculateMoneyMap.set(qfCode, awf.calculateMoney);
                                awfCalculateFormulaMap.set(qfCode, awf.calculateFormula);
                                awfInstructionsMap.set(qfCode, awf.instructions);
                            }
                        });
                    }
                });
            }

            // 组织安文费明细模板
            let unitAWFSummaryArray = [];
            let dispNo = 0;
            for (let key of constructMajorTypeSet) {
                let unitAwfSummary = new GljUnitAwfSummary();
                unitAwfSummary.dispNo = (++dispNo).toString();
                if (ObjectUtils.isNotEmpty(key)) {
                    // 获取定额中所有的专业
                    qfCodeList.forEach(qfCode => {
                        if (key.includes(qfCode)) {
                            unitAwfSummary.constructMajorTypeName = qfCodeNameMap.get(qfCode);
                            unitAwfSummary.constructMajorTypeCode = qfCode;//add  用于措施项目显示安文费取费专业
                        }
                    });
                }
                unitAwfSummary.calculateFormula = awfCalculateFormulaMap.get(key);
                // unitAwfSummary.rate = ObjectUtils.isNotEmpty(awfRateMap.get(key)) ? NumberUtil.numberScale(awfRateMap.get(key), precision2) : 100;
                unitAwfSummary.rate = ObjectUtils.isNotEmpty(awfRateMap.get(key)) && awfRateMap.get(key) !== 0 ? awfRateMap.get(key) : null;
                unitAwfSummary.price = awfPriceMap.get(key);
                unitAwfSummary.calculateMoney = awfCalculateMoneyMap.get(key);
                unitAwfSummary.instructions = awfInstructionsMap.get(key);
                unitAWFSummaryArray.push(unitAwfSummary);
            }
            return unitAWFSummaryArray;
        }
    }


    /**
     * 获取费用汇总或局部汇总，是否是多专业标识
     * @param args
     * @returns {Promise<*>}
     */
    async getIsSingleMajorFlag(args) {
        let {constructId, singleId, unitId, isPartFlag} = args;
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 判断是否是局部汇总
        if (isPartFlag) {
            return unitProject.isPartSingleMajorFlag;
        } else {
            return unitProject.isSingleMajorFlag;
        }

    }

    /**
     * 设置费用汇总或局部汇总，是否是多专业标识
     * @param args
     * @returns {Promise<*>}
     */
    async setIsSingleMajorFlag(args) {
        let {constructId, singleId, unitId, isPartFlag, isSingleMajorFlag, qfMajorType} = args;
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 判断是否是局部汇总
        if (isPartFlag) {
            unitProject.isPartSingleMajorFlag = isSingleMajorFlag;
            if(isSingleMajorFlag){
                unitProject.qfPartMajorType = qfMajorType;
            }
        } else {
            unitProject.isSingleMajorFlag = isSingleMajorFlag;
            unitProject.qfMajorType = qfMajorType;
        }
        // 修改单位的取费专业
        return ProjectDomain.getDomain(constructId).updateProject(unitProject);
    }


    /**
     * 获取当前单位的取费专业
     * @param args
     * @returns {Promise<*>}
     */
    async getQfMajorTypeByUnit(args) {
        let {constructId, singleId, unitId, isPartFlag} = args;
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 判断是否是局部汇总
        if (isPartFlag) {
            if (ObjectUtil.isEmpty(unitProject.qfPartMajorType)) {
                let baseFeeFileProject = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFileProject();
                baseFeeFileProject = baseFeeFileProject.filter(item => item.libraryCode === unitProject.constructMajorType && item.code.includes('0'));
                return baseFeeFileProject[0].qfCode;
                // let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByLibraryCode(unitProject.constructMajorType);
                // return gsBaseFreeRate.qfCode;
            } else {
                return unitProject.qfPartMajorType;
            }
        } else {
            return unitProject.qfMajorType;
        }
    }


    /**
     * 计算费用汇总条目
     * @param constructId
     * @param unitId
     * @param unitCostCodePriceArray  费用代码
     * @param unitCostSummaryArray  费用汇总
     */
    async countUnitCostSummaryTotalItem(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray) {
        //费用代码<费用代码,price>
        let priceMap = new Map();
        //计算基数 <费用汇总费用代号,calculateFormula>
        let codeFormulaMap = new Map();
        //费用汇总费率
        let codeRateMap = new Map();


        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        //费用代码
        for (let i = 0; i < unitCostCodePriceArray?.length; i++) {
            let unitCostCodePrice = unitCostCodePriceArray[i];
            let code = ObjectUtils.isEmpty(unitCostCodePrice.code) ? unitCostCodePrice.code : unitCostCodePrice.code.toLowerCase();
            priceMap.set(code, unitCostCodePrice.price)
        }
        if (ObjectUtils.isNotEmpty(unitCostSummaryArray)) {
            //费用汇总
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                let code = ObjectUtils.isEmpty(unitCostSummary.code) ? unitCostSummary.code : unitCostSummary.code.toLowerCase();
                if (ObjectUtils.isNotEmpty(unitCostSummary.calculateFormula)) {
                    codeFormulaMap.set(code, unitCostSummary.calculateFormula.toLowerCase());
                }
                if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                    let rate = parseFloat(unitCostSummary.rate);
                    if (unitCostSummary.isUpdateRate === true) {
                        codeRateMap.set(code, NumberUtil.numberScale(unitCostSummary.rate, freeRate));
                    } else {
                        codeRateMap.set(code, rate);
                    }
                    // codeRateMap.set(code, rate);
                } else {
                    // unitCostSummary.rate = 100;
                    codeRateMap.set(code, 100);
                }
            }

            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                if (ObjectUtils.isEmpty(unitCostSummary.calculateFormula)) {
                    continue;
                }

                //计算基数
                let calculateFormula = unitCostSummary.calculateFormula.toLowerCase();
                // 分解字符串成表达式和变量名
                const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
                //存放替换后的计算公式
                let afterCalculateFormula = calculateFormula;

                //递归计算费用汇总
                afterCalculateFormula = await this.recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je);

                unitCostSummary.calculateMoney = eval(afterCalculateFormula);
                let result;
                if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                    if (unitCostSummary.isUpdateRate === true) {
                        result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * NumberUtil.numberScale(unitCostSummary.rate / 100, freeRate);
                    } else {
                        result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * unitCostSummary.rate / 100;
                    }
                } else {
                    result = ObjectUtils.isEmpty(afterCalculateFormula) ? 0 : eval(afterCalculateFormula);
                }
                unitCostSummary.price = NumberUtil.numberScale(result, je);

                priceMap.set(ObjectUtils.isEmpty(unitCostSummary.code) ? unitCostSummary.code : unitCostSummary.code.toLowerCase(), unitCostSummary.price);
            }

        }
    }

    /**
     * 重新生成工程项目文件
     * @param args
     * @returns {Promise<void>}
     */
    async createTempConstruct(args) {
        let {constructId, singleId, unitId, deLists, csxmDeList, isPartSingleMajorFlag, qfPartMajorType, filePath} = args;
        let treeList = ResponseData.success(ProjectDomain.getDomain(constructId).getProjectTree())
        let projectTree = this.buildTree(ObjectUtils.cloneDeep(treeList.result))
        let unitProjectTree = projectTree[0];
        // 增加勾选标志
        this.markSelectedPath(unitProjectTree, unitId);

        let jbhzInfo = await this.getPartCostSummaryInfo(constructId);
        let {jbhzConstructId, jbhzFilePath} = jbhzInfo;

        if (ObjectUtils.isNotEmpty(filePath)) {
            // // 查询单位工程
            // let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            // // 导出项目
            // let options = {
            //     title: '保存文件',
            //     defaultPath: jbhzFilePath + '\\' + unitProject.name,  // 默认保存路径或者模版获取路径
            //     filters: [
            //         {name: '云算房', extensions: [FileOperatorType.File_TYPE_YSFG]}  // 可选的文件类型
            //     ]
            // };
            // let filePath = dialog.showSaveDialogSync(null, options);
            // if (ObjectUtils.isEmpty(filePath)) {
            //     return ResponseData.success();
            // } else {
            //     jbhzFilePath = filePath;
            // }
            jbhzFilePath = filePath;
        }

        // 导出项目
        if (ObjectUtils.isNotEmpty(unitProjectTree)) {
            await this.exportGljFile(unitProjectTree, jbhzFilePath, jbhzConstructId);
        }

        // 打开项目
        let ygsOperator = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
        if (!await this.service.gongLiaoJiProject.gljProjectService.checkFileExistence(jbhzFilePath)) {
            throw Error("路径有误");
        }
        //导入项目
        let importProjectObj = await PricingFileFindUtils.getProjectObjByPath(jbhzFilePath);
        //当前项目
        let projectObjById = ProjectDomain.getDomain(args.constructId).getRoot();
        if (importProjectObj.ProjectTree[0].deStandardReleaseYear !== projectObjById.deStandardReleaseYear) {
            throw Error('定额标准不一致，无法导入');
        }
        if ((importProjectObj.ProjectTree[0].projectTaxCalculation.taxCalculationMethod + '') !== (projectObjById.projectTaxCalculation.taxCalculationMethod + '')) {
            throw Error('计税方式不一致，无法导入');
        }
        let constructIdImport = importProjectObj.ProjectTree[0].sequenceNbr;     //导入文件的项目
        let contextMap = AppContext.getAllContexts();   //当前窗口打开的项目
        if (contextMap.has(constructIdImport)) {
            throw Error('不支持导入正在使用中的工程文件');
        }
        let fileProjectDomain = await ygsOperator.openFile(jbhzFilePath, false);
        jbhzConstructId = fileProjectDomain.getRoot().sequenceNbr;

        let deIdList = deLists.filter(item => item.isSelected === 1).map(item => item.sequenceNbr);
        let deTree = ProjectDomain.getDomain(jbhzConstructId).deDomain.getDe(item => item.unitId === unitId);

        // 勾选定额增加选择标志
        this.markSelectedDePath(deTree, deIdList);
        // 未被选中的定额树
        let unSelectDeTreeList = this.getUnselectedSubtrees(deTree);
        // 删除未被选中的定额树
        if (ObjectUtils.isNotEmpty(unSelectDeTreeList)) {
            for (let unSelectDeTree of unSelectDeTreeList) {
                if (unSelectDeTree.type !== DeTypeConstants.DE_TYPE_DEFAULT) {
                    await ProjectDomain.getDomain(jbhzConstructId).deDomain.removeDeRow(unSelectDeTree.sequenceNbr);
                }else {
                    let deTree = ProjectDomain.getDomain(jbhzConstructId).deDomain.getDeTree(item => item.unitId === unitId);
                    if (ObjectUtil.isNotEmpty(deTree)) {
                        let originalQuantityZeroList = deTree.filter(o => o.type !== DeTypeConstants.DE_TYPE_DEFAULT);
                        if (ObjectUtil.isNotEmpty(originalQuantityZeroList)) {
                            for (const o of originalQuantityZeroList) {
                                await ProjectDomain.getDomain(jbhzConstructId).deDomain.removeDeRow(o.deRowId);
                            }
                        }
                    }
                }
            }
        }
        // 定额
        let jbhzDeList = await this.service.gongLiaoJiProject.gljDeService.getDeAllDepth(jbhzConstructId, unitId, undefined)
        for (let de of deLists) {
            let jbhzDe = jbhzDeList.find(item => item.sequenceNbr === de.sequenceNbr);
            if (ObjectUtils.isNotEmpty(jbhzDe)) {
                jbhzDe.isSelected = de.isSelected;
            }
        }

        let csxmDeIdList = csxmDeList.filter(item => item.isSelected === 1).map(item => item.sequenceNbr);
        let csxmDeTree = ProjectDomain.getDomain(jbhzConstructId).csxmDomain.getDe(item => item.unitId === unitId);
        // 勾选定额增加选择标志
        this.markSelectedDePath(csxmDeTree, csxmDeIdList);
        // 未被选中的定额树
        let unSelectCsxmDeTreeList = this.getUnselectedSubtrees(csxmDeTree);
        // 删除未被选中的定额树
        if (ObjectUtils.isNotEmpty(unSelectCsxmDeTreeList)) {
            for (let unSelectDeTree of unSelectCsxmDeTreeList) {
                if (unSelectDeTree.type !== DeTypeConstants.DE_TYPE_DEFAULT && unSelectDeTree.type !== DeTypeConstants.DE_TYPE_FB) {
                    await ProjectDomain.getDomain(jbhzConstructId).csxmDomain.removeDeRow(unSelectDeTree.sequenceNbr, true);
                }else {
                    let deTree = ProjectDomain.getDomain(jbhzConstructId).csxmDomain.getDeTree(item => item.unitId === unitId);
                    if (ObjectUtil.isNotEmpty(deTree)) {
                        let originalQuantityZeroList = deTree.filter(o => o.type !== DeTypeConstants.DE_TYPE_DEFAULT && o.type !== DeTypeConstants.DE_TYPE_FB
                            && o.type !== DeTypeConstants.DE_TYPE_ZFB && o.type !== DeTypeConstants.DE_TYPE_DELIST);
                        if (ObjectUtil.isNotEmpty(originalQuantityZeroList)) {
                            for (const o of originalQuantityZeroList) {
                                await ProjectDomain.getDomain(jbhzConstructId).csxmDomain.removeDeRow(o.deRowId);
                            }
                        }
                    }
                }
            }
        }

        // 措施项目
        let jbhzCsxmDeList =await this.service.gongLiaoJiProject.gljStepItemCostService.getDeAllDepth(jbhzConstructId, unitId, undefined);
        for (let csxmDe of csxmDeList) {
            let jbhzCsxmDe = jbhzCsxmDeList.find(item => item.sequenceNbr === csxmDe.sequenceNbr);
            if (ObjectUtils.isNotEmpty(jbhzCsxmDe)) {
                jbhzCsxmDe.isSelected = csxmDe.isSelected;
            }
            // 处理安文费
            if (csxmDe.isCostDe === CostDeMatchConstants.NON_COST_DE) {
                let jbhzCsxmAwfDe = jbhzCsxmDeList.find(item => item.deName === csxmDe.deName && item.deCode === csxmDe.deCode);
                if (ObjectUtils.isNotEmpty(jbhzCsxmAwfDe)) {
                    jbhzCsxmAwfDe.isSelected = csxmDe.isSelected;
                }
            }
        }

        let csxmDeTreessssss = ProjectDomain.getDomain(jbhzConstructId).csxmDomain.getDe(item => item.unitId === unitId);  // todo bug
        let csxmDes = ProjectDomain.getDomain(jbhzConstructId).csxmDomain.getDeTree(item => item.unitId === unitId);  // todo bug

        // 设定费用汇总
        let param = {
            constructId: jbhzConstructId,
            singleId,
            unitId,
        }
        if (isPartSingleMajorFlag === true) {
            param.isSingleMajorFlag = true
            param.isPartFlag = false
            param.qfMajorType = qfPartMajorType
        }else {
            param.isSingleMajorFlag = false
            param.qfMajorType = ""
        }
        await this.service.gongLiaoJiProject.gljUnitCostSummaryService.supplyCostSummaryMajors(param)
    }


    async exportGljFile(unitProjectTree, filePath, constructId) {
        if (filePath && !filePath.canceled) {
            if (!filePath.toUpperCase().endsWith(CommonConstants.GAISUAN__FILE_DOT_SUFFIX)) {
                filePath += CommonConstants.GAISUAN__FILE_DOT_SUFFIX;
            }

            if (fs.existsSync(filePath)) {
                let obj = await PricingFileFindUtils.getProjectObjByPath(filePath);
                if (!ObjectUtils.isEmpty(obj)) {
                    let projectObj = ProjectDomain.getDomain(obj.ProjectTree[0].sequenceNbr).getRoot();
                    if (!ObjectUtils.isEmpty(projectObj)) {
                        return ResponseData.success(2);
                    }
                }
            }
            //导出的ysf
            let copyObj = await this.service.gongLiaoJiProject.gljCommonService.exportGljHandler(unitProjectTree, filePath, constructId);
            await YGLJOperator.writeFileWithPath1(copyObj);
            AppContext.removeContext(copyObj.sequenceNbr);

            return ResponseData.success(1);
        }
        return ResponseData.success(0);
    }


    buildTree(items) {
        // 创建一个映射表，用于快速查找节点
        const map = {};
        items.forEach(item => {
            map[item.sequenceNbr] = { ...item, children: [] };
        });

        // 构建树结构
        const tree = [];
        items.forEach(item => {
            const node = map[item.sequenceNbr];
            const parentId = item.parentId;

            // 如果没有父节点ID或找不到父节点，则作为根节点
            if (!parentId || !map[parentId]) {
                tree.push(node);
            } else {
                // 否则添加到父节点的children中
                map[parentId].children.push(node);
            }
        });
        return tree;
    }

    markSelectedPath(node, targetSeq) {
        if (!node) return false;
        // 先假设当前节点不是目标节点，selected = false
        let foundInChildren = false;
        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                // 递归标记子节点
                const found = this.markSelectedPath(child, targetSeq);
                if (found) foundInChildren = true;
            }
        }
        // 当前节点是目标节点，或者子节点中有目标节点，标记为 true，否则 false
        node.selected = (node.sequenceNbr === targetSeq) || foundInChildren;
        node.id = node.sequenceNbr;
        return node.selected;
    }

    /**
     * 获取所有 selected === false 的子树列表
     * @param {Object} node - 当前节点
     * @returns {Array} - selected 为 false 的子树数组
     */
    getUnselectedSubtrees(node) {
        if (!node) return [];

        // 如果当前节点 selected 为 false，直接将该节点（及其子树）作为一个子树返回
        if (node.selected === false) {
            return [node];
        }

        // 否则递归遍历子节点，收集所有 selected 为 false 的子树
        let result = [];
        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                result = result.concat(this.getUnselectedSubtrees(child));
            }
        }
        return result;
    }

    markSelectedDePath(node, targetSeqList) {
        if (!node) return false;
        let foundInChildren = false;
        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                const found = this.markSelectedDePath(child, targetSeqList);
                if (found){
                    foundInChildren = true;
                }
            }
        }
        // 判断当前节点的 sequenceNbr 是否在 targetSeqList 数组中
        node.selected = targetSeqList.includes(node.sequenceNbr) || foundInChildren;
        node.id = node.sequenceNbr;
        return node.selected;
    }

    findSubtreeBySequenceNbr(node, targetSeq) {
        if (!node) return null;

        // 如果当前节点就是目标，返回当前节点（包含完整子树）
        if (node.sequenceNbr === targetSeq) {
            return node;
        }

        if (node.children && node.children.length) {
            for (const child of node.children) {
                const childPathTree = this.findSubtreeBySequenceNbr(child, targetSeq);
                if (childPathTree) {
                    // 找到目标节点在某个子树中，返回当前节点的浅拷贝，children只包含这条路径
                    return {
                        ...node,
                        children: [childPathTree]
                    };
                }
            }
        }
        // 未找到
        return null;
    }


    async getPartCostSummaryInfo(constructId) {
        let modifiedConstructId = constructId.slice(0, 2) + "_" + constructId.slice(2);
        let jbhzConstructId = "jbhz_" + modifiedConstructId;
        let name = "jbhz_" + modifiedConstructId;
        let jbhzFilePath = await this.service.gongLiaoJiProject.gljAppService.getSetStoragePath(name);

        return {jbhzFilePath, jbhzConstructId};
    }

    async closePartCostSummary(args) {
        let {constructId, unitId} = args;
        let jbhzInfo = await this.getPartCostSummaryInfo(constructId);
        let {jbhzConstructId, jbhzFilePath} = jbhzInfo;
        let contextMap = AppContext.getAllContexts();   //当前窗口打开的项目
        if (contextMap.has(jbhzConstructId)) {
            await AppContext.removeContext(jbhzConstructId);
        }
        // 检查文件是否存在
        fs.access(jbhzFilePath, fs.constants.F_OK, (err) => {
            if (!err) {
                // 文件存在，执行删除操作
                fs.unlink(jbhzFilePath, (deleteErr) => {
                    if (deleteErr) {
                        console.error('文件删除失败', deleteErr);
                    } else {
                        console.log('文件删除成功');
                    }
                });
            }
        });
    }

    /**
     * 局部汇总-获取定额 专业列表
     * @param args
     * @returns {ResponseData}
     */
    async getCostSummaryMajorByDeList(args) {
        let {constructId, singleId, unitId, deLists = [], csxmDeList = []} = args;
        let majorList = [];
        // let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // deLists.push(...csxmDeList);
        // let groupDe = this.service.gongLiaoJiProject.gljUnitCostCodePriceService.groupByColumn(deLists, "costFileCode")
        // groupDe = this.service.gongLiaoJiProject.gljUnitCostCodePriceService.mergeSystemSzgcGroup(groupDe, CommonConstants.SYSTEM_SZGC, unitProject.qfMajorType)
        // let deMajors = Object.keys(groupDe).filter(item => ObjectUtils.isNotEmpty(item) && item !== "undefined")
        // for (const deMajor of deMajors) {
        //     let major = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisTypeOne(deMajor);
        //     if (ObjectUtils.isNotEmpty(major)) {
        //         let item = {};
        //         item[deMajor] = major.qfMajorName
        //         majorList.push(item);
        //     }
        // }

        // 获取取费表列表数据  根据取费表中的专业顺序进行排序  todo
        let qfbList = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList({
            constructId:constructId,
            singleId:singleId,
            unitId:unitId
        })
        qfbList = qfbList.sort((a, b) => a.sortNo - b.sortNo);
        let constructMajorTypeSet = [...new Set(qfbList.map(item => item.qfCode))];

        for (const deMajor of constructMajorTypeSet) {
            let major = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisTypeOne(deMajor);
            if (ObjectUtils.isNotEmpty(major)) {
                let item = {};
                item[deMajor] = major.qfMajorName;
                majorList.push(item);
            }
        }
        return majorList;
    }

}

GljUnitCostSummaryService.toString = () => '[class GljUnitCostSummaryService]';
module.exports = GljUnitCostSummaryService;