'use strict';

const { Service } = require('../../../../core');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const ProjectDomain = require('../../domains/ProjectDomain');
const FunctionTypeConstants = require('../../constants/FunctionTypeConstants');
const CostDeMatchConstants = require('../../constants/CostDeMatchConstants');
const WildcardMap = require('../../core/container/WildcardMap');
const { NumberUtil } = require('../../../../electron/utils/NumberUtil');
const DeTypeConstants = require('../../constants/DeTypeConstants');
const { In } = require('typeorm');
const { BaseDeGtfMajorRelation2022 } = require('../../models/BaseDeGtfMajorRelation2022');
const { BaseCslb2022 } = require('../../models/BaseCslb2022');
const { BaseDe2022 } = require('../../models/BaseDe2022');
const { ArrayUtil } = require('../../../../common/ArrayUtil');
const { Snowflake } = require('../../../../electron/utils/Snowflake');
const StandardDeModel = require('../../domains/deProcessor/models/StandardDeModel');
const { BaseFeeFile2022 } = require('../../models/BaseFeeFile2022');

/**
 * 总价措施费记取
 */
class GljZjcsCostMatchService extends Service {

  constructor(ctx) {
    super(ctx);
    this.baseDe2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDe2022);
    this.baseCSLB2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseCslb2022);
    this.baseDeGtfMajorRelation2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDeGtfMajorRelation2022);
    this.baseFeeFile2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseFeeFile2022);
    this.baseDeTypeArr = [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_RESOURCE, DeTypeConstants.DE_TYPE_USER_RESOURCE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE, DeTypeConstants.DE_TYPE_ZHUANSHI_FEE];
  }

  async zjcsCostMath(args) {
    let {
      constructId,
      singleId,
      unitId,
      data,
      unitIdList,
      increaseFeeHeight,
      heatingFee,
      rainySeasonConstruction,
      csfyCalculateBaseCode,
      csfyCalculateBaseArea,
      zzy
    } = args;
    // 设置缓存
    const unitZjcsCache = await this.zjcsCostMathCache(args);
    if (ObjectUtils.isEmpty(unitZjcsCache)) {
      const zjcdCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_ZJCS_COST_MATCH_CACHE);
      if (ObjectUtils.isEmpty(zjcdCache)) {
        let cacheObj = {};
        cacheObj[unitId] = args;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_ZJCS_COST_MATCH_CACHE, cacheObj);
      } else {
        zjcdCache[unitId] = args;
      }
    } else {
      ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_ZJCS_COST_MATCH_CACHE)[unitId] = args;
    }
    //过滤掉不计取的
    data = data.filter(k => k.isCheck == 1 && ObjectUtils.isNotEmpty(k.sequenceNbr));
    if (ObjectUtils.isEmpty(data)) {
      return;
    }
    for (const uId of unitIdList) {
      if (uId != unitId) {
        // 如果不是当前的单位的记取 需要把本次页面的设置信息同步到其他的单位工程中
        let param = {
          constructId: constructId,
          unitId: uId
        };
        const zjcsCache = await this.zjcsCostMathCache(param);
        if (ObjectUtils.isEmpty(zjcsCache)) {
          const cloneDeep = ObjectUtils.cloneDeep(args);
          cloneDeep.unitId = uId;
          delete cloneDeep.singleId;
          delete cloneDeep.data;
          delete cloneDeep.unitIdList;
          ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_ZJCS_COST_MATCH_CACHE)[uId] = cloneDeep;
        } else {
          zjcsCache.increaseFeeHeight = increaseFeeHeight;
          zjcsCache.heatingFee = heatingFee;
          zjcsCache.rainySeasonConstruction = rainySeasonConstruction;
          zjcsCache.csfyCalculateBaseCode = csfyCalculateBaseCode;
          zjcsCache.csfyCalculateBaseArea = csfyCalculateBaseArea;
          zjcsCache.zzy = zzy;
        }
      }
      // 删除当前单位的历史的费用定额
      await this.service.gongLiaoJiProject.gljConstructCostMathService.delCostDe(uId, null, constructId, [CostDeMatchConstants.ZJCS_DE]);
      // 获取总价措施需要的基数定额数据
      const yssAllData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === uId);
      const csxmAllData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === uId);
      let baseDeArr = [];
      baseDeArr = baseDeArr.concat(yssAllData.filter(item => this.baseDeTypeArr.includes(item.type) && item.isCostDe != CostDeMatchConstants.ZJCS_DE && item.isCostDe != CostDeMatchConstants.AZ_DE));
      baseDeArr = baseDeArr.concat(csxmAllData.filter(item => this.baseDeTypeArr.includes(item.type) && item.isCostDe != CostDeMatchConstants.ZJCS_DE && item.isCostDe != CostDeMatchConstants.AZ_DE));
      if (ObjectUtils.isEmpty(baseDeArr)) {
        continue;
      }
      // 将所有基数定额按施工组织措施类别进行分组
      const baseDeMap = ArrayUtil.group(baseDeArr, 'measureType');
      if (baseDeMap.has(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT)) {
        if (ProjectDomain.getDomain(constructId).getProjectById(unitId).deLibrary == '2022-SZGC-DEK') {
          // 如果有随主工程 那么把随主工程定额加到主专业对应的分组中
          const szgcDeArr = baseDeMap.get(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
          let zzyDeArr = baseDeMap.get(zzy.cslbName);
          zzyDeArr = zzyDeArr || [];
          zzyDeArr.push(...szgcDeArr);
          baseDeMap.set(zzy.cslbName, zzyDeArr);
          baseDeMap.delete(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
        } else {
          // 在工料机逻辑中 非市政工程单位  如果有随主工程的基数定额  直接删除  不作为基数定额
          baseDeMap.delete(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
        }
      }
      for (const cost of data) {
        let parent = csxmAllData.filter(item => item.sequenceNbr == cost.sequenceNbr);
        if (ObjectUtils.isEmpty(parent)) {
          // 同专业记取，找到相同类型的清单
          parent = csxmAllData.filter(k => k.deName == cost.qdName && k.pricingMethod == 2
            && k.type == DeTypeConstants.DE_TYPE_DELIST
            && ObjectUtils.isNotEmpty(k.parent)
            && k.parent.deName != '不可竞争措施项目'
          );
        }
        if (ObjectUtils.isEmpty(parent)) {
          continue;
        }
        parent.sort((a, b) => a.index - b.index);
        parent = parent[0];
        // 计算每个措施类别对应的计算基数
        let costBase = await this.costBase(baseDeMap, args, uId);
        // 确定该清单所需要的费用定额是那些 ,排除不符合条件的定额
        let costDeList = await this.getCostDeByQdzjcs(cost, baseDeMap, increaseFeeHeight, parent, args, uId);
        for (const costDe of costDeList) {
          //获取计算基数
          let deMathBase = costBase[costDe.rateName];
          // 判断是否计算基数乘以0.5
          //雨季施工增加费   冬季施工增加费
          if (heatingFee && costDe.zjcsClassCode === '1') {
            deMathBase = NumberUtil.multiply(deMathBase, 0.5);
          }
          if (rainySeasonConstruction && costDe.zjcsClassCode === '2') {
            deMathBase = NumberUtil.multiply(deMathBase, 0.5);
          }
          //赋值计算基数
          costDe.formula = deMathBase;
          costDe.caculatePrice = 1;
          costDe.baseNum = { def: deMathBase };
          let deRow = await ProjectDomain.getDomain(constructId).csxmDomain.createDeRow(costDe);
          deRow = await ProjectDomain.getDomain(constructId).csxmDomain.appendBaseDe(constructId, uId, costDe.standardId, deRow.deRowId);
          deRow.measureType = null; // 总价措施费用定额施工组织措施类别为空
          deRow.costMajorName = costDe.costMajorName;
          deRow.costFileCode = costDe.costFileCode;
          await ProjectDomain.getDomain(constructId).csxmDomain.updateQuantity(constructId, uId, deRow.deRowId, '1');
        }
      }
    }
  }

  async clearZjcsCost(args) {
    let { constructId, unitIdList } = args;
    for (const unitId of unitIdList) {
      let deLibrary = ProjectDomain.getDomain(constructId).getProjectById(unitId).deLibrary;
      // 删除历史的费用定额
      await this.service.gongLiaoJiProject.gljConstructCostMathService.delCostDe(unitId, null, constructId, [CostDeMatchConstants.ZJCS_DE]);
      // 删除总价措施缓存
      const zjcsCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_ZJCS_COST_MATCH_CACHE);
      if (ObjectUtils.isNotEmpty(zjcsCache) && ObjectUtils.isNotEmpty(zjcsCache[unitId])) {
        let unitCache = zjcsCache[unitId];
        if (ObjectUtils.isNotEmpty(unitCache.data)) {
          for (const item of unitCache.data) {
            item.isCheck = this.checkZjcsCost(item, deLibrary);
          }
        }
        unitCache.csfyCalculateBaseCode = null;
        unitCache.heatingFee = null;
        unitCache.rainySeasonConstruction = null;
      }
    }
  }

  checkZjcsCost(item, deLibrary) {
    if (item.zjcsClassCode === '11' || item.zjcsClassCode === '12') {
      return 0;
    }
    if (deLibrary == '2024-FGJZ-DEG') {
      if (item.zjcsClassCode == '16' && !item.zjcsClassName.includes('2.5m')) {
        // 25仿古 高台增加费只默认勾选2.5m的
        return 0;
      }
    } else if (deLibrary == '2024-GJXSGC-DEG') {
      if (item.zjcsClassCode == '16' && !item.zjcsClassName.includes('2~5m')) {
        // 25仿古建修缮  高台增加费只默认勾选2~5m的
        return 0;
      }
    }
    return 1;
  }

  async getCostDeByQdzjcs(cost, baseDeMap, increaseFeeHeight, parent, args, uId) {
    //获取所有的施工组织措施类别
    const cslist = Array.from(baseDeMap.keys());
    //获取措施项目费用定额  总价措施费用定额
    let baseCSLBDTOS = await this.baseCSLB2022Dao.find({
      where: { cslbName: In(cslist) },
      order: { sortNoFull: 'ASC' }
    });

    let cslbCodeList = baseCSLBDTOS.map(k => k.cslbCode);
    //如果是22定额标准 则从 22 表查
    let baseDeDTOS = await this.baseDe2022Dao.find({
      where: { cslbCode: In(cslbCodeList), isZj: 1 },
      order: { sortNo: 'ASC' }
    });

    //走到这里就是高台增加费
    if (cost.zjcsClassCode === '16') {
      if (!ObjectUtils.isEmpty(increaseFeeHeight)) {
        for (const cs of cslist) {
          //获取高台增加费定额
          let baseDeDTO = await this.queryBaseDe(cs, increaseFeeHeight);
          if (!ObjectUtils.isEmpty(baseDeDTO)) {
            for (let i = baseDeDTOS.length - 1; i >= 0; i--) {
              let element = baseDeDTOS[i];
              if (element.zjcsClassCode === baseDeDTO.zjcsClassCode && element.deCode !== baseDeDTO.deCode) {
                baseDeDTOS.splice(i, 1); // 删除元素
              }
            }
          } else {
            baseDeDTOS = [];
          }
        }
      }
    }

    let deList = [];
    deList = baseDeDTOS.filter(k => (k.zjcsClassCode === cost.zjcsClassCode));
    if (cost.zjcsClassCode === '16' && ObjectUtils.isNotEmpty(deList)) {
      deList = deList.filter(k => k.deCode == cost.deCode);
    }
    //计取地区筛选
    if (ObjectUtils.isNotEmpty(args.csfyCalculateBaseArea) && args.csfyCalculateBaseArea === CostDeMatchConstants.calculateBaseAreaZhangCheng) {
      deList = deList.filter(o => !o.deName.includes(CostDeMatchConstants.calculateBaseAreaOtherName));
    } else {
      deList = deList.filter(o => !o.deName.includes(CostDeMatchConstants.calculateBaseAreaZhangChengName));
    }
    if (ObjectUtils.isNotEmpty(deList)) {
      let sortDeList = [];
      for (const item of baseCSLBDTOS) {
        let cslbDeList = deList.filter(de => de.cslbCode == item.cslbCode);
        if (ObjectUtils.isNotEmpty(cslbDeList)) {
          cslbDeList.sort((a, b) => a.sortNo - b.sortNo);
        }
        sortDeList = sortDeList.concat(cslbDeList);
      }
      deList = sortDeList;
    }
    return this.deDataConvert(deList, parent, args, uId);
  }

  async deDataConvert(deList, qd, args, uId) {
    let array = [];
    let { constructId, singleId, unitId } = args;
    for (const de of deList) {
      let measureProjectDTO = new StandardDeModel(constructId, uId, Snowflake.nextId(), qd.deRowId, DeTypeConstants.DE_TYPE_DE);
      measureProjectDTO.fxCode = de.deCode;
      measureProjectDTO.bdCode = de.deCode;
      measureProjectDTO.name = de.deName;
      measureProjectDTO.quantityExpression = '1';
      measureProjectDTO.quantityExpressionNbr = 1;
      measureProjectDTO.quantity = 1;
      measureProjectDTO.isCostDe = CostDeMatchConstants.ZJCS_DE;
      measureProjectDTO.standardId = de.sequenceNbr;
      measureProjectDTO.libraryCode = de.libraryCode;
      measureProjectDTO.zjcsClassCode = de.zjcsClassCode;
      measureProjectDTO.unit = de.unit;
      measureProjectDTO.rateName = de.rateName;
      measureProjectDTO.libraryCode = de.libraryCode;
      const baseFeeFile = await this.baseFeeFile2022Dao.findOne({ where: { qfCode: de.qfCode } });
      if (ObjectUtils.isNotEmpty(baseFeeFile)) {
        measureProjectDTO.costMajorName = baseFeeFile.qfName;
        measureProjectDTO.costFileCode = baseFeeFile.qfCode;
      }
      array.push(measureProjectDTO);
    }
    return array;

  }

  async queryBaseDe(cslx, height) {
    let baseDeGtfMajorRelationEntity = await this.baseDeGtfMajorRelation2022Dao.findOne({ where: { addRateHeight: height } });
    let deCode = null;
    if (cslx === '仿古建筑工程') {
      deCode = baseDeGtfMajorRelationEntity.fgjzgc;
    }
    if (cslx === '绿化工程') {
      deCode = baseDeGtfMajorRelationEntity.lhgc;
    }
    if (cslx === '园林工程') {
      deCode = baseDeGtfMajorRelationEntity.ylgc;
    }
    if (cslx === '古建（明清）修缮工程') {
      deCode = baseDeGtfMajorRelationEntity.gjxsgc;
    }
    if (!ObjectUtils.isEmpty(deCode)) {
      let list = await this.baseDe2022Dao.find({
        where: { deCode: deCode, isZj: 1 }
      });
      if (!ObjectUtils.isEmpty(list)) {
        return list[0];
      }
    }
    return null;
  }

  async costBase(baseDeMap, args, uId) {
    //获取所有的施工组织措施类别
    const cslist = Array.from(baseDeMap.keys());
    let result = {};
    for (const cs of cslist) {
      let deList = baseDeMap.get(cs);
      //计算基数
      result[cs] = await this.mathBaseDeRjHj(deList, args, uId);
    }
    return result;
  }

  async mathBaseDeRjHj(deList, args, uId) {
    let { unitId, singleId, constructId } = args;
    //获取计取基数
    let csfyCalculateBaseCode = '';
    const cacheObj = await this.zjcsCostMathCache(args);
    if (ObjectUtils.isEmpty(cacheObj) || ObjectUtils.isEmpty(cacheObj.csfyCalculateBaseCode)) {
      // 多单位记取时  缓存只缓存在了主单位中，其他选择的单位没有缓存数据  所以需要从参数中获取
      csfyCalculateBaseCode = args.csfyCalculateBaseCode;
    } else {
      csfyCalculateBaseCode = cacheObj.csfyCalculateBaseCode;
    }
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let rSum = 0;
    let jSum = 0;
    //循环基数定额集合
    for (const de of deList) {
      //   人工费定额价合价 + 机械费定额价合价     ||   人工费市场价合价 + 机械费市场价合价
      if (csfyCalculateBaseCode === CostDeMatchConstants.RGFSCJ_JXFSCJ) {
        // 市场价
        rSum = NumberUtil.add(NumberUtil.numberScale(de.rTotalSum, precision.EDIT.DE.rTotalSum), rSum);
        jSum = NumberUtil.add(NumberUtil.numberScale(de.jTotalSum, precision.EDIT.DE.jTotalSum), jSum);
      } else {
        // 定额价
        rSum = NumberUtil.add(NumberUtil.numberScale(de.rdTotalSum, precision.EDIT.DE.rTotalSum), rSum);
        jSum = NumberUtil.add(NumberUtil.numberScale(de.jdTotalSum, precision.EDIT.DE.jTotalSum), jSum);
      }
    }
    return NumberUtil.add(rSum, jSum);
  }

  getRJPriceSCJ(item, taxCalculationMethod, csfyCalculateBaseCode) {
    if (csfyCalculateBaseCode === CostDeMatchConstants.RGFSCJ_JXFSCJ) {
      return taxCalculationMethod == 1 ? item.marketPrice : item.marketTaxPrice;
    }
    return taxCalculationMethod == 1 ? item.baseJournalPrice : item.baseJournalTaxPrice;
  }

  async zjcsClassList(args) {
    let { unitId, singleId, constructId } = args;
    //组装数据
    let resultModel = {};
    let zjcsClassList = [];
    let zjcsqdModelList = [];


    const yssAllData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    const csxmAllData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);

    //获取措施项目下的所有措施项  并且是定额组价
    const csxArr = csxmAllData.filter(item => item.type == DeTypeConstants.DE_TYPE_DELIST && item.pricingMethod == 2);
    const bkjzNode = csxmAllData.find(item => item.deName == '不可竞争措施项目');
    for (const item of csxArr) {
      if (ObjectUtils.isNotEmpty(bkjzNode) && item.parentId == bkjzNode.deRowId) {
        // 过滤不可竞争措施项目下的内容
        continue;
      }
      zjcsqdModelList.push({
        sequenceNbr: item.sequenceNbr,
        zjcsClassCode: ObjectUtils.isEmpty(item.zjcsClassCode) ? '' : item.zjcsClassCode,
        qdName: ObjectUtils.isEmpty(item.deName) ? '' : item.deName,
        qdCode: ObjectUtils.isEmpty(item.deName) ? '' : item.deCode,
        isCheck: 0
      });
    }
    const yssDeArr = yssAllData.filter(item => this.baseDeTypeArr.includes(item.type));
    const csxmDeArr = csxmAllData.filter(item => this.baseDeTypeArr.includes(item.type) && item.isCostDe != CostDeMatchConstants.ZJCS_DE);

    //判断是否需要记取总价措施费用
    if (ObjectUtils.isEmpty(yssDeArr) && ObjectUtils.isEmpty(csxmDeArr)) {
      return null;
    }
    let fbcs = yssDeArr.map(k => k.measureType);
    let djcs = csxmDeArr.map(k => k.measureType);
    fbcs.push(...djcs);
    //去重施工组织措施类别
    let cslbList = [...new Set(fbcs)];
    let libraryCode = ProjectDomain.getDomain(constructId).getProjectById(unitId).deLibrary;
    if (libraryCode != '2022-SZGC-DEK') {
      // 去掉随主工程
      cslbList = cslbList.filter(c => c != CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
    } else {
      // 市政工程时，如果有随主工程，则将市政的所有措施类别数据都进行展示
      if (cslbList.includes(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT)) {
        // 先去掉随主工程
        cslbList = cslbList.filter(c => c != CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
        let baseCSLBDTOS = await this.baseCSLB2022Dao.find();
        const szList = baseCSLBDTOS.filter(item => item.libraryCode == '2022-SZGC-DEK');
        // 再把市政所有的都添加进去
        szList.map(item => cslbList.push(item.cslbName));
        cslbList = [...new Set(cslbList)];
      }
    }
    //总价措施费用定额
    let baseCSLBDTOS = await this.baseCSLB2022Dao.find();
    let clsbCodes = baseCSLBDTOS.filter(item => cslbList.includes(item.cslbName)).map(k => k.cslbCode);

    let baseDeDTOS = await this.baseDe2022Dao.find({
      where: { cslbCode: In(clsbCodes), isZj: 1 },
      order: { sortNo: 'ASC' }
    });


    let baseDeDistinctDTOS = [];
    let deLibrary = ProjectDomain.getDomain(constructId).getProjectById(unitId).deLibrary;
    if (deLibrary == '2024-FGJZ-DEG' || deLibrary == '2024-GJXSGC-DEG') {
      this.handelGtzjFee(baseDeDTOS);
    }
    // 根据属性进行去重
    baseDeDistinctDTOS = baseDeDTOS.filter(dto => !ObjectUtils.isEmpty(dto.zjcsClassCode)).filter((item, index, self) => {
      return index === self.findIndex((i) => (i.zjcsClassName === item.zjcsClassName));
    });

    //排序
    baseDeDistinctDTOS.sort((a, b) => parseInt(a.zjcsClassCode) - parseInt(b.zjcsClassCode));

    let libraryCodeSet = new Set();
    for (const baseDeDTO of baseDeDistinctDTOS) {
      libraryCodeSet.add(baseDeDTO.libraryCode);
    }

    //封装总价措施列表数据
    for (const baseDeDTO of baseDeDistinctDTOS) {
      zjcsClassList.push({
        ['qdName']: baseDeDTO.zjcsClassName,
        ['deCode']: baseDeDTO.deCode,
        ['zjcsClassCode']: baseDeDTO.zjcsClassCode,
        ['zjcsClassName']: baseDeDTO.zjcsClassName,
        ['isCheck']: this.checkZjcsCost(baseDeDTO, deLibrary)
      });
    }

    // zjcsClassQd是下拉选项中的所有措施项
    resultModel.zjcsClassQd = zjcsqdModelList;
    // zjcsClassList是所有可以记出来的费用定额项
    resultModel.zjcsClassList = zjcsClassList;
    // zzy是定额的措施类别是随主工程的时，主专业的可选项   因为只有【市政】的有随主工程  所以此处只选市政的
    resultModel.zzy = baseCSLBDTOS.filter(item => item.libraryCode == '2022-SZGC-DEK');
    return resultModel;
  }

  handelGtzjFee(baseDeDTOS) {
    let gtzje = baseDeDTOS.filter(p => p.zjcsClassCode == '16');
    if (ObjectUtils.isNotEmpty(gtzje)) {
      for (let obj of gtzje) {
        // let name = this.switchGtzjfName1(obj.deName);
        // obj.zjcsClassName = obj.zjcsClassName + name;
        obj.zjcsClassName = obj.deName;
      }
    }
  }

  /**
   * 高台增加费名称转换
   * @returns {string}
   */
  switchGtzjfName1(deName) {
    let result = '';

    if (deName.includes('2.5m以内')) {
      result = '（2.5m）';
    } else if (deName.includes('6m以内')) {
      result = '（6m以内）';
    } else if (deName.includes('2~5m')) {
      result = '（2-5m）';
    } else if (deName.includes('5m以上')) {
      result = '（5m以上）';
    }
    return result;
  }
  async gtfResource(args) {
    return await this.app.appDataSource.getRepository(BaseDeGtfMajorRelation2022).find();
  }

  async zjcsCostMathCache(args) {
    let { unitId, singleId, constructId } = args;
    const zjcsCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_ZJCS_COST_MATCH_CACHE);
    if (ObjectUtils.isNotEmpty(zjcsCache)) {
      return zjcsCache[unitId];
    }
    return null;
  }

  async queryCalculateBaseDropDownList(type) {
    return [
      {
        code: 'RGF_DEJ+JXF_DEJ',
        name: '人工费基期价+机械费基期价'
      },
      {
        code: 'RGF+JXF',
        name: '人工费市场价+机械费市场价'
      }
    ];
  }

  async queryParticipationZjcsMatch(arg) {
    let { unitId, singleId, constructId } = arg;
    let obj = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_NO_PARTICIPATION_ZJCS_MATCH);
    if (ObjectUtils.isNotEmpty(obj)) {
      return ObjectUtils.isNotEmpty(obj[unitId]) ? obj[unitId] : false;
    } else {
      return false;
    }
  }

  // -------------------------------------------------------------------------------------------------------------------


  async autoMatchZjcsCost(args) {
    let { unitId, singleId, constructId } = args;

    const csxmAllData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);
    const zjcsCostDeArr = csxmAllData.filter(item => item.type == DeTypeConstants.DE_TYPE_DE && item.isCostDe == CostDeMatchConstants.ZJCS_DE);
    if (ObjectUtils.isEmpty(zjcsCostDeArr)) {
      return;
    }
    const cacheObj = await this.zjcsCostMathCache(args);
    const yssAllData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    // 总价措施的所有基数定额
    let baseDeArr = [];
    baseDeArr = baseDeArr.concat(yssAllData.filter(item => this.baseDeTypeArr.includes(item.type) && item.isCostDe != CostDeMatchConstants.ZJCS_DE && item.isCostDe != CostDeMatchConstants.AZ_DE));
    baseDeArr = baseDeArr.concat(csxmAllData.filter(item => this.baseDeTypeArr.includes(item.type) && item.isCostDe != CostDeMatchConstants.ZJCS_DE && item.isCostDe != CostDeMatchConstants.AZ_DE));
    // 将所有基数定额按施工组织措施类别进行分组
    const baseDeMap = ArrayUtil.group(baseDeArr, 'measureType');
    if (baseDeMap.has(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT)) {
      if (ProjectDomain.getDomain(constructId).getProjectById(unitId).deLibrary == '2022-SZGC-DEK') {
        // 如果有随主工程 那么把随主工程定额加到主专业对应的分组中
        const szgcDeArr = baseDeMap.get(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
        if (ObjectUtils.isEmpty(cacheObj)) {
          // 如果市政专业 基数定额有属主工程  但是没有缓存数据  那么不计取
          return;
        }
        let zzyDeArr = baseDeMap.get(cacheObj.zzy.cslbName);
        zzyDeArr = zzyDeArr || [];
        zzyDeArr.push(...szgcDeArr);
        baseDeMap.set(cacheObj.zzy.cslbName, zzyDeArr);
        baseDeMap.delete(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
      } else {
        // 在工料机逻辑中 非市政工程单位  如果有随主工程的基数定额  直接删除  不作为基数定额
        baseDeMap.delete(CostDeMatchConstants.TITLE_WITH_MARJOR_PROJECT);
      }
    }
    // 获取措施类别对应的计算基数
    const costBase = await this.costBase(baseDeMap, { unitId, constructId }, unitId);
    //获取单位下所有人材机数据
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let used_clsb = new Set();
    for (const costDe of zjcsCostDeArr) {
      let zjcs = csxmAllData.find(k => k.sequenceNbr === costDe.sequenceNbr);
      let deMathBase = 0;
      if (!ObjectUtils.isEmpty(zjcs)) {
        //该费用定额在其他总价措施下，正常计算
        //获取计算基数
        deMathBase = costBase[costDe.rateName];
        used_clsb.add(costDe.rateName);
        if (ObjectUtils.isEmpty(deMathBase)) {
          deMathBase = 0;
        }
        // 判断是否计算基数乘以0.5
        //雨季施工增加费   冬季施工增加费
        if (!ObjectUtils.isEmpty(cacheObj)) {
          if (cacheObj.heatingFee) {
            if (costDe.zjcsClassCode === '1') {
              deMathBase = NumberUtil.multiply(deMathBase, 0.5);
            }
          }
          if (cacheObj.rainySeasonConstruction) {
            if (costDe.zjcsClassCode === '2') {
              deMathBase = NumberUtil.multiply(deMathBase, 0.5);
            }
          }
        }
      }
      //获取费用定额的人材机数据
      let costDeRcjs = rcjList.filter(k => k.deId === costDe.sequenceNbr);
      //人材机明细：合计数量=定额工程量*消耗量*计算基数
      costDeRcjs.forEach(k => {
        //合计数量
        k.totalNumber = NumberUtil.divide100(NumberUtil.multiplyParams(NumberUtil.numberScale(k.resQty, precision.DETAIL.RCJ.resQty), NumberUtil.numberScale(costDe.quantity, precision.EDIT.DE.quantity), deMathBase));
        k.total = NumberUtil.multiply(NumberUtil.numberScale(k.totalNumber, precision.EDIT.DE.totalNumber), NumberUtil.numberScale(k.dePrice, precision.EDIT.DE.price));
      });
      //赋值计算基数
      costDe.formula = deMathBase;
      costDe.caculatePrice = 1;
      costDe.baseNum = { def: deMathBase };
      await ProjectDomain.getDomain(constructId).csxmDomain.notify(costDe, false);
      console.log('总价措施自动记取完成');
    }
    let flag = false;
    let baseDeClsb = Object.keys(costBase);
    if (baseDeClsb.length > 0 && !(baseDeClsb.every(element => used_clsb.has(element)))) {
      // 说明有的基数定额没有对应的费用定额
      flag = true;
    }
    let flagObj = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_NO_PARTICIPATION_ZJCS_MATCH);
    if (ObjectUtils.isNotEmpty(flagObj)) {
      flagObj[unitId] = flag;
    } else {
      ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_NO_PARTICIPATION_ZJCS_MATCH, { [unitId]: flag });
    }
  }

}

GljZjcsCostMatchService.toString = () => '[class GljZjcsCostMatchService]';
module.exports = GljZjcsCostMatchService;
