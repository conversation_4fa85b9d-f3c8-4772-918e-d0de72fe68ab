const ResourceKindConstants = require("./ResourceKindConstants"); 

class DeCommonConstants {


    static COMPENSATION_MATERIAL_RG_LABEL = "调差人工费";
    static COMPENSATION_MATERIAL_CL_LABEL = "调差材料费";    
    static COMPENSATION_MATERIAL_JX_LABEL = "调差机械费";

    static ADDTIONAL_MATERIAL_CODE_RG = "BCRGF";
    static ADDTIONAL_MATERIAL_CODE_RG_LABEL = "补充人工费";
    static ADDTIONAL_MATERIAL_CODE_JX = "BCJXF";
    static ADDTIONAL_MATERIAL_CODE_JX_LABEL = "补充机械费";


    static ADDTIONAL_MATERIAL_CODE_CL = "BCCLF";
    static ADDTIONAL_MATERIAL_CODE_CL_LABEL = "补充材料费";

    static ADDTIONAL_MATERIAL_CODE_ZC = "BCZCF";
    static ADDTIONAL_MATERIAL_CODE_ZC_LABEL = "补充主材费";

    static ADDTIONAL_MATERIAL_CODE_SB = "BCSBF";
    static ADDTIONAL_MATERIAL_CODE_SB_LABEL = "补充设备费";

    static initCodeAndName(standardModel,unit){
        let materialCode = "";
        let materialName = "";
        let count = 0;
        if(standardModel.kind == ResourceKindConstants.TYPE_ZC){
            materialCode = this.ADDTIONAL_MATERIAL_CODE_ZC;
            materialName = this.ADDTIONAL_MATERIAL_CODE_ZC_LABEL;
            // unit.BCZCFCount += 1;
            // count = unit.BCZCFCount ;
        }
        if(standardModel.kind == ResourceKindConstants.TYPE_SB){
            materialCode = this.ADDTIONAL_MATERIAL_CODE_SB;
            materialName = this.ADDTIONAL_MATERIAL_CODE_SB_LABEL;
            // unit.BCSBFCount += 1;
            // count = unit.BCSBFCount ;
        }
        if(standardModel.kind == ResourceKindConstants.TYPE_R){
            materialCode = this.ADDTIONAL_MATERIAL_CODE_RG;
            materialName = this.ADDTIONAL_MATERIAL_CODE_RG_LABEL;
            // unit.BCRGFCount += 1;
            // count = unit.BCRGFCount ;
        }
        if(standardModel.kind == ResourceKindConstants.TYPE_C){
            materialCode = this.ADDTIONAL_MATERIAL_CODE_CL;
            materialName = this.ADDTIONAL_MATERIAL_CODE_CL_LABEL;
            // unit.BCCLFCount += 1;
            // count = unit.BCCLFCount ;
        }
        if(standardModel.kind == ResourceKindConstants.TYPE_J){
            materialCode = this.ADDTIONAL_MATERIAL_CODE_JX;
            materialName = this.ADDTIONAL_MATERIAL_CODE_JX_LABEL;
            // unit.BCJXFCount += 1;
            // count = unit.BCJXFCount ;
        }
        standardModel.materialCode =  materialCode;
        standardModel.materialName =  materialName;

        // standardModel.materialCode =  materialCode + count;
        // standardModel.materialName =  materialName + count.toString().padStart(3,'0');//不兼容4位等
    }

    static COMMON_AZ_FEE_UNIT = "元";


    static ADDTIONAL_MATERIAL_CODE_RG_TZ = "RGFTZ";

    static ADDTIONAL_MATERIAL_CODE_RG_TZ_LABEL = "人工费调整";

    static ADDTIONAL_MATERIAL_CODE_CL_TZ = "CLFTZ";

    static ADDTIONAL_MATERIAL_CODE_CL_TZ_LABEL = "材料费调整";

    static ADDTIONAL_MATERIAL_CODE_JX_TZ = "JXFTZ";

    static ADDTIONAL_MATERIAL_CODE_JX_TZ_LABEL = "机械费调整";

    static ADDTIONAL_MATERIAL_CODE_ZC_TZ = "ZCFTZ";

    static ADDTIONAL_MATERIAL_CODE_ZC_TZ_LABEL = "主材费调整";

    static ADDTIONAL_MATERIAL_CODE_SB_TZ = "SBFTZ";

    static ADDTIONAL_MATERIAL_CODE_SB_TZ_LABEL = "设备费调整";

    static COMMON_UNIT = "元";

    static getTZName(type){
        if(type == '1'){ return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ_LABEL;}
        if(type == '2'){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ_LABEL;}
        if(type == '3'){ return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ_LABEL;}
        if(type == '4'){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_TZ_LABEL;}
        if(type == '5'){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_TZ_LABEL;}
        return "";
    }

    static getTZCode(type){
        if(type == '1'){ return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ;}
        if(type == '2'){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ;}
        if(type == '3'){ return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ;}
        if(type == '4'){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_TZ;}
        if(type == '5'){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_TZ;}
        return "";
      }

    static isCodeTZ(code){
        return code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
            ||  code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
            ||  code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ
            ||  code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_TZ
            ||  code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_TZ

    }

    static getTZNameByCode(code){
        if(code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ){ return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ_LABEL;}
        if(code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ_LABEL;}
        if(code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ){ return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ_LABEL;}
        if(code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_TZ){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_TZ_LABEL;}
        if(code === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_TZ){  return DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_TZ_LABEL;}
        return "";
    }
}

DeCommonConstants.toString = () => 'DeCommonConstants';
module.exports = DeCommonConstants;