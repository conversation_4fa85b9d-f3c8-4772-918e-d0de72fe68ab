const {Service} = require("../../../core");
const MenuBarEnum = require("../enums/MenuBarEnum");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require('../domains/ProjectDomain');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const BusinessConstants = require('../constants/BusinessConstants');
const GsProjectSettingEnum = require("../enums/GsProjectSettingEnum");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const YGSOperator = require("../core/tools/fileOperator/YGSOperator");
const {CryptoUtils} = require("../../../electron/utils/CrypUtils");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const GsPrecisionSetting = require("../enums/GsPrecisionSetting");
const AppContext = require("../core/container/APPContext");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const DeTypeConstants = require("../constants/DeTypeConstants");
const {DeFlattener} = require("../domains/calculators/de/DeFlattener")
const DeQualityUtils = require("../domains/utils/DeQualityUtils");
const UnitUtils = require('../core/tools/UnitUtils');
const DeUtils = require("../domains/utils/DeUtils");
const {NumberUtil} = require("../utils/NumberUtil");

class GsCommonService extends Service{

    constructor(ctx) {
        super(ctx);
        let {baseFeeFileService,itemBillProjectService,otherProjectService} = this.service;
        let {gsOtherProjectCostService,gsRcjCollectService,gsOverviewService,gsEquipmentCostsService,gsProjectCommonService,gsUnitCostSummaryService}=this.service.PreliminaryEstimate

        this.map = new Map();
        this.map.set(MenuBarEnum.PROJECT_OVERVIEW,{obj:gsOverviewService,fun:gsOverviewService.getChildrenMenuList});//項目概況-项目
        // this.map.set(MenuBarEnum.OTHER_PROJECT_COST,{obj:gsOtherProjectCostService,fun:gsOtherProjectCostService.getOtherProjectCostList}); // 建设其他费-项目
        this.map.set(MenuBarEnum.PROJECT_SBGZF,{obj:gsEquipmentCostsService,fun:gsEquipmentCostsService.getChildrenMenuList});//項目设备购置费
        // this.map.set(MenuBarEnum.ESTIMATES_SUMMARY,{obj:null,fun:null}); // 概算汇总-项目
        this.map.set(MenuBarEnum.UNIT_PROJECT_OVERVIEW,{obj:gsOverviewService,fun:gsOverviewService.getChildrenMenuList});//項目概況-单位
        // this.map.set(MenuBarEnum.FEE_COLLECTION_FORM,{obj:baseFeeFileService,fun:baseFeeFileService.getFeeCollectionTreeList});//取费文件列表-工程项目
        // this.map.set(MenuBarEnum.UNIT_FEE_COLLECTION_FORM,{obj:baseFeeFileService,fun:baseFeeFileService.getFeeCollectionTreeList});//取费文件列表-单位
        // this.map.set(MenuBarEnum.UNIT_ITEM_BILL,{obj:itemBillProjectService,fun:itemBillProjectService.queryUnitBranchTree});//分部分項-左側目錄書
        this.map.set(MenuBarEnum.UNIT_ITEM_BILL,{obj:gsProjectCommonService,fun:gsProjectCommonService.getFBTree});//分部分項-左側目錄書
        // this.map.set(MenuBarEnum.UNIT_OTHER_PROJECT,{obj:otherProjectService,fun:otherProjectService.menuData});//分部分項-左側目錄書
        this.map.set(MenuBarEnum.UNIT_COST_AGGREGATION,{obj:gsUnitCostSummaryService,fun:gsUnitCostSummaryService.getCostSummaryMajorMenuList});//费用汇总-单位
        /**
         * 人材机汇总左侧树
         */
        this.map.set(MenuBarEnum.RCJ_SUMMARY,{obj:gsRcjCollectService,fun:gsRcjCollectService.getRcjCellectMenuData});
        this.map.set(MenuBarEnum.SINGLE_RCJ_SUMMARY, { obj: gsRcjCollectService, fun: gsRcjCollectService.getRcjCellectMenuData });
        this.map.set(MenuBarEnum.UNIT_RCJ_SUMMARY,{obj:gsRcjCollectService,fun:gsRcjCollectService.getRcjCellectMenuData});
    }


    /**
     * 获取菜单栏数据
     * @param args
     * @return {Promise<*|ResponseData>}
     */
    async getMenuData(args){
        let type = args.type;
        let levelType = args.levelType;
        let code = args.code;
        let menuBarEnum = this.getMenuBarEnum(type,levelType,code);
        if (ObjectUtils.is_Undefined(menuBarEnum) || ObjectUtils.isNull(menuBarEnum)){
            return ResponseData.fail("参数错误");
        }
        let objDefinition = this.map.get(menuBarEnum);
        if (ObjectUtils.isEmpty(objDefinition)){
            return ;
        }
        let result =await objDefinition.fun.call(objDefinition.obj,args);
        // if (args.code === "3" &&
        //     result.itemList && result.itemList.length>0 &&
        //     args.constructId && args.constructId !== "" &&
        //     args.singleId && args.singleId !== "" &&
        //     args.unitId && args.unitId !== "" ) {
        //
        //     let baseFileId = PricingFileFindUtils.getMainFeeFile(args.constructId, args.singleId, args.unitId).feeFileId;
        //     for (let i = 0 ; i<result.itemList.length;++i) {
        //         if(result.itemList[i][baseFileId]) {
        //             result.itemList[i].defaultFeeFlag = 1;
        //         }
        //     }
        // }
        //
        // if (args.code === "3" &&
        //     (!args.singleId || args.singleId === "") &&
        //     result.itemList && result.itemList.length>0) {
        //     let unitFeeFiles;
        //     if (!args.unitId || args.unitId === "") {
        //         // 找全部的
        //         unitFeeFiles = [];
        //         let units = PricingFileFindUtils.getUnitList(args.constructId);
        //         for (let i = 0 ; i < units.length ; ++ i) {
        //             unitFeeFiles = unitFeeFiles.concat(units[i].feeFiles);
        //         }
        //     } else {
        //          // 找特定的 unit
        //         let unitp = PricingFileFindUtils.getUnitList(args.constructId).filter(f=>f.sequenceNbr === args.unitId);
        //         if (unitp.length > 0) {
        //             unitFeeFiles =  unitp[0].feeFiles;
        //         } else {
        //             unitFeeFiles =  PricingFileFindUtils.getUnitList(args.constructId)[0].feeFiles;
        //         }
        //     }
        //
        //     let defFeeFile = unitFeeFiles.filter(f=>f.defaultFeeFlag && f.defaultFeeFlag === 1)[0];
        //     let baseFileId = defFeeFile.feeFileId;
        //     for (let i = 0 ; i<result.itemList.length;++i) {
        //         if(result.itemList[i][baseFileId]) {
        //             result.itemList[i].defaultFeeFlag = 1;
        //         }
        //     }
        // }

        //console.log(result);
        return result;
    }


    /**
     * 获取枚举值
     * @param type
     * @param levelType
     * @param code
     * @return {object | string | bigint | number | boolean | symbol}
     */
    getMenuBarEnum(type,levelType,code){
        for (let menuBarEnumKey in MenuBarEnum) {
            let Bartype = MenuBarEnum[menuBarEnumKey].type;
            let BarlevelType = MenuBarEnum[menuBarEnumKey].levelType;
            let Barcode = MenuBarEnum[menuBarEnumKey].code;
            if (Bartype == type && BarlevelType ==levelType && Barcode== code){
                return MenuBarEnum[menuBarEnumKey];
            }
        }
    }


    /**
     * 获取外层菜单栏数据
     * @param args
     * @return {ResponseData}
     */
    getMenuList(args){
        let type = args.type;
        let levelType = args.levelType;
        let values = Object.values(MenuBarEnum);
        let result = values.filter(item =>item.type==type && item.levelType==levelType);
        return  ResponseData.success(result);
    }


    /**
     * 表格列设置查询
     * @param args
     * @returns {Promise<void>}
     */
    async getTableList(args) {
        let {constructId, singleId, unitId, businessId} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;

        //工程项目rcj表头
        if (businessId === BusinessConstants.PROJECT_RCJ_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.RCJ_TABLELIST + constructId);
        }
        //单位项目rcj表头
        if (businessId === BusinessConstants.UNIT_RCJ_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        //单位项目rcj表头
        if (businessId === BusinessConstants.SINGLE_RCJ_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + singleId);
        }
        //设置预算书表头
        if (businessId === BusinessConstants.UNIT_YSH_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        // 概算汇总
        if (businessId === BusinessConstants.PROJECT_GS_SUMMARY_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_GS_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.PROJECT_GS_TABLELIST + constructId);
        }
        //单位工程-造价分析 表头
        if (businessId === BusinessConstants.UNIT_ZJFX_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.UNIT_BGLSZ);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(unitId);
        }
        // 单位工程-独立费
        if (businessId === BusinessConstants.UNIT_DLF_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(unitId);
        }

        //单位工程-三材 表头
        if (businessId === BusinessConstants.UNIT_SC_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.SC_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.SC_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        return null
    }

    /**
     * 获取精度设置
     * @returns {Promise<void>}
     */
    getDefaultPrecisionSetting() {
        return ConvertUtil.deepCopy(GsPrecisionSetting);
    }

    /**
     * 获取精度设置
     * @param constructId
     * @returns {Promise<void>}
     */
    getPrecisionSetting(constructId) {
        if (ObjectUtils.isEmpty(constructId)) {
            return ConvertUtil.deepCopy(GsPrecisionSetting);
        }
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let precisionSettingObj = businessMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
        if (ObjectUtils.isEmpty(precisionSettingObj)) {
            precisionSettingObj = ConvertUtil.deepCopy(GsPrecisionSetting)
            businessMap.set(FunctionTypeConstants.PROJECT_PRECISION_SETTING, precisionSettingObj);
        }
        return ConvertUtil.deepCopy(precisionSettingObj);
    }

    /**
     * 精度设置
     * @param constructId
     * @param precisionSetting
     * @returns {Promise<void>}
     */
    async setPrecisionSetting(constructId, precisionSetting) {
        if (ObjectUtils.isEmpty(constructId)) {
            return;
        }
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let precisionSettingObj = businessMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
        if (ObjectUtils.isNotEmpty(precisionSettingObj)) {
            let new_totalNumber = precisionSetting.RCJ.totalNumber;
            let new_quantitySelect = precisionSetting.DE.quantitySelect;
            let new_quantity = precisionSetting.DE.quantity;
            let new_unitQuantity = precisionSetting.DE.unitQuantity;
            let old_totalNumber = precisionSettingObj.PRECISION_SETTING_UI.RCJ.totalNumber;
            let old_quantitySelect = precisionSettingObj.PRECISION_SETTING_UI.DE.quantitySelect;
            let old_quantity = precisionSettingObj.PRECISION_SETTING_UI.DE.quantity;
            let old_unitQuantity = precisionSettingObj.PRECISION_SETTING_UI.DE.unitQuantity;
            precisionSettingObj.PRECISION_SETTING_UI = precisionSetting;

            // 更新精度
            // 人材机 - 数量精度统一设置
            if (ObjectUtils.isNotEmpty(new_totalNumber)) {
                precisionSettingObj.DETAIL.RCJ.totalNumber = new_totalNumber;
                precisionSettingObj.RCJ_COLLECT.totalNumber = new_totalNumber;
            }

            // 定额 - 工程量精度统一设置
            if (ObjectUtils.isNotEmpty(new_quantity)) {
                precisionSettingObj.EDIT.DE.quantity = new_quantity; // 单价
            }
            if (new_quantitySelect === 'unitQuantity') {
                precisionSettingObj.EDIT.DE.isUnitQuantity = true;
                precisionSettingObj.EDIT.DE.unitQuantity = ConvertUtil.deepCopy(new_unitQuantity);
            } else {
                precisionSettingObj.EDIT.DE.isUnitQuantity = false;
            }


            if (new_totalNumber !== old_totalNumber
                || new_quantitySelect !== old_quantitySelect
                || new_quantity !== old_quantity
                || new_unitQuantity !== old_unitQuantity
            ) {
                // 所有单位工程
                let unitList = await this.service.PreliminaryEstimate.gsProjectCommonService.getProjectUnitAll(constructId);
                // 精度重新计算
                await this.recalculatePrecision(constructId,unitList);

                //--------- 重新计算工程量------start------
                
                //重新计算工程量及数据
                let projectDomain = ProjectDomain.getDomain(constructId);
                let ctx = projectDomain.ctx;
                let functionDataMap = projectDomain.functionDataMap;
                let allNodes = ctx.deMap.getAllNodes();
                let changeQuantityNodes = [];
                //数量变化 不处理  工程量 部署
                if (new_quantitySelect !== old_quantitySelect
                || new_quantity !== old_quantity
                || new_unitQuantity !== old_unitQuantity) {
                
                    let parentTypes = [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB];
                    changeQuantityNodes = allNodes.filter(item=>{
                        if(!parentTypes.includes(item.type)){
                            if(item.parent && parentTypes.includes(item.parent.type)){
                                return true;
                            }
                        }
                        return false;
                    });
                }

                for (let unit of unitList) {
                    //单位下有需要变更的数据  
                    let unitChangeNodes = changeQuantityNodes.filter(item=>item.unitId === unit.sequenceNbr);
                    if(ObjectUtils.isNotEmpty(unitChangeNodes)){
                        //查询功能变量值
                        let codeArgs =  {
                            constructId:constructId,
                            type:"变量表",
                            unitId:unit.sequenceNbr,
                            constructMajorType:unit.constructMajorType
                        }
                        let priceCodes = await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.costCodePrice(codeArgs);

                        for(let changeNode of unitChangeNodes){
                            let unitPriceCodes = priceCodes?[...priceCodes]:[];
                            //增加定额费用代码
                            let  unitNbr = UnitUtils.removeCharter(changeNode.unit);
                            await this.service.PreliminaryEstimate.gsDeService.addPriceCodes(constructId, unit.sequenceNbr, changeNode.sequenceNbr, unitPriceCodes);
                            let newQuantity = DeQualityUtils.evalQualityWithCodes(changeNode.quantityExpression, unitPriceCodes);
                            let digital = DeUtils.getQuantiyPrecision(precisionSettingObj,changeNode);
                            if(ObjectUtils.isNotEmpty(unitNbr))
                            {
                                changeNode.quantity = NumberUtil.numberFormat(NumberUtil.divide(newQuantity,unitNbr),digital);
                            }
                            else {
                                changeNode.quantity =  NumberUtil.numberFormat(newQuantity,digital);
                            }
                            let df = DeFlattener.getInstance(changeNode, ctx,true,unitPriceCodes,functionDataMap);
                            await df.analyze();
                        }               
                    }
                    let unitAllNodes = allNodes.filter(item=>item.unitId === unit.sequenceNbr)
                    await projectDomain.getDeDomain().notifyAll(constructId, unit.sequenceNbr,unitAllNodes);
                }
                 //--------- 重新计算工程量------end------
            }
        }
    }

    /**
     * 精度重新计算
     * @param constructId
     * @returns {Promise<void>}
     */
    async recalculatePrecision(constructId,unitList) {
        for (let unit of unitList) {
            // TODO 各业务精度重新计算
            //重新计算独立费
            await this.service.PreliminaryEstimate.gsIndependentCostsService.recalculatePrecisionDlf(constructId,unit.sequenceNbr);
            //todo 重新计算设备购置费
            await this.service.PreliminaryEstimate.gsEquipmentCostsService.recalculatePrecisionSbgzf(constructId,unit.sequenceNbr);

            // 重新计算费用汇总
            try {
                if (unit.qfMajorTypeMoneyMap) {
                    let qfMajorTypeMoneyMap = new Map(Object.entries(unit.qfMajorTypeMoneyMap));
                    for (let qfMajorType of Array.from(qfMajorTypeMoneyMap.keys())) {
                        await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                            constructId: constructId,
                            unitId: unit.sequenceNbr,
                            constructMajorType: qfMajorType
                        });
                    }
                }

                // 获取建设其他费费用
                let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
                // 获取建设其他费费用代码
                let otherProjectCostCodes = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_CODE);
                // 调用计算建设其他费
                await this.service.PreliminaryEstimate.gsOtherProjectCostService.countOtherProjectCost(constructId, otherProjectCostCodes, otherProjectCosts);

                // 更新计算概算费用代码
                let estimateCodeArray = await this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
                    constructId: constructId
                });
                // 获取概算汇总
                let estimateSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_SUMMARY);
                // 调用计算概算汇总
                await this.service.PreliminaryEstimate.gsEstimateSummaryService.countEstimateSummary(constructId, estimateSummaryArray, estimateCodeArray);

            } catch (error) {
                console.error("捕获到异常:", error);
            }

        }
    }

    /**
     * 表格列设置保存
     * @param args
     * @returns {Promise<void>}
     */
    async saveTableList(args) {
        let {constructId, singleId, unitId, businessId, header, isDefault} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;

        // // 获取该工程项目
        // let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // // 保存是否是全局变量
        // constructProject.isDefault = isDefault;
        // let businessIdArray = new Array();
        // businessIdArray.push(businessId);
        // constructProject.businessIdArray = businessIdArray;
        // ProjectDomain.getDomain(constructId).updateProject(constructProject);

        if (isDefault) {  // 当前单位
            //工程项目rcj表头
            if (businessId === BusinessConstants.PROJECT_RCJ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
                }

                let map ={};
                map.isDefault = isDefault
                map.header = header

                // map.set('isDefault', isDefault);
                // map.set('header', header);
                // objMap.set('isDefault', isDefault);
                objMap.set(FunctionTypeConstants.RCJ_TABLELIST + constructId, map);
            }
            //单位项目rcj表头
            if (businessId === BusinessConstants.UNIT_RCJ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
                }

                let map ={};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }
            //单位项目rcj表头
            if (businessId === BusinessConstants.SINGLE_RCJ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + singleId, map);
            }
            //设置预算书表头
            if (businessId === BusinessConstants.UNIT_YSH_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.YSH_TABLELIST, objMap);
                }

                 let map ={};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }
            // 概算汇总
            if (businessId === BusinessConstants.PROJECT_GS_SUMMARY_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.PROJECT_GS_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.PROJECT_GS_TABLELIST, objMap);
                }

                let map ={};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.PROJECT_GS_TABLELIST + constructId, map);
            }
            //单位工程-造价分析 表头
            if (businessId === BusinessConstants.UNIT_ZJFX_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.UNIT_BGLSZ);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.UNIT_BGLSZ, objMap);
                }

                let map ={};
                map.isDefault = isDefault
                map.header = header
                objMap.set(unitId, map);
            }
            // 单位工程-独立费
            if (businessId === BusinessConstants.UNIT_DLF_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.DLF_TABLELIST, objMap);
                }

                 let map ={};
                map.isDefault = isDefault
                map.header = header
                objMap.set(unitId, map);
            }
            //单位工程-三材 表头
            if (businessId === BusinessConstants.UNIT_SC_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.SC_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.SC_TABLELIST, objMap);
                }

                let map ={};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.SC_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }
            return null;
        } else {  // 全局

            // 获取该工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);

            // 获取该单项下的所有单位
            let unitProjectsByConstruct = new Array();
            if (ObjectUtils.isNotEmpty(constructProject.children)) {
                PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
            }

            //设置预算书表头
            if (businessId === BusinessConstants.UNIT_YSH_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.YSH_TABLELIST, objMap);
                }
                // 遍历设置所有单位
                for (let i = 0; i < unitProjectsByConstruct.length; i++) {
                    let unitProject = unitProjectsByConstruct[i];
                    let unitId = unitProject.sequenceNbr;

                     let map ={};
                    map.isDefault = isDefault
                    map.header = header
                    objMap.set(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
                }
                return objMap;
            }
            if (businessId === BusinessConstants.UNIT_DLF_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.DLF_TABLELIST, objMap);
                }
                // 遍历设置所有单位
                for (let i = 0; i < unitProjectsByConstruct.length; i++) {
                    let unitProject = unitProjectsByConstruct[i];
                    let unitId = unitProject.sequenceNbr;

                    let map ={};
                    map.isDefault = isDefault
                    map.header = header
                    objMap.set(unitId, map);
                }
                return objMap;
            }
        }
    }

    /**
     * 工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async getProjectSetting(arg){
        let {constructId} = arg;
        let projectDomain = ProjectDomain.getDomain(constructId);
        let businessMap = projectDomain.functionDataMap;

        let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
        if (ObjectUtils.isEmpty(objMap)) {
            objMap = new Map();
            businessMap.set(FunctionTypeConstants.PROJECT_SETTING, objMap);

            // 标准换算设置
            objMap.set(GsProjectSettingEnum.STANDARD_CONVERSION, GsProjectSettingEnum.STANDARD_CONVERSION_SETTING);
            // 关联子目
            objMap.set(GsProjectSettingEnum.RELATION_DE, GsProjectSettingEnum.RELATION_DE_SETTING);
            // 未计价材料
            objMap.set(GsProjectSettingEnum.UNPRICED, GsProjectSettingEnum.UNPRICED_SETTING);
        }
        //按市价设置
        let pricingFlag = projectDomain.getRoot().pricingMethod == 1;
        objMap.set(GsProjectSettingEnum.PRICING_METHOD, pricingFlag);
        //定额差额设置
        let dePriceSpread =businessMap.get(FunctionTypeConstants.DE_PRICE_SPREAD);
        objMap.set(FunctionTypeConstants.DE_PRICE_SPREAD, ObjectUtils.isEmpty(dePriceSpread)? false: dePriceSpread  );
        return objMap;
    }

    /**
     * 保存工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async saveProjectSetting(arg){
        let {constructId, gsProjectSettingCode, setting} = arg;
        let projectDomain = ProjectDomain.getDomain(constructId);
        let businessMap = projectDomain.functionDataMap;

        // 标准换算设置
        if(gsProjectSettingCode === GsProjectSettingEnum.STANDARD_CONVERSION){
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }

        // 按市价设置
        if(gsProjectSettingCode === GsProjectSettingEnum.PRICING_METHOD){
            projectDomain.getRoot().pricingMethod = setting? 1 : 0;

            let projects = projectDomain.getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT)
            for (let project of projects) {
                //修改组价方式后，联动修改预算书、措施项目表格列设置取值
                try {
                    await this.changeTableList(constructId, project.sequenceNbr);
                } catch (error) {
                    console.error("联动修改预算书、措施项目表格列设置取值；捕获到异常:", error);
                }
            }
        }

        // 关联子目弹窗设置
        if (gsProjectSettingCode === GsProjectSettingEnum.RELATION_DE) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }

        // 未计价材料设置
        if (gsProjectSettingCode === GsProjectSettingEnum.UNPRICED) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }
    }

    async changeTableList(constructId, unitId) {
        //不同组价方式显示取值不同 0 不按市场价， 1 按市场价
        let pricingMethod = ProjectDomain.getDomain(constructId).getRoot().pricingMethod;

        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
        if (ObjectUtils.isNotEmpty(objMap)) {
            let yssTableList = objMap.get(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
            if (ObjectUtils.isNotEmpty(yssTableList) && ObjectUtils.isNotEmpty(yssTableList.header)) {
                for (let item of yssTableList.header) {
                    await this.changeTableListValue(item, pricingMethod);
                }
            }
        }
    }

    async changeTableListValue(item, pricingMethod) {
        if (item.field === "baseJournalPrice" || item.field === "price") {
            item.field = pricingMethod === 0 ? "baseJournalPrice" : "price";
            item.dataIndex = pricingMethod === 0 ? "baseJournalPrice" : "price";
        } else if (item.field === "RDSum" || item.field === "RSum") {
            item.field = pricingMethod === 0 ? "RDSum" : "RSum";
            item.dataIndex = pricingMethod === 0 ? "RDSum" : "RSum";
        } else if (item.field === "rdTotalSum" || item.field === "rTotalSum") {
            item.field = pricingMethod === 0 ? "rdTotalSum" : "rTotalSum";
            item.dataIndex = pricingMethod === 0 ? "rdTotalSum" : "rTotalSum";
        } else if (item.field === "CDSum" || item.field === "CSum") {
            item.field = pricingMethod === 0 ? "CDSum" : "CSum";
            item.dataIndex = pricingMethod === 0 ? "CDSum" : "CSum";
        } else if (item.field === "cdTotalSum" || item.field === "cTotalSum") {
            item.field = pricingMethod === 0 ? "cdTotalSum" : "cTotalSum";
            item.dataIndex = pricingMethod === 0 ? "cdTotalSum" : "cTotalSum";
        } else if (item.field === "JDSum" || item.field === "JSum") {
            item.field = pricingMethod === 0 ? "JDSum" : "JSum";
            item.dataIndex = pricingMethod === 0 ? "JDSum" : "JSum";
        } else if (item.field === "jdTotalSum" || item.field === "jTotalSum") {
            item.field = pricingMethod === 0 ? "jdTotalSum" : "jTotalSum";
            item.dataIndex = pricingMethod === 0 ? "jdTotalSum" : "jTotalSum";
        } else if (item.field === "ZDSum" || item.field === "ZSum") {
            item.field = pricingMethod === 0 ? "ZDSum" : "ZSum";
            item.dataIndex = pricingMethod === 0 ? "ZDSum" : "ZSum";
        } else if (item.field === "zdTotalSum" || item.field === "zTotalSum") {
            item.field = pricingMethod === 0 ? "zdTotalSum" : "zTotalSum";
            item.dataIndex = pricingMethod === 0 ? "zdTotalSum" : "zTotalSum";
        } else if (item.field === "SDSum" || item.field === "SSum") {
            item.field = pricingMethod === 0 ? "SDSum" : "SSum";
            item.dataIndex = pricingMethod === 0 ? "SDSum" : "SSum";
        } else if (item.field === "sdTotalSum" || item.field === "sTotalSum") {
            item.field = pricingMethod === 0 ? "sdTotalSum" : "sTotalSum";
            item.dataIndex = pricingMethod === 0 ? "sdTotalSum" : "sTotalSum";
        } else if (item.field === "baseJournalTotalNumber" || item.field === "totalNumber") {
            item.field = pricingMethod === 0 ? "baseJournalTotalNumber" : "totalNumber";
            item.dataIndex = pricingMethod === 0 ? "baseJournalTotalNumber" : "totalNumber";
        }
    }


    /**
     * 定额操作缓存
     * @param args
     * @returns {Promise<void>}
     */
    async getDeSettingCache(args) {
        let {constructId, sequenceNbr} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let deSettingObj = businessMap.get(FunctionTypeConstants.DE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(deSettingObj)) {
            return null;
        }
        if (ObjectUtils.isEmpty(sequenceNbr)) {
            sequenceNbr = constructId;
        }
        return deSettingObj[sequenceNbr];
    }


    /**
     * 设置定额操作缓存
     * @param args
     * @returns {Promise<void>}
     */
    async setDeSettingCache(args) {
        let {constructId, operateCache, sequenceNbr} = args;
        if (ObjectUtils.isEmpty(sequenceNbr)) {
            sequenceNbr = constructId
        }
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let deSettingObj = businessMap.get(FunctionTypeConstants.DE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(deSettingObj)) {
            deSettingObj = {}
        }
        if (ObjectUtils.isNotEmpty(operateCache)) {
            deSettingObj[sequenceNbr] = operateCache;
        }
        businessMap.set(FunctionTypeConstants.DE_SETTING_CACHE, deSettingObj);
    }

    /**
     * 所有页签缓存
     * @param args
     * @returns {Promise<void>}
     */
    async getTableSettingCache(args) {
        let {constructId, currentId} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let tableSettingObj = businessMap.get(FunctionTypeConstants.TABLE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(tableSettingObj)) {
            return null;
        }
        if (ObjectUtils.isEmpty(currentId)) {
            currentId = tableSettingObj.currentId;
        }
        return tableSettingObj[currentId];
    }

    /**
     * 设置所有页签缓存
     * @param args
     * @returns {Promise<void>}
     */
    async setTableSettingCache(args) {
        let {constructId, tableSetting, currentId} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let tableSettingObj = businessMap.get(FunctionTypeConstants.TABLE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(tableSettingObj)) {
            tableSettingObj = {}
        }
        if (ObjectUtils.isNotEmpty(currentId)) {
            tableSettingObj.currentId = currentId
        }
        if (ObjectUtils.isNotEmpty(tableSetting)) {
            tableSettingObj[tableSetting.selLeftTreeId] = tableSetting;
        }
        businessMap.set(FunctionTypeConstants.TABLE_SETTING_CACHE, tableSettingObj);
    }

    async diffProject(arg) {
        let constructId = arg.constructId;
        let result = false;
        let context = AppContext.getContext(constructId);
        if (ObjectUtils.isNotEmpty(context)) {
            let projectDomain = ProjectDomain.getDomain(constructId);
            let root = ProjectDomain.getDomain(constructId).getRoot();
            let jsonObj = ObjectUtils.stringifyComplexObject(YGSOperator.prepareContent(projectDomain));
            let cacheProStr = await this.toJsonYsfString(jsonObj);

            let cacheDara = CryptoUtils.objectHash(cacheProStr, root.sequenceNbr, false);
            let fileData = global.editProMap.get(root.sequenceNbr);

            if (cacheDara == fileData) {
                result = true;
            }
        }
        return ResponseData.success(!result);
    }

    async toJsonYsfString(obj) {
        return JSON.stringify(obj, (key, value) => {
            return typeof value === 'undefined' ? null : value;
        });
    }

    async setDePriceSpread(constructId, display) {
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        businessMap.set(FunctionTypeConstants.DE_PRICE_SPREAD,display);
    }

}
GsCommonService.toString = () => '[class GsCommonService]';
module.exports = GsCommonService;
