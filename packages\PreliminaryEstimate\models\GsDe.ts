
import {GsModel} from "./GsModel";
import {GsRcj} from "./GsRcj";
import {GsDeRelation} from "./GsDeRelation";
import {GsDeRcjRelation} from "./GsDeRcjRelation";

/**
 * 定额表
 */
export class GsDe extends GsModel{
    public libraryCode: string; // 定额册编码

    public libraryName: string; // 定额库名称

    public classlevel01: string; // 一级分类

    public classlevel02: string; // 二级分类

    public classlevel03: string; // 三级分类

    public classlevel04: string; // 四级分类

    public classlevel05: string; // 五级分类
    public classlevelSplitConcat: string;//分类编码
    public deCode: string; // 定额编码

    public deName: string; // 定额名称

    public unit: string; // 单位

    public resQty: number; // 含量

    public quantity: string; // 工程量

    public changeQuantity: string; // 更改后前一个的d工程量

    public price: number; // 单价

    public total: number; // 合价

    public projectType: string; // 取费专业

    public sortNo: number; // 排序

    public agencyCode: string; // 机构代码

    public productCode: string; // 产品代码

    public isSubDe: number; // 是否是下级定额(0 不是 1 是)

    public isExistDe: number; // 是否存在下级定额(0 不存在 1 存在)

    public value: number; // 是否存在下级定额(0 不存在 1 存在)

    public rcjList: Array<GsRcj>; // 人材机

    public deRcjRelationList: Array<GsDeRcjRelation>; // 定额人材机关系

    public subDeList: Array<GsDeRelation>; //二级定额

    public annotations: string;  //批注
    /**
     * 清单批注是否显示 true显示  false 隐藏
     */
    public  isShowAnnotations: boolean;
    public calculateMethod:number;

    public displayStatu: number; // 显隐状态 当 displayStatu = BranchProjectDisplayConstant.max时展示数据
	public displaySign: number; // 控制显隐的箭头  BranchProjectDisplayConstant 里记录

    public importYgsDe:boolean; //是否是手建的导入工程部分

    public initDeRcjNameList:[]; //新建定额时初始化人材机id、名称对象集合

    constructor(sequenceNbr: string, recUserCode: string, recStatus: string, recDate: string, extend1: string, extend2: string, extend3: string, description: string, libraryCode: string, libraryName: string, classlevel01: string, classlevel02: string, classlevel03: string, classlevel04: string, classlevel05: string, deCode: string, deName: string, unit: string, resQty: number, quantity: string, changeQuantity: string, price: number, total: number, projectType: string, sortNo: number, agencyCode: string, productCode: string, isSubDe: number, isExistDe: number, value: number, rcjList: Array<GsRcj>, deRcjRelationList: Array<GsDeRcjRelation>, subDeList: Array<GsDeRelation>, annotations: string, isShowAnnotations: boolean, calculateMethod: number, displayStatu: number, displaySign: number, importYgsDe: boolean, initDeRcjNameList: [],classlevelSplitConcat:string) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.libraryCode = libraryCode;
        this.libraryName = libraryName;
        this.classlevel01 = classlevel01;
        this.classlevel02 = classlevel02;
        this.classlevel03 = classlevel03;
        this.classlevel04 = classlevel04;
        this.classlevel05 = classlevel05;
        this.classlevelSplitConcat = classlevelSplitConcat;
        this.deCode = deCode;
        this.deName = deName;
        this.unit = unit;
        this.resQty = resQty;
        this.quantity = quantity;
        this.changeQuantity = changeQuantity;
        this.price = price;
        this.total = total;
        this.projectType = projectType;
        this.sortNo = sortNo;
        this.agencyCode = agencyCode;
        this.productCode = productCode;
        this.isSubDe = isSubDe;
        this.isExistDe = isExistDe;
        this.value = value;
        this.rcjList = rcjList;
        this.deRcjRelationList = deRcjRelationList;
        this.subDeList = subDeList;
        this.annotations = annotations;
        this.isShowAnnotations = isShowAnnotations;
        this.calculateMethod = calculateMethod;
        this.displayStatu = displayStatu;
        this.displaySign = displaySign;
        this.importYgsDe = importYgsDe;
        this.initDeRcjNameList = initDeRcjNameList;
    }
}

/**
 * 二级定额是否存在
 */
export enum IS_EXIST_DE {
    EXIST_DE=1, //存在
    NO_EXIST_DE=0, //不存在
}