class RcjCommonConstants {

  static DEFAULT_IFDONORMATERIAL = 0;

  static LEVELMARK_ZERO = '0';


  static MARKSUM_JX = 1;

  static SCOPE_ALL = '0';

  static MARKSUM_BJX = 0;

  static TOTALNUMBER_DEFAULT = 0 ;

  static IFDONORMATERIAL = 1 ;

  static IFDONORMATERIAL_PARTYB = 2 ;

  static IFLOCKPRICE = 1 ;

  static IFLOCKPRICE_DEFAULT = 0 ;

  static MARKETPRICEDIFF = '1';

  static LEVELMARK = '0';

  static LEVELMARK_OTHER = '2';


  static SBFTAXREMOVAL = 11.36;

  static ERROR_MESSAGE = "unqualified";

  static ORIGINALQTY = 0;

  static SOURCEPRICE = "";

  static ISNUMLOCK = false;

  static ISNUMLOCK_TRUE = true;

  static RCJDETAILEDIT = false;

  static RCJDETAILEDIT_TRUE = true;

  static DEFAULT_RESQTY = 1;

  static MEUN_TYPE_INIT = 0;

  static MEUN_TYPE_UPDATE = 1;

  static ORDER_ASC ="asc";

  static ORDER_DESC ="desc";

  static SUPPLEMENT_RCJ_FLAG =1;

  static SUPPLEMENT_DE_RCJ_FLAG =1;

  static MATERIALS = "补充材料";
  static MECHANICAL = "补充机械";

  static RCJ_MEMORY_REPLACE = "2";

  static RCJ_MERGE_REPLACE = "0";


  static RCJ_SOURCE = {
    add: "add",
    default: "default"
  }



}
RcjCommonConstants.toString = () => 'RcjCommonConstants';
module.exports = RcjCommonConstants;