const StandardDeModel = require('./deProcessor/models/StandardDeModel');
const DeTypeConstants = require('../constants/DeTypeConstants');
const LabelConstants = require('../constants/LabelConstants');
const BaseDomain = require('./core/BaseDomain');
const DomainConstants = require('../constants/DomainConstants');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const PropertyUtil = require('./utils/PropertyUtil');
const EE = require('../../../core/ee');
const ResourceModel = require('./deProcessor/models/ResourceModel');
const { Snowflake } = require('../utils/Snowflake');
const ResourceConstants = require('../constants/ResourceConstants');
const ResourceDomain = require('./ResourceDomain');
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const UnitUtils = require('../core/tools/UnitUtils');
const {DeFlattener} = require("./calculators/de/DeFlattener");
const {DeCalculator} = require("./calculators/de/DeCalculator");
const {QDCalculator} = require("./calculators/de/QDCalculator");
const ResourceKindConstants = require("../constants/ResourceKindConstants");
const CommonConstants = require("../constants/CommonConstants");
const DeCommonConstants = require("../constants/DeCommonConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const _ = require("lodash");
const RcjTypeEnum = require('../../../electron/enum/RcjTypeEnum');
const {FBCalculator} = require("./calculators/de/FBCalculator");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResourceCalculator} = require("./calculators/resource/ResourceCalculator");
const {DePriceCalculator} = require("./calculators/de/DePriceCalculator");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const BranchProjectDisplayConstant = require("../constants/BranchProjectDisplayConstant");
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const DeUtils = require('./utils/DeUtils');
const DeQualityUtils = require('./utils/DeQualityUtils');
const WildcardMap = require('../core/container/WildcardMap');

class DeDomain extends BaseDomain {

  static FIELD_NAME_ROW_ID = "deRowId";
  static avoidProperty = ['children', 'parent', 'recUserCode', 'recStatus', 'recDate', 'extend1', 'extend2', 'extend3', 'description', 'agencyCode', 'productCode'];
  static baseDeToDeAvoidProperty = ['sequenceNbr', 'type', 'rcjList', 'subDeList', 'deRcjRelationList'];
  resourceDomain;
  functionDataMap;
  constructor(ctx) {
    super();
    this.ctx = ctx;
    this.resourceDomain = new ResourceDomain(ctx);
    this.initData();
  }
  initData() {
    //this.initDefaultDE();
  }

  /**
   * 给定额编辑器初始化默认显示为 "单位工程" 的特殊行.
   *
   */
  initDefaultDE(constructId, unitId) {
    if (ObjectUtil.isEmpty(this.getRoot(unitId))) {
      let defaultDe = new StandardDeModel(constructId, unitId, LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId()), 0, DeTypeConstants.DE_TYPE_DEFAULT);
      defaultDe.name = DeTypeConstants.DE_TYPE_DEFAULT_LABEL;
      this.ctx.deMap.addNode(defaultDe);
      return defaultDe;
    }
  }

  /**
   * 获取当前单位工程下的默认定额行."单位工程"这一行
   * @returns {T}
   */
  getRoot(unitId) {
    return this.ctx.deMap.getAllNodes().find(item => item.type === DeTypeConstants.DE_TYPE_DEFAULT && item.unitId === unitId);
  }
  /**
   *
   * @param deRowId
   * @returns {null}
   */
  findBrothersDes(deRowId) {
    let brothers = null;
    let currentDeRow = this.ctx.deMap.getNodeById(deRowId);
    if (ObjectUtil.isNotEmpty(currentDeRow) && ObjectUtil.isNotEmpty(currentDeRow.parentId)) {
      let parentNode = this.ctx.deMap.getNodeById(currentDeRow.parentId);
      brothers = parentNode.children;
    }
    return brothers;
  }
  /**
   * 创建定额
   * @param deModel
   * @returns {*}
   */
  async createDeRow(deModel,index, checkType = false) {

    if (ObjectUtil.isEmpty(deModel.parentId)) {
      throw Error('The parent id of De can not be null.');
    }
    switch (deModel.type) {
      case DeTypeConstants.DE_TYPE_EMPTY:
        //空定额行
        deModel.displayType = DeTypeConstants.DE_TYPE_EMPTY_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_DEFAULT:
        //整个工程
        deModel.displayType = DeTypeConstants.DE_TYPE_DEFAULT_LABEL;
        deModel.totalNumber = "0";
        break;
      case DeTypeConstants.DE_TYPE_FB:
        // 分部
        deModel.totalNumber = "0";
        deModel.displayType = DeTypeConstants.DE_TYPE_FB_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_ZFB:
        // 子分部
        deModel.totalNumber = "0";
        deModel.displayType = DeTypeConstants.DE_TYPE_ZFB_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_DELIST:
        // 清单 概算定额
        deModel.displayType = DeTypeConstants.DE_TYPE_DELIST_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_DE:
        // 定额
        deModel.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_ANZHUANG_FEE:
        // 定额
        deModel.displayType = DeTypeConstants.DE_TYPE_ANZHUANG_FEE_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_RESOURCE:
        //人材机 只在概算中有实现
        deModel.displayType = DeTypeConstants.DE_TYPE_RESOURCE_LABEL;
        deModel.isResouceDe = 1;
        break;
    }
    let parentNode = this.ctx.deMap.getNodeById(deModel.parentId);
    if(ObjectUtil.isEmpty(index))
    {
      this.ctx.deMap.addNode(deModel, parentNode);
    }
    else
    {
      this.ctx.deMap.addNodeAt(deModel, parentNode,index);
    }
    //填充箭头
    await this._fillArrow(deModel,parentNode);
    await this.doAfterCreate(deModel);
    deModel.assignDispNos();

    if(checkType){      
      //此时核查父级, 应该是这里啊  
      DeTypeCheckUtil.updateParentDeType(deModel,this.ctx);
    }
    if(deModel.type === DeTypeConstants.DE_TYPE_FB || deModel.type === DeTypeConstants.DE_TYPE_ZFB){
      deModel.costMajorName = null;
    }
    return deModel;
  }

  /**
   * 处理箭头
   * @param newNode
   * @param parentNode
   * @private
   */
  async _fillArrow(newNode, parentNode) {
    newNode.displaySign = ObjectUtils.isEmpty(newNode.children) ? BranchProjectDisplayConstant.noSign : BranchProjectDisplayConstant.open;
    //处理父节点箭头
    if (ObjectUtil.isNotEmpty(parentNode)) {
      parentNode.displaySign = BranchProjectDisplayConstant.open;
      await this.updateDe(parentNode);
    }

    if (newNode.type === DeTypeConstants.DE_TYPE_DE && newNode.displaySign === BranchProjectDisplayConstant.noSign && ObjectUtil.isNotEmpty(newNode.rcjList)) {
      //判断是否有主材人材机
      let filter = newNode.rcjList.filter(o => o.kind === RcjTypeEnum["TYPE5"].code);
      if (ObjectUtil.isNotEmpty(filter)) {
        newNode.displaySign = BranchProjectDisplayConstant.open;
      }
    }

  }

  /**
   * 创建定额后操作
   * @param deModel
   * @returns {Promise<void>}
   */
  async doAfterCreate(deModel) {
    let {service} = EE.app;
    //定额创建 初始化
    await service.PreliminaryEstimate.gsInitDeService.init(deModel);

    let parentDeRow = this.findFirstDeOrDeList(deModel);
    if(ObjectUtil.isNotEmpty(parentDeRow))
    {
      deModel.costFileCode = parentDeRow.costFileCode;
      deModel.costMajorName = parentDeRow.costMajorName;
    }
  }


  /**
   * 定额行的 人材机
   * @param constructId
   * @param unitId
   * @param resourceId
   * @param rowId
   */
  async appendDeResource(constructId, unitId, resourceId, rowId) {
    let {service} = EE.app;
    let deRow = this.getDeById(rowId);
    if(ObjectUtil.isNotEmpty(deRow))
    {
      this.removeRowRelatedDatas(deRow);
    }

    deRow.type = DeTypeConstants.DE_TYPE_RESOURCE;
    deRow.displaySign = BranchProjectDisplayConstant.noSign;
    let baseRCJ = await service.PreliminaryEstimate.gsBaseRcjService.queryRcjById({standardId:resourceId});
    PropertyUtil.copyProperties(baseRCJ, deRow, [...DeDomain.avoidProperty,"sequenceNbr"]);
    deRow.isDeResource = CommonConstants.COMMON_YES;
    baseRCJ.isDeResource = CommonConstants.COMMON_YES;
    baseRCJ.deRowId = rowId;
    deRow.marketPrice = baseRCJ.price;
    deRow.resQty = 0;
    deRow.totalNumber = 0;
    deRow.quantity = 0;
    deRow.deName = baseRCJ.materialName;
    deRow.deCode = baseRCJ.materialCode;
    deRow.deResourceKind = baseRCJ.kind;
    deRow.standardId = baseRCJ.sequenceNbr;
    deRow.specification = baseRCJ.specification;
    deRow.isFyrcj = baseRCJ.isFyrcj;

    let parentDeRow = this.findFirstDeOrDeList(deRow);
    let unitProject = await service.PreliminaryEstimate.gsProjectCommonService.getUnit(deRow.constructId, deRow.unitId);
    if(ObjectUtil.isNotEmpty(parentDeRow))
    {
      deRow.costFileCode = parentDeRow.costFileCode;
      deRow.costMajorName = parentDeRow.costMajorName;
    }else{
      
      deRow.costFileCode = unitProject.constructMajorType;
      if( deRow.costFileCode != baseRCJ.libraryCode){
        let baseDeLibraryModel = await service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(baseRCJ.libraryCode);
        deRow.costMajorName = baseDeLibraryModel.projectType;
        deRow.remark = baseDeLibraryModel.libraryName;
      }
      
    }

    await this.attachDeRCJ([baseRCJ], deRow, [],CommonConstants.COMMON_YES,true);

    if(deRow.isTempRemove === CommonConstants.COMMON_YES
      || (ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.isTempRemove === CommonConstants.COMMON_YES)
    ){
      
      if(deRow.changeQuantity){
        deRow.originalQuantity = deRow.changeQuantity;//保留旧的原始工程量
      }
      deRow.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(rowId);
    }else{
      await this.notify(deRow,true);
    }
    try {
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        constructMajorType: deRow.libraryCode
      });
      //联动计算装饰超高人材机数量单价
      await service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return deRow;
  }

  async appendUserResource(constructId, unitId, deRowId, userResource,add2UserRcj = true)
  {
    let {service} = EE.app;
    let deRow = this.getDeById(deRowId);
    if(ObjectUtil.isNotEmpty(deRow))
    {
      this.removeRowRelatedDatas(deRow);
    }
    let parentDeRow = this.findFirstDeOrDeList(deRow);
    deRow.type = DeTypeConstants.DE_TYPE_USER_RESOURCE;
    deRow.deResourceKind = userResource.kind;//增加补充人材机类型
    deRow.isDeResource = CommonConstants.COMMON_YES;
    PropertyUtil.copyProperties(userResource ,deRow, [...DeDomain.avoidProperty,"sequenceNbr","resQty","parentId","deResourceKind","type","isDeResource"]);
    deRow.marketPrice = deRow.dePrice;
    deRow.price = deRow.dePrice;
    deRow.deName = deRow.materialName;
    deRow.deCode = deRow.materialCode;
    if(ObjectUtil.isNotEmpty(parentDeRow))
    {
      deRow.costFileCode = parentDeRow.costFileCode;
      deRow.costMajorName = parentDeRow.costMajorName;
    }
    // await this.attachDeRCJ([userResource], deRow, [],CommonConstants.COMMON_NO);
 
    //新增一条人材机-----开始
    let newRG = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr
          , deRow.kind);
    newRG.isDeResource = CommonConstants.COMMON_YES;
    this.typeTransttion(newRG);
    // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
    this.initResourceByUserDe(newRG, deRow.materialCode,deRow.materialName, deRow.price,1,deRow);
    newRG.specification = deRow.specification;
    newRG.unit = deRow.unit;
    this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newRG);
    //新增一条人材机-----结束
    if(deRow.isTempRemove === CommonConstants.COMMON_YES
      || (ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.isTempRemove === CommonConstants.COMMON_YES)
    ){
      deRow.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(deRow.sequenceNbr);
    }else{
      await this.notify(deRow);
    }
    if(add2UserRcj){
      this._addUserRcj2Map(newRG);
    }else{      
      newRG.supplementRcjFlag = RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
    }
    await this._rcjMemory(constructId,unitId,service,this.functionDataMap,ConvertUtil.deepCopy(newRG));
    try {
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        constructMajorType: deRow.libraryCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return deRow;
  }

  _addUserDeRcj2Map(newRG){
    let {service} = EE.app;
    let rcjUserList = service.PreliminaryEstimate.gsRcjService.getUserRcj(newRG.constructId,newRG.unitId);
      newRG.supplementRcjFlag=RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
      newRG.supplementDeRcjFlag=RcjCommonConstants.SUPPLEMENT_DE_RCJ_FLAG;
      let deepResource = ConvertUtil.deepCopy(newRG);
      rcjUserList.push(deepResource);
  }

  _addUserRcj2Map(newRG){
    let {service} = EE.app;
    newRG.supplementRcjFlag=RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
    let rcjUserList = service.PreliminaryEstimate.gsRcjService.getUserRcj(newRG.constructId,newRG.unitId);
    let deepResource = ConvertUtil.deepCopy(newRG);
    rcjUserList.push(deepResource);
  }
  /**
   *
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param userDe
   */
  async appendUserDe(constructId, unitId, deRowId, userDe, add2UserDeBase = true) {
    let {service} = EE.app; 

    let deRow = this.getDeById(deRowId);
    if(ObjectUtil.isNotEmpty(userDe) && ObjectUtil.isNotEmpty( deRow))
    {
      this.removeRowRelatedDatas(deRow);
    }

    userDe.rfee = ObjectUtils.isNotEmpty(userDe.rfee) ? Number(userDe.rfee) : userDe.rfee;
    userDe.cfee = ObjectUtils.isNotEmpty(userDe.cfee) ? Number(userDe.cfee) : userDe.cfee;
    userDe.jfee = ObjectUtils.isNotEmpty(userDe.jfee) ? Number(userDe.jfee) : userDe.jfee;
    userDe.zcfee = ObjectUtils.isNotEmpty(userDe.zcfee) ? Number(userDe.zcfee) : userDe.zcfee;
    userDe.sbfee = ObjectUtils.isNotEmpty(userDe.sbfee) ? Number(userDe.sbfee) : userDe.sbfee;

    deRow.type = DeTypeConstants.DE_TYPE_USER_DE;
    //deRow.isDeResource = CommonConstants.COMMON_YES;
    PropertyUtil.copyProperties(userDe, deRow, [...DeDomain.baseDeToDeAvoidProperty,
      'price','total','totalNumber','CSum','RSum','JSum','rTotalSum','cTotalSum','jTotalSum','rfee'
      ,'cfee','jfee','sbfee','zcfee','parent','parentId','children']);
    deRow.deRowId = deRow.sequenceNbr;
    deRow.resQty = 0;
    deRow.price = 0;
    deRow.totalNumber = 0;
    deRow.total = 0;
    deRow.CSum = 0;
    deRow.RSum = 0;
    deRow.JSum = 0;
    deRow.rTotalSum = 0;
    deRow.cTotalSum = 0;
    deRow.jTotalSum = 0;
    deRow.quantity = 0;
    deRow.standardId = null;
    deRow.isDeResource = null;

    if(ObjectUtils.isNotEmpty(userDe.rcjList)){      
      await this._createUserDeResouceByDeletede(constructId, unitId, userDe, deRow);
    }else{
      userDe.rcjList = this.createUserDeResource(userDe, deRow);
      let constructRcjArray = service.PreliminaryEstimate.gsRcjService.getAllRcj({constructId,unitId});
      for(let newRcj of userDe.rcjList){
        this._addUserDeRcj2Map(newRcj);
        //处理补充人材机的编码处理
        await service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(newRcj,true,constructRcjArray);
      }
    }

    let parentDeRow = this.findFirstDeOrDeList(deRow);
    if(ObjectUtil.isNotEmpty(parentDeRow))
    {
      deRow.costFileCode = parentDeRow.costFileCode;
      deRow.costMajorName = parentDeRow.costMajorName;
    }
    //处理补充定额的 专业
    let classlevel01 = deRow.classifyLevel1?deRow.classifyLevel1.replace('工程','').replace('项目',''):'';
    deRow.classiflevel1 = this._removeChapterPrefix(classlevel01) +"-"+this._removeChapterPrefix(deRow.classifyLevel2);
    //处理定额的归属分类
    await this._fillClasslevelSplitConcat(deRow,service);

    // if(add2UserDeBase){
    //   this.add2UserDeBase(constructId, deRow);
    // }
    //本身或父类临时删除
    if(deRow.isTempRemove === CommonConstants.COMMON_YES
      || (ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.isTempRemove === CommonConstants.COMMON_YES)
    ){
      
      deRow.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(deRow.sequenceNbr);
    }else{
      await this.notify(deRow)

    }
    //加入内存中
    let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
    if(ObjectUtil.isNotEmpty(rcjs)){
      for(let item of rcjs){
        await this._rcjMemory(constructId,unitId,service,this.functionDataMap,ConvertUtil.deepCopy(item));
      }
    }

    return deRow;
  }
  //为了整理子目2018更好分组设置同类
  async _fillClasslevelSplitConcat(deRow, service){
    
    let qdParam = {
      libraryCode:deRow.libraryCode,
      classlevel01:deRow.classifyLevel1,
      classlevel02:deRow.classifyLevel2,
      classlevel03:deRow.classifyLevel3,
      classlevel04:deRow.classifyLevel4,
      page:1,
      limit:5
    };
    const classAllDes = await service.PreliminaryEstimate.gsBaseDeService.queryDeByBdCodeAndName(qdParam);
    if(ObjectUtil.isNotEmpty(classAllDes) && ObjectUtil.isNotEmpty(classAllDes.data)){
      deRow.classlevelSplitConcat = classAllDes.data[0].classlevelSplitConcat;
      deRow.sortNo = classAllDes.data[0].sortNo;
    }
  }
  async _createUserDeResouceByDeletede(constructId, unitId,userDe,deRow){
    let {service} = EE.app;
    let projectModel = this.ctx.treeProject.getAllNodes().find(item=>item.sequenceNbr === unitId);
      for(let rcjDetail of userDe.rcjList){
        let copyRcj = ConvertUtil.deepCopy(rcjDetail);
        copyRcj.rcjId = ConvertUtil.deepCopy(copyRcj.sequenceNbr);
        copyRcj.sequenceNbr = Snowflake.nextId();
        copyRcj.parentId = deRow.sequenceNbr;
        copyRcj.deId = deRow.sequenceNbr;
        copyRcj.deRowId = deRow.sequenceNbr;
        copyRcj.unitId = unitId;
        copyRcj.dePrice = copyRcj.marketPrice;
        copyRcj.originalQty= RcjCommonConstants.DEFAULT_RESQTY;
        copyRcj.isDeCompensation = CommonConstants.COMMON_NO;

        if (copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG)
            || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX)
            || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL)
            || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB)
            || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC)) {
          copyRcj.materialName = deRow.deName + copyRcj.materialName;
        }

        for (let key in RcjTypeEnum) {
          if (RcjTypeEnum[key].code == copyRcj.kind) {
            copyRcj.type = RcjTypeEnum[key].desc;
          }
        }

        //处理pbs
        if(ObjectUtils.isNotEmpty(rcjDetail.pbs)){
          for (const pbsItem of copyRcj.pbs) {
            pbsItem.parentId = copyRcj.sequenceNbr;
            pbsItem.unitId = unitId;
            pbsItem.dePrice = pbsItem.marketPrice;
            pbsItem.originalQty= RcjCommonConstants.DEFAULT_RESQTY;
            for (let key in RcjTypeEnum) {
              if (RcjTypeEnum[key].code == pbsItem.kind) {
                pbsItem.type = RcjTypeEnum[key].desc;
              }
            }
          }
        }
        // if(copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC)){
          
        //   this._addUserRcjCodeCreateAndCheck(copyRcj,projectModel,false);
        // }

        this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, copyRcj);
        await service.PreliminaryEstimate.gsRcjService.bcdeAddRcj(constructId,unitId,deRow,copyRcj);
      }
  }

  /**
   * 刷新人材机编码
   * @param constructId
   * @param unitId
   * @param copyRcj
   * @returns {Promise<null>}
   * @private
   */
  async _addUserDeChangeMaterialCode(constructId, unitId, copyRcj){
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();

    let rcjDeKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let constructProjectRcjs = this.ctx.resourceMap.getValues(rcjDeKey);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    constructProjectRcjs = constructProjectRcjs.filter(item => item.sequenceNbr !== copyRcj.sequenceNbr);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }

    constructProjectRcjs.forEach(item => constructRcjArray.push(item));
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }
    if (ObjectUtils.isNotEmpty(rcjDetailList)) {
      rcjDetailList.forEach(item => constructRcjArray.push(item));
    }


    let {service} = EE.app;
    if (!((ObjectUtils.isNotEmpty(copyRcj.levelMark) ? copyRcj.levelMark : ResourceConstants.LEVEL_MARK_NONE_PB) !== ResourceConstants.LEVEL_MARK_NONE_PB)) {
      await service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(copyRcj, true, constructRcjArray);
    } else {
      await service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, copyRcj, true);
    }
    //修改二次解析的子，触发修改父
    if (!copyRcj.hasOwnProperty('levelMark')) {
      let t1 = constructProjectRcjs.find(i => i.sequenceNbr === copyRcj.parentId);
      await service.PreliminaryEstimate.gsRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, t1, true);
    }

  }

  add2UserDeBase(constructId, userDe) {
    let userDeBase = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
    if (ObjectUtil.isEmpty(userDeBase)) {
      userDeBase = [];
      this.functionDataMap.set(FunctionTypeConstants.PROJECT_USER_DE, userDeBase);
    }
    let rcjLists = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,userDe.sequenceNbr);
    let rcjListCopy = [];
    if(ObjectUtils.isNotEmpty(rcjLists)){
      for(let rcjList of rcjLists){
        rcjListCopy.push(ConvertUtil.deepCopy(rcjList.value));
      }
    }
    delete userDe.parent;//删除父类
    delete userDe.parentId;//删除父类
    userDe.rcjList = rcjListCopy;
    userDeBase.push(userDe);

  }
  _fixCode(code){
    if(ObjectUtil.isEmpty(code)){
      return code;
    }
    if(code.indexOf('#')){
      return code.split('#')[0];
    }
    return code;
  }
 /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deCode
   * @param deRowId
   * @returns {T}
   */
 async checkAndQueryDe(constructId, unitId, deCode) {
  
  let {service} = EE.app; 
  let unitDes = this.ctx.deMap.getAllNodes().filter(item=>item.deCode === deCode && item.unitId === unitId
    && (item.type === DeTypeConstants.DE_TYPE_DELIST ||item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_USER_DE
       || item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE));
  //用户缓存定额数据
  let userDeBases = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
  let project =  this.ctx.treeProject.getNodeById(constructId);
  let pricingMethodFlag = project.pricingMethod == 1;//按市场价
  if(ObjectUtil.isNotEmpty(userDeBases)) {

    let resultUserDes = userDeBases.filter(item => item.deCode === deCode  && item.unitId === unitId);
    if(ObjectUtil.isNotEmpty(unitDes)){
      unitDes = unitDes.concat(resultUserDes);
    }else{
      unitDes = resultUserDes;
    }
  }
  //人材机定额缓存
  let unitAllMemory = await service.PreliminaryEstimate.gsRcjService.getRcjMemory(constructId, unitId);
  if(ObjectUtils.isNotEmpty(unitAllMemory)){
    let results = unitAllMemory.filter(item=>this._fixCode(item.materialCode) === deCode);
    if(ObjectUtil.isNotEmpty(results)){
      let rcjs = [];
      for(let item of results){
        let convertDeRow = this._convertResource2De({constructId,unitId,sequenceNbr:item.sequenceNbr,costFileCode:"",costMajorName:""},item);
        convertDeRow.deResourceKind = item.kind;
        if(convertDeRow.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
          convertDeRow.type = DeTypeConstants.DE_TYPE_USER_RESOURCE;
        }else{
          convertDeRow.type = DeTypeConstants.DE_TYPE_RESOURCE;
        }
        
        let rcjItemArr = rcjs.filter(item=>item.sequenceNbr === convertDeRow.sequenceNbr || item.sequenceNbr.indexOf(convertDeRow.sequenceNbr)>-1);
        if(rcjItemArr&&rcjItemArr.length>0){
          convertDeRow.sequenceNbr += ('__'+ rcjItemArr.length)
        }
        rcjs.push(convertDeRow)
      } 
      if(ObjectUtil.isNotEmpty(unitDes)){
        unitDes = unitDes.concat(rcjs);
      }else{
        unitDes = rcjs;
      }
    }
  }
  let result = await service.PreliminaryEstimate.gsBaseDeService.getDeAndRcjByDeCode(constructId, unitId, deCode);
  if(ObjectUtils.isEmpty(result)){
    let rcjItem = await service.PreliminaryEstimate.gsBaseRcjService.getRcjByCode(constructId, unitId, deCode);
    let resouceKind = null;
    if(ObjectUtils.isNotEmpty(rcjItem)){
      let baseRCJ = await service.PreliminaryEstimate.gsBaseRcjService.queryRcjById({standardId:rcjItem[0].sequenceNbr});
      baseRCJ.marketPrice = baseRCJ.price;
      resouceKind = baseRCJ.kind;
      let convertDeRow = this._convertResource2De({constructId,unitId,sequenceNbr:baseRCJ.sequenceNbr,costFileCode:"",costMajorName:""},baseRCJ);
      result = convertDeRow;
    }
    if(ObjectUtils.isNotEmpty(result)){
      result.type = DeTypeConstants.DE_TYPE_RESOURCE;
      result.deResourceKind = resouceKind;
    }
  }else{
    
    if(result.isExistDe == CommonConstants.COMMON_YES )
      {
        result.type = DeTypeConstants.DE_TYPE_DELIST;
        result.displayType = DeTypeConstants.DE_TYPE_DELIST_LABEL;
      }
      else {
        result.type = DeTypeConstants.DE_TYPE_DE;
        result.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
      }
    
  }
  let dbArr = [];
  if(ObjectUtils.isNotEmpty(result)){      
    dbArr.push(result);
  }
  if(ObjectUtil.isNotEmpty(unitDes)){
    unitDes = this._mergeDuplicates(unitDes,pricingMethodFlag)
    // unitDes = PropertyUtil.shallowCopyAndFilterProperties(unitDes,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree)
  }
  if(ObjectUtil.isNotEmpty(unitDes) && ObjectUtil.isNotEmpty(dbArr)){
    let key = `${dbArr[0].deCode}-${dbArr[0].deName}-${dbArr[0].type}-${dbArr[0].unit}-${dbArr[0].baseJournalPrice}`;
    //匹配定额本身及子级
    if(pricingMethodFlag){
      key = `${dbArr[0].deCode}-${dbArr[0].deName}-${dbArr[0].type}-${dbArr[0].unit}-${dbArr[0].price}`;
    }
    let childMapKey = new Map();
    if(ObjectUtil.isNotEmpty(dbArr[0].subDeList)){
      for(let child of dbArr[0].subDeList){
        child.type = DeTypeConstants.DE_TYPE_DE;
        //db没有baseJrounalPrice，所有同取一个值price
        let childKey = `${child.deCode}-${child.deName}-${child.type}-${child.unit}-${child.price}`;
        childMapKey.set(child.deCode,childKey);
      }
    }
    unitDes = unitDes.filter(item=>{
      let itemKey =  `${item.deCode}-${item.deName}-${item.type}-${item.unit}-${item.baseJournalPrice}`;
      if(pricingMethodFlag){
        itemKey =  `${item.deCode}-${item.deName}-${item.type}-${item.unit}-${item.price}`;
      }
      if(key !== itemKey){
        return true;
      }
      if(ObjectUtil.isNotEmpty(item.children)){
        if(item.children.length != childMapKey.size){
          return true;
        }
        let childMatch = true;
        for(let child of item.children){
          let childKey = `${child.deCode}-${child.deName}-${child.type}-${child.unit}-${child.baseJournalPrice}`;
          if(pricingMethodFlag){
            childKey = `${child.deCode}-${child.deName}-${child.type}-${child.unit}-${child.price}`;
          }
          let childDbkey = childMapKey.get(child.deCode);
          if(childKey !== childDbkey){
            childMatch = false;
          }
        }
        return !childMatch;
      }
      return false;
    });
  }
  if(ObjectUtils.isNotEmpty(result)){
     dbArr = PropertyUtil.shallowCopyAndFilterProperties(dbArr,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree)
  }
  if(ObjectUtil.isNotEmpty(unitDes)){
     unitDes = PropertyUtil.shallowCopyAndFilterProperties(unitDes,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree)
  }
  return  {local:unitDes,db:dbArr};
 }
 _mergeDuplicates(items, pricingMethodFlag){
  let mapKey = new Map();
  for(let item of items){
    let itemKeys = []
    this._add5KeyWithChild(item,itemKeys,pricingMethodFlag);
    mapKey.set(item.sequenceNbr,itemKeys);
  }
  let filtered = [];
  for(let i=0; i< items.length; i++){
    let itemKey = mapKey.get(items[i].sequenceNbr);
    for(let j=i+1;j<items.length; j++){
      let value =  mapKey.get(items[j].sequenceNbr);
      if(_.isEqual(value,itemKey)){
        filtered.push(items[j].sequenceNbr);
      }
    }
  }
   return items.filter(item=>filtered.indexOf(item.sequenceNbr) === -1);
 }

 _add5KeyWithChild(item,itemKeys,pricingMethodFlag){
  let key = `${item.deCode}-${item.deName}-${item.type}-${item.unit}-${item.baseJournalPrice}`;
  if(pricingMethodFlag){
    key = `${item.deCode}-${item.deName}-${item.type}-${item.unit}-${item.price}`;
  }
  itemKeys.push(key);
  if(ObjectUtil.isNotEmpty(item.children)){
    let childs = item.children;
    childs.sort((a,b) => a.index-b.index);
    for(let child of childs){
      this._add5KeyWithChild(child,itemKeys,pricingMethodFlag);
    }
  }
 }

 async _rcjMemory(constructId, unitId, service, businessMap, deepResource){
 //判定内存中不存在，则放入内存
  let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
  if (ObjectUtils.isEmpty(objMap) || objMap.size===0) {
    businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
    objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
  }
  let  memoryRcj=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
  if(ObjectUtils.isNotEmpty(memoryRcj)){
    let  existRcj = await service.PreliminaryEstimate.gsRcjCollectService.findAlikeRcj(memoryRcj,deepResource);
    if(ObjectUtils.isEmpty(existRcj)){
      memoryRcj.push(deepResource);
    }
  }else {
    let   memoryArray=new Array();
    memoryArray.push(deepResource)
    objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId , memoryArray );
  }
 }
  /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deCode
   * @param deRowId
   * @returns {T}
   */
  async getDesAndAppendDe(constructId, unitId, deCode, deRowId) {
    let found = CommonConstants.COMMON_NO;

    let deModel = null;
    let result = null;
    if (ObjectUtil.isEmpty(deCode)) {
      return null;
    }
    let {service} = EE.app;
    //1先查询基础定额
    let  row = this.getDeById(deRowId);
    result = await service.PreliminaryEstimate.gsBaseDeService.getDeAndRcjByDeCode(constructId, unitId, deCode);
    if (ObjectUtil.isNotEmpty(result)) {
      //如果找到直接添加
      found = CommonConstants.COMMON_YES;

      if(result.isExistDe == CommonConstants.COMMON_YES )
      {
        row.type = DeTypeConstants.DE_TYPE_DELIST;
      }
      else {
        row.type = DeTypeConstants.DE_TYPE_DE;
      }
      deModel = await this.appendBaseDe(constructId, unitId, result.sequenceNbr, deRowId,true);
      await this.extendQuantity(constructId, unitId, deRowId);
    }
    else if(ObjectUtil.isEmpty(result))
    {
      //查找编码对应的人材机
      result = await service.PreliminaryEstimate.gsBaseRcjService.getRcjByCode(constructId, unitId, deCode);
      if(ObjectUtil.isNotEmpty(result))
      {
        found = CommonConstants.COMMON_YES;
        deModel = await this.appendDeResource(constructId, unitId, result[0].sequenceNbr, deRowId);
        await this.extendQuantity(constructId, unitId, deRowId)
      }
    }
    //如果没有查到 在当前项目中查找
    if (ObjectUtil.isEmpty(result)) {
      let results = this.ctx.deMap.getAllNodes().filter(item => item.type === DeTypeConstants.DE_TYPE_USER_DE && item.deCode === deCode  && item.unitId === unitId);
      if(ObjectUtil.isNotEmpty(results)){
        if(results.length > 1){
          results.sort((a,b)=>b.updateDate-a.updateDate);
        }
        result = results[0];
        found = CommonConstants.COMMON_YES;
        deModel = await this.appendBaseDeByLocal(constructId, unitId, result, deRowId,false);
        await this.extendQuantity(constructId, unitId, deRowId);
      }
    }
    //如果没有查到
    if (ObjectUtil.isEmpty(result)) {
      //2查询用户定额
      let userDeBases = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
      if(ObjectUtil.isNotEmpty(userDeBases)) {

        result = userDeBases.find(item => item.deCode === deCode  && item.unitId === unitId);
        if (ObjectUtil.isNotEmpty(result)) {

          found = CommonConstants.COMMON_YES;
          if(ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty( deRowId))
          {
            this.removeRowRelatedDatas(row);
          }
          switch (result.type) {
            case DeTypeConstants.DE_TYPE_USER_DE:
              //3.1 如果是用户定额
              deModel = this.appendUserDe(constructId, unitId, deRowId, result,false);       
              // deModel = await this.appendBaseDeByLocal(constructId, unitId, result, deRowId,false);
              break;
            case DeTypeConstants.DE_TYPE_USER_RESOURCE:
              //3.2 如果是 用户定额人材机
              deModel = await this.appendUserResource(constructId, unitId, deRowId,result,false);

              break;
          }
        }

      }else{
        let {service} = EE.app;
        let userDeBases = service.PreliminaryEstimate.gsRcjService.getUserRcj(constructId,unitId);
        // let userDeBases = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
        if(ObjectUtil.isNotEmpty(userDeBases)) {

          let resultUs = userDeBases.filter(item => item.materialCode === deCode && item.unitId === unitId);
          if (ObjectUtil.isNotEmpty(resultUs)) {
            if(resultUs.length > 1){
              resultUs.sort((a,b)=>b.updateDate-a.updateDate);
            }
            result = resultUs[0];
            found = CommonConstants.COMMON_YES;
            let newResult = ConvertUtil.deepCopy(result);
            newResult.unitId=unitId;
            newResult.deId=deRowId;
            newResult.deRowId=deRowId;
            newResult.sequenceNbr=null;
            newResult.parentId = null;
             //3.2 如果是 用户定额人材机
             deModel = await this.appendUserResource(constructId, unitId, deRowId,newResult,false);
          }
        }
      }
      await this.extendQuantity(constructId, unitId, deRowId)
    }

    if(found === CommonConstants.COMMON_YES){
      return deModel;
    }
    return found;
  }


  _removeChapterPrefix(obj){
    if(ObjectUtil.isEmpty(obj)){
      return obj;
    }
    let aa = obj.replace(/^\(.*?\)/g,'');
    let bb = aa.replace(/^第([\s\S]*?)册\s*/g,'');
    let firstObj = bb.replace(/^第([\s\S]*?)章\s*/g,'');
    let dd = firstObj.replace(/(?:一|二|三|四|五|六|七|八|九|十)[、|^ ]/g,'');
    let ss = dd.replace(/[0-9A-za-z]([\s\S]*?)[\.|^ |]\s*/g,'');
    let secondObj = ss.replace(/^\d+(\.\d+)?/g,'');
    return secondObj;
  }
  _calculateLevel(deRow,typeLength){
    let count = 0;
     
    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.type) && ObjectUtil.isEmpty(deRow.fbType)){
      count++
    }   
    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.type) && ObjectUtil.isEmpty(deRow.children)) {
      return -999;
    } 
     
    if( ObjectUtil.isNotEmpty(deRow.children)){
      if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.children[0].type)){
        let max = -5;
        for(let child of deRow.children){
          let childLevel = this._calculateLevel(child,typeLength);
          max = max>childLevel?max:childLevel;
        }
        count += max;  
      }else{
        let resouceFlag = true;
        for(let child of deRow.children){
          if(child.type !== DeTypeConstants.DE_TYPE_USER_RESOURCE && child.type !== DeTypeConstants.DE_TYPE_RESOURCE ){
            resouceFlag = false;
          }
        }
        if(resouceFlag){//都是人材机的分部 会抵消typelength长度
          count = count - typeLength + 1;
        }
      }

    }
    return count;
  }

  
  async arrangeDe(unitId,types){
    let {service} = EE.app;
    //获取根定额
    let rootDe = this.getRoot(unitId);
    //排序
    types.sort((a,b)=>a-b);
    let arrangeNodes = [];
    //获取分部数据
    let fbList = [];
    this._getFbDepth(rootDe,fbList,[DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
    //获取该单位下所有定额
    let allNodes = this.getDes(item=>item.unitId === unitId);
    //计算层级
    if(types.indexOf('4') === -1 && types.length > 0){
      let typeAddLength = types.length;
      let rootLevel = this._calculateLevel(rootDe,typeAddLength);
      if((rootLevel + typeAddLength)>4){
        return 500;
      }
    }
    //如果有删除类型，先删除数据
    if(types.indexOf('4') > -1 && fbList.length > 0){
      //重置父级
      rootDe.children = []
      for(let node of allNodes){
        if(![DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.type)
          &&node.parent 
          && [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.parent.type)){
            if(node.parent != rootDe){
              node.parent = rootDe;
              node.parentId = rootDe.sequenceNbr;
              rootDe.children.push(node);
            }
          arrangeNodes.push(node);
        }
      }
      if(arrangeNodes.length>0){
        for(let fbNode of fbList){
          this.ctx.deMap.removeNodeMapById(fbNode.sequenceNbr);
          fbNode.parent.removeChild(fbNode);
        }
      }
    }else{
      //获取整理后生成的分部
      let deleteFbList = [];
      for(let fbNode of fbList){
        if(ObjectUtil.isNotEmpty(fbNode.fbType)){
          deleteFbList.push(fbNode);
        }
      }
      //获取整理后的分部的子分部
      let childAllFbList = [];
      for(let delFbNode of deleteFbList){
        let childFbList = [];
        this.findChilds(delFbNode,childFbList,[DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
        childAllFbList = childAllFbList.concat(childFbList);
      }
      //把不存在的子分部加入删除分部中
      for(let childAllItem of childAllFbList){
        if(deleteFbList.indexOf(childAllItem) == -1){
          deleteFbList.push(childAllItem);
        }
      }
      //删除子目的分部关系
      for(let node of allNodes){
        if(ObjectUtil.isEmpty(node.parent) && ![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.type)){
          node.parent = this.getDeById(node.parentId);
        }
        if(![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.type)
          && node.parent 
          && [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.parent.type)){
          if(deleteFbList.length >0 ){
            let upParentFb = this.getParentFromRange(deleteFbList,node);
            if(node !== upParentFb){
              node.parent = upParentFb.parent;
              node.parentId = upParentFb.parentId;
              upParentFb.parent.children.splice( upParentFb.parent.children.indexOf(upParentFb),0,node);
            }
          }
          arrangeNodes.push(node);
        }
      }
      //删除整理后的分部
      if(arrangeNodes.length>0){

        for(let fbNode of deleteFbList){
          this.ctx.deMap.removeNodeMapById(fbNode.sequenceNbr);
          fbNode.parent?fbNode.parent.removeChild(fbNode):null;
        }
      }
    }

    let caculateDe = null;
    let splitStr = "-AA-";
    if(arrangeNodes.length > 0){
      //补充定额
      let buchongNodes = [];
      let nonTypeNodes = [];
      for(let arrangeNode of arrangeNodes){
        if(arrangeNode.type === DeTypeConstants.DE_TYPE_EMPTY ){
          nonTypeNodes.push(arrangeNode);
        }
        if(arrangeNode.type === DeTypeConstants.DE_TYPE_USER_RESOURCE || arrangeNode.type === DeTypeConstants.DE_TYPE_RESOURCE ){
          buchongNodes.push(arrangeNode);
        }
        if(arrangeNode.type === DeTypeConstants.DE_TYPE_USER_DE && ObjectUtil.isEmpty(arrangeNode.classlevelSplitConcat)){
          await this._fillClasslevelSplitConcat(arrangeNode,service);
        }
        
      }
      //处理空类型的定额
      if(ObjectUtil.isNotEmpty(nonTypeNodes)){
        for(let noNode of nonTypeNodes){
          this.ctx.deMap.removeNodeMapById(noNode.sequenceNbr);
          noNode.parent?noNode.parent.removeChild(noNode):null;
        }
      }
      const groupArrangeNodes = arrangeNodes.reduce((acc,obj)=>{
        const key = obj.libraryCode;
        if(!acc[key]){
          acc[key]=[];
        }
        acc[key].push(obj);
        return acc;
      },{})
      //按定额册划分
      let groupKeys = Object.keys(groupArrangeNodes);
      let groupParentMap = new Map();
      for(let groupKey of groupKeys){
        let parentMap = new Map();
        let groupNodes = groupArrangeNodes[groupKey];
        groupNodes.sort((a,b)=>a.sortNo-b.sortNo);
        groupParentMap.set(groupKey,parentMap);
        //处理不同类型分部
        for(let type of types){
          if(type == '4'){
            continue;
          }
          //处理并按父分部分类
          for(let arrangeNode of groupNodes){
            //过滤安装及人材机定额
            if(arrangeNode.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE 
              || arrangeNode.type === DeTypeConstants.DE_TYPE_EMPTY
              || arrangeNode.type === DeTypeConstants.DE_TYPE_USER_RESOURCE 
              || arrangeNode.type === DeTypeConstants.DE_TYPE_RESOURCE){
              continue;
            }
            let splitConcat = arrangeNode.classlevelSplitConcat;
            let standarConcats = this._convertSplitConcat(splitConcat);
            let concatIndex = this._getStandardConcat(type,standarConcats);
            if(ObjectUtils.isEmpty(concatIndex) && type > "1"){
              concatIndex = this._getPrefix(arrangeNode,type - 1);
            }
            let key = arrangeNode.parentId + splitStr + this._getPrefix(arrangeNode,type) + splitStr + concatIndex;
            let parentFBList = null;
            if(parentMap.has(key)){
              parentFBList = parentMap.get(key);
            }else{
              parentFBList = [];
              parentMap.set(key,parentFBList)
            }
            let eIndex = parentFBList.findIndex(eItem=>eItem.sequenceNbr === arrangeNode.sequenceNbr);
            if(eIndex < 0){
              parentFBList.push(arrangeNode);
            }
          }
        }
      }
      for(let [groupKey,groupValue] of groupParentMap){
        let defaultCount = 1;//不同的定额册 顶级不重置，移到内部为分开
        let parentMap = groupValue;
        let countStr = defaultCount.toString().padStart(2,'0');
        let firstZY = true;
        //创建 新的分部
        for(let [key,value] of parentMap){
          let newModel = new StandardDeModel(value[0].constructId,value[0].unitId,Snowflake.nextId(),value[0].parentId,value[0].parent.type ===DeTypeConstants.DE_TYPE_DEFAULT ?DeTypeConstants.DE_TYPE_FB :DeTypeConstants.DE_TYPE_ZFB);
          let keyArr = key.split(splitStr);
          newModel.libraryCode = groupKey;
          newModel.deName = keyArr[1];
          let parentModel = value[0].parent;
          //2018走数据库的章节，其他自动排序
          if(groupKey == '2018-JZGC-GS' || groupKey == '2018-AZGC-GS'){
            newModel.deCode = countStr + keyArr[2];
          }else{   
            if(parentModel.type === DeTypeConstants.DE_TYPE_DEFAULT || ObjectUtil.isEmpty(parentModel.fbType)){
              if(firstZY){
                firstZY = false;
              }else{
                let zyFbNode = parentModel.children.find(item=>[DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(item.type) && item.deCode.startsWith(countStr) && item.libraryCode == groupKey);
                if(ObjectUtil.isNotEmpty(zyFbNode) && ObjectUtil.isNotEmpty(zyFbNode.children)){
                  let zyFnodeChild = zyFbNode.children[0];
                  let zyChildLevel1 =  DeTypeConstants.DE_TYPE_USER_DE === zyFnodeChild.type?zyFnodeChild.classifyLevel1:zyFnodeChild.classlevel01
                  let arrangeChildLevel1 =  DeTypeConstants.DE_TYPE_USER_DE === value[0].type?value[0].classifyLevel1:value[0].classlevel01
                  if(zyChildLevel1 !== arrangeChildLevel1){
                    defaultCount ++ ;
                    countStr = defaultCount.toString().padStart(2,'0');
                  }
                }
              }
            }
            if(ObjectUtil.isNotEmpty(parentModel.fbType)){
              countStr = parentModel.deCode.substring(0,2);
            }
            newModel.deCode = this._getArrangeCode(parentModel,countStr,types,value[0],groupKey);            
          }
          
          newModel.fbType = '1';//无具体意思，判断是否整理分部的部分
          let index = null;
          if(ObjectUtil.isNotEmpty(parentModel)){
            // index = parentModel.children.indexOf(value[0])  插入到最后，前面已经按册子和序号排序了
          }
          newModel = await this.createDeRow(newModel,index);
          newModel.displaySign = BranchProjectDisplayConstant.open;
          //重新挂载子目新的分部
          for(let valueItem of value){
            //解除父级children与现有关系
            valueItem.parent.removeChild(valueItem);
            newModel.addChild(valueItem)
          }
          caculateDe = newModel;
        }
      }
      //处理人材机分部
      if(ObjectUtil.isNotEmpty(buchongNodes) && ObjectUtil.isNotEmpty(types) 
        && ((types.indexOf('4') === -1 &&types.length > 0)|| (types.indexOf('4') > -1 &&types.length > 1)) ){
        // 按parentid 分组
        let buMap = new Map();
        for(let node of buchongNodes){
          let buArr;
          if(buMap.has(node.parentId)){
            buArr =  buMap.get(node.parentId);
          }else{
            buArr = [];
            buMap.set(node.parentId,buArr);
          }
          buArr.push(node);
        }
        for(let [key,value] of buMap){
          let newModel = new StandardDeModel(value[0].constructId,value[0].unitId,Snowflake.nextId(),key,DeTypeConstants.DE_TYPE_ZFB);
          newModel.deName = '补充分部';
          newModel.fbType = '3';
          newModel.deCode = '';//参照不设置编码
          // newModel.deCode = this._getArrangeCode(value[0].parent);
          newModel = await this.createDeRow(newModel);
          newModel.displaySign = BranchProjectDisplayConstant.open;
          //重新挂载子目新的分部
          for(let valueItem of value){
            valueItem.parent.removeChild(valueItem);
            newModel.addChild(valueItem);
          }
          caculateDe = newModel;
        }
      }
      
    }

    //处理计算引擎 ,随机一个分部都可以影响所有分部的重新计算
    if(caculateDe != null){
      
      let fc = FBCalculator.getInstance({constructId: caculateDe.constructId, unitId: caculateDe.unitId, deRowId: caculateDe.sequenceNbr},this.ctx);
      await fc.analyze();
    }
    //保存整理类型
    let objMap = this.functionDataMap.get(FunctionTypeConstants.YSH_TABLELIST);
    if(ObjectUtil.isEmpty(objMap)){
      objMap = new Map();
      this.functionDataMap.set(FunctionTypeConstants.YSH_TABLELIST,objMap);
    }
    objMap.set(FunctionTypeConstants.YSH_TABLELIST_ARRANGE+unitId,types);
    return 200;
  }
  //获得0编码
  _getStandardConcat(type,standardConcats){
    let standardCode = "";
    if(ObjectUtils.isEmpty(standardConcats)){
      return standardCode;
    }
    if(type == "2"){
      if(standardConcats.length < 1){
        return this._getStandardConcat("1",standardConcats);
      }else{
        return standardConcats[0];
      }
    }
    if(type == "3"){
      if(standardConcats.length < 2){
        return this._getStandardConcat("2",standardConcats);
      }else{
        return standardConcats[1];
      }
    }
    return standardCode;
  }
  //1.1.2转[01,01,02]
  _convertSplitConcat(splitConcat){
    let splitArr = [];
    if(ObjectUtils.isNotEmpty(splitConcat)){
      let codes = splitConcat.split(".");
      // for(let i = 0; i < codes.length; i++){
      //   let code = codes[i];
      //   let countStr = code.toString().padStart(2,'0');
      //   splitArr.push(countStr);
      // }
      //写死了，
      let zyCode = codes[0];//专业
      let zzCode = codes[1];//章
      let zjCode = codes.length>2?codes[2]:codes[1];//节
      let zyCountStr = zyCode.toString().padStart(2,'0');
      let zzCountStr = zyCountStr + zzCode.toString().padStart(2,'0');
      let zjCountStr = zzCountStr + zjCode.toString().padStart(2,'0');
      splitArr.push(zyCountStr);
      splitArr.push(zzCountStr);
      splitArr.push(zjCountStr);
    }
    return splitArr;      
  }

  _getMidPad(parentDe,childDe,countStr,groupKey){
    let midChild = parentDe.children.filter(item=>[DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(item.type) && item.deCode.startsWith(countStr) && item.libraryCode == groupKey);
    if(ObjectUtil.isEmpty(midChild)){
      return countStr + "01";
    }
    let realCode = null;
    for(let item of midChild){
      if(ObjectUtil.isEmpty(item.children) || item.children.length === 0){
        continue;
      }
      let iChild = item.children[0];
      let iChildLevel2 = DeTypeConstants.DE_TYPE_USER_DE === iChild.type?iChild.classifyLevel2:iChild.classlevel02
      let childDeLevel2 = DeTypeConstants.DE_TYPE_USER_DE === childDe.type?childDe.classifyLevel2:childDe.classlevel02
      if(iChildLevel2 == childDeLevel2){
          realCode = item.deCode;
          break;
        }
    }
    if(ObjectUtil.isEmpty(realCode)){
      let count = midChild.length + 1;
      let lastCountStr = count.toString().padStart(2,'0');
      return countStr+lastCountStr;
    }else{
      return realCode.substring(0, 4);
    }

  }
  /**
   * 自主排序
   * @param {*} arrangeDe 
   * @returns 
   */
  _getArrangeCode(arrangeDe,countStr,types, childDe, groupKey){
    // let count = 0;
    // for(let child of arrangeDe.children){
    //   if(child.fbType && child.fbType === '1'){
    //     count++;
    //   }
    // }
    // count ++;
    // let countStr = count.toString().padStart(2,'0');
    // if(arrangeDe.type === DeTypeConstants.DE_TYPE_DEFAULT || ObjectUtil.isEmpty(arrangeDe.fbType)){
    //   return countStr;
    // }else{
    //   return arrangeDe.deCode+countStr;
    // }

    if(arrangeDe.type === DeTypeConstants.DE_TYPE_DEFAULT || ObjectUtil.isEmpty(arrangeDe.fbType)){
      if(types.includes("1")){
        return countStr;
      }
      if(types.includes("2")){
        let count = arrangeDe.children.filter(item=>item.deCode.startsWith(countStr)&&item.libraryCode == groupKey).length;
        count = count+1;
        let midCountStr = count.toString().padStart(2,'0');
        return countStr + midCountStr;
      }
      if(types.includes("3")){
        let preCountStr = this._getMidPad(arrangeDe,childDe,countStr,groupKey);
        let count = arrangeDe.children.filter(item=>item.deCode.startsWith(preCountStr)&&item.libraryCode == groupKey).length;
        count = count+1;
        let midCountStr = count.toString().padStart(2,'0');
        
        //不存在中间章直接定死01
        return preCountStr + midCountStr;
      }
    }else{
      if(!types.includes("2")&&types.includes("3")){
        let preCountStr = this._getMidPad(arrangeDe,childDe,countStr,groupKey);
        let count = arrangeDe.children.filter(item=>item.deCode.startsWith(preCountStr)&&item.libraryCode == groupKey).length;
        count = count+1;
        let midCountStr = count.toString().padStart(2,'0');
        //不存在中间章直接定死01
        return preCountStr+midCountStr;
      }else{

        let count =  arrangeDe.children.filter(item=>item.deCode.startsWith(countStr)&&item.libraryCode == groupKey).length;
        count = count+1;
        let midCountStr = count.toString().padStart(2,'0');
        return arrangeDe.deCode+midCountStr;
      }
    }
  }

  /**
   * 前缀
   * @param {*} arrangeNode 
   * @param {*} type 
   * @returns 
   */
  _getPrefix(arrangeNode,type){
    let prefix = null;
    if(type == "1"){
      prefix = arrangeNode.type === DeTypeConstants.DE_TYPE_USER_DE?arrangeNode.classifyLevel1:arrangeNode.classlevel01
      if(ObjectUtil.isEmpty(prefix)){
        return "---";
      }
    }
    if(type == "2"){
      prefix = arrangeNode.type === DeTypeConstants.DE_TYPE_USER_DE?arrangeNode.classifyLevel2:arrangeNode.classlevel02
      if(ObjectUtil.isEmpty(prefix)){
        return this._getPrefix(arrangeNode, "1");
      }
    }
    if(type == "3"){
      prefix = arrangeNode.type === DeTypeConstants.DE_TYPE_USER_DE?arrangeNode.classifyLevel3:arrangeNode.classlevel03;
      if(ObjectUtil.isEmpty(prefix)){
        return this._getPrefix(arrangeNode, "2");
      }
    }
    return this._removeChapterPrefix(prefix); 
  }
  //获取范围内定额的父级
  getParentFromRange(deList,deNode){
    let de = deList.find(item=>item.sequenceNbr === deNode.parentId);
    if(ObjectUtil.isEmpty(de)){
      return deNode;
    }
    return this.getParentFromRange(deList,de);
  }
  /**
   * 上移下移功能
   * @param {*} sequenceNbr 
   * @param {*} type 
   */
  async moveUpAndDown(deRowIds,type){

    let nodesFilter = this.getDes(item=>deRowIds.indexOf(item.sequenceNbr)>-1);
    // 过滤找到最顶级的同级定额
    let nodes = [];
    if(ObjectUtil.isNotEmpty(nodesFilter)){
      let parentId = null;
      let tempNodes = [];
      for(let node of nodesFilter){
        let parentNode = nodesFilter.find(item=>item.sequenceNbr === node.parentId);
        if(ObjectUtil.isNotEmpty(parentNode)){
          continue;
        }
        tempNodes.push(node);
      }

      for(let node of tempNodes){
        if(ObjectUtil.isEmpty(parentId)){
          parentId = node.parentId;
        }
        if(node.parentId === parentId){
          nodes.push(node);
        }
      }
    }

    if(ObjectUtil.isNotEmpty(nodes) && ObjectUtil.isNotEmpty(nodes[0].parentId))
    {
      let parentNode = this.getDeById(nodes[0].parentId);
      if(ObjectUtil.isNotEmpty(parentNode)){
        let childrens = parentNode.children;
        if(ObjectUtils.isEmpty(childrens)){
          childrens = this.getDes(item=>item.parentId === parentNode.sequenceNbr);
        }
        //重置父级排序
        childrens.forEach((item,index)=> item.index=index);

        if(type === "up"){
          //nodes 排序  正向
          nodes.sort((a,b)=>a.index-b.index);
          for(let mNode of nodes){
            let curIndex = mNode.index;
            if(curIndex > 0){
              let preNode = childrens.find(item=>item.index === (curIndex-1));
              let preRealIndex = nodes.findIndex(item=>item.sequenceNbr === preNode.sequenceNbr);
              if(preRealIndex>-1){
                continue;
              }
              mNode.index = preNode.index;
              preNode.index = curIndex;
            }
          }
        }  
        if(type === "down"){
          //nodes 排序  反向
          nodes.sort((a,b)=>b.index-a.index);
          for(let mNode of nodes){
            let curIndex = mNode.index;
            if(curIndex < (childrens.length-1)){
              let nextNode = childrens.find(item=>item.index === (curIndex+1));
              let nextRealIndex = nodes.findIndex(item=>item.sequenceNbr === nextNode.sequenceNbr);
              if(nextRealIndex>-1){
                continue;
              }
              mNode.index = nextNode.index;
              nextNode.index = curIndex;
            }
          }
        }
      }
    }
  }

  /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deCode
   * @param deRowId
   * @returns {T}
   */
  async getDesAndAppendDeAz(constructId, unitId, deCode, deRowId) {
    let found = {};
    found.found = CommonConstants.COMMON_NO;

    if (ObjectUtil.isEmpty(deCode)) {
      return null;
    }

    //1先查询当前行定额数据
    let row = this.getDeById(deRowId);

    //查询当前单位安装费用类型
    let sameDecodeList = [];
    let anDeList = this.getDeTreeDepth(constructId, unitId, null, ["07"]);
    if (ObjectUtil.isEmpty(anDeList)) {
      return found;
    } else {
      sameDecodeList = anDeList.filter(p => p.deCode === deCode && p.deRowId !== deRowId);
      if (ObjectUtil.isEmpty(sameDecodeList)) {
        return found;
      }
    }

    if (sameDecodeList.length === 1) {
      found.found = CommonConstants.COMMON_YES;
      row.type = sameDecodeList[0].type;
      await this.appendBaseDeAz(constructId, unitId, sameDecodeList[0].sequenceNbr, deRowId, true);
      await this.extendQuantity(constructId, unitId, deRowId);
    } else {
      found.found = 2;
      found.list = sameDecodeList;
    }

    return found;
  }

  /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param resourcesDeRowId
   * @returns {T}
   */
  async getDeAndAppendDeAzInsert(constructId, unitId, deRowId, resourcesDeRowId) {
    let found = {};
    found.found = CommonConstants.COMMON_NO;

    let resourcesRow = this.getDeById(resourcesDeRowId);

    if (ObjectUtil.isEmpty(resourcesRow)) {
      return found;
    }
    found.found = CommonConstants.COMMON_YES;
    row.type = resourcesRow.type;
    await this.appendBaseDe(constructId, unitId, anDeList[0].sequenceNbr, deRowId, true);
    await this.extendQuantity(constructId, unitId, deRowId);

    return found;
  }



  async extendQuantity(constructId, unitId, deRowId) {
    let deRow = this.getDeById(deRowId);
    if(ObjectUtil.isNotEmpty(deRow)) {
      let quantityExpression = deRow.quantityExpression
      let originalQuantity = ObjectUtils.isEmpty(quantityExpression)? deRow.originalQuantity: quantityExpression;
      if(ObjectUtils.isNotEmpty(originalQuantity)){
        await this.updateQuantity(constructId, unitId, deRowId,deRow.originalQuantity, false, true, false);
      }
    }
  }
  _createDeTcResource(deModel,baseDe) {
    if (ObjectUtil.isNotEmpty(baseDe.compensation)  && baseDe.compensation.rgfCompensation != 0){
      let newRG = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(),deModel.sequenceNbr
          , ResourceKindConstants.TYPE_R);
      newRG.isDeResource = CommonConstants.COMMON_NO;
      newRG.isDeCompensation = CommonConstants.COMMON_YES;
      this.typeTransttion(newRG);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newRG, '', DeCommonConstants.COMPENSATION_MATERIAL_RG_LABEL, 1,baseDe.compensation.rgfCompensation,deModel);

      this.resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, newRG);
    }
    if (ObjectUtil.isNotEmpty(baseDe.compensation)  && baseDe.compensation.clfCompensation != 0){
      let newCL = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(),deModel.sequenceNbr
          , ResourceKindConstants.TYPE_C);
      newCL.isDeResource = CommonConstants.COMMON_NO;
      newCL.isDeCompensation = CommonConstants.COMMON_YES;
      this.typeTransttion(newCL);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newCL, '', DeCommonConstants.COMPENSATION_MATERIAL_CL_LABEL, 1,baseDe.compensation.clfCompensation,deModel);

      this.resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, newCL);
    }
    if (ObjectUtil.isNotEmpty(baseDe.compensation)  && baseDe.compensation.jxfCompensation != 0){
      let newJX = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(),deModel.sequenceNbr
          , ResourceKindConstants.TYPE_J);
      newJX.isDeResource = CommonConstants.COMMON_NO;
      newJX.isDeCompensation = CommonConstants.COMMON_YES;
      this.typeTransttion(newJX);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newJX, '', DeCommonConstants.COMPENSATION_MATERIAL_JX_LABEL, 1,baseDe.compensation.jxfCompensation,deModel);

      this.resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, newJX);
    }
  }
  /**
   *
   * @param userDe
   * @param deRow
   */
  createUserDeResource(userDe, deRow) {
    let userResource = [];
    let projectModel = this.ctx.treeProject.getAllNodes().find(item=>item.sequenceNbr === deRow.unitId);
    if (ObjectUtil.isNotEmpty(userDe.rfee))
    {
      let newRG = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr
          , ResourceKindConstants.INT_TYPE_R);
      newRG.isDeResource = CommonConstants.COMMON_NO;
      this.typeTransttion(newRG);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newRG, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_LABEL, userDe.rfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newRG,projectModel);
      userResource.push(newRG);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newRG);
    }
    if (ObjectUtil.isNotEmpty(userDe.cfee)) {
      let newCL = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.INT_TYPE_C);
      newCL.isDeResource = CommonConstants.COMMON_NO;
      this.typeTransttion(newCL);
      // BCCLF ;名称=“补充材料费”；单位=元；类别=材料费；消耗量=1；定额价=市场价=【输入的材料费】；其余00值为空 ③主材：材料编码=
      this.initResourceByUserDe(newCL, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_LABEL, userDe.cfee,1,deRow);
     
      DeCommonConstants.initCodeAndName(newCL,projectModel);
      userResource.push(newCL);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newCL);
    }
    if (ObjectUtil.isNotEmpty(userDe.jfee)) {
      let newJX = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.INT_TYPE_J);
      // BCJXF ;名称=“补充机械费”；单位=元；类别=机械费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空 ⑤设备：材料编码=
      newJX.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newJX);
      this.initResourceByUserDe(newJX, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_LABEL, userDe.jfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newJX,projectModel);
      userResource.push(newJX);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newJX);
    }
    if (ObjectUtil.isNotEmpty(userDe.zcfee)) {
      let newZC = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.INT_TYPE_ZC);
      newZC.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newZC);
      // BCZCF ;名称=“补充主材费”；单位=元；类别=主材费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空 ④机械：材料编码=
      this.initResourceByUserDe(newZC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_LABEL, userDe.zcfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newZC,projectModel);
      userResource.push(newZC);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newZC);
      deRow.isExistedZcSb = CommonConstants.COMMON_YES;
    }
    if (ObjectUtil.isNotEmpty(userDe.sbfee)) {
      let newSB = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.INT_TYPE_SB);
      newSB.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newSB);
      // BCSBF ;名称=“补充设备费”；单位=元；类别=人工费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空
      this.initResourceByUserDe(newSB, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_LABEL, userDe.sbfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newSB,projectModel);
      userResource.push(newSB);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newSB);
      deRow.isExistedZcSb = CommonConstants.COMMON_YES;
    }
    return userResource;
  }

   /**
   *
   * @param userDe
   * @param deRow
   */
  async getUserDeResource(userDe) {
    let userResource = [];
    if (ObjectUtil.isNotEmpty(userDe.rfee))
    {
      let newRG = new ResourceModel(null, null, null, null
          , ResourceKindConstants.INT_TYPE_R);
      newRG.isDeResource = CommonConstants.COMMON_NO;
      this.typeTransttion(newRG);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newRG, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_LABEL, userDe.rfee,1,newRG);

      // DeCommonConstants.initCodeAndName(newRG,projectModel);
      userResource.push(newRG);
    }
    if (ObjectUtil.isNotEmpty(userDe.cfee)) {
      let newCL = new ResourceModel(null, null, null, null, ResourceKindConstants.INT_TYPE_C);
      newCL.isDeResource = CommonConstants.COMMON_NO;
      this.typeTransttion(newCL);
      // BCCLF ;名称=“补充材料费”；单位=元；类别=材料费；消耗量=1；定额价=市场价=【输入的材料费】；其余00值为空 ③主材：材料编码=
      this.initResourceByUserDe(newCL, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_LABEL, userDe.cfee,1,newCL);

      // DeCommonConstants.initCodeAndName(newCL,projectModel);
      userResource.push(newCL);
    }
    if (ObjectUtil.isNotEmpty(userDe.jfee)) {
      let newJX = new ResourceModel(null, null, null, null, ResourceKindConstants.INT_TYPE_J);
      // BCJXF ;名称=“补充机械费”；单位=元；类别=机械费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空 ⑤设备：材料编码=
      newJX.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newJX);
      this.initResourceByUserDe(newJX, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_LABEL, userDe.jfee,1,newJX);

      // DeCommonConstants.initCodeAndName(newJX,projectModel);
      userResource.push(newJX);
    }
    if (ObjectUtil.isNotEmpty(userDe.zcfee)) {
      let newZC = new ResourceModel(null, null, null, null, ResourceKindConstants.INT_TYPE_ZC);
      newZC.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newZC);
      // BCZCF ;名称=“补充主材费”；单位=元；类别=主材费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空 ④机械：材料编码=
      this.initResourceByUserDe(newZC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_LABEL, userDe.zcfee,1,newZC);

      // DeCommonConstants.initCodeAndName(newZC,projectModel);
      userResource.push(newZC);
    }
    if (ObjectUtil.isNotEmpty(userDe.sbfee)) {
      let newSB = new ResourceModel(null, null, null, null, ResourceKindConstants.INT_TYPE_SB);
      newSB.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newSB);
      // BCSBF ;名称=“补充设备费”；单位=元；类别=人工费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空
      this.initResourceByUserDe(newSB, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_LABEL, userDe.sbfee,1,newSB);

      // DeCommonConstants.initCodeAndName(newSB,projectModel);
      userResource.push(newSB);
    }
    return userResource;
  }


  initResourceByUserDe(newResource, materialCode, materialName, price, resQty = 1,deRow) {
    newResource.materialCode = materialCode;
    newResource.materialName = materialName;
    newResource.dePrice = price;
    newResource.marketPrice = price;
    newResource.resQty = resQty;
    newResource.quantity = 0;
    newResource.unit = DeCommonConstants.COMMON_UNIT;
    newResource.totalNumber = 0;
    newResource.deRowId = deRow.sequenceNbr;
    newResource.deId = deRow.sequenceNbr;
    newResource.originalQty= RcjCommonConstants.DEFAULT_RESQTY;
    newResource.levelMark =RcjCommonConstants.LEVELMARK_ZERO;
    newResource.specification =null;
    newResource.isFyrcj =1;
  }

  typeTransttion(rcj){
    rcj.kind=rcj.type ;
    for (let key in RcjTypeEnum) {
      if (RcjTypeEnum[key].code == rcj.kind) {
        rcj.type =  RcjTypeEnum[key].desc;
      }
    }
  }

  /**
   *
   * @param deRowModel
   */
  removeRowRelatedDatas(deRowModel)
  {
    if(ObjectUtil.isEmpty(deRowModel))
    {
      return;
    }
    if (ObjectUtil.isNotEmpty(deRowModel)) {
      let childs = [];
      this.findDeRows(deRowModel, childs);
      this.removeRowRelatedDataById(childs);
    }
    this.ctx.resourceMap.removeByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRowModel.sequenceNbr);
  }

  findFirstDeOrDeList(deRow)
  {
    if(ObjectUtil.isEmpty(deRow))
    {
      return;
    }
    let parentList = [];
    this.findParents(deRow,parentList,[DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_DELIST]);
    if(ObjectUtils.isEmpty(parentList))
    {
      return;
    }
    return parentList[0];

  }

  async appendDeByUnitDe(localDe, unitId, deRowId,unitConversion,replaceResQty = false) {

    if(ObjectUtil.isEmpty(localDe)){
      return null;
    }
    let {service} = EE.app;
    let deModel = this.getDeById(deRowId);
    deModel.resQty = 0;
    deModel.totalNumber = 0;
    deModel.price = 0;
    //先清除数据
    if(ObjectUtil.isNotEmpty( deModel)){
      this.removeRowRelatedDatas(deModel);
    }
    //处理属性值
    PropertyUtil.copyProperties(localDe, deModel, DeDomain.baseDeToDeAvoidProperty.concat(['index','deRowId','parent','parentId','children']));
    if(!replaceResQty){
      deModel.quantity = 0; //重置工程量为0
      deModel.originalQuantity = 0;
      deModel.initDeRcjNameList = [];
    }
    deModel.type = localDe.type;
    deModel.unitId = unitId; //防止localDe unitId 与deModel不一致
    //处理定额的人材机
    let rcjList = localDe.rcjList;
    if(ObjectUtils.isNotEmpty(rcjList)){
      let initDeRcjNameList = [];
      for(let rcj of rcjList){
        let copyRcj =  ConvertUtil.deepCopy(rcj.value);
        this._resetRcjId(copyRcj,deModel.sequenceNbr);
        if(deModel.isTempRemove === CommonConstants.COMMON_YES && copyRcj.isTempRemove !== CommonConstants.COMMON_YES){
          copyRcj.isTempRemove = CommonConstants.COMMON_YES;
          copyRcj.changeResQty = copyRcj.resQty;
          copyRcj.resQty = 0;
        }
        this.resourceDomain.createResource(unitId,copyRcj.deRowId, copyRcj);
        let rcjNameObj = {};
        rcjNameObj.sequenceNbr = copyRcj.sequenceNbr;
        rcjNameObj.initMaterialName = copyRcj.materialName;
        initDeRcjNameList.push(rcjNameObj);
      }
      deModel.initDeRcjNameList = initDeRcjNameList;
    }
    //处理标准换算    
    let deConversion = localDe.conversion ;
    if(ObjectUtil.isNotEmpty(deConversion)){
      let copyedConversion = ConvertUtil.deepCopy(deConversion);
      copyedConversion.deRowId = deModel.sequenceNbr;
      copyedConversion.sequenceNbr = deModel.sequenceNbr;
      copyedConversion.unitId = unitId;
      unitConversion[deModel.sequenceNbr] = copyedConversion;
    }
    let unitProject =  this.ctx.treeProject.getNodeById(deModel.unitId);
    let oUnitProject =  this.ctx.treeProject.getNodeById(localDe.unitId);
     //取费专业类型不同时处理  
     if(unitProject.constructMajorType != oUnitProject.constructMajorType){
      let libraryMap = new Map();
      let baseDeLibraryModel2 = await service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(oUnitProject.constructMajorType);
      libraryMap.set(oUnitProject.constructMajorType,baseDeLibraryModel2);
    
      //颠倒displayLable类型 仅 父级 反转  ，其他子级不需要反转啊
      await this._reverseDisplayType(deModel,oUnitProject,service,libraryMap,false);
    }
    //处理子级定额
    if(ObjectUtils.isNotEmpty(localDe.children) &&  localDe.children.length > 0){
      let childIds = [];
      for(let child of localDe.children){        
        childIds.push(child.sequenceNbr);
      }
      
      await this.pasteDe(unitId,localDe.unitId,childIds, deModel,'childNoCircle',false);
      
    }else{
      //通知
      this.notify(deModel,true);
      DeTypeCheckUtil.checkAndUpdateDeType(deModel,this.ctx);
      try {
         
        await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
          constructId: deModel.constructId,
          unitId: deModel.unitId,
          constructMajorType: deModel.libraryCode
        });   
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }
    return deModel;
  }

  async appendBaseDeByLocal(constructId, unitId, localDe, deRowId,checkType=false) {
    let localDeCopy = ConvertUtil.deepCopy(localDe);//不要污染原始数据
    let rcjList = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,localDe.sequenceNbr);
    localDeCopy.rcjList = rcjList;
    let deConversion = await this.functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
    let unitConversion = deConversion[localDeCopy.unitId];
    localDeCopy.conversion = unitConversion[localDe.sequenceNbr];

    return await this.appendDeByUnitDe(localDeCopy,unitId,deRowId,unitConversion,false);
  }


  async appendBaseDeByLocalAZ(constructId, unitId, localDe, deRowId) {
    let {service} = EE.app;
    let deRowModel = await this.appendBaseDeByLocal(constructId, unitId, localDe, deRowId, false);
    deRowModel.azCalculateSelectDe = true;
    deRowModel.resQty = 1;
    deRowModel.quantity = 1;

    await service.PreliminaryEstimate.gsAZservice.deleteAzRcjRepeatAddRcj(constructId, unitId, deRowId);
    return deRowModel;
  }

  /**
   * 给之前创建好的定额行关联基础定额,这样形成完整的工程定额
   * @param constructId
   * @param unitId
   * @param deStandardId
   * @param deRowId
   */
  async appendBaseDe(constructId, unitId, deStandardId, deRowId,checkType=false) {

    let {service} = EE.app;
    let deModel = this.getDeById(deRowId);
    deModel.quantity = 0;
    deModel.resQty = 0;
    deModel.totalNumber = 0;
    deModel.price = 0;

    let baseDe = await service.PreliminaryEstimate.gsBaseDeService.getDeAndRcj(deStandardId);
    this. processMainMaterial (constructId,deRowId ,baseDe);
    await this._appendBaseDeByDe(constructId, unitId, baseDe, deModel, checkType, service);
    return deModel;
  }

  processMainMaterial (constructId,deRowId ,baseDe){
    let businessMap = this.functionDataMap;
    let deMainMaterial = businessMap.get(FunctionTypeConstants.UNIT_DE_MAINMATERIAL);
    if(ObjectUtils.isEmpty(deMainMaterial)){
      deMainMaterial = new Map();
      businessMap.set(FunctionTypeConstants.UNIT_DE_MAINMATERIAL, deMainMaterial);
    }
    let  updateFlag =false;
    let   mainMaterialRcjs=deMainMaterial.get( deRowId );
    if(ObjectUtils.isNotEmpty(baseDe) && ObjectUtils.isNotEmpty(baseDe.deRcjRelationList) && ObjectUtils.isNotEmpty(mainMaterialRcjs)){
      for (let i = 0; i <baseDe.deRcjRelationList.length; i++) {
        let  relationRcj = baseDe.deRcjRelationList[i];
        let  rcj = baseDe.rcjList[i];
        let mainMaterialRcj = mainMaterialRcjs.find(item=>item.materialCode ===relationRcj.materialCode);
        if(ObjectUtils.isNotEmpty(mainMaterialRcj)){
          relationRcj.materialCode = mainMaterialRcj.materialCode;
          relationRcj.materialName = mainMaterialRcj.materialName;
          relationRcj.resQty = mainMaterialRcj.resQty;
          rcj.specification = mainMaterialRcj.specification;
          rcj.unit = mainMaterialRcj.unit;
          rcj.price = mainMaterialRcj.marketPrice;
          updateFlag= true ;
        }
      }
    }
    if(updateFlag===true){
      deMainMaterial.delete(deRowId);
    }
  }

  async _appendBaseDeByDe(constructId, unitId, baseDe, deModel, checkType, service){
    if(ObjectUtil.isNotEmpty(baseDe) && ObjectUtil.isNotEmpty( deModel))
      {
        this.removeRowRelatedDatas(deModel);
      }
      await this.attachBaseDeProperty(baseDe, deModel);
      //appendBaseDe 初始化
      deModel.standardDeId = baseDe.standardDeId;
      deModel.isAppendBaseDe = true;
      await service.PreliminaryEstimate.gsInitDeService.init(deModel);

      await this.attachSubDe(baseDe, deModel);

      //处理展开/折叠标识
      deModel.displaySign = BranchProjectDisplayConstant.noSign;
      if (ObjectUtil.isNotEmpty(deModel.children)) {
        deModel.displaySign = BranchProjectDisplayConstant.open;
      }

      //处理借换标识
      //取费专业不同，显示类型为借，随机更新父级标记
      let unitProject = await service.PreliminaryEstimate.gsProjectCommonService.getUnit(deModel.constructId, deModel.unitId);
      let changeCostFlag = unitProject.constructMajorType !== baseDe.libraryCode;
      if(changeCostFlag){
        deModel.remark = baseDe.libraryName;//费用专业不同，备注啊
        deModel.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL;
        //借的子级为借
        if(deModel.type === DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(deModel.children)){
          deModel.children.forEach(item=>{
            item.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL
            item.remark =  baseDe.libraryName;
            item.classiflevel1 = deModel.classiflevel1;///子级专业同父级
          });
        }
      }else{
        deModel.remark = baseDe.libraryName;//费用专业不管相同与否都要备注啊
        if(deModel.type === DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(deModel.children)){
          deModel.children.forEach(item=>{
            item.remark =  baseDe.libraryName;
            item.classiflevel1 = deModel.classiflevel1;///子级专业同父级
          });
        }
      }
      
      if (!ObjectUtil.isEmpty(baseDe.deRcjRelationList) && !ObjectUtil.isEmpty(baseDe.rcjList)) {
        //处理人材机
        await this.attachDeRCJ(baseDe.deRcjRelationList, deModel, baseDe.rcjList);
      };
      if(ObjectUtil.isNotEmpty(baseDe.compensation)){
        this._createDeTcResource(deModel,baseDe)
      }

      deModel.updateValue(deModel);
  
      let parent = this.getDeById(deModel.parentId);
      if(checkType){
        DeTypeCheckUtil.updateParentDeType(deModel,this.ctx);
      }

      if(parent.isTempRemove === CommonConstants.COMMON_YES 
        || deModel.isTempRemove === CommonConstants.COMMON_YES){
          if(deModel.changeQuantity){
            deModel.originalQuantity = deModel.changeQuantity;//保留旧的原始工程量
          }
          deModel.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
          await this.tempRemoveDeRow(deModel.sequenceNbr);
      }else{
        if(deModel.type === DeTypeConstants.DE_TYPE_DE && parent.type !== DeTypeConstants.DE_TYPE_DEFAULT) {
          await this.notify({constructId, unitId, deRowId :deModel.parentId}, true);
        }
        else
        {
          await this.notify({constructId, unitId, deRowId : deModel.sequenceNbr}, true);
        }
  
      }
      try {
        await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          unitId: unitId,
          constructMajorType: deModel.libraryCode
        });
        //联动计算装饰超高人材机数量单价
        await service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
      } catch (error) {
        console.error("捕获到异常:", error);
      }
  }


  /**
   * 给之前创建好的定额行关联基础定额,这样形成完整的工程定额
   * @param constructId
   * @param unitId
   * @param deStandardId
   * @param deRowId
   */
  async appendBaseDeAz(constructId, unitId, deStandardId, deRowId, checkType = false) {

    let {service} = EE.app;
    let deModel = this.getDeById(deRowId);
    deModel.quantity = 0;
    deModel.resQty = 0;

    let baseDe = this.getDeById(deStandardId);

    //处理借换标识
    //取费专业不同，显示类型为借，随机更新父级标记
    let unitProject = await service.PreliminaryEstimate.gsProjectCommonService.getUnit(deModel.constructId, deModel.unitId);
    let changeCostFlag = unitProject.constructMajorType !== baseDe.libraryCode;
    if (changeCostFlag) {
      deModel.remark = baseDe.libraryName;//费用专业不同，备注啊
      deModel.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL;
      //借的子级为借
      if (deModel.type === DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(deModel.children)) {
        deModel.children.forEach(item => {
          item.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL
          item.remark = baseDe.libraryName;
        });
      }
    }


    //计取定额的人材机
    let baseDeRcjList = await service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, baseDe.deRowId, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
    if (ObjectUtil.isNotEmpty(baseDeRcjList)) {
      //给当前添加人材机
      for (let item of baseDeRcjList) {
        let rcj = {};
        rcj.constructId = constructId;
        rcj.unitId = unitId;
        rcj.sequenceNbr = Snowflake.nextId();
        rcj.kind = item.kind;
        rcj.deRowId = deRowId;
        rcj.displaySign = item.displayType;
        rcj.isDeResource = item.isDeResource;
        rcj.dePrice = item.dePrice;
        rcj.marketPrice = item.marketPrice;
        rcj.unit = item.unit;
        rcj.levelMark = item.levelMark;
        rcj.type = item.type;
        rcj.parentId = deRowId;
        rcj.materialCode = item.materialCode;
        rcj.materialName = item.materialName;
        rcj.resQty = item.resQty;
        this.resourceDomain.createResource(unitId, deRowId, rcj);
      }
    }

    deModel.totalNumber = 0;
    deModel.price = baseDe.totalNumber;
    deModel.deCode = baseDe.deCode;
    deModel.deName = baseDe.deName;
    deModel.type = baseDe.type;
    deModel.unit = baseDe.unit;
    deModel.updateValue(deModel);

    let parent = this.getDeById(deModel.parentId);
    if (checkType) {
      DeTypeCheckUtil.updateParentDeType(deModel, this.ctx);
    }
    //本身或父级是临时删除，则需要临时删除处理
    if (parent.isTempRemove === CommonConstants.COMMON_YES
        || deModel.isTempRemove === CommonConstants.COMMON_YES) {
      if (deModel.changeQuantity) {
        deModel.originalQuantity = deModel.changeQuantity;//保留旧的原始工程量
      }
      deModel.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(deModel.sequenceNbr);
    } else {
      if (deModel.type === DeTypeConstants.DE_TYPE_DE && parent.type !== DeTypeConstants.DE_TYPE_DEFAULT) {
        // await this.notify({constructId, unitId, deRowId: deModel.parentId}, true);
      } else {
        // await this.notify({constructId, unitId, deRowId}, true);
      }
    }
    try {
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        constructMajorType: deModel.libraryCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return deModel;

  }
  _isRightKind(type,kind){
    if(type == '1' && kind == ResourceKindConstants.INT_TYPE_R){
      return true;
    }
    if(type == '3' && kind == ResourceKindConstants.INT_TYPE_J){
      return true;
    }
    if(type == '2' && (kind == ResourceKindConstants.INT_TYPE_C || kind == 6 || kind == 7 || kind == 8 || kind == 9 || kind == 10)){
      return true;
    }
    if(type == '4' && kind == ResourceKindConstants.INT_TYPE_SB){
      return true;
    }
    if(type == '5' && kind == ResourceKindConstants.INT_TYPE_ZC){
      return true;
    }
    return false;
  }
  _getRCJPrice(deRow,type){
    if(type == '1'){
      return deRow.RSum;
    }
    if(type == '3'){
      return deRow.JSum;
    }
    if(type == '2'){
      return deRow.CSum;
    }
    if(type == '4'){
      return deRow.SSum;
    }
    if(type == '5'){
      return deRow.ZSum;
    }
    return 0;
  }
  async updateRCJPrice(constructId, unitId, deRow,price,type){
    let {service} = EE.app;

    let precision = this.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);

    //删除调整数据
    let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
    let tzCode = DeCommonConstants.getTZCode(type);
    rcjs.forEach(item =>{        
      if(item.materialCode == tzCode){
        let deleteKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr,item.sequenceNbr);
        this.ctx.resourceMap.removeByPattern(deleteKey);
      }        
    });

    let oldDeRow = deRow;
    //计算原始数据金额
    if(deRow.resourceTZ === CommonConstants.COMMON_YES){
      //计算不包主材设备就调整材料的单价， 
      oldDeRow = await DePriceCalculator.getInstance({constructId, unitId,deRowId:deRow.sequenceNbr},this.ctx).analyze();
    }
    let resQty =  price - this._getRCJPrice(oldDeRow,type);
    resQty = NumberUtil.numberScale(resQty,precision.DETAIL.RCJ.resQty);
    let differenceResource = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr,type);
    differenceResource.unit = "元";
    differenceResource.type = RcjTypeEnum['TYPE'+type].desc;
    differenceResource.kind = RcjTypeEnum['TYPE'+type].code;
    this.initResourceByUserDe(differenceResource,DeCommonConstants.getTZCode(type), DeCommonConstants.getTZName(type), 1,resQty,deRow);
    if(resQty != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResource);
    }
    if(differenceResource.kind ==  ResourceKindConstants.INT_TYPE_SB || differenceResource.kind ==  ResourceKindConstants.INT_TYPE_ZC){
      deRow.isExistedZcSb =  CommonConstants.COMMON_YES;
    }
    //更新标识
    DeTypeCheckUtil.checkAndUpdateDeType(deRow, this.ctx);
    await this.notify(deRow, false);//单价修改不影响工程量
    deRow.resourceTZ = CommonConstants.COMMON_YES;
    //通知费用汇总
    try{
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        constructMajorType: deRow.libraryCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 单价修改按比例分配
   * @param {*} constructId 
   * @param {*} unitId 
   * @param {*} deRowId 
   * @param {*} price 
   * @param {*} rSum 
   * @param {*} cSum 
   * @param {*} jSum 
   * @returns 
   */
  async updatePriceWithRCJ(constructId, unitId, deRowId,price, rSum,cSum,jSum) {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if(ObjectUtils.isEmpty(deRow)){
      return;
    }
    //用户定额的单价修改处理不一样，先删除人材机，然后增加即可
    if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE){
      //1.先删除人材机
      let rcjDeKey = WildcardMap.generateKey(unitId, deRowId) + WildcardMap.WILDCARD;
      let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
      rcjs.forEach(item =>{        
        
          let deleteKey = WildcardMap.generateKey(unitId, deRowId,item.sequenceNbr);
          this.ctx.resourceMap.removeByPattern(deleteKey);
      });
      //2. 然后新增
      let newRcjLists = this.createUserDeResource({rfee:rSum,cfee:cSum,jfee:jSum},deRow);
      let constructRcjArray = service.PreliminaryEstimate.gsRcjService.getAllRcj({constructId,unitId});
      for(let newRcj of newRcjLists){
        //新增补充人材机放入缓存
        this._addUserDeRcj2Map(newRcj);
        //处理补充人材机的编码处理
        await service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(newRcj,true,constructRcjArray);
      } 
    }else{
      //调整公共处理
      this._updatePriceCommon(constructId, unitId,deRow,price,rSum,cSum,jSum);
    }
    //更新标识
    DeTypeCheckUtil.checkAndUpdateDeType(deRow, this.ctx);
    await this.notify(deRow, false);//单价修改不影响工程量

    //通知费用汇总
    try{
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        constructMajorType: deRow.libraryCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }

  /**
   * 修改定额单价
   * @param {*} constructId 
   * @param {*} unitId 
   * @param {*} deRowId 
   * @param {*} price 
   * @param {*} isTc 
   * @returns 
   */
  async updatePrice(constructId, unitId, deRowId, price, isTc) {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if (ObjectUtil.isEmpty(deRow.price)) {
      throw Error("输入单价不能为空.");
    }

    let project =  this.ctx.treeProject.getNodeById(constructId);
    let pricingMethodFlag = project.pricingMethod == 1;//按市场价
    let precision = this.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
    price = NumberUtil.numberFormat(price,precision.EDIT.DE.price);//定额保留两位，注意了啊
    let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
    if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
      if(pricingMethodFlag){
        //rcjs[0].value.price = price; 不知道何用
        deRow.price = price;
      }else{
        throw Error("定额价不能修改");
      }
      rcjs[0].value.marketPrice = price;//
      DeTypeCheckUtil.updateParentDeType(deRow, this.ctx);
      let rcjlist = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      let rcjDetail = rcjlist.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES).value;
      rcjDetail.marketPrice = price;
      await service.PreliminaryEstimate.gsRcjService.updateRcjDetail({constructId, singleId:null, unitId
        , deId:deRow.sequenceNbr
        , rcjDetailId:rcjDetail.sequenceNbr
        , constructRcj:{
          marketPrice : rcjDetail.marketPrice
        }});
      await this.notify(deRow, false); //单价修改不影响工程量
      return ;
    }

    let deleteTZList = [];
    for(let item of rcjs){
      //删除调整数据
      if(item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
        || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
        || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ){
          
          deleteTZList.push(item);

          let resourceKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr,item.sequenceNbr);
          this.ctx.resourceMap.removeByPattern(resourceKey);
      }
    }
    
    if(deRow.resourceTZ === CommonConstants.COMMON_YES || isTc){
      //计算不包主材设备就调整材料的单价， 
      if(deRow.type === DeTypeConstants.DE_TYPE_DELIST){
        await QDCalculator.getInstance({constructId, unitId,deRowId},this.ctx).analyze();
      }else{
        await DeCalculator.getInstance({constructId, unitId,deRowId},this.ctx).analyze();
      }
    }
    let oldDeRow = deRow;
    let oldPrice = oldDeRow.baseJournalPrice;
    if(pricingMethodFlag){
      oldPrice = oldDeRow.price;
    }
    let resQtyR = null;
    let resQtyC = null;
    let resQtyJ = null;
    if(oldPrice == 0 && deleteTZList.length > 0){
      let difference = price - oldPrice;
  
      // 差额乘以比例 算出来的是消耗量  人 单位 为：人工 机械 为 ：台班 机械？
      let percentR = ObjectUtil.isEmpty(deRow.RDSum) || deRow.RDSum == 0 ?  0 : deRow.RDSum / oldPrice;
      if(pricingMethodFlag){
        percentR = ObjectUtil.isEmpty(deRow.RSum) || deRow.RSum == 0 ?  0 : deRow.RSum / oldPrice;
      }
      let percentC = ObjectUtil.isEmpty(deRow.CDSum) || deRow.CDSum == 0 ?  0 : deRow.CDSum / oldPrice ;
      if(pricingMethodFlag){
        percentC = ObjectUtil.isEmpty(deRow.CSum) || deRow.CSum == 0 ?  0 : deRow.CSum /oldPrice;
      }
      // let percentJ = ObjectUtil.isEmpty(deRow.JSum) || deRow.JSum == 0 ?  0 : deRow.JSum / deRow.price ;
      resQtyR = 0;
      resQtyC = 0;
      resQtyJ = 0;
      for(let item of deleteTZList){
        if(item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ){
          resQtyR += (percentR * difference+item.resQty);
        }
        if(item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ){
          resQtyC += (percentC * difference+item.resQty);
        }
        if(item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ){
          resQtyJ += item.resQty;
        }   
      }
      resQtyR = NumberUtil.numberFormat(resQtyR,precision.DETAIL.RCJ.resQty);
      resQtyC = NumberUtil.numberFormat(resQtyC,precision.DETAIL.RCJ.resQty);
      resQtyJ = NumberUtil.numberFormat(NumberUtil.subtract(price,NumberUtil.add(resQtyR,resQtyC)),precision.DETAIL.RCJ.resQty);

    }else{
      if (ObjectUtil.isEmpty(oldPrice) || oldPrice == 0) {
        throw Error("定额下没有人材机无法调整");
      }

      let difference = price - oldPrice;
      //子级定额按照定额人工费单价、材料费单价、机械费单价比例计算，因为子级定额级别的人工费单价即是人材机比例
      //父级定额的人工费单价是通过子级定额计算而来，因此需要查询父级定额本身自己的人材机数据去计算
      if (ObjectUtil.isEmpty(deRow.children)) {
        // 差额乘以比例 算出来的是消耗量  人 单位 为：人工 机械 为 ：台班 机械？
        //update 20240808 ye 保证原始比例不变
        let percentR = ObjectUtil.isEmpty(oldDeRow.RSum) || oldDeRow.RSum == 0 ? 0 : oldDeRow.RSum / oldDeRow.price;//调整了price
        if (!pricingMethodFlag) {
          percentR = ObjectUtil.isEmpty(oldDeRow.RDSum) || oldDeRow.RDSum == 0 ? 0 : oldDeRow.RDSum / oldDeRow.baseJournalPrice;
        }
        let percentC = ObjectUtil.isEmpty(oldDeRow.CSum) || oldDeRow.CSum == 0 ? 0 : oldDeRow.CSum / oldDeRow.price;
        if (!pricingMethodFlag) {
          percentC = ObjectUtil.isEmpty(oldDeRow.CDSum) || oldDeRow.CDSum == 0 ? 0 : oldDeRow.CDSum / oldDeRow.baseJournalPrice;
        }
        // let percentJ = ObjectUtil.isEmpty(oldDeRow.JSum) || oldDeRow.JSum == 0 ?  0 : oldDeRow.JSum / oldDeRow.price ;
        resQtyR = NumberUtil.numberFormat(NumberUtil.multiply(percentR, difference), precision.DETAIL.RCJ.resQty);
        resQtyC = NumberUtil.numberFormat(NumberUtil.multiply(percentC, difference), precision.DETAIL.RCJ.resQty);
        resQtyJ = NumberUtil.numberFormat(NumberUtil.subtract(difference, NumberUtil.add(resQtyR, resQtyC)), precision.DETAIL.RCJ.resQty);
      } else {
        let resQtyR1 = 0;
        let resQtyC1 = 0;
        let resQtyJ1 = 0;
        for (let item1 of rcjs) {
          if (item1.type === "人工费") {
            resQtyR1 = NumberUtil.add(resQtyR1, item1.resQty);
          }
          if (item1.type === "材料费") {
            resQtyC1 = NumberUtil.add(resQtyC1, item1.resQty);
          }
          if (item1.type === "机械费") {
            resQtyJ1 = NumberUtil.add(resQtyJ1, item1.resQty);
          }
        }
        let resQtyJ111 = NumberUtil.addParams(resQtyR1, resQtyC1, resQtyJ1);
        if (resQtyJ111 !== 0) {
          let percentR = resQtyR1 / resQtyJ111;//调整了price
          let percentC = resQtyC1 / resQtyJ111;
          resQtyR = NumberUtil.numberFormat(NumberUtil.multiply(percentR, difference), precision.DETAIL.RCJ.resQty);
          resQtyC = NumberUtil.numberFormat(NumberUtil.multiply(percentC, difference), precision.DETAIL.RCJ.resQty);
          if (resQtyJ1 === 0) {
            resQtyC = NumberUtil.numberFormat(NumberUtil.subtract(difference, resQtyR), precision.DETAIL.RCJ.resQty);
          }
          resQtyJ = NumberUtil.numberFormat(NumberUtil.subtract(difference, NumberUtil.add(resQtyR, resQtyC)), precision.DETAIL.RCJ.resQty);
        } else {
          let percentR = ObjectUtil.isEmpty(oldDeRow.RSum) || oldDeRow.RSum == 0 ? 0 : oldDeRow.RSum / oldDeRow.price;//调整了price
          if (!pricingMethodFlag) {
            percentR = ObjectUtil.isEmpty(oldDeRow.RDSum) || oldDeRow.RDSum == 0 ? 0 : oldDeRow.RDSum / oldDeRow.baseJournalPrice;
          }
          let percentC = ObjectUtil.isEmpty(oldDeRow.CSum) || oldDeRow.CSum == 0 ? 0 : oldDeRow.CSum / oldDeRow.price;
          if (!pricingMethodFlag) {
            percentC = ObjectUtil.isEmpty(oldDeRow.CDSum) || oldDeRow.CDSum == 0 ? 0 : oldDeRow.CDSum / oldDeRow.baseJournalPrice;
          }
          // let percentJ = ObjectUtil.isEmpty(oldDeRow.JSum) || oldDeRow.JSum == 0 ?  0 : oldDeRow.JSum / oldDeRow.price ;
          resQtyR = NumberUtil.numberFormat(NumberUtil.multiply(percentR, difference), precision.DETAIL.RCJ.resQty);
          resQtyC = NumberUtil.numberFormat(NumberUtil.multiply(percentC, difference), precision.DETAIL.RCJ.resQty);
          resQtyJ = NumberUtil.numberFormat(NumberUtil.subtract(difference, NumberUtil.add(resQtyR, resQtyC)), precision.DETAIL.RCJ.resQty);
        }
      }
    }
    //调整公共处理
    this._updatePriceCommon(constructId, unitId,deRow,price,resQtyR,resQtyC,resQtyJ);
    //更新标识
    DeTypeCheckUtil.checkAndUpdateDeType(deRow, this.ctx);
    await this.notify(deRow, false);//单价修改不影响工程量
    let newSum = NumberUtil.addParams(deRow.RDSum,deRow.CDSum,deRow.JDSum);
    if(pricingMethodFlag){
      newSum = NumberUtil.addParams(deRow.RSum,deRow.CSum,deRow.JSum);
    }
    if(newSum !== price){
      let newRcjs = this.ctx.resourceMap.getValues(rcjDeKey);
      let jxTzRcj = newRcjs.find(item=>item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ);
      if(ObjectUtil.isEmpty(jxTzRcj)){
        jxTzRcj = newRcjs.find(item=>item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ);
      }
      if(ObjectUtil.isNotEmpty(jxTzRcj)){
        let newResQty = NumberUtil.add(jxTzRcj.resQty,NumberUtil.subtract(price,newSum));
        jxTzRcj.originalQty = newResQty;
        await service.PreliminaryEstimate.gsRcjService.updateRcjDetail({constructId, singleId:null, unitId
        , deId:deRow.sequenceNbr
        , rcjDetailId:jxTzRcj.sequenceNbr
        , constructRcj:{
          resQty : newResQty,
          zcTzRcj : true
        }});
      }
    }

    //通知费用汇总
    try{
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        constructMajorType: deRow.libraryCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  _updatePriceCommon(constructId, unitId,deRow,price,resQtyR,resQtyC,resQtyJ){
    let {service} = EE.app;
    let differenceResourceR = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_R);
    differenceResourceR.unit = "元";
    let differenceResourceC = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_C);
    differenceResourceC.unit = "元";
    let differenceResourceJ = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_J);
    differenceResourceJ.unit = "元";
    
    differenceResourceR.type = RcjTypeEnum['TYPE1'].desc;
    differenceResourceC.type = RcjTypeEnum['TYPE2'].desc;
    differenceResourceJ.type = RcjTypeEnum['TYPE3'].desc;
    differenceResourceR.kind = RcjTypeEnum['TYPE1'].code;
    differenceResourceC.kind = RcjTypeEnum['TYPE2'].code;
    differenceResourceJ.kind = RcjTypeEnum['TYPE3'].code;
    this.initResourceByUserDe(differenceResourceR, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ_LABEL, 1,resQtyR,deRow);
    this.initResourceByUserDe(differenceResourceC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ_LABEL, 1,resQtyC,deRow);
    this.initResourceByUserDe(differenceResourceJ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ_LABEL, 1,resQtyJ,deRow);
    if(resQtyR != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceR);
    }
    if(resQtyC != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceC);
    }
    if(resQtyJ != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceJ);
    }
    let tcRcjs = []
    tcRcjs.push(differenceResourceR)
    tcRcjs.push(differenceResourceC)
    tcRcjs.push(differenceResourceJ)
    service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, differenceResourceJ, 'addMergeTz', null, null, {tcRcjs});
    if(resQtyR === 0 && resQtyC === 0 && resQtyJ === 0){
      //删除
      // let tzItem = originalQtyMap?.get(differenceResourceJ.materialCode)
      // if(ObjectUtils.isNotEmpty(tzItem)){
      // }
      // service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, tcRcjs, 'del', null, null);
      // service.PreliminaryEstimate.gsConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, null, 'addMergeTz', null, null);
    }
    //子级定额直接修改单价，父级定额修改完加上原来单价
    if (ObjectUtil.isNotEmpty(deRow.children)) {
      deRow.price = NumberUtil.add(deRow.price, price);
    } else {
      deRow.price = price;
    }
    deRow.resourceTZ = CommonConstants.COMMON_YES;//有过单价 调整的标识
  }

  async updateUnit(constructId, unitId, deRowId, unit)
  {

    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    try {

      let oUnit = UnitUtils.removeCharter(deRow.unit);
      if(ObjectUtil.isEmpty(oUnit))
      {
        oUnit = 1;
      }
      let nUnit = UnitUtils.removeCharter(unit);
      if(ObjectUtil.isEmpty(nUnit))
      {
        nUnit = 1;
      }

      deRow.unit = unit;
      let isChangeQuantity = false;
      if(oUnit != nUnit)
      {//当系数不一样时,需要计算
        isChangeQuantity = true;
      }
      if(isChangeQuantity){
        let coefficient = NumberUtil.numberScale(oUnit/nUnit,5);
        let parent = this.getDeById(deRow.parentId);
        let parentQuantity = parent.quantity;
        let precision = this.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
        let digital = DeUtils.getQuantiyPrecision(precision,deRow);
        if(ObjectUtil.isEmpty(parentQuantity) || parentQuantity === 0)
        {
           parentQuantity = deRow.quantity;
           deRow.quantity = NumberUtil.numberFormat(NumberUtil.multiply(parentQuantity,coefficient) , digital);
        }
        else
        {
          //原始工程量及消耗量重新计算
          let resQty = NumberUtil.multiply(NumberUtil.numberFormat(deRow.resQty,precision.EDIT.DE.resQty), coefficient);
          deRow.quantity = NumberUtil.numberFormat(NumberUtil.multiply(resQty,parentQuantity), digital);
        }
        //表达式及原始工程量变化
        deRow.quantityExpression = deRow.originalQuantity = NumberUtil.multiply(deRow.quantity , nUnit);
        //修改
        await this.notify({ constructId, unitId, deRowId },true);
        //单位变化引起消耗量变换，消耗量变换必定一起父级单价变换，此时切换类型
        DeTypeCheckUtil.updateParentDeType(deRow,this.ctx);
      }

      //人材机定额单位变化引起编码变化
      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE
        ||deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
      ){
        let rcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
        let rcjDetail = rcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES).value;
        rcjDetail.unit = unit;
        await service.PreliminaryEstimate.gsRcjService.updateRcjDetail({constructId, singleId:null, unitId
          , deId:deRow.sequenceNbr
          , rcjDetailId:rcjDetail.sequenceNbr
          , constructRcj:{
            unit:rcjDetail.unit
        }});
      }
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        constructMajorType: deRow.libraryCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return PropertyUtil.filterObjectProperties(deRow, BaseDomain.avoidProperty);

  }

  createDeTcResourceForConversion(constructId, unitId,deRow,resQtyR,resQtyC,resQtyJ) {
    let differenceResourceR = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_R);
    differenceResourceR.unit = "元";
    let differenceResourceC = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_C);
    differenceResourceC.unit = "元";
    let differenceResourceJ = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_J);
    differenceResourceJ.unit = "元";

    differenceResourceR.type = RcjTypeEnum['TYPE1'].desc;
    differenceResourceC.type = RcjTypeEnum['TYPE2'].desc;
    differenceResourceJ.type = RcjTypeEnum['TYPE3'].desc;
    differenceResourceR.kind = RcjTypeEnum['TYPE1'].code;
    differenceResourceC.kind = RcjTypeEnum['TYPE2'].code;
    differenceResourceJ.kind = RcjTypeEnum['TYPE3'].code;
    this.initResourceByUserDe(differenceResourceR, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ_LABEL, 1,resQtyR,deRow);
    this.initResourceByUserDe(differenceResourceC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ_LABEL, 1,resQtyC,deRow);
    this.initResourceByUserDe(differenceResourceJ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ_LABEL, 1,resQtyJ,deRow);
    if(resQtyR != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceR);
    }
    if(resQtyC != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceC);
    }
    if(resQtyJ != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceJ);
    }

    return {
      differenceResourceR,
      differenceResourceC,
      differenceResourceJ
    }
  }

  /**
   * 重新计算所有数据
   * constructId 项目id
   * unitId   单位id
   * allNodes 该单位下所有的定额
   */
  async notifyAll(constructId,unitId,allNodes){

    let deArrs = allNodes.filter(item=>![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DELIST].includes(item.type));
    for(let deRow of deArrs){
      let dc = DeCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId:deRow.sequenceNbr},this.ctx);
      await dc.analyze();
    }
    let qdIdList = allNodes.filter(item=>DeTypeConstants.DE_TYPE_DELIST == item.type);
    for(let qdRow of qdIdList){
      let qc = QDCalculator.getInstance({constructId: constructId, unitId: unitId,deRowId:qdRow.sequenceNbr}, this.ctx);
      await qc.analyze();
    }
    let rootRows = allNodes.filter(item=>DeTypeConstants.DE_TYPE_DEFAULT == item.type);
    for(let rootRow of rootRows){
      let fc = FBCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId: rootRow.sequenceNbr},this.ctx);
      await fc.analyze();
    }
  }

  /**
   * 更新取费装专业
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param costFileCode
   * @param costMajorName
   * @returns {Promise<void>}
   */
  async updateChargingDiscipline(constructId, unitId, deRowId, costFileCode,costMajorName)
  {
    
    let {service} = EE.app; 
    let costFileCodeSet = new Set();
    costFileCodeSet.add(costFileCode);
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if(ObjectUtil.isNotEmpty(deRow))
    {
      let childRowList = [deRow];
      this.findChilds(deRow, childRowList, [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_RESOURCE]);
      for (let child of childRowList)
      {
        costFileCodeSet.add(child.costFileCode);
        child.costFileCode = costFileCode;
        child.costMajorName = costMajorName;
        await this.updateDe(child);
      }
      if(costFileCodeSet.size > 1){
        try {
          for(let item of costFileCodeSet) {
            await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
              constructId: constructId,
              unitId: unitId,
              constructMajorType: item
            });
          }
        } catch (error) {
          console.error("捕获到异常:", error);
        }
      }
      return deRow;
    }
    else{
      throw Error("未找到定额行.");
    }

  }

  /**
   * 递归查询子节点 并可通过类型过滤 返回找到的row 数组.
   * @param deRow
   * @param childRowList
   * @param types
   */
  findChilds(deRow,childRowList,types)
  {
    if(ObjectUtil.isNotEmpty(deRow) && ObjectUtil.isNotEmpty(deRow.children))
    {
      for (let child of deRow.children)
      {
        if(types.includes(child.type))
        {
          childRowList.push(child);
          this.findChilds(child,childRowList,types);
        }
      }

    }
  }

  /**
   * 修改消耗量
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param resQty
   * @returns {Promise<*>}
   */
  async updateResQty(constructId, unitId, deRowId, resQty)
  {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if(ObjectUtil.isNotEmpty(deRow) && ObjectUtil.isNotEmpty(resQty) 
      && deRow.resQty !== resQty)
    {

      let precision = this.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
      
      let calResQty = eval(resQty);//已验证无需处理
      deRow.inputResQty = resQty;      //输入值计算，有可能是表达式
      deRow.resQty = NumberUtil.numberFormat(calResQty,precision.EDIT.DE.resQty);

      let parentDeRow = this.ctx.deMap.getNodeById(deRow.parentId);
      if (ObjectUtil.isNotEmpty(parentDeRow) && ObjectUtil.isNotEmpty(parentDeRow.quantity) && parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST )
      {
        deRow.quantity = NumberUtil.multiply(parentDeRow.quantity, deRow.resQty);
      }
      await  this.notify({ constructId, unitId, deRowId:deRow.parentId },true);
      //消耗量变换影响父级标识
      if( ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST){
        DeTypeCheckUtil.updateParentDeType(deRow,this.ctx);
      }
      try {
        await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          unitId: unitId,
          constructMajorType: deRow.libraryCode
        });

        //联动计算装饰超高人材机数量
        await service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
        //联动计取安装费
        await service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "delete");
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }
    else {
      throw Error("输入值不能为空");
    }
    return PropertyUtil.filterObjectProperties(deRow, BaseDomain.avoidProperty);
  }

  /**
   *
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param quantity
   * @returns {Promise<{}>}
   */
  async updateQuantity(constructId, unitId, deRowId, quantity,changeResQty = true,filterTempRemoveRow = true,resetDeQuantities = true,priceCodes=[], quantityExpression = 'QDL') {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if (!ObjectUtil.isEmpty(deRow)) {
      let  unitNbr = UnitUtils.removeCharter(deRow.unit);
      if(ObjectUtil.isEmpty(priceCodes)){
        let unitProject =  this.ctx.treeProject.getNodeById(unitId);  
        //校验quantity
        let codeArgs =  {
          constructId:constructId,
          type:"变量表",
          unitId:unitId,
          constructMajorType:unitProject.constructMajorType
        }
        priceCodes = await service.PreliminaryEstimate.gsUnitCostCodePriceService.costCodePrice(codeArgs);
      }
      
      deRow.quantityExpression = deRow.originalQuantity = quantity;
      if(quantity == 0){
        deRow.quantityExpression = '';
      }
      //增加定额费用代码
      await service.PreliminaryEstimate.gsDeService.addPriceCodes(constructId, unitId, deRowId, priceCodes);
      //这里无需trycatch，内部玩耍无错误，有问题请核查来源
      let resultQuantity = DeQualityUtils.evalQualityWithCodes(quantity,priceCodes);
      let precision = this.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
      let digital = DeUtils.getQuantiyPrecision(precision,deRow);
      if(ObjectUtil.isNotEmpty(unitNbr))
      {
        deRow.quantity = NumberUtil.numberFormat(NumberUtil.divide(resultQuantity,unitNbr),digital);
      }
      else {
        deRow.quantity =  NumberUtil.numberFormat(resultQuantity,digital);
      }
      //
      if(changeResQty){
        let parent = this.ctx.deMap.getNodeById(deRow.parentId);
        //父级工程量为0 子级也为0
        if( parent.quantity === 0 && parent.type === DeTypeConstants.DE_TYPE_DELIST){
          deRow.quantity = 0;
        }

        if (parent.quantity > 0 && parent.type !== DeTypeConstants.DE_TYPE_DEFAULT 
          && parent.type !== DeTypeConstants.DE_TYPE_FB 
          && parent.type !== DeTypeConstants.DE_TYPE_ZFB){
            deRow.resQty = NumberUtil.numberFormat(NumberUtil.divide(deRow.quantity,parent.quantity),precision.EDIT.DE.resQty);
            //消耗量保留3为后0,重置工程量为0
            if(deRow.resQty === 0){
               deRow.quantity = 0;
            }
        }
      
        DeTypeCheckUtil.checkAndUpdateDeType(deRow,this.ctx,true);
      }
      // deRow.quantityExpression = quantityExpression? quantityExpression : 'QDL';
      // if (ObjectUtils.isNotEmpty(deRow.children)) {
      //   for (let child of deRow.children) {
      //     child.quantityExpression = deRow.quantityExpression;
      //   }
      // }
      if(resetDeQuantities){
        await service.PreliminaryEstimate.gsInitDeService.initDeQuantities(deRow);
        // 获取定额下所有子级定额
        let childrens = await service.PreliminaryEstimate.gsDeService.getAllChildren(deRow);
        let des = []
        des.push(deRow)
        des = des.concat(childrens).filter(item => item.type === DeTypeConstants.DE_TYPE_DELIST || item.type === DeTypeConstants.DE_TYPE_DE)
        for (let de of des) {
          await service.PreliminaryEstimate.gsInitDeService.initDeQuantities(de);
        }
      }

      try {
        await  this.notify({ constructId, unitId, deRowId },true,filterTempRemoveRow,priceCodes);
        //联动计算装饰超高人材机数量
        await service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
        await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          unitId: unitId,
          constructMajorType: deRow.libraryCode
        });

        //联动计取安装费
        await service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "delete");
        //人材机数量变化，联动计算三材量并同步其他费
        await service.PreliminaryEstimate.gsRcjCollectService.updateOtherProjectScGJ(constructId);
      } catch (error) {
        console.error("捕获到异常:", error);
      }
      return PropertyUtil.filterObjectProperties(deRow, BaseDomain.avoidProperty);
    }
  }
  
  async notifyQuantity(deRowBak,reCalculateQuantity = true,filterTempRemoveRow=true,priceCodes=[]) {
    let {service} = EE.app;
    let deRow = this.getDeById(deRowBak.deRowId);
    if(ObjectUtils.isEmpty(deRow)){
      return;
    }
    let parentDeRow = this.getDeById(deRow.parentId);
    let parentDeFlag = false;
    if(parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST){
      parentDeFlag = true;
    }
     
    let curResultQuantity = 0;
    let  unitNbr = UnitUtils.removeCharter(deRow.unit);
    //增加定额费用代码
    await service.PreliminaryEstimate.gsDeService.addPriceCodes(deRow.constructId, deRow.unitId, deRow.sequenceNbr, priceCodes);
    //这里无需trycatch，内部玩耍无错误，有问题请核查来源
    let resultQuantity = DeQualityUtils.evalQualityWithCodes(deRow.originalQuantity,priceCodes);
    if(ObjectUtil.isNotEmpty(unitNbr))
    {
      curResultQuantity = NumberUtil.numberScale(NumberUtil.divide(resultQuantity,unitNbr),5);
    }else{
      curResultQuantity= resultQuantity;
    }
    //父级与自己计算是否相同
    if(parentDeFlag && parentDeRow.quantity == 0){
      deRow.quantity = 0;
    }
    if(parentDeFlag && parentDeRow.quantity > 0){
      let calResultQuantity = NumberUtil.numberScale(NumberUtil.divide(parentDeRow.quantity,deRow.resQty),5);
      if(calResultQuantity != curResultQuantity){
        deRow.resQty  = NumberUtil.numberScale(NumberUtil.divide(curResultQuantity,parentDeRow.quantity),5);
      }
      deRow.quantity  = curResultQuantity;
    }else{
      deRow.quantity  = curResultQuantity;
    }

    await this.notify(deRow,reCalculateQuantity,filterTempRemoveRow,priceCodes);
  }
  /**
   * 基于定额重新铺设工程量 人材机 定额的单价合价
   * @param deRow
   * @param reCalculateQuantity
   * @returns {Promise<void>}
   */
  async notify(deRow,reCalculateQuantity = true,filterTempRemoveRow=true,priceCodes=[]) {
    //第一步 重新计算该清单下所有子清单和定额的 工程量
    deRow = this.getDeById(deRow.deRowId);
    let parent = this.ctx.deMap.getNodeById(deRow.parentId);
    if(ObjectUtil.isEmpty(deRow)){
      throw Error("无法找到定额 : " + deRow.sequenceNbr + ".");
    }
    if (reCalculateQuantity) {
      if(ObjectUtils.isEmpty(priceCodes)){ //获取编码
        let {service} = EE.app;    
        let unitProject =  this.ctx.treeProject.getNodeById(deRow.unitId);    
        //校验quantity
        let codeArgs =  {
            constructId:deRow.constructId,
            type:"变量表",
            unitId:deRow.unitId,
            constructMajorType:unitProject.constructMajorType
        }
        priceCodes = service.PreliminaryEstimate.gsUnitCostCodePriceService.costCodePrice(codeArgs); 
      }
      let df = DeFlattener.getInstance(deRow, this.ctx,filterTempRemoveRow,priceCodes,this.functionDataMap);
      await df.analyze();
      //数组顺序为从下到上，人材机计算可以分开计算，但是定额计算需要从下到上
      //重新计算当前定额或者清单下所有人材机
      for(let relatedDeDeRow of df.relatedDeRows){
        //只有定额下有人才机明细需要重新计算 而清单不用
        if(ObjectUtil.isNotEmpty(relatedDeDeRow) && (relatedDeDeRow.type != DeTypeConstants.DE_TYPE_ZFB 
          && relatedDeDeRow.type != DeTypeConstants.DE_TYPE_FB 
          && relatedDeDeRow.type != DeTypeConstants.DE_TYPE_DEFAULT )) {
             //1.重新计算人材机
            let rc = ResourceCalculator.getInstance({constructId: relatedDeDeRow.constructId, unitId: relatedDeDeRow.unitId, deRowId:relatedDeDeRow.sequenceNbr}, this.ctx);
            await rc.analyze(); 
            //人材机修改notify于定额notify此时分开，互补影响
            // await this.resourceDomain.notify({constructId: relatedDeDeRow.constructId, unitId: relatedDeDeRow.unitId, deRowId}, this.ctx);
          }
      }
      let orderedDeRow = [];
      this._executeDeCalculator(deRow,orderedDeRow);
      for(let i=0;i<orderedDeRow.length;i++){
        if(orderedDeRow[i].type ===  DeTypeConstants.DE_TYPE_DELIST){
          let qc = QDCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId,deRowId:orderedDeRow[i].sequenceNbr}, this.ctx);
          await qc.analyze();
        }else{
          let dc = DeCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId:orderedDeRow[i].sequenceNbr},this.ctx);
          await dc.analyze();
        }
      }
      
    }
    else
    {
      let rc = ResourceCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId:deRow.sequenceNbr}, this.ctx);
      await rc.analyze();
      if(deRow.type === DeTypeConstants.DE_TYPE_DELIST){
        let qc = QDCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId,deRowId:deRow.sequenceNbr}, this.ctx);
        await qc.analyze();
      }else{
        let dc = DeCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId:deRow.sequenceNbr},this.ctx);
        await dc.analyze();
      }
    }
    
    if(ObjectUtil.isNotEmpty(parent) && parent.type === DeTypeConstants.DE_TYPE_DELIST)
    {
      let qdAllParentIds = DeUtils.findAllQdParendIds(parent.parentId,this.ctx);
      let qdIdList =[];
      qdIdList.push(parent.sequenceNbr);
      qdIdList = qdIdList.concat(qdAllParentIds);
      for(let qdId of qdIdList){
        let qc = QDCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId,deRowId:qdId}, this.ctx);
        await qc.analyze();
      }
    }
    let fc = FBCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId: deRow.parentId},this.ctx);
    await fc.analyze();
  }

  _executeDeCalculator(deRow,orderedDeRow){
      for(let child of deRow?.children){
        this._executeDeCalculator(child,orderedDeRow);
      }
      orderedDeRow.push(deRow);
  }
  /**
   * 处理定额基本信息
   * @param baseDe
   * @param deModel
   *
   */
  async attachBaseDeProperty(baseDe, deModel) {
    PropertyUtil.copyProperties(baseDe, deModel, DeDomain.baseDeToDeAvoidProperty);
    if(baseDe.isExistDe === CommonConstants.COMMON_YES)
    {
      deModel.type = DeTypeConstants.DE_TYPE_DELIST;
      deModel.displayType = DeTypeConstants.DE_TYPE_DELIST_LABEL;
      deModel.standardId = baseDe.sequenceNbr;

    }
    else
    {
      deModel.type = DeTypeConstants.DE_TYPE_DE;
      deModel.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
      deModel.standardId = baseDe.sequenceNbr;
    }
    deModel.initResQty = baseDe.resQty;//初始含量
    let parentDeRow = this.findFirstDeOrDeList(deModel);
    if(ObjectUtil.isNotEmpty(parentDeRow))
    {
      deModel.costFileCode = parentDeRow.costFileCode;
      deModel.costMajorName = parentDeRow.costMajorName;
    }else{
      
      //非概算定额  直接显示单位的取费专业，，gsinitdeservice init 中处理，此处不要跳过
      if(baseDe.libraryName.indexOf('概算定额') === -1){
        deModel.costFileCode = null;
      }else{
        deModel.costFileCode = baseDe.libraryCode;
        deModel.costMajorName = baseDe.projectType;
      }
    }
    //处理classiflevel1  专业   字段
    let classlevel01 = deModel.classlevel01?deModel.classlevel01.replace('工程','').replace('项目',''):'';
    deModel.classiflevel1 = this._removeChapterPrefix(classlevel01) +"-"+this._removeChapterPrefix(deModel.classlevel02);
    //处理  取费专业  
    await this.updateDe(deModel);
  }
  /**
   * 追加定额下的人材机
   * @param deRcjRelationList
   * @param deModel
   * @param rcjList
   * @param isBaseRCJ
   */
  async attachDeRCJ(deRcjRelationList, deModel,rcjList,isBaseRCJ, setResQty1 = false) {
    let {service} = EE.app;
    let childCodes = [];
    let childIds = [];
    let initDeRcjNameList = [];
    for (let rcj of deRcjRelationList) {
      //constructId,unitId,sequenceNbr,parentId,type
      let resource = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(), deModel.sequenceNbr, rcj.kind);
      PropertyUtil.copyProperties(rcj, resource, ['sequenceNbr']);
      resource.rcjId = rcj.rcjId;
      resource.deRowId = deModel.sequenceNbr;
      resource.parentId = deModel.sequenceNbr;
      resource.deId = deModel.sequenceNbr;
      resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
      resource.specification= rcj.specification;
      resource.producer = null;
      resource.manufactor = null;
      resource.brand = null;
      if(setResQty1){
        resource.resQty = 1;//添加人材机定额时定额人材机的消耗量应该为1
      }
      resource.deliveryLocation = null;
      resource.qualityGrade= null;
      resource.remark= null;
      resource.markSum = RcjCommonConstants.MARKSUM_JX;
      resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
      resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
      resource.libraryCode = rcj.libraryCode;
      resource.originalQty = rcj.resQty;
      resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
      resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
      resource.numLockNum = 0;
      resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
      if(ObjectUtil.isNotEmpty(isBaseRCJ) && ObjectUtil.isEmpty( resource.dePrice ))
      {
        resource.dePrice = rcj.price;
        resource.marketPrice = rcj.price;
        resource.levelMark = rcj.levelMark;
        resource.unit = rcj.unit;
      }
      if(ObjectUtil.isNotEmpty(rcjList))
      {
        let baseRcj = rcjList.find(item => item.sequenceNbr === rcj.rcjId);
        resource.dePrice = baseRcj.price;
        resource.marketPrice = baseRcj.price;
        resource.price=baseRcj.price;
        resource.unit = baseRcj.unit;
        resource.kindSc = baseRcj.kindSc;
        resource.transferFactor = baseRcj.transferFactor;
        resource.scCount = NumberUtil.multiply(resource.totalNumber, resource.transferFactor);
        resource.isFyrcj = baseRcj.isFyrcj ;
        if (ObjectUtil.isNotEmpty(baseRcj)) {
          resource.specification= baseRcj.specification;
          resource.levelMark = baseRcj.levelMark;
          rcj.levelMark =  resource.levelMark ;
        }
      }
      for (let key in RcjTypeEnum) {
        if (RcjTypeEnum[key].code == resource.kind) {
          resource.type =  RcjTypeEnum[key].desc;
        }
      }
      let childPbsCodes = [];
      if (!ObjectUtil.isEmpty(resource.levelMark) && resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL
          || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) {
        await this.attachPBs(rcj, deModel.constructId, deModel.unitId, deModel.sequenceNbr, resource.sequenceNbr);
        let subSourcePrice = '';
        for(let index = 0; index < rcj.pbs.length; index++) {
          let item=rcj.pbs[index];
          item.marketPrice=item.dePrice;
          item.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
          item.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
          item.markSum =RcjCommonConstants.MARKSUM_JX;
          item.originalQty = item.resQty;
          item.unitId = resource.unitId;
          item.constructId = resource.constructId;
          item.isFyrcj = resource.isFyrcj;
          for (let key in RcjTypeEnum) {
            if (RcjTypeEnum[key].code == item.kind) {
              item.type =  RcjTypeEnum[key].desc;
            }
          }
          if(resource.kind != ResourceKindConstants.INT_TYPE_ZC 
            && resource.kind != ResourceKindConstants.INT_TYPE_SB){
              childPbsCodes.push(item.materialCode);
          }
          service.PreliminaryEstimate.gsRcjService.processingMarketPrice(item);
          if(ObjectUtils.isNotEmpty(item.sourcePrice)){
            subSourcePrice =  item.sourcePrice;
          }
        }
        resource.sourcePrice = subSourcePrice
        resource.pbs = rcj.pbs;
      }
      
      if(resource.kind != ResourceKindConstants.INT_TYPE_ZC 
        && resource.kind != ResourceKindConstants.INT_TYPE_SB){
        let addCode = resource.materialCode;
        if(childPbsCodes.length>0){
          childPbsCodes.push(childPbsCodes[0]);
          childPbsCodes[0] = addCode;
          addCode = childPbsCodes.join(',')
        }
        childCodes.push(addCode);
      }
      // 市场价同步
      service.PreliminaryEstimate.gsRcjService.processingMarketPrice(resource);
      service.PreliminaryEstimate.gsRcjService.processingDonorMaterial(resource);
      //处理主材和设备,挂一个主材及设备定额
      if(resource.kind == ResourceKindConstants.INT_TYPE_ZC || resource.kind == ResourceKindConstants.INT_TYPE_SB){
        //
        deModel.isExistedZcSb = CommonConstants.COMMON_YES;
        let constructRcjArray= service.PreliminaryEstimate.gsRcjService.getAllRcj(resource);
        await service.PreliminaryEstimate.gsRcjCollectService.changeMaterialCodeMemory(resource, true, constructRcjArray);
      }
      this.resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, resource);
      if(isBaseRCJ){
        deModel.ifLockStandardPrice = resource.ifLockStandardPrice;
        deModel.markSum = resource.markSum;
      }
      let rcjNameObj = {};
      rcjNameObj.sequenceNbr = resource.sequenceNbr;
      rcjNameObj.initMaterialName = resource.materialName;
      initDeRcjNameList.push(rcjNameObj);
      childIds.push(resource.sequenceNbr);
      if (ObjectUtil.isNotEmpty(resource.pbs) && resource.pbs.length > 1) {
        for (let pb of resource.pbs) {
          childIds.push(pb.sequenceNbr);
        }
      }
    }
    deModel.initChildCodes = childCodes;
    deModel.initDeRcjNameList = initDeRcjNameList;
    deModel.initChildIds = childIds;

  }
  /**
   *
   * @param rcj
   * @param constructId
   * @param unitId
   * @param deId
   * @param parentId
   * @returns {Promise<void>}
   */
  async attachPBs(rcj, constructId, unitId, deId, parentId) {
    await this.resourceDomain.createResourcePBs(unitId, deId, parentId, rcj);
  }
  /**
   * 清单下追加补充定额
   * @param baseDe
   * @param deModel
   */
  async attachSubDe(baseDe, deModel) {
    let {service} = EE.app;
    if (!ObjectUtil.isEmpty(baseDe.subDeList)) {
      //constructId,unitId,sequenceNbr,parentId,type
      let childCodes = [];
      let childIds = [];
      for (let subDe of baseDe.subDeList) {
        let subDeModel = new StandardDeModel(deModel.constructId, deModel.unitId, Snowflake.nextId(), deModel.sequenceNbr, DeTypeConstants.DE_TYPE_DE);
        PropertyUtil.copyProperties(subDe, subDeModel, DeDomain.baseDeToDeAvoidProperty);
        subDeModel.initResQty = subDe.resQty;//初始化消耗量
        subDeModel.standardId = subDe.sequenceNbr;//指向标准id
        subDeModel.rcjList = subDe.rcjList;//指向标准id
        await this.createDeRow(subDeModel);

        subDe.costFileCode = deModel.costFileCode;
        subDe.costMajorName = deModel.costMajorName;
        childCodes.push(subDeModel.deCode);
        childIds.push(subDeModel.sequenceNbr)

        await this.attachDeRCJ(subDe.deRcjRelationList, subDeModel,subDe.rcjList);
      }
      deModel.initChildCodes = childCodes;
      deModel.initChildIds = childIds;
      try {
        await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
          constructId: deModel.constructId,
          unitId: deModel.unitId,
          constructMajorType: deModel.libraryCode
        });
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }
  }
  /**
   * 获得定额
   * @param predicate
   * @returns {*}
   */
  getDe(predicate) {
    return this.ctx.deMap.getAllNodes().find(predicate);
  }
  /**
   * 获得定额
   * @param predicate
   * @returns {*}
   */
  getDes(predicate) {
    return this.ctx.deMap.getAllNodes().filter(predicate);
  }
  /**
   *
   * @returns {*|any[]}
   */
  getDeTree(predicate) {
    return PropertyUtil.shallowCopyAndFilterProperties(this.ctx.deMap.getAllNodes().filter(predicate), BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }


  updateDeIsShowAnnotations(unitId, isShowAnnotations) {
    let allNodes1 = this.ctx.deMap.getAllNodes();
    let allNodes = allNodes1.filter(o => o.unitId === unitId);
    for (let item of allNodes) {
      item.isShowAnnotations = isShowAnnotations;
    }
  }


  /**
   * 深度获取某个节点下的所有子节点 孙节点一直到最末级
   * @param constructId
   * @param deRowId
   * @param unitId
   * @param types
   * @returns {*}
   */
  getDeTreeDepth(constructId,unitId,deRowId,types,posId)
  {
    let de =  ObjectUtil.isEmpty(deRowId) ? this.getRoot(unitId) : this.ctx.deMap.getNodeById(deRowId);
    let deList = [];
    this._getDeDepth(de,deList,types,posId);
    return PropertyUtil.shallowCopyAndFilterProperties(deList,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }


  /**
   * 深度获取某个节点下的所有子节点 孙节点一直到最末级
   * @param constructId
   * @param deRowId
   * @param unitId
   * @param types
   * @returns {*}
   */
  getDeAllTreeDepth(constructId,unitId,deRowId,types)
  {
    let de =  ObjectUtil.isEmpty(deRowId) ? this.getRoot(unitId) : this.ctx.deMap.getNodeById(deRowId);
    let deList = [];
    this._getDeAllDepth(de,deList,types);
    return PropertyUtil.shallowCopyAndFilterProperties(deList,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }
  getFbTreeDepth(constructId,unitId,deRowId,types){
    let de =  ObjectUtil.isEmpty(deRowId) ? this.getRoot(unitId) : this.ctx.deMap.getNodeById(deRowId);
    let deList = [];
    this._getFbDepth(de,deList,types);
    return PropertyUtil.shallowCopyAndFilterProperties(deList,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }
  _getFbDepth(de,deList,types)
  {
    if(ObjectUtil.isNotEmpty(de)) {
      if(ObjectUtil.isEmpty(types) || types.includes(de.type))
      {
        deList.push(de);
      }
      // let deItem = this.ctx.deMap.getNodeById(de.sequenceNbr);
      if (de.children && de.children.length > 0)
      {
          for (let subDe of de.children) {
            this._getFbDepth(subDe, deList, types);
          }
      }
    }
  }
  /**
   * 内部方法 外部勿用
   * @param de
   * @param deList
   * @param types
   */
  _getDeDepth(de,deList,types,posId)
  {
    if(ObjectUtil.isNotEmpty(de)) {
      if(ObjectUtil.isEmpty(types) || types.includes(de.type))
      {
        deList.push(de);
        //保证数据显示主材或设备
        if(de.isExistedZcSb === CommonConstants.COMMON_YES &&(de.type === DeTypeConstants.DE_TYPE_DE 
          || de.type === DeTypeConstants.DE_TYPE_USER_DE
          || (de.type === DeTypeConstants.DE_TYPE_DELIST&&de.displaySign === BranchProjectDisplayConstant.open))){
            this._addZcSb2De(de,deList);
          }
      }
      let deItem = this.ctx.deMap.getNodeById(de.sequenceNbr);
      if (deItem.children.length)
      {
        if (posId){
         this._getAncestorIds(deItem, posId).map(de=>{
           de.displaySign = BranchProjectDisplayConstant.open
         })
        }
        if (de.displaySign === BranchProjectDisplayConstant.open) {
          let childrens = deItem.children;
          //排序
          childrens.sort((a,b)=>a.index-b.index);
          for (let subDe of childrens) {
            this._getDeDepth(subDe, deList, types);
          }
        }
      }
    }
  }
  _getAncestorIds(treeData, nodeId) {
    const ancestorNodes = [];

    function traverse(node, path) {
      if (node.sequenceNbr === nodeId) {
        ancestorNodes.push(...path);
        return true;
      }

      if (node.children) {
        for (const child of Object.values(node.children)) {
          const found = traverse(child, [...path, node]);
          if (found) return true;
        }
      }

      return false;
    }

    traverse(treeData, []);
    return ancestorNodes;
  }
  /**
   * 内部方法 外部勿用
   * @param de
   * @param deList
   * @param types
   */
  _getDeAllDepth(de,deList,types)
  {
    if(ObjectUtil.isNotEmpty(de)) {
      if(ObjectUtil.isEmpty(types) || types.includes(de.type))
      {
        deList.push(de);
        //保证数据显示主材或设备
        if(de.isExistedZcSb === CommonConstants.COMMON_YES &&(de.type === DeTypeConstants.DE_TYPE_DE
          || de.type === DeTypeConstants.DE_TYPE_USER_DE
          || de.type === DeTypeConstants.DE_TYPE_DELIST)){
            this._addZcSb2De(de,deList);
          }
      }
      let deItem = this.ctx.deMap.getNodeById(de.sequenceNbr);
      if (deItem.children.length)
      {
        for (let subDe of deItem.children) {
          this._getDeAllDepth(subDe, deList, types);
        }
      }
    }
  }
  /**
   * 增加主材或设备到定额数据中
   * @param {*} de 
   * @param {*} deList 
   */
  _addZcSb2De(de,deList){
    let rcjDeKey = WildcardMap.generateKey(de.unitId, de.sequenceNbr) + WildcardMap.WILDCARD;
    let subResources =  this.ctx.resourceMap.getValues(rcjDeKey);
    // let subResources = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
    for(let rcj of subResources){
      if(rcj.kind == ResourceKindConstants.INT_TYPE_SB ){
        let deRow =  this._convertResource2De(de,rcj);
        deRow.SSum = deRow.price;
        deRow.sTotalSum = deRow.totalNumber;
        deList.push(deRow);
      }
      if(rcj.kind == ResourceKindConstants.INT_TYPE_ZC){
        let deRow =  this._convertResource2De(de,rcj);
        deRow.ZSum = deRow.price;
        deRow.zTotalSum = deRow.totalNumber;
        deList.push(deRow);
      }
    };
  }
  /**
   * 人材机转为定额
   * @param {*} parentDeRow 
   * @param {*} resource 
   * @returns 
   */
  _convertResource2De(parentDeRow,resource){
    let deRow = new StandardDeModel(parentDeRow.constructId,parentDeRow.unitId,resource.sequenceNbr,parentDeRow.sequenceNbr, DeTypeConstants.SUB_DE_TYPE_DE);
    deRow.kind = DeTypeConstants.SUB_DE_TYPE_DE;
    deRow.deResourceKind = parseInt(resource.kind);//增加补充人材机类型
    deRow.isDeResource = CommonConstants.COMMON_YES;
    PropertyUtil.copyProperties(resource ,deRow, [...DeDomain.avoidProperty,"sequenceNbr","parentId","deResourceKind","kind","type","isDeResource"]);
    deRow.price = resource.marketPrice;
    deRow.baseJournalPrice = resource.dePrice;
    deRow.quantity = resource.totalNumber;
    deRow.originalQuantity = resource.totalNumber;
    deRow.deName = resource.materialName;
    deRow.deCode = resource.materialCode; 
    deRow.costFileCode = parentDeRow.costFileCode;
    deRow.costMajorName = parentDeRow.costMajorName;
    deRow.standardId = resource.rcjId;
    deRow.totalNumber = resource.total;
    deRow.isNumLock = resource.isNumLock;//特殊处理
    //防止为undefined
    resource.resQtyFactor = ObjectUtils.isEmpty(resource.resQtyFactor) ? 1: resource.resQtyFactor;
    //消耗量表达式
    deRow.resqtyExp = `${resource.originalQty}*${resource.resQtyFactor}`

    return deRow;
  }

  _getRcjTypeEnumDescByCode(rcjTypeEnumCode) {
    for (let enumKey in RcjTypeEnum) {
      if (RcjTypeEnum[enumKey].code == rcjTypeEnumCode) {
        return RcjTypeEnum[enumKey].desc;
      }
    }
    return null;
  }
  /**
   * 更新定额
   * @param deModel
   */
  async updateDe(deModel, updateResouceKind=false) {
    
    let {service} = EE.app; 
    let de = this.ctx.deMap.getNodeById(deModel.sequenceNbr);
    let changeMaterialCode = false;
    if(updateResouceKind 
      && (de.type === DeTypeConstants.DE_TYPE_RESOURCE || de.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)
      && (de.deName !== deModel.deName || de.specification !== de.specification)){
        changeMaterialCode = true;
    }
    if (ObjectUtil.isNotEmpty(de)) {
      deModel.index = de.index;//不可更改index值;
      deModel.children = de.children;//不可更改children值;
      de.updateValue(deModel);
    } else {
      throw Error('没有找到工程:' + deModel.sequenceNbr);
    }
    if(changeMaterialCode){
      let rcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
        let rcjDetail = rcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES).value;
        rcjDetail.materialName = de.deName;
        await service.PreliminaryEstimate.gsRcjService.updateRcjDetail({constructId:de.constructId, singleId:null, unitId:de.unitId
          , deId:de.sequenceNbr
          , rcjDetailId:rcjDetail.sequenceNbr
          , constructRcj:{
            materialName:rcjDetail.materialName
        }});
    }
    if(updateResouceKind){
      let rcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
      if(ObjectUtil.isNotEmpty(rcjs)){
        let rcjDetailMap = rcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES);
        if(ObjectUtil.isNotEmpty(rcjDetailMap)){
          let  rcjDetail = rcjDetailMap.value;
          // rcjDetail.kind = de.deResourceKind;
          // rcjDetail.specification = de.specification;
          await service.PreliminaryEstimate.gsRcjService.updateRcjDetail({constructId:de.constructId, singleId:null, unitId:de.unitId
            , deId:de.sequenceNbr
            , rcjDetailId:rcjDetail.sequenceNbr
            , constructRcj:{
              kind: de.deResourceKind,
              specification:  de.specification
            }});

        }
      }
    }
    let notifyResource = false;
    if(updateResouceKind 
        && (de.type === DeTypeConstants.DE_TYPE_RESOURCE || de.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)
        && de.deResourceKind !== deModel.deResourceKind){
      notifyResource = true;
    }
    if(notifyResource){
      let rcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
      rcjs[0].value.type = this._getRcjTypeEnumDescByCode(deModel.deResourceKind);
      rcjs[0].value.kind = deModel.deResourceKind;

      this.resourceDomain.notify({constructId: de.constructId, unitId: de.unitId, deRowId: de.sequenceNbr}, this.ctx);
      
      await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
        constructId: de.constructId,
        unitId: de.unitId,
        constructMajorType: de.libraryCode
      });    
    }
    //this.notify();
  }


  /**
   * 更新定额,单纯的更新等额数据，向上汇总
   * @param deModel
   */
  updateDeOnlyOwnData(deModel) {
    let de = this.ctx.deMap.getNodeById(deModel.sequenceNbr);
    if (ObjectUtil.isNotEmpty(de)) {
      de.updateValue(deModel);
    } else {
      throw Error('没有找到工程:' + deModel.sequenceNbr);
    }
  }

  /**
   * 删除定额
   * @param deRowId
   */
  async removeDeRow(deRowId, isCountCost = true) {
    try {
      let {service} = EE.app;

      let deRow = this.ctx.deMap.getNodeById(deRowId);
      if(ObjectUtil.isEmpty(deRow))
      {
        return;
      }      
      let deRowBack = ConvertUtil.deepCopy(deRow);
      //用户定额删除后记录在缓存中为了未来使用
      if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE){
        this.add2UserDeBase(deRow.constructId,deRowBack);
      }
      this.removeRowRelatedDatas(deRow);
      this.ctx.deMap.removeNode(deRowId);

      if(deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT){
        //此时核查父级, 应该是这里啊  
        DeTypeCheckUtil.updateParentDeType(deRow,this.ctx);
  
        //记录，删除定额不用重新计算工程量？20240712其他是否又
        await this.notify(this.ctx.deMap.getNodeById(deRow.parentId),false);

        //计算父级是否展开/折叠
        await this.calDeDisplaySign(deRow.parentId);

        // let fc = FBCalculator.getInstance({constructId:deRow.constructId, unitId:deRow.unitId,deRowId: deRow.sequenceNbr},this.ctx);
        // await fc.analyze();
        if(isCountCost){
          await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
            constructId: deRow.constructId,
            unitId: deRow.unitId,
            constructMajorType: deRow.libraryCode
          });

          //联动计算装饰超高人材机数量
          await service.PreliminaryEstimate.gsDeService.calculateZSFee(deRow.constructId, deRow.unitId, true);
          //联动计算安装费
          await service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAloneDelete(deRow.constructId, deRow.unitId, deRow, "delete");
        }
      }

      // 删除定额后，其他相关操作
      await service.PreliminaryEstimate.gsInitDeService.remove(deRowBack);
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  removeRowRelatedDataById(relatedRowIds)
  {
    for (let rowId of relatedRowIds)
    {
      this.ctx.resourceMap.removeByValueProperty(DeDomain.FIELD_NAME_ROW_ID,rowId);
      this.ctx.deMap.removeNode(rowId);
    }

  }

  async calDeDisplaySign(deParentId) {
    let { service } = EE.app;
    if (ObjectUtil.isNotEmpty(deParentId)) {
      let parentDeRow = this.ctx.deMap.getNodeById(deParentId);
      let sign = true;
      if (ObjectUtil.isNotEmpty(parentDeRow) && ObjectUtil.isNotEmpty(parentDeRow.children)) {
        sign = false;
      }
      if (parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST || parentDeRow.type === DeTypeConstants.DE_TYPE_DE || parentDeRow.type === DeTypeConstants.DE_TYPE_USER_DE) {
        let rcjLists = await service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(parentDeRow.constructId, parentDeRow.unitId, deParentId, parentDeRow.type);
        if (ObjectUtil.isNotEmpty(rcjLists)) {
          let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
          if (ObjectUtil.isNotEmpty(filter)) {
            sign = false;
          }
        }
      }

      if (sign && parentDeRow.displaySign === BranchProjectDisplayConstant.open) {
        parentDeRow.displaySign = BranchProjectDisplayConstant.noSign;
      }
    }
  }

  /**
   * 通过定额行ID删除下面所有对应的定额 人材机
   * @param deRow
   * @param relatedRowIds
   */
  findDeRows(deRow,relatedRowIds)
  {
    for (let subDeRow of deRow.children)
    {
      relatedRowIds.push(subDeRow.sequenceNbr);
      if(subDeRow.type === DeTypeConstants.DE_TYPE_DELIST || subDeRow.type === DeTypeConstants.DE_TYPE_FB
          || subDeRow.type === DeTypeConstants.DE_TYPE_ZFB)
      {
        this.findDeRows(subDeRow,relatedRowIds);
      }

    }
  }
  /**
   * 通过定额ID 获得定额实例
   * @param deId
   * @returns {*}
   */
  getDeById(deId) {
    return this.ctx.deMap.getNodeById(deId);
  }
  static getClassName() {
    return DomainConstants.CODE_DE_DOMAIN;
  }

  /**
   * 过滤属性
   * @param deItem
   * @returns {{constructId, libraryName, classlevel04, classlevel05, isExistDe, projectType, standardId, type, total, price, unitId, deCode, classlevel01, classlevel02, classlevel03, quantity, index, libraryCode, parentId, sortNo, displayType, unit, deName, resQty, sequenceNbr}}
   */
  static filter4DeTree(deItem) {
    const {constructId,unitId, sequenceNbr, deRowId,type, parentId, displayType, index, libraryCode, libraryName,  deCode, deName, classlevel01, classlevel02,unit, resQty, quantity,originalQuantity, price, totalNumber, projectType, sortNo, isExistDe, standardId ,isDeResource,rcjDetailEdit,quantityExpression,costMajorName,costFileCode,deResourceKind,isTempRemove,changeQuantity,levelMark,annotations,isShowAnnotations,calculateMethod,displaySign,displayStatu,importYgsDeDRGCFB,importYgsDeYYGCFB,remark,rTotalSum,cTotalSum,jTotalSum,sTotalSum,zTotalSum,RSum,CSum,JSum,SSum,ZSum,rdTotalSum,cdTotalSum,jdTotalSum,zdTotalSum,sdTotalSum,RDSum,JDSum,CDSum,SDSum,ZDSum,specification,isFirstTempRemove,isNumLock,color,classiflevel1,ifLockStandardPrice,markSum,initDeRcjNameList,baseJournalPrice,baseJournalTotalNumber,isFyrcj,resqtyExp} = deItem; // 解构赋值，只保留需要的属性
    return {constructId,unitId, sequenceNbr,deRowId, type, parentId, displayType, index, libraryCode, libraryName, deCode, deName, classlevel01, classlevel02, unit, resQty, quantity,originalQuantity, price, totalNumber, projectType, sortNo, isExistDe, standardId ,isDeResource,rcjDetailEdit,quantityExpression,costMajorName,costFileCode,deResourceKind,isTempRemove,changeQuantity,levelMark,annotations,isShowAnnotations,calculateMethod,displaySign,displayStatu,importYgsDeDRGCFB,importYgsDeYYGCFB,remark,rTotalSum,cTotalSum,jTotalSum,sTotalSum,zTotalSum,RSum,CSum,JSum,SSum,ZSum,rdTotalSum,cdTotalSum,jdTotalSum,zdTotalSum,sdTotalSum,RDSum,JDSum,CDSum,SDSum,ZDSum,specification,isFirstTempRemove,isNumLock,color,classiflevel1,ifLockStandardPrice,markSum,initDeRcjNameList,baseJournalPrice,baseJournalTotalNumber,isFyrcj,resqtyExp}; // 返回一个只包含所需属性的新对象
  }

  /**
   * 所有定额在各自分部下按排序规则重新排序，排序规则如下：
   * 1-按“当前专业子目→借用子目→自行补充子目”顺序排序；
   * 2-对当前专业子目按章节顺序排序；
   * 3-对相同子目按输入的先后顺序排序；
   * 4-对自行补充子目按编码序列排序。
   * 5-借用定额、人材机定额均视为借用子目。
   * 6-自行补充定额、安装记取定额均视为自行补充子目。
   * @param {*} unitId 
   */
  async sortDe(unitId, libraryCode){
    let rootDe = this.getRoot(unitId);
    if(ObjectUtil.isEmpty(rootDe)){
      return;
    }
    
    this._sortChildDe(rootDe,libraryCode);
  }

  _sortChildDe(rootDe,libraryCode){
    if(ObjectUtil.isNotEmpty(rootDe.children)&& rootDe.children.length > 0 && ![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(rootDe.children[0].type)){
      let  curFeeChild = []; //当前取费专业下的子目
      let  curRcjChild = [];
      let  jieFeeChild = []; //其他取费专业子目
      let jieRcjChild = [];
      let  buFeeChild = []; //补充子目
      for(let child of rootDe.children){
        if(child.type === DeTypeConstants.DE_TYPE_RESOURCE){
          if(libraryCode === child.costFileCode){
            curRcjChild.push(child);
          }else{
            jieRcjChild.push(child);
          }
          continue;
        }
        //补充定额
        if([DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_USER_RESOURCE,DeTypeConstants.DE_TYPE_ANZHUANG_FEE].includes(child.type)){
          buFeeChild.push(child);          
        }else{
           //非补充定额
          if(libraryCode === child.costFileCode){
            curFeeChild.push(child);
          }else{
            jieFeeChild.push(child);          
          }
        }
      }
      //按照章节排序
      curFeeChild.sort((a,b)=>{ 
        if(a.sortNo>b.sortNo){
          return 1;
        }
        if(a.sortNo<b.sortNo){
          return -1;
        }
        return 0;

      })
      //rcj按编码排序
      curRcjChild.sort((a,b)=>{
        if(a.deCode>b.deCode){
          return 1;
        }
        if(a.deCode<b.deCode){
          return -1;
        }
        return 0; 
      })
      jieRcjChild.sort((a,b)=>{
        if(a.deCode>b.deCode){
          return 1;
        }
        if(a.deCode<b.deCode){
          return -1;
        }
        return 0; 
      })
      // jieFeeChild  按输入顺序，  借用定额、人材机定额均视为借用子目
      jieFeeChild.sort((a,b)=>{
        
        if(a.sortNo>b.sortNo){
          return 1;
        }
        if(a.sortNo<b.sortNo){
          return -1;
        }
        return 0; 
      })
      //buFeeChild  按照编码排序
      buFeeChild.sort((a,b)=>{
        if(a.deCode>b.deCode){
          return 1;
        }
        if(a.deCode<b.deCode){
          return -1;
        }
        return 0;        
      })

      let i = 0;
      for(let child of curFeeChild){
        child.index = i;
        i++;
      }
      for(let child of curRcjChild){
        child.index = i;
        i++;
      }
      for(let child of jieFeeChild){
        child.index = i;
        i++;
      }
      for(let child of jieRcjChild){
        child.index = i;
        i++;
      }
      for(let child of buFeeChild){
        child.index = i;
        i++;
      }
      return;
    }

    if(ObjectUtil.isNotEmpty(rootDe.children)&&rootDe.children.length > 0 && [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(rootDe.type)){
      for(let child of rootDe.children){
          this._sortChildDe(child,libraryCode);
      }
    }
  }
  /**
   * 无限制，只找满足types的父级
   * @param {*} deRow 
   * @param {*} parentList 
   * @param {*} types 
   */
  newFindParents(deRow,parentList,types)
  {
    if(ObjectUtil.isNotEmpty(deRow.parentId) ) {
       
      let parentRow = this.getDeById(deRow.parentId);

      if (ObjectUtil.isNotEmpty(parentRow) && types.includes(parentRow.type))
      {
        parentList.push(parentRow);
        this.newFindParents(parentRow,parentList,types);
      }
    }
  }
  /**
   * parentRow.type === DeTypeConstants.DE_TYPE_FB || parentRow.type === DeTypeConstants.DE_TYPE_ZFB
   *           || parentRow.type === DeTypeConstants.DE_TYPE_DEFAULT
   * 找到当前定额上层的分部或者子分部,直到返回默认的顶级Row
   * @returns {*}
   * @param deRow
   * @param parentList
   * @param types
   */
  findParents(deRow,parentList,types)
  {
    if(ObjectUtil.isNotEmpty(deRow.parentId) ) {
      if(deRow.type === DeTypeConstants.DE_TYPE_DEFAULT)
      {
        parentList.push(deRow);
        return;
      }

      let parentRow = this.getDeById(deRow.parentId);

      if (ObjectUtil.isNotEmpty(parentRow) && types.includes(parentRow.type))
      {
        parentList.push(parentRow);
        this.findParents(parentRow,parentList,types);
      }
    }
  }
  replaceAndCloneIds(des, newConstructId, newUnitId) {
    const clonedDes = _.cloneDeep(des);
    return clonedDes.map(unit => ({
      ...unit,
      constructId: newConstructId,
      unitId: newUnitId
    }));
  }
  /**
     * 临时删除定额
     * @param deRowId
     */
  async tempRemoveDeRow(deRowId, isFrontCall=false) {
    try {
      let {service} = EE.app;

      let deRow = this.ctx.deMap.getNodeById(deRowId);
      //不可对整个项目及分部子分部临时删除
      if(ObjectUtils.isEmpty(deRow) || deRow.isTempRemove === CommonConstants.COMMON_YES
        || deRow.type === DeTypeConstants.DE_TYPE_DEFAULT 
        || deRow.type === DeTypeConstants.DE_TYPE_FB
        || deRow.type === DeTypeConstants.DE_TYPE_ZFB ){
        return;
      }
      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE 
        || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
          deRow.price = 0;
      }
      
      if(deRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtil.isEmpty(deRow.originalQuantity)){
        let  unitNbr = UnitUtils.removeCharter(deRow.unit);
        if(ObjectUtil.isEmpty(unitNbr)){
          unitNbr = 1;
        }
        deRow.originalQuantity = NumberUtil.numberScale(deRow.quantity * unitNbr,5);
      }

      //标识删除
      deRow.isTempRemove = CommonConstants.COMMON_YES;
      if(isFrontCall){
        deRow.isFirstTempRemove = CommonConstants.COMMON_YES;
      }
      if(ObjectUtils.isEmpty(deRow.quantityExpression)){
        deRow.changeQuantity = deRow.originalQuantity;
      }else{
        deRow.changeQuantity = deRow.quantityExpression;
      }
      //获得子项，并标记
      let childs = [];
      let allRcjs = [];
      let rcjs =  this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      if(ObjectUtils.isNotEmpty(rcjs)){
        allRcjs = allRcjs.concat(rcjs);
      }
      this.findDeRows(deRow, childs);
      for(let childId of childs){
        //子定额设置为删除
        let childDeRow = this.ctx.deMap.getNodeById(childId);
        childDeRow.isFirstTempRemove = CommonConstants.COMMON_NO;
        
        //子定额已经临时删除了不再次删除
        if(childDeRow.isTempRemove === CommonConstants.COMMON_YES){
          continue;
        }
        childDeRow.isTempRemove = CommonConstants.COMMON_YES;
        childDeRow.changeQuantity = childDeRow.originalQuantity;

        let childRcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,childDeRow.sequenceNbr);
        if(ObjectUtils.isNotEmpty(childRcjs)){
          allRcjs = allRcjs.concat(childRcjs);
        }
        
      }
      //人材机数据
      for(let rcj of allRcjs) {
        //rcj
        if( rcj['value'].isTempRemove == CommonConstants.COMMON_YES){
          rcj['value'].isFirstTempRemove = CommonConstants.COMMON_NO;//重置
          continue;
        }
        rcj['value'].changeResQty = rcj['value'].resQty;
        rcj['value'].resQty = 0;
        rcj['value'].isTempRemove = CommonConstants.COMMON_YES;
        //pbs处理
        if(ObjectUtils.isNotEmpty(rcj['value'].pbs)){
          rcj['value'].pbs.forEach(pbsItem => {
            // pbsItem.changeResQty = pbsItem.resQty;
            // pbsItem.resQty = 0;
            pbsItem.isTempRemove = CommonConstants.COMMON_YES;
            pbsItem.isFirstTempRemove = CommonConstants.COMMON_NO;//重置
          });
        }
      };
      //更新该定额工程量0
      await this.updateQuantity(deRow.constructId,deRow.unitId,deRow.sequenceNbr,0,false,false,false);
     
      // let deRowBack = ConvertUtil.deepCopy(deRow);
      // 删除定额后，其他相关操作
      // 工程量明细，标准换算  都不需要处理
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }

  /**
     * 取消临时删除定额
     * @param deRowId
     */
  async cancelTempRemoveDeRow(deRowId) {
    try {
      let {service} = EE.app;

      let deRow = this.ctx.deMap.getNodeById(deRowId);
      //父级为临时删除的项不可取消临时删除
      let parentRow = this.ctx.deMap.getNodeById(deRow.parentId);
      //父级为临时删除不能取消临时删除
      if(parentRow.isTempRemove === CommonConstants.COMMON_YES 
        || deRow.isTempRemove === CommonConstants.COMMON_NO){
        return ;
      }
      //所有人材机
      let allRcjs = [];
      //
      let rcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      if(ObjectUtils.isNotEmpty(rcjs)){
        allRcjs = allRcjs.concat(rcjs);
      }
      
      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || 
        deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
          let rcjSelf = allRcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES)
          if(ObjectUtils.isNotEmpty(rcjSelf)){
            deRow.price =rcjSelf.value.price; 
          }
          let parentDe = this.ctx.deMap.getNodeById(deRow.parentId);
          if(parentDe.type === DeTypeConstants.DE_TYPE_DELIST){
            deRow.changeQuantity = deRow.resQty*parentDe.quantity;
          }
      }
      //标识删除恢复
      deRow.isTempRemove = CommonConstants.COMMON_NO;
      //获得子项，并标记
      let childs = [];
      this.findDeRows(deRow, childs);
      for(let child of childs){
        //子定额设置为删除
        let childDeRow = this.ctx.deMap.getNodeById(child);
        childDeRow.isTempRemove = CommonConstants.COMMON_NO;
        childDeRow.quantity = childDeRow.changeQuantity;
        //人材机数据
        let childRcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,childDeRow.sequenceNbr);
        if(ObjectUtils.isNotEmpty(childRcjs)){
          allRcjs = allRcjs.concat(childRcjs);
        }
      }
      //人材机数据
      
      allRcjs.forEach(rcj  => {
        //rcj
        rcj['value'].resQty = rcj['value'].changeResQty;
        rcj['value'].isTempRemove = CommonConstants.COMMON_NO;
        if(ObjectUtils.isNotEmpty(rcj['value'].pbs)){
          rcj['value'].pbs.forEach(pbsItem => {
            // pbsItem.resQty = pbsItem.changeResQty;
            pbsItem.isTempRemove = CommonConstants.COMMON_NO;
          })
        }
      });

      let precision = this.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
      let digital = DeUtils.getQuantiyPrecision(precision,deRow);
      if(parentRow.type == DeTypeConstants.DE_TYPE_DELIST){
        let  unitNbr = UnitUtils.removeCharter(deRow.unit);
        if(ObjectUtil.isEmpty(unitNbr)){
          unitNbr= 1;
        }
        deRow.changeQuantity = NumberUtil.numberFormat(NumberUtil.multiply(parentRow.quantity,deRow.resQty),digital)*unitNbr;
      }
      //更新该定额工程量0   恢复
      await this.updateQuantity(deRow.constructId,deRow.unitId,deRow.sequenceNbr,deRow.changeQuantity,false,false,false);
      if(parentRow.type == DeTypeConstants.DE_TYPE_DELIST){
        deRow.quantityExpression = '';
      }
      // let deRowBack = ConvertUtil.deepCopy(deRow);
      // 删除定额后，其他相关操作
      // 工程量明细，标准换算  都不需要处理
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 批量删除临时删除数据
   * @param deRowId
   */
  async realTempRemoveDeRow(constructId,unitProjects) {
    try {
      let {service} = EE.app;
      let relatedDeDeRowIds = [];
      let relatedRcjDetails = [];
      unitProjects.forEach(unitProject => {
        let rooDeRow = this.getRoot(unitProject.sequenceNbr);
        if(rooDeRow.isTempRemove === CommonConstants.COMMON_YES){
          relatedDeDeRowIds.push(rooDeRow.sequenceNbr);
        }
        this.findUnTempRemoveDeRows(rooDeRow, relatedDeDeRowIds,relatedRcjDetails);
      })
      return {de: relatedDeDeRowIds, rcj: relatedRcjDetails};
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 查找所有临时删除的定额数据
   * @param deRow
   * @param relatedRowIds
   */
  findUnTempRemoveDeRows(deRow,relatedRowIds, relatedRcjDetails)
  {
    //定额不是临删，但是人材机是临删的，需要处理
    if(deRow.isTempRemove === CommonConstants.COMMON_NO){
      let rcjLists = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      //需要对二次解析材料明细处理吗, 二次解析的配比，需要单独处理吗？
      for(let rcjDetailMap of rcjLists){
        let rcjDetail = rcjDetailMap.value;
        if(rcjDetail.isTempRemove === CommonConstants.COMMON_YES){
          relatedRcjDetails.push({sequenceNbr:rcjDetail.sequenceNbr,deRowId:rcjDetail.deRowId,unitId:deRow.unitId});
        }else{
          //有配比，要考虑配比
          if(ObjectUtils.isNotEmpty(rcjDetail.pbs)){
            rcjDetail.pbs.forEach(pbs => {
              if(pbs.isTempRemove === CommonConstants.COMMON_YES){
                relatedRcjDetails.push({sequenceNbr:pbs.sequenceNbr,deRowId:rcjDetail.deRowId,unitId:deRow.unitId});
              }});
          }
        }
      }
    }
    for (let subDeRow of deRow.children)
    {
      //找到临时删除项即可返回，不深挖
      if(subDeRow.isTempRemove === CommonConstants.COMMON_YES){
        relatedRowIds.push(subDeRow.sequenceNbr);
      }else{
        this.findUnTempRemoveDeRows(subDeRow,relatedRowIds,relatedRcjDetails);
      }
    }
  }

  async batchCancelTempRemoveDeRow(constructId,unitProjects) {
    try{
      let {service} = EE.app;
      let relatedDeDeRowIds = [];
      let relatedRcjDetails = [];
      unitProjects.forEach(unitProject => {
        let rooDeRow = this.getRoot(unitProject.sequenceNbr);
        if(rooDeRow.isTempRemove === CommonConstants.COMMON_YES){
          relatedDeDeRowIds.push(rooDeRow.sequenceNbr);
        }
        this.findUnTempRemoveDeRows(rooDeRow, relatedDeDeRowIds,relatedRcjDetails);
      })
      
      return {de: relatedDeDeRowIds, rcj: relatedRcjDetails};
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 粘贴定额  废弃了，不用了，详见gsdeservice.pastede
   * @param {*} constructId 
   * @param {*} unitId 
   * @param {*} oUnitId 
   * @param {*} idList 
   * @param {*} index 
   */
  async pasteDe(unitId,oUnitId,idList,prevDeRow, type,clearResqty = true){
    if(ObjectUtils.isEmpty(type)){
      type = "";
    }
    let {service} = EE.app;
    let preParentId = prevDeRow.parentId;
    let index = prevDeRow.index + 1;
    if(type === "child" || type === "childNoCircle"){
      preParentId = prevDeRow.sequenceNbr;
      if(prevDeRow.children &&  prevDeRow.children.length > 0){
        index = prevDeRow.children.length;
      }else{
        index = 0;
        prevDeRow.displaySign = BranchProjectDisplayConstant.open;
      }
    }
    let pasteDeList = [];
    let pasteRcjList = [];
    let pasteUserRcjList = [];
    let fbList = [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB];
    let deList = [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_RESOURCE,DeTypeConstants.DE_TYPE_ANZHUANG_FEE,DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_USER_RESOURCE,DeTypeConstants.DE_TYPE_EMPTY];
    let curTypeList = null;

    let parentNode = this.ctx.deMap.getNodeById(preParentId);
    if(parentNode&&parentNode.children && parentNode.children.length > 0){
      let newDeRow = parentNode.children[0];
      if(newDeRow.type === DeTypeConstants.DE_TYPE_FB || newDeRow.type === DeTypeConstants.DE_TYPE_ZFB){
        curTypeList = fbList;
      }else{
        curTypeList = deList;
      }
    }else if(parentNode && parentNode.type === DeTypeConstants.DE_TYPE_DELIST){
      curTypeList = deList;
    }
    
    let unitProject =  null;
    let oUnitProject =  null;
    
    //查询单位下的标准换算数据
    let deConversion = await this.functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
    let newDeIdConversion = {};
    let notifyQuantityMap = new Set();
    //let rcjUserList = await this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    for(let id of idList){
      let deRow = this.getDeById(id);
      if(!deRow){
        continue;
      }
      oUnitId = deRow.unitId;
      let parentList = [];
      this.findParents(deRow, parentList, [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
      let idExists = idList.find(itemSequenceNbr=>{
        let parent = parentList.find(parentItem=>parentItem.sequenceNbr === itemSequenceNbr);
        if(parent){
          return true;
        }
        return false;
      });
      if(idExists){
        continue;
      }
      // 复制定额
      let newDeRow = ConvertUtil.deepCopy(deRow);
      // pasteDeList.push(newDeRow);      
      if(ObjectUtils.isEmpty(curTypeList)){
        if(newDeRow.type === DeTypeConstants.DE_TYPE_FB || newDeRow.type === DeTypeConstants.DE_TYPE_ZFB){
          curTypeList = fbList;
        }else{
          curTypeList = deList;
        }
      }else{
        if(curTypeList.indexOf(newDeRow.type) === -1){
          continue;
        }
      }
      
      //重置id
      let rcjUserList = service.PreliminaryEstimate.gsRcjService.getUserRcj(newDeRow.constructId,oUnitId);
      this._resetDeRowId(newDeRow,unitId,preParentId,pasteDeList,pasteRcjList,pasteUserRcjList,type,prevDeRow.sequenceNbr,notifyQuantityMap,deConversion,newDeIdConversion, rcjUserList);
      
      if(unitProject == null){
        unitProject =  this.ctx.treeProject.getNodeById(unitId);
      }
      if(oUnitProject == null){
        oUnitProject =  this.ctx.treeProject.getNodeById(oUnitId);
      }
      //取费专业类型不同时处理
      if(unitProject.constructMajorType != oUnitProject.constructMajorType){
        let libraryMap = new Map();
        let baseDeLibraryModel = await service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(unitProject.constructMajorType);
        libraryMap.set(unitProject.sequenceNbr,baseDeLibraryModel);
        let baseDeLibraryModel2 = await service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(oUnitProject.constructMajorType);
        libraryMap.set(oUnitProject.sequenceNbr,baseDeLibraryModel2);
      
        //颠倒displayLable类型
        await this._reverseDisplayType(newDeRow,oUnitProject,service,libraryMap);
      }
      
    }

    if(ObjectUtils.isNotEmpty(pasteDeList)){
      let libraryCodeSet = new Set();//设置libraryCoee;
      let clearQuantity = false;
      if(parentNode.type === DeTypeConstants.DE_TYPE_DELIST){
        clearQuantity = true;
      }
      //无需notify及通知
      let emptyDes = [];
      //处理定额粘贴
      for(let pasteDeRow of pasteDeList){
        if(pasteDeRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE){
          continue;
        }
        //跨单位复制的用户定额，需要增加到新的单位中用户定额缓存中
        // if(pasteDeRow.type ===  DeTypeConstants.DE_TYPE_USER_DE && unitId !== oUnitId){
        //   this.add2UserDeBase(pasteDeRow.constructId,pasteDeRow);
        // }
        if(pasteDeRow.type === DeTypeConstants.DE_TYPE_EMPTY){
          emptyDes.push(pasteDeRow);
        }else{
          libraryCodeSet.add(pasteDeRow.libraryCode);
        }

        if(clearQuantity){
          pasteDeRow.quantity = 0;
          pasteDeRow.originalQuantity = 0;
          if(clearResqty){
            pasteDeRow.resQty = 0;
          }
          pasteDeRow.costFileCode = parentNode.costFileCode;
          pasteDeRow.costMajorName = parentNode.costMajorName;
        }
        pasteDeRow.children = [];//清空子级
        let parentDeRow = parentNode;
        if(pasteDeRow.parentId == parentNode.sequenceNbr){
          //维护定额map
          this._addNode2DeMap(pasteDeRow, parentNode,index);
          index += 1;
        }else{
          let newParentNode = pasteDeList.find(item=>item.sequenceNbr === pasteDeRow.parentId);
          parentDeRow = newParentNode;
           //维护定额map
           this._addNode2DeMap(pasteDeRow, newParentNode);
        }
        if(pasteDeRow.isTempRemove === CommonConstants.COMMON_YES){
          pasteDeRow.changeQuantity = 0;
        }
        if(parentDeRow.isTempRemove === CommonConstants.COMMON_YES&& pasteDeRow.isTempRemove !== CommonConstants.COMMON_YES){
          pasteDeRow.isTempRemove = CommonConstants.COMMON_YES;
          // pasteDeRow.changeQuantity = pasteDeRow.originalQuantity;
          pasteDeRow.changeQuantity = 0;
          pasteDeRow.originalQuantity = 0;
          pasteDeRow.quantity = 0;
        }
        if(parentDeRow.isTempRemove === CommonConstants.COMMON_YES
          && pasteDeRow.isTempRemove === CommonConstants.COMMON_YES
          && pasteDeRow.isFirstTempRemove === CommonConstants.COMMON_YES){
          pasteDeRow.isFirstTempRemove = CommonConstants.COMMON_NO;
        }
        if(parentDeRow.isTempRemove !== CommonConstants.COMMON_YES
          && pasteDeRow.isTempRemove === CommonConstants.COMMON_YES
          && pasteDeRow.isFirstTempRemove !== CommonConstants.COMMON_YES){
          pasteDeRow.isFirstTempRemove = CommonConstants.COMMON_YES;
        }

        if([DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(parentDeRow.type)){
          pasteDeRow.resQty = null;
        }
        
        if([DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(pasteDeRow.type)){
          pasteDeRow.resQty = null;        
          pasteDeRow.quantity = null;
          pasteDeRow.originalQuantity = null;
        }else{          
          if(!clearQuantity && notifyQuantityMap.has(pasteDeRow.sequenceNbr)){
            this._addNotifyQuantityMap(pasteDeRow);
          }
        }

        pasteDeRow.initDeRcjNameList = [];  //重置人材机初始化信息
      }
      //处理人材机粘贴
      for(let pasteRcj of pasteRcjList){
        let newParentNode = pasteDeList.find(item=>item.sequenceNbr === pasteRcj.deRowId);
        if(newParentNode.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE){
          continue;
        }
        if(newParentNode.isTempRemove === CommonConstants.COMMON_YES && pasteRcj.isTempRemove !== CommonConstants.COMMON_YES){
          pasteRcj.isTempRemove = CommonConstants.COMMON_YES;
          pasteRcj.changeResQty = pasteRcj.resQty;
          pasteRcj.resQty = 0;
        }
        this.resourceDomain.createResource(newParentNode.unitId,pasteRcj.deRowId, pasteRcj);

        //重新计算定额的人材机初始化信息
        let rcjNameObj = {};
        rcjNameObj.sequenceNbr = pasteRcj.sequenceNbr;
        rcjNameObj.initMaterialName = pasteRcj.materialName;
        let initDeRcjNameList1 = newParentNode.initDeRcjNameList;
        if (ObjectUtil.isNotEmpty(initDeRcjNameList1)) {
          initDeRcjNameList1.push(rcjNameObj);
          newParentNode.initDeRcjNameList = initDeRcjNameList1;
        } else {
          let initDeRcjNameList2 = [];
          initDeRcjNameList2.push(rcjNameObj);
          newParentNode.initDeRcjNameList = initDeRcjNameList2;
        }

      }
      //处理用户人材机
      for(let pasteUserRcj of pasteUserRcjList){
        let rcjUserList = service.PreliminaryEstimate.gsRcjService.getUserRcj(pasteUserRcj.constructId,unitId);
        let  rcj=rcjUserList.find(item => item.materialCode === pasteUserRcj.materialCode );
        if(ObjectUtils.isEmpty(rcj)){
          rcjUserList.push(ConvertUtil.deepCopy(pasteUserRcj));
        }
      }
      //处理标准换算粘贴
      if(ObjectUtil.isNotEmpty(newDeIdConversion)){
        let unitConversion2 = deConversion[unitId];
        if(ObjectUtil.isEmpty(unitConversion2)){
          unitConversion2 = {};
          deConversion[unitId] = unitConversion2;
        }
        Object.entries(newDeIdConversion).forEach(([key, value]) => {
          value.deRowId = key;
          value.sequenceNbr = key;
          value.deId = key;
          value.unitId = unitId;
          unitConversion2[key] = value;
        });
      }
      // 处理计算引擎数据变化
      if(!(emptyDes.length > 0 && pasteRcjList.length ===0 && emptyDes.length === pasteDeList.length)){
        this.notify(parentNode,clearResqty?clearQuantity:!clearResqty);
      }
      //处理定额的类型变化
      DeTypeCheckUtil.checkAndUpdateDeType(parentNode,this.ctx);
      try {
        //费用汇总变化
        for(let libraryCode of libraryCodeSet){
          await service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
            constructId: prevDeRow.constructId,
            unitId: prevDeRow.unitId,
            constructMajorType: libraryCode
          });
  
        }
        if(!(emptyDes.length > 0 && pasteRcjList.length ===0 && emptyDes.length === pasteDeList.length)){
          //联动计算装饰超高人材机数量
          await service.PreliminaryEstimate.gsDeService.calculateZSFee(prevDeRow.constructId, prevDeRow.unitId, true);
          //联动计取安装费
          await service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(prevDeRow.constructId, prevDeRow.unitId, prevDeRow.deRowId, "delete");
        }
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }
  }

  _checkPasteDeHasMap(deRow){
    let deGclMap = this.functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
    if(ObjectUtil.isEmpty(deGclMap)){
        return false;
    }
    
    let deSet = deGclMap.get(deRow.unitId);
    if(ObjectUtil.isEmpty(deSet)){
        return false;
    }
    let deExist = deSet.find(item=>item.unitId === deRow.unitId && item.deRowId === deRow.sequenceNbr);
    if(ObjectUtil.isNotEmpty(deExist)){
      return true;              
    }
    return false;
  }

  _addNotifyQuantityMap(addDeRow){
    let deGclMap = this.functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
    if(ObjectUtil.isEmpty(deGclMap)){
        deGclMap = new Map();
        this.functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY,deGclMap);
    }
    let deRowBak = {constructId:addDeRow.constructId, unitId:addDeRow.unitId, deRowId:addDeRow.sequenceNbr};
    let deSet = deGclMap.get(addDeRow.unitId);
    if(ObjectUtil.isEmpty(deSet)){
        deSet = [];
        deGclMap.set(addDeRow.unitId,deSet);
    }
    let deExist = deSet.find(item=>item.unitId === addDeRow.unitId && item.deRowId === addDeRow.sequenceNbr);
    if(ObjectUtil.isNotEmpty(deExist)){
        let index = deSet.indexOf(deExist);
        deSet.splice(index,1);
    }
    deSet.push(deRowBak);
  }

  async _reverseDisplayType(de,oUnitProject,service,libraryMap, isReverseChild = true){
    switch (de.displayType) {
      case '定':
          de.displayType = '借';
          break;
      case '借':
          de.displayType = '定';
          break;
      case '借换':
          de.displayType = '定换';
          break;
      case '换':
          de.displayType = '借换';
          break;
      default:
           ;
    }
    //与旧的相同，则取费专业变化
    let newlibraryModel = libraryMap.get(de.unitId);
    if(oUnitProject.constructMajorType === de.libraryCode && newlibraryModel.libraryCode != de.libraryCode) {
      let baseDeLibraryModel = libraryMap.get(oUnitProject.sequenceNbr);
      de.remark = baseDeLibraryModel.libraryName;
    }
    if(oUnitProject.constructMajorType != de.libraryCode && newlibraryModel.libraryCode == de.libraryCode){
      de.remark = null;
    }
    if(oUnitProject.constructMajorType != de.libraryCode && newlibraryModel.libraryCode != de.libraryCode){
     
      if(de.displayType.indexOf('定')> -1){
        de.displayType = de.displayType.replace('定','借')
      }else{
        de.displayType = '借'+de.displayType;
      }
    }
    if(isReverseChild&&de.children&&de.children.length>0){
      for(let child of de.children){
        this._reverseDisplayType(child,oUnitProject,service,libraryMap);
      }
    }
  }

  _addNode2DeMap(deRow, parentNode,index){
    if(index){
      this.ctx.deMap.addNodeAt(deRow, parentNode,index);
    }else{
      this.ctx.deMap.addNode(deRow, parentNode);
    }
  }
  /**
   * 粘贴定额重置id
   * @param {*} newDeRow 旧的定额副本
   * @param {*} unitId 新的单位
   * @param {*} parentId 新的父级
   * @param {*} pasteDeList 要赋值的定额
   * @param {*} pasteRcjList 要复制的人材机
   * @param {*} pasteUserRcjList 要复制的用户人材机
   * @param {*} type
   * @param {*} topPreParentId 
   * @param {*} notifyQuantityMap shifou 
   * @param {*} deConversion   标准换算
   * @param {*} newDeIdConversion   标准换算
   * @param {*} rcjUserList   用户人材机
   * @returns
   */
  _resetDeRowId(newDeRow,unitId,parentId,pasteDeList,pasteRcjList,pasteUserRcjList, type,topPreParentId,notifyQuantityMap,deConversion,newDeIdConversion,rcjUserList){
    if(type === "childNoCircle" && topPreParentId === newDeRow.sequenceNbr){ //防止循环嵌套
      return;
    }
    let rcjList = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,newDeRow.sequenceNbr);
    let newDeId = Snowflake.nextId();
    let oUnitId = newDeRow.unitId;//旧的单位id，不要变化啊
    let oDeId = newDeRow.sequenceNbr;
    //先判断是否存在
    if(this._checkPasteDeHasMap(newDeRow,this.functionDataMap)){
      notifyQuantityMap.add(newDeId);
    }
    newDeRow.sequenceNbr = newDeId;
    newDeRow.unitId = unitId;
    newDeRow.parentId = parentId;
    newDeRow.deRowId = newDeRow.sequenceNbr;///此处人材机查询使用作为sequenceNbr
    //处理标准换算
    if(newDeRow.type === DeTypeConstants.DE_TYPE_DELIST || newDeRow.type === DeTypeConstants.DE_TYPE_DE){
      let unitConversion = deConversion[oUnitId];
      if(ObjectUtil.isNotEmpty(unitConversion)){
        let deConversion = unitConversion[oDeId];
        if(ObjectUtil.isNotEmpty(deConversion)){
          let copyedConversion = ConvertUtil.deepCopy(deConversion);
          newDeIdConversion[newDeRow.sequenceNbr] = copyedConversion;
        }
      }
    }
    for(let rcj of rcjList){
      let copyRcj =  ConvertUtil.deepCopy(rcj.value);
      this._resetRcjId(copyRcj,newDeRow.sequenceNbr);
      copyRcj.unitId = unitId;
      pasteRcjList.push(copyRcj);

      if (rcj.value.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG) {
        let userRcj = rcjUserList.find(item => item.sequenceNbr === rcj.value.sequenceNbr);
        if (userRcj) {
          let copyUserRcj =  ConvertUtil.deepCopy(userRcj);
          copyUserRcj.sequenceNbr = copyRcj.sequenceNbr;
          copyUserRcj.deRowId = copyRcj.deRowId;
          copyUserRcj.unitId = copyRcj.unitId;
          copyUserRcj.deId = copyRcj.deRowId;
          copyUserRcj.parentId = copyRcj.deRowId;
          pasteUserRcjList.push(copyUserRcj);
        }
      }
    }

    pasteDeList.push(newDeRow);;
    if(ObjectUtil.isNotEmpty(newDeRow.children)){
      for (let child of newDeRow.children)
      {
        this._resetDeRowId(child,unitId,newDeRow.sequenceNbr,pasteDeList,pasteRcjList,pasteUserRcjList,type,topPreParentId,notifyQuantityMap,deConversion,newDeIdConversion,rcjUserList)
      }
    }
  }
  _resetRcjId(rcj,deRowId){
    
      rcj.sequenceNbr = Snowflake.nextId();
      rcj.deRowId = deRowId;
      rcj.deId = deRowId;
      rcj.parentId = deRowId;
      if(ObjectUtil.isNotEmpty(rcj.pbs)){
        for (let pbs of rcj.pbs)
        {
          pbs.sequenceNbr = Snowflake.nextId();
          pbs.parentId = rcj.sequenceNbr;
        }
      }
     
  }
}

DeDomain.toString = () => 'DeDomain';
module.exports = DeDomain;
