
/**
 * 工程量明细列表
 */
export class GsQuantitiesModel{

    public constructId: string; // 工程项目id

    public unitId: string; //单位id

    public quotaListId: string; //定额id

    public quantities: Array<Object>; //工程量明细

    public zmVariableRuleList: Array<Object>; //子目规则

    constructor(constructId: string, unitId: string, quotaListId: string, quantities: Array<Object>, zmVariableRuleList: Array<Object>) {
        this.constructId = constructId;
        this.unitId = unitId;
        this.quotaListId = quotaListId;
        this.quantities = quantities;
        this.zmVariableRuleList = zmVariableRuleList;
    }
}
