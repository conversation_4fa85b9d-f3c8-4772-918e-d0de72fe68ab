// exceljs 所需的 polyfills
// require('core-js/modules/es.promise');
// require('core-js/modules/es.string.includes');
// require('core-js/modules/es.object.assign');
// require('core-js/modules/es.object.keys');
// require('core-js/modules/es.symbol');
// require('core-js/modules/es.symbol.async-iterator');
// require('regenerator-runtime/runtime');

// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {GSExcelUtil} = require("../utils/GSExcelUtil");
const {NumberUtil} = require("../utils/NumberUtil");

class GSWriteExcelBySheetUtil {

    constructor() {
    }

    /********工程项目层级*************/
    //1、A.0.1 封面
    async writeDataToProjectSheet1(data, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "（项目名称）");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充项目编码
        let importCell = GSExcelUtil.findValueCell(worksheet, "档案号：（项目编码）");
        let filterImport = data.filter(object => object.name == "项目编码")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[1].value = "档案号：" + filterImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importRow);

        //填充造价咨询单位
        let importCompanyCell = GSExcelUtil.findValueCell(worksheet, "（造价咨询单位）");
        let filterCompanyImport = data.filter(object => object.name == "造价咨询单位信息-造价咨询单位")[0];
        let importCompanyRow = worksheet.getRow(importCompanyCell.cell._row._number);
        if (filterCompanyImport != null && filterCompanyImport.remark != null) {
            importCompanyRow._cells[1].value = filterCompanyImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow);
    }


    //2、A.0.2 签署页
    async writeDataToProjectSheet2(data, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "（项目名称）");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number - 1].value = constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充项目编码
        let importCell = GSExcelUtil.findValueCell(worksheet, "档案号：（项目编码）");
        let filterImport = data.filter(object => object.name == "项目编码")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[importCell.cell._column._number - 1].value = "档案号：" + filterImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importRow);

        //填充设计单位编制人
        let importCompanyCell = GSExcelUtil.findValueCell(worksheet, "（设计单位编制人）");
        let filterCompanyImport = data.filter(object => object.name == "设计单位信息-编制人")[0];
        let importCompanyRow = worksheet.getRow(importCompanyCell.cell._row._number);
        if (filterCompanyImport != null && filterCompanyImport.remark != null) {
            importCompanyRow._cells[importCompanyCell.cell._column._number - 1].value = filterCompanyImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow);

        //填充设计单位审核人
        let importCompanyCell1 = GSExcelUtil.findValueCell(worksheet, "（设计单位审核人）");
        let filterCompanyImport1 = data.filter(object => object.name == "设计单位信息-核对人（复核人）")[0];
        let importCompanyRow1 = worksheet.getRow(importCompanyCell1.cell._row._number);
        if (filterCompanyImport1 != null && filterCompanyImport1.remark != null) {
            importCompanyRow1._cells[importCompanyCell1.cell._column._number - 1].value = filterCompanyImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow1);

        //填充造价单位审核人
        let importCompanyCell2 = GSExcelUtil.findValueCell(worksheet, "（造价单位审核人）");
        let filterCompanyImport2 = data.filter(object => object.name == "造价咨询单位信息-核对人（复核人）")[0];
        let importCompanyRow2 = worksheet.getRow(importCompanyCell2.cell._row._number);
        if (filterCompanyImport2 != null && filterCompanyImport2.remark != null) {
            importCompanyRow2._cells[importCompanyCell2.cell._column._number - 1].value = filterCompanyImport2.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow2);

        //填充设计单位审核人
        let importCompanyCell3 = GSExcelUtil.findValueCell(worksheet, "（设计单位法定代表人）");
        let filterCompanyImport3 = data.filter(object => object.name == "设计单位信息-法定代表人或其授权人")[0];
        let importCompanyRow3 = worksheet.getRow(importCompanyCell3.cell._row._number);
        if (filterCompanyImport3 != null && filterCompanyImport3.remark != null) {
            importCompanyRow3._cells[importCompanyCell3.cell._column._number - 1].value = filterCompanyImport3.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow3);
    }


    //3、A.0.3 目录
    async writeDataToProjectSheet3(list, worksheet) {
        let headCount = 1;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 1;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 1, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i + 1;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].headLine;
                }
            }
        }

        await GSExcelUtil.deleteBlankRow(worksheet);
    }


    //4、【封面4】审核签署表
    async writeDataToProjectSheet4(data, worksheet) {
        let row = worksheet._rows[1];
        for (let i = 0; i < row._cells.length; i++) {

            let cell = row._cells[i];
            cell.value = {
                'richText': [{
                    'font': {
                        'bold': false, 'size': 12, 'color': {'theme': 1},
                        'name': 'Calibri', 'family': 2, 'scheme': 'minor'
                    }, 'text': data
                }]
            };
            cell.protection = {
                locked: false,
                hidden: false,
            };
            break;
        }
    }


    //5、B.0.1 总概算表
    async writeDataToProjectSheet5(data, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "（项目名称）");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充项目编码
        let importCell = GSExcelUtil.findValueCell(worksheet, "档案号：（项目编码）");
        let filterImport = data.filter(object => object.name == "项目编码")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[1].value = "档案号：" + filterImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importRow);

        //填充造价咨询单位
        let importCompanyCell = GSExcelUtil.findValueCell(worksheet, "（造价咨询单位）");
        let filterCompanyImport = data.filter(object => object.name == "造价咨询单位信息-造价咨询单位")[0];
        let importCompanyRow = worksheet.getRow(importCompanyCell.cell._row._number);
        if (filterCompanyImport != null && filterCompanyImport.remark != null) {
            importCompanyRow._cells[1].value = filterCompanyImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow);
    }


    //6、B.0.2 总概算表
    async writeDataToProjectSheet6(data, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "（项目名称）");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充项目编码
        let importCell = GSExcelUtil.findValueCell(worksheet, "档案号：（项目编码）");
        let filterImport = data.filter(object => object.name == "项目编码")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[1].value = "档案号：" + filterImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importRow);

        //填充造价咨询单位
        let importCompanyCell = GSExcelUtil.findValueCell(worksheet, "（造价咨询单位）");
        let filterCompanyImport = data.filter(object => object.name == "造价咨询单位信息-造价咨询单位")[0];
        let importCompanyRow = worksheet.getRow(importCompanyCell.cell._row._number);
        if (filterCompanyImport != null && filterCompanyImport.remark != null) {
            importCompanyRow._cells[1].value = filterCompanyImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow);
    }


    //7、B.0.3 其他费用计算表
    async writeDataToProjectSheet7(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：（项目名称）");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3 && ObjectUtils.isNotEmpty(list[countRow].price)) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].quantity)) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].amount)) {
                    cell.value = list[countRow].amount;
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].calculationMethod)) {
                    let newVar = await this.queryQtfyjsCalculationMethod(list[countRow].calculationMethod);
                    cell.value = newVar;
                }
                if (cell.col == 7) {
                    //计算过程
                    cell.value = list[countRow].calculationProcess;
                }
                if (cell.col == 8) {
                    //计价依据
                    cell.value = list[countRow].calculationBasis;
                }
            }
        }

        let {total} = data["projectQtfyjsbHeji"];
        //定位到最后的合计行
        let heJiCell = GSExcelUtil.findValueCell(worksheet, "合计");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[4].value = total;//送审合价合计
    }


    //8、B.0.5 概算汇总表
    async writeDataToProjectSheet8(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].jzFee;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].azFee;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].sbgzFee;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].qtFee;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].unit)) {
                    cell.value = list[countRow].unit;
                }
                //todo 概算汇总获取
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].average)) {
                    cell.value = list[countRow].average;
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].unitCost)) {
                    cell.value = list[countRow].unitCost;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].proportion;
                }
            }
        }

    }


    //9、B.0.3 其他费用计算表
    async writeDataToProjectSheet9(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].jzFee;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].azFee;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].sbgzFee;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].qtFee;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].unit)) {
                    cell.value = list[countRow].unit;
                }
                //todo 概算汇总获取
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].average)) {
                    cell.value = list[countRow].average;
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].unitCost)) {
                    cell.value = list[countRow].unitCost;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].proportion;
                }
            }
        }

    }

    async writeDataToProjectSheet10(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].jzFee;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].azFee;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].sbgzFee;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].qtFee;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].unit)) {
                    cell.value = list[countRow].unit;
                }
                //todo 概算汇总获取
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].average)) {
                    cell.value = list[countRow].average;
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].unitCost)) {
                    cell.value = list[countRow].unitCost;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].proportion;
                }
            }
        }

    }

    async writeDataToProjectSheet11(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3 && ObjectUtils.isNotEmpty(list[countRow].jzFee)) {
                    cell.value = NumberUtil.divide(list[countRow].jzFee, 10000).toFixed(2);
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].azFee)) {
                    cell.value = NumberUtil.divide(list[countRow].azFee, 10000).toFixed(2);
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].sbgzFee)) {
                    cell.value = NumberUtil.divide(list[countRow].sbgzFee, 10000).toFixed(2);
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].qtFee)) {
                    cell.value = NumberUtil.divide(list[countRow].qtFee, 10000).toFixed(2);
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].price)) {
                    if (!isNaN(parseFloat(list[countRow].price))) {
                        cell.value = NumberUtil.divide(list[countRow].price, 10000).toFixed(2);
                    } else {
                        cell.value = list[countRow].price;
                    }
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].unit)) {
                    // cell.value = list[countRow].unit;
                    cell.value = "万元";
                }
                //todo 概算汇总获取
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].average)) {
                    cell.value = list[countRow].average;
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].unitCost)) {
                    // cell.value = list[countRow].unitCost;
                    cell.value = NumberUtil.divide(list[countRow].unitCost, 10000).toFixed(2);
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].proportion;
                }
            }
        }

    }

    async writeDataToProjectSheet12(data, list, worksheet) {
        // 填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i + 1;
                    // cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 3 && ObjectUtils.isNotEmpty(list[countRow].specification)) {
                    cell.value = list[countRow].specification;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].unit)) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].totalNumber)) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].total)) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].sourcePrice)) {
                    cell.value = list[countRow].sourcePrice;
                }
                if (cell.col == 8) {
                    //todo 备注字段
                    cell.value = "";
                }
            }
        }
    }


    async writeDataToProjectSheet13(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number - 1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let constructNameCell1 = GSExcelUtil.findValueCell(worksheet, "总概算编号：");
        let constructNameImport1 = data.filter(object => object.name == "项目编码")[0];
        let constructNameRow1 = worksheet.getRow(constructNameCell1.cell._row._number);
        if (constructNameImport1 != null && constructNameImport1.remark != null) {
            constructNameRow1._cells[constructNameCell1.cell._column._number - 1].value = "总概算编号：" + constructNameImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow1);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3 && ObjectUtils.isNotEmpty(list[countRow].jzFee)) {
                    cell.value = NumberUtil.divide(list[countRow].jzFee, 10000).toFixed(2);
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].gzFee)) {
                    cell.value = NumberUtil.divide(list[countRow].gzFee, 10000).toFixed(2);
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].azFee)) {
                    cell.value = NumberUtil.divide(list[countRow].azFee, 10000).toFixed(2);
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].qtFee)) {
                    cell.value = NumberUtil.divide(list[countRow].qtFee, 10000).toFixed(2);
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].price)) {
                    cell.value = NumberUtil.divide(list[countRow].price, 10000).toFixed(2);
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].tzjzFee)) {
                    cell.value = NumberUtil.divide(list[countRow].tzjzFee, 10000).toFixed(2);
                }
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].tzszgzFee)) {
                    cell.value = NumberUtil.divide(list[countRow].tzszgzFee, 10000).toFixed(2);
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].tzazFee)) {
                    cell.value = NumberUtil.divide(list[countRow].tzazFee, 10000).toFixed(2);
                }
                if (cell.col == 11 && ObjectUtils.isNotEmpty(list[countRow].tzqtFee)) {
                    cell.value = NumberUtil.divide(list[countRow].tzqtFee, 10000).toFixed(2);
                }
                if (cell.col == 12 && ObjectUtils.isNotEmpty(list[countRow].tzprice)) {
                    cell.value = NumberUtil.divide(list[countRow].tzprice, 10000).toFixed(2);
                }
                if (cell.col == 13 && ObjectUtils.isNotEmpty(list[countRow].diffAmount)) {
                    cell.value = NumberUtil.divide(list[countRow].diffAmount, 10000).toFixed(2);
                }
                if (cell.col == 14 && ObjectUtils.isNotEmpty(list[countRow].remark)) {
                    cell.value = list[countRow].remark;
                }
            }
        }

    }


    async writeDataToProjectSheet14(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].quantity)) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].fob_usd)) {
                    //离岸价（美元）
                    cell.value = list[countRow].fob_usd;
                }

                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].costNameMY)) {
                    //美元费用名称
                    cell.value = list[countRow].costNameMY;
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].costMoneyMY)) {
                    //美元费用金额
                    cell.value = list[countRow].costMoneyMY;
                }
                if (cell.col == 8) {
                    //到岸价（美元）
                    cell.value = list[countRow].cif_usd;
                }
                if (cell.col == 9) {
                    //到岸价（人民币）
                    cell.value = list[countRow].cif_cny;
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].costNameRMB)) {
                    //人民币费用名称
                    cell.value = list[countRow].costNameRMB;
                }
                if (cell.col == 11 && ObjectUtils.isNotEmpty(list[countRow].costMoneyRMB)) {
                    //人民币费用金额
                    cell.value = list[countRow].costMoneyRMB;
                }
                if (cell.col == 12) {
                    //单价
                    cell.value = list[countRow].price;
                }
                if (cell.col == 13) {
                    //合价
                    cell.value = list[countRow].totalPrice;
                }
            }
        }
    }


    async writeDataToProjectSheet15(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    // cell.value = list[countRow].dispNo;
                    cell.value = i + 1;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].specification;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].unit)) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].quantity)) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].factoryPrice)) {
                    cell.value = list[countRow].factoryPrice;
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].transportAndOtherRates)) {
                    cell.value = list[countRow].transportAndOtherRates;
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].price)) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].totalPrice)) {
                    cell.value = list[countRow].totalPrice;
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].producer)) {
                    cell.value = list[countRow].producer;
                }
            }
        }
    }


    async writeDataToSheetXioji(data, worksheet) {

        let valueCell = GSExcelUtil.findContainValueCell(worksheet, "编制人：");
        if (ObjectUtils.isNotEmpty(valueCell)) {
            let constructNameImport = data.filter(object => object.name == "设计单位信息-编制人")[0];
            for (let constructNameCell of valueCell) {
                let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
                if (constructNameImport != null && constructNameImport.remark != null) {
                    constructNameRow._cells[constructNameCell.cell._column._number - 1].value = "编制人：" + constructNameImport.remark;
                }
                GSExcelUtil.traversalRowToCellBottom(constructNameRow);
            }
        }

        let valueCell1 = GSExcelUtil.findContainValueCell(worksheet, "审核人：");
        if (ObjectUtils.isNotEmpty(valueCell1)) {
            let constructNameImport1 = data.filter(object => object.name == "设计单位信息-核对人（复核人）")[0];
            for (let constructNameCell1 of valueCell1) {
                let constructNameRow1 = worksheet.getRow(constructNameCell1.cell._row._number);
                if (constructNameImport1 != null && constructNameImport1.remark != null) {
                    constructNameRow1._cells[constructNameCell1.cell._column._number - 1].value = "审核人：" + constructNameImport1.remark;
                }
                GSExcelUtil.traversalRowToCellBottom(constructNameRow1);
            }
        }


        let valueCell2 = GSExcelUtil.findContainValueCell(worksheet, "审定人：");
        if (ObjectUtils.isNotEmpty(valueCell2)) {
            let constructNameImport2 = data.filter(object => object.name == "造价咨询单位信息-核对人（复核人）")[0];
            for (let constructNameCell2 of valueCell2) {
                let constructNameRow2 = worksheet.getRow(constructNameCell2.cell._row._number);
                if (constructNameImport2 != null && constructNameImport2.remark != null) {
                    constructNameRow2._cells[constructNameCell2.cell._column._number - 1].value = "审定人：" + constructNameImport2.remark;
                }
                GSExcelUtil.traversalRowToCellBottom(constructNameRow2);
            }
        }
    }


    async writeDataToProjectSheet16(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].materialCode;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 3 && ObjectUtils.isNotEmpty(list[countRow].specification)) {
                    cell.value = list[countRow].specification;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].unit)) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].totalNumber)) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].dePrice)) {
                    cell.value = list[countRow].dePrice;
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].dePrice) && ObjectUtils.isNotEmpty(list[countRow].totalNumber)) {
                    cell.value = NumberUtil.numberFormat(NumberUtil.multiply(list[countRow].totalNumber, list[countRow].dePrice),2);
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].marketPrice)) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].total)) {
                    cell.value =  NumberUtil.numberFormat(list[countRow].total,2);
                }
            }
        }

    }


    async writeDataToProjectSheet17(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let constructNameCell1 = GSExcelUtil.findValueCell(worksheet, "总概算编号：");
        let constructNameImport1 = data.filter(object => object.name == "项目编码")[0];
        let constructNameRow1 = worksheet.getRow(constructNameCell1.cell._row._number);
        if (constructNameImport1 != null && constructNameImport1.remark != null) {
            constructNameRow1._cells[constructNameCell1.cell._column._number].value = "总概算编号：" + constructNameImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow1);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3 && ObjectUtils.isNotEmpty(list[countRow].jzFee)) {
                    cell.value = list[countRow].jzFee;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].sbgzFee)) {
                    cell.value = list[countRow].sbgzFee;
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].azFee)) {
                    cell.value = list[countRow].azFee;
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].qtFee)) {
                    cell.value = list[countRow].qtFee;
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].price)) {
                    cell.value = list[countRow].price;
                }
                //todo 引进部分取值
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].MY)) {
                    cell.value = list[countRow].MY;
                }
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].RMB)) {
                    cell.value =list[countRow].RMB;
                }

                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].proportion)) {
                    cell.value = list[countRow].proportion;
                }
            }
        }
    }


    async writeDataToProjectSheet18(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number].value = "工程名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let constructNameCell1 = GSExcelUtil.findValueCell(worksheet, "总概算编号：");
        let constructNameImport1 = data.filter(object => object.name == "项目编码")[0];
        let constructNameRow1 = worksheet.getRow(constructNameCell1.cell._row._number);
        if (constructNameImport1 != null && constructNameImport1.remark != null) {
            constructNameRow1._cells[constructNameCell1.cell._column._number].value = "总概算编号：" + constructNameImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow1);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3 && ObjectUtils.isNotEmpty(list[countRow].average)) {
                    cell.value = list[countRow].average;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].jzFee)) {
                    cell.value =list[countRow].jzFee;
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(list[countRow].sbgzFee)) {
                    cell.value = list[countRow].sbgzFee;
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].azFee)) {
                    cell.value = list[countRow].azFee;
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].qtFee)) {
                    cell.value = list[countRow].qtFee;
                }
                if (cell.col == 8 && ObjectUtils.isNotEmpty(list[countRow].price)) {
                    cell.value = list[countRow].price;
                }
                //todo 引进部分取值
                if (cell.col == 9 && ObjectUtils.isNotEmpty(list[countRow].MY)) {
                    cell.value = list[countRow].MY;
                }
                if (cell.col == 10 && ObjectUtils.isNotEmpty(list[countRow].RMB)) {
                    cell.value =list[countRow].RMB;
                }
                if (cell.col == 11 && ObjectUtils.isNotEmpty(list[countRow].proportion)) {
                    cell.value = list[countRow].proportion;
                }
            }
        }

    }


    async writeDataToProjectSheet19(data, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "（项目名称）");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number - 1].value = constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充项目编码
        let importCell = GSExcelUtil.findValueCell(worksheet, "（建设单位名称）");
        let filterImport = data.filter(object => object.name == "建设单位信息-建设单位")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[importCell.cell._column._number - 1].value = filterImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importRow);

        //填充造价咨询单位
        let importCompanyCell = GSExcelUtil.findValueCell(worksheet, "（造价单位名称）");
        let filterCompanyImport = data.filter(object => object.name == "造价咨询单位信息-造价咨询单位")[0];
        let importCompanyRow = worksheet.getRow(importCompanyCell.cell._row._number);
        if (filterCompanyImport != null && filterCompanyImport.remark != null) {
            importCompanyRow._cells[importCompanyCell.cell._column._number - 1].value = filterCompanyImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow);
    }


    async writeDataToProjectSheet20(data, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "（项目名称）");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number - 1].value = constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);


        //填充项目编码
        let importCell111 = GSExcelUtil.findValueCell(worksheet, "档案号：（项目编码）");
        let filterImport111 = data.filter(object => object.name == "项目编码")[0];
        let importRow111 = worksheet.getRow(importCell111.cell._row._number);
        if (filterImport111 != null && filterImport111.remark != null) {
            importRow111._cells[importCell111.cell._column._number - 1].value = "档案号：" + filterImport111.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importRow111);

        //填充项目编码
        let importCell = GSExcelUtil.findValueCell(worksheet, "造价编制人");
        let filterImport = data.filter(object => object.name == "造价咨询单位信息-编制人")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[importCell.cell._column._number - 1].value = filterImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importRow);

        //填充造价咨询单位
        let importCompanyCell = GSExcelUtil.findValueCell(worksheet, "造价审核人");
        let filterCompanyImport = data.filter(object => object.name == "造价咨询单位信息-核对人（复核人）")[0];
        let importCompanyRow = worksheet.getRow(importCompanyCell.cell._row._number);
        if (filterCompanyImport != null && filterCompanyImport.remark != null) {
            importCompanyRow._cells[importCompanyCell.cell._column._number - 1].value = filterCompanyImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow);

        //填充造价咨询单位
        let importCompanyCell1 = GSExcelUtil.findValueCell(worksheet, "造价技术负责人");
        let filterCompanyImport1 = data.filter(object => object.name == "造价咨询单位信息-技术负责人")[0];
        let importCompanyRow1 = worksheet.getRow(importCompanyCell1.cell._row._number);
        if (filterCompanyImport1 != null && filterCompanyImport1.remark != null) {
            importCompanyRow1._cells[importCompanyCell1.cell._column._number - 1].value = filterCompanyImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow1);

        //填充造价咨询单位
        let importCompanyCell2 = GSExcelUtil.findValueCell(worksheet, "造价单位法人");
        let filterCompanyImport2 = data.filter(object => object.name == "造价咨询单位信息-法定代表人或其授权人")[0];
        let importCompanyRow2 = worksheet.getRow(importCompanyCell2.cell._row._number);
        if (filterCompanyImport2 != null && filterCompanyImport2.remark != null) {
            importCompanyRow2._cells[importCompanyCell2.cell._column._number - 1].value = filterCompanyImport2.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow2);

        //填充造价咨询单位
        let importCompanyCell3 = GSExcelUtil.findValueCell(worksheet, "造价单位名称");
        let filterCompanyImport3 = data.filter(object => object.name == "造价咨询单位信息-造价咨询单位")[0];
        let importCompanyRow3 = worksheet.getRow(importCompanyCell3.cell._row._number);
        if (filterCompanyImport3 != null && filterCompanyImport3.remark != null) {
            importCompanyRow3._cells[importCompanyCell3.cell._column._number - 1].value = filterCompanyImport3.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow3);


        //填充时间
        let importCompanyCell4 = GSExcelUtil.findValueCell(worksheet, "年     月    日");
        let filterCompanyImport4 = data.filter(object => object.name == "造价咨询单位信息-编制时间")[0];
        let importCompanyRow4 = worksheet.getRow(importCompanyCell4.cell._row._number);
        if (filterCompanyImport4 != null &&  ObjectUtils.isNotEmpty(filterCompanyImport4.remark)) {
            const formattedDate = await this.formatDateString(filterCompanyImport4.remark, "YYYY年 MM月 DD日");
            importCompanyRow4._cells[importCompanyCell4.cell._column._number - 1].value = formattedDate;
        }
        GSExcelUtil.traversalRowToCellBottom(importCompanyRow4);
    }

    async formatDateString(dateStr, format) {
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = date.getMonth();
        const day = date.getDate();

        format = format.replace("YYYY", year);
        format = format.replace("MM", String(month + 1).padStart(2, '0'));
        format = format.replace("MMM", months[month]);
        format = format.replace("DD", String(day).padStart(2, '0'));

        return format;
    }


    async queryQtfyjsCalculationMethod(calculationMethod) {
        let result = "";
        switch (calculationMethod) {
            case 1:
                result = "单价*数量";
                break;
            case 2:
                result = "计算基数*费率";
                break;
            case 3:
                result = "费用计算器";
                break;
            case 4:
                result = "手动输入";
                break;
        }
        return result;
    }


    /********单项工程层级*************/
    //1、B.0.05 综合概算表
    async writeDataToSingleSheet1(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单项名称：");
        let constructNameImport = data.filter(object => object.name == "单项名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number].value = "单项名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let constructNameCell1 = GSExcelUtil.findValueCell(worksheet, "综合概算编号：");
        let constructNameImport1 = data.filter(object => object.name == "项目编码")[0];
        let constructNameRow1 = worksheet.getRow(constructNameCell1.cell._row._number);
        if (constructNameImport1 != null && constructNameImport1.remark != null) {
            constructNameRow1._cells[constructNameCell1.cell._column._number].value = "综合概算编号：" + constructNameImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow1);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    // if (i == 0) {
                    //     cell.value = "1"
                    // } else {
                    //     cell.value = "1." + i;
                    // }
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].projectName;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].average;
                }
                if (cell.col == 4 && ObjectUtils.isNotEmpty(list[countRow].jzCostSummary)) {
                    if(list[countRow].jzCostSummary == 0){
                        cell.value='';
                    }else{
                        cell.value = NumberUtil.divide(list[countRow].jzCostSummary, 10000).toFixed(2);
                    }
                }
                if (cell.col == 5 && ObjectUtils.isNotEmpty(cell.value = list[countRow].qzsbf)) {
                    if(list[countRow].qzsbf == 0){
                        cell.value='';
                    }else{
                        cell.value = NumberUtil.divide(list[countRow].qzsbf, 10000).toFixed(2);
                    }
                }
                if (cell.col == 6 && ObjectUtils.isNotEmpty(list[countRow].azCostSummary)) {
                    if(list[countRow].azCostSummary == 0){
                        cell.value='';
                    }else{
                        cell.value = NumberUtil.divide(list[countRow].azCostSummary, 10000).toFixed(2);
                    }
                }
                if (cell.col == 7 && ObjectUtils.isNotEmpty(list[countRow].budgetzjf)) {
                    if(list[countRow].budgetzjf == 0){
                        cell.value='';
                    }else{
                        cell.value = NumberUtil.divide(list[countRow].budgetzjf, 10000).toFixed(2);
                    }
                }
                if (cell.col == 8) {
                    cell.value="";
                //     cell.value = "美元";
                }
                if (cell.col == 9) {
                    cell.value="";
                //     cell.value = "折合人民币";
                }
            }
        }
    }


    //B.0.11 综合概算对比表
    async writeDataToSingleSheet2(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单项名称：");
        let constructNameImport = data.filter(object => object.name == "单项名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number].value = "单项名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let constructNameCell1 = GSExcelUtil.findValueCell(worksheet, "总概算编号：");
        let constructNameImport1 = data.filter(object => object.name == "项目编码")[0];
        let constructNameRow1 = worksheet.getRow(constructNameCell1.cell._row._number);
        if (constructNameImport1 != null && constructNameImport1.remark != null) {
            constructNameRow1._cells[constructNameCell1.cell._column._number].value = "总概算编号：" + constructNameImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow1);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].jzFee;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].gzFee;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].azFee;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].tzjzFee;
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].tzszgzFee;
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].tzazFee;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].tzprice;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].diffAmount;
                }
                if (cell.col == 12) {
                    cell.value = list[countRow].remark;
                }
            }
        }
    }


    /********单位工程层级*************/
    //1、封面
    async writeDataToUnitSheet1(data, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "（单项工程名称）+（单位工程名称）");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[constructNameCell.cell._column._number - 1].value = constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);


        //填充建设单位
        let constructNameCell1 = GSExcelUtil.findValueCell(worksheet, "（项目概况中建设单位名称）");
        let constructNameImport1 = data.filter(object => object.name == "建设单位信息-建设单位")[0];
        let constructNameRow1 = worksheet.getRow(constructNameCell1.cell._row._number);
        if (constructNameImport1 != null && constructNameImport1.remark != null) {
            constructNameRow1._cells[constructNameCell1.cell._column._number - 1].value = constructNameImport1.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow1);


        //填充工程规模
        let constructNameCell2 = GSExcelUtil.findValueCell(worksheet, "（工程特征中工程规模）");
        let constructNameImport2 = data.filter(object => object.name == "工程规模")[0];
        let constructNameRow2 = worksheet.getRow(constructNameCell2.cell._row._number);
        if (constructNameImport2 != null && constructNameImport2.remark != null) {
            constructNameRow2._cells[constructNameCell2.cell._column._number - 1].value = constructNameImport2.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow2);


        //填充工程造价
        let constructNameCell3 = GSExcelUtil.findValueCell(worksheet, "（造价分析中工程总造价数值，保留两位小数）");
        let constructNameImport3 = data.filter(object => object.name == "工程总造价（小写）")[0];
        let constructNameRow3 = worksheet.getRow(constructNameCell3.cell._row._number);
        if (constructNameImport3 != null && constructNameImport3.remark != null) {
            constructNameRow3._cells[constructNameCell3.cell._column._number - 1].value = constructNameImport3.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow3);

        //填充造价指标
        let constructNameCell4 = GSExcelUtil.findValueCell(worksheet, "（造价分析中单位造价数值，保留两位小数）");
        let constructNameImport4 = data.filter(object => object.name == "单方造价")[0];
        let constructNameRow4 = worksheet.getRow(constructNameCell4.cell._row._number);
        if (constructNameImport4 != null && constructNameImport4.remark != null) {
            constructNameRow4._cells[constructNameCell4.cell._column._number - 1].value = constructNameImport4.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow4);

        //填充编制日期
        let constructNameCell5 = GSExcelUtil.findValueCell(worksheet, "（工程概况-基本工程信息中编制时间）");
        let constructNameImport5 = data.filter(object => object.name == "编制时间")[0];
        let constructNameRow5 = worksheet.getRow(constructNameCell5.cell._row._number);
        if (constructNameImport5 != null && constructNameImport5.remark != null) {
            constructNameRow5._cells[constructNameCell5.cell._column._number - 1].value = constructNameImport5.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow5);
    }


    //3、【分部6】分部分项清单对比表(含关联项)
    async writeDataToUnitSheet3(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let listCopy = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    listCopy.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, listCopy, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].deCode;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].deName;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 8) {
                    if (ObjectUtils.isNotEmpty(list[countRow].dispNo)) {
                        cell.value = ObjectUtils.isNotEmpty(list[countRow].rgf) ? list[countRow].rgf : 0;
                    }
                }
                if (cell.col == 9) {
                    if (ObjectUtils.isNotEmpty(list[countRow].dispNo)) {
                        cell.value = ObjectUtils.isNotEmpty(list[countRow].clf) ? list[countRow].clf : 0;
                    }
                }
                if (cell.col == 10) {
                    if (ObjectUtils.isNotEmpty(list[countRow].dispNo)) {
                        cell.value = ObjectUtils.isNotEmpty(list[countRow].jxf) ? list[countRow].jxf : 0;
                    }
                }
            }
        }

        let {total} = data["unitDwgcgysba4sHeji"];
        //定位到最后的合计行
        let heJiCell = GSExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = total;
    }


    async writeDataToUnitSheet4(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let listCopy = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    listCopy.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, listCopy, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].deCode;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].deName;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 8) {
                    if (ObjectUtils.isNotEmpty(list[countRow].dispNo)) {
                        cell.value = ObjectUtils.isNotEmpty(list[countRow].rgf) ? list[countRow].rgf : 0;
                    }
                }
                if (cell.col == 9) {
                    if (ObjectUtils.isNotEmpty(list[countRow].dispNo)) {
                        cell.value = ObjectUtils.isNotEmpty(list[countRow].clf) ? list[countRow].clf : 0;
                    }
                }
                if (cell.col == 10) {
                    if (ObjectUtils.isNotEmpty(list[countRow].dispNo)) {
                        cell.value = ObjectUtils.isNotEmpty(list[countRow].jxf) ? list[countRow].jxf : 0;
                    }
                }
                if (cell.col == 11) {
                    if (ObjectUtils.isNotEmpty(list[countRow].dispNo)) {
                        cell.value = ObjectUtils.isNotEmpty(list[countRow].grhj) ? list[countRow].grhj : 0;
                    }
                }
            }
        }

        let {total} = data["unitDwgcgysba4hHeji"];
        //定位到最后的合计行
        let heJiCell = GSExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = total;
    }


    //5、【其他1】其他项目审核对比表
    async writeDataToUnitSheet5(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    // cell.value = list[countRow].dispNo;
                    cell.value = i + 1;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].instructions;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].rate;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].price;
                }
            }
        }
    }


    //6、【计日工1】计日工审核对比表
    async writeDataToUnitSheet6(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    // cell.value = list[countRow].dispNo;
                    cell.value = i + 1;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].instructions;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].rate;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].price;
                }
            }
        }

    }


    //7、【人材机2】人材机审核对比表
    async writeDataToUnitSheet7(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    // cell.value = list[countRow].dispNo;
                    cell.value = i + 1;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].instructions;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].rate;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].price;
                }
            }
        }
    }


    //8、单位工程人材机汇总表
    async writeDataToUnitSheet8(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    // cell.value = i + 1;
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].dePrice;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].total;
                }
            }
        }

        let {total, ystotal, sctotal} = data["unitRcjHeji"];
        //定位到最后的合计行
        let heJiCell = GSExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[4].value = ystotal;
        row._cells[5].value = sctotal;
        row._cells[6].value = total;
    }


    //9、单位工程人材机价差表（人材机汇总）
    async writeDataToUnitSheet9(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    // cell.value = i + 1;
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialName;
                    if (ObjectUtils.isNotEmpty(list[countRow].specification)) {
                        cell.value = list[countRow].materialName + "\t\n" + list[countRow].specification;
                    }
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].dePrice;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].priceDifferenc;
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].priceDifferencSum;
                }
            }
        }

        let {ystotal, sctotal, jc, jctotal} = data["unitRcjjcHeji"];
        //定位到最后的合计行
        let heJiCell = GSExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[4].value = ystotal;
        row._cells[5].value = sctotal;
        row._cells[6].value = jc;
        row._cells[7].value = jctotal;
    }


    //10、单位工程三材汇总表
    async writeDataToUnitSheet10(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i + 1;
                    // cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialName;
                    if (ObjectUtils.isNotEmpty(list[countRow].specification)) {
                        cell.value = list[countRow].materialName + "\t\n" + list[countRow].specification;
                    }
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].totalNumber;
                }
            }
        }
    }


    //11、单位工程主材表
    async writeDataToUnitSheet11(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i + 1;
                    // cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialName;
                    if (ObjectUtils.isNotEmpty(list[countRow].specification)) {
                        cell.value = list[countRow].materialName + "\t\n" + list[countRow].specification;
                    }
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].total;
                }
            }
        }
    }


    //12、【工程量1】审定工程量计算书
    async writeDataToUnitSheet12(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i + 1;
                    // cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    if (ObjectUtils.isNotEmpty(list[countRow].specification)) {
                        cell.value = list[countRow].materialName + "\t\n" + list[countRow].specification;
                    } else {
                        cell.value = list[countRow].materialName;
                    }
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].total;
                }
            }
        }
    }


    //11、【安全文施1】安全文明施工费明细对比表
    async writeDataToUnitSheet13(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].totalPrice;
                }
            }
        }


        let {total} = data["unitDlfHeji"];
        //定位到最后的合计行
        let heJiCell = GSExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = total;
    }


    async writeDataToUnitSheet14(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i + 1;
                    // cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialCode;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].specification;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].dePrice;
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].total;
                }
            }
        }
    }

    

    async writeDataToUnitSheet15(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = GSExcelUtil.findValueCell(worksheet, "单位名称：");
        let constructNameImport = data.filter(object => object.name == "单位名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "单位名称：" + constructNameImport.remark;
        }
        GSExcelUtil.traversalRowToCellBottom(constructNameRow);

        let headCount = 4;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let listCopy = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    listCopy.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, listCopy, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = GSExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].deCode;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].deName;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 6) {
                    // cell.value = list[countRow].price;
                }
                if (cell.col == 7) {
                    // cell.value = list[countRow].totalNumber;
                }
            }
        }

        // let {total} = data["unitDwqfzbfxbHeji"];
        // //定位到最后的合计行
        // let heJiCell = GSExcelUtil.findValueCell(worksheet, "合计");
        // let row = worksheet.getRow(heJiCell.cell._row._number);
        // row._cells[6].value = total;
    }


}

module.exports = {
    ShenHeWriteExcelBySheetUtil: new GSWriteExcelBySheetUtil()
};
