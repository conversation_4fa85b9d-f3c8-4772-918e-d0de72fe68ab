const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {FreeRateProjectModel} = require("../models/FreeRateProjectModel");
const {FreeRateModel} = require("../models/FreeRateModel");
const {Service} = require('../../../core');
const ProjectDomain = require("../domains/ProjectDomain");
const WildcardMap = require("../core/container/WildcardMap");
const {ObjectUtils} = require("../utils/ObjectUtils");

class GljInitProjectService extends Service {

    constructor(ctx) {
        super(ctx);

    }

    /**
     * 工程项目
     */
    async init(projectModel, constructId) {
        // 初始化项目基本信息
        await this.initProjectBasicInfo(projectModel, constructId);
        // 初始化项目取费表费率
        await this.initProjectFreeRates(constructId);
        // 初始化工程量明细
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QUANTITIES, new Map());
        // 初始化标准换算
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_CONVERSION, new Map());
        // 初始化定额说明
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_DE_CONTENT, new Map());
        // 初始化人材机主要材料设置
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.MAIN_MATERIAL_SETTING, new Map());
    }

    /**
     * 初始化项目基本信息
     * @returns {Promise<void>}
     */
    async initProjectBasicInfo(projectModel, constructId) {
        let list = await this.service.gongLiaoJiProject.gljOverviewService.initDataProject(projectModel);
        // ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY)
        //     .set(this.service.gongLiaoJiProject.gljOverviewService.getDataMapKey(null, FunctionTypeConstants.JBXX_KEY_TYPE_11), list);
    }

    /**
     * 初始化项目取费表费率
     * @returns {Promise<void>}
     */
    async initProjectFreeRates(constructId) {

        let freeRateProjectModel = await this.service.gongLiaoJiProject.gljFreeRateService.initFreeDescribe();
        freeRateProjectModel.constructId = constructId;

        //初始化费率总览
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, new FreeRateProjectModel());
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, new Map());
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, new Map());
        //初始化费率说明
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_FLSM, freeRateProjectModel);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_FLSM, new Map());
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_FLSM, new Map());
    }

    /**
     * 修改计税方式后初始化
     * @returns {Promise<void>}
     */
    async initProjectFreeRatesV1(constructId) {
        let args={};
        let qfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        if(ObjectUtils.isEmpty(qfbMap.childFreeRate)){
            return ;
        }
        let keys = Array.from(qfbMap.childFreeRate.keys());
        for(const key of keys){
            //获取每一个取费数据
            let qfObj = qfbMap.childFreeRate.get(key);
            args.libraryCode=qfObj.libraryCode;
            args.type=1;
            args.constructId=constructId;
            args.freeFile=qfObj;
            //恢复默认费率
            await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateDataV1(args);
            //更新取费表数据
            // ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, unitQfbMap.set(freeKey, newVar));
        }
    }


    /**
     * 初始化所有取费
     * @param constructId
     * @returns {Promise<void>}
     */
    async initProjectAllFreeRate(constructId) {
        // 工程项目-取费表
        await this.initProjectFreeRatesV1(constructId);
        // 单项工程-取费表
        let singleList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectSingleAll(constructId);
        for (let single of singleList) {
            await this.service.gongLiaoJiProject.gljInitSingleProjectService.initProjectSingleFreeRatesV1(constructId, single.sequenceNbr);
        }
        // 单位工程-取费表
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectUnitAll(constructId);
        for (let unit of unitList) {
            let args={constructId,unitId:unit.sequenceNbr};
            let freeRateModel = await this.service.gongLiaoJiProject.gljBaseFreeRateService.queryFreeRateModel(3,constructId,unit.parentId,unit.sequenceNbr);
            if (ObjectUtils.isNotEmpty(freeRateModel)) {
                await this.service.gongLiaoJiProject.gljFreeRateService.recoverUnitFreeRate(args);
            }
        }
    }

    /**
     * 定额重新计算
     * @param constructId
     * @returns {Promise<void>}
     */
    async recalculateDe(constructId) {
        // 单位工程
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectUnitAll(constructId);
        //定额计算获取所有定额
        let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        // let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        for (let unit of unitList) {
            deBaseDomain.notifyAll(constructId,unit.sequenceNbr);
            // csxmDomain.notifyAll(constructId,unit.sequenceNbr);
        }
    }

    /**
     * 人材机汇总重新计算
     * @param constructId
     * @returns {Promise<void>}
     */
    async recalculateRcjCollect(constructId) {
        // 人材机汇总甲供价格重新计算
        // await this.recalculateRcjCollectDonorMaterialPrice(constructId);
    }

    /**
     * 费用汇总重新计算
     * @param constructId
     * @returns {Promise<void>}
     */
    async recalculateCostSummary(constructId) {
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectUnitAll(constructId);
        for (let unit of unitList) {
            let args= {
                constructId: constructId,
                singleId: unit.parentId,
                unitId: unit.sequenceNbr,
                qfMajorType: null
            };
            // 对专业类型进行费用代码，并对费用汇总填写数据
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice(args);
        }
    }


    /**
     * 基本工程信息-项目名称重刷
     * @param constructId
     * @returns {Promise<void>}
     */
    async recalculateOverview(constructId) {
        // 获取工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // 获取项目层级基本信息
        let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(
            await this.service.gongLiaoJiProject.gljOverviewService.getDataMapKey(null, FunctionTypeConstants.JBXX_KEY_TYPE_11));
        list.find(item => item.name === '项目名称').remark = constructProject.name;
    }

    async recalculateCostMatch(constructId) {
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectUnitAll(constructId);
        for (let unit of unitList) {
            await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
                unitId: unit.sequenceNbr,
                singleId: null,
                constructId: constructId
            });
        }
    }


    /**
     * 人材机汇总甲供价重新计算
     * @param constructId
     * @returns {Promise<void>}
     */
    async recalculateRcjCollectDonorMaterialPrice(constructId) {
        // 人材机汇总甲供价格重新计算
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectUnitAll(constructId);
        let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let pricingMethod = ProjectDomain.getDomain(constructId).getRoot().pricingMethod
        for (let unit of unitList) {
            let rcjKey = WildcardMap.generateKey(unit.sequenceNbr) + WildcardMap.WILDCARD;
            let rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
            for (let rcj of rcjList) {
                if (ObjectUtils.isNotEmpty(rcj.donorMaterialPrice)) {
                    if (taxMethod === 1 && pricingMethod === 0) { // 一般计税且非市场价组价，取不含税基期价
                        rcj.donorMaterialPrice =rcj.baseJournalPrice;
                    }else if (taxMethod === 1 && pricingMethod === 1) { // 一般计税且市场价组价，取不含税市场价
                        rcj.donorMaterialPrice =rcj.marketPrice;
                    }else if (taxMethod === 0 && pricingMethod === 0) { // 简易计税且非市场价组价，取含税基期价
                        rcj.donorMaterialPrice =rcj.baseJournalTaxPrice;
                    }else if (taxMethod === 0 && pricingMethod === 1) { // 简易计税且市场价组价，取含税市场价
                        rcj.donorMaterialPrice =rcj.marketTaxPrice;
                    }
                }
            }
        }
    }



    /**
     * 刷新所有费用汇总
     * @param constructId
     * @returns {Promise<void>}
     */
    async initProjectAllCostSummary(constructId) {
        // 单位工程-取费表
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectUnitAll(constructId);
        for (let unit of unitList) {
            let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
            let freeRate = unitQfbMap.get(unit.sequenceNbr);
            if (freeRate) {
                await this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unit.sequenceNbr, freeRate);
            }
        }
    }

        /**
     * 移除工程项目
     * @param pid
     * @param constructId
     */
    async remove(pid, constructId) {
        //删除单位级别人材机主要材料设置
        let newVar = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.MAIN_MATERIAL_SETTING);
        if(ObjectUtils.isNotEmpty(newVar)){
            newVar.delete(pid);
        }
    }

}

GljInitProjectService.toString = () => '[class GljInitProjectService]';
module.exports = GljInitProjectService;
