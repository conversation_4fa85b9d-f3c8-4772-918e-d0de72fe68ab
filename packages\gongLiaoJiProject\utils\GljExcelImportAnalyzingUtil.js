const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("./ObjectUtils");
const ExcelEnum = require("../enums/GljExcelEnum");
const {DateUtils} = require("./DateUtils");
const XLSX = require('xlsx');
const Excel = require("exceljs");
const xeUtils = require("xe-utils");
const {Snowflake} = require("../utils/Snowflake");
const _ = require('lodash');
const {GljExcelUtil} = require("./GljExcelUtil");
class GljExcelImportAnalyzingUtil {

    async _readFm(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [2, 2], {col: "项目名称", contents: ["合计", "小计"]})
    }

    async _readBzsm(rows, sheetName) {
        return await this._readReal(rows, sheetName, 4, [2, 2], {col: "项目名称", contents: ["合计", "小计"]})
    }

    async _readStxmysb(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "子目名称", contents: ["合计", "小计"]})
    }

    async _readCsxmysb(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "子目名称", contents: ["合计", "小计"]})
    }

    async _readDlfb(rows, sheetName) {
        return await this._readReal(rows, sheetName, 4, [3, 3], {col: "费用名称", contents: ["合计", "小计"]})
    }

    async _readDwgcjgclb(rows, sheetName) {
        return await this._readReal(rows, sheetName, 4, [3, 3], {col: "名称及规格", contents: ["合计", "小计"]})
    }

    async _readDwgcschzb(rows, sheetName) {
        return await this._readReal(rows, sheetName, 4, [3, 3], {col: "材料名称", contents: ["合计", "小计"]})
    }

    async _readFxgcrcjhzbSt(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "子目名称", contents: ["合计", "小计"]})
    }

    async _readFxgcrcjhzbCs(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "子目名称", contents: ["合计", "小计"]})
    }

    async _readDwgcgcljssSt(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "子目名称", contents: ["合计", "小计"]})
    }

    async _readDwgcgcljssCs(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "子目名称", contents: ["合计", "小计"]})
    }

    async _readSdfmxb(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "专业名称", contents: ["合计", "小计"]})
    }

    async _readSdfmxbDlsz(rows, sheetName) {
        return await this._readReal(rows, sheetName, 5, [3, 4], {col: "专业名称", contents: ["合计", "小计"]})
    }



    async _readReal(rows, sheetName, headerNumber, titleRows, amountTo) {
        let result = [];
        //获取数据
        //const rows = ExcelUtil.readSheetContentByWorkBook(workbook, sheetName);
        if(ObjectUtils.isEmpty(rows)){
            return result;
        }

        // 表格列标题获取
        let titleMap = await this._getTitleMap(rows, titleRows);
        if(sheetName === "水电费明细表"){
            titleMap = await this._getTitleMapSdf(rows, titleRows);
        }

        let n = headerNumber;

        // 循环处理每行数据
        while (n < rows.length) {
            const r = rows[n];
            let newData = {};
            let ifNotEmptyRow = false;

            // 处理分部分项行数据，生成目标数据
            for(let [key, val] of titleMap){
                newData[val] = ObjectUtils.isEmpty(r.get(key)) ? null : ("" + r.get(key)).replaceAll(/^\s+|\s+$/g, "");
                ifNotEmptyRow = ifNotEmptyRow || ObjectUtils.isNotEmpty(newData[val]);
            }


            let xmmc = ObjectUtils.isNotEmpty(newData[amountTo.col]) ? newData[amountTo.col].replaceAll(/\s/g, "") : "";
            if(amountTo.contents.includes(xmmc)){
                n += 1;
                continue;
            }


            // 如果不是拆分的新行，并且不是空行，加入到结果结果集中
            if (ifNotEmptyRow) {
                result.push(newData);
            }

            n++;
        }

        return result;
    }

    async _getTitleMap(rows, titleRows) {
        // 表格列标题获取
        let titleMap = new Map();
        for (let i = titleRows[0]; i <= titleRows[1]; i++) {
            const r = rows[i];
            for (let [key, val] of r) {
                if (ObjectUtils.isNotEmpty(val)) {
                    val = val.replaceAll(/\s/g, "");
                    titleMap.set(key, val);
                }
            }
        }
        return titleMap;
    }

    async _getTitleMapSdf(rows, titleRows) {
        // 表格列标题获取
        let titelArr = [];
        let titleMap = new Map();
        for (let i = titleRows[0]; i <= titleRows[1]; i++) {
            const r = rows[i];
            for (let [key, val] of r) {
                if (ObjectUtils.isNotEmpty(val)) {
                    val = val.replaceAll(/\s/g, "");
                    if (i === titleRows[1]) {
                        if (titelArr.includes(val)) {
                            titleMap.set(key, val + "1");
                            titelArr.push(val);
                        } else {
                            titleMap.set(key, val);
                            titelArr.push(val);
                        }
                    } else {
                        titleMap.set(key, val);
                        titelArr.push(val);
                    }
                }
            }
        }
        return titleMap;
    }

}
module.exports = {
    GljExcelImportAnalyzingUtil: new GljExcelImportAnalyzingUtil()
};




