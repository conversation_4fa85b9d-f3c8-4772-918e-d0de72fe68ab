const ProjectDomain = require("../domains/ProjectDomain");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {GljCostAnalysisUnitVO} = require("../models/GljCostAnalysisUnitVO");
const gljZjfx = require('../jsonData/glj_zjfx.json');
const gljZjfxType = require('../jsonData/glj_zjfx_type.json');
const CostAnalysisTypeConstants = require("../constants/CostAnalysisTypeConstants");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const {GljCostAnalysisSingleVO} = require("../models/GljCostAnalysisSingleVO");
const {GljCostAnalysisConstructVO} = require("../models/GljCostAnalysisConstructVO");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {GljCostAnalysisVO} = require("../models/GljCostAnalysisVO");
const {Service} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const UtilsPs = require("../../../core/ps");
const {FileUtils} = require("../utils/FileUtils");
const {dialog} = require("electron");
const XLSX = require("xlsx");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ArrayUtil} = require("../../../common/ArrayUtil");

/**
 * 造价分析  service
 */
class GljCostAnalysisService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取造价分析
     * @param args
     * @returns {Promise<GljCostAnalysisVO>}
     */
    async getCostAnalysisData(args) {
        let {constructId, singleId, unitId, type} = args;
        //返回对象
        let costAnalysisVO = new GljCostAnalysisVO();
        // 工程项目层级
        if (type === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            costAnalysisVO.costAnalysisConstructVOList = await this.generateConstructCostAnalysisData(constructId);
            // 单项层级
        } else if (type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            let singleProject = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === singleId
                && item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE);
            costAnalysisVO.costAnalysisSingleVOList = await this.generateSingleCostAnalysisData(constructId, singleProject[0], "1");
            // 单位层级
        } else if (type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            costAnalysisVO.costAnalysisUnitVOList = await this.generateUnitCostAnalysisData(constructId, singleId, unitId);
        }
        return costAnalysisVO;
    }

    /**
     * 获去当前单项下的所有子集造价分析
     * @param constructId
     * @param singleProject
     * @param dispNo
     * @returns {any[]}
     */
    async generateSingleCostAnalysisData(constructId, singleProject, dispNo) {
        let singleCostAnalysisArray = [];
        let singleCostAnalysis = new GljCostAnalysisSingleVO();
        // 子单项
        let singleProjects = singleProject.children;
        singleCostAnalysis.dispNo = dispNo;
        singleCostAnalysis.sequenceNbr = singleProject.sequenceNbr;
        singleCostAnalysis.projectName = singleProject.name;
        singleCostAnalysis.average = ObjectUtils.isNotEmpty(singleProject.average) ? singleProject.average : 0;  // //工程规模
        singleCostAnalysis.levelType = singleProject.levelType;  //工程项目

        singleCostAnalysis.budgetzjf = 0; // 预算书-直接费 合计
        singleCostAnalysis.ysrgf = 0;   // 其中-人工费
        singleCostAnalysis.ysclf = 0;   // 其中-材料费
        singleCostAnalysis.ysjxf = 0;   // 其中-机械费
        singleCostAnalysis.yssbf = 0;   // 其中-设备费
        singleCostAnalysis.yszcf = 0;   // 其中-主材费

        singleCostAnalysis.csxmf = 0;   // 措施项目费
        singleCostAnalysis.csrgf = 0;   // 措施-人工费
        singleCostAnalysis.csclf = 0;   // 措施-材料费
        singleCostAnalysis.csjxf = 0;   // 措施-机械费
        singleCostAnalysis.cssbf = 0;   // 措施-设备费
        singleCostAnalysis.cszcf = 0;   // 措施-主材费

        singleCostAnalysis.qyglf = 0;  // 企业管理费
        singleCostAnalysis.lr = 0;  // 利润
        singleCostAnalysis.dlf = 0; // 独立费
        singleCostAnalysis.aqwmsgf = 0; // 安全生产、文明施工费
        singleCostAnalysis.sj = 0;  // 税金
        singleCostAnalysis.unitcost = 0;  // 单方工程造价
        singleCostAnalysis.costProportion = 0.00;  // 造价占比
        singleCostAnalysis.childrenList = [];
        let allSubSingles = [];
        // 遍历添加造价分析数据
        await this.getSingleLevelCostAnalysis(singleProjects, singleCostAnalysis, dispNo, constructId, allSubSingles);
        singleCostAnalysisArray.push(singleCostAnalysis);

        // 对子项单项进行向上累加
        await this.getSubSingleCostAnalysisDataTotal(constructId, allSubSingles, singleProject);
        return singleCostAnalysisArray;
    }

    /**
     * 获取当前单项的所有层级
     * @param singleProjects
     * @param singleCostAnalysis
     * @param dispNo
     * @param constructId
     * @param allSubSingles
     */
    async getSingleLevelCostAnalysis(singleProjects, singleCostAnalysis, dispNo, constructId, allSubSingles) {
        let subSingleCostAnalysis = [];
        // 当该层是单位时，获取所有单位造价分析
        let units = singleProjects.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
        if (ObjectUtils.isNotEmpty(units)) {
            let commonCostAnalysis = {};
            singleCostAnalysis.childrenList = await this.generateUnitsCostAnalysisData(units, dispNo, constructId, commonCostAnalysis);
            singleCostAnalysis.projectCost = commonCostAnalysis.projectCost;
            singleCostAnalysis.jzgcProjectCost = commonCostAnalysis.jzgcProjectCost;
            singleCostAnalysis.zsgcProjectCost = commonCostAnalysis.zsgcProjectCost;
            singleCostAnalysis.azgcProjectCost = commonCostAnalysis.azgcProjectCost;
            singleCostAnalysis.szgcProjectCost = commonCostAnalysis.szgcProjectCost;
            singleCostAnalysis.yllhProjectCost = commonCostAnalysis.yllhProjectCost;
            singleCostAnalysis.fggcProjectCost = commonCostAnalysis.fggcProjectCost;
            singleCostAnalysis.xsgcProjectCost = commonCostAnalysis.xsgcProjectCost;
            singleCostAnalysis.yhgcProjectCost = commonCostAnalysis.yhgcProjectCost;
            // 添加单项层级
            allSubSingles.push(singleCostAnalysis);
        } else {
            // 当该层不是单位是，进行层级遍历，获取单项层级
            if (ObjectUtils.isNotEmpty(singleProjects)) {
                for (let i = 0; i < singleProjects.length; i++) {
                    let subSingleProject = singleProjects[i];
                    let singleCostAnalysisVO = new GljCostAnalysisSingleVO();
                    singleCostAnalysisVO.sequenceNbr = subSingleProject.sequenceNbr;
                    singleCostAnalysisVO.dispNo = dispNo + "." + (i + 1);
                    singleCostAnalysisVO.projectName = subSingleProject.name;
                    singleCostAnalysisVO.average = ObjectUtils.isNotEmpty(subSingleProject.average) ? subSingleProject.average : 0;

                    if (ObjectUtils.isNotEmpty(subSingleProject.children)) {
                        // 当该单项下是单位，且不包含单项  注：单项下只能包括子单项或者全是单位
                        let units = subSingleProject.children.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.parentId === subSingleProject.sequenceNbr);
                        if (ObjectUtils.isNotEmpty(units)) {
                            subSingleCostAnalysis = await this.getSubSingleContainUnitsCostAnalysisData(constructId, subSingleProject, singleCostAnalysisVO.dispNo);
                            if (ObjectUtils.isEmpty(singleCostAnalysis.childrenList)) {
                                singleCostAnalysis.childrenList = subSingleCostAnalysis;
                            } else {
                                singleCostAnalysis.childrenList.push(...subSingleCostAnalysis);
                            }
                            // 添加单项层级
                            allSubSingles.push(singleCostAnalysis);
                            allSubSingles.push(singleCostAnalysisVO);
                            // 当该单项下不是单位，是单项，继续获取单项层级
                        } else {
                            // 封装造价层级数据
                            subSingleCostAnalysis.push(singleCostAnalysisVO);
                            singleCostAnalysis.childrenList = subSingleCostAnalysis;
                            // 添加单项层级
                            allSubSingles.push(singleCostAnalysis);
                            allSubSingles.push(singleCostAnalysisVO);
                            await this.getSingleLevelCostAnalysis(subSingleProject.children, singleCostAnalysisVO, singleCostAnalysisVO.dispNo, constructId, allSubSingles);
                        }
                    } else {
                        // 处理单项下没有子单项、单位的情况
                        subSingleCostAnalysis = await this.getSubSingleContainUnitsCostAnalysisData(constructId, subSingleProject, singleCostAnalysisVO.dispNo);
                        if (ObjectUtils.isEmpty(singleCostAnalysis.childrenList)) {
                            singleCostAnalysis.childrenList = subSingleCostAnalysis;
                        } else {
                            singleCostAnalysis.childrenList.push(...subSingleCostAnalysis);
                        }
                        allSubSingles.push(singleCostAnalysis);
                        allSubSingles.push(singleCostAnalysisVO);
                    }
                }
            } else {
                singleCostAnalysis.projectCost = 0;
                singleCostAnalysis.jzgcProjectCost = 0;
                singleCostAnalysis.zsgcProjectCost = 0;
                singleCostAnalysis.azgcProjectCost = 0;
                singleCostAnalysis.szgcProjectCost = 0;
                singleCostAnalysis.yllhProjectCost = 0;
                singleCostAnalysis.fggcProjectCost = 0;
                singleCostAnalysis.xsgcProjectCost = 0;
                singleCostAnalysis.yhgcProjectCost = 0;
            }
        }
    }


    /**
     * 对同一层级的子单项造价分析数据进行汇总
     * @param constructId
     * @param allSubSingles  该单项所有的子单项
     * @param subSingleProject
     * @returns {Promise<GljCostAnalysisSingleVO>} 返回该单项及子单项的造价分析数据
     */
    async getSubSingleCostAnalysisDataTotal(constructId, allSubSingles, subSingleProject) {

        let projectCost = 0;  // 工程造价
        let jzgcProjectCost = 0;  // 建筑工程造价
        let zsgcProjectCost = 0;  // 装饰工程造价
        let azgcProjectCost = 0;  // 安装工程造价
        let szgcProjectCost = 0;  // 市政工程造价
        let yllhProjectCost = 0;  // 园林绿化工程
        let fggcProjectCost = 0;  // 仿古建筑工程
        let xsgcProjectCost = 0;  // 古建（明清）修缮工程
        let yhgcProjectCost = 0;  // 市政设施维修养护工程

        let budgetzjf = 0; // 预算书-直接费 合计
        let ysrgf = 0;   // 其中-人工费
        let ysclf = 0;   // 其中-材料费
        let ysjxf = 0;   // 其中-机械费
        let yssbf = 0;   // 其中-设备费
        let yszcf = 0;   // 其中-主材费

        let csxmf = 0;   // 措施项目费
        let csrgf = 0;   // 措施-人工费
        let csclf = 0;   // 措施-材料费
        let csjxf = 0;   // 措施-机械费
        let cssbf = 0;   // 措施-设备费
        let cszcf = 0;   // 措施-主材费

        let qyglf = 0;  // 企业管理费
        let lr = 0;  // 利润
        let dlf = 0; // 独立费
        let aqwmsgf = 0; // 安全生产、文明施工费
        let sj = 0;  // 税金

        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // 获取该单项下的所有单位
        let unitProjectsByConstruct = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
        // 整个项目的总工程造价值
        let constructCost = 0;
        for (let j = 0; j < unitProjectsByConstruct.length; j++) {
            let unitProject = unitProjectsByConstruct[j];
            // 获取该单位的费用汇总
            let param = {
                constructId: constructId,
                singleId: unitProject.parentId,
                unitId: unitProject.sequenceNbr
            }
            let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
            constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
        }

        // 获取该单项下的所有单位
        let unitProjects = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(subSingleProject.children, unitProjects);
        // 存放所有单位对应的造价分析的和
        if (ObjectUtils.isNotEmpty(unitProjects)) {
            for (let j = 0; j < unitProjects.length; j++) {
                let unitProject = unitProjects[j];

                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                // 获取费用代码
                let unitCostCodePriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostCode(param);
                // 获取单位工程费用汇总
                let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);

                //单项合计计算
                budgetzjf = NumberUtil.add(budgetzjf, unitCostCodePriceMap.get("ZJF"));  // 预算书-直接费
                ysrgf = NumberUtil.add(ysrgf, unitCostCodePriceMap.get("RGF"));  // 其中-人工费
                ysclf = NumberUtil.add(ysclf, unitCostCodePriceMap.get("CLF"));  // 其中-材料费
                ysjxf = NumberUtil.add(ysjxf, unitCostCodePriceMap.get("JXF"));  // 其中-机械费
                yszcf = NumberUtil.add(yszcf, unitCostCodePriceMap.get("ZCF"));  // 其中-主材费
                yssbf = NumberUtil.add(yssbf, unitCostCodePriceMap.get("SBF"));  // 其中-设备费

                csxmf = NumberUtil.add(csxmf, unitCostCodePriceMap.get("CSXMHJ"));    // 其他措施费
                csrgf = NumberUtil.add(csrgf, unitCostCodePriceMap.get("JSCS_RGF"));  // 措施-人工费
                csclf = NumberUtil.add(csclf, unitCostCodePriceMap.get("JSCS_CLF"));  // 措施-材料费
                csjxf = NumberUtil.add(csjxf, unitCostCodePriceMap.get("JSCS_JXF"));  // 措施-机械费
                cssbf = NumberUtil.add(cssbf, unitCostCodePriceMap.get("JSCS_ZCF"));  // 措施-主材费
                cszcf = NumberUtil.add(cssbf, unitCostCodePriceMap.get("JSCS_SBF"));  // 措施-设备费

                dlf = NumberUtil.add(dlf, unitCostCodePriceMap.get("DLF"));  // 独立费
                qyglf = NumberUtil.add(qyglf, unitCostSummaryPriceMap.get("企业管理费"));  // 企业管理费
                lr = NumberUtil.add(lr, unitCostSummaryPriceMap.get("利润"));  // 利润
                aqwmsgf = NumberUtil.add(aqwmsgf, unitCostSummaryPriceMap.get("安全文明施工费"));  // 安全生产、文明施工费
                sj = NumberUtil.add(sj, unitCostSummaryPriceMap.get("税金"));  // 税金

                let unitjzgcProjectCostTotal = 0, unitzsgcProjectCostTotal = 0, unitazgcProjectCostTotal = 0,
                    unitszgcProjectCostTotal = 0, unityllhProjectCostTotal = 0, unitfggcProjectCostTotal = 0,
                    unitxsgcProjectCostTotal = 0, unityhgcProjectCostTotal = 0;
                // 获取该单位的所有取费专业
                let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));
                let qfMajorTypeParam = {
                    qfMajorTypes: Array.from(qfMajorTypeMoneyMap.keys())
                }
                // 查询满足qfCode数据，并进行libarayCode分组
                let groupCostAnalysisTypeMap = await this.getTypeByCostAnalysisType(qfMajorTypeParam);
                // 遍历groupLibaryCodeMap的key和value
                for (let [key, value] of groupCostAnalysisTypeMap) {
                    if (key === CostAnalysisTypeConstants.ZJFZ_JZGC) {
                        value.forEach(item => {
                            unitjzgcProjectCostTotal = NumberUtil.add(unitjzgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_ZSGC) {
                        value.forEach(item => {
                            unitzsgcProjectCostTotal = NumberUtil.add(unitzsgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_AZGC) {
                        value.forEach(item => {
                            unitazgcProjectCostTotal = NumberUtil.add(unitazgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_SZGC) {
                        value.forEach(item => {
                            unitszgcProjectCostTotal = NumberUtil.add(unitszgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_YLGC) {
                        value.forEach(item => {
                            unityllhProjectCostTotal = NumberUtil.add(unityllhProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_FGGC) {
                        value.forEach(item => {
                            unitfggcProjectCostTotal = NumberUtil.add(unitfggcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_XSGC) {
                        value.forEach(item => {
                            unitxsgcProjectCostTotal = NumberUtil.add(unitxsgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_YHGC) {
                        value.forEach(item => {
                            unityhgcProjectCostTotal = NumberUtil.add(unityhgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                }
                jzgcProjectCost = NumberUtil.add(jzgcProjectCost, unitjzgcProjectCostTotal);
                zsgcProjectCost = NumberUtil.add(zsgcProjectCost, unitzsgcProjectCostTotal);
                azgcProjectCost = NumberUtil.add(azgcProjectCost, unitazgcProjectCostTotal);
                szgcProjectCost = NumberUtil.add(szgcProjectCost, unitszgcProjectCostTotal);
                yllhProjectCost = NumberUtil.add(yllhProjectCost, unityllhProjectCostTotal);
                fggcProjectCost = NumberUtil.add(fggcProjectCost, unitfggcProjectCostTotal);
                xsgcProjectCost = NumberUtil.add(xsgcProjectCost, unitxsgcProjectCostTotal);
                yhgcProjectCost = NumberUtil.add(yhgcProjectCost, unityhgcProjectCostTotal);
            }
        }
        projectCost = NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost, yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost);

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let jzgm = precision.COST_ANALYSIS.jzgm;

        for (let i = 0; i < allSubSingles.length; i++) {
            let singleCostAnalysisVO = allSubSingles[i];

            singleCostAnalysisVO.projectCost = projectCost;
            singleCostAnalysisVO.jzgcProjectCost = jzgcProjectCost;
            singleCostAnalysisVO.zsgcProjectCost = zsgcProjectCost;
            singleCostAnalysisVO.azgcProjectCost = azgcProjectCost;
            singleCostAnalysisVO.szgcProjectCost = szgcProjectCost;
            singleCostAnalysisVO.yllhProjectCost = yllhProjectCost;
            singleCostAnalysisVO.fggcProjectCost = fggcProjectCost;
            singleCostAnalysisVO.xsgcProjectCost = xsgcProjectCost;
            singleCostAnalysisVO.yhgcProjectCost = yhgcProjectCost;

            singleCostAnalysisVO.budgetzjf = budgetzjf;
            singleCostAnalysisVO.ysrgf = ysrgf;
            singleCostAnalysisVO.ysclf = ysclf;
            singleCostAnalysisVO.ysjxf = ysjxf;
            singleCostAnalysisVO.yssbf = yssbf;
            singleCostAnalysisVO.yszcf = yszcf;

            singleCostAnalysisVO.csxmf = csxmf;
            singleCostAnalysisVO.csrgf = csrgf;
            singleCostAnalysisVO.csclf = csclf;
            singleCostAnalysisVO.csjxf = csjxf;
            singleCostAnalysisVO.cssbf = cssbf;
            singleCostAnalysisVO.cszcf = cszcf;

            singleCostAnalysisVO.qyglf = qyglf;
            singleCostAnalysisVO.lr = lr;
            singleCostAnalysisVO.dlf = dlf;
            singleCostAnalysisVO.aqwmsgf = aqwmsgf;
            singleCostAnalysisVO.sj = sj;

            singleCostAnalysisVO.average = ObjectUtils.isNotEmpty(subSingleProject.average) ? subSingleProject.average : 0;
            singleCostAnalysisVO.unitcost = NumberUtil.divide(NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost,
                yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost), NumberUtil.numberScale(singleCostAnalysisVO.average, jzgm));  // 单方造价(元/m、元/㎡);
            singleCostAnalysisVO.costProportion = NumberUtil.divide(NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost,
                yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost), constructCost) * 100; // 造价占比
            singleCostAnalysisVO.levelType = ProjectTypeConstants.PROJECT_TYPE_SINGLE;
        }
    }

    /**
     * 获取指定单项下的所有的单位
     * @param constructId
     * @param subSingleProject  子单项
     * @param dispNo
     * @returns []
     */
    async getSubSingleContainUnitsCostAnalysisData(constructId, subSingleProject, dispNo) {

        let projectCost = 0; // 工程造价
        let jzgcProjectCost = 0;  // 建筑工程造价
        let zsgcProjectCost = 0;  // 装饰工程造价
        let azgcProjectCost = 0;  // 安装工程造价
        let szgcProjectCost = 0;  // 市政工程造价
        let yllhProjectCost = 0;  // 园林绿化工程
        let fggcProjectCost = 0;  // 仿古建筑工程
        let xsgcProjectCost = 0;  // 古建（明清）修缮工程
        let yhgcProjectCost = 0;  // 市政设施维修养护工程

        let budgetzjf = 0; // 预算书-直接费 合计
        let ysrgf = 0;   // 其中-人工费
        let ysclf = 0;   // 其中-材料费
        let ysjxf = 0;   // 其中-机械费
        let yssbf = 0;   // 其中-设备费
        let yszcf = 0;   // 其中-主材费

        let csxmf = 0;   // 其他措施费
        let csrgf = 0;   // 措施-人工费
        let csclf = 0;   // 措施-材料费
        let csjxf = 0;   // 措施-机械费
        let cssbf = 0;   // 措施-设备费
        let cszcf = 0;   // 措施-主材费

        let qyglf = 0;  // 企业管理费
        let lr = 0;   // 利润
        let dlf = 0;  // 独立费
        let aqwmsgf = 0; // 安全生产、文明施工费
        let sj = 0;  // 税金

        // 指定单项下的所有单位
        let unitProjectsBySingle = subSingleProject.children;
        // 整个项目的总工程造价值
        let constructCost = 0;
        if (ObjectUtils.isNotEmpty(unitProjectsBySingle)) {
            // 获取该工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
            // 获取该单项下的所有单位
            let unitProjectsByConstruct = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);

            for (let j = 0; j < unitProjectsByConstruct.length; j++) {
                let unitProject = unitProjectsByConstruct[j];
                // 获取该单位的费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
                constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
            }

            for (let j = 0; j < unitProjectsBySingle.length; j++) {
                let unitProject = unitProjectsBySingle[j];
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                // 获取费用代码
                let unitCostCodePriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostCode(param);
                // 获取单位工程费用汇总
                let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);

                //单项合计计算
                budgetzjf = NumberUtil.add(budgetzjf, unitCostCodePriceMap.get("ZJF"));  // 预算书-直接费
                ysrgf = NumberUtil.add(ysrgf, unitCostCodePriceMap.get("RGF"));  // 其中-人工费
                ysclf = NumberUtil.add(ysclf, unitCostCodePriceMap.get("CLF"));  // 其中-材料费
                ysjxf = NumberUtil.add(ysjxf, unitCostCodePriceMap.get("JXF"));  // 其中-机械费
                yszcf = NumberUtil.add(yszcf, unitCostCodePriceMap.get("ZCF"));  // 其中-主材费
                yssbf = NumberUtil.add(yssbf, unitCostCodePriceMap.get("SBF"));  // 其中-设备费

                csxmf = NumberUtil.add(csxmf, unitCostCodePriceMap.get("CSXMHJ"));    // 其他措施费
                csrgf = NumberUtil.add(csrgf, unitCostCodePriceMap.get("JSCS_RGF"));  // 措施-人工费
                csclf = NumberUtil.add(csclf, unitCostCodePriceMap.get("JSCS_CLF"));  // 措施-材料费
                csjxf = NumberUtil.add(csjxf, unitCostCodePriceMap.get("JSCS_JXF"));  // 措施-机械费
                cssbf = NumberUtil.add(cssbf, unitCostCodePriceMap.get("JSCS_ZCF"));  // 措施-主材费
                cszcf = NumberUtil.add(cssbf, unitCostCodePriceMap.get("JSCS_SBF"));  // 措施-设备费

                dlf = NumberUtil.add(dlf, unitCostSummaryPriceMap.get("DLF"));  // 独立费
                qyglf = NumberUtil.add(qyglf, unitCostSummaryPriceMap.get("企业管理费"));   // 企业管理费
                lr = NumberUtil.add(lr, unitCostSummaryPriceMap.get("利润"));   // 利润
                aqwmsgf = NumberUtil.add(aqwmsgf, unitCostSummaryPriceMap.get("安全文明施工费")); // 安全生产、文明施工费
                sj = NumberUtil.add(sj, unitCostSummaryPriceMap.get("税金"));  // 税金

                let unitjzgcProjectCostTotal = 0, unitzsgcProjectCostTotal = 0, unitazgcProjectCostTotal = 0,
                    unitszgcProjectCostTotal = 0,
                    unityllhProjectCostTotal = 0, unitfggcProjectCostTotal = 0, unitxsgcProjectCostTotal = 0,
                    unityhgcProjectCostTotal = 0;

                // 获取该单位的所有取费专业
                let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));
                let qfMajorTypeParam = {
                    qfMajorTypes: Array.from(qfMajorTypeMoneyMap.keys())
                }
                // 查询满足qfCode数据，并进行libarayCode分组
                let groupCostAnalysisTypeMap = await this.getTypeByCostAnalysisType(qfMajorTypeParam);
                // 遍历groupLibaryCodeMap的key和value
                for (let [key, value] of groupCostAnalysisTypeMap) {
                    if (key === CostAnalysisTypeConstants.ZJFZ_JZGC) {
                        value.forEach(item => {
                            unitjzgcProjectCostTotal = NumberUtil.add(unitjzgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_ZSGC) {
                        value.forEach(item => {
                            unitzsgcProjectCostTotal = NumberUtil.add(unitzsgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_AZGC) {
                        value.forEach(item => {
                            unitazgcProjectCostTotal = NumberUtil.add(unitazgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_SZGC) {
                        value.forEach(item => {
                            unitszgcProjectCostTotal = NumberUtil.add(unitszgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_YLGC) {
                        value.forEach(item => {
                            unityllhProjectCostTotal = NumberUtil.add(unityllhProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_FGGC) {
                        value.forEach(item => {
                            unitfggcProjectCostTotal = NumberUtil.add(unitfggcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_XSGC) {
                        value.forEach(item => {
                            unitxsgcProjectCostTotal = NumberUtil.add(unitxsgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_YHGC) {
                        value.forEach(item => {
                            unityhgcProjectCostTotal = NumberUtil.add(unityhgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                }
                jzgcProjectCost = NumberUtil.add(jzgcProjectCost, unitjzgcProjectCostTotal);
                zsgcProjectCost = NumberUtil.add(zsgcProjectCost, unitzsgcProjectCostTotal);
                azgcProjectCost = NumberUtil.add(azgcProjectCost, unitazgcProjectCostTotal);
                szgcProjectCost = NumberUtil.add(szgcProjectCost, unitszgcProjectCostTotal);
                yllhProjectCost = NumberUtil.add(yllhProjectCost, unityllhProjectCostTotal);
                fggcProjectCost = NumberUtil.add(fggcProjectCost, unitfggcProjectCostTotal);
                xsgcProjectCost = NumberUtil.add(xsgcProjectCost, unitxsgcProjectCostTotal);
                yhgcProjectCost = NumberUtil.add(yhgcProjectCost, unityhgcProjectCostTotal);
            }
        }
        projectCost = NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost, yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost);

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let jzgm = precision.COST_ANALYSIS.jzgm;

        //存放单项工程数据
        let singleCostAnalysisArray = [];
        let singleCostAnalysisVO = new GljCostAnalysisSingleVO();
        singleCostAnalysisVO.dispNo = dispNo;
        singleCostAnalysisVO.projectName = subSingleProject.name;
        singleCostAnalysisVO.sequenceNbr = subSingleProject.sequenceNbr;

        singleCostAnalysisVO.projectCost = projectCost;
        singleCostAnalysisVO.jzgcProjectCost = jzgcProjectCost;
        singleCostAnalysisVO.zsgcProjectCost = zsgcProjectCost;
        singleCostAnalysisVO.azgcProjectCost = azgcProjectCost;
        singleCostAnalysisVO.szgcProjectCost = szgcProjectCost;
        singleCostAnalysisVO.yllhProjectCost = yllhProjectCost;
        singleCostAnalysisVO.fggcProjectCost = fggcProjectCost;
        singleCostAnalysisVO.xsgcProjectCost = xsgcProjectCost;
        singleCostAnalysisVO.yhgcProjectCost = yhgcProjectCost;

        singleCostAnalysisVO.budgetzjf = budgetzjf;
        singleCostAnalysisVO.ysrgf = ysrgf;
        singleCostAnalysisVO.ysclf = ysclf;
        singleCostAnalysisVO.ysjxf = ysjxf;
        singleCostAnalysisVO.yssbf = yssbf;
        singleCostAnalysisVO.yszcf = yszcf;

        singleCostAnalysisVO.csxmf = csxmf;
        singleCostAnalysisVO.csrgf = csrgf;
        singleCostAnalysisVO.csclf = csclf;
        singleCostAnalysisVO.csjxf = csjxf;
        singleCostAnalysisVO.cssbf = cssbf;
        singleCostAnalysisVO.cszcf = cszcf;

        singleCostAnalysisVO.qyglf = qyglf;
        singleCostAnalysisVO.lr = lr;
        singleCostAnalysisVO.dlf = dlf;
        singleCostAnalysisVO.aqwmsgf = aqwmsgf;
        singleCostAnalysisVO.sj = sj;
        singleCostAnalysisVO.average = ObjectUtils.isNotEmpty(subSingleProject.average) ? subSingleProject.average : 0;  // 工程规模
        singleCostAnalysisVO.unitcost = NumberUtil.divide(NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost,
            yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost), NumberUtil.numberScale(singleCostAnalysisVO.average, jzgm));  // 单方造价(元/m、元/㎡)
        singleCostAnalysisVO.costProportion = NumberUtil.divide(NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost,
            yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost), constructCost) * 100;  // 造价占比

        //存放所有单位对应的造价分析
        let commonCostAnalysis = {};
        singleCostAnalysisVO.childrenList = await this.generateUnitsCostAnalysisData(unitProjectsBySingle, singleCostAnalysisVO.dispNo, constructId, commonCostAnalysis);
        singleCostAnalysisVO.jzgcProjectCost = commonCostAnalysis.jzgcProjectCost;
        singleCostAnalysisVO.zsgcProjectCost = commonCostAnalysis.zsgcProjectCost;
        singleCostAnalysisVO.azgcProjectCost = commonCostAnalysis.azgcProjectCost;
        singleCostAnalysisVO.szgcProjectCost = commonCostAnalysis.szgcProjectCost;
        singleCostAnalysisVO.yllhProjectCost = commonCostAnalysis.yllhProjectCost;
        singleCostAnalysisVO.fggcProjectCost = commonCostAnalysis.fggcProjectCost;
        singleCostAnalysisVO.xsgcProjectCost = commonCostAnalysis.xsgcProjectCost;
        singleCostAnalysisVO.yhgcProjectCost = commonCostAnalysis.yhgcProjectCost;
        singleCostAnalysisVO.levelType = ProjectTypeConstants.PROJECT_TYPE_SINGLE;
        singleCostAnalysisArray.push(singleCostAnalysisVO);
        return singleCostAnalysisArray;
    }


    /**
     * 获取单位工程造价分析
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns []
     */
    async generateUnitCostAnalysisData(constructId, singleId, unitId) {
        // 获取单位工程
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 单位造价分析模板
        let zjfxArray = [];
        for (let i in gljZjfx) {
            let obj = new GljCostAnalysisUnitVO();
            ConvertUtil.setDstBySrc(gljZjfx[i], obj);
            zjfxArray.push(obj);
        }

        // 获取该单位的费用汇总
        let param = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        }
        // 获取费用代码
        let unitCostCodePriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostCode(param);
        // 获取该单位的费用汇总
        let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let jzgm = precision.COST_ANALYSIS.jzgm;

        // 数值数组
        let contexts = [];
        contexts.push(unitCostSummaryPriceMap.get("工程造价"));  // 工程造价  小写
        contexts.push(NumberUtil.numToCny(unitCostSummaryPriceMap.get("工程造价")));  // 工程造价  大写

        contexts.push(NumberUtil.divide(unitCostSummaryPriceMap.get("工程造价"), NumberUtil.numberScale(unitProject.average, jzgm)));  //单方造价(元/m、元/㎡)
        contexts.push(unitCostCodePriceMap.get("ZJF")); // 预算书-直接费
        contexts.push(unitCostCodePriceMap.get("RGF"));   // 其中-人工费
        contexts.push(unitCostCodePriceMap.get("CLF"));   // 其中-材料费
        contexts.push(unitCostCodePriceMap.get("JXF"));   // 其中-机械费
        contexts.push(unitCostCodePriceMap.get("ZCF"));   // 其中-主材费
        contexts.push(unitCostCodePriceMap.get("SBF"));   // 其中-设备费

        contexts.push(unitCostCodePriceMap.get("CSXMHJ"));    // 措施项目费
        contexts.push(unitCostCodePriceMap.get("JSCS_RGF"));  // 措施人工费
        contexts.push(unitCostCodePriceMap.get("JSCS_CLF"));  // 措施材料费
        contexts.push(unitCostCodePriceMap.get("JSCS_JXF"));  // 措施机械费
        contexts.push(unitCostCodePriceMap.get("JSCS_ZCF"));  // 措施-主材费
        contexts.push(unitCostCodePriceMap.get("JSCS_SBF"));  // 措施-设备费
        contexts.push(unitCostCodePriceMap.get("DLF"));   // 独立费

        contexts.push(unitCostSummaryPriceMap.get("价款调整"));   // 价款调整
        contexts.push(unitCostSummaryPriceMap.get("企业管理费"));   // 企业管理费
        contexts.push(unitCostSummaryPriceMap.get("利润"));   // 利润
        contexts.push(unitCostSummaryPriceMap.get("安全文明施工费"));   // 安全生产、文明施工费
        contexts.push(unitCostSummaryPriceMap.get("税金"));    // 税金

        // 给对应的模板放置数据值
        for (let i = 0; i < zjfxArray.length; i++) {
            zjfxArray[i].context = contexts[i];
        }
        // 遍历，进行DispNo序号添加
        let result = [];
        let parentItem = new GljCostAnalysisUnitVO();
        for (const item of zjfxArray) {
            if (item.dispNo.toString().indexOf(".") === -1) {
                // 如果没有小数点的行，就说明是父级
                parentItem = item;
                parentItem.childrenList = [];
                if (item.visible === true) {
                    result.push(parentItem);
                }
            } else {
                // 其他是子集
                if (item.visible === true) {
                    parentItem.childrenList.push(item);
                }
            }
        }
        return result;
    }

    /**
     * 获取多有单位的造价分析
     * @param unitProjects
     * @param singDispNo
     * @param constructId
     * @param commonCostAnalysis
     * @returns {any[]}
     */
    async generateUnitsCostAnalysisData(unitProjects, singDispNo, constructId, commonCostAnalysis) {
        //存放所有单位对应的造价分析
        let unitCostAnalysis = [];
        if (!ObjectUtils.isEmpty(unitProjects)) {

            // 获取该工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
            // 获取该单项下的所有单位
            let unitProjectsByConstruct = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
            // 整个项目的总工程造价值
            let constructCost = 0;
            for (let j = 0; j < unitProjectsByConstruct.length; j++) {
                let unitProject = unitProjectsByConstruct[j];
                // 获取该单位的费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
                constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
            }

            // 小数点精度
            let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
            let jzgm = precision.COST_ANALYSIS.jzgm;

            let jzgcProjectCostTotal = 0, zsgcProjectCostTotal = 0, azgcProjectCostTotal = 0, szgcProjectCostTotal = 0,
                yllhProjectCostTotal = 0,
                fxjzProjectCostTotal = 0, fxazProjectCostTotal = 0, fggcProjectCostTotal = 0, xsgcProjectCostTotal = 0,
                yhgcProjectCostTotal = 0,
                projectCostTotal = 0;
            // 遍历所有单位，进行赋值
            for (let j = 0; j < unitProjects.length; j++) {
                let costAnalysisSingleVO = new GljCostAnalysisSingleVO();
                let unitProject = unitProjects[j];

                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                // 获取费用代码
                let unitCostCodePriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostCode(param);
                // 获取费用汇总
                let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);

                let unitjzgcProjectCostTotal = 0, unitzsgcProjectCostTotal = 0, unitazgcProjectCostTotal = 0,
                    unitszgcProjectCostTotal = 0,
                    unityllhProjectCostTotal = 0, unitfggcProjectCostTotal = 0, unitxsgcProjectCostTotal = 0,
                    unityhgcProjectCostTotal = 0;
                // 获取该单位的所有取费专业
                let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));
                let qfMajorTypeParam = {
                    qfMajorTypes: Array.from(qfMajorTypeMoneyMap.keys())
                }
                // 查询满足qfCode数据，并进行libarayCode分组
                let groupCostAnalysisTypeMap = await this.getTypeByCostAnalysisType(qfMajorTypeParam);
                // 遍历groupLibaryCodeMap的key和value
                for (let [key, value] of groupCostAnalysisTypeMap) {
                    if (key === CostAnalysisTypeConstants.ZJFZ_JZGC) {
                        value.forEach(item => {
                            unitjzgcProjectCostTotal = NumberUtil.add(unitjzgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_ZSGC) {
                        value.forEach(item => {
                            unitzsgcProjectCostTotal = NumberUtil.add(unitzsgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_AZGC) {
                        value.forEach(item => {
                            unitazgcProjectCostTotal = NumberUtil.add(unitazgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_SZGC) {
                        value.forEach(item => {
                            unitszgcProjectCostTotal = NumberUtil.add(unitszgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_YLGC) {
                        value.forEach(item => {
                            unityllhProjectCostTotal = NumberUtil.add(unityllhProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_FGGC) {
                        value.forEach(item => {
                            unitfggcProjectCostTotal = NumberUtil.add(unitfggcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_XSGC) {
                        value.forEach(item => {
                            unitxsgcProjectCostTotal = NumberUtil.add(unitxsgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                    if (key === CostAnalysisTypeConstants.ZJFZ_YHGC) {
                        value.forEach(item => {
                            unityhgcProjectCostTotal = NumberUtil.add(unityhgcProjectCostTotal, qfMajorTypeMoneyMap.get(item.qfMajorType));
                        })
                    }
                }
                costAnalysisSingleVO.projectCost = NumberUtil.addParams(unitjzgcProjectCostTotal, unitzsgcProjectCostTotal, unitazgcProjectCostTotal,
                    unitszgcProjectCostTotal, unityllhProjectCostTotal, unitfggcProjectCostTotal, unitxsgcProjectCostTotal, unityhgcProjectCostTotal);
                costAnalysisSingleVO.jzgcProjectCost = unitjzgcProjectCostTotal;
                jzgcProjectCostTotal = NumberUtil.add(jzgcProjectCostTotal, costAnalysisSingleVO.jzgcProjectCost);

                costAnalysisSingleVO.zsgcProjectCost = unitzsgcProjectCostTotal;
                zsgcProjectCostTotal = NumberUtil.add(zsgcProjectCostTotal, costAnalysisSingleVO.zsgcProjectCost);

                costAnalysisSingleVO.azgcProjectCost = unitazgcProjectCostTotal;
                azgcProjectCostTotal = NumberUtil.add(azgcProjectCostTotal, costAnalysisSingleVO.azgcProjectCost);

                costAnalysisSingleVO.szgcProjectCost = unitszgcProjectCostTotal;
                szgcProjectCostTotal = NumberUtil.add(szgcProjectCostTotal, costAnalysisSingleVO.szgcProjectCost);

                costAnalysisSingleVO.yllhProjectCost = unityllhProjectCostTotal;
                yllhProjectCostTotal = NumberUtil.add(yllhProjectCostTotal, costAnalysisSingleVO.yllhProjectCost);

                costAnalysisSingleVO.fggcProjectCost = unitfggcProjectCostTotal;
                fggcProjectCostTotal = NumberUtil.add(fggcProjectCostTotal, costAnalysisSingleVO.fggcProjectCost);

                costAnalysisSingleVO.xsgcProjectCost = unitxsgcProjectCostTotal;
                xsgcProjectCostTotal = NumberUtil.add(xsgcProjectCostTotal, costAnalysisSingleVO.xsgcProjectCost);

                costAnalysisSingleVO.yhgcProjectCost = unityhgcProjectCostTotal;
                yhgcProjectCostTotal = NumberUtil.add(yhgcProjectCostTotal, costAnalysisSingleVO.yhgcProjectCost);

                costAnalysisSingleVO.budgetzjf = unitCostCodePriceMap.get("ZJF"); // 预算书-直接费
                costAnalysisSingleVO.ysrgf = unitCostCodePriceMap.get("RGF");   // 其中-人工费
                costAnalysisSingleVO.ysclf = unitCostCodePriceMap.get("CLF");   // 其中-材料费
                costAnalysisSingleVO.ysjxf = unitCostCodePriceMap.get("JXF");   // 其中-机械费
                costAnalysisSingleVO.yssbf = unitCostCodePriceMap.get("SBF");   // 其中-设备费
                costAnalysisSingleVO.yszcf = unitCostCodePriceMap.get("ZCF");   // 其中-主材费

                costAnalysisSingleVO.csxmf = unitCostCodePriceMap.get("CSXMHJ");    // 其他措施费
                costAnalysisSingleVO.csrgf = unitCostCodePriceMap.get("JSCS_RGF");  // 其他措施费-人工费
                costAnalysisSingleVO.csclf = unitCostCodePriceMap.get("JSCS_CLF");  // 其他措施费-材料费
                costAnalysisSingleVO.csjxf = unitCostCodePriceMap.get("JSCS_JXF");  // 其他措施费-机械费
                costAnalysisSingleVO.cssbf = unitCostCodePriceMap.get("JSCS_SBF");  // 其他措施费-设备费
                costAnalysisSingleVO.cszcf = unitCostCodePriceMap.get("JSCS_ZCF");  // 其他措施费-主材费

                costAnalysisSingleVO.dlf = unitCostCodePriceMap.get("DLF");
                costAnalysisSingleVO.jktz = unitCostSummaryPriceMap.get("价款调整");   // 价款调整
                costAnalysisSingleVO.qyglf = unitCostSummaryPriceMap.get("企业管理费");   // 企业管理费
                costAnalysisSingleVO.lr = unitCostSummaryPriceMap.get("利润");   // 利润
                costAnalysisSingleVO.aqwmsgf = unitCostSummaryPriceMap.get("安全文明施工费");   // 安全生产、文明施工费
                costAnalysisSingleVO.sj = unitCostSummaryPriceMap.get("税金");    // 税金

                // 造价占比 = 当前行（建筑工程造价+安装工程造价）/总工程造价
                costAnalysisSingleVO.costProportion = NumberUtil.divide(NumberUtil.addParams(costAnalysisSingleVO.jzgcProjectCost,
                    costAnalysisSingleVO.zsgcProjectCost, costAnalysisSingleVO.azgcProjectCost, costAnalysisSingleVO.szgcProjectCost,
                    costAnalysisSingleVO.yllhProjectCost, costAnalysisSingleVO.fggcProjectCost, costAnalysisSingleVO.xsgcProjectCost,
                    costAnalysisSingleVO.yhgcProjectCost), constructCost) * 100;
                // 不同位置相同层级中的工程规模应保持一致。
                costAnalysisSingleVO.average = ObjectUtils.isNotEmpty(unitProject.average) ? unitProject.average : 0;
                // 单方造价 = 当前行（建筑工程造价+安装工程造价）/当前行工程规模
                costAnalysisSingleVO.unitcost = NumberUtil.divide(NumberUtil.addParams(costAnalysisSingleVO.jzgcProjectCost,
                    costAnalysisSingleVO.zsgcProjectCost, costAnalysisSingleVO.azgcProjectCost, costAnalysisSingleVO.szgcProjectCost,
                    costAnalysisSingleVO.yllhProjectCost, costAnalysisSingleVO.fggcProjectCost, costAnalysisSingleVO.xsgcProjectCost,
                    costAnalysisSingleVO.yhgcProjectCost), NumberUtil.numberScale(costAnalysisSingleVO.average, jzgm));
                costAnalysisSingleVO.levelType = ProjectTypeConstants.PROJECT_TYPE_UNIT;
                costAnalysisSingleVO.projectName = unitProject.name;
                costAnalysisSingleVO.sequenceNbr = unitProject.sequenceNbr;
                if (singDispNo !== null) {
                    costAnalysisSingleVO.dispNo = singDispNo + '.' + (j + 1);
                } else {
                    costAnalysisSingleVO.dispNo = (j + 1);
                }
                unitCostAnalysis.push(costAnalysisSingleVO);
            }
            projectCostTotal = NumberUtil.addParams(jzgcProjectCostTotal, zsgcProjectCostTotal, azgcProjectCostTotal, szgcProjectCostTotal,
                yllhProjectCostTotal, fxjzProjectCostTotal, fxazProjectCostTotal, fggcProjectCostTotal, xsgcProjectCostTotal, yhgcProjectCostTotal);
            commonCostAnalysis.projectCost = projectCostTotal;
            commonCostAnalysis.jzgcProjectCost = jzgcProjectCostTotal;
            commonCostAnalysis.zsgcProjectCost = zsgcProjectCostTotal;
            commonCostAnalysis.azgcProjectCost = azgcProjectCostTotal;
            commonCostAnalysis.szgcProjectCost = szgcProjectCostTotal;
            commonCostAnalysis.yllhProjectCost = yllhProjectCostTotal;
            commonCostAnalysis.fggcProjectCost = fggcProjectCostTotal;
            commonCostAnalysis.xsgcProjectCost = xsgcProjectCostTotal;
            commonCostAnalysis.yhgcProjectCost = yhgcProjectCostTotal;
        }
        return unitCostAnalysis;
    }

    /**
     * 工程项目造价分析
     * @returns {Promise<GljCostAnalysisSingleVO[]>}
     * @param constructId
     */
    async generateConstructCostAnalysisData(constructId) {
        // 获取工程项目
        let projectObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        let costAnalysisVOList = [];
        // 获取工程项目下所有的单项造价分析数据
        if (projectObj.children && projectObj.children.length > 0) {
            let constructCostAnalysiss = [];

            // 判断当前层级是否是单位时，进行层级遍历，获取单位层级
            let units = projectObj.children.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
            if (ObjectUtils.isNotEmpty(units)) {
                let commonCostAnalysis = {};
                constructCostAnalysiss = await this.generateUnitsCostAnalysisData(units, "1", constructId, commonCostAnalysis);
            } else {
                for (let i = 0; i < projectObj.children.length; i++) {
                    //获取该单项工程对应的造价分析
                    let singleCostAnalysiss = await this.generateSingleCostAnalysisData(constructId, projectObj.children[i], "1." + (i + 1));
                    constructCostAnalysiss.push(...singleCostAnalysiss);
                }
            }

            let constructCostAnalysisVO = new GljCostAnalysisConstructVO();
            let jzgcProjectCost = 0;  // 工程费用汇总-建筑工程造价
            let zsgcProjectCost = 0;  // 工程费用汇总-装饰工程造价
            let azgcProjectCost = 0;  // 工程费用汇总-安装工程造价
            let szgcProjectCost = 0;  // 工程费用汇总-市政工程造价
            let yllhProjectCost = 0;  // 工程费用汇总-园林绿化工程造价
            let fggcProjectCost = 0;  // 工程费用汇总-仿古建筑工程造价
            let xsgcProjectCost = 0;  // 工程费用汇总-古建（明清）修缮工程造价
            let yhgcProjectCost = 0;  // 工程费用汇总-市政设施维修养护工程造价

            let budgetzjf = 0; // 预算书-直接费 合计
            let ysrgf = 0;   // 其中-人工费
            let ysclf = 0;   // 其中-材料费
            let ysjxf = 0;   // 其中-机械费
            let yssbf = 0;   // 其中-设备费
            let yszcf = 0;   // 其中-主材费

            let csxmf = 0;   // 措施项目费
            let csrgf = 0;   // 措施-人工费
            let csclf = 0;   // 措施-材料费
            let csjxf = 0;   // 措施-机械费
            let cssbf = 0;   // 措施-设备费
            let cszcf = 0;   // 措施-主材费

            let qyglf = 0;  // 企业管理费
            let lr = 0;  // 利润
            let dlf = 0; // 独立费
            let aqwmsgf = 0; // 安全生产、文明施工费
            let sj = 0;  // 税金

            // 整个项目的总工程造价值
            let constructCost = 0;
            // 获取该单项下的所有单位
            let unitProjectsByConstruct = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(projectObj.children, unitProjectsByConstruct);

            for (let j = 0; j < unitProjectsByConstruct.length; j++) {
                let unitProject = unitProjectsByConstruct[j];
                // 获取该单位的费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
                constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
            }

            for (let j = 0; j < constructCostAnalysiss.length; j++) {
                let singleCostAnalysis = constructCostAnalysiss[j];
                //单项合计计算
                budgetzjf = NumberUtil.add(budgetzjf, singleCostAnalysis.budgetzjf);  // 预算书-直接费
                ysrgf = NumberUtil.add(ysrgf, singleCostAnalysis.ysrgf);  // 其中-人工费
                ysclf = NumberUtil.add(ysclf, singleCostAnalysis.ysclf);  // 其中-材料费
                ysjxf = NumberUtil.add(ysjxf, singleCostAnalysis.ysjxf);  // 其中-机械费
                yssbf = NumberUtil.add(yssbf, singleCostAnalysis.yssbf);  // 其中-设备费
                yszcf = NumberUtil.add(yszcf, singleCostAnalysis.yszcf);  // 其中-主材费

                csxmf = NumberUtil.add(csxmf, singleCostAnalysis.csxmf);  // 措施项目费
                csrgf = NumberUtil.add(csrgf, singleCostAnalysis.csrgf);  // 其中-人工费
                csclf = NumberUtil.add(csclf, singleCostAnalysis.csclf);  // 其中-材料费
                csjxf = NumberUtil.add(csjxf, singleCostAnalysis.csjxf);  // 其中-机械费
                cssbf = NumberUtil.add(cssbf, singleCostAnalysis.cssbf);  // 其中-设备费
                cszcf = NumberUtil.add(cszcf, singleCostAnalysis.cszcf);  // 其中-主材费

                dlf = NumberUtil.add(dlf, singleCostAnalysis.dlf);  // 独立费
                qyglf = NumberUtil.add(qyglf, singleCostAnalysis.qyglf);   // 企业管理费
                lr = NumberUtil.add(lr, singleCostAnalysis.lr);   // 利润
                aqwmsgf = NumberUtil.add(aqwmsgf, singleCostAnalysis.aqwmsgf); // 安全生产、文明
                sj = NumberUtil.add(sj, singleCostAnalysis.sj);  // 税金

                // 建筑工程费 = ∑各建筑单位工程造价合价
                jzgcProjectCost = NumberUtil.add(jzgcProjectCost, singleCostAnalysis.jzgcProjectCost);
                // 装饰工程费 = ∑各安装单位工程造价合价
                zsgcProjectCost = NumberUtil.add(zsgcProjectCost, singleCostAnalysis.zsgcProjectCost);
                // 安装工程费 = ∑各安装单位工程造价合价
                azgcProjectCost = NumberUtil.add(azgcProjectCost, singleCostAnalysis.azgcProjectCost);
                // 市政工程费 = ∑各市政单位工程造价合价
                szgcProjectCost = NumberUtil.add(szgcProjectCost, singleCostAnalysis.szgcProjectCost);
                // 园林绿化工程费 = ∑各园林绿化单位工程造价合价
                yllhProjectCost = NumberUtil.add(yllhProjectCost, singleCostAnalysis.yllhProjectCost);
                // 仿古建筑工程费 = ∑各仿古建筑单位工程造价合价
                fggcProjectCost = NumberUtil.add(fggcProjectCost, singleCostAnalysis.fggcProjectCost);
                // 古建（明清）修缮工程费 = ∑各古建（明清）修缮单位工程造价合价
                xsgcProjectCost = NumberUtil.add(xsgcProjectCost, singleCostAnalysis.xsgcProjectCost);
                // 市政设施维修养护工程费 = ∑各市政设施维修养护单位工程造价合价
                yhgcProjectCost = NumberUtil.add(yhgcProjectCost, singleCostAnalysis.yhgcProjectCost);
            }

            // 小数点精度
            let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
            let jzgm = precision.COST_ANALYSIS.jzgm;

            constructCostAnalysisVO.dispNo = "1";
            constructCostAnalysisVO.projectName = projectObj.name;
            constructCostAnalysisVO.sequenceNbr = projectObj.sequenceNbr;
            constructCostAnalysisVO.projectCost = constructCost;
            constructCostAnalysisVO.jzgcProjectCost = jzgcProjectCost;
            constructCostAnalysisVO.zsgcProjectCost = zsgcProjectCost;
            constructCostAnalysisVO.azgcProjectCost = azgcProjectCost;
            constructCostAnalysisVO.szgcProjectCost = szgcProjectCost;
            constructCostAnalysisVO.yllhProjectCost = yllhProjectCost;
            constructCostAnalysisVO.fggcProjectCost = fggcProjectCost;
            constructCostAnalysisVO.xsgcProjectCost = xsgcProjectCost;
            constructCostAnalysisVO.yhgcProjectCost = yhgcProjectCost;

            constructCostAnalysisVO.budgetzjf = budgetzjf;
            constructCostAnalysisVO.ysrgf = ysrgf;
            constructCostAnalysisVO.ysclf = ysclf;
            constructCostAnalysisVO.ysjxf = ysjxf;
            constructCostAnalysisVO.yssbf = yssbf;
            constructCostAnalysisVO.yszcf = yszcf;

            constructCostAnalysisVO.csxmf = csxmf;
            constructCostAnalysisVO.csrgf = csrgf;
            constructCostAnalysisVO.csclf = csclf;
            constructCostAnalysisVO.csjxf = csjxf;
            constructCostAnalysisVO.cssbf = cssbf;
            constructCostAnalysisVO.cszcf = cszcf;

            constructCostAnalysisVO.qyglf = qyglf;
            constructCostAnalysisVO.lr = lr;
            constructCostAnalysisVO.dlf = dlf;
            constructCostAnalysisVO.aqwmsgf = aqwmsgf;
            constructCostAnalysisVO.sj = sj;
            constructCostAnalysisVO.average = ObjectUtils.isNotEmpty(projectObj.average) ? projectObj.average : 0  // 工程规模
            constructCostAnalysisVO.unitcost = NumberUtil.divide(NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost,
                yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost), NumberUtil.numberScale(Number(constructCostAnalysisVO.average), jzgm)); // 单方造价(元/m、元/㎡)
            constructCostAnalysisVO.costProportion = NumberUtil.divide(NumberUtil.addParams(jzgcProjectCost, zsgcProjectCost, azgcProjectCost, szgcProjectCost,
                yllhProjectCost, fggcProjectCost, xsgcProjectCost, yhgcProjectCost), constructCost) * 100; // 造价占比

            //存放所有单项对应的造价分析
            constructCostAnalysisVO.childrenList = constructCostAnalysiss;
            constructCostAnalysisVO.levelType = ProjectTypeConstants.PROJECT_TYPE_PROJECT;
            costAnalysisVOList.push(constructCostAnalysisVO);
        }
        return costAnalysisVOList;
    }

    /**
     * 将取费专业按照造价分析专业分组
     *
     * @param args
     * @returns {Promise<[]|Map<unknown, unknown>>}
     */
    async getTypeByCostAnalysisType(args) {
        let {qfMajorTypes} = args;
        // 获取造价信息与取费专业的所有关系
        let costAnalysisTypes = gljZjfxType;
        // 按照取费专业过滤
        costAnalysisTypes = costAnalysisTypes.filter(item => {
            return qfMajorTypes.includes(item.qfMajorType);
        });
        // 按照造价分析专业分组
        return ArrayUtil.group(costAnalysisTypes, "costAnalysisType");
    }

    /**
     * 获取费用类型
     * @returns {Promise<[]|Map<unknown, unknown>>}
     */
    async getCostAnalysisTypeAll() {
        // 获取造价信息与取费专业的所有关系
        return ConvertUtil.deepCopy(gljZjfxType);
    }

    /**
     * 获取费用类型
     * @param qfMajorType
     * @returns {Promise<[]|Map<unknown, unknown>>}
     */
    async getCostAnalysisTypeOne(qfMajorType) {
        return ConvertUtil.deepCopy(gljZjfxType).find(item => item.qfMajorType === qfMajorType);
    }

    /**
     * 修改造价分析
     * @param args
     * @returns {Promise<void>}
     */
    async updateCostAnalysis(args) {
        // sequenceNbr:修改工程规模的单项或者单位id   flag:是否修改该单项下的所有子项工程规模  average:工程规模  type：单位、单项类型
        let {constructId, sequenceNbr, average, type, flag} = args;

        if (ProjectTypeConstants.PROJECT_TYPE_PROJECT === type) {
            // 获取当前工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(sequenceNbr);
            constructProject.average = average;
            ProjectDomain.getDomain(constructId).updateProject(constructProject);
            if (flag) {
                // 获取费用代码
                let costCodeCodePriceMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
                // 修改该单项下的所有子项工程规模
                await this.updateAverageByNode(constructId, constructProject, average, costCodeCodePriceMap);
            }
        }
        if (ProjectTypeConstants.PROJECT_TYPE_SINGLE === type) {
            // 获取当前单项
            let singleProject = ProjectDomain.getDomain(constructId).getProjectById(sequenceNbr);
            singleProject.average = average;
            ProjectDomain.getDomain(constructId).updateProject(singleProject);
            if (flag) {
                // 获取费用代码
                let costCodeCodePriceMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
                // 修改该单项下的所有子项工程规模
                await this.updateAverageByNode(constructId, singleProject, average, costCodeCodePriceMap);
            }
        }
        if (ProjectTypeConstants.PROJECT_TYPE_UNIT === type) {
            // 获取当前单位
            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(sequenceNbr);
            let oldAverage = unitProject.average;
            unitProject.average = average;
            ProjectDomain.getDomain(constructId).updateProject(unitProject);

            // 获取费用代码
            let costCodeCodePriceMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            for (let [key, value] of costCodeCodePriceMap) {
                if (ObjectUtils.isEmpty(value)) {
                    continue;
                }
                if (key.includes(unitProject.sequenceNbr)) {
                    value.find(item => item.code === "GCGM").price = average;
                }
            }
            //判断是否通知定额修改
            if (oldAverage != average) {
                await this._notifyUpdateQuantity(unitProject);
            }
        }
    }

    async _notifyUpdateQuantity(unitProject) {
        //获得项目domain，其中functionMap才是全的
        let projectDomain = ProjectDomain.getDomain(unitProject.constructId);
        let deDomain = projectDomain.getDeDomain();
        let functionDataMap = projectDomain.functionDataMap;
        let deGclMap = functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
        if (ObjectUtils.isNotEmpty(deGclMap)) {
            let deNotifySet = deGclMap.get(unitProject.sequenceNbr);
            if (ObjectUtils.isNotEmpty(deNotifySet)) {
                let priceCodes = await deDomain.getQuantityExpressionCodes(unitProject.constructId, unitProject.sequenceNbr, functionDataMap);
                for (let de of deNotifySet) {
                    await deDomain.notifyQuantity(de, true, true, priceCodes);
                }// 更新费用代码工程规模
                await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                    constructId: unitProject.constructId,
                    unitId: unitProject.sequenceNbr,
                    constructMajorType: unitProject.constructMajorType
                });
            }
            // 同步计算工程量明细
            await this.service.gongLiaoJiProject.gljQuantitiesService.recaculateQuantityByUnit(unitProject.constructId, unitProject.sequenceNbr, true);
        }
    }


    /**
     * 修改该单项下的所有子项工程规模
     * @param constructId
     * @param node  指定节点
     * @param average  工程规模
     * @param costCodeCodePriceMap
     */
    async updateAverageByNode(constructId, node, average, costCodeCodePriceMap) {
        if (ObjectUtils.isNotEmpty(node.children)) {
            for (let i = 0; i < node.children.length; i++) {
                let childNode = node.children[i];
                let oldAverage = childNode.average;
                childNode.average = average;
                ProjectDomain.getDomain(constructId).updateProject(childNode);
                // 修改费用代码
                if (ProjectTypeConstants.PROJECT_TYPE_UNIT === childNode.type) {
                    for (let [key, value] of costCodeCodePriceMap) {
                        if (ObjectUtils.isEmpty(value)) {
                            continue;
                        }
                        if (key.includes(childNode.sequenceNbr)) {
                            value.find(item => item.code === "GCGM").price = average;
                        }
                    }
                    //判断是否通知定额修改
                    if (oldAverage != average) {
                        await this._notifyUpdateQuantity(childNode);
                    }
                }
                await this.updateAverageByNode(constructId, childNode, average, costCodeCodePriceMap)
            }
        }
    }


    /**
     * 导出单位造价分析
     * @param costAnalyses
     */
    async exportUnitCostAnalysis(costAnalyses) {
        if (ObjectUtils.isEmpty(costAnalyses)) {
            return
        }
        let copyCostAnalyses = ConvertUtil.deepCopy(costAnalyses);
        let rowList = [];
        for (let costAnalysis of copyCostAnalyses) {
            rowList.push(costAnalysis)
            if (ObjectUtils.isNotEmpty(costAnalysis.childrenList) && costAnalysis.childrenList.length > 0) {
                let childrenList = costAnalysis.childrenList
                for (let child of childrenList) {
                    rowList.push(child)
                }
            }
        }

        // 存放造价分析文件的路径
        const exportDir = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\造价分析';
        FileUtils.ensurePathExists(exportDir)
        let count = FileUtils.countDirectories(exportDir);
        let options = {
            title: '保存文件',
            defaultPath: exportDir + '\\造价分析' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: ['xlsx']} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.endsWith('xlsx')) {
                filePath += 'xlsx';
            }

            // 提取需要的字段
            const filteredData = rowList.map(item => ({
                '序号': item.dispNo,
                '名称': item.name,
                '内容': item.context,
            }));

            // 转换数据为工作表
            const worksheet = XLSX.utils.json_to_sheet(filteredData);

            // 创建工作簿并添加工作表
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
            // 写入Excel文件
            XLSX.writeFile(workbook, filePath);
            return ResponseData.success(filePath);
        }
    }

    getRowList(copyCostAnalyses, rowList, storey, prefix = '') {
        let i = 1;
        for (let costAnalysis of copyCostAnalyses) {
            costAnalysis.dispNo = prefix ? `${prefix}.${i}` : `${i}`;
            rowList.push(costAnalysis);
            if (ObjectUtils.isNotEmpty(costAnalysis.childrenList) && costAnalysis.childrenList.length > 0) {
                let childrenList = costAnalysis.childrenList;
                rowList = this.getRowList(childrenList, rowList, storey + 1, costAnalysis.dispNo);
            }
            i++;
        }
        return rowList;
    }

    /**
     * 导出工程项目造价分析
     * @param costAnalyses
     */
    async exportProjectCostAnalysis(costAnalyses) {
        if (ObjectUtils.isEmpty(costAnalyses)) {
            return
        }
        let copyCostAnalyses = ConvertUtil.deepCopy(costAnalyses);
        let rowList = [];
        rowList = this.getRowList(copyCostAnalyses, rowList, 0, '');
        // 存放造价分析文件的路径
        const exportDir = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\造价分析';
        // FileUtils.ensurePathExists(exportDir)
        let count = FileUtils.countDirectories(exportDir);
        let options = {
            title: '保存文件',
            defaultPath: exportDir + '\\造价分析' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: ['xlsx']} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.endsWith('xlsx')) {
                filePath += 'xlsx';
            }

            // 提取需要的字段
            const filteredData = rowList.map(item => ([item.dispNo, item.projectName, item.projectCost, item.budgetzjf, item.ysrgf, item.ysclf, item.ysjxf, item.csxmf, item.csrgf, item.csclf, item.csjxf, item.dlf,
                item.jzgcProjectCost, item.zsgcProjectCost, item.azgcProjectCost, item.szgcProjectCost, item.yllhProjectCost, item.fggcProjectCost, item.xsgcProjectCost, item.yhgcProjectCost, item.qyglf, item.lr, item.costProportion = NumberUtil.numberScale(item.costProportion, 2), item.aqwmsgf, item.sj, item.average, item.unitcost
            ]));

            // 创建表头
            let headerRow1 = ['序号', '名称', '金额', '实体项目', '实体项目', '实体项目', '实体项目', '措施项目', '措施项目', '措施项目', '措施项目', '独立费', '按专业汇总', '按专业汇总', '按专业汇总', '按专业汇总', '按专业汇总', '管理费', '利润', '占造价占比（%）', '安全生产、文明施工费', '税金', '工程规模(㎡/m）', '单方造价(元/㎡或元/m）'];
            let headerRow2 = ['序号', '名称', '金额', '实体项目合计', '人工费', '材料费', '机械费', '措施项目合计', '措施人工费', '措施材料费', '措施机械费', '独立费', '建筑工程', '装饰装修工程', '安装工程', '市政工程', '园林绿化工程', '仿古建筑工程', '古建（明清）修缮工程', '市政设施维修养护工程', '管理费', '利润', '占造价占比（%）', '安全生产、文明施工费', '税金', '工程规模(㎡/m）', '单方造价(元/㎡或元/m）'];
            // 转换数据为工作表
            let worksheet = XLSX.utils.aoa_to_sheet([headerRow1, headerRow2, ...filteredData]);

            // 定义合并的单元格
            let merges = [];

            // 检查每一列的第一行和第二行是否相等，如果相等则添加到合并数组中
            for (let i = 0; i < headerRow1.length; i++) {
                if (headerRow1[i] === headerRow2[i]) {
                    merges.push({s: {r: 0, c: i}, e: {r: 1, c: i}});
                }
            }
            // 检查第一行中的相等单元格并进行合并
            let startCol = -1; // 合并的起始列
            for (let i = 0; i < headerRow1.length; i++) {
                if (i === 0 || headerRow1[i] !== headerRow1[i - 1]) {
                    // 如果当前列的值与前一列不同，重置起始列
                    startCol = i;
                }

                // 检查是否是最后一列或下一个单元格不同
                if (i === headerRow1.length - 1 || headerRow1[i] !== headerRow1[i + 1]) {
                    if (i > startCol) {
                        // 如果起始列和当前列不同，添加合并信息
                        merges.push({s: {r: 0, c: startCol}, e: {r: 0, c: i}});
                    }
                }
            }

            // 设置合并单元格
            worksheet['!merges'] = merges;

            // 创建工作簿并添加工作表
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
            // 写入Excel文件
            XLSX.writeFile(workbook, filePath);
            return ResponseData.success(filePath);
        }
    }

}

GljCostAnalysisService.toString = () => '[class GljCostAnalysisService]';
module.exports = GljCostAnalysisService;