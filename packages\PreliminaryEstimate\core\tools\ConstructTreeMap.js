const _ = require("lodash");
const {ObjectUtils} = require("../../utils/ObjectUtils");
class DoublyLinkedListNode {
    constructor(node, prev = null, next = null) {
        this.node = node;
        this.prev = prev;
        this.next = next;
    }
}

class DoublyLinkedList {
    constructor() {
        this.head =  new DoublyLinkedListNode(null, null);
        this.length = 0;
    }
    findTailNode() {
        let current = this.head;
        while (current.next !== null) {
            current = current.next;
        }
        return current;
    }
    appendNode(newNode) {
        let tailNode = this.findTailNode();

        newNode.prev = tailNode;
        newNode.next = null; // 新节点作为新的尾节点，其next指针应为null

        if (tailNode !== null) {
            tailNode.next = newNode;
        } else {
            this.head.next = newNode; // 如果链表为空，新节点直接成为头节点的下一个节点
        }
        this.length++;
    }
    prependNode(newNode) {
        if (this.head.next === null) { // 如果链表为空
            this.head.next = newNode;
            newNode.prev = this.head;
        } else {
            let oldHead = this.head.next;
            newNode.prev = this.head;
            newNode.next = oldHead;
            oldHead.prev = newNode;
            this.head.next = newNode;
        }
        this.length++;
    }
    insertAfterMatchedId(matchedId, newNode) {
        let current = this.head;
        let matchedNode = null;

        // 查找与给定matchedId匹配的节点
        while (current !== null && current.next !== null) { // 需要检查当前节点的next是否为null，因为头节点的value为null
            current = current.next;
            if (current.sequenceNbr === matchedId) {
                matchedNode = current;
                break;
            }
        }
        // 如果找到匹配的节点，则在其后插入新节点
        if (matchedNode !== null) {
            newNode.prev = matchedNode;
            newNode.next = matchedNode.next;

            if (matchedNode.next !== null) {
                matchedNode.next.prev = newNode;
            }
            matchedNode.next = newNode;
            this.length++;
        } else {
            throw new Error(`No node with ID ${matchedId} found in the list.`);
        }
    }
    insertBeforeMatchedId(matchedId, newNode) {
        let current = this.head;
        let matchedNode = null;
        let prevNode = null;

        // 查找与给定matchedId匹配的节点及其前一个节点
        while (current !== null && current.next !== null) {
            if (current.next.sequenceNbr === matchedId) {
                matchedNode = current.next;
                prevNode = current;
                break;
            }
            current = current.next;
        }
        // 如果找到匹配的节点，则在其后插入新节点
        if (matchedNode !== null) {
            newNode.prev = prevNode;
            newNode.next = matchedNode;

            prevNode.next = newNode;
            matchedNode.prev = newNode;
            this.length++;
        } else {
            throw new Error(`No node with ID ${matchedId} found in the list.`);
        }
    }

    removeNodeById(id) {
        let currentNode = this.head;
        let targetNode = null;

        // 寻找具有指定ID的节点
        while (currentNode !== null && currentNode.next !== null) {
            if (currentNode.next.sequenceNbr === id) {
                targetNode = currentNode.next;
                break;
            }
            currentNode = currentNode.next;
        }

        // 如果找到了目标节点
        if (targetNode !== null) {
            // 更新相邻节点的prev和next指针以移除目标节点
            if (targetNode.prev !== null) {
                targetNode.prev.next = targetNode.next;
            } else {
                this.head.next = targetNode.next; // 处理目标节点为头节点的情况
            }

            if (targetNode.next !== null) {
                targetNode.next.prev = targetNode.prev;
            }

            // 减少链表长度
            this.length--;

            return targetNode; // 返回已删除的节点
        } else {
            throw new Error(`No node with ID ${id} found in the list.`);
        }
    }
}
class TreeNode {
    constructor() {

    }

    assignDispNos() {
        let currentNumber = 1;
        for (const child of this.children) {
            child.dispNo = this.dispNo?(this.dispNo+"."+currentNumber++):currentNumber++;
        }
    }

    /**
     * 刷新下标
     */
    refreshIndex(){
        let index = 0;
        for (const child of this.children) {
            child.index = index++;
        }
    }

    /**
     * 向当前节点添加子节点
     * @param {TreeNode} child - 要添加的子节点
     */
    addChild(child) {
        child.parent = this; // 设置子节点的父节点引用
        child.parentId = this.sequenceNbr;
        this.children.push(child);
        child.index = this.children.length-1;
        //this.updateDispNoAfterChildAddition(child);
    }
    addChildAt(child, index) {

        if (index < 0 || index > this.children.length) {
            // throw new Error('Invalid index for adding a child node');
            //重置为最后一个节点
            index = this.children.length;
        }

        child.parent = this; // 设置子节点的父节点引用
        child.parentId = this.sequenceNbr;
        child.index =index;
        this.children.splice(index, 0, child); // 按指定下标插入子节点
        // 更新从插入位置开始的所有后续子节点的 index
        for (let i = index; i < this.children.length; i++) {
            this.children[i].index = i;
        }
    }

    /**
     *
     * @param {object} newValue
     */
    updateValue(newValue) {
        for(let key in newValue)
        {
            this[key] = newValue[key];
        }
    }

    removeChild(targetChild) {
        const index = this.children.indexOf(targetChild);
        if (index !== -1) {
            this.children.splice(index, 1);
            targetChild.parent = null; // 清除被删除节点的父节点引用
            //this.updateDispNoAfterChildRemoval(targetChild);
            this.refreshIndex();//删除后重置index
            return true;
        }
        return false;
    }
}

class Tree {
    constructor() {
        this.root = null;
        this.nodeMap = new Map(); // 添加一个节点映射表，
        this.dll = new DoublyLinkedList();
    }

    /**
     * 根据节点 sequenceNbr 获取节点
     * @param nodeId
     * @returns {any}
     */
    getNodeById(nodeId) {
        return this.nodeMap.get(nodeId);
    }
    removeNodeMapById(nodeId) {
        return this.nodeMap.delete(nodeId);
    }
    //获取所有节点集合
    getAllNodes() {
        return Array.from(this.nodeMap.values());
    }
    /*
    * 查找符合条件的第一个节点 直接返回
    * */
    find(filter){
        let item =this.getAllNodes().filter(filter);
        if(item&&item.length>0){
            return item[0];
        }
        return null;
    }
    /**
     * 根据条件获取所有节点
     * @param filter
     * @returns {any[]}
     */
    filter(filter){
        return  this.getAllNodes().filter(filter);
    }
    //根据条件获取某个节点下的所有子集
    filterAllSubsets(node,filter) {
        const subsets = [];
        if(filter(node)){
            subsets.push(node);
        }
        for (const child of node.children) {
            const childSubsets = this.findAllSubsets(child,filter);
            subsets.push(...childSubsets); // 合并子节点子集数据到当前子集数组
        }
        return subsets;
    }
    //把所有节点转list  并返回副本
    flattenTree(rootNode,filter=()=>true) {
        const flattenedList = [];

        function traverse(node) {
            if (!node) return;
            if(filter(node)){
                let row =  _.cloneDeep(node);
                delete row.parent;
                delete row.children;
                delete row.prev;
                delete row.next;
                // 将当前节点添加到列表中
                flattenedList.push(row);
            }
            // 遍历当前节点的所有子节点
            node.children.forEach(traverse);
        }

        traverse(rootNode);

        return flattenedList;
    }
    //返回节点下的所有子节点
    findAllSubsets(node) {
        const subsets = [];
        subsets.push(node);
        for (const child of node.children) {
            const childSubsets = this.findAllSubsets(child);
            subsets.push(...childSubsets); // 合并子节点子集数据到当前子集数组
        }
        return subsets;
    }
    /**
     * 添加子节点
     * @param newNode
     * @param parentNode
     */
    addNode(newNode, parentNode = null) {
        this.nodeMap.set(newNode.sequenceNbr, newNode); // 将节点添加到节点映射表中
        if (parentNode === null) {
            this.root = newNode;
        } else {
            parentNode.addChild(newNode); // 将节点添加到父节点的子节点列表中
        }
    }
    /**
     * 添加到某个点下的某个位置
     * @param newNode
     * @param parentNode
     * @param index
     */
    addNodeAt(newNode, parentNode = null, index) {
        this.nodeMap.set(newNode.sequenceNbr, newNode); // 将节点添加到节点映射表中
        if (parentNode === null) {
            this.root = newNode;
        } else {
            parentNode.addChildAt(newNode,index); // 将节点添加到父节点的子节点列表中
        }
    }


    /**
     * 更新等额父节点
     * @param oldNode
     * @param parentNode
     */
    addParentNodeChild(oldNode, parentNode = null) {
        let oldNodeData = this.nodeMap.get(oldNode.sequenceNbr);
        if (ObjectUtils.isEmpty(oldNodeData)) {
            this.nodeMap.set(oldNode.sequenceNbr, oldNode);
            oldNodeData = oldNode;
        }
        parentNode.addChild(oldNodeData); // 将节点添加到父节点的子节点列表中
    }

    /**
     * 清空节点child
     * @param nodeId
     * @returns {boolean}
     */
    deleteParentNodeChild(nodeId) {
        let newVar = this.nodeMap.get(nodeId);
        newVar.children = [];
    }


    /**
     * 移除节点
     * @param nodeId
     * @returns {*|boolean}
     */
    removeNode(nodeId) {
        const node = this.getNodeById(nodeId);
        if (node && node.parent) {
            this.removeNodeMapById(nodeId);
            return node.parent.removeChild(node);
        } else if (node && node === this.root) {
            this.root = null;
            this.removeNodeMapById(nodeId);
            return true;
        }
        return false;
    }
    sortChildren(node) {
        node.children.sort((a, b) => a.index < b.index);
        node.children.forEach(child => this.sortChildren(child));
    }

}
function treeToArray(tree) {
    let arr = tree.flattenTree(tree.root);
    return arr;
}
// function arrayToTree(nodeArray) {
//     // Step 1: 解析数组数据
//     const nodeMap = new Map();
//     let arrQd = [];
//     for (const nodeData of nodeArray) {
//         const { sequenceNbr} = nodeData;
//         // Step 2: 构建节点
//         const newNode = new TreeNode(sequenceNbr, nodeData, null);
//         if(newNode.kind === BranchProjectLevelConstant.qd){
//             arrQd.push(newNode);
//         }
//         nodeMap.set(sequenceNbr, newNode);
//     }
//     // Step 3: 连接节点
//     for (const [sequenceNbr, node] of nodeMap.entries()) {
//         const parentNode = nodeMap.get(node.parentId);
//
//         if (parentNode) {
//             parentNode.addChild(node);
//         }
//     }
//     arrQd = arrQd.sort((a,b)=>a.dispNo-b.dispNo);
//     // Step 4: 初始化树实例
//     const rootNodes = Array.from(nodeMap.values()).filter(node => !node.parent);
//     const tree = new Tree();
//     tree.nodeMap = nodeMap;
//     for (const rootNode of rootNodes) {
//         tree.addNode(rootNode);
//     }
//     arrQd.forEach((qd,index)=>{
//         tree.dll.appendNode(qd);
//     });
//     tree.sortChildren(tree.root);
//     return tree;
// }

module.exports = {Tree, TreeNode,treeToArray};

