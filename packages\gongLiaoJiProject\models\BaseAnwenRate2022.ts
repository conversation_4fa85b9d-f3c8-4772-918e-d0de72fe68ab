import {BaseModel} from "./BaseModel";
import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

/**
 * 安文费费率
 */
@Entity({name: "base_anwen_rate_2022"})
export class BaseAnwenRate2022 extends BaseModel {


    /**
     * 工程所在地
     */
    @Column({nullable:true,name:"project_location"})
    public  projectLocation: string;

    /**
     * 临路面数
     */
    @Column({nullable:true,name:"road_surface_num"})
    public  roadSurfaceNum: string;

    /**
     * 建筑面积
     */
    @Column({nullable:true,name:"floor_space"})
    public  floorSpace: string;

    /**
     * 市政工程造价
     */
    @Column({nullable:true,name:"municipal_construction_cost"})
    public  municipalConstructionCost: string;

    /**
     * 安文费率值（%）
     */
    //@Column({nullable:true,name:"anwen_rate"})
    public  anwenRate: string;


    /**
     * 安文费率值（%） 一般计税
     */
    @Column({nullable:true,name:"anwen_rate_ybjs"})
    public  anwenRateYbjs: string;

    /**
     * 安文费率值（%） 简易计税
     */
    @Column({nullable:true,name:"anwen_rate_jyjs"})
    public  anwenRateJyjs: string;

    /**
     * 定额库编码
     */
    @Column({nullable:true,name:"library_code"})
    public  libraryCode: string;

    /**
     * 是否删除(1是:0,否)
     */
    @Column({nullable:true,name:"del_flag"})
    public  delFlag: string;

    /**
     * 备注
     */
    @Column({nullable:true,name:"remark"})
    public  remark: string;

    /**
     * 取费专业
     */
    @Column({nullable:true,name:"qf_code"})
    public  qfCode: string;

}
