const {Service} = require("../../../core");

const ProjectDomain = require('../domains/ProjectDomain');
const { ObjectUtils } = require('../utils/ObjectUtils');
const {Snowflake} = require("../utils/Snowflake");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {GsProjectOverview} = require('../models/GsProjectOverview');
const {GsProjectOrgInstructions} = require('../models/GsProjectOrgInstructions');

const gsjbxx = require("../jsonData/gs_jbxx.json");
const gsUnitJbxx = require("../jsonData/gs_unit_jbxx.json");
const gsUnitGclx = require("../jsonData/gs_unit_gclx.json");
const gsUnitGclxAz = require("../jsonData/gs_unit_gclx_az.json");
const { listeners } = require("process");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {ResponseData} = require("../utils/ResponseData");
const xeUtils = require("xe-utils");
const ConstructMajorTypeEnum = require("../enums/ConstructMajorTypeEnum");


class GsOverviewService extends Service{

    constructor(ctx) {
        super(ctx);
    }
    
    getChildrenMenuList(args){
        let array = new Array();
        let obj1 ={};
        obj1[FunctionTypeConstants.JBXX_KEY_TYPE_11]="基本工程信息";
        //     "11":"基本工程信息"
        // };
        let obj2 ={};
        obj2[FunctionTypeConstants.JBXX_KEY_TYPE_12]="编制说明";

        //     FunctionTypeConstants.JBXX_KEY_TYPE_12:"编制说明"
        // };
        array.push(obj1);
        array.push(obj2);
        if(args.levelType=="3"){
            let obj3 ={};
            obj3[FunctionTypeConstants.JBXX_KEY_TYPE_13]="工程特征";
            //     FunctionTypeConstants.JBXX_KEY_TYPE_13:"工程特征"
            // };
            array.push(obj3);
        }
        let treeList = {};
        treeList.itemList = array;
        return treeList;
    }

    saveOrgInstructions(args){
        const {constructId,unitId,context} = args;
        if(ObjectUtils.isEmpty(context)){
            context = "";
        }
        let orgInstruction = new GsProjectOrgInstructions(constructId,unitId,context);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId,FunctionTypeConstants.JBXX_KEY_TYPE_12),orgInstruction);
    }

    /**
     * 11 基本信息 12 编制说明 13 特征 
     * @param {*} unitId 
     * @param {*} type 
     * @returns 
     */
    getDataMapKey(unitId,type){
        
        if(ObjectUtils.isEmpty(unitId)){
            unitId = "0";//保持key风格一致性
        }
        return "JBXX-" + unitId + "-" + type;
    }
    _reorder(array){
        if(ObjectUtils.isEmpty(array)){
            return;
        }
        array.forEach((item, index) => item.dispNo = index + 1)
    }
    /**
     * 保存列表
     * @param args
     */
    async saveList(args) {
        
        const {constructId,unitId,orgInstruction,type,operateType} = args;
        if(type === FunctionTypeConstants.JBXX_KEY_TYPE_12){
            this.saveOrgInstructions(args);
        } else {
            let list =  ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId,type));
            let newOrg = new GsProjectOverview();
            ConvertUtil.setDstBySrc(orgInstruction, newOrg);
            if(operateType === "insert"){
                let orgIn = list.find(item=>item.sequenceNbr === orgInstruction.sequenceNbr);
                let insertItem = new GsProjectOverview();
                insertItem.sequenceNbr = Snowflake.nextId();
                insertItem.addFlag = 1;
                insertItem.type = 0;
                insertItem.lockFlag = 0;
                insertItem.name="";
                insertItem.parentId = orgIn.parentId;
                let dispNo = newOrg.dispNo;
                if(insertItem.parentId == "0"){
                    list.forEach(childItem=>{if(childItem.parentId === orgIn.sequenceNbr){dispNo = childItem.dispNo}});
                    insertItem.parentId =  orgIn.sequenceNbr;
                }                 
                list.splice(dispNo,0,insertItem)
            }
            if(operateType === "edit"){
                 
                let newOrgInstruction = list.find(item=>item.sequenceNbr == newOrg.sequenceNbr);
                if(newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '工程规模'){

                    let liandongDe = false;
                    let deUnitProject = null;
                    if(type === FunctionTypeConstants.JBXX_KEY_TYPE_13){
                        let newUnitProjectModel = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === unitId);
                        let oldAverage = newUnitProjectModel[0].average
                        newUnitProjectModel[0].average = newOrg.context;
                        liandongDe = oldAverage != newOrg.context;
                        deUnitProject = newUnitProjectModel[0];
                        // 更新费用代码工程规模
                        await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                            constructId: constructId,
                            unitId: unitId,
                            constructMajorType: newUnitProjectModel.constructMajorType
                        });
                        //如果工程规模变动，联动定额使用工程规模表达式的工程量变化
                        if(liandongDe){
                            let deGclMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
                            if(ObjectUtils.isNotEmpty(deGclMap)){
                                let deNotifySet = deGclMap.get(unitId);
                                if(ObjectUtils.isNotEmpty(deNotifySet)){
                                    //校验quantity
                                    let codeArgs =  {
                                        constructId:constructId,
                                        type:"变量表",
                                        unitId:unitId,
                                        constructMajorType:deUnitProject.constructMajorType
                                    }
                                    let priceCodes = await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.costCodePrice(codeArgs);
                                    for(let de of deNotifySet){
                                        await ProjectDomain.getDomain(constructId).deDomain.notifyQuantity(de,true,true,priceCodes);
                                      //联动计取安装费
                                      await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, de.deRowId, "delete");
                                    }
                                    // 更新费用代码工程规模
                                    await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                                        constructId: constructId,
                                        unitId: unitId,
                                        constructMajorType: newUnitProjectModel.constructMajorType
                                    });
                                }
                            }
                            // 同步计算工程量明细
                            await this.service.PreliminaryEstimate.gsQuantitiesService.recaculateQuantityByUnit(constructId, unitId, true);
                        }
                    }else{
                        let newUnitProjectModel  = ProjectDomain.getDomain(constructId).getRoot();
                        newUnitProjectModel.average = newOrg.remark;
                        // // 更新费用代码工程规模
                        // await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                        //     constructId: constructId,
                        //     unitId: unitId,
                        //     constructMajorType: UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ
                        // });
                        // await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                        //     constructId: constructId,
                        //     unitId: unitId,
                        //     constructMajorType: UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ
                        // });
                    }
                }
                if(newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '工程规模单位'){
                    let newUnitProjectModel  = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === unitId);
                    newUnitProjectModel[0].averageUnit = newOrg.context;
                }
                if(newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '工程名称'){
                    let newUnitProjectModel  = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === unitId);
                    newUnitProjectModel[0].name = newOrg.remark;
                }
                if(newOrgInstruction.addFlag !== 1 && newOrgInstruction.name === '项目编号'){
                    let newUnitProjectModel  = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === constructId);
                    newUnitProjectModel[0].code = newOrg.remark;
                }
                if(newOrgInstruction.addFlag !== 1 &&  newOrgInstruction.name === '项目名称'){
                    let newUnitProjectModel  = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === constructId);
                    newUnitProjectModel[0].name = newOrg.remark;
                }
                newOrgInstruction.name = newOrg.name;
                newOrgInstruction.remark = newOrg.remark;
                newOrgInstruction.context = newOrg.context;
            }
            if(operateType === "copy"){
                newOrg.sequenceNbr = Snowflake.nextId();
                newOrg.addFlag = 1;
                let dispNo = newOrg.dispNo;
                if(newOrg.parentId === "0"){
                    list.forEach(childItem=>{if(childItem.parentId === newOrg.sequenceNbr){dispNo = childItem.dispNo}});
                }
                list.splice(dispNo,0,newOrg)
            }
            this._reorder(list);
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId,type),list);
        }
    }
    /**
     * 删除列表
     * @param args
     */
    async delete(args) {
        
        const {constructId,unitId,sequenceNbr,type} = args; 
        let list =  ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId,type));
        list = list.filter(item=>item.sequenceNbr !== sequenceNbr);
        this._reorder(list);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId,type),list);
    }

    /**
     * 根据名称获取信息行
     * @param {*} constructId 项目id
     * @param {*} unitId 单位id
     * @param {*} type  FunctionTypeConstants.JBXX_KEY_TYPE_13 工程特征  FunctionTypeConstants.JBXX_KEY_TYPE_11 基本信息
     * @param {*} name  列名
     * @returns 
     */
    async getByName(constructId,unitId,type,name){
        let list =  ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId,type));
        let item = null;
        if(ObjectUtils.isNotEmpty(list)){
            item = list.find(item=>item.name === name);
        }
        return item;
    }
    /**
     * 获取信息列表
     * @param args
     */
   async getList(args) {
        const {constructId,unitId,type} = args; 
        let list =  ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId,type));
        if(ObjectUtils.isEmpty(list)){  // type !== FunctionTypeConstants.JBXX_KEY_TYPE_12 &&
            list =  await this.initData(args);
        }else{

            if(type === FunctionTypeConstants.JBXX_KEY_TYPE_12){
                list = list instanceof Map ? Object.fromEntries(list.entries()):list;
            }else{
                
                let gcgmItem = null;
                // 兼容map对象处理
                if(list[0] instanceof Map){
                    for (let index = 0; index < list.length; index++) {
                        
                        list[index]= Object.fromEntries(list[index].entries())
                        if('工程规模'===list[index].name){
                            gcgmItem = list[index];
                        }
                    }
                }else{
                    for(let item of list){
                        if('工程规模'===item.name){
                            gcgmItem = item;
                        }
                    }
                }
                if(gcgmItem && type === FunctionTypeConstants.JBXX_KEY_TYPE_11){
                    let rootProject = ProjectDomain.getDomain(constructId).getRoot();
                    gcgmItem.remark = rootProject.average;
                }else if(gcgmItem && type === FunctionTypeConstants.JBXX_KEY_TYPE_13){
                    let rootProject = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr===unitId);
                    gcgmItem.context = rootProject[0].average;
                }
            }
        }
        return list;    
    }

    /**
     * 初始化基本信息、工程特征
     * @param project
     */
    initDataProject(project){
       let constructId = project.sequenceNbr;
        let list = [];
        let map = new Map();
        for (let i in gsjbxx) {
            gsjbxx[i].type = 0;
            gsjbxx[i].addFlag = 0;
            gsjbxx[i].lockFlag = 0;
            let listProjectOverviewXX = new GsProjectOverview();
            ConvertUtil.setDstBySrc(gsjbxx[i], listProjectOverviewXX);
            if('项目编号'===gsjbxx[i].name){
                listProjectOverviewXX.remark = project.code;
            }
            if('项目名称'===gsjbxx[i].name){
                listProjectOverviewXX.remark = project.name;
            }
            if('工程规模'===gsjbxx[i].name){
                listProjectOverviewXX.remark = project.average;
                if(project.average && project.average == '0'){
                    listProjectOverviewXX.remark = null;
                }
            }
            listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
            let parentId = map.get(listProjectOverviewXX.groupCode);
            if(ObjectUtils.isEmpty(parentId)){
                listProjectOverviewXX.parentId = "0";
                map.set(listProjectOverviewXX.groupCode,listProjectOverviewXX.sequenceNbr);
            }else{
                listProjectOverviewXX.parentId = parentId;
            }
            list.push(listProjectOverviewXX);
        }
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(null, FunctionTypeConstants.JBXX_KEY_TYPE_11), list);

        // 初始化编制说明
        let unitId = null;
        let orgInstruction = this.initOrgInstructions(constructId, unitId);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, FunctionTypeConstants.JBXX_KEY_TYPE_12), orgInstruction);

        // return list;
    }

    /**
     * type 0 项目  1 单位信息
     * @param args
     */
    async initData(args){
        const {constructId,unitId,type} = args; 
        let list = [];
        let project = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === constructId);
        if(ObjectUtils.isEmpty(unitId)){
            list = this.initDataProject(project[0]);
        }else{
            
            let unitProject = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === unitId);
            if(type === FunctionTypeConstants.JBXX_KEY_TYPE_11){
                for (let i in gsUnitJbxx) {
                    gsUnitJbxx[i].addFlag = 0;
                    gsUnitJbxx[i].type = 0;
                    gsUnitJbxx[i].lockFlag = 0;
                    let listProjectOverviewXX = new GsProjectOverview();
                    ConvertUtil.setDstBySrc(gsUnitJbxx[i], listProjectOverviewXX);
                    listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
                    if('工程名称'===gsUnitJbxx[i].name){
                        listProjectOverviewXX.remark = unitProject[0].name
                    }
                    if('工程专业'===gsUnitJbxx[i].name){
                        let deLibrary = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(unitProject[0].constructMajorType);
                        listProjectOverviewXX.remark = deLibrary?deLibrary.projectType:unitProject[0].constructMajorType;
                        gsUnitJbxx[i].lockFlag = 1;
                    }
                    if('编制依据'===gsUnitJbxx[i].name){
                        //获取取费专业数据,默认河北石家庄,
                        let costMajorList = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getDeLibrariesByDirection('130000','130100');
                        let deLibrary = costMajorList.find(costItem => costItem.libraryCode === unitProject[0].constructMajorType);
                        listProjectOverviewXX.remark = deLibrary?deLibrary.libraryName:costMajorList[0].libraryName;
                        listProjectOverviewXX.jsonStr = costMajorList.map(item=>item.libraryName).join(',');
                        gsUnitJbxx[i].lockFlag = 1;
                    }
                    list.push(listProjectOverviewXX);
                }
            }
            if(type === FunctionTypeConstants.JBXX_KEY_TYPE_13){
                let gsUnitGclxInitList = gsUnitGclx;
                if (unitProject[0].constructMajorType === ConstructMajorTypeEnum.TYPE1.code) {
                    gsUnitGclxInitList = gsUnitGclxAz;
                }

                for (let i in gsUnitGclxInitList) {
                    gsUnitGclxInitList[i].type = 1;
                    gsUnitGclxInitList[i].addFlag = 0;
                    gsUnitGclxInitList[i].lockFlag = 0;
                    let listProjectOverviewTz = new GsProjectOverview();
                    ConvertUtil.setDstBySrc(gsUnitGclxInitList[i], listProjectOverviewTz);
                    
                    if('工程规模'===gsUnitGclxInitList[i].name){
                        listProjectOverviewTz.context = unitProject[0].average;
                        if(unitProject[0].average && unitProject[0].average == '0'){
                            listProjectOverviewTz.context = null;
                        }
                    }

                    if (ObjectUtils.isNotEmpty(listProjectOverviewTz.parentId)) {
                        let find = gsUnitGclxInitList.find(p => p.sequenceNbr === listProjectOverviewTz.parentId);
                        let find1 = list.find(o => o.name === find.name);
                        listProjectOverviewTz.parentId = find1.sequenceNbr;
                    }

                    listProjectOverviewTz.sequenceNbr = Snowflake.nextId();
                    list.push(listProjectOverviewTz);
                }
            }
            // 初始化编制说明
            if (type === FunctionTypeConstants.JBXX_KEY_TYPE_12) {
                list = this.initOrgInstructions(constructId, unitId);
            }
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, type), list);
        }
        return list;
    }


    /**
     * 初始化编制说明
     * @param constructId
     * @param unitId
     * @returns {GsProjectOrgInstructions}
     */
    initOrgInstructions(constructId, unitId) {
        let context = "<article class=\"4ever-article\" data-clipboard-cangjie=\"[&quot;root&quot;,{&quot;copyFrom&quot;:&quot;im-native&quot;},[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;一、工程概况&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;1.工程名称：&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;2.建设地点：&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;3.工程规模：&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;4.工程开.竣工日期：&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;二、主要技术经济指标&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;1.工程总造价：&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;2.单方造价：&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;三、编制依据&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;四、建筑、安装工程费用计算方法及其费用记取的说明&quot;]]],[&quot;p&quot;,{}],[&quot;p&quot;,{},[&quot;span&quot;,{&quot;data-type&quot;:&quot;text&quot;},[&quot;span&quot;,{&quot;data-type&quot;:&quot;leaf&quot;},&quot;五、其他有关说明的问题&quot;]]]]\">\n" +
            "<p><span style=\"font-size: 14px;\">一、工程概况</span></p>\n" +
            "<p>&nbsp;</p>\n" +
            "<p><span style=\"font-size: 14px;\">二、主要技术经济指标</span></p>\n" +
            "<p>&nbsp;</p>\n" +
            "<p><span style=\"font-size: 14px;\">三、编制依据</span></p>\n" +
            "<p><span style=\"white-space: pre; font-size: 14px;\"> </span></p>\n" +
            "<p><span style=\"font-size: 14px;\">四、工程费用计算</span></p>\n" +
            "<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 1.建筑工程：</span></p>\n" +
            "<p><span style=\"white-space: pre; font-size: 14px;\"> </span></p>\n" +
            "<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 2.设备安装工程：</span></p>\n" +
            "<p>&nbsp;</p>\n" +
            "<p><span style=\"font-size: 14px;\">五、引进设备材料有关费率取定及依据</span></p>\n" +
            "<p><span style=\"font-size: 14px;\">&nbsp; &nbsp; 国外运输费、国外运输保险费、海关税费、增值税、国内运杂费、其他有关税费</span></p>\n" +
            "<p><span style=\"white-space: pre; font-size: 14px;\"> </span></p>\n" +
            "<p><span style=\"font-size: 14px;\">六、工程建设其他费用、预备费等的说明</span></p>\n" +
            "<p><span style=\"white-space: pre; font-size: 14px;\"> </span></p>\n" +
            "<p><span style=\"font-size: 14px;\">七、其他应说明的问题</span></p>\n" +
            "</article>";
        return new GsProjectOrgInstructions(constructId, unitId, context);
    }

    /**
     * 上移下移基本信息，工程特征
     * @param args
     * @returns {*}
     */
    async moveUpAndDownOverview(args) {
        let {constructId, unitId, moveType, sequenceNbrArray, type, projectId} = args;
        constructId = projectId;
        //获取原始数据
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(unitId, type));
        let otherProjectCostsLevel1 = xeUtils.toArrayTree(otherProjectCosts, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });

        sequenceNbrArray.forEach(sequenceNbr => {
            // 获取当前选定行的父节点，获取他的子集
            let projectCostParentNode = {}
            projectCostParentNode.children = otherProjectCosts;
            if (ObjectUtils.isNotEmpty(projectCostParentNode) && ObjectUtils.isNotEmpty(projectCostParentNode.children)) {
                // 遍历建设其他费，找到点击行
                let projectCostNode = projectCostParentNode.children.find(item => item.sequenceNbr === sequenceNbr);

                if (ObjectUtils.isEmpty(projectCostNode.parentId) || projectCostNode.parentId == "0") {
                    projectCostParentNode.children = otherProjectCostsLevel1;
                }

                // 将指定行，在集合中向上/向下移一个位置
                if (moveType === "up") {
                    this.moveItemUp(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                } else if (moveType === "down") {
                    this.moveItemDown(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                }


                if (ObjectUtils.isEmpty(projectCostNode.parentId) || projectCostNode.parentId == "0") {
                    otherProjectCosts = xeUtils.toTreeArray(otherProjectCostsLevel1);
                }
            }
        });

        let sortNo = 1;
        otherProjectCosts.forEach(o => {
            o.dispNo = sortNo++;
        });

        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(unitId, type), otherProjectCosts);
        return ResponseData.success(otherProjectCosts);
    }


    /**
     * 向上移位
     * @param array
     * @param index
     */
    moveItemUp(array, index) {
        // 检查索引是否大于0，因为不能移动第一个元素到更前面去
        if (index > 0) {
            // 保存要移动的元素
            let item = array.splice(index, 1)[0];
            // 在当前位置之前插入元素
            array.splice(index - 1, 0, item);
        }
    }


    /**
     * 向下移位
     * @param array
     * @param index
     */
    moveItemDown(array, index) {
        // 检查index是否在数组的有效范围内并且不是最后一个元素
        if (index >= 0 && index < array.length - 1) {
            // 使用splice取出要移动的元素
            const element = array.splice(index, 1)[0];
            // 将取出的元素插入到其下方的位置
            array.splice(index + 1, 0, element);
        }
        // return array;
    }

}
GsOverviewService.toString = () => '[class GsOverviewService]';
module.exports = GsOverviewService;