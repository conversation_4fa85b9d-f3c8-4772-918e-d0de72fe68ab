const MathItemHandler = require("./mathItemHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. XXXX *n 对应材料编码 乘
 */
class XXX$VMathHandler extends MathItemHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        let mathSubArr = mathItem.math.split(/\s+/);
        mathItem.fromRCJCode = mathSubArr[0];
        mathItem.fromRCJLibraryCode = this.rule.libraryCode;
        mathItem.parseMath = mathSubArr[1];
        mathItem.operator = this.mathOperator(mathItem.parseMath);
    }

    async activeRCJ() {
        let item = this.mathItem;
        let designatedMaterials = this.findActiveRCJByCode(item.fromRCJCode);

        if(ObjectUtils.isEmpty(designatedMaterials)){
            let rcj = await this.addNewRCJ(item.fromRCJLibraryCode, item.fromRCJCode);
            item.activeRCJs = [rcj];
        }else{
            if(item.fromRCJCode != designatedMaterials[0].materialCode){
                this.notStandardActiveRcjCodes.push([item.fromRCJCode, designatedMaterials[0].materialCode]);
            }
            this.mathItem.activeRCJs.push(designatedMaterials[0]);
        }
    }
}

module.exports = XXX$VMathHandler;