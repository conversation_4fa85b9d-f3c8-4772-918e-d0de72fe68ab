const path = require('path');
const {Service} = require("../../../core");
const util = require('util');
const exec = util.promisify(require('child_process').exec);
const UtilsPs = require('../../../core/ps');
const fs = require('fs');
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const {PricingFileFindUtils} = require("../domains/ProjectDomain");
const ExcelEnum = require("../enums/GSExcelEnum");
const {ShenHeWriteExcelBySheetUtil} = require("../utils/GSWriteExcelBySheetUtil");
const {GSExcelUtil} = require("../utils/GSExcelUtil");
const projectLevelConstant = require("../constants/ProjectLevelConstant");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectLevelConstant = require("../constants/ProjectLevelConstant");

class GsPdfService extends Service {
    constructor(ctx) {
        super(ctx);
    }


    async runCommand(command) {
        try {
            const {stdout, stderr} = await exec(command);
            console.log(`命令输出结果: ${stdout}`);
            console.error(`命令错误输出: ${stderr}`);
        } catch (error) {
            console.error(`执行命令时出错: ${error}`);
        }
    }


    async excelToPdfDemo() {

        // const ext = 'pdf'; // Output extension.
        // const inputPath = path.join("C:\\Users\\<USER>\\Desktop\\pdf", '/攻城2.xlsx');
        // const outputPath = path.join("C:\\Users\\<USER>\\Desktop\\pdf", `/攻城2.${ext}`);
        //
        // // var workbook = new asposeCells.Workbook("C:\\Users\\<USER>\\Desktop\\模板参考\\示例\\一般计税\\招标项目（招标控制价）\\导出excel（该文件夹下工程项目excel为标准文件格式）\\攻城2\\攻城2.xlsx");
        //
        // // var saveOptions = asposeCells.PdfSaveOptions();
        // // workbook.save("Book1.pdf", saveOptions);
        // //设置环境变量
        // let javaCommand = UtilsPs.getExtraResourcesDir()+"\\jre\\bin\\java";
        // let javaHomePath = UtilsPs.getExtraResourcesDir()+"\\jre";
        // let jarPath = UtilsPs.getExtraResourcesDir()+"\\pdfUtil.jar";
        // let parameters = "C:\\Users\\<USER>\\Desktop\\模板参考\\示例\\一般计税\\招标项目（招标控制价）\\导出excel（该文件夹下工程项目excel为标准文件格式）\\攻城2\\建筑工程.xlsx"
        //     +"   "+"C:\\Users\\<USER>\\Desktop\\模板参考\\示例\\一般计税\\招标项目（招标控制价）\\导出excel（该文件夹下工程项目excel为标准文件格式）\\攻城2\\建筑工程.pdf";
        // await this.runCommand(javaCommand+" -DJAVA_HOME="+javaHomePath+"  -jar "+jarPath+"  "+parameters);


        let project = await this.initWorkBook(projectLevelConstant.construct);
        //初始化workbook后的重新写入都是依照 worksheets属性的排序进行的

        //生成excel
        let excelFilePath = UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\export\\pdf.xlsx";
        let pdfPath = UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\export\\demo.pdf";
        await this.createDirectory(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\export");
        await project.xlsx.writeFile(excelFilePath);
        //设置环境变量
        let javaCommand = UtilsPs.getExtraResourcesDir() + "\\jre\\bin\\java";
        let javaHomePath = UtilsPs.getExtraResourcesDir() + "\\jre";
        let jarPath = UtilsPs.getExtraResourcesDir() + "\\pdfUtil.jar";
        let parameters = excelFilePath
            + "   " + pdfPath;

        await this.runCommand(javaCommand + " -DJAVA_HOME=" + javaHomePath + "  -jar " + jarPath + "  " + parameters);
        //删除原来生成的excel文件

        // let total = 0;
        // for (let i = 0; i < unit._worksheets.length; i++) {
        //     let worksheet1 = unit._worksheets[i];
        //     if (worksheet1 != null) {
        //         total++;
        //     }
        // }
        //
        // let worksheet = unit.getWorksheet("封面2 招标控制价（标底）");
        // unit.removeWorksheet(worksheet.id);
        //
        // let worksheetQd = unit.getWorksheet("表1-1 工程量清单编制说明");
        // worksheetQd.id = 45;
        // unit.removeWorksheet(worksheetQd.id);
        // unit.removeWorksheet(2);
        // unit.removeWorksheet(24);
        console.log("");
    }


    async excelToPdf(params) {

        const dialogOptions = {
            title: '保存文件',
            defaultPath: params.headLine,
            filters: [{name: 'pdf', extensions: ['pdf']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            await this.exportPdf(params, filePath);
            return true;
        } else {
            return false;
        }
    }

    async exportPdf(params, pdfPath) {
        let project = await this.initWorkBook(projectLevelConstant.construct);
        let single = await this.initWorkBook(projectLevelConstant.single);
        let unit = await this.initWorkBook(projectLevelConstant.unit);

        let fileDir = this.getProjectRootPath() + "\\excelTemplate\\gs\\" + params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        await this.parseParams(params, project, single, unit, fileDir, args, workBookList);

        //合成一个大excel文件  最后生成一个pdf文件
        //先对workBookList[0]._worksheets 按照 worksheets 进行重排  worksheets属性 始终是有序的
        for (let i = 0; i < workBookList[0].worksheets.length; i++) {
            //确定_worksheets 中当前的索引  及id相同的索引 进行位置交换
            let indexCur = await this.getIndexIn_worksheets(i + 1, workBookList[0]);
            let indexId = await this.getIndexOfSameId(workBookList[0].worksheets[i].id, workBookList[0]);
            [workBookList[0]._worksheets[indexCur], workBookList[0]._worksheets[indexId]] = [workBookList[0]._worksheets[indexId], workBookList[0]._worksheets[indexCur]];
        }
        for (let i = 1; i < workBookList.length; i++) {
            let bookElement = workBookList[i];
            for (let j = 0; j < bookElement.worksheets.length; j++) {
                let worksheet = bookElement.worksheets[j];
                if (worksheet != null) {
                    workBookList[0]._worksheets.push(worksheet);
                }
            }
        }
        //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
        //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
        let orderNo = 0;
        for (let i = 0; i < workBookList[0]._worksheets.length; i++) {
            let worksheetSam = workBookList[0]._worksheets[i];
            if (worksheetSam != null) {
                worksheetSam.id = ++orderNo;
                worksheetSam.orderNo = orderNo;
            }
        }
        //生成excel
        let excelFilePath = UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\gs\\pdf.xlsx";
        await this.createDirectory(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\gs");
        await workBookList[0].xlsx.writeFile(excelFilePath);
        //设置环境变量
        let javaCommand = UtilsPs.getExtraResourcesDir() + "\\jre\\bin\\java";
        let javaHomePath = UtilsPs.getExtraResourcesDir() + "\\jre";
        let jarPath = UtilsPs.getExtraResourcesDir() + "\\pdfUtil.jar";
        let parameters = excelFilePath
            + "   " + pdfPath;

        await this.runCommand(javaCommand + " -DJAVA_HOME=" + javaHomePath + "  -jar " + jarPath + "  " + parameters);
        //删除原来生成的excel文件
        fs.unlink(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\gs\\pdf.xlsx", (err) => {
            if (err) {
                console.error('删除文件时出错:', err);
                return;
            }
            console.log('文件删除成功！');
        });
    }

    async getIndexIn_worksheets(order, workbook) {
        let index = 0;
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i] != null) {
                index++;
                if (index == order) {
                    return i;
                }
            }
        }
    }

    async getIndexOfSameId(idParam, workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i] != null && workbook._worksheets[i].id == idParam) {
                return i;
            }
        }
    }

    getProjectRootPath() {
        // let relativePath = __filename;
        // let index = relativePath.indexOf("pricing-cs");
        // let prefix = relativePath.substring(0,index);
        return UtilsPs.getExtraResourcesDir();
        // return prefix+"pricing-cs";
    }

    async createDirectory(directoryPath) {
        if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, {recursive: true});
        } else {
        }
    }

    async deleteDirectory(dirPath) {
        if (fs.existsSync(dirPath)) {
            fs.readdirSync(dirPath).forEach(file => {
                const filePath = path.join(dirPath, file);

                if (fs.lstatSync(filePath).isDirectory()) {
                    deleteDirectory(filePath); // 递归删除子目录
                } else {
                    fs.unlinkSync(filePath); // 删除文件
                }
            });

            fs.rmdirSync(dirPath); // 删除空目录
            console.log('目录删除成功');
        } else {
            console.log('目录不存在');
        }
    }

    async initWorkBook(projectLevel) {
        let loadDir = this.getProjectRootPath() + "\\excelTemplate\\gs";
        let loadPath = "";

        if (projectLevel == projectLevelConstant.single) {
            loadPath = loadDir + "\\单项工程层级.xlsx";
        } else if (projectLevel == projectLevelConstant.unit) {
            loadPath = loadDir + "\\单位工程层级.xlsx";
        } else if (projectLevel == projectLevelConstant.construct) {
            loadPath = loadDir + "\\工程项目层级.xlsx";
        }

        //加载workbook
        let workbook = await GSExcelUtil.readToWorkBook(loadPath);
        return workbook;
    }

    async parseParams(params, project, single, unit, fileDir, args, workBookList) {
        if (args == null) {
            args = {};
        }
        for (let i = 0; i < params.childrenList.length; i++) {
            let param = params.childrenList[i];
            //如果为总工程层级
            if (param.projectLevel != null && param.projectLevel == projectLevelConstant.construct) {
                args["constructId"] = params.id;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(project, param.projectLevel, param.headLine, args);
                } else {
                    project.removeWorksheet(param.headLine);
                }
                if (project.worksheets.length == 1 && project.worksheets[0].name == "格式替换sheet") {
                    project.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == projectLevelConstant.single) {
                args["singleId"] = params.id;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(single, param.projectLevel, param.headLine, args);
                } else {
                    single.removeWorksheet(param.headLine);
                }
                if (single.worksheets.length == 1 && single.worksheets[0].name == "格式替换sheet") {
                    single.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == projectLevelConstant.unit) {
                args["unitId"] = params.id;
                args["singleId"] = params.singleId;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(unit, param.projectLevel, param.headLine, args);
                } else {
                    unit.removeWorksheet(param.headLine);
                }
                if (unit.worksheets.length == 1 && unit.worksheets[0].name == "格式替换sheet") {
                    unit.removeWorksheet("格式替换sheet");
                }
            }
        }


        if (params.childrenList != null && params.childrenList[0].projectLevel == projectLevelConstant.construct) {
            project.removeWorksheet("格式替换sheet");
            let projectTableList = params.childrenList.filter(o => ObjectUtils.isNotEmpty(o.projectLevel) && o.projectLevel === 1 && o.selected === true);
            if (ObjectUtils.isNotEmpty(projectTableList)) {
                await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet3(projectTableList, project.getWorksheet("A.0.3 目录"));  //写入工程级别目录数据
            } else {
                project.removeWorksheet("A.0.3 目录");
            }
            if (project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(project);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == projectLevelConstant.single) {
            single.removeWorksheet("格式替换sheet");
            if (single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(single);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == projectLevelConstant.unit) {
            unit.removeWorksheet("格式替换sheet");
            await this.service.PreliminaryEstimate.gsExportQueryService.calUnitSheetZyhz(args.constructId, args.singleId, params.unitId, unit);  //判断是否专业汇总去除sheet
            if (unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(unit);
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList != null);//含有子节点的节点
        if (filter != null) {
            let directory;
            for (let i = 0; i < filter.length; i++) {
                //同时对single  和 unit对象进行初始化
                single = await this.initWorkBook(projectLevelConstant.single);
                unit = await this.initWorkBook(projectLevelConstant.unit);
                directory = fileDir + "\\" + filter[i].headLine;
                await this.parseParams(filter[i], project, single, unit, directory, args, workBookList);
            }
        }
    }


    async getWorkSheetWithData(workbook, projectType, sheetName, args) {
        let worksheet = workbook.getWorksheet(sheetName);
        args["workbook"] = workbook;
        try {
            await this.switchWorkSheet(projectType, worksheet, args);
        } catch (e) {
            console.log("报表填充数据异常");
        }
        return worksheet;
    }

    async switchWorkSheet(projectType, worksheet, args) {
        if (projectType == ProjectLevelConstant.construct) {
            let constructProjectJBXX = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //工程项目层级
                case "A.0.1 封面":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet1(constructProjectJBXX, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 7;
                    headArgsQd1['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    break;
                case "A.0.2 签署页":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet2(constructProjectJBXX, worksheet);
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 8;
                    headArgsQd2['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    break;
                case "A.0.3 目录":
                    // let constructProjectTotal = await this.getconstructProjectSheet3(args, constructProjectJBXX);
                    // await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet3(constructProjectTotal, worksheet);
                    // let headArgsQd3 = {};
                    // headArgsQd3['headStartNum'] = 1;
                    // headArgsQd3['headEndNum'] = 11;
                    // headArgsQd3['titlePage'] = true;
                    // await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    break;
                case "A.0.4 编制说明":
                    let bzsm = constructProjectJBXX.filter(object => object.name == "编制说明")[0];
                    if (ObjectUtils.isNotEmpty(bzsm) && ObjectUtils.isNotEmpty(bzsm.remark)) {
                        let remark = await GSExcelUtil.removeTags(bzsm.remark);
                        await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet4(remark, worksheet);
                    }
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 2;
                    headArgsQd4['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    break;
                case "B.0.1 总概算表":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet5(constructProjectJBXX, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 7;
                    headArgsQd5['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    break;
                case "B.0.2 总概算表":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet6(constructProjectJBXX, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 7;
                    headArgsQd6['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    break;
                case "B.0.3 其他费用计算表":
                    let constructProjectSheet7List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet7List(args);
                    constructProjectJBXX["projectQtfyjsbHeji"] = args["projectQtfyjsbHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet7(constructProjectJBXX, constructProjectSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 3;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell7 = GSExcelUtil.findValueCell(worksheet, "合计");
                    let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(13, row7.number + 1, worksheet, workSheetGeshi, 4, 8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.5 概算汇总表":
                    let constructProjectSheet8List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet8List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet8(constructProjectJBXX, constructProjectSheet8List, worksheet);
                    let headArgsQd8 = {};
                    headArgsQd8['headStartNum'] = 1;
                    headArgsQd8['headEndNum'] = 4;
                    headArgsQd8['titlePage'] = false;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.6 概算汇总表（含工程建设其他费细项）":
                    let constructProjectSheet9List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet9List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet9(constructProjectJBXX, constructProjectSheet9List, worksheet);
                    let headArgsQd9 = {};
                    headArgsQd9['headStartNum'] = 1;
                    headArgsQd9['headEndNum'] = 4;
                    headArgsQd9['titlePage'] = false;
                    headArgsQd9['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.7 概算汇总表（金额为0不输出）":
                    let constructProjectSheet10List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet10List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet10(constructProjectJBXX, constructProjectSheet10List, worksheet);
                    let headArgsQd10 = {};
                    headArgsQd10['headStartNum'] = 1;
                    headArgsQd10['headEndNum'] = 4;
                    headArgsQd10['titlePage'] = false;
                    headArgsQd10['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.8 概算汇总表（万元）":
                    let constructProjectSheet11List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet11List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet11(constructProjectJBXX, constructProjectSheet11List, worksheet);
                    let headArgsQd11 = {};
                    headArgsQd11['headStartNum'] = 1;
                    headArgsQd11['headEndNum'] = 4;
                    headArgsQd11['titlePage'] = false;
                    headArgsQd11['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd11);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.9 工程项目材料数量及价格表":
                    let constructProjectSheet12List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet12List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet12(constructProjectJBXX, constructProjectSheet12List, worksheet);
                    let headArgsQd12 = {};
                    headArgsQd12['headStartNum'] = 1;
                    headArgsQd12['headEndNum'] = 3;
                    headArgsQd12['titlePage'] = false;
                    headArgsQd12['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd12);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(23, worksheet._rows.length + 1, worksheet, workSheetGeshi, 4, 8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.10 总概算对比表":
                    let constructProjectSheet13List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet13List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet13(constructProjectJBXX, constructProjectSheet13List, worksheet);
                    let headArgsQd13 = {};
                    headArgsQd13['headStartNum'] = 1;
                    headArgsQd13['headEndNum'] = 4;
                    headArgsQd13['titlePage'] = false;
                    headArgsQd13['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(25, worksheet._rows.length + 1, worksheet, workSheetGeshi, 7, 14);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.12 进口设备材料货价及从属费用计算表":
                    let constructProjectSheet14List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet14List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet14(constructProjectJBXX, constructProjectSheet14List, worksheet);
                    let headArgsQd14 = {};
                    headArgsQd14['headStartNum'] = 1;
                    headArgsQd14['headEndNum'] = 4;
                    headArgsQd14['titlePage'] = false;
                    headArgsQd14['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd14);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(27, worksheet._rows.length + 1, worksheet, workSheetGeshi, 7, 13);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    if (ObjectUtils.isNotEmpty(constructProjectSheet14List)) {
                        await this.service.PreliminaryEstimate.gsExportQueryService.sheetMerge(worksheet, headArgsQd14['headEndNum'], "");
                    }
                    break;
                case "B.0.13 国内采购设备表":
                    let constructProjectSheet15List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet15List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet15(constructProjectJBXX, constructProjectSheet15List, worksheet);
                    let headArgsQd15 = {};
                    headArgsQd15['headStartNum'] = 1;
                    headArgsQd15['headEndNum'] = 3;
                    headArgsQd15['titlePage'] = false;
                    headArgsQd15['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd15);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(29, worksheet._rows.length + 1, worksheet, workSheetGeshi, 5, 10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.14 项目设备费汇总表":
                    let constructProjectSheet16List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet16List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet16(constructProjectJBXX, constructProjectSheet16List, worksheet);
                    let headArgsQd16 = {};
                    headArgsQd16['headStartNum'] = 1;
                    headArgsQd16['headEndNum'] = 3;
                    headArgsQd16['titlePage'] = false;
                    headArgsQd16['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd16);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(31, worksheet._rows.length + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    break;
                case "附表B.0.1 总概算表":
                    let constructProjectSheet17List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet17List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet17(constructProjectJBXX, constructProjectSheet17List, worksheet);
                    let headArgsQd17 = {};
                    headArgsQd17['headStartNum'] = 1;
                    headArgsQd17['headEndNum'] = 4;
                    headArgsQd17['titlePage'] = false;
                    headArgsQd17['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd17);
                    await GSExcelUtil.copyRowsWithOtherSheetHeji(33, worksheet._rows.length + 1, worksheet, workSheetGeshi, 4, 8, 10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "附表B.0.2 总概算表":
                    let constructProjectSheet18List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSheet18List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet18(constructProjectJBXX, constructProjectSheet18List, worksheet);
                    let headArgsQd18 = {};
                    headArgsQd18['headStartNum'] = 1;
                    headArgsQd18['headEndNum'] = 4;
                    headArgsQd18['titlePage'] = false;
                    headArgsQd18['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd18);
                    await GSExcelUtil.copyRowsWithOtherSheetHeji(35, worksheet._rows.length + 1, worksheet, workSheetGeshi, 4, 8, 11);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "1 封面（适用于中介单位）":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet19(constructProjectJBXX, worksheet);
                    let headArgsQd19 = {};
                    headArgsQd19['headStartNum'] = 1;
                    headArgsQd19['headEndNum'] = 9;
                    headArgsQd19['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd19);
                    break;
                case "2 签署页（适用于中介单位）":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet20(constructProjectJBXX, worksheet);
                    let headArgsQd20 = {};
                    headArgsQd20['headStartNum'] = 1;
                    headArgsQd20['headEndNum'] = 11;
                    headArgsQd20['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd20);
                    break;
            }
        }
        if (projectType == ProjectLevelConstant.single) {
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            let constructSingleJBXX = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSingleJBXX(args);
            switch (worksheet.name) {
                case "B.0.05 综合概算表":
                    let constructSingleSheet1List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructSingleSheet1List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSingleSheet1(constructSingleJBXX, constructSingleSheet1List, worksheet);
                    let headArgsQd21 = {};
                    headArgsQd21['headStartNum'] = 1;
                    headArgsQd21['headEndNum'] = 4;
                    headArgsQd21['titlePage'] = false;
                    headArgsQd21['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd21);
                    await GSExcelUtil.copyRowsWithOtherSheetHeji(1, worksheet._rows.length + 1, worksheet, workSheetGeshi, 2, 5, 7);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructSingleJBXX, worksheet);
                    break;
                case "B.0.11 综合概算对比表":
                    let constructSingleSheet2List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructSingleSheet2List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSingleSheet2(constructSingleJBXX, constructSingleSheet2List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(3, worksheet._rows.length + 1, worksheet, workSheetGeshi, 5, 12);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructSingleJBXX, worksheet);
                    break;
            }
        }
        if (projectType == ProjectLevelConstant.unit) {
            let constructUnitJBXX = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructProjectSingleUnitJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //单位工程层级
                case "封面":
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet1(constructUnitJBXX, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 11;
                    headArgsQd1['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    break;
                case "编制说明":
                    let unitBzsm = constructUnitJBXX.filter(object => object.name == "编制说明")[0];
                    if (ObjectUtils.isNotEmpty(unitBzsm) && ObjectUtils.isNotEmpty(unitBzsm.remark)) {
                        let remark = await GSExcelUtil.removeTags(unitBzsm.remark);
                        await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet4(remark, worksheet);
                    }
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 2;
                    headArgsQd2['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    break;
                case "单位工程概预算表(A4竖)":
                    let constructUnitSheet3List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet3List(args);
                    constructUnitJBXX["unitDwgcgysba4sHeji"] = args["unitDwgcgysba4sHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet3(constructUnitJBXX, constructUnitSheet3List, worksheet);
                    let headArgsQd3 = {};
                    headArgsQd3['headStartNum'] = 1;
                    headArgsQd3['headEndNum'] = 4;
                    headArgsQd3['titlePage'] = false;
                    headArgsQd3['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程概预算表(A4横)":
                    let constructUnitSheet4List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet4List(args);
                    constructUnitJBXX["unitDwgcgysba4hHeji"] = args["unitDwgcgysba4hHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet4(constructUnitJBXX, constructUnitSheet4List, worksheet);
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 4;
                    headArgsQd4['titlePage'] = false;
                    headArgsQd4['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程概预算表(自然单位)":
                    let constructUnitSheet30List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet30List(args);
                    constructUnitJBXX["unitDwgcgysba4sHeji"] = args["unitDwgcgysba4sHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet3(constructUnitJBXX, constructUnitSheet30List, worksheet);
                    let headArgsQd30 = {};
                    headArgsQd30['headStartNum'] = 1;
                    headArgsQd30['headEndNum'] = 4;
                    headArgsQd30['titlePage'] = false;
                    headArgsQd30['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd30);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程费用表":
                    let constructUnitSheet5List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet5List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet5(constructUnitJBXX, constructUnitSheet5List, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 3;
                    headArgsQd5['titlePage'] = false;
                    headArgsQd5['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    await this.calDwgcfyHeji(args["unitDwgcfyHeji"], worksheet);
                    // let heJiCell5 = GSExcelUtil.findValueCell(worksheet, "合计");
                    // let row5 = worksheet.getRow(heJiCell5.cell._row._number);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(9, row5.number + 1, worksheet, workSheetGeshi, 2, 4, 6);
                    // await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程费用表(建筑工程)":
                    let constructUnitSheet6List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet6List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet6(constructUnitJBXX, constructUnitSheet6List, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 3;
                    headArgsQd6['titlePage'] = false;
                    headArgsQd6['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    await this.calDwgcfyHeji(args["unitDwgcfyjzgcHeji"], worksheet);
                    // let heJiCell6 = GSExcelUtil.findValueCell(worksheet, "合计");
                    // let row6 = worksheet.getRow(heJiCell6.cell._row._number);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(11, row6.number + 1, worksheet, workSheetGeshi, 4, 8, 12);
                    // await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程费用表(安装工程)":
                    let constructUnitSheet7List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet7List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet7(constructUnitJBXX, constructUnitSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 3;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    await this.calDwgcfyHeji(args["unitDwgcfyzgcHeji"], worksheet);
                    // let heJiCell7 = GSExcelUtil.findValueCell(worksheet, "合计");
                    // let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(13, row7.number + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    // await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程人材机汇总表":
                    let constructUnitSheet8List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet8List(args);
                    constructUnitJBXX["unitRcjHeji"] = args["unitRcjHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet8(constructUnitJBXX, constructUnitSheet8List, worksheet);
                    let headArgsQd8 = {};
                    headArgsQd8['headStartNum'] = 1;
                    headArgsQd8['headEndNum'] = 3;
                    headArgsQd8['titlePage'] = false;
                    headArgsQd8['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程人材机价差表（人材机汇总）":
                    let constructUnitSheet9List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet9List(args);
                    constructUnitJBXX['unitRcjjcHeji'] = args["unitRcjjcHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet9(constructUnitJBXX, constructUnitSheet9List, worksheet);
                    let headArgsQd9 = {};
                    headArgsQd9['headStartNum'] = 1;
                    headArgsQd9['headEndNum'] = 3;
                    headArgsQd9['titlePage'] = false;
                    headArgsQd9['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程三材汇总表":
                    let constructUnitSheet10List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet10List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet10(constructUnitJBXX, constructUnitSheet10List, worksheet);
                    let headArgsQd10 = {};
                    headArgsQd10['headStartNum'] = 1;
                    headArgsQd10['headEndNum'] = 3;
                    headArgsQd10['titlePage'] = false;
                    headArgsQd10['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程主材表":
                    let constructUnitSheet11List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet11List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet11(constructUnitJBXX, constructUnitSheet11List, worksheet);
                    let headArgsQd11 = {};
                    headArgsQd11['headStartNum'] = 1;
                    headArgsQd11['headEndNum'] = 3;
                    headArgsQd11['titlePage'] = false;
                    headArgsQd11['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd11);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "预拌砼汇总表":
                    let constructUnitSheet12List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet12List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet12(constructUnitJBXX, constructUnitSheet12List, worksheet);
                    let headArgsQd12 = {};
                    headArgsQd12['headStartNum'] = 1;
                    headArgsQd12['headEndNum'] = 3;
                    headArgsQd12['titlePage'] = false;
                    headArgsQd12['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd12);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "独立费表":
                    let constructUnitSheet13List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet13List(args);
                    constructUnitJBXX["unitDlfHeji"] = args["unitDlfHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet13(constructUnitJBXX, constructUnitSheet13List, worksheet);
                    let headArgsQd13 = {};
                    headArgsQd13['headStartNum'] = 1;
                    headArgsQd13['headEndNum'] = 3;
                    headArgsQd13['titlePage'] = false;
                    headArgsQd13['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "主要材料价格表":
                    let constructUnitSheet14List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet14List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet14(constructUnitJBXX, constructUnitSheet14List, worksheet);
                    let headArgsQd14 = {};
                    headArgsQd14['headStartNum'] = 1;
                    headArgsQd14['headEndNum'] = 3;
                    headArgsQd14['titlePage'] = false;
                    headArgsQd14['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd14);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位全费指标分析表":
                    let constructUnitSheet15List = await this.service.PreliminaryEstimate.gsExportQueryService.getconstructUnitSheet15List(args);
                    constructUnitJBXX["unitDwgcgysba4sHeji"] = args["unitDwgcgysba4sHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet15(constructUnitJBXX, constructUnitSheet15List, worksheet);
                    let headArgsQd15 = {};
                    headArgsQd15['headStartNum'] = 1;
                    headArgsQd15['headEndNum'] = 4;
                    headArgsQd15['titlePage'] = false;
                    headArgsQd15['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd15);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                default:
            }
        }

    }

    async getRatioWidthSheet(workSheet) {

        let pageSetupJsonObject = {
            "fitToPage": false,
            "margins": {
                "left": 0.585166666666667,
                "right": 0.585166666666667,
                "top": 0.979166666666667,
                "bottom": 0,
                "header": 0.979166666666667,
                "footer": 0
            },
            "paperSize": 9,
            "orientation": "portrait",
            "horizontalDpi": 4294967295,
            "verticalDpi": 4294967295,
            "pageOrder": "downThenOver",
            "blackAndWhite": false,
            "draft": false,
            "cellComments": "None",
            "errors": "displayed",
            "scale": 100,
            "fitToWidth": 1,
            "fitToHeight": 1,
            "firstPageNumber": 1,
            "useFirstPageNumber": false,
            "usePrinterDefaults": false,
            "copies": 1,
            "showRowColHeaders": false,
            "showGridLines": false,
            "horizontalCentered": true,
            "verticalCentered": false
        }
        let columnWidthTotal = 0;
        for (let i = 0; i < workSheet._columns.length; i++) {
            let columnWidth = workSheet._columns[i].width;
            columnWidthTotal += columnWidth;
        }
        let differ = 0;
        if (workSheet.name.includes("【封面3】审核签署表")
            || workSheet.name.includes("【封面4】工程审核认证单")
            || workSheet.name.includes("【封面5】工程造价审查书")
            || workSheet.name.includes("【项1】工程审核汇总对比表")
            || workSheet.name.includes("【人材机1】人材机汇总对比表")
            || workSheet.name.includes("【费1】单位工程审核对比表")
            || workSheet.name.includes("【分部1】分部分项清单对比表")
            || workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")
            || workSheet.name.includes("【措施1】措施项目审核对比表")
            || workSheet.name.includes("【人材机2】人材机审核对比表")
            || workSheet.name.includes("【人材机3】人材机价差汇总对比表")
            || workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")
            || workSheet.name.includes("【规费1】规费明细对比表")
            || workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            differ = (ExcelEnum.A4WidthHorizontal - ExcelEnum.A4LeftHorizontal - ExcelEnum.A4RightHorizontal);
        } else {
            differ = (ExcelEnum.A4Width - ExcelEnum.A4Left - ExcelEnum.A4Right);
        }

        for (let i = 0; i < workSheet._columns.length; i++) {
            workSheet._columns[i].width = (workSheet._columns[i].width / columnWidthTotal) * differ;
        }
        if (workSheet.name.includes("封面") || workSheet.name.includes("扉页") || workSheet.name.includes("编制说明")
            || workSheet.name.includes("工程项目总价表")) {
            workSheet.pageSetup = pageSetupJsonObject;
        }
    }


}

GsPdfService.toString = () => '[class GsPdfService]';
module.exports = GsPdfService;
