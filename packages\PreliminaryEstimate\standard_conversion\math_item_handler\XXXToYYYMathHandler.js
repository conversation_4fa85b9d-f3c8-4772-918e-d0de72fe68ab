const MathItemHandler = require("./mathItemHandler");
const {ObjectUtil} = require("../../../../common/ObjectUtil");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. XXXX 替换为 YYYY，保留消耗量
 */
class XXXToYYYMathHandler extends MathItemHandler{

    async activeRCJ() {
        let item = this.mathItem;
        if(item.fromRCJCode == item.toRCJCode && item.fromRCJLibraryCode == item.toRCJLibraryCode){
            return;
        }

        let fromRCjS = this.findActiveRCJByCode(item.fromRCJCode);

        let toRcj = await this.conversionService.getRCJ(this.ctx.constructId, this.ctx.unitId,item.toRCJLibraryCode, item.toRCJCode, this.ctx.de)
        // 只修改第一条匹配的材料
        if(ObjectUtil.isNotEmpty(fromRCjS)){
            if(item.fromRCJCode != fromRCjS[0].materialCode) {
                this.notStandardActiveRcjCodes.push([item.fromRCJCode, fromRCjS[0].materialCode]);
            }
            let newRcj = await this.editRcj(fromRCjS[0],toRcj);
            item.activeRCJs.push(newRcj);
        }
        // for(let curRcj of fromRCjS){
        //     let newRcj = await this.editRcj(curRcj,toRcj);
        //     item.activeRCJs.push(newRcj);
        // }
    }
    async computeResQty() {
        // 什么都不做
    }
}

module.exports = XXXToYYYMathHandler;