const {Service} = require('../../../core');
const {ObjectUtil} = require("../../../common/ObjectUtil");
const ProjectDomain = require("../domains/ProjectDomain");
const DeDomain = require("../domains/DeDomain");
const StandardDeModel = require("../domains/deProcessor/models/StandardDeModel");
const {Snowflake} = require("../utils/Snowflake");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const DeTypeConstants = require("../constants/DeTypeConstants");
const BranchProjectDisplayConstant = require("../constants/BranchProjectDisplayConstant");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const WildcardMap = require("../core/container/WildcardMap");
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ConvertUtil} = require("../utils/ConvertUtils")
const AnZhuangJiQqConstants = require("../constants/AnZhuangJiQqConstants");
const DeQualityUtils = require("../domains/utils/DeQualityUtils");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const ResourceKindConstants = require("../constants/ResourceKindConstants");
const {GljDeContentModel} = require("../models/GljDeContentModel");
const TSRCJConstants = require("../constants/TSRCJConstants");
const UnitUtils = require("../core/tools/UnitUtils");
const DeUtils = require('../domains/utils/DeUtils');
const {dialog} = require("electron");
const XLSX = require('xlsx');
const Excel = require('exceljs');
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");

const OpenLevelList = {
    ALL: {type: 0, name: "展开所有"},
    ONE: {type: 1, name: "一级分部"},
    TWO: {type: 2, name: "二级分部"},
    THREE: {type: 3, name: "三级分部"},
    FOUR: {type: 4, name: "四级分部"},
    DE: {type: 5, name: "子目"},
    ZHUCAI: {type: 6, name: "主材/设备"}
}

/**
 * 定额 service
 * @class
 */
class GljDeService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 添加定额
     * @param deRow
     * @param baseDeId
     * @returns {Promise<ResponseData>}
     */
    async createDeRowAppendBaseDe(deRow, baseDeId) {
        let index;
        let prevDeRow;
        if(ObjectUtil.isNotEmpty(deRow.prevRowId))
        {
            prevDeRow = prevDeRow = ProjectDomain.getDomain(deRow.constructId).deDomain.getDeById(deRow.prevRowId);
            index = prevDeRow.index + 1;
        }

        let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
        await ProjectDomain.getDomain(deRow.constructId).deDomain.createDeRow(deRowModel,index);
        return await ProjectDomain.getDomain(deRowModel.constructId).deDomain.appendBaseDe(deRowModel.constructId,deRowModel.unitId,baseDeId,deRowModel.sequenceNbr,true);
    }

    /**
     *
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async appendBaseDe2De(constructId, unitId, deStandardId, deRowId) {
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        let deRow ;
        deRow = await deDomain.appendBaseDe(constructId, unitId, deStandardId, deRowId,true);
        await deDomain.extendQuantity(constructId, unitId, deRow.sequenceNbr);
        return deRow;
    }

    /**
     * 更新定额单价
     * @param constructId
     * @param unitId
     * @param deRowId
     * @param price
     * @param isTc
     * @returns {Promise<void>}
     */
    async updatePrice(constructId, unitId, deRowId, price, isTc = false) {
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        await deDomain.updatePrice(constructId, unitId, deRowId, price, isTc);
    }

    /**
     * 更新定额消耗量
     * @param constructId
     * @param unitId
     * @param deRowId
     * @param resQty
     * @returns {Promise<void>}
     */
    async updateResQty(constructId, unitId, deRowId, resQty)
    {
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        await deDomain.updateResQty(constructId, unitId, deRowId, resQty);
    }


    /**
     * 展开至那级集合查询
     * @param constructId
     * @param unitId
     */
    async openLevelCheckList(constructId, unitId) {
        let result = OpenLevelList;
        result.ALL.hasCheck = false;
        result.ONE.hasCheck = false;
        result.TWO.hasCheck = false;
        result.THREE.hasCheck = false;
        result.FOUR.hasCheck = false;
        result.DE.hasCheck = false;
        result.ZHUCAI.hasCheck = false;

        let deList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
        if (deList.length > 1) {
            result.ALL.hasCheck = true;
            result.ZHUCAI.hasCheck = true;
        }

        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let top = deList.find(o => o.sequenceNbr === unit.defaultDeId);
        let ONEList = deList.filter(o => o.parentId === top.sequenceNbr && (o.type === DeTypeConstants.DE_TYPE_FB || o.type === DeTypeConstants.DE_TYPE_ZFB));
        if (ObjectUtil.isNotEmpty(ONEList)) {
            result.ONE.hasCheck = true;
            ONEList.forEach(item => {
                let TWOList = deList.filter(o => o.parentId === item.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_ZFB);
                if (ObjectUtil.isNotEmpty(TWOList)) {
                    result.TWO.hasCheck = true;
                    TWOList.forEach(item => {
                        let THREEList = deList.filter(o => o.parentId === item.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_ZFB);
                        if (ObjectUtil.isNotEmpty(THREEList)) {
                            result.THREE.hasCheck = true;
                            THREEList.forEach(item => {
                                let FOURList = deList.filter(o => o.parentId === item.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_ZFB);
                                if (ObjectUtil.isNotEmpty(FOURList)) {
                                    result.FOUR.hasCheck = true;
                                }
                            })
                        }
                    })
                }
            })
        }

        let DEList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_EMPTY || o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_USER_DE);
        if (ObjectUtil.isNotEmpty(DEList)) {
            for (const o of DEList) {
                let find = deList.find(p => p.sequenceNbr === o.parentId);
                if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DELIST && find.type !== DeTypeConstants.DE_TYPE_DE) {
                    //只有最父级才展开
                    if (o.type === DeTypeConstants.DE_TYPE_EMPTY || o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_USER_DE) {
                        result.DE.hasCheck = true;
                        break;
                    } else if (o.type === DeTypeConstants.DE_TYPE_DE) {
                        if (o.libraryCode !== AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE && o.libraryCode !== AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE_JZ) {
                            result.DE.hasCheck = true;
                            break;
                        } else {
                            if (ObjectUtils.isNotEmpty(o.classlevel02) && o.classlevel02.includes("补充定额")) {
                                result.DE.hasCheck = true;
                                break;
                            }
                        }
                    }
                }
            }
        }
        let DEList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
        if (ObjectUtil.isNotEmpty(DEList1)) {
            for (const o of DEList1) {
                let find = deList.find(p => p.sequenceNbr === o.parentId);
                if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DELIST && find.type !== DeTypeConstants.DE_TYPE_DE) {
                    result.DE.hasCheck = true;
                    break;
                }
            }
        }


        // let ZHUCAIList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_USER_DE);
        // if (ObjectUtil.isNotEmpty(ZHUCAIList)) {
        //     for (const o of ZHUCAIList) {
        //         let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, o.deRowId, o.type);
        //         if (ObjectUtil.isNotEmpty(rcjLists)) {
        //             let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
        //             if (ObjectUtil.isNotEmpty(filter)) {
        //                 result.ZHUCAI.hasCheck = true;
        //             }
        //         }
        //
        //     }
        // }
        //
        // let ZHUCAIList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
        // if (ObjectUtil.isNotEmpty(ZHUCAIList1)) {
        //     ZHUCAIList1.forEach(p => {
        //         let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(p.sequenceNbr);
        //         if (de.deResourceKind == 4 || de.deResourceKind == 5) {
        //             result.ZHUCAI.hasCheck = true;
        //         }
        //         result.DE.hasCheck = true;
        //     });
        // }

        return result;
    }


    /**
     * 展开至
     * @param constructId
     * @param unitId
     * @param type
     */
    async openLevel(constructId, unitId, type) {
        let ONEList = [];
        let TWOList = [];
        let THREEList = [];
        let FOURList = [];
        let DEList = [];

        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let deList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
        let top = deList.find(o => o.sequenceNbr === unit.defaultDeId);

        if (type == OpenLevelList.ALL.type) {
            //展开所有
            deList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.close) {
                    o.displaySign = BranchProjectDisplayConstant.open;
                    ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.ONE.type) {
            //展开至一级
            ONEList = await this.openLevelOne(constructId, top, deList);
            //二级的数据折叠
            ONEList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.open) {
                    o.displaySign = BranchProjectDisplayConstant.close;
                    ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.TWO.type) {
            //展开至一级
            ONEList = await this.openLevelOne(constructId, top, deList);
            //展开至二级
            TWOList = await this.openLevelTwo(constructId, ONEList, deList);

            //三级的数据折叠
            TWOList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.open) {
                    o.displaySign = BranchProjectDisplayConstant.close;
                    ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.THREE.type) {
            //展开至一级
            ONEList = await this.openLevelOne(constructId, top, deList);
            //展开至二级
            TWOList = await this.openLevelTwo(constructId, ONEList, deList);
            //展开至三级
            THREEList = await this.openLevelTwo(constructId, TWOList, deList);

            //三级的数据折叠
            THREEList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.open) {
                    o.displaySign = BranchProjectDisplayConstant.close;
                    ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.FOUR.type) {
            //展开至一级
            ONEList = await this.openLevelOne(constructId, top, deList);
            //展开至二级
            TWOList = await this.openLevelTwo(constructId, ONEList, deList);
            //展开至三级
            THREEList = await this.openLevelTwo(constructId, TWOList, deList);
            //展开至四级
            FOURList = await this.openLevelTwo(constructId, THREEList, deList);

            //四级的数据折叠
            FOURList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.open) {
                    o.displaySign = BranchProjectDisplayConstant.close;
                    ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.DE.type) {
            let DEList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_EMPTY || o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_USER_DE);
            let list = [];
            for (let item of DEList) {
                let find = deList.find(p => p.sequenceNbr === item.parentId);
                if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DELIST && find.type !== DeTypeConstants.DE_TYPE_DE) {
                    if (item.type === DeTypeConstants.DE_TYPE_EMPTY || item.type === DeTypeConstants.DE_TYPE_DELIST || item.type === DeTypeConstants.DE_TYPE_USER_DE) {
                        let openData = await this.openLineAndParent1(item, deList);
                        openData.forEach(p => {
                            list.push(p.sequenceNbr);
                        });
                    } else if (item.type === DeTypeConstants.DE_TYPE_DE) {
                        if (item.libraryCode !== AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE && item.libraryCode !== AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE_JZ) {
                            let openData = await this.openLineAndParent1(item, deList);
                            openData.forEach(p => {
                                list.push(p.sequenceNbr);
                            });
                        } else {
                            if (ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.includes("补充定额")) {
                                let openData = await this.openLineAndParent1(item, deList);
                                openData.forEach(p => {
                                    list.push(p.sequenceNbr);
                                });
                            }
                        }
                    }
                }
            }
            let DEList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
            if (ObjectUtil.isNotEmpty(DEList1)) {
                for (const item1 of DEList1) {
                    let find = deList.find(p => p.sequenceNbr === item1.parentId);
                    if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DELIST && find.type !== DeTypeConstants.DE_TYPE_DE) {
                        let openData = await this.openLineAndParent1(item1, deList);
                        openData.forEach(p => {
                            list.push(p.sequenceNbr);
                        });
                    }
                }
            }


            let noFbList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_FB && !list.includes(o.sequenceNbr));
            if (ObjectUtil.isNotEmpty(noFbList)) {
                noFbList.forEach(t => {
                    let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(t.sequenceNbr);
                    if (de.displaySign === BranchProjectDisplayConstant.open) {
                        de.displaySign = BranchProjectDisplayConstant.close;
                    }
                });
            }

            for (let item of DEList) {
                if (!list.includes(item.sequenceNbr)) {
                    let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(item.sequenceNbr);
                    if (de.displaySign === BranchProjectDisplayConstant.open) {
                        de.displaySign = BranchProjectDisplayConstant.close;
                    }

                    let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, item.sequenceNbr, item.type);
                    if (ObjectUtil.isNotEmpty(rcjLists)) {
                        let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
                        if (ObjectUtil.isNotEmpty(filter)) {
                            de.displaySign = BranchProjectDisplayConstant.close;
                        }
                    }
                }
            }
        } else if (type == OpenLevelList.ZHUCAI.type) {

            //展开所有
            deList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.close) {
                    o.displaySign = BranchProjectDisplayConstant.open;
                    ProjectDomain.getDomain(constructId).deDomain.updateDe(o);
                }
            })


            // let ZHUCAIList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_USER_DE);
            // let openDataList = [];
            // if (ObjectUtil.isNotEmpty(ZHUCAIList)) {
            //     for (let item of ZHUCAIList) {
            //         let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(item.sequenceNbr);
            //
            //         let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, item.deRowId, item.type);
            //         if (ObjectUtil.isNotEmpty(rcjLists)) {
            //             let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
            //             if (ObjectUtil.isNotEmpty(filter)) {
            //                 let openData = await this.openLineAndParent1(item, deList);
            //                 openData.forEach(p => {
            //                     openDataList.push(p.sequenceNbr);
            //                 });
            //                 openDataList.push(item.sequenceNbr);
            //                 de.displaySign = BranchProjectDisplayConstant.open;
            //             } else {
            //                 if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                     de.displaySign = BranchProjectDisplayConstant.close;
            //                 }
            //             }
            //         } else {
            //             if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                 de.displaySign = BranchProjectDisplayConstant.close;
            //             }
            //         }
            //     }
            // }
            //
            //
            // let ZHUCAIList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
            // if (ObjectUtil.isNotEmpty(ZHUCAIList1)) {
            //     for (const p of ZHUCAIList1) {
            //         let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(p.sequenceNbr);
            //         if (de.deResourceKind == 4 || de.deResourceKind == 5) {
            //             let openData = await this.openLineAndParent1(p, deList);
            //             openData.forEach(p => {
            //                 openDataList.push(p.sequenceNbr);
            //             });
            //             openDataList.push(p.sequenceNbr);
            //             de.displaySign = BranchProjectDisplayConstant.noSign;
            //         } else {
            //             if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                 de.displaySign = BranchProjectDisplayConstant.noSign;
            //             }
            //         }
            //     }
            // }
            //
            // if (ObjectUtil.isNotEmpty(openDataList)) {
            //     let noFbList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_FB && !openDataList.includes(o.sequenceNbr));
            //     if (ObjectUtil.isNotEmpty(noFbList)) {
            //         noFbList.forEach(t => {
            //             let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(t.sequenceNbr);
            //             if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                 de.displaySign = BranchProjectDisplayConstant.close;
            //             }
            //         });
            //     }
            // }
        }
    }


    async openLevelOne(constructId, top, deList) {
        top.displaySign = BranchProjectDisplayConstant.open;
        ProjectDomain.getDomain(constructId).deDomain.updateDe(top);
        return  deList.filter(o => o.parentId === top.sequenceNbr && (o.type === DeTypeConstants.DE_TYPE_FB || o.type === DeTypeConstants.DE_TYPE_ZFB));
    }

    async openLevelTwo(constructId, ONEList, deList) {
        let resultList = [];
        //展开至二级
        ONEList.forEach(item => {
            let TWOListLishi = deList.filter(o => o.parentId === item.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_ZFB);
            let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(item.sequenceNbr);
            if (ObjectUtil.isNotEmpty(TWOListLishi)) {
                de.displaySign = BranchProjectDisplayConstant.open;
                resultList = resultList.concat(TWOListLishi);
            } else {
                if (de.displaySign === BranchProjectDisplayConstant.open) {
                    de.displaySign = BranchProjectDisplayConstant.close;
                }
            }
        });
        return resultList;
    }


    async openLineAndParent(pointLine, mockAllData) {
        let allData = mockAllData;
        let selectData = [];
        this.getAllSelectData(pointLine, mockAllData, selectData);
        let parent = allData.find(item => item.sequenceNbr === pointLine.parentId);
        this.doChangeLineDisplayOne(selectData, parent, BranchProjectDisplayConstant.open);
        return selectData;
    }




    async openLineAndParent1(pointLine, mockAllData) {
        let allData = mockAllData;
        let selectData = [];
        this.getAllSelectData1(pointLine, mockAllData, selectData);
        let parent = allData.find(item => item.sequenceNbr === pointLine.parentId);
        this.doChangeLineDisplayOne(selectData, parent, BranchProjectDisplayConstant.open);
        return selectData;
    }


    async getAllSelectData1(pointLine, mockAllData, selectData) {
        if (ObjectUtil.isNotEmpty(pointLine.parentId)) {
            let parentDe = mockAllData.find(o => o.sequenceNbr === pointLine.parentId);
            if (ObjectUtil.isNotEmpty(parentDe)) {
                selectData.push(parentDe);
                await this.getAllSelectData1(parentDe, mockAllData, selectData);
            }
        }
    }


    getAllSelectData(pointLine, mockAllData, selectData) {
        let allData = mockAllData;
        if (pointLine.type === DeTypeConstants.DE_TYPE_DELIST || pointLine.type === DeTypeConstants.DE_TYPE_FB || pointLine.type === DeTypeConstants.DE_TYPE_ZFB || pointLine.type === DeTypeConstants.DE_TYPE_DEFAULT) {
            let influenceLines = this._getInfluence(allData, pointLine).datas;
            influenceLines.forEach(item => {
                selectData.push(item);
            });
        } else if (pointLine.type === DeTypeConstants.DE_TYPE_DE || pointLine.type === DeTypeConstants.DE_TYPE_USER_DE) {
            let childs = allData.filter(item => item.parentId === pointLine.sequenceNbr);
            childs.forEach(item => {
                selectData.push(item);
            });
        }

        let parent = allData.find(item => item.sequenceNbr === pointLine.parentId);
        if (parent) {
            this.getAllSelectData(parent, allData, selectData);
        }
    }

    /**
     * 根据 pointLine 返回 pointLine的所有子项目
     * 例： pointLine 为分部 返回子 分部 ，清单，定额
     *     pointLine 为清单 返回子 清单，定额
     * @param allData    分部分项或者措施项目全量数据
     * @param pointLine  父行
     * @param unInfluce  要剔除的行，可以不传
     * @return {{datas: *[], end: number, begin: number}|{datas, end, begin: number}|{datas: *, end: number, begin: number}}
     * @private
     */
    _getInfluence(allData, pointLine, unInfluce) {
        if (pointLine.type === BranchProjectLevelConstant.top) {
            return {
                "begin": 0,
                "end": allData.length,
                "datas": allData
            };
        }
        if (pointLine.type === BranchProjectLevelConstant.fb ||
            pointLine.type === BranchProjectLevelConstant.zfb ||
            pointLine.type === BranchProjectLevelConstant.qd) {
            let begin = -1;
            let end = allData.length;
            let parents = this._findParents(allData, pointLine);
            for (let index = 0; index < allData.length; ++index) {
                if (allData[index].sequenceNbr === pointLine.sequenceNbr) {
                    begin = index;
                    continue;
                }
                if (begin > -1) {
                    // 1.下一行级别高于当前行，2.parentId在当前行的 [parentIds] 中 则认为结束
                    if (Number.parseInt(pointLine.type) > Number.parseInt(allData[index].type) ||
                        parents[allData[index].parentId]) {
                        end = index;
                        break;
                    }
                }
            }

            let slice = allData.slice(begin, end);
            if(ObjectUtil.isNotEmpty(slice)){
                slice = slice.filter(o=>o.type !== DeTypeConstants.DE_TYPE_DE);
            }

            return {
                "begin": begin,
                "end": end,
                "datas": slice,
            }; // [bengin, end) bengin为被选中的行
        }
        return {
            "begin": 0, "end": 0, "datas": this._findLine(allData, pointLine)
        };
    }

    doChangeLineDisplayOne(influenceLines, parent, sign) {
        for (let index = 0; index < influenceLines.length; ++index) {
            if (influenceLines[index].type != BranchProjectLevelConstant.de) {
                if (influenceLines[index].sequenceNbr == parent.sequenceNbr) {
                    influenceLines[index].displaySign = sign;
                    // influenceLines[index].displayStatu = 10;
                    let de = ProjectDomain.getDomain(parent.constructId).ctx.deMap.getNodeById(influenceLines[index].sequenceNbr);
                    de.displaySign = BranchProjectDisplayConstant.open;
                } else if (influenceLines[index].parentId == parent.parentId) {
                    // influenceLines[index].displayStatu = 10;
                } else {
                    influenceLines[index].displaySign = sign;
                    // influenceLines[index].displayStatu = 10;
                    let de = ProjectDomain.getDomain(parent.constructId).ctx.deMap.getNodeById(influenceLines[index].sequenceNbr);
                    de.displaySign = BranchProjectDisplayConstant.open;
                }
            }
        }
    }

    _findParents(allData, line) {
        let parents = {};
        parents[line.parentId] = true;
        let lineIndex;
        let buffer = line;
        for (let index = 0; index < allData.length; ++index) {
            if (line.sequenceNbr === allData[index].sequenceNbr) {
                lineIndex = index;
                break;
            }
        }
        while (lineIndex > 0) {
            if (allData[lineIndex].sequenceNbr === buffer.parentId) {
                buffer = allData[lineIndex];
                parents[buffer.parentId] = true;
            }
            --lineIndex;
        }

        return parents;
    }

     _findLine(allData, pointLine) {
        let res = [];
        for (let i = 0; i < allData.length; ++i) {
            if (pointLine.sequenceNbr === allData[i].sequenceNbr) {
                res.push(allData[i]);
            }
        }
        return res;
    }


    async calculateZSFee(constructId, unitId, isCaclu) {
        return ;
        // throw new Error("not supported!")
        if(!isCaclu){
            return;
        }


        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let rcjAll = ProjectDomain.getDomain(constructId).ctx.resourceMap.getValues(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        let deList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId && item.type !== DeTypeConstants.DE_TYPE_FB && item.type !== DeTypeConstants.DE_TYPE_ZFB);
        let csxmList = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && item.type !== DeTypeConstants.DE_TYPE_FB && item.type !== DeTypeConstants.DE_TYPE_ZFB && item.type !== DeTypeConstants.DE_TYPE_DELIST);
        if (ObjectUtil.isNotEmpty(rcjAll)) {
            let rcjxxAll = rcjAll.filter(o => ZSFeeConstants.ZS_RCJ_LIST.includes(o.materialCode) && o.unit === "%");
            if (ObjectUtil.isNotEmpty(rcjxxAll)) {
                // let result = [];

                // rcjxxAll.forEach(o => {
                //     //筛选非装饰定额下的降效系数人材机
                //     let find = deList.find(p => p.deRowId === o.deRowId);
                //     //todo 增加章节判断
                //     if (!ZSFeeConstants.ZS_DE_LIST.includes(find.deCode)) {
                //         result.push(o);
                //     }
                // });

                if (ObjectUtil.isNotEmpty(rcjxxAll)) {
                    for (let item of rcjxxAll) {
                        //1查询跟人材机父级定额平级的定额
                        let rcjParentDe = deList.find(p => p.deRowId === item.deRowId);
                        let rcjParentCsxm = csxmList.find(p => p.deRowId === item.deRowId);
                        if (ObjectUtil.isEmpty(rcjParentDe) && ObjectUtil.isEmpty(rcjParentCsxm)) {
                            continue;
                        }

                        let baseCount = 0;
                        if (ObjectUtil.isNotEmpty(rcjParentDe)) {
                            //同级别除了自己，除了装饰定额、除了安装定额的定额数据
                            let deParentChildDeList = deList.filter(p => p.parentId === rcjParentDe.parentId && !ZSFeeConstants.ZS_DE_LIST.includes(p.deCode) && p.type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE && p.deRowId !== rcjParentDe.deRowId);
                            //2统计平级定额的计算基数 各自人工/机械定额价*各自消耗量）*除最父级外，其余各父级定额的消耗量
                            if (item.type === "人工费") {
                                baseCount = await this.calRSum(deParentChildDeList, deList, 0, 1);
                            } else if (item.type === "机械费") {
                                baseCount = await this.calJSum(deParentChildDeList, deList, 0, 1);
                            }
                            //3基数*父级定额工程量*自身消耗量/100
                            item.totalNumber = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.multiplyParams(baseCount, rcjParentDe.quantity, item.resQty), 100), 5);
                            item.updateCalcuTotalNumber = true;
                        } else if (ObjectUtil.isNotEmpty(rcjParentCsxm)) {
                            //同级别除了自己，除了装饰定额、除了安装定额的定额数据
                            let deParentChildDeList = csxmList.filter(p => p.parentId === rcjParentCsxm.parentId && !ZSFeeConstants.ZS_DE_LIST.includes(p.deCode) && p.type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE && p.deRowId !== rcjParentCsxm.deRowId);
                            //2统计平级定额的计算基数 各自人工/机械定额价*各自消耗量）*除最父级外，其余各父级定额的消耗量
                            if (item.type === "人工费") {
                                baseCount = await this.calRSum(deParentChildDeList, csxmList, 0, 1);
                            } else if (item.type === "机械费") {
                                baseCount = await this.calJSum(deParentChildDeList, csxmList, 0, 1);
                            }
                            //3基数*父级定额工程量*自身消耗量/100
                            item.totalNumber = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.multiplyParams(baseCount, rcjParentCsxm.quantity, item.resQty), 100), 5);
                            item.updateCalcuTotalNumber = true;
                        }

                        // if (ObjectUtils.isNotEmpty(item.isTempRemove) && item.isTempRemove === CommonConstants.COMMON_YES) {
                        //     // constructRcj.changeResQty = filter[0].resQty;
                        //     let rcjDeKey = WildcardMap.generateKey(unitId, item.deRowId) + WildcardMap.WILDCARD;
                        //     let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
                        //     let rcjRes = rcjList.find(o => o.sequenceNbr === item.sequenceNbr);
                        //     if (ObjectUtils.isNotEmpty(rcjRes)) {
                        //         rcjRes.changeResQty = rcjRes.resQty;
                        //     }
                        // }

                        await ProjectDomain.getDomain(constructId).getResourceDomain().notify(item);

                        // let constructRcj = {};
                        // constructRcj.totalNumber = item.totalNumber;
                        // let args = {};
                        // args.constructId = constructId;
                        // args.singleId = unit.parentId;
                        // args.unitId = unitId;
                        // args.deId = item.deId;
                        // args.rcjDetailId = item.sequenceNbr;
                        // args.constructRcj = constructRcj;
                        // await this.service.PreliminaryEstimate.gsRcjService.updateRcjDetail(args);

                        delete item.updateCalcuTotalNumber;
                    }
                    //todo 人材机数量修改，联动计算三材钢筋，并同步至其他费
                    // await this.service.gongLiaoJiProject.gljRcjCollectService.updateOtherProjectScGJ(constructId);
                }
            }

            let rcjcsAll = rcjAll.filter(o => TSRCJConstants.TS_RCJ_LIST.includes(o.materialCode.replace(/#\d+/g, '')));
            if (ObjectUtil.isNotEmpty(rcjcsAll)) {
                for (let item of rcjcsAll) {
                    let yssRowObject = deList.find(p => p.deRowId === item.deRowId);
                    let csxmRowObject = csxmList.find(p => p.deRowId === item.deRowId);
                    let parentDe = ObjectUtil.isNotEmpty(yssRowObject) ? yssRowObject : csxmRowObject;
                    let rdTotalSum = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.pricingMethod === 0 ? parentDe.rdTotalSum : parentDe.rTotalSum;
                    let jdTotalSum = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.pricingMethod === 0 ? parentDe.jdTotalSum : parentDe.jTotalSum;
                    let rjdTotalSum = NumberUtil.add(rdTotalSum, jdTotalSum);
                    item.totalNumber = NumberUtil.numberScale(NumberUtil.multiplyParams(item.resQty, rjdTotalSum, rjdTotalSum), 5);
                    if (ObjectUtil.isEmpty(csxmRowObject) && parentDe.isZj != "1") {
                        //预算书非措施定额下数量恒为0
                        item.totalNumber = 0;
                    }
                    await ProjectDomain.getDomain(constructId).getResourceDomain().notify(item);
                }
            }
        }
    }


    async calRSum(deParentChildDeList, deList, RSum, level) {
        for (let item of deParentChildDeList) {
            if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
                || item.type === DeTypeConstants.DE_TYPE_DELIST) {
                RSum = NumberUtil.add(RSum, item.RSum);
            }
        }
        return RSum;
    }

    async calJSum(deParentChildDeList, deList, JSum, level) {
        for (let item of deParentChildDeList) {
            if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
                || item.type === DeTypeConstants.DE_TYPE_DELIST) {
                JSum = NumberUtil.add(JSum, item.JSum);
            }
        }
        return JSum;
    }

    /**
     * 获取所有定额
     * @param constructId
     * @param unitId
     * @param deRowId
     * @returns {Promise<void>}
     */
    async getDeAllDepth(constructId, unitId, deRowId) {
        let deList = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByUnit(constructId, unitId);
        if (ObjectUtil.isEmpty(deList)) {
            return [];
        }
        deRowId = ObjectUtils.isNotEmpty(deRowId)? deRowId : deList[0].sequenceNbr;
        let deLists = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId,unitId,deRowId,undefined);
        //重置序号
        if(ObjectUtil.isNotEmpty(deLists)){
            let index = 1;
            let sets = new Set();

            deLists.forEach(element => {
                if(element.type === DeTypeConstants.DE_TYPE_DEFAULT
                    || element.type === DeTypeConstants.DE_TYPE_FB
                    || element.type === DeTypeConstants.DE_TYPE_ZFB
                ){
                    sets.add(element.sequenceNbr);
                }
            });

            let type04CloseList = [];

            deLists.forEach(element => {
                if(element.type !== DeTypeConstants.DE_TYPE_DEFAULT
                    && element.type !== DeTypeConstants.DE_TYPE_FB
                    && element.type !== DeTypeConstants.DE_TYPE_ZFB
                    && sets.has(element.parentId)
                ){
                    element.dispNo = index;
                    index++;
                }

                if (ObjectUtil.isEmpty(element.isShowAnnotations)) {
                    element.isShowAnnotations = false;
                }

                if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && element.displaySign === BranchProjectDisplayConstant.noSign) {
                    //如果定额无箭头，则展开
                    let filter = deLists.filter(o => o.parentId === element.sequenceNbr);
                    if (ObjectUtil.isNotEmpty(filter)) {
                        element.displaySign = BranchProjectDisplayConstant.open;
                    }
                }

                if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && element.displaySign === BranchProjectDisplayConstant.close) {
                    //如果定额收起，则不展示05类型
                    type04CloseList.push(element.deRowId);
                }

            });

            // if(ObjectUtil.isNotEmpty(type04CloseList)){
            //     deLists = deLists.filter(p=>ObjectUtil.isEmpty(p.parentId) || !type04CloseList.includes(p.parentId));
            // }
        }
        return deLists;
    }

    /**
     * 替换定额文本
     * @param deRowId  替换的字段
     * @param args 包含查找参数，不要乱删，需要与search同步改
     */
    replaceDeTxt(constructId,deRowIds, resultDes, conditions , replaceTxt){
        //检查是否需要替换
        let isReplace = false;
        for(let field of Object.keys(conditions)) {
            let searchTxt = conditions[field][0].value;
            if (ObjectUtil.isNotEmpty(searchTxt) || ObjectUtil.isNotEmpty(replaceTxt)) {
                isReplace = true;
            }
        }
        
        if(ObjectUtil.isNotEmpty(deRowIds) && isReplace){            
            //同步更新内存数据
            for(let sequenceNbr of deRowIds){
                let searchNode = resultDes.find(o=>o.sequenceNbr === sequenceNbr);
                let deNode = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(sequenceNbr);
                for(let field of Object.keys(conditions)) {
                    let searchTxt = conditions[field][0].value;
                    if (ObjectUtil.isEmpty(searchTxt) && ObjectUtil.isEmpty(replaceTxt)) {
                        continue;
                    }
                    if (ObjectUtil.isEmpty(searchTxt)) {
                        deNode[field] = replaceTxt;
                        searchNode?searchNode[field] = replaceTxt : null;
                    }else{
                        deNode[field] = deNode[field]?.replace(searchTxt, replaceTxt);
                        searchNode?searchNode[field] = searchNode[field]?.replace(searchTxt, replaceTxt) : null;
                    }
                }
            }
        }
        //再次执行查找功能
        return resultDes;
    }
    /**
     * 查找-- 
     * 不管是预算书还是措施项目都是这一个接口
     * 相同的数据
     * @param args
     * @returns {Promise<void>}
     */
    async search(args) {
        let {constructId, unitId, deRowId, category, logicalOperator, conditions, projectScales, replaceTxt} = args;

        logicalOperator = logicalOperator === '&&' ? 'AND' :'OR';
        let result = [];
        let deListByDe = [];
        let deListByRcj = [];
        let fieldConditions = {};
        let conditionsEmpty = true;
        for (let field of Object.keys(conditions)) {
            let conditionsFilter = conditions[field].filter(item => ObjectUtils.isNotEmpty(item.value));
            // 添加逻辑运算符
            conditionsFilter = ObjectUtils.insertElement(conditionsFilter, 'AND')
            if (ObjectUtils.isNotEmpty(conditionsFilter)) {
                fieldConditions[field] = conditionsFilter;
                conditionsEmpty = false;
            }
        }
        let unitIds = new Set();
        if(ObjectUtils.isEmpty(projectScales)){
            unitIds.add(unitId);
        }
        for(let project of projectScales){
            if(project.type === ProjectTypeConstants.PROJECT_TYPE_UNIT){
                unitIds.add(project.sequenceNbr);
            }
            if(project.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE){
                let singleProject = ProjectDomain.getDomain(constructId).getProjectById(project.sequenceNbr);
                let unitList = [];
                ProjectDomain.getDomain(constructId).findUnit(singleProject, unitList, []);
                if (ObjectUtils.isNotEmpty(unitList)) {
                    for (let unit of unitList) {
                        unitIds.add(unit.sequenceNbr);
                    }
                }
            }
            if(project.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT){
                let firstProject = ProjectDomain.getDomain(constructId).getProjectById(project.sequenceNbr);
                let unitList = [];
                ProjectDomain.getDomain(constructId).findUnit(firstProject, unitList, []);
                if (ObjectUtils.isNotEmpty(unitList)) {
                    for (let unit of unitList) {
                        unitIds.add(unit.sequenceNbr);
                    }
                }
            }
        }
        let deData = [];
        for(let unitId of unitIds){
            //预算书
            let unitDeData = await this.getDeAllDepth(constructId, unitId, deRowId);
            if (ObjectUtils.isNotEmpty(unitDeData)) {
                deData = deData.concat(unitDeData);
            }
            //措施项目
            let unitCsxmDeData = await this.service.gongLiaoJiProject.gljStepItemCostService.getDeAllDepth(constructId, unitId, deRowId);
            if (ObjectUtils.isNotEmpty(unitCsxmDeData)) {
                // 处理是否措施定额，用于跳转页签
                unitCsxmDeData.forEach(item=>item.isCsxmDe = true);
                deData = deData.concat(unitCsxmDeData);
            }
        }
        // let deList = ConvertUtil.deepCopy(deData);
        // deList.shift();
        let deList = deData.filter(item =>  ![DeTypeConstants.SUB_DE_TYPE_DE,DeTypeConstants.DE_TYPE_EMPTY, DeTypeConstants.DE_TYPE_DEFAULT].includes(item.type));
        // 分部&&子目
        let types = []
        if (category.includes("分部")) {
            let typeConditions = [
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_FB
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_ZFB
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_DELIST
                }
            ]
            types = types.concat(typeConditions)
        }
        if (category.includes("子目")) {
            let typeConditions = [
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_DE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.SUB_DE_TYPE_DE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_RESOURCE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_USER_DE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_USER_RESOURCE
                }
            ]
            types = types.concat(typeConditions)
        }
        types = ObjectUtils.insertElement(types, 'OR')

        // 执行搜索
        if(conditionsEmpty){
            deListByDe = deList;
        }else{
            deListByDe = ObjectUtils.filterData(deList, fieldConditions, logicalOperator);
        }
        deListByDe = ObjectUtils.filterData(deListByDe, {type: types}, 'AND');

        result = deListByDe;
        let deLineId = result.map(item => item.sequenceNbr)
        // result = result.filter(item => !(item.type === DeTypeConstants.SUB_DE_TYPE_DE && !deLineId.includes(item.parentId)))
        // 添加没有显示的伪定额
        for (let item1 of deList) {
            if (item1.type === DeTypeConstants.SUB_DE_TYPE_DE
                && !deLineId.includes(item1.sequenceNbr)
                && deLineId.includes(item1.parentId)
            ) {
                item1.isFilter = true
                result.push(item1)
            }
        }

        // 人材机
        // if (category.includes("人材机")) {
        //     let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        //     let rcjAllList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        //     let rcjAllListCopy = ConvertUtil.deepCopy(rcjAllList);
        //     let rcjList = new Array();
        //     rcjAllListCopy.forEach(item => {
        //         if (ObjectUtils.isNotEmpty(item.pbs)) {
        //             item.pbs.forEach(item2 => {
        //                 item2.deId = item.deId
        //                 rcjList.push(item2);
        //             })
        //         } else {
        //             rcjList.push(item)
        //         }
        //     });
        //
        //     let rcjFieldConditions = {
        //         materialName: fieldConditions.deName,
        //         materialCode: fieldConditions.deCode,
        //         unit: fieldConditions.unit,
        //         totalNumber: fieldConditions.quantity,
        //         RSum: fieldConditions.RSum,
        //         CSum: fieldConditions.CSum,
        //         JSum: fieldConditions.JSum,
        //         rTocalSum: fieldConditions.rTocalSum,
        //         cTocalSum: fieldConditions.cTocalSum,
        //         jTocalSum: fieldConditions.jTocalSum,
        //     }
        //     // 执行搜索
        //     let rcjs = ObjectUtils.filterData(rcjList, rcjFieldConditions, logicalOperator, 'AND');
        //     let deIds = Array.from(new Set(rcjs.map(item => item.deId)))
        //     deListByRcj = deList.filter(item => deIds.includes(item.sequenceNbr))
        // }

        // if (logicalOperator === 'AND') {
        //     // 计算交集
        //     result = ObjectUtils.getIntersection(deListByDe, deListByRcj, 'sequenceNbr')
        // } else if (logicalOperator === 'OR'){
        //     // 计算并集
        //     result = ObjectUtils.getUnion(deListByDe, deListByRcj, 'sequenceNbr')
        // }
        if(ObjectUtils.isNotEmpty(replaceTxt)){
            return this.replaceDeTxt(constructId, deRowId,result,conditions,replaceTxt);
        }
        return result;
    }

    /**
     * 过滤
     * @param args
     * @returns {Promise<void>}
     */
    async filterDe(args) {
        let {constructId, unitId, deRowId, conditions} = args;
        // 转换为新的结构
        let newConditions = {
            color: conditions.color.map(value => ({ operator: '===', value })),
        };
        let deData = await this.service.gongLiaoJiProject.gljDeService.getDeAllDepth(constructId, unitId, deRowId);
        let deList = ConvertUtil.deepCopy(deData);
        // deList.shift();
        // 执行搜索
        newConditions.annotations = []
        let formulas = [];
        for (let annotation of conditions.annotations) {
            let annotationConditions = []
            if (annotation) {
                annotationConditions.push({operator: "!==", value: undefined})
                annotationConditions.push({operator: "!==", value: null})
                annotationConditions.push({operator: "!==", value: ''})
                annotationConditions = ObjectUtils.insertElement(annotationConditions, 'AND')
                formulas.push(ObjectUtils.buildItemFormula("annotations", annotationConditions));
            } else {
                annotationConditions.push({operator: "===", value: undefined})
                annotationConditions.push({operator: "===", value: null})
                annotationConditions.push({operator: "===", value: ''})
                annotationConditions = ObjectUtils.insertElement(annotationConditions, 'OR')
                formulas.push(ObjectUtils.buildItemFormula("annotations", annotationConditions));
            }
        }

        let colorConditions = conditions.color.map(value => ({ operator: '===', value }))
        colorConditions = ObjectUtils.insertElement(colorConditions, 'OR')
        formulas.push(ObjectUtils.buildItemFormula("color", colorConditions));
        let formula = formulas.filter(item => item !== "()").join(' || ')

        let parentList = [];
        let deDomain = ProjectDomain.getDomain(constructId).deDomain
        let des = deList.filter(item => {
            return eval(formula);
        });
        for (let de of des) {
            deDomain.findParents(de, parentList, [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
        }
        let parentIds = parentList.map(item => item.sequenceNbr)

        let result = deList.filter(item => {
            let formulaFlag = eval(formula)
            let result = formulaFlag || (parentIds.includes(item.sequenceNbr)
                    && (item.type === DeTypeConstants.DE_TYPE_DEFAULT
                        || item.type === DeTypeConstants.DE_TYPE_FB
                        || item.type === DeTypeConstants.DE_TYPE_ZFB
                        || item.type === DeTypeConstants.DE_TYPE_DELIST
                    )
                )
            let show = formulaFlag || (parentIds.includes(item.sequenceNbr)
                    && (item.type === DeTypeConstants.DE_TYPE_DEFAULT
                        || item.type === DeTypeConstants.DE_TYPE_FB
                        || item.type === DeTypeConstants.DE_TYPE_ZFB
                    )
                )
            if (show) {
                item.isFilter = true
            }
            return result;
        })
        let deLine = deList.find(item => item.sequenceNbr === deRowId)
        let resultUnitLine = result.find(item => item.sequenceNbr === deRowId)
        if (ObjectUtils.isNotEmpty(deLine) && ObjectUtils.isEmpty(resultUnitLine)) {
            deLine.isFilter = true;
            result.unshift(deLine)
        }
        // 删掉没有父级的伪定额
        let deLineId = result.map(item => item.sequenceNbr)
        result = result.filter(item => !(item.type === DeTypeConstants.SUB_DE_TYPE_DE && !deLineId.includes(item.parentId)))
        // 添加没有显示的伪定额
        for (let item1 of deList) {
            if (item1.type === DeTypeConstants.SUB_DE_TYPE_DE
                && !deLineId.includes(item1.sequenceNbr)
                && deLineId.includes(item1.parentId)
            ) {
                item1.isFilter = true
                result.push(item1)
            }
        }
        return result;
    }

    /**
     * 根据定额id 向上展开父级
     * @param args
     * @returns {Promise<void>}
     */
    async openParentDe(args) {
        let {constructId, singleId, unitId, deId} = args;
        let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deId);
        let parentList = [];
        let deDomain = ProjectDomain.getDomain(constructId).deDomain
        deDomain.findParents(de, parentList, [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
        for (let parent of parentList) {
            parent.displaySign = BranchProjectDisplayConstant.open;
        }
        ProjectDomain.getDomain(constructId).deDomain.updateDe(de);
        return ResponseData.success(true);
    }

    /**
     * 更新定额工程量
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateQuantity(args) {
        let {constructId, unitId, deId, quantity, quantityExpression} = args;
        if(ObjectUtil.isNotEmpty(quantity) && !ObjectUtil.isNumber(quantity)){
            quantity = String(quantity).toUpperCase();
        }
        let resetDeQuantities = args.resetDeQuantities === false ? false : true;
        if(ObjectUtil.isNotEmpty(quantity) && !ObjectUtil.isNumber(quantity)){
            if (String(quantity).toUpperCase().includes("GCLMXHJ")){
                resetDeQuantities = false;
            }
        }
        let projectDomain = ProjectDomain.getDomain(constructId);
        let deDomain = projectDomain.deDomain;
        let deRow = deDomain.getDeById(deId);
        if(ObjectUtil.isEmpty(deRow)){
            return ResponseData.fail("定额不存在");
        }
        let priceCodes = await deDomain.getQuantityExpressionCodes(constructId,unitId,projectDomain.functionDataMap);
        if(ObjectUtil.isEmpty(priceCodes)){
            priceCodes = [];
        }
        //增加定额费用代码
        await this.service.gongLiaoJiProject.gljDeService.addPriceCodes(constructId, unitId, deId, priceCodes);

        // 精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);

        //判断是否满足定额
        let reduceCodes = DeUtils.reduceCostDe(deRow,priceCodes);
        //工程量明细匹配不做任何处理
        let addFunctionAvalible = false;
        
        let tokens = DeQualityUtils.evalQualityTokens(quantity);
        let tokenUnexist = false;
        let errorCodes = [];
        if(ObjectUtil.isNotEmpty(tokens) && ObjectUtil.isEmpty(reduceCodes)){
            tokenUnexist = true;
            errorCodes = tokens;
        }else if(ObjectUtil.isNotEmpty(tokens) && ObjectUtil.isNotEmpty(reduceCodes)){
            for(let token of tokens){
                let priceCode = reduceCodes.find(item=>item.code == token);
                if(ObjectUtil.isEmpty(priceCode)){
                    tokenUnexist = true;
                    errorCodes.push(token);
                }
                addFunctionAvalible = true;
            }
        }
        if(tokenUnexist){
            return ResponseData.fail('工程量表达式错误：费用代码【'+errorCodes.join(',')+'】不存在或循环引用');
        }

        let deGclMap = deDomain.functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
        if(ObjectUtil.isEmpty(deGclMap)){
            deGclMap = new Map();
            deDomain.functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY,deGclMap);
        }
        let newVar = await deDomain.updateQuantity(constructId, unitId, deId, quantity,true,true, resetDeQuantities,priceCodes, quantityExpression);
        // 修改关联子目工程量
        let relationDeList = await this.existRelationDe({constructId, deRowId: deId});
        if (ObjectUtil.isNotEmpty(relationDeList)) {
            // 获取子目规则 zmVariableRuleList
            let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
            let unitQuantiesMap = quantitiesMap.get(unitId);
            let pointLine = unitQuantiesMap.get(deId);
            let zmVariableRuleList = pointLine.zmVariableRuleList
            if (ObjectUtil.isNotEmpty(zmVariableRuleList)) {
                let  unitNbr = UnitUtils.removeCharter(newVar.unit);
                if(ObjectUtil.isEmpty(unitNbr)){
                    unitNbr = 1;
                }
                let originalQuantity = NumberUtil.numberScale(newVar.quantity * unitNbr, precision.EDIT.DE.quantity);
                zmVariableRuleList.find(item => item.variableCode === 'GCL' || item.variableCode === 'HSGCL').resultValue = originalQuantity;
                let zmVariableRule = zmVariableRuleList.find(item => item.variableCode === 'HSGCL')
                for (let relationDe of relationDeList) {
                    //更新定额费用代码
                    let zmPriceCodes = [
                        {code: 'GCL',price: originalQuantity}
                    ]
                    if (zmVariableRule.variableCode === 'HSGCL') {
                        zmPriceCodes = [
                            {code: 'HSGCL',price: originalQuantity}
                        ]
                    }
                    await this.service.gongLiaoJiProject.gljDeService.setDeCostCode(constructId, unitId, relationDe.sequenceNbr, zmPriceCodes);
                    // 计算子目工程量
                    let result = await this.service.gongLiaoJiProject.gljBaseDeRelationService.zmCalculateQuantity(constructId, zmVariableRuleList, relationDe);
                    let relationDeQuantityExpression = relationDe.quantityExpression
                    await deDomain.updateQuantity(constructId, unitId, relationDe.sequenceNbr, result.quantity,true,true, true, priceCodes, relationDeQuantityExpression);
                    let relationDe2 = ProjectDomain.getDomain(constructId).deDomain.getDe(item => item.unitId === unitId && item.sequenceNbr === relationDe.sequenceNbr);
                    if (ObjectUtil.isNotEmpty(relationDe2)){
                        relationDe2.quantityExpression = relationDeQuantityExpression
                    }
                }
            }
        }

        if(addFunctionAvalible){
            let deRowBak = {constructId, unitId, deRowId:deId};
            let deSet = deGclMap.get(unitId);
            if(ObjectUtil.isEmpty(deSet)){
                deSet = [];
                deGclMap.set(unitId,deSet);
            }
            let deExist = deSet.find(item=>item.unitId === unitId && item.deRowId === deId);
            if(ObjectUtil.isNotEmpty(deExist)){
                let index = deSet.indexOf(deExist);
                deSet.splice(index,1);
            }
            deSet.push(deRowBak);
        }
        return ResponseData.success(newVar);
    }

    /**
     * 增加定额费用代码
     * @param constructId
     * @param unitId
     * @param deId
     * @param priceCodes
     * @returns {Promise<void>}
     */
    async addPriceCodes(constructId, unitId, deId, priceCodes) {
        if(ObjectUtil.isEmpty(priceCodes)){
            priceCodes = [];
        }
        let deCostCode = await this.service.gongLiaoJiProject.gljDeService.getDeCostCode(constructId, unitId, deId);
        priceCodes.push(...ObjectUtil.cloneDeep(deCostCode?.priceCodes))
    }


    /**
     * 初始化定额费用代码
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async getDeCostCode(constructId, unitId, deId) {
        let deCostCode = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.DE_COST_CODE);
        if (ObjectUtil.isEmpty(deCostCode) || ObjectUtil.isEmpty(deCostCode[unitId]) || ObjectUtil.isEmpty(deCostCode[unitId][deId])) {
            let deLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
            await this.service.gongLiaoJiProject.gljInitDeService.initDeCostCode(deLine);
            deCostCode = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.DE_COST_CODE);
        }
        return deCostCode[unitId][deId]
    }


    /**
     * 初始化定额费用代码
     * @param constructId
     * @param unitId
     * @param deId
     * @param priceCodes
     * @returns {Promise<void>}
     */
    async setDeCostCode(constructId, unitId, deId, priceCodes) {
        let deCostCode = await this.getDeCostCode(constructId, unitId, deId);
        priceCodes = ObjectUtil.isEmpty(priceCodes) ? [] : priceCodes;
        for (let item of priceCodes) {
            let priceCode = deCostCode.priceCodes.find(item2 => item2.code === item.code);
            if (ObjectUtil.isEmpty(priceCode)) {
                priceCode = {
                    code: item.code,
                    price: item.price
                }
                deCostCode?.priceCodes?.push(priceCode)
            } else {
                priceCode.price = item.price;
            }
        }
    }

    /**
     *   获取定额主材设备
     * @param args
     * @returns {Promise<void>}
     */
    async getMainMaterialAndEquipment (args){
        let baseDe = await this.service.gongLiaoJiProject.gljBaseDeService.getDeAndRcj(args.deStandardId);
        let  deRcjs= [];
        if(ObjectUtils.isNotEmpty(baseDe?.rcjList)){
            for (let i = 0; i < baseDe.rcjList.length; i++) {
                let  base=baseDe.rcjList[i];
                let  relaction=baseDe.deRcjRelationList[i];
                if(relaction.kind  ===4  || relaction.kind === 5){
                    let rcj = {
                        "materialCode": relaction.materialCode,
                        "materialName": relaction.materialName,
                        "specification": base.specification,
                        "resQty": relaction.resQty,
                        "marketPrice": base.price,
                        "marketTaxPrice": base.marketTaxPrice,
                        "baseJournalPrice": base.baseJournalPrice,
                        "baseJournalTaxPrice": base.baseJournalTaxPrice,
                        "unit": base.unit,
                        "constructId" : args.constructId,
                        "unitId" : args.unitId
                    };
                    this.service.gongLiaoJiProject.gljRcjService.processingMarketPrice(rcj);
                    deRcjs.push(rcj);
                }
            }
        }
        return deRcjs;
    }

    /**
     *  修改主材设备
     * @param args
     * @returns {Promise<void>}
     */
    async updateMainMaterialAndEquipment (args){
        let {constructId, unitId, deRowId, rcjs } = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let deMainMaterial = businessMap.get(FunctionTypeConstants.UNIT_DE_MAINMATERIAL);
        if(ObjectUtils.isEmpty(deMainMaterial)){
            deMainMaterial = new Map();
            businessMap.set(FunctionTypeConstants.UNIT_DE_MAINMATERIAL, deMainMaterial);
        }
        deMainMaterial.set( deRowId , rcjs)
    }
    /**
     * 定额是否关联子目
     * @param args
     * @returns {Promise<void>}
     */
    async existRelationDe(args) {
        let {constructId, deRowId} = args
        let relationDeList = await ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.fDeId === deRowId && item.isRelationDe === true && String(item.quantityExpression)?.includes('GCL') && !String(item.quantityExpression).includes('GCLMXHJ'));
        return relationDeList;
    }
    /**
     * 补充定额编码 规则处理
     * @param args
     * @returns {Promise<void>}
     */
    async getDefaultCode(constructId, unitId, prefix) {
        let regex = new RegExp(`${prefix}\\d{3}`);
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        let businessMap = deDomain.functionDataMap;
        let cacheUserDeList = businessMap.get(FunctionTypeConstants.PROJECT_USER_DE);
        if(ObjectUtil.isEmpty(cacheUserDeList)){
            cacheUserDeList = [];
        }
        let curUserDeList = deDomain.ctx.allDeMap.getAllNodes().filter(item=>item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_USER_DE);
        let deUserList = [...cacheUserDeList,...curUserDeList];
        // 放入用戶rcj
        if (ObjectUtils.isEmpty(deUserList)) {
            return String(1).padStart(3, '0');
        } else {
            let result = deUserList.filter(item => {
                return regex.test(item.deCode) && item.unitId === unitId;
            });
            let num = String(result.length + 1).padStart(3, '0');
            return this._handleAlike(result, num, result.length + 1, prefix);
        }
    }

    _handleAlike(resultList, code, length, type) {
        let relust = resultList.find(item => item.deCode === (type + code));
        if (ObjectUtils.isNotEmpty(relust)) {
            return this._handleAlike(resultList, String(length + 1).padStart(3, '0'), length + 1, type);
        }
        return String(code).padStart(3, '0');
    }

    /**
     * 分部数据能否升降级
     * @param constructId
     * @param unitId
     * @param deLists
     * @returns {Promise<void>}
     */
    async fbUpAndDown(constructId, unitId, deLists) {
        let allData = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
        for (let de of allData) {
            let deRow = deLists.find(item => item.sequenceNbr === de.sequenceNbr);
            let item = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(de.sequenceNbr);
            if (item.type !== DeTypeConstants.DE_TYPE_FB && item.type !== DeTypeConstants.DE_TYPE_ZFB) {
                continue;
            }
            if (ObjectUtil.isEmpty(deRow)) {
                continue;
            }
            // 升级判断
            deRow.isUp = true;
            if (item.parent.type === DeTypeConstants.DE_TYPE_DEFAULT) {
                deRow.isUp = false;
            }

            // 降级判断
            deRow.isDown = false;
            // 四级分部不可以降级
            let level = this.getDeLevel(item, 0);
            let childLevel = this.getDeChildLevel(item);
            if (level + childLevel - 1 < 4) {
                let index = item.parent.children.findIndex(item1 => item1.sequenceNbr === item.sequenceNbr);
                if (index !== 0) {
                    let parent = item.parent.children[index-1];
                    // 上面分部下没有定额，可以降级
                    let de1 = parent.children.find(item => item.type !== DeTypeConstants.DE_TYPE_ZFB && item.type !== DeTypeConstants.DE_TYPE_FB);
                    if (ObjectUtil.isEmpty(de1)) {
                        deRow.isDown = true;
                    }
                }
            }
        }

    }

    // 获取当前层级数
    getDeLevel(node, level=0) {
        if (node.parent) {
            level++;
            level = this.getDeLevel(node.parent, level);
        }
        return level;
    }

    /**
     * 获取子级分部层数
     * @param node
     * @returns {number}
     */
    getDeChildLevel(node) {
        if (!node) return 0; // 如果节点为空，返回0
        if (!node.children || node.children.length === 0) return 1; // 如果没有子节点，返回1
        let maxDepth = 0; // 初始化最大深度
        for (let child of node.children) {
            if (child.type !== DeTypeConstants.DE_TYPE_FB && child.type !== DeTypeConstants.DE_TYPE_ZFB) {
                continue;
            }
            maxDepth = Math.max(maxDepth, this.getDeChildLevel(child)); // 递归调用获取子节点的最大深度
        }
        return maxDepth + 1; // 返回当前节点的最大深度
    }

    async fbDataUpAndDown(operateAction,selectId,unit,type,constructId, singleId, unitId) {
        let nodeById = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(selectId);
        if (operateAction === 'up' && nodeById.type === DeTypeConstants.DE_TYPE_ZFB) {
            nodeById.parent.children =  nodeById.parent.children.filter(item => item.sequenceNbr !== nodeById.sequenceNbr);
            if (ObjectUtil.isEmpty(nodeById.parent.children)) {
                nodeById.parent.displaySign = BranchProjectDisplayConstant.noSign;
            }

            let parent = nodeById.parent.parent;
            let oldParentId = nodeById.parentId
            nodeById.parent.children.forEach((item, index) => {
                item.index = index;
            });
            nodeById.parentId = parent.sequenceNbr
            nodeById.parent = parent
            if (parent.type === DeTypeConstants.DE_TYPE_DEFAULT) {
                nodeById.type = DeTypeConstants.DE_TYPE_FB
            }
            // 升级，保留分部顺序
            let index = nodeById.parent.children.findIndex(obj => obj.sequenceNbr === oldParentId);
            nodeById.parent.children.splice(index + 1, 0, nodeById);
            nodeById.parent.children.forEach((item, index) => {
                item.index = index;
            });
        }
        if (operateAction === 'down') {
            let index = nodeById.parent.children.findIndex(item => item.sequenceNbr === nodeById.sequenceNbr);
            if (index === 0) {
                return
            }
            let parent = nodeById.parent.children[index-1];
            // 上面分部下有定额，则不能降级
            let de1 = parent.children.find(item => item.type !== DeTypeConstants.DE_TYPE_ZFB && item.type !== DeTypeConstants.DE_TYPE_FB);
            if (ObjectUtil.isNotEmpty(de1)) {
                return;
            }
            nodeById.parent.children = nodeById.parent.children.filter(item => item.sequenceNbr !== nodeById.sequenceNbr);
            nodeById.parent.children.forEach((item, index) => {
                item.index = index;
            });
            nodeById.parentId = parent.sequenceNbr
            nodeById.parent = parent
            nodeById.type = DeTypeConstants.DE_TYPE_ZFB
            nodeById.parent.children.push(nodeById);
            nodeById.parent.displaySign = BranchProjectDisplayConstant.open;
            nodeById.parent.children.forEach((item, index) => {
                item.index = index;
            });

        }

    }

    // async fbDataUpAndDown(operateAction,selectId,unit,type,constructId, singleId, unitId) {
    //     //所有分部数据
    //     let allData = ProjectDomain.getDomain(unitId).deDomain.getDeTree(item => item.unitId === unitId);
    //     let nodeById = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(selectId);
    //     let fbDataByFbId = allData.findAllSubsets(nodeById);
    //     //获取所有数据
    //     let itemBillProjects = allData;
    //     //获取当前选中数据
    //     let selectElement = itemBillProjects.find(fb=>fb.sequenceNbr==selectId);
    //     //获取当前分部下的所有数据
    //     // let fbDataByFbId = this.getFbDataByFbId(1, 300000, selectId, itemBillProjects, this.disPlayType, true);
    //
    //     //过滤掉所有数据
    //     let newArr = itemBillProjects.filter(item => !fbDataByFbId.includes(item));
    //     //获取分部操作后的parentId
    //     let parentId;
    //     let parent;
    //     if(operateAction=="up"){
    //         parent = itemBillProjects.find(fb=>fb.sequenceNbr==selectElement.parentId);
    //         parentId= parent.parentId;
    //         if(parent.type==DeTypeConstants.DE_TYPE_FB){
    //             fbDataByFbId[0].type=parent.type;
    //         }
    //
    //     }else {
    //         // let findex = -1;
    //         // for (let i = 0; i < itemBillProjects.length; i++) {
    //         //     findex++;
    //         //     if (itemBillProjects[i].sequenceNbr == selectElement.sequenceNbr) {
    //         //         break;
    //         //     }
    //         // }
    //         //降级是降至上一个同级别的最后一个下级最后
    //         parent=itemBillProjects.filter(fb=>fb.sequenceNbr==selectElement.parentId)[0].children.find(f=>f.index==selectElement.index-1)
    //         parentId=parent.sequenceNbr;
    //         fbDataByFbId[0].type=DeTypeConstants.DE_TYPE_ZFB;
    //     }
    //     //获取插入数据位置
    //     let addIndex=this.getSjJjTargetIndex(itemBillProjects,selectElement,operateAction,newArr);
    //     //修改新数据的parentId
    //     fbDataByFbId[0].parentId=parentId;
    //     // 使用 splice() 方法在指定位置插入数据
    //     newArr.splice(addIndex+1, 0, ...fbDataByFbId);
    //     //填充数据
    //     // unit.itemBillProjects=newArr;
    //     let find = newArr.find(item=>item.sequenceNbr==parentId);
    //     find.displaySign=BranchProjectDisplayConstant.open;
    //     //重新设置下挂清单定额数据的dispNo
    //     this.againDispNoV1(newArr);
    //     //对数据进行排序处理
    //     newArr.sort((a, b) => a.index - b.index);
    //     //转为树
    //     // unit.itemBillProjects=tree.arrayToTree(newArr);
    //     //状态数据处理
    //     this.setItemDtaStatus(newArr);
    //     //计算单价构成数据
    //     //获取所有的分部数据
    //     let fbList=itemBillProjects.filter(fb=>fb.type==DeTypeConstants.DE_TYPE_FB || fb.type==DeTypeConstants.DE_TYPE_ZFB);
    //     // for(const fb of fbList){
    //     //     let unitApplicationContext = new UnitApplicationContext({constructId, singleId, unitId, pageType: "fbfx"});
    //     //     unitApplicationContext.item=fb;
    //     //     await unitApplicationContext.after();
    //     // }
    //
    //
    // }

    /**
     * 获取升级降级的目标索引位置
     * @param itemBillProjects
     * @param selectElement
     * @param operateAction
     * @returns {number}
     */
    getSjJjTargetIndex(itemBillProjects,selectElement,operateAction,newArr){

        let fbElement;
        //获取指定指定分布再原数据中的索引
        if (operateAction == "up") {
            fbElement =itemBillProjects.find(fb=>fb.sequenceNbr==selectElement.parentId);
            let data = this.getFbDataByFbId(1, 300000, fbElement.sequenceNbr, newArr, this.disPlayType, true);
            fbElement=data[data.length-1];
        }else {
            //如果是分部 获取要换的分部 下标
            let fb = itemBillProjects.filter(fb=>fb.parentId==selectElement.parentId);
            // let fbIndex=-1;
            // for (let i = 0; i < fb.length; i++) {
            //     fbIndex=fbIndex+1;
            //     if (fb[i].sequenceNbr == selectElement.sequenceNbr) {
            //         break;
            //     }
            // }
            //获取下一个分部的所有数据
            fbElement = fb.find(i=>i.index==selectElement.index-1);
            let data = this.getFbDataByFbId(1, 300000, fbElement.sequenceNbr, newArr, this.disPlayType, true);
            fbElement=data[data.length-1];

        }
        let findex = -1;
        for (let i = 0; i < newArr.length; i++) {
            findex++;
            if (newArr[i].sequenceNbr == fbElement.sequenceNbr) {
                break;
            }
        }
        return findex;
    }

    againDispNoV1(itemBillProjectDTOS) {
        try {
            // 给 dispNo 重新排序
            // let itemBillProjectDTOS = unit.itemBillProjects;
            let parentId = null;
            //获取单位工程行数据
            const collect = itemBillProjectDTOS.filter(item => {
                return item.type ==DeTypeConstants.DE_TYPE_DEFAULT;
            });
            if (collect.length > 0) {
                parentId = collect[0].sequenceNbr;
                this.setDispNoV1(parentId, 1, 1, itemBillProjectDTOS);
            }

        } catch (err) {
            throw new Error('An error occurred during reordering dispNo:', err);
        }
    }

    setDispNoV1(sequenceNbr, qdDispNo, deDispNo, itemBillProjectDTOList,) {
        // 查询当前单位工程数据
        const itemBillProjectEntities = itemBillProjectDTOList.filter(item => item.parentId === sequenceNbr);

        if (itemBillProjectEntities.length > 0) {
            deDispNo = 1;
            for (const item of itemBillProjectEntities) {
                // 清单
                if (item.type ===DeTypeConstants.DE_TYPE_DELIST) {
                    // 设置
                    item.dispNo = String(qdDispNo);
                    qdDispNo++;
                }
                // 定额
                if (item.type === DeTypeConstants.DE_TYPE_DE) {
                    item.dispNo = (qdDispNo - 1) + '.' + deDispNo;
                    deDispNo += 1;
                }
                qdDispNo = this.setDispNoV1(item.sequenceNbr, qdDispNo, deDispNo, itemBillProjectDTOList);
            }
        }
        return qdDispNo;
    }

    setItemDtaStatus(mockAllData) {

        let returnArray = mockAllData;

        let map = new Map();
        //是否可以上下移动判断
        for (let i = 0; i < returnArray.length; i++) {
            let element = returnArray[i];

            let filter = [];
            if (map.get(element.parentId) != null) {
                filter = map.get(element.parentId);
            } else {
                filter = returnArray.filter(item => item.parentId == element.parentId);
                map.set(element.parentId, filter);
            }
            if (filter.indexOf(element) == 0) {
                element['isFirst'] = true;
            } else {
                element['isFirst'] = false;
            }

            if (filter.indexOf(element) == filter.length - 1) {
                element['isLast'] = true;
            } else {
                element['isLast'] = false;
            }
            //分部是否可以上调下调判断
            //给所有的数据设置不可以升降级
            element.isUpFb = true;
            element.isDownFb = true;
            //如果是分部 不可升级
            if (element.type == DeTypeConstants.DE_TYPE_FB) {
                element.isUpFb = false;
                //获取同层级数据 如果没有同层级其他数据将不可以进行降级操作
                //如果同层级有数据  但是当前分部是第一行数据 不可降级操作
                let ts = returnArray.filter(fb => fb.parentId == element.parentId);
                if (ts.length > 1) {
                    if (element.index==0) {
                        element.isDownFb = false;
                    } else {
                        let b = this.isOnlyQd(element.parent,returnArray);
                        element.isDownFb = b?false:true;
                    }
                } else {
                    element.isDownFb = false;
                }
            }

            //判断若下移会导致分部层级超过四层则【降级】按钮置灰  暂未处理TODO SUNPO
            if (element.type == DeTypeConstants.DE_TYPE_ZFB) {
                let ts = returnArray.filter(fb => fb.parentId == element.parentId);
                if (ts.length > 1) {
                    if (element.index==0) {
                        element.isDownFb = false;
                    } else {
                        let b = this.isOnlyQd(element.parent,returnArray);
                        element.isDownFb = b?false:true;
                    }
                } else {
                    element.isDownFb = false;
                }
                //如果选中分部的层级结构是第四级 或者分部中包含第四级分部数据不可以降级
                let fourLeve = this.isFourLeve(element, returnArray);
                if (fourLeve) {
                    element.isDownFb = false;
                }

            }
        }

    }

    isOnlyQd(node,allData){
        //获取node下的所有数据
        let children = allData.filter(fb => fb.parentId == node.sequenceNbr);
        if(ObjectUtils.isNotEmpty(children)){
            if(children[0].type==DeTypeConstants.DE_TYPE_DELIST){
                return  true;
            }
        }
        return  false;
    }

    /**
     * 判断分部是不是第四级  或者是否包含四级
     * @param selectElement
     * @param allData
     */
    isFourLeve(selectElement,allData){

        //是不是第四级
        let levelNum = this.getLevelNum(1,selectElement,allData);
        if(levelNum==4){
            return true;
        }else {
            //判断内部数据是否包含第四层
            let filter = allData.filter(fb => fb.parentId === selectElement.sequenceNbr && fb.type==DeTypeConstants.DE_TYPE_ZFB);
            if(levelNum==2){
               if(ObjectUtils.isNotEmpty(filter)){
                   for(const zfb of filter){
                       let zfbList = allData.filter(fb => fb.parentId === zfb.sequenceNbr && fb.type==DeTypeConstants.DE_TYPE_ZFB);
                       if(ObjectUtils.isNotEmpty(zfbList)){
                           return  true;
                       }
                   }
               }
            }
            if(levelNum==3){
                if(ObjectUtils.isNotEmpty(filter)){
                    return  true;
                }
            }
        }

        return  false;


    }

    /**
     * 获取层级数
     * @param selectElement
     * @param allData
     */
    getLevelNum(levelNum,selectElement,allData){
        // 检查终止条件：当前元素的kind等于"01"
        if (selectElement.type === DeTypeConstants.DE_TYPE_FB) {
            return levelNum;
        }
        // 查找父级元素
        const parentId = selectElement.parentId;
        const parentFilter = allData.find(fb => fb.sequenceNbr === parentId);
        // 如果找不到父级元素，说明已经到达最顶层，返回当前层级
        if (!parentFilter) {
            return levelNum;
        }

        // 继续向上递归查找
        return this.getLevelNum(levelNum + 1, parentFilter, allData);
    }

    async replaceDe(constructId, unitId, deStandardId, deRowId) {
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        //替换查询原来定额的章节
        let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRowId);
        let deRow = await deDomain.appendBaseDe(constructId, unitId, deStandardId, deRowId,true);
        await deDomain.extendQuantity(constructId, unitId, deRow.sequenceNbr);
        return deRow;
    }

    /**
     * 移动定额到分部
     * @param deModel
     * @param prevDeRowId
     * @returns {Promise<void>}
     */
    async moveDeToFb(deModel, prevDeRowId, deRow) {
        let de = ProjectDomain.getDomain(deModel.constructId).ctx.deMap.getNodeById(prevDeRowId);
        if (deModel.type !== DeTypeConstants.DE_TYPE_FB && deModel.type !== DeTypeConstants.DE_TYPE_ZFB) {
            return
        }
        if (ObjectUtils.isNotEmpty(deRow) && de.type !== DeTypeConstants.DE_TYPE_DEFAULT) {
            let index = de.children.findIndex(item => item.sequenceNbr === deRow.sequenceNbr);
            deModel.children = de.children.slice(index);
            deModel.displaySign = BranchProjectDisplayConstant.open;
            de.children = de.children.slice(0, index);

            for (let deChild of deModel.children) {
                deChild.parentId = deModel.sequenceNbr;
                deChild.parent = deModel;
            }
            de.children.forEach((item, index) => {
                item.index = index;
            });
            deModel.children.forEach((item, index) => {
                item.index = index;
            });
            await ProjectDomain.getDomain(deModel.constructId).deDomain.notify(deModel,false);
        }else {
            let line = de.children.find(item => item.type !== DeTypeConstants.DE_TYPE_FB && item.type !== DeTypeConstants.DE_TYPE_ZFB)
            if (ObjectUtils.isNotEmpty(line)
                && (deModel.type === DeTypeConstants.DE_TYPE_FB || deModel.type === DeTypeConstants.DE_TYPE_ZFB)
                && deModel.parentId === prevDeRowId //添加子分部
            ) {
                deModel.children = de.children.filter(item => item.sequenceNbr !== deModel.sequenceNbr);
                deModel.displaySign = BranchProjectDisplayConstant.open;
                de.children = de.children.filter(item => item.sequenceNbr === deModel.sequenceNbr);
                for (let deChild of deModel.children) {
                    deChild.parentId = deModel.sequenceNbr;
                    deChild.parent = deModel;
                }
                de.children.forEach((item, index) => {
                    item.index = index;
                });
                deModel.children.forEach((item, index) => {
                    item.index = index;
                });
                await ProjectDomain.getDomain(deModel.constructId).deDomain.notify(deModel,false);
            }
        }
    }

    moveKeyBefore(map, keyToMove, referenceKey) {
        const newMap = new Map();
        const keys = Array.from(map.keys());
        for (const key of keys) {
            // 如果当前键是参考键，则先添加要移动的键
            if (key === referenceKey) {
                newMap.set(keyToMove, map.get(keyToMove));
            }
            // 添加当前键
            newMap.set(key, map.get(key));
        }
        return newMap;
    }

    /**
     * 删除分部
     * @param constructId
     * @param deRowId
     */
    async delFb(constructId, deRowId) {
        let deRow = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRowId);

        // 父级下存在分部子分部则不允许删除
        let paretChilds = deRow.parent.children;
        let curIndex = 0;
        for(let i=0;i<paretChilds.length;i++){
            if(paretChilds[i].sequenceNbr === deRowId){
                curIndex = i;
            }
        }
        if (paretChilds.length>1 && deRow.children.length > 0) {
            // return "删除分部会导致子目与分部同级，当前不可删除。";
            let fbLine = null;
            if(curIndex === 0){
                fbLine = paretChilds[curIndex+1];
            }else{
                fbLine = paretChilds[curIndex-1];
            }
            for (let child of deRow.children) {
                child.parentId = fbLine.sequenceNbr;
                child.parent = fbLine;
                if(ObjectUtils.isEmpty(fbLine.children)){
                    fbLine.children=[]
                    fbLine.displaySign = BranchProjectDisplayConstant.open;
                }
                fbLine.children.push(child);
            }
        }else{

            for (let child of deRow.children) {
                child.parentId = deRow.parent.sequenceNbr;
                child.parent = deRow.parent;
            }
            deRow.parent.children = deRow.parent.children.concat(deRow.children).filter(item => item.sequenceNbr !== deRowId);
        }
        deRow.children = [];
        ProjectDomain.getDomain(constructId).ctx.deMap.removeNode(deRowId);
        ProjectDomain.getDomain(constructId).ctx.resourceMap.removeByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRowId);
    }

    getAllChildren(node) {
        let result = [];
        // 遍历当前节点的子节点
        if (node.children && node.children.length > 0) {
            for (let child of node.children) {
                result.push(child); // 添加当前子节点
                // 递归调用以获取该子节点的所有子级
                result = result.concat(this.getAllChildren(child));
            }
        }
        return result;
    }

    /**
     * 说明信息格式转换
     * @param deContentMap
     */
    transSMXX(deContentMap) {
        deContentMap = ObjectUtils.convertObjectToMap(deContentMap);
        for (let [key, unitDeContentMap] of deContentMap) {
            unitDeContentMap = ObjectUtils.convertObjectToMap(unitDeContentMap);
            for (let [key, deContentModel] of unitDeContentMap) {
                deContentModel = ObjectUtils.copyProp(deContentModel, new GljDeContentModel());
                unitDeContentMap.set(key, deContentModel);
            }
            deContentMap.set(key, unitDeContentMap);
        }
        return deContentMap;
    }

}

GljDeService.toString = () => '[class GljDeService]';
module.exports = GljDeService;
