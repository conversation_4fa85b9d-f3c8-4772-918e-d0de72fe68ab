const {NumberUtil} = require('./NumberUtil');
const {ObjectUtils} = require('./ObjectUtils');

class GsExportSettingUtil {
    
    QUANTITY_ALL;
    PRICE_ALL;
    TOTALNUMBER_ALL = 4; // 合计数量
    TOTALNUMBER_3_ALL = 3; // 合计数量
    GRHZ_PRICE_ALL = 3; // 工日合计
    QUANTITY_DEFAULT = 5; // 工程量精度统一设置（不补零）

    constructor() {
        this.QUANTITY_ALL = 3; // 工程量精度统一设置
        this.PRICE_ALL = 2; // 价格精度统一设置
    }

    /**
     * 不足补零
     * @param key
     * @param formualMap
     * @returns {string}
     * @private
     */
    formatWith0(price, scale) {
        if(ObjectUtils.isEmpty(price)) {
            price = 0;
        }
        let number = NumberUtil.numberFormat(price, scale);
        return number.toFixed(scale);
    }
}
// let gsExportSettingUtil = new GsExportSettingUtil();
// console.log(gsExportSettingUtil.formatWith0('0',gsExportSettingUtil.PRICE_ALL)); // 输出: "0.000"
// console.log(gsExportSettingUtil.formatWith0(0,gsExportSettingUtil.PRICE_ALL)); // 输出: "0.000"
// console.log(gsExportSettingUtil.formatWith0(1234.5297,gsExportSettingUtil.PRICE_ALL)); // 输出: "1,234.530"
// console.log(gsExportSettingUtil.formatWith0(1234.5237,gsExportSettingUtil.PRICE_ALL)); // 输出: "1,234.524"
module.exports = {
    GsExportSettingUtil: new GsExportSettingUtil()
};