const {Controller} = require("../../../core");

const {ResponseData} = require("../utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const WildcardMap = require("../core/container/WildcardMap");
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { ObjectUtils } = require('../utils/ObjectUtils');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const DeTypeConstants = require("../constants/DeTypeConstants");
const CommonConstants = require("../constants/CommonConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const gsRcjCollectType = require("../jsonData/gs_rcj_collect_type.json");
const BranchProjectLevelConstant = require("../constants/BranchProjectLevelConstant");
class gsRcjController extends Controller {

  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取该定额下所有人材机，不管是03定额还是04定额
   * @param {*} args 
   * @returns 
   */
  async getAllRcjDetailByDeId(args){
    let {constructId, unitId, deRowId} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetailByDeId(constructId,unitId,deRowId);
    return ResponseData.success(result);
  }

  /**
   * 获取各层级人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllRcjDetail(args) {
    let {constructId, unitId, deRowId,type} = args;
    let rcjLists = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, deRowId,type);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let  display =businessMap.get(FunctionTypeConstants.DE_PRICE_SPREAD);
    if(ObjectUtils.isEmpty(display)){
      display =false;
    }
    if(ObjectUtils.isEmpty(rcjLists)){
      return ResponseData.success([]);
    }
    let pbs = []; //定额人材机配比
    for (let rcj of rcjLists) {
      if ((rcj.isDeResource === CommonConstants.COMMON_YES || rcj.isDeCompensation === CommonConstants.COMMON_YES) && ObjectUtils.isNotEmpty(rcj.pbs)) {
        let copyPbs = ConvertUtil.deepCopy(rcj.pbs)
        Array.prototype.push.apply(pbs, copyPbs);
      }
    }
    let copyRcjList = ConvertUtil.deepCopy(rcjLists);
    Array.prototype.push.apply(copyRcjList, pbs);
    if(BranchProjectLevelConstant.top===type || BranchProjectLevelConstant.fb===type || BranchProjectLevelConstant.zfb===type){
      for (let copyRcj of copyRcjList) {
        if (ObjectUtils.isNotEmpty(copyRcj.unitTotalNumber)) {
          copyRcj.totalNumber = copyRcj.unitTotalNumber;
        }
        copyRcj.isTempRemove = 0;
      }
    }
    copyRcjList.forEach(item =>{
      item.resQtyFactor =  ObjectUtils.isEmpty(item.resQtyFactor) ? 1: item.resQtyFactor
      item.resqtyExp =  `${item.originalQty}*${item.resQtyFactor}`
      if(ObjectUtils.isNotEmpty(item.pbs)){
        item.pbs.forEach(item1=>{
          item1.resQtyFactor =  ObjectUtils.isEmpty(item1.resQtyFactor) ? 1: item1.resQtyFactor
          item1.resqtyExp =  `${item1.originalQty}*${item1.resQtyFactor}`
          if(item1.isFyrcj === 1 && item1.unit ==='%' ){
            item1.unit ='元';
          }
        })
      }
    });
    let result = copyRcjList.filter(item => {
      if(item.isDeResource !== CommonConstants.COMMON_YES && item.isDeCompensation === CommonConstants.COMMON_YES  ){
        if(display === false){
          return   null ;
        }
      }
      return  item;
    });
    return ResponseData.success(result);
  }


  /**
   * 通过索引 增加人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async addRcjData(args) {
    let {deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjId} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.addRcjData(deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjId, {});
    if (ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.code)) {
      return result
    }
    return ResponseData.success(result);
  }


  /**
   * 通过索引 替换人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async replaceRcjData(args) {
    let {deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjDetailId,rcjId,factor} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.replaceRcjData(deId, baseRcjModel, constructId, singleId, unitId,deRowId,rcjDetailId,rcjId,factor, {});
    if (ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.code)) {
      return result
    }
    return ResponseData.success(result);
  }



  /**
   *  判断人材机code 是否存在内存，
   * @param args
   * @returns {ResponseData}
   */
  codeExistInUnit(args) {
    let {constructId, singleId, unitId, code} = args;
    let rcjUserList = this.service.PreliminaryEstimate.gsRcjService.getUserRcj(constructId,unitId);
    let rcjUser=rcjUserList.find(item => code === item.materialCode && item.unitId ===unitId)
    if(ObjectUtil.isNotEmpty(rcjUser)){
      return ResponseData.success(true); // 如果没有匹配的项，返回false
    }
    return ResponseData.success(false);
  }


  /**
   *   补充 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async supplementRcjData(args) {
    let {deId, constructId, singleId, unitId,deRowId ,detail,rcjId,rcjDetailId,replaceFlag} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.supplementRcjData(deId, constructId, singleId, unitId,deRowId,detail,rcjId,rcjDetailId,replaceFlag);
    deId, constructId, singleId, unitId,deRowId,detail,rcjId,rcjDetailId,replaceFlag
    return ResponseData.success(result);
  }

  /**
   * 按定额生成主材/设备
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async supplementRcjDataByDe(args) {
    let {deId, constructId, singleId, unitId, deRowId, kind, materialCode} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.supplementRcjDataByDe(deId, constructId, singleId, unitId, deRowId, kind, materialCode);
    return ResponseData.success(result);
  }

  /**
   * 将主材/设备同步到子目
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async syncDeDataByRcj(args) {
    let {deId, constructId, singleId, unitId, deRowId, content, addType} = args;
    await this.service.PreliminaryEstimate.gsRcjService.syncDeDataByRcj(deId, constructId, singleId, unitId, deRowId, content, addType);
    return ResponseData.success(true);
  }

  /**
   *   通过编码 替换 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async replaceRcjByCodeData(args) {
    let {constructId, singleId, unitId,deRowId,code,deId, rcjDetailId ,rcjId,replaceFlag} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.replaceRcjByCodeData(deId, constructId, singleId, unitId,deRowId,code,rcjDetailId,rcjId,replaceFlag);
    return  ResponseData.success(result);
  }



  /**
   *   删除 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async deleteRcjByCodeData(args) {
    let {deId, constructId, unitId,rcjDetailId} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.deleteRcjByCodeData(deId, constructId, unitId,rcjDetailId, true, {});
    return  ResponseData.success(result);
  }

  /**
   * 批量删除 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async batchDeleteRcjByCodeData(args) {
    let {deId, constructId, unitId, rcjDetailIds} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.batchDeleteRcjByCodeData(deId, constructId, unitId, rcjDetailIds, true, {});
    return ResponseData.success(result);
  }

  /**
   * 批量临时删除 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async batchTempRemoveRcjRow(args) {
    let {constructId, deRowId, rcjDetailIds} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.batchTempRemoveRcjRow(constructId, deRowId, rcjDetailIds);
    return ResponseData.success(result);
  }

  /**
   * 批量取消临时删除 人材机明细
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async batchCancelTempRemoveRcjRow(args) {
    let {constructId, deRowId, rcjDetailIds} = args;
    let result = await this.service.PreliminaryEstimate.gsRcjService.batchCancelTempRemoveRcjRow(constructId, deRowId, rcjDetailIds);
    return ResponseData.success(result);
  }

  /**
   * 编辑 人材机明细区
   * @param args
   * @returns {ResponseData}
   */
  async updateRcjDetail(args){
    const result = await this.service.PreliminaryEstimate.gsRcjService.updateRcjDetail(args);
    return ResponseData.success(result);
  }

  /**
   * 编辑 人材机明细区   多行
   * @param args
   * @returns {ResponseData}
   */
  async multiUpdateRcjDetail(args){
    const result = await this.service.PreliminaryEstimate.gsRcjService.multiUpdateRcjDetail(args);
    return ResponseData.success(result);
  }










  /**
   * 获取单位工程下所有人材机
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllRcjByUnit(args) {
    let {constructId, unitId} = args;
    let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjKey);
    return ResponseData.success(rcjList);
  }

  /**
   * 获取定额下所有人材机
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getAllRcjByDe(args) {
    let {constructId, unitId, deId} = args;
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    return ResponseData.success(rcjList);
  }


  /**
   * 获取补充编码序列
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getDefaultCode(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.getDefaultCode(args);
    return ResponseData.success(result);
  }

  /**
   *  查询内存下拉code
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getMemoryCode(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.getMemoryCode(args);
    return ResponseData.success(result);
  }


  /**
   * 下载人材机汇总模板
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async downloadExcelRcjTotal(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.downloadExcelRcjTotal(args);
    return ResponseData.success(result);
  }

  /**
   * 单位工程 人材机汇总 导入 Excel 修改人材机汇总 市场价
   * @param args
   * @returns {Promise<void>}
   */
  async useUnitExcelRcjPrice(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.useUnitExcelRcjPrice(args);
    return ResponseData.success(result);
  }


  /**
   * 工程项目 人材机汇总 导入 Excel 修改人材机汇总 市场价
   * @param args
   * @returns {Promise<void>}
   */
  async useConstructExcelRcjPrice(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.useConstructExcelRcjPrice(args);
    return ResponseData.success(result);
  }

  /**
   * 单项工程 人材机汇总 导入 Excel 修改人材机汇总 市场价
   * @param args
   * @returns {Promise<void>}
   */
  async useSingleExcelRcjPrice(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.useSingleExcelRcjPrice(args);
    return ResponseData.success(result);
  }

  /**
   * 获取补充定额下的人材机数据
   * @param args
   * @return {Promise<ResponseData>}
   */
  async getSupplementDeByRcj(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.getSupplementDeByRcj(args);
    return ResponseData.success(result);
  }


  /**
   * 批量修改主材 查询
   */
  async zcRcjBatchSelect(args) {
    let res = await this.service.PreliminaryEstimate.gsRcjService.zcRcjBatchSelect(args);
    return ResponseData.success(res);
  }

  /**
   * 批量修改主材 修改
   */
  async zcRcjBatchUpdate(args) {
    let res = await this.service.PreliminaryEstimate.gsRcjService.zcRcjBatchUpdate(args);
    return ResponseData.success(res);
  }


}
gsRcjController.toString = () => '[class gsRcjController]';
module.exports = gsRcjController;
