const RuleHandler = require("./ruleHandler");

class Kind4<PERSON>uleHandler extends RuleHandler {
    deCodeUpdateInfo() {
        let blackSubArray = [];

        for (let handler of this.rule.mathHandlers) {
            blackSubArray.push(`[${handler.showMath || handler.oriMath}]`);
        }

        return {redStr: null, blackStr: blackSubArray.join(",")}
    }

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = this.formatConversionExplain(this.rule.relation);
    }

    deNameUpdateInfo(rule){
        let blackSubArray = [];

        for (let handler of this.rule.mathHandlers) {
            blackSubArray.push(this.formatConversionExplain(handler.showMath || handler.oriMath));
        }

        return blackSubArray.join(",")
        // return this.formatConversionExplain(
        //     rule.relation
        // );
    }
}

module.exports = Kind4RuleHandler;
