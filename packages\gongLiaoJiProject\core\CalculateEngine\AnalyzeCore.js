const {cLexer:lexer } = require('chain-lexer');
const Decimal = require('decimal.js');
const {ObjectUtils} = require('../../utils/ObjectUtils');

/**
 * 公式解析引擎
 * <AUTHOR>
 * @date 2023/11/29
 */
 class AnalyzeCore{
   static TOKEN_TYPE_ID = "Identifier";
   //函数
   static functions = [ 'sin','cos','tan','arctan','sqr',
        'sqrt','log','log10','ln','max','min',
        'abs','round','exp'];
    //操作符
   static operators = ['+','-','*','/','%','^','(',')'];

    //操作符和函数的优先级
   static operatorPriority = {
        '+':2,'-':2,'*':3,'/':3,'%':3,'^':4,'(':1,')':1,
        'sin':5,'cos':5,'tan':5,'arctan':5,'sqr':5,
        'sqrt':5,'log':5,'log10':5,'ln':5,'max':5,'min':5,
        'abs':5,'round':5,'exp':5
   }

   /**
    * 公式解析
    * @param calculateBase
    * @returns {any[]}
    */
   static renderParams(calculateBase,isFilter = true, isRepeat = true) {
        let paramsSet= isRepeat?new Set():[];
        lexer.start(calculateBase);
        let parsedTokens = lexer.DFA.result.tokens;
        //合并负数的情况
        for(let i=0;i<parsedTokens.length;i++){
            let token = parsedTokens[i];
            if(i>0 && (token.type === 'Number' || token.type === 'Float') && parsedTokens[i-1].value==='-'){
                
                let preToken = i>1 ? parsedTokens[i-2] : null;
                if(ObjectUtils.isEmpty(preToken) || preToken.value  === '(' || preToken.value === ',' || preToken.type === 'Operator'){
                    token.value = '-' + token.value;
                    parsedTokens[i-1].deleted = true; // 删除前面的符号
                }
            }
        }
        parsedTokens = parsedTokens.filter(token => !token.deleted);
        //过滤掉非标识符的token
        if(isFilter){
            parsedTokens = parsedTokens.filter(token => token.type === AnalyzeCore.TOKEN_TYPE_ID);
        }
        for (let token of parsedTokens) {
            isRepeat?paramsSet.add(token.value):paramsSet.push(token.value);
        };
        return isRepeat?Array.from(paramsSet):paramsSet;
    }

    static renderFunction(params,calculateBase){
        let result = this.createFormulaRunner(calculateBase);
        return result;
    }

    //创建公式执行器
    static createFormulaRunner(calculateBase){
        // const tokens = this.tokenize(calculateBase);
        const tokens = this.renderParams(calculateBase,false,false);
        const postfix = this.infixToPostfix(tokens);
        // console.log(postfix)
        return (variables) =>{
            const stack = [];
            
            let args = null;
            for(let i=0; i < postfix.length;i++){
                let token = postfix[i];
                if(this.isFunction(token)){
                    let funcOperation = this.createFunctionOperation(args,token);
                    // console.log('funcOperation'+funcOperation);
                    stack.push(funcOperation);
                    args = null;
                }else if(this.isOperator(token)){
                    const right = stack.pop();
                    const left = stack.pop();
                    let operation = this.createOperation(left,right,token);
                    stack.push(operation);
                }else if(token === '|'){
                    let param = stack.pop();
                    if(param && postfix[i-1] !== '&&&'){
                        args.push(param)
                    }else{
                        stack.push(param);
                    }
                }else if(token === '&&&'){
                    args = [];//donull
                }else{
                    stack.push(this.getOperand(token,variables));
                }
            }
           
            return stack.pop().toDecimal().toNumber();
        }
    }

    //token解析器
    // static tokenize(formula){
    //     return formula.match(/(\d+\.?\d*|\w+)|([+\-*/%^(),])/g).filter(x=>x.trim());
    // }

    //中缀表达式转后缀表达式
    static infixToPostfix(tokens){
        const output=[];
        const stack = [];
        let functionName = null;
        for(let i=0; i < tokens.length; i++){
            const token = tokens[i];
            if(!functionName && this.isFunction(token) && i+1 < tokens.length && tokens[i+1] === '('){
                functionName = token;
                stack.push('('); // 用于标记函数开始
                output.push('&&&'); // 用于标记函数开始
                i++;
                continue;
            }
            if(functionName && token === ','){
                while(stack.length && stack[stack.length - 1] !== '('){
                    output.push(stack.pop());
                }
                output.push('|');
                continue;
            }

            if(token === '('){
                stack.push(token);
            }else if(token === ')'){
                while(stack.length && stack[stack.length - 1] !== '('){
                    output.push(stack.pop());
                }
                stack.pop();
                if(functionName){
                    output.push('|');
                    output.push(functionName);
                    functionName = null; // 重置函数名
                }
            }else if(this.isOperator(token)){
                while(stack.length && AnalyzeCore.operatorPriority[stack[stack.length-1]]>=AnalyzeCore.operatorPriority[token]){
                    
                    output.push(stack.pop());
                }
                stack.push(token);
            }else{
                output.push(token);
            }
        }
        return output.concat(stack.reverse());
    }

    //创建操作
    static createOperation(left,right,operator){
        
        const methodMap = {
            "+":'add','-':'sub','*':'mul','/':'div','%':'mod','^':'pow'
        };
        return {
            toDecimal:()=>{
                return left.toDecimal()[methodMap[operator]](right.toDecimal())
            }
        };
    }
    static createFunctionOperation(args,funcName){
        //根据函数的参数及名称
        // console.log('createFunctionOperation',args,funcName);
        const funcMap = {
            'sin':(x)=>Decimal.sin(x),
            'cos':(x)=>Decimal.cos(x),
            'tan':(x)=>Decimal.tan(x),
            'arctan':(x)=>Decimal.atan(x),
            'sqr':(x)=>x.mul(x),
            'sqrt':(x)=>Decimal.sqrt(x),
            'log':(x)=>Decimal.log(x),
            'log10':(x)=>Decimal.log10(x),
            'ln':(x)=>Decimal.ln(x),
            'max':(x,y)=>Decimal.max(x,y),
            'min':(x,y)=>Decimal.min(x,y),
            'abs':(x)=> Decimal.abs(x),
            'round':(x)=>Decimal.round(x),
            'exp':(x)=>Decimal.exp(x)
        };
        let params = [];
        for(let arg of args){
            if(arg.toDecimal){
                params.push(arg.toDecimal());
            }else{
                params.push(arg);
            }
        }        
        return {
            toDecimal:()=>{
                if(ObjectUtils.isEmpty(params)){
                    throw new Error("参数无效");
                }
                let result = funcMap[funcName.toLowerCase()](...params);
                if(!isFinite(result) || isNaN(parseFloat(result))){
                    throw new Error("参数无效");
                }
                return result;
            }
        };
    }

    //获取操作数
    static getOperand(token,variables){
        return {
            toDecimal: ()=>{
                //未定义为0，不知道妥不妥
                if(variables[token] == undefined || ObjectUtils.isNumber(token) || ObjectUtils.isNumberStr(token)){
                    return new Decimal(token);
                }
                return new Decimal(String(variables[token]));
            }
        };
    }

    //判断是否为操作符
    static isOperator(token){
        return AnalyzeCore.operators.includes(token);
    }
    //判断是否为函数
    static isFunction(token){
        return AnalyzeCore.functions.includes(token.toLowerCase());
    }
}

module.exports = {AnalyzeCore}
