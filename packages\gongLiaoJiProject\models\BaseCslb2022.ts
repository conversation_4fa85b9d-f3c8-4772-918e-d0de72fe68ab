import { Column, Entity } from 'typeorm';

import { BaseModel } from './BaseModel';

@Entity({name: "base_cslb_2022"})
export class BaseCslb2022 extends BaseModel {
    @Column({ nullable: true, name: 'library_code' })
    public libraryCode: string; // '所属定额册编码',
    @Column({nullable:true,name:"library_name"})
    public libraryName: string; // '所属定额册名称',
    @Column({nullable:true,name:"cslb_code"})
    public cslbCode: string; // '措施类别编码',
    @Column({nullable:true,name:"cslb_name"})
    public cslbName: string; // '措施类别名称',
    @Column({nullable:true,name:"unit_project_name"})
    public unitProjectName: string; // '',
    @Column({ nullable: true, name: 'sort_no' })
    public sortNo: string; // '排序',
    @Column({ nullable: true, name: 'sort_no_full' })
    public sortNoFull: string; // '排序',
}
