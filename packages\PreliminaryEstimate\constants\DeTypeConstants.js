class DeTypeConstants {

  //默认  空行
  static DE_TYPE_EMPTY = "-1";
  static DE_TYPE_EMPTY_LABEL = "";
  //默认 整个项目
  static DE_TYPE_DEFAULT = "0";
  static DE_TYPE_DEFAULT_LABEL = "单位工程";
  // 分部
  static DE_TYPE_FB = "01";
  static DE_TYPE_FB_LABEL = "部";

  // 子分部
  static DE_TYPE_ZFB = "02";
  static DE_TYPE_ZFB_LABEL = "子";
  // 清单 概算定额
  static DE_TYPE_DELIST = "03";
  static DE_TYPE_DELIST_LABEL = "定";
  // 定额
  static DE_TYPE_DE = "04";


  static DE_TYPE_DE_LABEL = "定";

  static DE_TYPE_JIEHUAN_LABEL = "借换";
  static DE_TYPE_JIE_LABEL = "借";
  static DE_TYPE_HUAN_LABEL = "换";

  // 伪定额
  static SUB_DE_TYPE_DE = "05";
  static SUB_DE_TYPE_DE_LABEL = "定";

  //人材机 只在概算中有实现
  static DE_TYPE_RESOURCE = "06";
  static DE_TYPE_RESOURCE_LABEL = "人 ";
  //人  机 (设 主材 材)此三个可以互相转换

  //安装费用定额
  static DE_TYPE_ANZHUANG_FEE = "07";
  // 费替换为’安‘
  static DE_TYPE_ANZHUANG_FEE_LABEL = "安"

  //用户定额
  static DE_TYPE_USER_DE = "08";
  static DE_TYPE_USER_DE_LABEL = "补"

  //用户人材机
  static DE_TYPE_USER_RESOURCE = "09";
  static DE_TYPE_USER_RESOURCE_LABEL = "补";
  static DE_COPY_BUFFER = "DE_COPY_BUFFER";
}
DeTypeConstants.toString = () => 'DeConstants';
module.exports = DeTypeConstants;