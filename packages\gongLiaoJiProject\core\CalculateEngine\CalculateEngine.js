const _ = require("lodash");
const { AnalyzeCore } = require('./AnalyzeCore');
const {NumberUtil} = require("../../utils/NumberUtil");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const LogUtil = require("../tools/logUtil");

class CalculateEngine{


    constructor() {
        //规则的定义
        this.definitionMap = {};
        //规则的实例
        this.instanceMap = {};
    }

    cupmutefilter() {
        return true;
    }
    convertValue(value,param) {
        return NumberUtil.numberScale(value, 8);
    }
    //解析基础参数
    preload(fns){
        for (let key in fns){
            let fvalue = fns[key];
            let value = null;
            if(typeof fvalue == "function"){
                value = this.getValue(fvalue())
            }else {
                value = fvalue;
            }
            this.instanceMap[key] = value;
            this.definitionMap[key] = fvalue;
        }
    }
    loadRules(rules){
        for (const rulesKey in rules) {
            this.definitionMap[rulesKey] = rules[rulesKey];
        }
    }
    //解析模板

    //解析参数值
    parser(param){
        if(param in this.instanceMap) {
            return this.instanceMap[param];
        }
        let calculate = this.definitionMap[param];
        LogUtil.renderLogger(param + "==============>" + calculate);
        let arr = AnalyzeCore.renderParams(calculate);
        if(arr.length > 0){
            //递归解析参数
            let paramsObj ={};
            //函数不做单独计算
            for(let item of arr){
                if(AnalyzeCore.functions.includes(item.toLowerCase())){
                    continue;
                }
                paramsObj[item] = this.parser(item);
            };
            let fn = AnalyzeCore.renderFunction(arr,calculate);
            this.instanceMap[param] = this.execute(fn,paramsObj,arr,param);
        } else {
            this.instanceMap[param] = Number(calculate);
        }
        return this.instanceMap[param];
    }
    //计算函数
    execute(fn,paramsObj,arr,param){
        let length = 0;
        //判断参数中是否有数组有数组的情况需要循环计算累计
        for (const argumentsKey in paramsObj) {
            if(_.isArray(paramsObj[argumentsKey])) {
                length = paramsObj[argumentsKey].length;
            }else {
                let v = paramsObj[argumentsKey];
                if(typeof v =="object") {
                    let res = this.getRuntimeValue(v,param);
                    if(ObjectUtils.isEmpty(res)){//f防止undefined导致计算无法运行
                        res = 0;
                    }
                    paramsObj[argumentsKey] = res;
                }
            }
        }
        let value = 0;
        if(length){
            for(let i=0;i<length;i++){
                let params ={};
                arr.forEach(item => {
                    params[item] =  _.isArray(paramsObj[item])?paramsObj[item][i]:paramsObj[item];
                });
                value += fn(params);
            }
        }else {
            value += fn(paramsObj);
        }
        if (typeof value == "number") {
            value = this.convertValue(value,param);
        }
        return value;
    }
    //运行时获取值 子类实现
    getRuntimeValue({type,kind,column},param){
        throw Error("getRuntimeValue，不可直接调用");
    }
    //规则转换 页面展示规则和 实际计算规则不一样

    //用于基础数据的获取
    getValue({type,kind,column}){
        //获取方式留给子类实现
        throw Error("getValue需要子类实现，不可直接调用");
    }
}
module.exports =  {
    CalculateEngine
}
