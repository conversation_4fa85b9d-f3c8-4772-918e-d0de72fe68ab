const {ObjectUtils} = require("../../../utils/ObjectUtils");
const fbCalculatorBaseFn = {
    //清单工程量
    "JSum": () => {
        return {
            "type": "item",
            "column":"JSum"
        };
    },"CSum": () => {
        return {
            "type": "item",
            "column":"CSum"
        };
    },"RSum": () => {
        return {
            "type": "item",
            "column":"RSum"
        };
    }
    ,"SSum": () => {
        return {
            "type": "item",
            "column":"SSum"
        };
    }
    ,"ZSum": () => {
        return {
            "type": "item",
            "column":"ZSum"
        };
    }
    ,"RDSum": () => {
        return {    
            "type": "item",
            "column":"RDSum"
        };
    }
    ,"CDSum": () => {
        return {
            "type": "item",
            "column":"CDSum"
        };
    }
    ,"JDSum": () => {
        return {
            "type": "item",
            "column":"JDSum"
        };
    }
    ,"SDSum": () => {
        return {
            "type": "item",
            "column":"SDSum"
        };
    }
    ,"ZDSum": () => {
        return {
            "type": "item",
            "column":"ZDSum"
        };
    }
    ,"totalNumber": () => {
        return {
            "type": "item",
            "column":"totalNumber"
        };
    },
    "baseJournalTotalNumber": () => {
        return {
            "type": "item",
            "column":"baseJournalTotalNumber"
        };
    },
    //清单合价
    "rdTotalSum": () => {
        return {
            "type": "item",
            "column":"rdTotalSum"
        };
    },"cdTotalSum": () => {
        return {
            "type": "item",
            "column":"cdTotalSum"
        };
    },"jdTotalSum": () => {
        return {
            "type": "item",
            "column":"jdTotalSum"
        };
    },"sdTotalSum": () => {
        return {
            "type": "item",
            "column":"sdTotalSum"
        };
    },"zdTotalSum": () => {
        return {
            "type": "item",
            "column":"zdTotalSum"
        };
    },
    "rTotalSum": () => {
        return {
            "type": "item",
            "column":"rTotalSum"
        };
    },"cTotalSum": () => {
        return {
            "type": "item",
            "column":"cTotalSum"
        };
    },"jTotalSum": () => {
        return {
            "type": "item",
            "column":"jTotalSum"
        };
    },"sTotalSum": () => {
        return {
            "type": "item",
            "column":"sTotalSum"
        };
    },"zTotalSum": () => {
        return {
            "type": "item",
            "column":"zTotalSum"
        };
    }




}
const FBRules = {
    "rTotalSum":{
        "name":"市场价",
        "mathFormula":"rTotalSum*1",
    },
    "cTotalSum":{
        "name":"市场价",
        "mathFormula":"cTotalSum*1",
    },
    "jTotalSum":{
        "name":"市场价",
        "mathFormula":"jTotalSum*1",
    },
    "sTotalSum":{
        "name":"设备合价",
        "mathFormula":"sTotalSum*1",
    },
    "zTotalSum":{
        "name":"主材合价",
        "mathFormula":"zTotalSum*1",
    },
    "rdTotalSum":{
        "name":"市场价",
        "mathFormula":"rdTotalSum*1",
    },
    "cdTotalSum":{
        "name":"市场价",
        "mathFormula":"cdTotalSum*1",
    },
    "jdTotalSum":{
        "name":"市场价",
        "mathFormula":"jdTotalSum*1",
    },
    "zdTotalSum":{
        "name":"市场价",
        "mathFormula":"zdTotalSum*1",
    },
    "sdTotalSum":{
        "name":"市场价",
        "mathFormula":"sdTotalSum*1",
    },
    "RDSum":{
        "name":"市场价",
        "mathFormula":"RDSum*1",
    },
    "CDSum":{
        "name":"市场价",
        "mathFormula":"CDSum*1",
    },
    "JDSum":{
        "name":"市场价",
        "mathFormula":"JDSum*1",
    },
    "SDSum":{
        "name":"市场价",
        "mathFormula":"SDSum*1",
    },
    "ZDSum":{
        "name":"市场价",
        "mathFormula":"ZDSum*1",
    },
    "RSum":{
        "name":"市场价",
        "mathFormula":"RSum*1",
    },
    "CSum":{
        "name":"市场价",
        "mathFormula":"CSum*1",
    },
    "JSum":{
        "name":"市场价",
        "mathFormula":"JSum*1",
    },
    "SSum":{
        "name":"市场价",
        "mathFormula":"SSum*1",
    },
    "ZSum":{
        "name":"市场价",
        "mathFormula":"ZSum*1",
    },
    "totalNumber":{
        "name":"市场价",
        "mathFormula":"totalNumber*1",
    },
    "baseJournalTotalNumber":{
        "name":"市场价",
        "mathFormula":"baseJournalTotalNumber*1",
    }

}
module.exports = {FBRules,fbCalculatorBaseFn}