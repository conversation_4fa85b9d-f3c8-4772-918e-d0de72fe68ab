const { CalculateEngine } = require('../../core/CalculateEngine/CalculateEngine');
const {AnalyzeCore } = require('../../core/CalculateEngine/AnalyzeCore');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const FunctionTypeConstants = require("../../constants/FunctionTypeConstants");

class DeQualityUtils {

    
  static addNotifyQuantityMap(addDeRow,functionDataMap){
    let deGclMap = functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
    if(ObjectUtils.isEmpty(deGclMap)){
        deGclMap = new Map();
        functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY,deGclMap);
    }
    let deRowBak = {constructId:addDeRow.constructId, unitId:addDeRow.unitId, deRowId:addDeRow.sequenceNbr};
    let deSet = deGclMap.get(addDeRow.unitId);
    if(ObjectUtils.isEmpty(deSet)){
        deSet = [];
        deGclMap.set(addDeRow.unitId,deSet);
    }
    let deExist = deSet.find(item=>item.unitId === addDeRow.unitId && item.deRowId === addDeRow.sequenceNbr);
    if(ObjectUtils.isNotEmpty(deExist)){
        let index = deSet.indexOf(deExist);
        deSet.splice(index,1);
    }
    deSet.push(deRowBak);
  }

    static evalQualityTokens(expression, noFunctions = true) {
        let newTokens = [];
        if(ObjectUtils.isEmpty(expression) || ObjectUtils.isNumber(expression) || ObjectUtils.isNumberStr(expression)){
            return newTokens;
        }
        let tokens =  AnalyzeCore.renderParams(expression);
         if(noFunctions){
            //过滤掉非标识符的token
            for(let i=0;i<tokens.length;i++){
                let token = tokens[i];           
                if(AnalyzeCore.functions.includes(token.toLowerCase())){
                    continue;
                }            
                newTokens.push(token);
            }
        }else{
            newTokens = tokens;
        }
        return newTokens;
    }

    //基于费用代码的计算工程量
    static evalQualityWithCodes(expression, priceCodes){
        if(ObjectUtils.isEmpty(expression)){
            return 0;
        }
        if(ObjectUtils.isNumber(expression)){
            return expression;
        }
        if(ObjectUtils.isNumberStr(expression)){
            return expression;
        }
        const regex = /[+\-*/^]/;  
        //不过滤函数
        let tokens = DeQualityUtils.evalQualityTokens(expression,false);
        //兼容^
        // test 方法用来测试字符串是否匹配正则表达式  
        // 如果匹配到，则返回 true，否则返回 false  
        if(tokens.length === 0){
            if(!regex.test(expression)){
                return expression;   
            }
            
            if(expression.indexOf("^") > -1){
                let fun = AnalyzeCore.renderFunction(null,expression);
                return fun({});    
            }
            return eval(expression);
        }
        
        let aar = new CalculateEngine();
        for(let priceCode of priceCodes){
            let key = priceCode.code
            aar.instanceMap[key] = priceCode.price;
        }
        aar.definitionMap["GCL_EX_CAL"] = expression;
        let result = aar.parser("GCL_EX_CAL");
        return result ;
    }
}
DeQualityUtils.toString = () => 'DeQualityUtils';
module.exports = DeQualityUtils;