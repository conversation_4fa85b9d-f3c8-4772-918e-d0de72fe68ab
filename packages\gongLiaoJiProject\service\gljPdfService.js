const path = require('path');
const {Service} = require("../../../core");
const util = require('util');
const exec = util.promisify(require('child_process').exec);
const UtilsPs = require('../../../core/ps');
const fs = require('fs');
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const projectLevelConstant = require("../constants/ProjectLevelConstant");
const ProjectDomain = require("../domains/ProjectDomain");
const TaxCalculationMethodEnum = require("../enums/TaxCalculationMethodEnum");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {GljExcelUtil} = require("../utils/GljExcelUtil");
const GljExcelEnum = require("../enums/GljExcelEnum");

class GsPdfService extends Service {
    constructor(ctx) {
        super(ctx);
    }


    async runCommand(command) {
        try {
            const {stdout, stderr} = await exec(command);
            console.log(`命令输出结果: ${stdout}`);
            console.error(`命令错误输出: ${stderr}`);
        } catch (error) {
            console.error(`执行命令时出错: ${error}`);
        }
    }


    async excelToPdfDemo() {

        // const ext = 'pdf'; // Output extension.
        // const inputPath = path.join("C:\\Users\\<USER>\\Desktop\\pdf", '/攻城2.xlsx');
        // const outputPath = path.join("C:\\Users\\<USER>\\Desktop\\pdf", `/攻城2.${ext}`);
        //
        // // var workbook = new asposeCells.Workbook("C:\\Users\\<USER>\\Desktop\\模板参考\\示例\\一般计税\\招标项目（招标控制价）\\导出excel（该文件夹下工程项目excel为标准文件格式）\\攻城2\\攻城2.xlsx");
        //
        // // var saveOptions = asposeCells.PdfSaveOptions();
        // // workbook.save("Book1.pdf", saveOptions);
        // //设置环境变量
        // let javaCommand = UtilsPs.getExtraResourcesDir()+"\\jre\\bin\\java";
        // let javaHomePath = UtilsPs.getExtraResourcesDir()+"\\jre";
        // let jarPath = UtilsPs.getExtraResourcesDir()+"\\pdfUtil.jar";
        // let parameters = "C:\\Users\\<USER>\\Desktop\\模板参考\\示例\\一般计税\\招标项目（招标控制价）\\导出excel（该文件夹下工程项目excel为标准文件格式）\\攻城2\\建筑工程.xlsx"
        //     +"   "+"C:\\Users\\<USER>\\Desktop\\模板参考\\示例\\一般计税\\招标项目（招标控制价）\\导出excel（该文件夹下工程项目excel为标准文件格式）\\攻城2\\建筑工程.pdf";
        // await this.runCommand(javaCommand+" -DJAVA_HOME="+javaHomePath+"  -jar "+jarPath+"  "+parameters);


        let project = await this.initWorkBook(projectLevelConstant.construct);
        //初始化workbook后的重新写入都是依照 worksheets属性的排序进行的

        //生成excel
        let excelFilePath = UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\export\\pdf.xlsx";
        let pdfPath = UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\export\\demo.pdf";
        await this.createDirectory(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\export");
        await project.xlsx.writeFile(excelFilePath);
        //设置环境变量
        let javaCommand = UtilsPs.getExtraResourcesDir() + "\\jre\\bin\\java";
        let javaHomePath = UtilsPs.getExtraResourcesDir() + "\\jre";
        let jarPath = UtilsPs.getExtraResourcesDir() + "\\pdfUtil.jar";
        let parameters = excelFilePath
            + "   " + pdfPath;

        await this.runCommand(javaCommand + " -DJAVA_HOME=" + javaHomePath + "  -jar " + jarPath + "  " + parameters);
        //删除原来生成的excel文件

        // let total = 0;
        // for (let i = 0; i < unit._worksheets.length; i++) {
        //     let worksheet1 = unit._worksheets[i];
        //     if (worksheet1 != null) {
        //         total++;
        //     }
        // }
        //
        // let worksheet = unit.getWorksheet("封面2 招标控制价（标底）");
        // unit.removeWorksheet(worksheet.id);
        //
        // let worksheetQd = unit.getWorksheet("表1-1 工程量清单编制说明");
        // worksheetQd.id = 45;
        // unit.removeWorksheet(worksheetQd.id);
        // unit.removeWorksheet(2);
        // unit.removeWorksheet(24);
        console.log("");
    }

    //sheetName传值的话表示为预览页的导出
    async excelToPdf(params,lanMuName,sheetName,startPage,totalPage) {

        const dialogOptions = {
            title: '保存文件',
            defaultPath: ObjectUtils.isNotEmpty(sheetName)?sheetName:params.headLine,
            filters: [{name: 'pdf', extensions: ['pdf']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            await this.exportPdf(params, filePath,lanMuName,startPage,totalPage);
            return true;
        } else {
            return false;
        }
    }

    async exportPdf(params, pdfPath,lanMuName,startPage,totalPage) {
        let construct = ProjectDomain.getDomain(params.id).getProjectById(params.id);
        let taxCalculationMethodPath = "";
        let taxCalculationMethod = construct.projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxCalculationMethodPath = "简易";
        }else {
            taxCalculationMethodPath = "一般";
        }
        let project = await this.service.gongLiaoJiProject.gljExportQueryService.initWorkBook(projectLevelConstant.construct,lanMuName,taxCalculationMethodPath);
        let unit = await this.service.gongLiaoJiProject.gljExportQueryService.initWorkBook(projectLevelConstant.unit,lanMuName,taxCalculationMethodPath);

        let fileDir = this.getProjectRootPath() + "\\excelTemplate\\glj\\" + params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        args["lanMuName"] = lanMuName;
        args['startPage'] = startPage;
        args['totalPage'] = totalPage;
        args['fileType'] = "pdf";
        args['totalPdfPageSize'] = 0;
        await this.parseParams(params, project, null, unit, fileDir, args, workBookList,taxCalculationMethodPath,lanMuName);

        //合成一个大excel文件  最后生成一个pdf文件
        //先对workBookList[0]._worksheets 按照 worksheets 进行重排  worksheets属性 始终是有序的
        for (let i = 0; i < workBookList[0].worksheets.length; i++) {

            //处理pdf的页码
            if (args['fileType'] === "pdf") {
                if (ObjectUtils.isNotEmpty(args['startPage']) && ObjectUtils.isEmpty(args['totalPage'])) {
                    for (let worksheet1 of workBookList[0].worksheets) {
                        let cellList = GljExcelUtil.findContainValueCell(worksheet1, GljExcelEnum.totalPdfPageSizeReplaceStr);
                        const grouped = cellList.reduce((result, obj) => {
                            const key = obj.cell._row._number;
                            if (!result[key]) {
                                result[key] = [];
                            }
                            result[key].push(obj.cell);
                            return result;
                        }, {});
                        let mergeMap = new Map(Object.entries(grouped));
                        for (let [key, value] of mergeMap) {
                            for (let i = 0; i < value.length; i++) {
                                let elementCell = value[i];
                                elementCell.value = elementCell.value.replace(GljExcelEnum.totalPdfPageSize, args['totalPdfPageSize']);
                                elementCell.style.alignment.vertical = "bottom";
                            }
                        }
                    }
                }
            }


            //确定_worksheets 中当前的索引  及id相同的索引 进行位置交换
            let indexCur = await this.getIndexIn_worksheets(i + 1, workBookList[0]);
            let indexId = await this.getIndexOfSameId(workBookList[0].worksheets[i].id, workBookList[0]);
            [workBookList[0]._worksheets[indexCur], workBookList[0]._worksheets[indexId]] = [workBookList[0]._worksheets[indexId], workBookList[0]._worksheets[indexCur]];
        }
        for (let i = 1; i < workBookList.length; i++) {
            let bookElement = workBookList[i];
            for (let j = 0; j < bookElement.worksheets.length; j++) {
                let worksheet = bookElement.worksheets[j];
                if (worksheet != null) {
                    workBookList[0]._worksheets.push(worksheet);
                }
            }
        }
        //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
        //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
        let orderNo = 0;
        for (let i = 0; i < workBookList[0]._worksheets.length; i++) {
            let worksheetSam = workBookList[0]._worksheets[i];
            if (worksheetSam != null) {
                worksheetSam.id = ++orderNo;
                worksheetSam.orderNo = orderNo;
            }
        }
        //生成excel
        let excelFilePath = UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\glj\\pdf.xlsx";
        await this.createDirectory(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\glj");
        await workBookList[0].xlsx.writeFile(excelFilePath);
        //设置环境变量
        let javaCommand = UtilsPs.getExtraResourcesDir() + "\\jre\\bin\\java";
        let javaHomePath = UtilsPs.getExtraResourcesDir() + "\\jre";
        let jarPath = UtilsPs.getExtraResourcesDir() + "\\pdfUtil.jar";
        let parameters = "\""+excelFilePath+"\""
            +"   "+"\""+pdfPath+"\"";

        await this.runCommand(javaCommand + " -DJAVA_HOME=" + javaHomePath + "  -jar " + jarPath + "  " + parameters);
        //删除原来生成的excel文件
        fs.unlink(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\glj\\pdf.xlsx", (err) => {
            if (err) {
                console.error('删除文件时出错:', err);
                return;
            }
            console.log('文件删除成功！');
        });
    }

    async getIndexIn_worksheets(order, workbook) {
        let index = 0;
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i] != null) {
                index++;
                if (index == order) {
                    return i;
                }
            }
        }
    }

    async getIndexOfSameId(idParam, workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i] != null && workbook._worksheets[i].id == idParam) {
                return i;
            }
        }
    }

    getProjectRootPath() {
        // let relativePath = __filename;
        // let index = relativePath.indexOf("pricing-cs");
        // let prefix = relativePath.substring(0,index);
        return UtilsPs.getExtraResourcesDir();
        // return prefix+"pricing-cs";
    }

    async createDirectory(directoryPath) {
        if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, {recursive: true});
        } else {
        }
    }

    async deleteDirectory(dirPath) {
        if (fs.existsSync(dirPath)) {
            fs.readdirSync(dirPath).forEach(file => {
                const filePath = path.join(dirPath, file);

                if (fs.lstatSync(filePath).isDirectory()) {
                    deleteDirectory(filePath); // 递归删除子目录
                } else {
                    fs.unlinkSync(filePath); // 删除文件
                }
            });

            fs.rmdirSync(dirPath); // 删除空目录
            console.log('目录删除成功');
        } else {
            console.log('目录不存在');
        }
    }



    async parseParams(params, project, single, unit, fileDir, args, workBookList,taxCalculationMethodPath,lanMuName) {
        if (args == null) {
            args = {};
        }
        args.lanMuName = lanMuName;

        if (params.levelType == projectLevelConstant.construct) {
            args["constructId"] = params.id;
        }else if (params.levelType == projectLevelConstant.single) {
            args["singleId"] = params.id;
        }else {
            args["unitId"] = params.id;
        }

        for (let i = 0; i < params.childrenList.length; i++) {
            let param = params.childrenList[i];
            //如果为总工程层级
            if (param.projectLevel != null && param.projectLevel == "project") {
                // args["constructId"] = params.id;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    args["id"] = param.id;
                    await this.service.gongLiaoJiProject.gljExportQueryService.getWorkSheetWithData(project, params.levelType, param.headLine, args);
                } else {
                    project.removeWorksheet(param.headLine);
                }
            }
            if (param.projectLevel != null && param.projectLevel == "single") {
                // args["singleId"] = params.id;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    args["id"] = param.id;
                    await this.service.gongLiaoJiProject.gljExportQueryService.getWorkSheetWithData(single, params.levelType, param.headLine, args);
                } else {
                    single.removeWorksheet(param.headLine);
                }
            }
            if (param.projectLevel != null && param.projectLevel == "unit") {

                //前面查询为了唯一把id换为单位id+报表id，这里重置为报表id
                if(String(param.id).includes("-")){
                    param.id = Number(param.id.split("-")[1]);
                }

                // args["unitId"] = params.id;
                // args["singleId"] = params.singleId;
                args["levelType"] = params.levelType;
                if(param.id==11){
                    param.headLine = '单位工程造价汇总表(省站标准) (2)';
                }
                if (param.selected) {
                    args["id"] = param.id;
                    args["ceping"] = param.ceping;
                    await this.service.gongLiaoJiProject.gljExportQueryService.getWorkSheetWithData(unit, params.levelType, param.headLine, args);
                } else {
                    unit.removeWorksheet(param.headLine);
                }
            }
        }

        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "project") {
            await this.service.gongLiaoJiProject.gljExportQueryService.resetOrderNo(params,project);
            if (project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(project);
            }
        }
        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "single") {
            await this.service.gongLiaoJiProject.gljExportQueryService.resetOrderNo(params,single);
            if (single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(single);
            }
        }
        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "unit") {
            //判断单位工程的计税方式,如果没有增值税报表就进行删除
            let argsObject = {};
            argsObject['levelType'] = 3;//表明为单位工程层级
            if (params.biddingType == 2) {  //如果预算工程整体为单位工程
                argsObject['constructId'] = params.id;
            }else {
                //如果是其他栏目的  走到这里 args['singleId'] 为空  因为前面的递归遍历并没有给其赋值
                let unitArray = ProjectDomain.getDomain(args['constructId']).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
                let unitObject = unitArray.filter(item => item.sequenceNbr==params.id);
                args['singleId'] = unitObject[0].spId;
                argsObject['constructId'] = args['constructId'];
                argsObject['singleId'] = args['singleId'];
                argsObject['unitId'] = params.id;
            }
            await this.service.gongLiaoJiProject.gljExportQueryService.resetOrderNo(params,unit);
            if (unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(unit);
            }
        }


        let filter = params.childrenList.filter(itemParam => itemParam.childrenList != null);//含有子节点的节点
        if (filter != null) {
            let directory;
            for (let i = 0; i < filter.length; i++) {
                //同时对single  和 unit对象进行初始化
                single = await this.service.gongLiaoJiProject.gljExportQueryService.initWorkBook(projectLevelConstant.single,lanMuName,taxCalculationMethodPath);
                unit = await this.service.gongLiaoJiProject.gljExportQueryService.initWorkBook(projectLevelConstant.unit,lanMuName,taxCalculationMethodPath);
                directory = fileDir + "\\" + filter[i].headLine;
                await this.parseParams(filter[i], project, single, unit, directory, args, workBookList,taxCalculationMethodPath,lanMuName);
            }
        }
    }


}

GsPdfService.toString = () => '[class GsPdfService]';
module.exports = GsPdfService;
