const { Controller } = require('../../../core');
const ProjectDomain = require('../domains/ProjectDomain');
const { Snowflake } = require('../../../electron/utils/Snowflake');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const ProjectModel = require('../domains/projectProcessor/models/ProjectModel');
const SingleProjectModel = require('../domains/projectProcessor/models/SingleProjectModel');
const UnitProjectModel = require('../domains/projectProcessor/models/UnitProjectModel');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const FBFXModel = require('../domains/deProcessor/models/FBFXModel');
const DeListModel = require('../domains/deProcessor/models/DeListModel');
const DeModel = require('../domains/deProcessor/models/DeModel');
const ResourceModel = require('../domains/deProcessor/models/ResourceModel');
const DeTypeConstants = require('../constants/DeTypeConstants');
const AppContext = require('../core/container/APPContext');
const FileOperator = require('../core/tools/fileOperator/FileOperator');
const FileOperatorType = require('../constants/FileOperatorType');
const GsDeController = require("./gsDeController");
const { ResponseData } = require('../../../electron/utils/ResponseData');
const _ = require("lodash");
const DeDomain = require("../domains/DeDomain");
const DeUtils = require("../domains/utils/DeUtils");
const PropertyUtil = require('../domains/utils/PropertyUtil');

class GsProjectController extends Controller {

  constructor(ctx) {
    super(ctx);
  }
 async moveUpAndDown(args){
  let{constructId,sequenceNbr,type} = args;
  await ProjectDomain.getDomain(constructId).moveUpAndDown(sequenceNbr,type);
  return ResponseData.success();
 }
  /**
   * 获得最近打开的项目列表
   */
  getRecentProjects()
  {
    ProjectDomain.getRecentProject();
  }
  /**
   * 创建工程项目/单项/单位
   * @param args ProjectModel 为创建的Model,rootId 为
   * @returns {*}
   */
  async createProject(args)
  {
    let {ProjectModel :argsModel ,constructId} = args;

    argsModel.sequenceNbr = Snowflake.nextId();

    if(ObjectUtil.isEmpty(constructId) && argsModel.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT)
    {
      constructId = argsModel.sequenceNbr;
    }
    console.log("constructId-->:" + constructId + "type--->" + argsModel.type + "id=====》" + argsModel.sequenceNbr);
    let newProject =  new ProjectModel(argsModel.sequenceNbr,argsModel.type,null);
    newProject.init(argsModel);
    newProject.type = argsModel.type;
    newProject.parentId = argsModel.parentId;
    this.do4Type(argsModel,newProject);
    await ProjectDomain.getDomain(constructId).createProject(newProject);

    //工程下有单位，此时新建单项，将单位父级修改为新建的单项
    if (newProject.parentId === constructId && argsModel.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
       await this.updateUnitParentSingle(constructId, newProject);
    }

    return ResponseData.success(ProjectDomain.filter4ProjectTree(newProject));
  }


  async updateUnitParentSingle(constructId, newProject) {
        let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.parentId === constructId);
        if (ObjectUtil.isNotEmpty(unitProjects)) {
            let root = ProjectDomain.getDomain(constructId).ctx.treeProject.root;
            ProjectDomain.getDomain(constructId).ctx.treeProject.root.children = root.children.filter(o => o.type == ProjectTypeConstants.PROJECT_TYPE_SINGLE);
            unitProjects.forEach(o => {
                o.parentId = newProject.sequenceNbr;
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(o, newProject);
            })
        }
  }


  async updateUnitParentSingle1(constructId, newProject) {
        let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.parentId === newProject.sequenceNbr);
        if (ObjectUtil.isNotEmpty(unitProjects)) {
            let root = ProjectDomain.getDomain(constructId).ctx.treeProject.root;
            ProjectDomain.getDomain(constructId).ctx.treeProject.root.children = root.children.filter(o => o.type == ProjectTypeConstants.PROJECT_TYPE_SINGLE);
            unitProjects.forEach(o => {
                o.parentId = newProject.sequenceNbr;
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(o, newProject);
            })
        }
  }



  do4Type(argsModel,newProject)
  {
    switch (argsModel.type)
    {
      case ProjectTypeConstants.PROJECT_TYPE_UNIT:
            if(!ObjectUtil.isEmpty(argsModel.constructMajorType))
            {
              newProject.constructMajorType = argsModel.constructMajorType;
            }
            break;
      case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
            if(!ObjectUtil.isEmpty(argsModel.deLibrary)) {
              newProject.deLibrary = argsModel.deLibrary;
            }
            break;
    }
  }

  /**
   *
   * 通过项目Id获得工程项目的项目树
   * @param args
   * @returns {string}
   */
  async getProjectTree(args)
  {
    let {constructId} = args;
    return  ResponseData.success(ProjectDomain.getDomain(constructId).getProjectTree());
  }
  //删除工程项目
  async removeProject(args)
  {
    let {pid ,constructId} = args
    let removeFlag = await ProjectDomain.getDomain(constructId).removeProject(pid);
    if(removeFlag){
      return ResponseData.success();
    }else{
      return ResponseData.fail("单位工程不存在");
    }
  }

  async updateProject(args)
  {
    let {ProjectModel,constructId} = args;
    return ResponseData.success(ProjectDomain.getDomain(constructId).updateProject(ProjectModel));
  }
  async checkConstructMajorTypeByUnitId(args)
  {
    let {unitId,constructId} = args;
    let projectModels = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === unitId)
    if(ObjectUtil.isEmpty(projectModels) || ObjectUtil.isEmpty(projectModels[0]))
    {
      throw Error("单位工程不存在.");
    }
    let result = false;
    if(projectModels[0].type === ProjectTypeConstants.PROJECT_TYPE_UNIT && !ObjectUtil.isEmpty(projectModels[0].constructMajorType))
    {
      result = true;
    }
    return ResponseData.success(result);
  }

  async getProjectDetailById(args)
  {
    let {pid,constructId} = args;
    return ResponseData.success(ProjectDomain.getDomain(constructId).getProjectById(constructId));
  }

  /**
   * 两个项目的导入操作，合并两个项目
   * list 为前端组织好的全量新的树机构  并不是数组
   * rateType 00 当前费率为准 01 导入费率为准
   * @param {*} args 
   * @returns 
   */
  async composeProject(args){
    let {constructId,iConstructId,list,rateType} = args;
    // 0 记录新增的单项及单位
    let iAddSingleMap = new Map(); //新增的单项id，导入的 iSingleId--->newSingleId
    let iAddUnitMap = new Map(); //新增的单位id，导入的 iUnitId--->newUnitId

    //1.处理项目树tree    计算：list-curTreeList =
    // 新增的单位或单项
    await this.newReCreateTreeProject(list,constructId,iAddSingleMap,iAddUnitMap);
    let currentP = ProjectDomain.getDomain(constructId);
    let sourceP = ProjectDomain.getDomain(iConstructId);
    if(ObjectUtil.isNotEmpty(sourceP) && ObjectUtil.isNotEmpty(currentP) && ObjectUtil.isNotEmpty(iAddUnitMap))
    {
        for (let [iUnitId,newUnitId] of iAddUnitMap.entries())
        {
            //2.处理 demap
            let sourceDes = sourceP.ctx.deMap.filter(item => item.unitId === iUnitId);
            let oldDeIdMaps = DeUtils.buildTreeWithParents(sourceDes,currentP.ctx.deMap,constructId,newUnitId);
            //3.处理 resoucemap 人材机
            let sourceResourceList = sourceP.ctx.resourceMap.valuesAsArray().filter(item => item.unitId === iUnitId);
            for (let resource of sourceResourceList )
            {
              let newResource = _.cloneDeep(resource);
              resource.constructId = constructId;
              resource.unitId = newUnitId;
              let newDeId = oldDeIdMaps.get(resource.deRowId);//
              if(ObjectUtil.isEmpty(newDeId)){
                continue;
              }
              newResource.deRowId = newDeId;
              currentP.resourceDomain.createResource(newUnitId,newDeId,newResource);
            }
        }

    }
    //4.处理 functionMap
    // 通用处理，包含导入的iSingleId和iUnitId统统迁移
    let curFunctionMap =  ProjectDomain.getDomain(constructId).functionDataMap;
    let iFunctionMap = ProjectDomain.getDomain(iConstructId).functionDataMap;

    iFunctionMap.forEach((iValue,iKey)=>{
      if(iValue instanceof Map){
        //遍历下一层的数据，k中包含iUnitId
        iValue.forEach((v,k)=>{
          let iUnitId = null;
          let newUnitId = null;
          //包含单位的数据
          iAddUnitMap.forEach((iAddV,iAddK)=>{
            if(k.indexOf(iAddK) > -1){
              newUnitId = iAddV;
              iUnitId = iAddK;
            }
          });
          if(newUnitId){
            iAddSingleMap.forEach((sV,sK)=>{k = k.replace(sK,sV)});//暂不考虑单项数据
            let newkey = k.replace(iUnitId,newUnitId);
            if(!curFunctionMap.has(iKey)){
              curFunctionMap.set(iKey,new Map());
            }
            curFunctionMap.get(iKey).set(newkey,v);//无需考虑ikey是否存在，，初始化必定存在
          }
        })
      }
    })

    //取费表影响,当前费率为准是负责
    if(rateType==='00'){
       await this.service.PreliminaryEstimate.gsFreeRateService.afterImportUnifiedApplication(constructId, [...iAddUnitMap.values()]) 
    }

    return ResponseData.success(args);
  }


  async saveTreeProject(args){
    let {constructId,list} = args;
    if(ObjectUtil.isEmpty(list) || list.length ===0){
      return ResponseData.fail("参数为空")
    }

    //但前工程项目的树结构
    let curTreeList = ProjectDomain.getDomain(constructId).getProjectTree();
    let iAddSingleMap = new Map(); //新增的单项id，导入的 iSingleId--->newSingleId
    let iAddUnitMap = new Map(); //新增的单位id，导入的 iUnitId--->newUnitId
    let removePids = [];
    let updateProjects = []
    for(let item of curTreeList){
      let curItem = list.find(lItem=>lItem.sequenceNbr === item.sequenceNbr);
      if(!curItem){
        removePids.push(item.sequenceNbr);
      }else{
          if (curItem.name !== item.name || curItem.constructMajorType !== item.constructMajorType || curItem.parentId !== item.parentId) {
              updateProjects.push(curItem);
          }
      }
    }
    let projectDomain = ProjectDomain.getDomain(constructId)
    //1.处理删除  
    if(removePids.length > 0){
      for(let pid of removePids){
        let project = projectDomain.getProjectById(pid);
        if(project){
          projectDomain.removeProject(pid);
        }
      }
    }
    //2.处理变更
    if(updateProjects.length>0){
      for(let updateItem of updateProjects){
        projectDomain.updateProject(updateItem);
      }
    }

      // ---------------------------------
      let curTreeIds = curTreeList.map(item => item.sequenceNbr);// 旧树ids
      for (let item of list) {
          if (!curTreeIds.includes(item.sequenceNbr)) {
              //重新维护树结构
              let newProject = new ProjectModel(item.sequenceNbr, item.type, item.parentId);
              for (const valueKey in item) {
                  if (valueKey !== 'children' && valueKey !== 'sequenceNbr' && valueKey !== 'parentId' && valueKey !== 'constructId') {
                      newProject[valueKey] = item[valueKey];
                  }
              }
              await projectDomain.createProject(newProject);

              //工程下直挂单位，此时新建单项，将单位父级修改为新建的单项
              if (newProject.parentId === constructId && item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
                  let filterList = list.filter(fitem => fitem.parentId == newProject.sequenceNbr);
                  if (ObjectUtil.isNotEmpty(filterList)) {
                      await this.updateUnitParentSingle1(constructId, newProject);
                  }
              }

          }
      }

      // ---------------------------------
      // //3.处理新增  todo
      // //3.1 生成树形结构
      // list = xeUtils.toArrayTree(list, {
      //     key: 'sequenceNbr',
      //     parentKey: 'parentId',
      // });
      // // list = this.generalTree(list);
      // //3.2 创建xin项目
      // await this.reCreateTreeProject(list[0], curTreeList, constructId, iAddSingleMap, iAddUnitMap);
    
    //需要操作什么？就可以iAddUnitMap 和 iAddSingleMap
    return ResponseData.success();
  }


  // generalTree(list){
  //   let result = [];
  //   for(let item of list){
  //     if(!item.parentId){
  //       result.push({
  //         type:item.type
  //         ,sequenceNbr:item.sequenceNbr
  //         ,name:item.name
  //         ,constructMajorType:item.type===ProjectTypeConstants.PROJECT_TYPE_UNIT?item.constructMajorType:null
  //         ,children:[]
  //         ,parentId:null});
  //     }else{
  //       let parentItem = this.findResultItem(result,item.parentId);
  //       if(parentItem && !ObjectUtil.isEmpty(parentItem)){
  //         parentItem.children.push({
  //           type:item.type
  //           ,sequenceNbr:item.sequenceNbr
  //           ,name:item.name
  //           ,constructMajorType:item.type===ProjectTypeConstants.PROJECT_TYPE_UNIT?item.constructMajorType:null
  //           ,children:[]
  //           ,parentId:parentItem.sequenceNbr});
  //       }
  //     }
  //   }
  //   return result
  // }
  //
  // findResultItem(result,parentId){
  //   let resultItem = null;
  //   for(let item of result){
  //     if(item.sequenceNbr === parentId){
  //       resultItem =  item;
  //     }else{
  //       if(item.children && item.children.length > 0){
  //         resultItem = this.findResultItem(item.children,parentId);
  //       }
  //     }
  //   }
  //   return resultItem;
  // }

  async newReCreateTreeProject(list,constructId,iAddSingleMap,iAddUnitMap){
    if(list.type !== ProjectTypeConstants.PROJECT_TYPE_PROJECT && list.whetherNew){
         let newSequenceNbr = Snowflake.nextId();
         //新增的id存起来
         if(list.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE){
           iAddSingleMap.set(list.sequenceNbr,newSequenceNbr);
         } else{
           iAddUnitMap.set(list.sequenceNbr,newSequenceNbr);
         }
         let parentId = list.parentId;
         if(parentId != constructId){
           for(const[sKey,sValue] of iAddSingleMap){
             if(parentId === sKey){
               parentId = sValue;
             }
           }
         }
         //重新维护树结构
         let newProject =  new ProjectModel(newSequenceNbr,list.type,parentId);
         //忽略children[]
         for (const valueKey in list) {
           if(valueKey !== 'children' && valueKey !== 'sequenceNbr' && valueKey !== 'parentId' && valueKey !== 'constructId'){
             newProject[valueKey]= list[valueKey];
           }
         }
         let projectDomain = ProjectDomain.getDomain(constructId)
         await projectDomain.createProject(newProject);
     }
     if(list.children && list.children.length > 0){
       for(let item of list.children){
         item.parentId = list.sequenceNbr;//防止有误
         //判断是否新增的
        //  if(item.whetherNew){
          await this.newReCreateTreeProject(item,constructId,iAddSingleMap,iAddUnitMap);
        //  }
       }
     }
     return ;
 }


  async reCreateTreeProject(list,curTreeList,constructId,iAddSingleMap,iAddUnitMap){
     if(list.type !== ProjectTypeConstants.PROJECT_TYPE_PROJECT){
        let treeItem = curTreeList.find(fItem=>fItem.sequenceNbr === list.sequenceNbr);
        if(!treeItem){
          let newSequenceNbr = Snowflake.nextId();
          //新增的id存起来
          if(list.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE){
            iAddSingleMap.set(list.sequenceNbr,newSequenceNbr);
          } else{
            iAddUnitMap.set(list.sequenceNbr,newSequenceNbr);
          }
          let parentId = list.parentId;
          if(parentId != constructId){
            for(const[sKey,sValue] of iAddSingleMap){
              if(parentId === sKey){
                parentId = sValue;
              }
            }
          }
          //重新维护树结构
          let newProject =  new ProjectModel(newSequenceNbr,list.type,parentId);
          //忽略children[]
          for (const valueKey in list) {
            if(valueKey !== 'children' && valueKey !== 'sequenceNbr' && valueKey !== 'parentId' && valueKey !== 'constructId'){
              newProject[valueKey]= list[valueKey];
            }
          }
          let projectDomain = ProjectDomain.getDomain(constructId)
          await projectDomain.createProject(newProject);
        }
      }
      if(list.children && list.children.length > 0){
        for(let item of list.children){
          item.parentId = list.sequenceNbr;//防止有误
          await this.reCreateTreeProject(item,curTreeList,constructId,iAddSingleMap,iAddUnitMap);
        }
      }
      return ;
  }

    /**
     * 拖拽项目结构(调整位置、调整顺序、批量复制、批量删除)
     */
    async gsDragDropProjectStructure(arg) {
        await this.service.PreliminaryEstimate.gsProjectService.gsDragDropProjectStructure(arg);
        return ResponseData.success(true);
    }

    async batchModifyName(arg) {
        return await this.service.PreliminaryEstimate.gsProjectService.batchModifyName(arg);
    }

}
GsProjectController.toString = () => 'GsProjectController';
module.exports = GsProjectController;

function main()
{
  let c = new GsProjectController({});
  let deC = new GsDeController({});
  let construct = new ProjectModel(Snowflake.nextId(),ProjectTypeConstants.PROJECT_TYPE_PROJECT,null);//顶级工程项目
  construct.constructName = "ConstructProject";
  construct.code = "00001";
  construct.biddingType = "xxxxxxxx";
  let response = c.createProject({"ProjectModel":construct});
  let { result } = response
  construct = result;
  let single = new SingleProjectModel(null,ProjectTypeConstants.PROJECT_TYPE_SINGLE,null);//单项
  single.name = "singleProject";
  single.code = "00001-1";
  single.parentId = result.sequenceNbr;
  single.biddingType = "xxxxxxxx-1";
  response = c.createProject({"ProjectModel":single,"constructId": construct.sequenceNbr});
  let { result:resultSingle } = response
  single = resultSingle;

  single = c.createProject({"ProjectModel":single,"constructId": construct.sequenceNbr});

  let unit = new UnitProjectModel(null,ProjectTypeConstants.PROJECT_TYPE_UNIT,null);//单位
  unit.name = "unitProject";
  unit.code = "00001-1-1";
  unit.parentId = single.sequenceNbr;
  unit.biddingType = "xxxxxxxx-1";

  //rootId 就是顶级工程项目id,如果本身就是顶级工程项目,则rootid 为null

  response = c.createProject({"ProjectModel":unit,"constructId": construct.sequenceNbr});
  let { result:resultUnit} = response;
  unit = resultUnit;

  unit = c.createProject({"ProjectModel":unit,"constructId": construct.sequenceNbr});

  console.log(c.getProjectTree({"pid":construct.sequenceNbr}));

  let defaultDe = deC.initDefaultDE({"constructId":construct.sequenceNbr,"unitId":unit.sequenceNbr});

  let fb = new FBFXModel(construct.sequenceNbr,unit.sequenceNbr,Snowflake.nextId(),defaultDe.sequenceNbr,DeTypeConstants.DE_TYPE_DEFAULT_FB);//分部
  fb.name = "分部1";
  let qd = new DeListModel(construct.sequenceNbr,unit.sequenceNbr,Snowflake.nextId(),fb.sequenceNbr,DeTypeConstants.DE_TYPE_DEFAULT_DeList);//清单 或者 概算定额
  qd.name = "清单1-1";
  let de = new DeModel(construct.sequenceNbr,unit.sequenceNbr,Snowflake.nextId(),qd.sequenceNbr,DeTypeConstants.DE_TYPE_DEFAULT_DE);//定额
  de.name = "定额1-1-1";
  let rcj = new ResourceModel(construct.sequenceNbr,unit.sequenceNbr,Snowflake.nextId(),qd.sequenceNbr,DeTypeConstants.DE_TYPE_DEFAULT_RESOURCE);//人材机 和 定额都挂在同一个清单下
  rcj.name = "人材机1-1-2";
  //console.log(c.getProjectDetailById({"pid":parent.sequenceNbr}));
  deC.createDe({"de":fb});
  deC.createDe({"de":qd});
  deC.createDe({"de":de});
  deC.createDe({"de":rcj});

  let projectDomain = AppContext.getContext(construct.sequenceNbr);
  FileOperator.getOperator(FileOperatorType.File_TYPE_YGS).save(projectDomain,"");


  //console.log(deC.getDeTree4Unit({"constructId":construct.sequenceNbr,"unitId":unit.sequenceNbr}));
}
// main();


/**
 * [
 *  {"sequenceNbr":"7184108641501315072","type":1,"parentId":null,"displaySign":0,"name":"ConstructProject","code":"00001"},
 *  {"sequenceNbr":"7184108641501315073","type":2,"parentId":"7184108641501315072","displaySign":0,"name":"singleProject","code":"00001-1"},
 *  {"sequenceNbr":"7184108641501315074","type":3,"parentId":"7184108641501315073","displaySign":0,"name":"unitProject","code":"00001-1-1"}
 * ]
 * [
 *  {"constructId":"7184108641501315072","unitId":"7184108641501315074","sequenceNbr":0,"type":null,"parentId":"0","displaySign":0,"name":"整个项目"},
 *  {"constructId":"7184108641501315072","unitId":"7184108641501315074","sequenceNbr":"7184108641513897984","type":"01","parentId":0,"displaySign":0,"name":"分部1"},
 *  {"constructId":"7184108641501315072","unitId":"7184108641501315074","sequenceNbr":"7184108641513897985","type":"03","parentId":"7184108641513897984","displaySign":0,"name":"清单1-1"},
 *  {"constructId":"7184108641501315072","unitId":"7184108641501315074","sequenceNbr":"7184108641513897986","type":"04","parentId":"7184108641513897985","displaySign":0,"name":"定额1-1-1"},
 *  {"constructId":"7184108641501315072","unitId":"7184108641501315074","sequenceNbr":"7184108641513897987","type":"05 ","parentId":"7184108641513897985","displaySign":0,"name":"人材机1-1-2"}
 * ]
 *
 */