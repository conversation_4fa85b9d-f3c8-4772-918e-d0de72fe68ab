const Kind3RuleHandler = require("./Kind3RuleHandler");
const Kind3TypeCMathHandler = require("../math_item_handler/Kind3TypeCMathHandler");
const ConstantUtil = require("../../enums/ConstantUtil");
const { NumberUtil } = require("../../utils/NumberUtil");
const {Snowflake} = require("../../utils/Snowflake");
const {ObjectUtil} = require("../../../../common/ObjectUtil");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {StandardConvertMod} = require("../../enums/ConversionSourceEnum");
const ProjectDomain = require("../../domains/ProjectDomain");
const WildcardMap = require("../../core/container/WildcardMap");

class GsKind3TypeCRuleHandler extends Kind3RuleHandler {
    constructor(strategyCtx, rule)  {
        super(strategyCtx, rule);
        this.formatMath = strategyCtx.conversionService.mathFormat(rule.math, rule);
    }

    async initEffectRCJ(){
        let rule = this.rule;
        this.relationDe = await this.getRelationDe(rule.libraryCode, rule.relationDeId);
        let relationDe = this.relationDe;
        if (ObjectUtil.isEmpty(relationDe)) {
            return;
        }
        let deRcjRelationList = null;
        deRcjRelationList = await this.ctx.service.PreliminaryEstimate.gsBaseDeService.getDeRcjRelationByDeId(relationDe.sequenceNbr);

        let rcjIdList = deRcjRelationList.map(i => i.rcjId);
        //获取父级材料数据
        let deRcjs = await this.ctx.service.PreliminaryEstimate.gsBaseRcjService.getRcjListByRcjIdList(rcjIdList);

        let deRcjMapById = deRcjs?.reduce((map, element) => {
            map.set(element.sequenceNbr, element);
            return map;
        }, new Map());

        return deRcjRelationList.map((r) => {
            let rcj = deRcjMapById?.get(r.rcjId);
            return {
                resQty: r.resQty,
                libraryCode: r.libraryCode,
                materialCode: r.materialCode,
                kind: r.kind,
                unit: rcj?.unit
            }
        });
    }


    async addDeByRule(){
        let {
            constructId,
            unitId,
            singleId,
            de,
            unitProject,
            deBeLong,
            deLine,
            deUpDateObj,
            constructProjectRcjs,
        } = this.ctx;

        let rule = this.rule;
        let ruleMath = this.formatMath;
        let newMath = ruleMath;
        let addDeNumber = 0;
        if("+-*/".includes(ruleMath.charAt(0))){
            let druleMath = ruleMath.split('=')[1];
            addDeNumber = NumberUtil.numberScale(eval(druleMath), 6);
            newMath = ruleMath.charAt(0) + addDeNumber;
        }else{
            addDeNumber = NumberUtil.numberScale(eval(ruleMath), 6);
            newMath = "" + addDeNumber;
        }

        // 没有新增定额直接退出
        if(ObjectUtil.isEmpty(this.rule.relationDeCode)){
            return;
        }
        // 获取相关定额 子集定额
        let relationDe = await this.ctx.service.PreliminaryEstimate.gsBaseDeService.getDeAndRcj(rule.relationDeId);
        let subDeList = relationDe.subDeList?relationDe.subDeList:[];
        for (let subDe of subDeList) {
            let model = {
                constructId: constructId,
                unitId: unitId,
                type: '03',
                parentId: de.sequenceNbr,
            }
            let standardDeModelList = await this.ctx.service.PreliminaryEstimate.gsProjectCommonService.findDesByDeIdAndStandardDeId(constructId, unitId, de.sequenceNbr, subDe.standardDeId);
            let addedDe = standardDeModelList.filter(item => !ObjectUtil.isEmpty(item.conversionAddByRule))[0];
            if (ObjectUtil.isEmpty(addedDe)) {
                addedDe = await this.ctx.service.PreliminaryEstimate.gsDeService.createDeRowAppendBaseDe(model, subDe.standardDeId);
            }
            addedDe.conversionAddByRule = {
                sequenceNbr: Snowflake.nextId(),
                type: "",
                kind: "0",
                math: newMath,
                relation: newMath,
                defaultValue: 1,
                selectedRule: addDeNumber,
                index: -1,
                libraryCode: rule.libraryCode,
                ruleInfo: newMath,
                selected: true,
            };
            // 修改定额消耗量
            addedDe.resQty = subDe.resQty
            // 修改定额单价
            // let oldPrice = addedDe.price;
            // addedDe.price = NumberUtil.accMul(addedDe.price, addDeNumber);
            // 对其父级进行调差
            // let parentDeRow = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(addedDe.parentId);
            // let parentDePrice = NumberUtil.accAdd(parentDeRow.price, NumberUtil.accMul(oldPrice, addDeNumber - 1))
            // await this.ctx.service.PreliminaryEstimate.gsDeService.updatePrice(constructId, unitId, parentDeRow.sequenceNbr, parentDePrice, true);
            let ruleDeIdObj = {
                deId: addedDe.sequenceNbr,
                ruleId: rule.sequenceNbr
            }
            if(de.addByRuleDeIds) {
                de.addByRuleDeIds.push(ruleDeIdObj);
            }else{
                de.addByRuleDeIds = [ruleDeIdObj];
            }
            deUpDateObj.realAddedDes ? deUpDateObj.realAddedDes.push(addedDe) : deUpDateObj.realAddedDes = [addedDe]
        }
        deUpDateObj.addedDes.push(relationDe);
    }

    /**
     * 修改人材机
     * @returns {Promise<void>}
     */
    async updateRcj() {
        let {
            constructId,
            unitId,
            de,
            deUpDateObj,
            constructProjectRcjs,
        } = this.ctx;
        let addedDes = deUpDateObj.realAddedDes;
        // 更新人材机
        constructProjectRcjs = await ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        for (let addedDe of addedDes) {
            // 人材机明细消耗量 * 系数
            let deRcjs = constructProjectRcjs.filter((v) => v.deRowId === addedDe.sequenceNbr);
            for (let rcj of deRcjs) {
                rcj.deId = rcj.deRowId;
                rcj.initResQty = ObjectUtil.isEmpty(rcj.initResQty)? rcj.resQty : rcj.initResQty;
                rcj.resQty = NumberUtil.accMul(rcj.initResQty, addedDe.conversionAddByRule.selectedRule);
                // if (rcj.deRowId === this.ctx.deLine.sequenceNbr) {
                //     rcj.conversionResQty = rcj.resQty
                // }
            }
        }
    }

    /**
     * 逐条执行换算规则
     */
    async execute(){
        await this.prepare();
        await this.addDeByRule();
        await this.updateRcj();
        this.after();
    }

    analysisRule(){
        let formulaStandard = this.formulaStandardizationConversion(this.rule.math);
        return [new Kind3TypeCMathHandler(this, formulaStandard)];
    }

    deCodeUpdateInfo() {
        let mathAfterCalcu = this.mathAfterCalculation(this.rule.mathHandlers[0].formatMath)
        let addedDes = this.ctx.deUpDateObj.addedDes
        let redSubArray = [];
        for (let addedDe of addedDes) {
            redSubArray.push(`${addedDe.deCode}${mathAfterCalcu}`);
        }
        return {redStr: "[" + redSubArray.join(",") + "]", blackStr: null}
    }

    deNameUpdateInfo(rule) {
        return `${this.rule.relation}:${this.rule.selectedRule}`;
    }
}
module.exports = GsKind3TypeCRuleHandler;
