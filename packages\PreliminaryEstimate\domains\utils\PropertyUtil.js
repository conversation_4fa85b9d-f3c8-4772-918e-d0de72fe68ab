//const { textSpanIntersectsWithTextSpan } = require('typescript');
const { ObjectUtil } = require('../../../../common/ObjectUtil');
const {Snowflake} = require("../../utils/Snowflake");
const FunctionTypeConstants = require("../../constants/FunctionTypeConstants");


class PropertyUtil {


  static mapToJson(map) {
    // 创建一个空对象来保存转换后的数据
    const obj = {};
    // 遍历 Map 的每个键值对
    for (const [key, value] of map) {
      // 如果值是另一个 Map，则递归调用 mapToJson
      if (value instanceof Map) {
        obj[key] = PropertyUtil.mapToJson(value);
      } else {
        // 如果值不是 Map，则直接添加到对象中
        // 注意：处理 null、undefined 或其他不能直接转换为 JSON 的值
        obj[key] = value !== undefined ? value : null; // 将 undefined 替换为 null
      }
    }
    // 返回转换后的对象
    return obj;
  }

  static shallowCopyAndFilterProperties(array, propertiesToFilter) {
    // 创建一个新的数组来存储结果
    const copiedArray = [];

    // 遍历原始数组
    for (let i = 0; i < array.length; i++) {
      // 创建一个新的对象来存储过滤后的属性
      const newObject = {};

      // 遍历原始对象的所有属性
      for (const key in array[i]) {
        // 如果属性不在要过滤的属性列表中，则复制到新对象
        if (!propertiesToFilter.includes(key)) {
          newObject[key] = array[i][key];
        }
      }
      // 将新对象添加到结果数组中
      copiedArray.push(newObject);
    }
    // 返回结果数组
    return copiedArray;
  }


  static filterObjectProperties(obj, blacklist) {
    const filteredObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && !blacklist.includes(key)) {
        // 如果是对象或数组，递归调用 filterObjectProperties
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          // 如果是数组，则递归过滤数组的每个元素
          if (Array.isArray(obj[key])) {
            filteredObj[key] = obj[key].map(item => {
              // 如果数组元素也是对象或数组，递归处理
              return typeof item === 'object' && item !== null ? PropertyUtil.filterObjectProperties(item, blacklist) : item;
            });
          } else {
            // 如果不是数组，则是对象，递归过滤对象的属性
            filteredObj[key] = PropertyUtil.filterObjectProperties(obj[key], blacklist);
          }
        } else {
          // 如果不是对象或数组，则直接赋值
          filteredObj[key] = obj[key];
        }
      }
    }
    return filteredObj;
  }

  static copyProperties(source, target, avoidProperties) {
    for (let key in source) {
      if (!ObjectUtil.isEmpty(avoidProperties)) {
        if (!avoidProperties.includes(key)) {
          target[key] = source[key];
        }
      } else {
        target[key] = source[key];
      }
    }
  }

  static objectToMapRecursive(obj) {
    // 创建一个新的Map对象
    const map = new Map();

    // 递归辅助函数，用于遍历对象属性
    function traverseObject(obj, parentKey = '') {
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          const fullPath = parentKey ? `${parentKey}.${key}` : key;
          const value = obj[key];

          // 如果是对象但不是数组、函数等，则递归处理
          if (value !== null && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Map)) {
            map.set(fullPath, PropertyUtil.objectToMapRecursive(value)); // 递归转换为Map
          }
          // 如果是数组，则递归处理数组中的每个对象
          else if (Array.isArray(value)) {
            const arrayMap = []; // 使用数组来保存Map，因为Map的键必须是唯一的
            for (let i = 0; i < value.length; i++) {
              if (value[i] !== null && typeof value[i] === 'object' && !(value[i] instanceof Map)) {
                arrayMap.push(PropertyUtil.objectToMapRecursive(value[i])); // 递归转换为Map
              } else {
                arrayMap.push(value[i]); // 非对象直接添加到数组
              }
            }
            map.set(fullPath, arrayMap); // 将处理后的数组作为值添加到Map中
          }
          // 对于非对象和数组的值，直接添加到Map中
          else {
            map.set(fullPath, value);
          }
        }
      }
    }

    // 开始遍历对象
    traverseObject(obj);

    return map;
  }

  static newObjectToMapRecursive(obj, ignoreFiledArr = []) {

    //mapKeyLevel 定义key遍历层级,,value为数组，，数组索引为层级，一般为3层，
    let mapKeyLevel = new Map();
    mapKeyLevel.set(FunctionTypeConstants.PROJECT_QFB,[0,1,0])//0不转为map，1转map,

    const map = new Map();
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (ignoreFiledArr.includes(key)) {
          map.set(key, obj[key]);
        } else {
          let value = [1, 0, 0];//默认
          for (const [mKey, mValue] of mapKeyLevel) {
            if (key.startsWith(mKey)) {
              value = mValue;
            }
          }
          map.set(key, object2Maprecursive(obj[key], 0, value)); // 递归调用以处理嵌套对象
        }
      }
    }
    return map;

    function object2Maprecursive(obj,level,value){
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }
      //数组越界默认转map
      if(level>=value.length || value[level] == 1){
        const map = new Map();
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            map.set(key, object2Maprecursive(obj[key],level + 1,value)); // 递归调用以处理嵌套对象
          }
        }
        return map;
      }else if (Array.isArray(obj)) {
        for (let item in obj) {
          object2Maprecursive(item,level + 1,value)
        }
        return obj;
      }else{

        //处理对象中的对象
        for (let key in obj) {
          if(obj.hasOwnProperty(key) &&  typeof obj[key] === 'object'){
            obj[key] = object2Maprecursive(obj[key],level + 1,value)
          }
        }
        return obj;
      }
    }
  }

  static cloneNode(node) {
    return JSON.parse(JSON.stringify(node));
  }

  static replaceIdsInTree(node, generatedIds = new Set()) {
    const newNode = PropertyUtil.cloneNode(node);
    newNode.id = Snowflake.nextId();

    if (generatedIds.has(newNode.id)) {
      throw new Error('Generated ID already exists. Ensure IDs are unique.');
    }
    generatedIds.add(newNode.id);

    if (newNode.children) {
      newNode.children = newNode.children.map(child =>
        PropertyUtil.replaceIdsInTree(child, generatedIds)
      );
    }
    return newNode;
  }
  static groupBy(arr, key) {
    return arr.reduce((groups, item) => {
      // 检查key属性是否存在且不为null或undefined
      if (item.hasOwnProperty(key) && ObjectUtil.isNotEmpty(item[key])) {
        // 获取用于分组的属性值
        const groupKey = item[key];

        // 初始化该分组键对应的数组（如果尚未存在）
        if (!groups.has(groupKey)) {
          groups.set(groupKey, []);
        }
        // 将当前对象添加到对应的分组中
        groups.get(groupKey).push(item);
      } else {
        console.warn(`Element with index ${arr.indexOf(item)} has an invalid key: ${key}`);
      }

      return groups;
    }, new Map());
  }
  static uniqueByProperty(arr, prop) {
    const uniqueMap = arr.reduce((unique, obj) => {
      const value = obj[prop];
      if (!unique.has(value)) {
        unique.set(value, obj);
      }
      return unique;
    }, new Map());

    return Array.from(uniqueMap.values());
  }
}
PropertyUtil.toString = () => 'PropertyUtil';
module.exports = PropertyUtil;