const BaseDomain = require('../../../domains/core/BaseDomain');
const PropertyUtil  = require('../../../domains/utils/PropertyUtil');
const path = require('node:path');
const fsp = require('fs').promises;
const fs = require('fs');
const ProjectDomain = require('../../../domains/ProjectDomain');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const { Snowflake } = require('../../../utils/Snowflake');
const { replacer } = require('mathjs');
const CommonConstants = require("../../../constants/CommonConstants");
const LogUtil = require('../logUtil');
const EE = require("../../../../../core/ee");
const {PricingFileFindUtils} = require("../../../../../electron/utils/PricingFileFindUtils");
const JSZip = require("jszip");
const {CryptoUtils} = require("../../../../../electron/utils/CrypUtils");



class YGSOperator{

  static directory = "";
  static fileEXT = ".ygs";
  static fileSavingSet = new Set();//暂时处理，用于保存正在保存的文件

  static async saveByFullPath(projectDomain,savePath)
  {
    let {service} = EE.app;
    let jsonObj = ObjectUtils.stringifyComplexObject(YGSOperator.prepareContent(projectDomain));
    jsonObj.path = savePath;
    jsonObj.biddingType = CommonConstants.GSFILE_BIDDINGTYPE;
    // return await service.ysfHandlerService.creatYsfFile(jsonObj);
    return await YGSOperator.writeFileWithPath1(jsonObj);

    // return await YGSOperator.writeFileWithPath(ObjectUtils.toJsonString(jsonObj),savePath);
  }

  static async save(projectDomain,savePath)
  {
    let rootProject = projectDomain.getRoot();
    //为了打开文件，需要将路径补全
    let prePath = YGSOperator.getFilePath(rootProject.name,savePath);
    rootProject.path = prePath;
    rootProject.biddingType = CommonConstants.GSFILE_BIDDINGTYPE;
    let jsonObj = ObjectUtils.stringifyComplexObject(YGSOperator.prepareContent(projectDomain));
    jsonObj.path = savePath;
    jsonObj.biddingType = CommonConstants.GSFILE_BIDDINGTYPE;
    // return await service.ysfHandlerService.creatYsfFile(jsonObj);
    return await YGSOperator.writeFileWithPath1(jsonObj);

    // return await YGSOperator.writeFile(rootProject.name,ObjectUtils.toJsonString(jsonObj),savePath);
  }
  /**
   * 准备写入内容
   * @param projectDomain
   * @param sectionsArray
   */
  static prepareContent(projectDomain,sectionsArray = [])
  {
    let content = new Map();

      if(ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_PROJECT))
      {
        content.set(ProjectDomain.KEY_PROJECT_TREE,projectDomain.ctx.treeProject.getAllNodes().map(item => PropertyUtil.filterObjectProperties(item, BaseDomain.avoidProperty)));
      }
      if(ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_DE))
      {
        content.set(ProjectDomain.KEY_DE_TREE,projectDomain.ctx.deMap.getAllNodes().map(item => PropertyUtil.filterObjectProperties(item, BaseDomain.avoidProperty))) ;
      }
      if(ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_RESOURCE))
      {
        content.set(ProjectDomain.KEY_RESOURCES,projectDomain.ctx.resourceMap.asArray().map(item => PropertyUtil.filterObjectProperties(item[1], BaseDomain.avoidProperty))) ;
      }
      if(ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_FUNCTION_DATA))
      {
        content.set(ProjectDomain.KEY_FUNCTION_DATA_MAP,(PropertyUtil.mapToJson(projectDomain.functionDataMap)));
      }



    return content;
  }

  /**
   *
   * @param fileName
   * @param savePath
   * @returns {string}
   */
  static getFilePath(fileName,savePath)
  {
    const filePath = path.join( savePath,fileName + YGSOperator.fileEXT);
    const cwd = process.cwd();
    return path.resolve(cwd, filePath);
  }
  /**
   * 写入文件
   */
   static async writeFile(fileName,content,savePath)
  {
    let absolutePath = YGSOperator.getFilePath(fileName,savePath);
    await YGSOperator.writeFileWithPath(content,absolutePath);
    return absolutePath;
  }
  static async writeFileWithPath(content,absolutePath)
  {
    const writeStream = fs.createWriteStream(absolutePath, { encoding: 'utf8' });
    if(YGSOperator.fileSavingSet.has(absolutePath)){
      // console.error("文件正在保存中，请稍后再试");
      //直接放弃本地保存
      return absolutePath;
    }
    YGSOperator.fileSavingSet.add(absolutePath);
    // 这里假设dataSource是一个数组或可迭代对象，每个元素是要写入的一行文本
    //writeStream.write(`概算文件:\n`);
    // for (const line of content) {
      // writeStream.write(`${line}`); // 写入一行文本，并添加换行符
    // }
    writeStream.write(content);
    // 监听'finish'事件，当所有数据都被写入底层资源时触发
    return new Promise((resolve,reject)=>{
      writeStream.on('finish', () => {
        YGSOperator.fileSavingSet.delete(absolutePath);
        console.log(`文件写入${absolutePath}完成`);
        resolve(absolutePath);
      });
  
      // 监听'error'事件，处理写入过程中可能出现的错误
      writeStream.on('error', (err) => {
        YGSOperator.fileSavingSet.delete(absolutePath);
        console.error('写入文件时发生错误:', err);
        reject(err);
      });
  
      // 结束写入流（如果数据源不是流，这一步是必要的）
      writeStream.end();
    });
    
  }


  static async writeFileWithPath1(obj) {
    if (!obj instanceof ProjectDomain) {
      throw new Error("参数有误");
    }
    obj.fileCode = Snowflake.nextId();
    let data = await this.toJsonYsfString(obj);
    data = CryptoUtils.encryptAESData(data);
    // 创建一个新的压缩包实例
    const zip = new JSZip();
    // 添加JSON数据到压缩包中
    zip.file('file.json', data);
    // 生成压缩包
    await zip.generateAsync({type: 'nodebuffer'}).then(function (content) {
      // 将压缩包数据写入磁盘并将后缀名改为ysf
      fs.writeFileSync(obj.path, content);
    }).catch(function (error) {
      console.error('创建压缩包时发生错误:', error);
    });

    // 在最后一步更新文件hash值  放在前面可能会引起hash之后的文件被修改
    let projectDomain = ProjectDomain.getDomain(obj.ProjectTree[0].sequenceNbr);
    let data1 = await this.toJsonYsfString(ObjectUtils.stringifyComplexObject(YGSOperator.prepareContent(projectDomain)));// ObjectUtils.toJsonString(obj);
    CryptoUtils.objectHash(data1, obj.ProjectTree[0].sequenceNbr, true);

    return obj;
  }

  static async toJsonYsfString(obj) {
    return JSON.stringify(obj, (key, value) => {
      return typeof value === 'undefined' ? null : value;
    });
  }


  /**
   *
   * @param filePath
   */
  static async openFile(filePath, changeConstructId = false) {
    let projectDomain;

    try {
      if(YGSOperator.fileSavingSet.has(filePath)){
        console.log("文件正在保存中，请稍后再试");
        return projectDomain;
      }
      // 使用 fs.promises.readFile 读取文件内容
      // let data = await fsp.readFile(filePath, 'utf8');
      let data1 = await PricingFileFindUtils.getProjectObjByPath(filePath);
      let data = JSON.stringify(data1);

      if(changeConstructId){
        //  获取项目id
        let oldConstructId = await YGSOperator.getFirstConstructId(data);
         // 随机生成一个sequence替换constructid,避免重复
        let newConstructId = Snowflake.nextId();
        // 替换constructid
        console.log("----old:"+ oldConstructId+",new:" + newConstructId + "----")
        const regex = new RegExp(oldConstructId, 'g');
        data = data.replace(regex, newConstructId);
      }
      // 尝试解析 JSON 数据
      const parsedData = JSON.parse(data);

      // 调用另一个异步函数并等待其完成
      projectDomain = await YGSOperator.destructuringFile(parsedData);

    } catch (error) {
      if (error.code === 'ENOENT') {
        console.error('文件不存在:', filePath);
      } else if (error.name === 'SyntaxError') {
        console.error('解析 JSON 数据时出错:', error);
      } else {
        console.error('读取文件或解析 JSON 时出错:', error);
      }
      // 如果发生错误，您可能希望抛出错误或返回 null/undefined
      // throw error; // 或者返回 null/undefined，取决于您的错误处理策略
    }
    // 返回 projectDomain 的值
    return projectDomain;
  }
  /**
   * 获取项目ID
   * @param {*} jsonString 
   * @returns 
   */
  static async getFirstConstructId(jsonString){
    const parsedData = JSON.parse(jsonString);
    return await ProjectDomain.getImportProjectId(parsedData);
  }

  /**
     * 获取项目ID
     * @param {*} jsonString
     * @returns
     */
    static async getConstructRoot(jsonString) {
        const parsedData = JSON.parse(jsonString);
        return await ProjectDomain.getImportProjectRoot(parsedData);
    }

  /**
   *
   * @param parsedData
   * @param parsedData
   */
  static async destructuringFile(parsedData)
  {
    return await ProjectDomain.importProject(parsedData);
  }

}
module.exports = YGSOperator;