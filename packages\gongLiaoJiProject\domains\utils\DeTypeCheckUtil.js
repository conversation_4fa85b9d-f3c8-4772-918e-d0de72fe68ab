const { ObjectUtils } = require('../../utils/ObjectUtils');
const DeTypeConstants = require("../../constants/DeTypeConstants");
const CommonConstants = require("../../constants/CommonConstants");
const WildcardMap = require('../../core/container/WildcardMap');
const EE = require('../../../../core/ee');
const CostDeMatchConstants = require('../../constants/CostDeMatchConstants');
const { IFDONORMATERIAL } = require('../../constants/RcjCommonConstants');

class DeTypeCheckUtil {
  async checkAllDeType(deRow,ctx,isConversion = false){
    //特殊处理标准换算的的数据
    if(isConversion){
      await this._resetInitChildCodes(deRow,ctx);
    }
    this._updateChildDeType(deRow,ctx);
    this.updateParentDeType(deRow,ctx);
  }

  async _resetInitChildCodes(deRow,ctx){
    let rcjDeKey = WildcardMap.generateKey(deRow.unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs =  ctx.resourceMap.getValues(rcjDeKey);
    if(ObjectUtils.isEmpty(rcjs)){
      return;
    }
    let initChildCodes = deRow.initChildCodes;
    for(let rcj of  rcjs){
      if(ObjectUtils.isNotEmpty(rcj.changeResQtyRuleIds)){
        //存在
        initChildCodes.forEach(iItem=>{
          if(this._fixCode(rcj.materialCode) === this._fixCode(iItem.code)){
            iItem.sequenceNbr = rcj.sequenceNbr;
          }
        })

      }
      if(ObjectUtils.isNotEmpty(rcj.editFromConversion)){
        initChildCodes.forEach(iItem=>{
          if(this._fixCode(rcj.editFromConversion.fromRCJCode) === this._fixCode(iItem.code)){
            iItem.sequenceNbr = rcj.sequenceNbr;
          }
        })
      }
      //原始编码
      if(rcj.originalQty>0 && ObjectUtils.isEmpty(rcj.changeResQtyRuleIds) && ObjectUtils.isEmpty(rcj.editFromConversion)){
        initChildCodes.forEach(iItem=>{
          if(this._fixCode(rcj.materialCode) === this._fixCode(iItem.code)){
            iItem.sequenceNbr = rcj.sequenceNbr;
          }
        })
      }
    }
    // let {service} = EE.app;
    // let conversionInfo = await service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvertList(deRow.constructId, deRow.unitId, deRow.sequenceNbr);
    // console.log(conversionInfo)
    //为了测试
  }

  _updateChildDeType(deRow,ctx){
    
    if(ObjectUtils.isNotEmpty(deRow.children)){
      for(let child of deRow?.children){
        this.checkAndUpdateDeType(child,ctx, false);
        this._updateChildDeType(child,ctx);
      }
    }
    
    this.checkAndUpdateDeType(deRow,ctx, false);
  }
  /**
   * 
   * @param {*} deRow 
   * @param {*} ctx 
   * @param {*} isTraceTop 
   * @param {*} deRange   'all' 所有定额
   * @returns 
   */
  checkAndUpdateDeType(deRow, ctx, isTraceTop=true, deRange){
      let typeArr = []
      //定额，装饰及安文费
      
    let changeLibrary = false;
    
    let unitProject =  ctx.treeProject.getNodeById(deRow.unitId);
    changeLibrary = unitProject.constructMajorType !== deRow.libraryCode;

      if(DeTypeConstants.DE_TYPE_DE == deRow.type
         && (ObjectUtils.isEmpty(deRow.isCostDe)
         || deRow.isCostDe == CostDeMatchConstants.NON_COST_DE || deRow.isCostDe == CostDeMatchConstants.DS_CY_DE 
         || deRow.isCostDe == CostDeMatchConstants.DX_CY_DE ||  deRow.isCostDe == CostDeMatchConstants.FXTJ_CZYS
         || deRow.isCostDe == CostDeMatchConstants.FXTJ_ZXXJX ||  deRow.isCostDe == CostDeMatchConstants.FXTJ_GCSDF
         || deRow.isCostDe == CostDeMatchConstants.GJMQ_DE_CZYS || deRow.isCostDe == CostDeMatchConstants.GJMQ_DE_ZXXJX ||  deRow.isCostDe == CostDeMatchConstants.FGJZ_DE_ZXXJX ) ){
        typeArr = this._dealWithDe04(deRow,ctx,changeLibrary);
      }
      if(DeTypeConstants.DE_TYPE_DE == deRow.type 
        && (deRow.isCostDe == CostDeMatchConstants.AWF_DE || deRow.isCostDe == CostDeMatchConstants.ZJCS_DE)){
           if(changeLibrary){
              typeArr.push(DeTypeConstants.DE_TYPE_JIE_LABEL);
           }else{
              typeArr.push(DeTypeConstants.DE_TYPE_FEE_LABEL);
           }
      }
      if(DeTypeConstants.DE_TYPE_ANZHUANG_FEE === deRow.type 
        || (DeTypeConstants.DE_TYPE_DE == deRow.type && deRow.isCostDe == CostDeMatchConstants.AZ_DE)){
          if(changeLibrary){
            typeArr.push(DeTypeConstants.DE_TYPE_JIE_LABEL);
          }else{
            typeArr.push(DeTypeConstants.DE_TYPE_ANZHUANG_FEE_LABEL);
          }        
      }
      if(DeTypeConstants.DE_TYPE_ZHUANSHI_FEE === deRow.type
        || (DeTypeConstants.DE_TYPE_DE == deRow.type && [CostDeMatchConstants.CG_DE, CostDeMatchConstants.FXTJ_CG,CostDeMatchConstants.GJMQ_DE_CG].includes(deRow.isCostDe))
      ){
        if(changeLibrary){
          typeArr.push(DeTypeConstants.DE_TYPE_JIE_LABEL);
        }else{
          typeArr.push(DeTypeConstants.DE_TYPE_ZHUANSHI_FEE_LABEL);
        }
      }
      if(DeTypeConstants.DE_TYPE_DELIST === deRow.type){
        typeArr = this._dealWithDe03(deRow,ctx);
      }
      //其他类型不管了，如果要管需要考虑debasedomain 的checkAndQuery方法  
      deRow.displayType = typeArr.join(' ');

      if(isTraceTop){
          this.updateParentDeType(deRow,ctx,deRange);
      }
      
  }
  /**
   * 处理04类型数据
   * @param {*} deRow 
   * @param {*} ctx 
   * @returns 
   */
  _dealWithDe04(deRow, ctx,changeLibrary){
        
    //1.当前单位与专业是否一致   定/借
    //2.检查人材机有误删减       调
    //3.检查原始人材机是否有消耗量一致   换
    let typeArr = []
    

    //人材机消耗量变化 '调'
    let changeResQty = false;
    //只有替换替换人材机才是“换”   and    类型变更
    let isReplaceRcj = false;
    //人材机删减判断 ‘调’
    let isaddRcj = false;
    //标准换算  ‘换’
    let isConversion = false;
    let rcjKey = WildcardMap.generateKey(deRow.unitId,deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs = ctx.resourceMap.getValues(rcjKey);
    let rcjAll = []
    if(ObjectUtils.isNotEmpty(rcjs)){
      for(let rcj of rcjs){
        rcjAll.push(rcj);
        if(ObjectUtils.isNotEmpty(rcj.pbs)){
          for(let pbs of rcj.pbs){
            rcjAll.push(pbs);
          }
        }
      }
    }
    if(rcjAll.length>0 && deRow.initChildCodes.length === 0){
      isaddRcj = true;
    }
    if(rcjAll.length === 0 && deRow.initChildCodes.length > 0){
      isaddRcj = true;
    }
    let specialAddRcjs = [];
    if(rcjAll.length>0 && deRow.initChildCodes.length >0){

      for(let initRcj of deRow.initChildCodes){
        let rcj = rcjAll.find(item=>item.sequenceNbr === initRcj.sequenceNbr)
        if(ObjectUtils.isEmpty(rcj)){
          //如果消耗量与编码一致  不认定为新增
          specialAddRcjs.push(initRcj);
          // isaddRcj = true;
        }else{
          if(this._fixCode(rcj.materialCode) !== this._fixCode(initRcj.code)){
            isReplaceRcj = true;//替换了人材机，编码不一致
          }
          if(ObjectUtils.isNotEmpty(rcj.editFromConversion) ||  ObjectUtils.isNotEmpty(rcj.addFromConversionStdRuleId) || ObjectUtils.isNotEmpty(rcj.changeResQtyStdIds)){
            isConversion = true;
          }
        }
      }
      if(rcjAll.length> deRow.initChildCodes.length){
        for(let rcj of rcjAll){
          let initRcj = deRow.initChildCodes.find(item=>item.sequenceNbr === rcj.sequenceNbr);
          if(ObjectUtils.isEmpty(initRcj)){
               if(ObjectUtils.isNotEmpty( rcj.addFromConversionStdRuleId) || ObjectUtils.isNotEmpty(rcj.editFromConversion) ){
                 isConversion = true;
               }else {
                 isaddRcj  = true;
               }
            }
          if(ObjectUtils.isNotEmpty(rcj.editFromConversion) ||  ObjectUtils.isNotEmpty(rcj.addFromConversionStdRuleId) || ObjectUtils.isNotEmpty(rcj.changeResQtyStdIds)){
            isConversion = true;
          }
        }
      }
    }

    //重复检验（替换、kind检查）
    for(let replace of deRow.initDeRcjNameList){
      //非原始人材机进行了替换的操作
      let originalRcj = deRow.initChildCodes.find(item=>item.sequenceNbr === replace.sequenceNbr)
      let curRcjExist = rcjAll.find(item=>item.sequenceNbr === replace.sequenceNbr && replace?.code?.replace( /#\d+/g, '') !== item.materialCode.replace( /#\d+/g, ''));
      if(ObjectUtils.isNotEmpty(curRcjExist) && ObjectUtils.isEmpty(curRcjExist.editFromConversion)){
        isReplaceRcj  = true;
      }
      // kind 处理
      let kindRcjExist = rcjAll.find(item=>item.sequenceNbr === replace.sequenceNbr && replace.kind!== item.kind);
      if(ObjectUtils.isNotEmpty(kindRcjExist)){
        isReplaceRcj  = true;
      }

    }
    //措施项目不计算消耗量
    if(deRow.isCsxmDe){

    }else{
      //人材机消耗量有不一致的情况
      if(!this._checkResQty(deRow.resQty, deRow.initResQty)) {
        changeResQty = true;
      }
    }
    for(let rcj of rcjAll){

      let resQty = rcj.resQty;
      if(rcj.isTempRemove === CommonConstants.COMMON_YES){
        resQty = rcj.changeResQty;
      }
      //如果消耗量与编码一致  不认定为新增及更改消耗量
      let specialAddRcj = specialAddRcjs.find(item=>item.code === rcj.materialCode && item.resQty === resQty);
      if(ObjectUtils.isNotEmpty(specialAddRcj)){
        specialAddRcjs = specialAddRcjs.filter(item=>item.sequenceNbr !== specialAddRcj.sequenceNbr);
        continue;
      }
      //人材机没有处理initResQty，所以用originalQty替换
      if(!this._checkResQty(resQty, rcj.originalQty)){
        if(ObjectUtils.isEmpty(rcj.editFromConversion)
          && ObjectUtils.isEmpty(rcj.addFromConversionStdRuleId)
          && (ObjectUtils.isEmpty(rcj.changeResQtyStdIds) || (ObjectUtils.isNotEmpty(rcj.changeResQtyStdIds) && ObjectUtils.isNotEmpty(rcj.changeResQtyCunsumerIds)) || rcj.consumerResQty ===rcj.resQty)
        ){
          changeResQty = true;
        }
      }
    }
    if(specialAddRcjs.length>0){
      isaddRcj = true;
    }
    
    this._dealWithType(typeArr,changeLibrary,isaddRcj,changeResQty,isReplaceRcj,isConversion);
    return typeArr;
  }

  _dealWithType(typeArr,changeLibrary,isaddRcj,changeResQty,isReplaceRcj,isConversion){
    //借定处理
    if(changeLibrary){      
      if(typeArr.length === 0){
        typeArr.push(DeTypeConstants.DE_TYPE_JIE_LABEL)
      }
      if(isaddRcj || changeResQty){
        typeArr.push('调');
      }
      if(isReplaceRcj || isConversion){
        typeArr.push(DeTypeConstants.DE_TYPE_HUAN_LABEL)
      }
    }else{
      if(isaddRcj || changeResQty){
        if(isReplaceRcj || isConversion){
          typeArr.push('调');
          typeArr.push(DeTypeConstants.DE_TYPE_HUAN_LABEL)
        }else{
            typeArr.push('调');
        }
      }else{
        if(isReplaceRcj || isConversion){
          typeArr.push(DeTypeConstants.DE_TYPE_HUAN_LABEL)
        }else{
          typeArr.push(DeTypeConstants.DE_TYPE_DE_LABEL);
        }
      }
    }
  }

  /**
   * 处理03类型数据
   * @param {*} deRow 
   * @param {*} ctx 
   * @returns 
   */
  _dealWithDe03(deRow,ctx){
    let typeArr = [];
    //与当前专业是否一致
    let unitProject =  ctx.treeProject.getNodeById(deRow.unitId);      
    let changeLibrary = unitProject.constructMajorType !== deRow.libraryCode;

    //只有替换替换人材机才是“换”
    let isReplaceRcj = false;
    // 检查是否有人材机
    let isaddRcj = false;
    //标准换算
    let isConversion = false;
    let rcjKey = WildcardMap.generateKey(deRow.unitId,deRow.sequenceNbr)+ WildcardMap.WILDCARD;
    let rcjs = ctx.resourceMap.getValues(rcjKey);
    if(ObjectUtils.isNotEmpty(rcjs)){
      rcjs = rcjs.filter(item=>item.isDeCompensation != CommonConstants.COMMON_YES);
      if(rcjs.length>0){
        isaddRcj = true; 
      }
    }
    let childs = [...deRow.children];
    //定额删减 
    let ischangeDe = false;
    if(childs.length == deRow.initChildCodes.length){
      // 检查子定额是否修改
      for(let initRcj of deRow.initChildCodes){
        let childDe = childs.find(item=>item.deCode == initRcj.code);
        if(ObjectUtils.isEmpty(childDe)){
          ischangeDe = true;
        }else{
          //子级消耗量或定额数据有变化处理
          if(!this._checkResQty(childDe.initResQty, childDe.resQty) || 
            (ObjectUtils.isNotEmpty(childDe.displayType) 
              && (childDe.displayType.indexOf('调')>-1 || childDe.displayType.indexOf('换')>-1))
          ){
            ischangeDe = true;
          }
          childs = childs.filter(item=>item.sequenceNbr !== childDe.sequenceNbr);
        }            
      }
    }else{
      ischangeDe = true;
    }
    
    this._dealWithType(typeArr,changeLibrary,isaddRcj,ischangeDe,isReplaceRcj,isConversion);
    return typeArr;
  }

  updateParentDeType(deRow,ctx,deRange){
        
    if([DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_FB].includes(deRow.type)){
      return 
    }
    //向上检查
    let parent = ctx.allDeMap.getNodeById(deRow.parentId);
    
    if(ObjectUtils.isNotEmpty(parent)){
      
      if(parent.type === DeTypeConstants.DE_TYPE_DELIST && !parent.isCsxmDe){
        
        let typeArr = this._dealWithDe03(parent,ctx);
        parent.displayType = typeArr.join(' ');
        this.updateParentDeType(parent,ctx,deRange);
      }
      if(parent.type === DeTypeConstants.DE_TYPE_DE){
        this.checkAndUpdateDeType(parent,ctx,true,deRange);
      }
    }
  }
  _checkResQty(resQty,initResQty){
    resQty = ObjectUtils.isEmpty(resQty)?0:resQty;
    initResQty = ObjectUtils.isEmpty(initResQty)?0:initResQty;
    return resQty == initResQty;
  }
  _fixCode(code){
    if(code.indexOf('#')){
      code = code.split('#')[0];
    }
    code = code.toUpperCase();
    return code;
  }
}
module.exports = {
  DeTypeCheckUtil: new DeTypeCheckUtil()
}