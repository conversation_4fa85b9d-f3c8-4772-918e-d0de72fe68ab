'use strict';

const { Service } = require('../../../../core');
const { BaseDeZsCgRelation2022 } = require('../../models/BaseDeZsCgRelation2022');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const { BaseDe2022 } = require('../../models/BaseDe2022');
const ProjectDomain = require('../../domains/ProjectDomain');
const FunctionTypeConstants = require('../../constants/FunctionTypeConstants');
const BranchProjectLevelConstant = require('../../constants/BranchProjectLevelConstant');
const CostDeMatchConstants = require('../../constants/CostDeMatchConstants');
const WildcardMap = require('../../core/container/WildcardMap');
const { NumberUtil } = require('../../../../electron/utils/NumberUtil');
const DeTypeConstants = require('../../constants/DeTypeConstants');
const { ObjectUtil } = require('../../../../common/ObjectUtil');
const FxtjCostMatchContext = require('./fxtjCostMatch/gljFxtjCostMatchContext');
const GjmqCostMatchContext = require('./gjmqCostMatch/gljGjmqCostMatchContext')
const GljFgjzZxxjxFeeMatchHandle = require('./fgjzCostMatch/gljFgjzZxxjxFeeMatchHandle');

/**
 * 自动计算各项费用记取
 * @class
 */
class GljAutoCostMatchService extends Service {

  constructor(ctx) {
    super(ctx);
    this.baseDeZsCgRelation2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDeZsCgRelation2022);
  }

  async autoCostMath(args) {
    // 垂直运输费
    await this.autoCalculateCzys(args);
    // 超高费
    await this.autoCalculateCgFee(args);
    // 泵送增加费
    await this.service.gongLiaoJiProject.gljPumpingAddFeeService.autoCalculatePumpingAddFee(args);
    // 安装费用记取
    await this.service.gongLiaoJiProject.gljAzCostMathService.autoMatchAzCost(args);
    // 房修土建费用记取
    await this.fxtjCostMatch(args);
    // 古建明清费用记取
    await this.gjmqCostMatch({constructId:args.constructId,singleId:args.singleId,unitId:args.unitId});
    //仿古的中小型机械使用费
    await new GljFgjzZxxjxFeeMatchHandle().autoCostMatch({constructId:args.constructId,singleId:args.singleId,unitId:args.unitId,feeType:CostDeMatchConstants.FGJZ_ZXXJX});
    // 总价措施记取
    await this.service.gongLiaoJiProject.gljZjcsCostMatchService.autoMatchZjcsCost(args);
    // 水电费
    await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.autoCalculateWaterElectricCost(args);
    // 费用汇总
    await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      constructId: args.constructId,
      unitId: args.unitId,
      qfMajorType: null
    });
  }

  async gjmqCostMatch(args) {
    let { constructId, singleId, unitId } = args;
    let fxtjCache = null;
    fxtjCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_GJMQ_COST_MATCH_CACHE);
    if (ObjectUtils.isNotEmpty(fxtjCache)) {
      fxtjCache = fxtjCache[unitId];
    }
    let fxtjMatchFlagArr = [CostDeMatchConstants.GJMQ_CZYS, CostDeMatchConstants.GJMQ_ZXXJX];
    if (ObjectUtils.isNotEmpty(fxtjCache) && ObjectUtils.isNotEmpty(fxtjCache.cgCache)) {
      fxtjMatchFlagArr.push(CostDeMatchConstants.GJMQ_CG);
    }
    // 依次自动记取不同的房修土建费用
    for (const fxtjMatchFlag of fxtjMatchFlagArr) {
      args.feeType = fxtjMatchFlag;
      await new GjmqCostMatchContext(fxtjMatchFlag).autoCostMatch(args);
    }
  }

  async fxtjCostMatch(args) {
    let { constructId, singleId, unitId } = args;
    let fxtjCache = null;
    fxtjCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FXTJ_COST_MATCH_CACHE);
    if (ObjectUtils.isNotEmpty(fxtjCache)) {
      fxtjCache = fxtjCache[unitId];
    }
    let fxtjMatchFlagArr = [CostDeMatchConstants.CZYS, CostDeMatchConstants.ZXXJX, CostDeMatchConstants.GCSDF];
    if (ObjectUtils.isNotEmpty(fxtjCache) && ObjectUtils.isNotEmpty(fxtjCache.cgCache)) {
      fxtjMatchFlagArr.push(CostDeMatchConstants.CG);
    }
    // 依次自动记取不同的房修土建费用
    for (const fxtjMatchFlag of fxtjMatchFlagArr) {
      args.feeType = fxtjMatchFlag;
      await new FxtjCostMatchContext(fxtjMatchFlag).autoCostMatch(args);
    }
  }

  /**
   * 自动计算垂直运输费
   */
  async autoCalculateCzys(args) {
    const { constructId, singleId, unitId } = args;
    // 所有定额
    const yssAllData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    const csxmAllData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);
    // 获取垂直运输的费用定额
    let allCostDeArr = [];
    allCostDeArr = allCostDeArr.concat(yssAllData.filter(item => item.type == BranchProjectLevelConstant.de && (item.quantityExpression == CostDeMatchConstants.DSZSGR || item.quantityExpression == CostDeMatchConstants.DXZSGR)));
    allCostDeArr = allCostDeArr.concat(csxmAllData.filter(item => item.type == BranchProjectLevelConstant.de && (item.quantityExpression == CostDeMatchConstants.DSZSGR || item.quantityExpression == CostDeMatchConstants.DXZSGR)));
    if (ObjectUtils.isEmpty(allCostDeArr)) {
      return;
    }
    // 获取所有的基数定额
    let baseDeArr = await this.service.gongLiaoJiProject.gljConstructCostMathService.getCzysBaseDeArr(yssAllData, csxmAllData);
    if (ObjectUtils.isNotEmpty(baseDeArr)) {
      baseDeArr = [...baseDeArr.yssDeList, ...baseDeArr.csxmDeList];
    } else {
      baseDeArr = [];
    }
    let dsBaseDeArr = [];
    let dxBaseDeArr = [];
    // 获取缓存
    let czysCache = await this.service.gongLiaoJiProject.gljConstructCostMathService.cyCostMathCache(args);
    // 根据缓存把基数定额分为地上、地下
    if (ObjectUtils.isNotEmpty(czysCache)) {
      const cacheDownBaseDeArr = czysCache.data.filter(item => item.type == BranchProjectLevelConstant.de && item.upOrDown == 'down');
      let dxIds = [];
      if (ObjectUtils.isNotEmpty(cacheDownBaseDeArr)) {
        dxIds = cacheDownBaseDeArr.map(item => item.deRowId);
      }
      for (const item of baseDeArr) {
        if (dxIds.includes(item.deRowId)) {
          dxBaseDeArr.push(item);
        } else {
          dsBaseDeArr.push(item);
        }
      }
    } else {
      dsBaseDeArr = dsBaseDeArr.concat(baseDeArr);
    }

    // 获取地上的工程量值
    let dsQuantityExpression = await this.service.gongLiaoJiProject.gljConstructCostMathService.getCzysQuantityExpression(dsBaseDeArr, unitId, constructId);
    // 获取地下的工程量值
    let dxQuantityExpression = await this.service.gongLiaoJiProject.gljConstructCostMathService.getCzysQuantityExpression(dxBaseDeArr, unitId, constructId);

    // 此处是把地上和地下的值存到单位工程中，然后通知定额地上和地下的值有变动   定额会自动进行联动计算
    await this.service.gongLiaoJiProject.gljConstructCostMathService.setCostValueToFunctionMap(constructId, unitId, dsQuantityExpression, CostDeMatchConstants.DSZSGR);
    await this.service.gongLiaoJiProject.gljConstructCostMathService.setCostValueToFunctionMap(constructId, unitId, dxQuantityExpression, CostDeMatchConstants.DXZSGR);
  }

  /**
   * 获取定额的人材机
   */
  getDeRcjList(deArr, unitId, constructId) {
    if (ObjectUtils.isEmpty(deArr)) {
      return [];
    }
    const deIds = deArr.map(item => item.deRowId);
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    return rcjList.filter(item => deIds.includes(item.parentId));
  }

  // -------------------------------------------------------------------------------------------------------------------


  async autoCalculateCgFee(args) {
    const { constructId, singleId, unitId } = args;
    // 所有定额
    const yssAllData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    const csxmAllData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);
    // 获取超高费的费用定额
    let allCostDeArr = [];
    allCostDeArr = allCostDeArr.concat(yssAllData.filter(item => [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ZHUANSHI_FEE].includes(item.type) && item.isCostDe == CostDeMatchConstants.CG_DE && item.quantityExpression == CostDeMatchConstants.CGRGHJ));
    allCostDeArr = allCostDeArr.concat(csxmAllData.filter(item => [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ZHUANSHI_FEE].includes(item.type) && item.isCostDe == CostDeMatchConstants.CG_DE && item.quantityExpression == CostDeMatchConstants.CGRGHJ));
    if (ObjectUtils.isEmpty(allCostDeArr)) {
      return;
    }
    let cacheData = await this.service.gongLiaoJiProject.gljConstructCostMathService.cgCostMathCache(args);
    // 缓存数据
    if (ObjectUtils.isEmpty(cacheData)) {
      return;
    }
    let { data, optionType, csxmMatchFlag } = cacheData;
    let storeyList = await this.baseDeZsCgRelation2022Dao.find({
      where: { location: 1 }
    });
    let currentBaseDe = [];
    // 获取当前超高的所有基数定额
    let cgBaseDeArr = [];
    const cgBaseDeObj = ObjectUtils.cloneDeep(await this.service.gongLiaoJiProject.gljConstructCostMathService.getCgBaseDeArr(yssAllData, csxmMatchFlag == 0 ? [] : csxmAllData));
    cgBaseDeArr = cgBaseDeArr.concat(cgBaseDeObj.yssDeList);
    cgBaseDeArr = cgBaseDeArr.concat(cgBaseDeObj.csxmDeList);
    if (ObjectUtils.isNotEmpty(cgBaseDeArr)) {
      // 找出不在缓存中的基数定额
      let newDeDataList = [];
      let oldDeDataList = [];
      cgBaseDeArr.map(itemA => {
        const oldDe = data.find(itemB => itemB.sequenceNbr === itemA.sequenceNbr);
        if (ObjectUtils.isNotEmpty(oldDe)) {
          // 原来缓存中就存在的数据
          oldDeDataList.push(oldDe);
        } else {
          // 原来缓存中不存在的数据
          newDeDataList.push(itemA);
        }
      });
      // 如果存在原来缓存中不存在的数据  需要根据父级设置新的定额的【檐高层数】和【超高记取类型】
      if (ObjectUtils.isNotEmpty(newDeDataList)) {
        for (const de of newDeDataList) {
          // 如果这个定额的父级是缓存中的清单、分部、单位工程   那么直接使用缓存中的父级的【檐高层数】和【超高记取类型】
          // 如果这个定额的父级不是缓存中的数据，那么就一级一级递归往上找  直到找到在缓存中的上级或者到了单位工程层级之后，直接使用这个上的【檐高层数】和【超高记取类型】
          await this.findCacheData(de, data, currentBaseDe, de, constructId, unitId);
        }
      }
      currentBaseDe = currentBaseDe.concat(oldDeDataList);
      if (ObjectUtils.isNotEmpty(currentBaseDe)) {
        currentBaseDe = currentBaseDe.filter(item => item.matchFlag != 0);
      }
      for (const baseDe of currentBaseDe) {
        const findObj = storeyList.find(item => item.sequenceNbr == baseDe.eavesHeight);
        baseDe.cgDeCode = findObj.cgDeCode;
      }
    }

    for (const costDe of allCostDeArr) {
      //计算基数
      let deMathBase = 0;
      // 当前费用定额对应的基数定额
      let costDeBaseDeArr = [];
      if (optionType == 1) {
        // 对应分部
        costDeBaseDeArr = currentBaseDe.filter(item => costDe.deCode == item.cgDeCode && costDe.parentId == item.parentId);
      } else {
        // 指定措施 或者 指定分部
        costDeBaseDeArr = currentBaseDe.filter(item => costDe.deCode == item.cgDeCode);
      }
      // 根据基数定额获取到当前费用定额的计算基数
      deMathBase = await this.service.gongLiaoJiProject.gljConstructCostMathService.getCgBaseValue(costDeBaseDeArr, constructId, unitId, costDe.unit);
      costDe.formula = deMathBase;
      // 根据基数定额获取到当前费用定额的工程量
      costDe.quantityExpressionNbr = deMathBase;
      const regex = /\b\d+\b/;
      let match = costDe.unit.match(regex);
      if (match) {
        const number = parseInt(match[0]);
        costDe.quantity = NumberUtil.divide(costDe.quantityExpressionNbr, number);
      } else {
        costDe.quantity = NumberUtil.divide(costDe.quantityExpressionNbr, 1);
      }
      // 设置超高费费用定额的工程量表达式的值
      await this.service.gongLiaoJiProject.gljConstructCostMathService.setCostValueToFunctionMap(constructId, unitId, costDe.quantityExpressionNbr, CostDeMatchConstants.CGRGHJ);
      const currentCostDe = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(costDe.sequenceNbr);
      if (ObjectUtils.isNotEmpty(currentCostDe)) {
        await ProjectDomain.getDomain(constructId).csxmDomain.notify(costDe, false);
      } else {
        await ProjectDomain.getDomain(constructId).deDomain.notify(costDe, false);
      }
    }
  }

  async findCacheData(de, data, baseDeList, current, constructId, unitId) {
    // 先查询current的父级在不在缓存中
    const cacheParentData = data.find(item => item.sequenceNbr == current.parentId);
    if (ObjectUtils.isNotEmpty(cacheParentData)) {
      // 在缓存中找到了父级  直接使用父级的【檐高层数】和【超高记取类型】
      de.matchFlag = cacheParentData.matchFlag;
      de.eavesHeight = cacheParentData.eavesHeight;
      baseDeList.push(de);
      return;
    }
    // 缓存中没有就去所有的里面找出这条数据  然后查他的上级
    let parentData;
    parentData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId && item.sequenceNbr == current.parentId);
    if (ObjectUtils.isEmpty(parentData)) {
      parentData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId && item.sequenceNbr == current.parentId);
    }
    if (ObjectUtils.isNotEmpty(parentData)) {
      if (ObjectUtils.isEmpty(parentData.parentId)) {
        // 说明是顶级单位工程  直接使用【檐高层数】和【超高记取类型】
        de.matchFlag = cacheParentData.matchFlag;
        de.eavesHeight = cacheParentData.eavesHeight;
        baseDeList.push(de);
        return;
      }
      this.findCacheData(de, data, baseDeList, parentData, constructId, unitId);
    }
  }

}

GljAutoCostMatchService.toString = () => '[class GljAutoCostMatchService]';
module.exports = GljAutoCostMatchService;
