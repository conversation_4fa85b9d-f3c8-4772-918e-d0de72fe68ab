const Kind4RuleHandler = require("./Kind4RuleHandler");

class Kind0RuleHandler extends Kind4RuleHandler {
    deCodeUpdateInfo() {
        let redSubArray = [];

        for (let handler of this.rule.mathHandlers) {
            redSubArray.push(`[${handler.showMath||handler.oriMath}]`);
        }

        return { redStr: redSubArray.join(","), blackStr: null};
    }

}

module.exports = Kind0RuleHandler;
