const MathItemHandler = require("./mathItemHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. HXXXX YYYY *n 对应材料编码 乘
 *    2. HXXXX YYYY n 对应材料编码 重置为 n
 */
class Txxx$yyy$VMathHandler extends MathItemHandler{

    constructor(ruleCtx, math) {
        super(ruleCtx, math);
        this.resQtyBaseRcj = null;
    }

    /**
     * 准备数据：根据不同规则准备定额、人材机、规则的取消处理等
     */
    async prepare() {
        await super.prepare();
        this.setResQtyBaseRcj();
    }

    setResQtyBaseRcj(){
        let fromRCjS = this.findActiveRCJByCode(this.mathItem.fromRCJCode);
        if(ObjectUtils.isNotEmpty(fromRCjS)){
            this.resQtyBaseRcj = fromRCjS[0];
        }
    }

    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 6;
        let mathSubArr = mathItem.math.substring(1).trim().split(/\s+/);
        mathItem.toRCJCode = mathSubArr[0];
        mathItem.toRCJLibraryCode = this.rule.libraryCode;
        mathItem.fromRCJCode = mathSubArr[1];
        mathItem.fromRCJLibraryCode = this.rule.libraryCode;
        mathItem.parseMath = mathSubArr[2];
        mathItem.operator = this.mathOperator(mathItem.parseMath);
    }

    async activeRCJ() {
        let item = this.mathItem;
        let toRCjS = this.findActiveRCJByCode(item.toRCJCode);
        if(ObjectUtils.isEmpty(toRCjS)){
            let rcj = await this.addNewRCJ(item.toRCJLibraryCode, item.toRCJCode);
            item.activeRCJs = [rcj];
        }else{
            if(item.toRCJCode != toRCjS[0].materialCode) {
                this.notStandardActiveRcjCodes.push([item.toRCJCode, toRCjS[0].materialCode]);
            }
            this.mathItem.activeRCJs.push(toRCjS[0]);
        }
    }

    async computeResQty() {
        // 1. 获取resQtyBaseRcj的消耗量，赋值给activeRcj
        let toRcj = this.mathItem.activeRCJs[0];
        let resQty = ObjectUtils.isEmpty(this.resQtyBaseRcj) ? 0 : this.resQtyBaseRcj.resQty;

        if(toRcj.tempDeleteFlag){
            toRcj.tempDeleteBackupResQty = resQty;
        }else{
            toRcj.resQty = resQty;
        }

        // 2. 执行人材机消耗量计算
        await super.computeResQty();
    }
}

module.exports = Txxx$yyy$VMathHandler;