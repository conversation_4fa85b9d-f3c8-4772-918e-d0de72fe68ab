const Kind3RuleHandler = require("./Kind3RuleHandler");
const { NumberUtil } = require("../../../../common/NumberUtil");
const { Snowflake } = require("../../utils/Snowflake");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const ProjectDomain = require("../../domains/ProjectDomain");
const FunctionTypeConstants = require("../../constants/FunctionTypeConstants");

class Kind3TypeBRuleHandler extends Kind3RuleHandler {
    constructor(strategyCtx, rule)  {
        super(strategyCtx, rule);
        this.formatMath = strategyCtx.conversionService.mathFormat(rule.math, rule);
    }

    async addDeByRule(){
        const {
            constructId,
            unitId,
            singleId,
            de,
            unitProject,
            deBeLong,
            deLine,
            isCsxmDe,
        } = this.ctx;
        let rule = this.rule;

        let ruleMath = this.formatMath;
        let newMath = ruleMath;
        let addDeNumber = 0;
        if("+-*/".includes(ruleMath.charAt(0))){
            addDeNumber = this.mathAfterCalculation(ruleMath.substring(1),this.showMathDigits);//NumberUtil.numberScale(eval(ruleMath.substring(1)), 6);
            newMath = ruleMath.charAt(0) + addDeNumber;
        }else{
            addDeNumber = this.mathAfterCalculation(ruleMath.substring(1),this.showMathDigits);//NumberUtil.numberScale(eval(ruleMath), 6);
            newMath = "" + addDeNumber;
        }

        // 如果新增定额数量小于1，则直接退出
        // if(addDeNumber < 1){
        //     return;
        // }

        // 获取相关定额 子集定额
        let relationDe = await this.ctx.service.gongLiaoJiProject.gljBaseDeService.getDeAndRcj(rule.relationDeId);
        // 新增定额
        let model = {
            constructId: constructId,
            unitId: unitId,
            type: '04',
            parentId: deLine.parentId,
            prevRowId: deLine.sequenceNbr,
        }
        let standardDeModelList = await this.ctx.service.gongLiaoJiProject.gljProjectCommonService.findDesByDeIdAndStandardDeId(constructId, unitId, de.sequenceNbr, relationDe.standardDeId);
        let addedDe = standardDeModelList.filter(item => !ObjectUtils.isEmpty(item.conversionAddByRule))[0];
        if (ObjectUtils.isEmpty(addedDe)) {
            let deUnitNum = this._getUnitNumber(deLine.unit);
            if (!isCsxmDe) {
                addedDe = await this.ctx.service.gongLiaoJiProject.gljDeService.createDeRowAppendBaseDe(model, relationDe.standardDeId);
                // 修改工程量
                let params = {
                    constructId,
                    unitId,
                    deId: addedDe.sequenceNbr,
                    quantity: NumberUtil.multiply(deLine.quantity, deUnitNum),
                    resetDeQuantities: false,
                    quantityExpression: "HSGCL"
                }
                await this.ctx.service.gongLiaoJiProject.gljDeService.updateQuantity(params);
                // 子目关联
                addedDe.quantityExpression = "HSGCL"
                addedDe.isRelationDe = true
                addedDe.fDeId = deLine.sequenceNbr

            } else{
                let param = {
                    constructId,
                    singleId,
                    unitId,
                    pointLine: model,
                    indexId: relationDe.standardDeId,
                };
                addedDe = await this.ctx.service.gongLiaoJiProject.gljStepItemCostService.fillFromIndexPageByConversion(param);
                // 修改工程量
                let upDateInfo2={
                    column: "originalQuantity",
                    value: NumberUtil.multiply(deLine.quantity, deUnitNum),
                }
                await this.ctx.service.gongLiaoJiProject.gljStepItemCostService.upDateOnList(constructId, singleId, unitId, addedDe.sequenceNbr, upDateInfo2);
                let relationDe2 = ProjectDomain.getDomain(constructId).csxmDomain.getDe(item=>item.sequenceNbr === addedDe.sequenceNbr);
                relationDe2.isRelationDe = true
                relationDe2.fDeId = deLine.sequenceNbr
                relationDe2.quantityExpression = 'HSGCL'
            }

            //更新定额费用代码
            let zmPriceCodes = [
                {code: 'HSGCL',price: addedDe.quantity}
            ]
            await this.ctx.service.gongLiaoJiProject.gljDeService.setDeCostCode(constructId, unitId, addedDe.sequenceNbr, zmPriceCodes);
            let zmVariableRuleList = [
                {
                    "id": 1,
                    "groupId": 1,
                    "variableName": "子目工程量（m）",
                    "variableCode": "HSGCL",
                    "formula": "",
                    "default": "",
                    "value": "",
                    "deEffectRange": "",
                    "ifEditable": 0,
                    "remark": null,
                    "resultValue": addedDe.quantity,
                    "index": 1
                }
            ];
            let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
            let unitQuantiesMap = quantitiesMap.get(unitId);
            let pointLine = unitQuantiesMap.get(deLine.sequenceNbr);
            pointLine.zmVariableRuleList = zmVariableRuleList
        }
        let addedDeConversion = await this.ctx.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, addedDe.sequenceNbr);
        addedDeConversion.conversionAddByRule = {
            sequenceNbr: Snowflake.nextId(),
            type: "",
            kind: "0",
            math: newMath,
            relation: newMath,
            defaultValue: 1,
            selectedRule: addDeNumber,
            index: -1,
            libraryCode: rule.libraryCode,
            ruleInfo: newMath,
            selected: true
        };
        // 标准换算相关
        addedDeConversion.mainMatConvertMod =  this.ctx.de.mainMatConvertMod

        let ruleDeIdObj = {
            deId: addedDe.sequenceNbr,
            ruleId: rule.sequenceNbr
        }
        if(de.addByRuleDeIds) {
            de.addByRuleDeIds.push(ruleDeIdObj);
        }else{
            de.addByRuleDeIds = [ruleDeIdObj];
        }

        this.ctx.deUpDateObj.addedDes.push(addedDe);
    }

    /**
     * 逐条执行换算规则
     */
    async execute(){
        await this.prepare();
        await this.addDeByRule();
        this.after();
    }

    analysisRule(){
        // 什么都不做
    }

    deCodeUpdateInfo() {
        return {redStr: null, blackStr: null}
    }

    deNameUpdateInfo(rule) {

    }

    deTypeUpdateInfo(rule){
        // 什么都不做
    }
    _getUnitNumber(deUnit){
        const match = deUnit.match(/^[.0-9]+/);
        return match ? parseFloat(match[0]) : 1;
    }
}
module.exports = Kind3TypeBRuleHandler;
