const JSONMathHandler = require("./JSONMathHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {NumberUtil} = require("../../../../common/NumberUtil");
/**
 * 处理RCJ相关规则：根据指定人材机消耗量计算消耗量
 */
class JSONRANGEMathHandler extends JSONMathHandler{
    setParseMath() {
        let qtyStr = this.mathJson.math;
        for (let oneBase of this.mathJson.base) {
            let oneBaseMath = this.createMathReal(this.mathJson.range, oneBase, this.rule.selectedRule)+"";
            let oneBaseQty = this.conversionService.mathAfterCalculation(oneBaseMath, this.conversionInfoDigits);

            qtyStr = this.mathReplaceAllDeal(qtyStr, oneBase.id, oneBaseQty)

        }
        this.parseMath = qtyStr;
    }

    createMathReal(range, oneBase, inputValue){
        let rangeKs =  Object.keys(range.rangeKV) || [];
        let dstK = rangeKs.find(k => k == inputValue);
        if(ObjectUtils.isNotEmpty(dstK)){
            return range.rangeKV[dstK][oneBase.prop];
        }

        let oriMath = range.condition;
        let startK = null;
        let endK = null;
        rangeKs.sort((a, b) => a - b);

        for (let rangeK of rangeKs) {
            if(rangeK > inputValue){
                endK = rangeK;
                break;
            }else{
                startK = rangeK;range.rangeKV[rangeK];
            }
        }

        let startObj = range.rangeKV[startK];
        let endObj = range.rangeKV[endK];
        let newMath = this.mathReplaceAllDeal(oriMath, "startValue", startObj[oneBase.prop]);
        newMath = this.mathReplaceAllDeal(newMath, "endValue", endObj[oneBase.prop]);
        newMath = this.mathReplaceAllDeal(newMath, "startKey", startK);
        newMath = this.mathReplaceAllDeal(newMath, "endKey", endK);
        newMath = this.mathReplaceAllDeal(newMath, "V", inputValue);

        return newMath;
    }
}

module.exports = JSONRANGEMathHandler;