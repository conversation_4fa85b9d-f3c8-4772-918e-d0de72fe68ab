const RuleHandler = require("./ruleHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");
class Kind3RuleHandler extends RuleHandler {

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = `${this.rule.relation}:${this.rule.selectedRule}`;
    }

    mathAfterCalculation(numStr,digits){
        return this.ctx.conversionService.mathAfterCalculation(numStr,digits);
    }

    deCodeUpdateInfo() {
        let redSubArray = [];
        for (let handler of this.rule.mathHandlers) {
            let math = handler.mathItem.math;
            let parseMath = handler.mathItem.parseMath;
            if(ObjectUtils.isNotEmpty(handler.showMath)){
                redSubArray.push(handler.showMath);
            }else{
                let result = this.mathAfterCalculation(parseMath,this.showMathDigits);
                redSubArray.push(math.replace(parseMath, result));
            }

        }
        return {redStr: "["+redSubArray.join(",")+"]", blackStr: null}
    }

    deNameUpdateInfo(rule) {
        let inputValue = this.mathAfterCalculation(""+this.rule.selectedRule,this.showMathDigits);
        return `${rule.relation}:${inputValue}`;
    }
}
module.exports = Kind3RuleHandler;
