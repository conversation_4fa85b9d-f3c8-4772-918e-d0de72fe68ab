const {Service} = require("../../../core");
const {app} = require('electron');
const {ResponseData} = require("../utils/ResponseData");
const ExportSheetNameEnum = require("../enums/GljExportSheetNameEnum");
const ProjectDomain = require("../domains/ProjectDomain");
const {GljExcelUtil} = require("../utils/GljExcelUtil.js");
const GljExcelEnum = require("../enums/GljExcelEnum");
const {GljWriteExcelBySheetUtil} = require("../utils/GljWriteExcelBySheetUtil.js");
const path = require('path');
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const UtilsPs = require('../../../core/ps');
const AdmZip = require('adm-zip');
const fs = require('fs');
const {
    app: electronApp, dialog, shell, BrowserView, Notification, powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const projectLevelConstant = require("../constants/ProjectLevelConstant");
const {map} = require("rxjs");
const Decimal = require("decimal.js");

const ProjectLevelConstant = require("../constants/ProjectLevelConstant");
const DeTypeConstants = require("../constants/DeTypeConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const _ = require('lodash');
const TaxCalculationMethodEnum = require("../enums/TaxCalculationMethodEnum");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const xeUtils = require("xe-utils");
const BranchProjectLevelConstant = require("../constants/BranchProjectLevelConstant");
const {DateTimeUtil} = require("../utils/DateTimeUtil");
const CommonConstants = require("../constants/CommonConstants");
const {GljExportConfig} = require("../models/GljExportConfig");
const {GljExcelOperateUtil} = require("../utils/GljExcelOperateUtil");
const GljExportSheetNameEnum = require("../enums/GljExportSheetNameEnum");
const ExcelJS = require('exceljs');
const {RowDTO,CellDTO,DataTemplate,ColumnDTO} = require("../report/dataTemplate");
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const excelTemplatePath = UtilsPs.getExtraResourcesDir()+"\\excelTemplate\\export\\pdf.xlsx"
class GljExportQueryService extends Service {
    constructor(ctx) {
        super(ctx);
    }

    //展示报表查看的表名列表
    async showExportHeadLine(itemLevel, args) {

        let result = [];
        //定义一个存放数据值和cell的对象
        function HeadLineList(desc, baoBiaoList) {
            this.desc = desc;//存放大标题
            this.baoBiaoList = baoBiaoList;
        }

        if (itemLevel == ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            itemLevel = "project";
        }
        if (itemLevel == ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            itemLevel = "single";
        }
        if (itemLevel == ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            itemLevel = "unit";
        }


        // let object = await this.initReportStructureObject(itemLevel,args.constructId,args.singleId,args.unitId);
        let construct = ProjectDomain.getDomain(args.constructId).getProjectById(args.constructId);
        let headLineList = ObjectUtils.isNotEmpty(construct.reportViewObject)&&ObjectUtils.isNotEmpty(construct.reportViewObject.headLineList) ? construct.reportViewObject.headLineList : [];
        let headLineListUnit = [];
        if (ObjectUtils.isNotEmpty(args.unitId)) {
            let unit = ProjectDomain.getDomain(args.constructId).getProjectById(args.unitId);
            headLineListUnit = ObjectUtils.isNotEmpty(unit.reportViewObject)&&ObjectUtils.isNotEmpty(unit.reportViewObject.headLineList) ? unit.reportViewObject.headLineList : [];
        }

        //存放大栏目下的表的表名
        let shengZhanList = _.cloneDeep(ExportSheetNameEnum.省站标准.filter(function (element) {
            if (element.projectLevel == itemLevel) return element;
        }));
        let generalList = _.cloneDeep(ExportSheetNameEnum.通用.filter(function (element) {
            if (element.projectLevel == itemLevel) return element;
        }));
        if (ObjectUtils.isNotEmpty(shengZhanList)) {
            let headLineList1 = new HeadLineList("省站标准",shengZhanList);
            for (let item of headLineList1.baoBiaoList) {
                if (itemLevel === "unit") {
                    item.id = await this.appendUnitId(args.unitId, item.id);
                    if (ObjectUtils.isNotEmpty(headLineListUnit) && headLineListUnit.includes(item.id)) {
                        item.selected = true;
                    } else {
                        item.selected = false;
                    }
                } else {
                    if (ObjectUtils.isNotEmpty(headLineList) && headLineList.includes(item.id)) {
                        item.selected = true;
                    } else {
                        item.selected = false;
                    }
                }
            }
            result.push(headLineList1);
        }
        if (ObjectUtils.isNotEmpty(generalList)) {
            let headLineList2 = new HeadLineList("通用",generalList);
            for(let item of headLineList2.baoBiaoList){
                if (itemLevel === "unit") {
                    item.id = await this.appendUnitId(args.unitId, item.id);
                    if (ObjectUtils.isNotEmpty(headLineListUnit) && headLineListUnit.includes(item.id)) {
                        item.selected = true;
                    } else {
                        item.selected = false;
                    }
                } else {
                    if (ObjectUtils.isNotEmpty(headLineList) && headLineList.includes(item.id)) {
                        item.selected = true;
                    } else {
                        item.selected = false;
                    }
                }
            }
            result.push(headLineList2);
        }

        if (itemLevel === "unit") {
            let cepingList = _.cloneDeep(ExportSheetNameEnum.省站标准测评响应.filter(function (element) {
                if (element.projectLevel == itemLevel) return element;
            }));
            if (ObjectUtils.isNotEmpty(cepingList)) {
                let headLineList3 = new HeadLineList("省站标准(测评响应)", cepingList);
                for (let item of headLineList3.baoBiaoList) {
                    item.id = await this.appendUnitIdCeping(args.unitId,item.id);
                    if (ObjectUtils.isNotEmpty(headLineListUnit) && headLineListUnit.includes(item.id)) {
                        item.selected = true;
                    } else {
                        item.selected = false;
                    }
                }
                result.push(headLineList3);
            }

            let cepingList1 = _.cloneDeep(ExportSheetNameEnum.通用测评响应.filter(function (element) {
                if (element.projectLevel == itemLevel) return element;
            }));
            if (ObjectUtils.isNotEmpty(cepingList1)) {
                let headLineList3 = new HeadLineList("通用(测评响应)", cepingList1);
                for (let item of headLineList3.baoBiaoList) {
                    item.id = await this.appendUnitIdCeping(args.unitId,item.id);
                    if (ObjectUtils.isNotEmpty(headLineListUnit) && headLineListUnit.includes(item.id)) {
                        item.selected = true;
                    } else {
                        item.selected = false;
                    }
                }
                result.push(headLineList3);
            }
        }

        return result;
    }

    //初始化单位、单项及工程项目的报表结构树对象
    async initReportStructureObject(itemLevel,constructId,singleId,unitId) {
        let object = null;//该对象
        if (itemLevel == "project") {
            object = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        }
        if (itemLevel == "single") {
            object = ProjectDomain.getDomain(constructId).getProjectById(singleId);
        }
        if (itemLevel == "unit") {
            object = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        }
        //如果对象的模板不为空并且版本一致时 直接返回  否则需要重新初始化模板
        if (!(await this.judgeProjectLevelToUpdateTemplate(object))) return object.reportViewObject;
        let result =await this.initKindsOfBaoBiaoTemplate(itemLevel,constructId,singleId,unitId);
        //如果不是手动新建的  并且 没有被表格设计过 则更新该sheet的模板  （保留上一个版本表格设计过的模板）
        if (ObjectUtils.isNotEmpty(object['reportViewObject'])) {
            await this.updateTemplateNotDesigned(object['reportViewObject']['省站标准'],result.zhaoBiaoList);
            await this.updateTemplateNotDesigned(object['reportViewObject']['通用'],result.touBiaoList);
        }else {
            object['reportViewObject'] = {};
            object['reportViewObject']['省站标准'] = result.zhaoBiaoList;
            object['reportViewObject']['通用'] = result.touBiaoList;
        }
        object['reportViewObject']['version'] = app.getVersion();
        return object.reportViewObject;
    }

    /**
     * 判断单项、单位、工程项目层级的报表模板是否需要重新初始化
     * @param object
     * @returns true 表示需要重新初始化
     */
    async judgeProjectLevelToUpdateTemplate(object) {
        let version = app.getVersion();
        //如果对象的模板不为空并且版本一致时 直接返回  否则需要重新初始化模板
        if (ObjectUtils.isNotEmpty(object.reportViewObject) && (ObjectUtils.isNotEmpty(object.reportViewObject.version)&& ObjectUtils.isNotEmpty(version)
            && object.reportViewObject.version==version)
        ) return false;
        return true;
    }

    async initKindsOfBaoBiaoTemplate(itemLevel,constructId,singleId,unitId) {
        let constructIs2022= true;
        let ExportSheetNameEnumZhaoBiao = {};
        let ExportSheetNameEnumTouBiao = {};
        let args = {};
        if (itemLevel == "project"||itemLevel == "single") {  //
            args['levelType'] = 1;
            args['constructId'] = constructId;
        }else if (itemLevel == "unit") {
            args['levelType'] = 3;
            args['constructId'] = constructId;
            args['singleId'] = singleId;
            args['unitId'] = unitId;
        }
        let taxCalculation = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation;
        if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            if (constructIs2022) {
                ExportSheetNameEnumZhaoBiao = GljExportSheetNameEnum;
                ExportSheetNameEnumTouBiao = GljExportSheetNameEnum;
            }else {
                ExportSheetNameEnumZhaoBiao = GljExportSheetNameEnum;
                ExportSheetNameEnumTouBiao = GljExportSheetNameEnum;
            }
        }else {
            if (constructIs2022) {
                ExportSheetNameEnumZhaoBiao = GljExportSheetNameEnum;
                ExportSheetNameEnumTouBiao = GljExportSheetNameEnum;
            }else {
                ExportSheetNameEnumZhaoBiao = GljExportSheetNameEnum;
                ExportSheetNameEnumTouBiao = GljExportSheetNameEnum;
            }
        }


        let zhaoBiaoList = _.cloneDeep(ExportSheetNameEnumZhaoBiao.省站标准).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });
        let touBiaoList = _.cloneDeep(ExportSheetNameEnumTouBiao.通用).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });


        if (itemLevel == "single") {
            let singleProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);
            if (ObjectUtils.isNotEmpty(singleProject.subSingleProjects)) {
                // let elementZb = zhaoBiaoList.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                // elementZb.excelDataTemplate = DXGCFHZ1_5.招标项目报表;
                // let elementTb = touBiaoList.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                // elementTb.excelDataTemplate = DXGCFHZ1_5.投标项目报表;
                // let elementGcl = gongChengLiangList.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                // elementGcl.excelDataTemplate = DXGCFHZ1_5.工程量清单报表;
            }
        }

        if (itemLevel == "unit") {
            if (constructIs2022) {  //如果是22定额标准项目
                // zhaoBiaoList = zhaoBiaoList.filter(function (element) {
                //     if (!element.headLine.includes("增值税报表")) {
                //         return element;
                //     }
                // });
                // touBiaoList = touBiaoList.filter(function (element) {
                //     if (!element.headLine.includes("增值税报表")) {
                //         return element;
                //     }
                // });
                // gongChengLiangList = gongChengLiangList.filter(function (element) {
                //     if (!element.headLine.includes("增值税报表")) {
                //         return element;
                //     }
                // });
            }else {
                // let args = {};
                // args['levelType'] = 3;
                // args['constructId'] = constructId;
                // args['singleId'] = singleId;
                // args['unitId'] = unitId;
                // let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
                // //简易计税  去掉增值税表
                // if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                //     zhaoBiaoList = zhaoBiaoList.filter(item => !(item.headLine=="增值税报表"));
                //     touBiaoList = touBiaoList.filter(item => !(item.headLine=="增值税报表"));
                //     gongChengLiangList = gongChengLiangList.filter(item => !(item.headLine=="增值税报表"));
                // } else { //12的一般计税  临时删除
                //     let zhaoBiaoZZS = zhaoBiaoList.filter(item => item.headLine=="增值税报表")[0];
                //     let touBiaoZZS = touBiaoList.filter(item => item.headLine=="增值税报表")[0];
                //     let gongChengLiangZZS = gongChengLiangList.filter(item => item.headLine=="增值税报表")[0];
                //     zhaoBiaoZZS.children = zhaoBiaoZZS.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                //         ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                //     touBiaoZZS.children = touBiaoZZS.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                //         ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                //     gongChengLiangZZS.children = gongChengLiangZZS.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                //         ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                // }
            }
        }

        //去除父级标题项  其他栏目 不做处理
        await this.removeParentHeadLine(zhaoBiaoList);
        await this.removeParentHeadLine(touBiaoList);

        //初始化横竖版状态
        await this.setIsLandScapeTrue(zhaoBiaoList.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(zhaoBiaoList.filter(item => item.headLine.includes("单项工程造价分析表")));

        await this.setIsLandScapeTrue(touBiaoList.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(touBiaoList.filter(item => item.headLine.includes("单项工程造价分析表")));

        return {"shengzhanList":zhaoBiaoList,"tongyongList":touBiaoList};
    }

    async removeParentHeadLine(headLineList) {
        for (let i = 0; i < headLineList.length; i++) {
            if (ObjectUtils.isNotEmpty(headLineList[i].children)) {
                let parent = headLineList[i];
                for (let j = 0; j < parent.children.length; j++) {
                    let child = parent.children[j];
                    headLineList.splice(i+j,0,child);
                }
                let indexOf = headLineList.indexOf(parent);
                headLineList.splice(indexOf,1);
                await this.removeParentHeadLine(headLineList);
                break;
            }
        }
    }

    async setIsLandScapeTrue(headLineList) {
        if (ObjectUtils.isEmpty(headLineList)) return;
        for (let i = 0; i < headLineList.length; i++) {
            headLineList[i].isLandScape = true;
        }
    }


    //更新工程中没有被设计过的模板
    async updateTemplateNotDesigned(baoBiaoListOrigin,baoBiaoList) {
        for (let i = 0; i < baoBiaoListOrigin.length; i++) {
            if (ObjectUtils.isNotEmpty(baoBiaoListOrigin[i].isManual)) {  //如果是手动新建的报表不进行更新
                continue;
            }
            if (ObjectUtils.isEmpty(baoBiaoListOrigin[i].isUpdated) || !baoBiaoListOrigin[i].isUpdated) {
                let filter = baoBiaoList.filter(item => item.headLine==baoBiaoListOrigin[i].headLine);
                if (ObjectUtils.isNotEmpty(filter)) {
                    baoBiaoListOrigin[i].excelDataTemplate = filter[0].excelDataTemplate;
                }
            }
        }

        //将新版本多出来的新增模板 添加进去
        let elementList = baoBiaoList.filter(itemReport => ObjectUtils.isEmpty(baoBiaoListOrigin.filter(item => item.headLine==itemReport.headLine)));
        baoBiaoListOrigin.push(...elementList);
    }



    async calUnitSheetZyhz(constructId, unitId, singleId, worksheet) {
        let args = {};
        args.constructId = constructId;
        args.singleId = singleId;
        args.unitId = unitId;
        args.type = 1;
        args.levelType = projectLevelConstant.unit;
        args.code = "8";
        let majorMenuList = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(args);
        if (ObjectUtils.isNotEmpty(majorMenuList)) {
            if (majorMenuList.itemList.length === 1) {
                worksheet.removeWorksheet("单位工程费用表(安装工程)");
                worksheet.removeWorksheet("单位工程费用表(建筑工程)");
                //未进行专业汇总，只显示一个主专业
                // let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
                // if (ObjectUtils.isNotEmpty(unit) && ObjectUtils.isNotEmpty(unit.constructMajorType)) {
                //     let JZGC = unit.constructMajorType.includes("JZGC");    //主专业是建筑
                //     let AZGC = unit.constructMajorType.includes("AZGC");    //主专业是安装
                //     if (JZGC) {
                //         worksheet.removeWorksheet("单位工程费用表(安装工程)");
                //     }
                //     if (AZGC) {
                //         worksheet.removeWorksheet("单位工程费用表(建筑工程)");
                //     }
                // }
            } else if (majorMenuList.itemList.length > 1) {

            }
        } else {
            worksheet.removeWorksheet("单位工程费用表(安装工程)");
            worksheet.removeWorksheet("单位工程费用表(建筑工程)");
            //未进行专业汇总，只显示一个主专业
            // let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            // if (ObjectUtils.isNotEmpty(unit) && ObjectUtils.isNotEmpty(unit.constructMajorType)) {
            //     let JZGC = unit.constructMajorType.includes("JZGC");    //主专业是建筑
            //     let AZGC = unit.constructMajorType.includes("AZGC");    //主专业是安装
            //     if (JZGC) {
            //         worksheet.removeWorksheet("单位工程费用表(安装工程)");
            //     }
            //     if (AZGC) {
            //         worksheet.removeWorksheet("单位工程费用表(建筑工程)");
            //     }
            // }
        }
    }


    async showSheetStyle(args) {
        let {itemLevel, sheetName,constructObj,lanMuName,id,isTemporaryEdit} = args;
        //前面查询为了唯一把id换为单位id+报表id，这里重置为报表id
        if(ObjectUtils.isNotEmpty(id) && String(id).includes("-")){
            id = Number(id.split("-")[1]);
            args["id"] = id;
        }
        let taxMethod = ProjectDomain.getDomain(constructObj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let taxMethodPath = "";
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxMethodPath = "\\简易";
        }else {
            taxMethodPath = "\\一般";
        }
        let shenhe = this.getProjectRootPath() + "\\excelTemplate\\glj"+taxMethodPath+"\\"+lanMuName;
        if (itemLevel == ProjectLevelConstant.construct) {
            shenhe = shenhe + "\\工程项目层级.xlsx";
        }else if (itemLevel == ProjectLevelConstant.unit) {
            shenhe = shenhe + "\\单位层级.xlsx";
        }
        let loadPath = shenhe;

        let workbook = await GljExcelUtil.readToWorkBook(loadPath);
        args["workbook"] = workbook;
        //对于重复的表名 进行重置
        if (id==11){
            sheetName = "单位工程造价汇总表(省站标准) (2)";
        }
        let workSheet = workbook.getWorksheet(sheetName);
        try {
            workSheet = await this.switchWorkSheet(itemLevel, workSheet, args);
            if (isTemporaryEdit) {   //如果是临时编辑
                return await this.dealWithWorkSheetToJsonData(workSheet,sheetName);
            }
        } catch (e) {
            console.log("报表填充数据异常",e);
        }
        let result;
        try {
            result = await GljExcelUtil.findCellStyleList(workSheet);
        } catch (e) {
            console.log("报表填充数据异常",e);
        }
        return result;
    }


    async dealLanMuNameCeping(lanMuName) {
        if (lanMuName.includes("(") || lanMuName.includes(")")) {
            lanMuName = lanMuName.replace("(", '');
            lanMuName = lanMuName.replace(")", '');
        }
        return lanMuName;
    }

    async queryLanMuData(constructId,lanMuName) {
        let project = [];
        let single = [];
        let unit = [];

        lanMuName = await this.dealLanMuNameCeping(lanMuName);

        let construct = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        let headLineList = ObjectUtils.isNotEmpty(construct.reportViewObject)&&ObjectUtils.isNotEmpty(construct.reportViewObject.headLineList)?construct.reportViewObject.headLineList:[];
        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum[lanMuName], "project", project);
        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum[lanMuName], "unit", unit);

        if (ObjectUtils.isNotEmpty(project)) {
            for (let item of project) {
                if (ObjectUtils.isNotEmpty(headLineList) && headLineList.includes(item.id)) {
                    item.selected = true;
                }
            }
        }

        let result = {};
        let newProject = await this.deepCopy(project);
        result["headLine"] = construct.name;
        result["childrenList"] = newProject;
        result["id"] = construct.sequenceNbr;
        result["levelType"] = construct.type;

        for (let i = 0; i < construct.children.length; i++) {
            await this.traverseSingleForExport(construct.sequenceNbr,construct.children[i],newProject,lanMuName,unit);
        }
        //对result进行递归遍历  增加唯一序号
        let object = {};
        object['sequenceNbr'] = 1;
        await this.addOrderNum(result, object);

        return result;
    }


    async traverseSingleForExport(constructId,singleProject,result,lanMuName,unitReport) {
        if (singleProject.type == ProjectLevelConstant.unit) {    //如果是工程项目下直接新建了单位工程
            let unitObject = {};
            unitObject["headLine"] = singleProject.name;
            unitObject["childrenList"] = await this.deepCopy(unitReport);
            unitObject["levelType"] = singleProject.type;
            unitObject["id"] = singleProject.sequenceNbr;
            result.push(unitObject);
            return;
        }
        let singleObject = {};
        singleObject["headLine"] = singleProject.name;
        let singleChildren = [];
        singleObject["childrenList"] = singleChildren;
        singleObject["levelType"] = singleProject.type;
        singleObject["id"] = singleProject.sequenceNbr;
        result.push(singleObject);

        if (!ObjectUtils.isEmpty(singleProject.children) && singleProject.children[0].type==projectLevelConstant.unit) {  //如果是含有单位的单项
            for (let i = 0; i < singleProject.children.length; i++) {
                let unitProject = ProjectDomain.getDomain(constructId).getProjectById(singleProject.children[i].sequenceNbr);
                let headLineList = ObjectUtils.isNotEmpty(unitProject.reportViewObject)&&ObjectUtils.isNotEmpty(unitProject.reportViewObject.headLineList) ? unitProject.reportViewObject.headLineList : [];
                let childrenList = await this.deepCopy(unitReport);
                if (ObjectUtils.isNotEmpty(childrenList)) {
                    for (let item of childrenList) {
                        if(lanMuName.includes("测评响应")){
                            item.id = await this.appendUnitIdCeping(unitProject.sequenceNbr,item.id);
                        } else {
                            item.id = await this.appendUnitId(unitProject.sequenceNbr,item.id);
                        }
                        if (ObjectUtils.isNotEmpty(headLineList) && headLineList.includes(item.id)) {
                            item.selected = true;
                        }
                    }
                }

                let unitObject = {};
                unitObject["headLine"] = singleProject.children[i].name;
                unitObject["childrenList"] = childrenList;
                unitObject["levelType"] = singleProject.children[i].type;
                unitObject["id"] = singleProject.children[i].sequenceNbr;
                singleChildren.push(unitObject);
            }
        }

        if (!ObjectUtils.isEmpty(singleProject.children) && singleProject.children[0].type==projectLevelConstant.single) {     //如果当前单项是子单项
            for (let i = 0; i < singleProject.children.length; i++) {
                await this.traverseSingleForExport(constructId,singleProject.children[i],singleChildren,lanMuName,unitReport);
            }
        }
    }

    async appendUnitId(unitId, reportId) {
        return unitId + "-" + reportId;
    }
    async appendUnitIdCeping(unitId, reportId) {
        return unitId + "ceping" + "-" + reportId;
    }

    async addOrderNum(result, object) {
        if (result['sequenceNbr'] == null) {
            result['sequenceNbr'] = object.sequenceNbr++;
        }
        if (result.childrenList != null) {
            for (let i = 0; i < result.childrenList.length; i++) {
                await this.addOrderNum(result.childrenList[i], object);
            }
        }
    }

    async deepCopy(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        let clone = Array.isArray(obj) ? [] : {};

        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                clone[key] = await this.deepCopy(obj[key]);
            }
        }

        return clone;
    }

    async exportExcel(params,lanMuName,startPage,totalPage) {
        // let {constructName} = params.headLine;
        // let defaultStoragePath = ProjectDomain.getDefaultStoragePath(constructName);
        const dialogOptions = {
            title: '保存文件', defaultPath: params.headLine, filters: [{name: 'zip', extensions: ['zip']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.exportExcelZip(params, filePath,lanMuName,startPage,totalPage);
            return true;
            // this.service.shenHeYuSuanProject.systemService.openWindowForProject(constructName,sequenceNbr);
        } else {
            return false;
        }
    }

    async exportExcelZip(params, filePath,lanMuName,startPage,totalPage) {
        let construct = ProjectDomain.getDomain(params.id).getProjectById(params.id);

        if (ObjectUtils.isEmpty(construct.exportConfig)) {  //导出设置的配置
            construct.exportConfig = new GljExportConfig(null, null, "excelWithLevel");
        }

        let taxCalculationMethodPath = "";
        let taxCalculationMethod = construct.projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxCalculationMethodPath = "简易";
        }else {
            taxCalculationMethodPath = "一般";
        }
        let project = await this.initWorkBook(projectLevelConstant.construct,lanMuName,taxCalculationMethodPath);
        let unit = await this.initWorkBook(projectLevelConstant.unit,lanMuName,taxCalculationMethodPath);

        let fileDir = this.getProjectRootPath() + "\\excelTemplate\\glj\\" + params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        args["lanMuName"] = lanMuName;
        args['startPage'] = startPage;
        args['totalPage'] = totalPage;
        args['fileType'] = "excel";
        await this.parseParams(params, project, null, unit, fileDir, args,taxCalculationMethodPath,lanMuName,workBookList);


        if (construct.exportConfig.batchExport=="excelWithLevel") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                await workBookList[i].sheet.xlsx.writeFile(workBookList[i].filename);
            }
        }else if (construct.exportConfig.batchExport=="excelWithAll") { //所有sheet到一个excel中

            await this.createDirectory(workBookList[0].fileDir);
            //合成一个大excel文件
            //先对workBookList[0]._worksheets 按照 worksheets的顺序进行重排  worksheets的属性orderNo 始终是有序的
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                //按照worksheets的元素排列  确定在_worksheets 中当前的索引  及id相同的对应索引 进行位置交换 使当前索引上的sheet是想要的对应id的sheet
                let indexCur = await this.getIndexIn_worksheets(i+1,workBookList[0].sheet);
                let indexId = await this.getIndexOfSameId(workBookList[0].sheet.worksheets[i].id,workBookList[0].sheet);
                [workBookList[0].sheet._worksheets[indexCur], workBookList[0].sheet._worksheets[indexId]] = [workBookList[0].sheet._worksheets[indexId],workBookList[0].sheet._worksheets[indexCur]];
            }

            //更新表名 增加格式 【单位工程名称】
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                let worksheet1 = workBookList[0].sheet.worksheets[i];
                let levelName = workBookList[0].filename.replace(workBookList[0].fileDir+"\\","").replace(".xlsx","");
                worksheet1.name = worksheet1.name+"【"+levelName+"】";
            }
            for (let i = 1; i < workBookList.length; i++) {
                let bookElement = workBookList[i].sheet;
                for (let j = 0; j < bookElement.worksheets.length; j++) {
                    let worksheet = bookElement.worksheets[j];
                    if (worksheet != null) {
                        let levelName = workBookList[i].filename.replace(workBookList[i].fileDir+"\\","").replace(".xlsx","");
                        worksheet.name =worksheet.name+"【"+levelName+"】";
                        workBookList[0].sheet._worksheets.push(worksheet);
                    }
                }
            }
            //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
            //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
            let orderNo = 0;
            let map = new Map();//键为sheet名称  值为重复的累加次数
            for (let i = 0; i < workBookList[0].sheet._worksheets.length; i++) {
                let worksheetSam = workBookList[0].sheet._worksheets[i];
                if (worksheetSam != null) {
                    let replace = worksheetSam.name.replace(/【.*?】/g, '');
                    let levelName = worksheetSam.name.replace(replace,'').replace("【","").replace("】","");
                    if (map.has(replace)) {
                        map.set(replace, map.get(replace)+1);
                    }else {
                        map.set(replace,1);
                    }
                    let wholeChar = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    if (wholeChar.length > 31) {
                        levelName = levelName.substring(0,levelName.length-(wholeChar.length-32));//去掉】  则名称多延长一位
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace);
                    }else {
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    }
                    worksheetSam.id = ++orderNo;
                    worksheetSam.orderNo = orderNo;
                }
            }
            await workBookList[0].sheet.xlsx.writeFile(workBookList[0].fileDir+"\\"+construct.constructName+".xlsx");
        }else if (construct.exportConfig.batchExport=="excelWithSingleSheet") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                let sheet = workBookList[i].sheet;
                for (let j = 0; j < sheet.worksheets.length; j++) {
                    let worksheet = sheet.worksheets[j];
                    let workbook = new ExcelJS.Workbook();
                    await GljExcelOperateUtil.addWorkSheet(workbook,worksheet,worksheet.name);
                    await workbook.xlsx.writeFile(workBookList[i].fileDir+"\\"+worksheet.name+".xlsx");
                }
            }
        }
        //对 对应的目录进行压缩 生成zip文件
        // 创建一个新的 Zip 实例
        const zip = new AdmZip();

        // 递归遍历目录及其子目录中的文件和子目录
        function traverseDirectory(dirPath, relativePath = '') {
            const files = fs.readdirSync(dirPath);

            files.forEach(file => {
                const filePath = path.join(dirPath, file);
                const stats = fs.statSync(filePath);

                if (stats.isDirectory()) {
                    const fileRelativeNext = path.join(relativePath, file);
                    zip.addFile(fileRelativeNext + '/', Buffer.alloc(0), '', 493); // 添加空文件夹
                    traverseDirectory(filePath, fileRelativeNext);
                } else {
                    zip.addLocalFile(filePath, relativePath);
                }
            });
        }

        traverseDirectory(fileDir, params.headLine);
        // 将 zip 文件保存到指定路径
        zip.writeZip(filePath);

        function deleteDirectory(dirPath) {
            if (fs.existsSync(dirPath)) {
                fs.readdirSync(dirPath).forEach(file => {
                    const filePath = path.join(dirPath, file);

                    if (fs.lstatSync(filePath).isDirectory()) {
                        deleteDirectory(filePath); // 递归删除子目录
                    } else {
                        fs.unlinkSync(filePath); // 删除文件
                    }
                });

                fs.rmdirSync(dirPath); // 删除空目录
                console.log('目录删除成功');
            } else {
                console.log('目录不存在');
            }
        }

        deleteDirectory(fileDir);
    }
    
    /**
     * Exports a signal to an Excel file.
     *
     * @param {any} args - The arguments required for exporting the signal.
     * @param {Function} callback - The callback function to be called after exporting the signal.
     * @returns {Promise<boolean>} - Returns a promise indicating whether the export was successful or not.
     */
    async exportSignalExcel(args){
        const dialogOptions = {
            title: '保存文件', defaultPath: args.headLine, filters: [{name: 'excel', extensions: ['xlsx']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.exportDirectoryTable(args,filePath);
            return true;
        } else {
            return false;
        }
    }

    async exportSignalPdf(args) {
        const dialogOptions = {
            title: '保存文件', defaultPath: args.headLine, filters: [{name: 'pdf', extensions: ['pdf']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.createDirectory(UtilsPs.getExtraResourcesDir()+"\\excelTemplate\\export");
            await this.exportDirectoryTable(args,excelTemplatePath);
            // await callback(args,excelTemplatePath);
            await this.convertExcelToPDF(filePath)
            return true;
        } else {
            return false;
        }
    }

    async convertExcelToPDF(pdfPath) {
        //生成excel
        // let excelFilePath = UtilsPs.getExtraResourcesDir()+"\\excelTemplate\\export\\pdf.xlsx";
        //设置环境变量
        let javaCommand = UtilsPs.getExtraResourcesDir()+"\\jre\\bin\\java";
        let javaHomePath = UtilsPs.getExtraResourcesDir()+"\\jre";
        let jarPath = UtilsPs.getExtraResourcesDir()+"\\pdfUtil.jar";
        let parameters = "\""+excelTemplatePath+"\""
            +"   "+"\""+pdfPath+"\"";

        await this.service.gongLiaoJiProject.gljPdfService.runCommand(javaCommand+" -Dfile.encoding=UTF-8  -DJAVA_HOME="+javaHomePath+"  -jar "+jarPath+"  "+parameters);
        //删除原来生成的excel文件
        fs.unlink(UtilsPs.getExtraResourcesDir()+"\\excelTemplate\\export\\pdf.xlsx", (err) => {
            if (err) {
                console.error('删除文件时出错:', err);
                return;
            }
            console.log('文件删除成功！');
        });
    }

    /**
     * Exports a directory table to an Excel file.
     *
     * @param {Object} args - The arguments for exporting the directory table.
     * @param {string} args.jsonDataVO - The JSON data for the directory table.
     * @param {string} args.sheetName - The name of the worksheet in the Excel file.
     * @param {string} filePath - The file path for the exported Excel file.
     * @returns {Promise<void>}
     */
    async exportDirectoryTable(args,filePath,type) {
        let {jsonDataVO,sheetName,page,isLandScape} = args;
        let workbook = new ExcelJS.Workbook();
        let worksheet = workbook.addWorksheet(sheetName);
        let orientation = "portrait";
        let top = GljExcelEnum.A4Top/72;
        let bottom = GljExcelEnum.A4Bottom/72;
        let left = GljExcelEnum.A4Left/72;
        let right = GljExcelEnum.A4Right/72;
        if (isLandScape) { //如果是横版
            orientation = "landscape";//较宽  横版
            top = GljExcelEnum.A4TopHorizontal/72;
            bottom = GljExcelEnum.A4BottomHorizontal/72;
            left = GljExcelEnum.A4LeftHorizontal/72;
            right = GljExcelEnum.A4RightHorizontal/72;
        }
        let max = 16838;
        let man = 1;
        let pageNum = page.sheetBreak.map((id,max,man) =>({id:id+1,max,man}));
        // worksheet.rowBreaks = page.sheetBreak;
        worksheet.rowBreaks = pageNum;

        let pageSetupJsonObject = {
            "fitToPage": false,
            "margins": {
                "left": left,
                "right": right,
                "top": top,
                "bottom": bottom,
                "header": 0,
                "footer": 0
            },
            "paperSize": 9,
            "orientation": orientation,
            "horizontalDpi": 4294967295,
            "verticalDpi": 4294967295,
            "pageOrder": "downThenOver",
            "blackAndWhite": false,
            "draft": false,
            "cellComments": "None",
            "errors": "displayed",
            "scale": 100,
            "fitToWidth": 1,
            "fitToHeight": 1,
            "firstPageNumber": 1,
            "useFirstPageNumber": false,
            "usePrinterDefaults": false,
            "copies": 1,
            "showRowColHeaders": false,
            "showGridLines": false,
            "horizontalCentered": true,
            "verticalCentered": false
        }
        worksheet.pageSetup = pageSetupJsonObject;

        let result = [];
        let dataTemplate = new DataTemplate();
        let jsonData = _.cloneDeep(jsonDataVO);
        //--start--------初始化 dataTemplate----------------------------
        if (ObjectUtils.isEmpty(dataTemplate)) {
            dataTemplate = new DataTemplate();
        }
        dataTemplate.mergeData = jsonData.sheets["sheet1"].mergeData;
        for (let cellDataKey in jsonData.sheets["sheet1"].cellData) {
            let rowInfo = jsonData.sheets["sheet1"].cellData[cellDataKey];//某一行的信息
            let rowDTO = new RowDTO();

            let rowDatum = jsonData.sheets["sheet1"].rowData[cellDataKey];//约束条件
            if (ObjectUtils.isEmpty(rowDatum)) {
                break;
            }
            rowDTO.height = await GljExcelOperateUtil.convertPxToRowHeight(rowDatum.h);
            rowDTO.rowNum = Number.parseInt(cellDataKey)+1;
            rowDTO.field = rowDatum.field;
            rowDTO.parentName = rowDatum.parentName;
            rowDTO.dataSourceType = rowDatum.dataSourceType;
            let statics = 0;
            for (let rowInfoKey in rowInfo) {
                if (statics== jsonData.sheets["sheet1"].columnCount) break;
                statics++;
                let cellDTO = new CellDTO();
                let cellInfo = rowInfo[rowInfoKey];//前台的数据
                if (ObjectUtils.isEmpty(cellInfo)) cellInfo = {};
                let cellStyle = await dataTemplate.convertCellUnitStyle(jsonData.styles[cellInfo.s],rowDTO.field);
                cellDTO.value = ObjectUtils.isNotEmpty(cellInfo.v)?cellInfo.v:"";
                if (typeof cellDTO.value == "string") {
                    cellDTO.value = cellDTO.value.replace(/`/g, '\"');
                }
                cellDTO.style = cellStyle;
                cellDTO.columnNum = Number.parseInt(rowInfoKey)+1;
                if (ObjectUtils.isNotEmpty(cellInfo.custom)&& ObjectUtils.isNotEmpty(cellInfo.custom.precision)) {
                    cellDTO.custom.precision = Number.parseInt(cellInfo.custom.precision);
                }
                cellDTO.styleId = cellInfo.s;
                rowDTO.cellInfoList.push(cellDTO);
            }
            dataTemplate.rowInfoList.push(rowDTO);
        }
        let columnNumLimit = 0;
        for (let columnDataKey in jsonData.sheets["sheet1"].columnData) {
            columnNumLimit++;
            if (columnNumLimit > jsonData.sheets["sheet1"].columnCount) {
                break;
            }
            let columnDTO = new ColumnDTO();
            columnDTO.colNum = Number.parseInt(columnDataKey)+1;
            let widthObject = jsonData.sheets["sheet1"].columnData[columnDataKey];
            columnDTO.width = await GljExcelOperateUtil.convertPxToColumnWidth(widthObject.w);
            dataTemplate.columnInfoList.push(columnDTO);
        }
        dataTemplate.columnNum = dataTemplate.columnInfoList.length;//一共多少列
        dataTemplate.rowNum = dataTemplate.rowInfoList.length;

        await dataTemplate.generateDataTemplate(worksheet);
        await dataTemplate.setColumnWidth(worksheet);
        await dataTemplate.dealWithTemplateMerge(worksheet);

        //将模板sheet和要生成的数据sheet进行分离
        // let workSheetTemplate = _.cloneDeep(worksheet);
        // await dataTemplate.dealWithMerge(worksheet);
        // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test-After.xlsx");
        //处理sheet  pageSetup属性的问题
        // let orientation = "portrait";
        // let top = GljExcelEnum.A4Top/72;
        // let bottom = GljExcelEnum.A4Bottom/72;
        // let left = GljExcelEnum.A4Left/72;
        // let right = GljExcelEnum.A4Right/72;
        // if (isLandScape) { //如果是横版
        //     orientation = "landscape";//较宽  横版
        //     top = GljExcelEnum.A4TopHorizontal/72;
        //     bottom = GljExcelEnum.A4BottomHorizontal/72;
        //     left = GljExcelEnum.A4LeftHorizontal/72;
        //     right = GljExcelEnum.A4RightHorizontal/72;
        // }

        // await DataRelation.prototype.adjust(worksheet);
        // if ( 'pdf' === type){
        // await this.service.gongLiaoJiProject.gljExportQueryService.adjustPdf(worksheet);
        await this.adjustPdf(worksheet);
        // }
        //----------excel模板生成结束--------------------------------------
        // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\test-Before.xlsx");
        await worksheet.workbook.xlsx.writeFile(filePath);
        //生成 dataTypeList 只针对数据行类型
    }

    async parseParams(params, project, single, unit, fileDir, args,taxCalculationMethodPath,lanMuName,workBookList) {
        if (args == null) {
            args = {};
        }
        args.lanMuName = lanMuName;
        if (params.levelType == projectLevelConstant.construct) {
            args["constructId"] = params.id;
        }else if (params.levelType == projectLevelConstant.single) {
            args["singleId"] = params.id;
        }else {
            args["unitId"] = params.id;
        }
        for (let i = 0; i < params.childrenList.length; i++) {
            let param = params.childrenList[i];
            //如果为总工程层级
            if (param.projectLevel != null && param.projectLevel == "project") {

                args["levelType"] = params.levelType;
                if (param.selected) {
                    args["id"] = param.id;
                    await this.getWorkSheetWithData(project, params.levelType, param.headLine, args);
                } else {
                    project.removeWorksheet(param.headLine);
                }
            }
            if (param.projectLevel != null && param.projectLevel == "single") {
                args["levelType"] = params.levelType;
                if (param.selected) {
                    args["id"] = param.id;
                    await this.getWorkSheetWithData(single, params.levelType, param.headLine, args);
                } else {
                    single.removeWorksheet(param.headLine);
                }
            }
            if (param.projectLevel != null && param.projectLevel == "unit") {
                args["levelType"] = params.levelType;

                //前面查询为了唯一把id换为单位id+报表id，这里重置为报表id
                if(String(param.id).includes("-")){
                    param.id = Number(param.id.split("-")[1]);
                }

                if(param.id==11){
                    param.headLine = '单位工程造价汇总表(省站标准) (2)';
                }
                if (param.selected) {
                    args["id"] = param.id;
                    args.ceping = param.ceping;
                    await this.getWorkSheetWithData(unit, params.levelType, param.headLine, args);
                } else {
                    unit.removeWorksheet(param.headLine);
                }
            }
        }

        //针对不同的workbook 生成该一层级的excel文件
        let filename = fileDir + "\\" + params.headLine + ".xlsx";

        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "project") {
            await this.resetOrderNo(params,project);
            if (project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push({sheet:project,fileDir:fileDir,filename:filename});
            }
        }
        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "single") {
            await this.resetOrderNo(params,single);
            if (single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push({sheet:single,fileDir:fileDir,filename:filename});
            }
        }
        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "unit") {
            await this.resetOrderNo(params,unit);
            if (unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push({sheet:unit,fileDir:fileDir,filename:filename});
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList!=null);//含有子节点的节点
        if (filter != null) {
            let directory;
            for (let i = 0; i < filter.length; i++) {
                //同时对single  和 unit对象进行初始化
                single = await this.initWorkBook(ProjectTypeConstants.PROJECT_TYPE_PROJECT,lanMuName,taxCalculationMethodPath);
                unit = await this.initWorkBook(ProjectTypeConstants.PROJECT_TYPE_UNIT,lanMuName,taxCalculationMethodPath);
                directory = fileDir +"\\"+filter[i].headLine;
                await this.parseParams(filter[i],project,single,unit,directory,args,taxCalculationMethodPath,lanMuName,workBookList);
            }
        }
    }

    async resetOrderNo(params,workbook) {
        let selectedSheet = params.childrenList.filter(item => item.selected).map(item => item.headLine);
        await this.removeRedundantWorkSheets(selectedSheet,workbook);
        let orderNo = 0;
        for (let i = 0; i < params.childrenList.length; i++) {
            let element = params.childrenList[i];
            if (element.selected) {
                orderNo++;
                let filterWorkSheet = workbook._worksheets.filter(item => item.name==element.headLine);
                if (ObjectUtils.isNotEmpty(filterWorkSheet)) {
                    filterWorkSheet[0].orderNo = orderNo;
                }
            }
        }
    }

    async removeRedundantWorkSheets(selectedSheet,workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (ObjectUtils.isNotEmpty(workbook._worksheets[i]) && !selectedSheet.includes(workbook._worksheets[i].name)) {
                await workbook.removeWorksheet(workbook._worksheets[i].name);
                await this.removeRedundantWorkSheets(selectedSheet,workbook);
            }
        }

    }


    async initWorkBook(projectLevel,lanMuName,taxCalculateMethod) {
        let loadDir = this.getProjectRootPath() + "\\excelTemplate\\glj\\"+taxCalculateMethod+"\\"+lanMuName;
        let loadPath = "";

        if (projectLevel == ProjectTypeConstants.PROJECT_TYPE_PROJECT && lanMuName=="省站标准") {
            loadPath = loadDir + "\\工程项目层级.xlsx";
        } else if (projectLevel == ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            loadPath = loadDir + "\\单位层级.xlsx";
        }
        if (ObjectUtils.isEmpty(loadPath)) return null;
        //加载workbook
        let workbook = await GljExcelUtil.readToWorkBook(loadPath);
        return workbook;
    }

    async getWorkSheetWithData(workbook, projectType, sheetName, args) {
        let worksheet = workbook.getWorksheet(sheetName);
        args["workbook"] = workbook;
        let constructObj = {
            "constructId":args.constructId,
            "singleId":args.singleId,
            "unitId":args.unitId
        }
        args["constructObj"] = constructObj;
        args["sheetName"] = sheetName;
        try {
            worksheet = await this.switchWorkSheet(projectType, worksheet, args);
            if (ObjectUtils.isNotEmpty(args['fileType']) && args['fileType'] == "pdf") {
                await this.adjustPdf(worksheet);
            }
        } catch (e) {
            console.log("报表填充数据异常" + sheetName);
        }
        return worksheet;
    }

    async adjustPdf(workSheet) {
        function roundUpToFiveDecimalPlaces(num) {
            // 乘以 10 的五次方，将小数部分移到整数部分
            const multiplier = 1000;

            // 向上舍入
            const roundedNum = Math.floor(num * multiplier) / multiplier;

            return roundedNum;
        }
        let rowBreakBeforeIndex = 0;
        let traverse = false;
        let contentHeight = 0;
        if (ObjectUtils.isNotEmpty(workSheet.pageSetup) && ObjectUtils.isNotEmpty(workSheet.pageSetup.orientation) && workSheet.pageSetup.orientation == "portrait") {
            contentHeight = GljExcelEnum.A4HeightPDF-(GljExcelEnum.A4Top/(GljExcelEnum.A4Height/GljExcelEnum.A4HeightPDF))-(GljExcelEnum.A4Bottom/(GljExcelEnum.A4Height/GljExcelEnum.A4HeightPDF));
        }else {  //如果为横版
            contentHeight = GljExcelEnum.A4HeightHorizontalPDF-(GljExcelEnum.A4TopHorizontal/(GljExcelEnum.A4HeightHorizontal/GljExcelEnum.A4HeightHorizontalPDF))-(GljExcelEnum.A4BottomHorizontal/(GljExcelEnum.A4HeightHorizontal/GljExcelEnum.A4HeightHorizontalPDF));
        }
        let heightRatio = 0.90;//根据表 单位工程费汇总表(省站标准)-多专业取费  字体缩放比例调整为0.9较为合理
        for (let i = 0; i < workSheet._rows.length; i++) {
            let row = workSheet._rows[i];
            for (let j = 0; j < row._cells.length; j++) {
                if (ObjectUtils.isNotEmpty(row._cells[j].style)&&(ObjectUtils.isNotEmpty(row._cells[j].style.font))) {
                    let styleObject = _.cloneDeep(row._cells[j].style);
                    styleObject.font.size = styleObject.font.size*heightRatio;
                    row._cells[j].style = {};
                    row._cells[j].style = styleObject;
                }
            }
        }
        for (let n = 0; n < workSheet.rowBreaks.length; n++) {
            traverse = true;
            let rowBreakIndex = workSheet.rowBreaks[n].id-1;
            let slice = workSheet._rows.slice(rowBreakBeforeIndex,rowBreakIndex+1);
            let rowTotalHeight = 0;
            for (let i = 0; i < slice.length; i++) {
                let rowCur = slice[i];
                rowTotalHeight+= rowCur.height;
            }
            rowBreakBeforeIndex = rowBreakIndex+1;
            let number = contentHeight-rowTotalHeight;
            let totalHeight = 0;
            for (let i = 0; i < slice.length-1; i++) {
                slice[i].height = await GljExcelOperateUtil.rowHeightToFixed((slice[i].height/rowTotalHeight)*number+slice[i].height);
                totalHeight+= slice[i].height;
            }
            slice[slice.length-1].height = roundUpToFiveDecimalPlaces(contentHeight-totalHeight);
        }
        if (traverse) {
            let index = workSheet.rowBreaks[workSheet.rowBreaks.length-1].id-1;
            let splice = workSheet._rows.slice(index+1,workSheet._rows.length);
            let rowTotalHeight = 0;
            for (let i = 0; i < splice.length; i++) {
                let rowCur = splice[i];
                rowTotalHeight +=rowCur.height;
            }
            let number = contentHeight-rowTotalHeight;
            let totalHeight = 0;
            for (let i = 0; i < splice.length-1; i++) {
                let rowCur = splice[i];
                rowCur.height = await GljExcelOperateUtil.rowHeightToFixed((rowCur.height/rowTotalHeight)*number+rowCur.height);
                totalHeight+= splice[i].height;
            }
            splice[splice.length-1].height = roundUpToFiveDecimalPlaces(contentHeight-totalHeight);
        }
        if (workSheet.rowBreaks.length == 0) {  //如果只有一页
            let sliceRows = workSheet._rows.slice(0,workSheet._rows.length);
            let rowTotalHeight = 0;
            for (let i = 0; i < sliceRows.length; i++) {
                let rowCur = sliceRows[i];
                rowTotalHeight +=rowCur.height;
            }
            let number = contentHeight-rowTotalHeight;
            let totalHeight = 0;
            for (let i = 0; i < sliceRows.length-1; i++) {
                let rowCur = sliceRows[i];
                rowCur.height = await GljExcelOperateUtil.rowHeightToFixed((rowCur.height/rowTotalHeight)*number+rowCur.height);
                totalHeight+= sliceRows[i].height;
            }
            sliceRows[sliceRows.length-1].height = roundUpToFiveDecimalPlaces(contentHeight-totalHeight);
        }


        //针对列宽
        let columnWidthTotal = 0;
        for (let i = 0; i < workSheet._columns.length; i++) {
            let columnWidth = workSheet._columns[i].width;
            columnWidthTotal += columnWidth;
        }
        let differ = 0;
        if (ObjectUtils.isNotEmpty(workSheet.pageSetup) && ObjectUtils.isNotEmpty(workSheet.pageSetup.orientation) && workSheet.pageSetup.orientation == "portrait") {
            differ = (GljExcelEnum.A4WidthPDF-GljExcelEnum.A4Left/(GljExcelEnum.A4Width/GljExcelEnum.A4WidthPDF)-GljExcelEnum.A4Right/(GljExcelEnum.A4Width/GljExcelEnum.A4WidthPDF));
        }else {
            differ = (GljExcelEnum.A4WidthHorizontalPDF-GljExcelEnum.A4LeftHorizontal/(GljExcelEnum.A4WidthHorizontal/GljExcelEnum.A4WidthHorizontalPDF)-GljExcelEnum.A4RightHorizontal/(GljExcelEnum.A4WidthHorizontal/GljExcelEnum.A4WidthHorizontalPDF));
        }
        // let number = differ - columnWidthTotal;
        let columnWidthTotal2 = 0;
        for (let i = 0; i < workSheet._columns.length-1; i++) {
            workSheet._columns[i].width = Number(((workSheet._columns[i].width/columnWidthTotal)*differ).toFixed(4));
            columnWidthTotal2+= workSheet._columns[i].width;
        }
        workSheet._columns[workSheet._columns.length-1].width = await GljExcelOperateUtil.colWidthToFixed(differ-columnWidthTotal2);
    }


    // "bd": {     边界
    //     "t": {
    //         "cl": {
    //             "rgb": "rgb(0,0,0)"
    //         },
    //         "s": 1
    //     },
    //     "b": {
    //         "cl": {
    //             "rgb": "rgb(0,0,0)"
    //         },
    //         "s": 1
    //     },
    //     "l": {
    //         "cl": {
    //             "rgb": "rgb(0,0,0)"
    //         },
    //         "s": 1
    //     },
    //     "r": null
    // },
    // "cl": {   	foreground
    //     "rgb": "rgb(0, 0, 0)"
    // },
    // "bg": {      background
    //     "rgb": "rgb(208,208,208)"
    // },
    // "fs": 10,    fontSize pt
    // "ht": 2,     horizontalAlignment   水平方向  CENTER 2   LEFT 1  RIGHT 3  UNSPECIFIED 0
    // "vt": 2,     vertical alignment     BOTTOM 3  MIDDLE 2    TOP 1    UNSPECIFIED 0
    // "bl": 0,     bold  0: false 1: true
    // "it": 0,     italic 0: false 1: true
    // "ff": "宋体", fontFamily
    // "tb": 1   3为换行处理   1溢出到下一个单元格  2截断处理


    // "p_tDmI": {
    //     "ul": {       //underline
    //         "s": 0,
    //         "cl": {
    //             "rgb": "rgb(255,0,0)"
    //         }
    //     },
    //     "st": {    //删除线
    //         "s": 0,
    //         "cl": {
    //             "rgb": "rgb(255,0,0)"
    //         }
    //     },
    //     "ol": {     //overline 覆盖线  上划线
    //         "s": 0,
    //         "cl": {
    //             "rgb": "rgb(255,0,0)"
    //         }
    //     },
    //     "tr": {     //文本倾斜角度 textRotation
    //         "a": 0,
    //         "v": 0
    //     },
    //     "td": 0,//文本方向
    //     "pd": {     //内边距
    //         "t": 0,
    //         "b": 1,
    //         "l": 2,
    //         "r": 2
    //     },
    // },
    async convertCellStyle(style) {
        let styleDesign = {};
        styleDesign['ff'] = ObjectUtils.isNotEmpty(style.font)?style.font.name:"宋体";   //为空给默认字体  ex:SimSun、宋体
        styleDesign['tb'] = 3;
        styleDesign['it'] = 0;//斜体
        styleDesign['bl'] = 0;//加粗
        styleDesign['vt'] = await this.getVerticalAlignment(style);
        styleDesign['ht'] = await this.getHorizontalAlignment(style);
        styleDesign['fs'] = ObjectUtils.isNotEmpty(style.font)?style.font.size:10;
        styleDesign['cl'] = {
            "rgb": "rgb(0, 0, 0)"   //全黑
        };
        styleDesign['bg'] = {
            "rgb": "rgb(255,255,255)"   //全白
        };
        //indexed 1 00FFFFFF  完全透明的白色   8 00000000   9  00FFFFFF
        styleDesign['bd'] = {
            "t":ObjectUtils.isNotEmpty(style.border)?await this.getBorderObject(style.border.top):null,
            "b":ObjectUtils.isNotEmpty(style.border)?await this.getBorderObject(style.border.bottom):null,
            "l":ObjectUtils.isNotEmpty(style.border)?await this.getBorderObject(style.border.left):null,
            "r":ObjectUtils.isNotEmpty(style.border)?await this.getBorderObject(style.border.right):null,
        };
        return styleDesign;
    }

    async getBorderObject(borderObject) {
        if (ObjectUtils.isEmpty(borderObject)) return null;
        return {
            "cl": {
                "rgb": "rgb(0,0,0)"
            },
            "s": 1
        }
    }

    async getVerticalAlignment(style) {
        if (ObjectUtils.isEmpty(style)||null == style.alignment) return null;
        //top 为1 middle 为2  bottom为3
        if (style.alignment.vertical == 'middle') {
            return 2;
        } else if (style.alignment.vertical == 'top') {
            return 1;
        }else if (style.alignment.vertical == 'bottom') {
            return 3;
        }
    }

    async getHorizontalAlignment(style) {
        if (ObjectUtils.isEmpty(style) || null == style.alignment) return null;
        // 值为2 为居中  值为1 为居左
        if (style.alignment.horizontal == 'center') {
            return 2;
        } else if (style.alignment.horizontal == 'left') {
            return 1;
        } else if (style.alignment.horizontal == 'right') {
            return 3;
        }
    }

    async generateRandomString(length,set) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        while (ObjectUtils.isEmpty(result)||set.has(result)) {
            result = '';
            for (let i = 0; i < length; i++) {
                const randomIndex = Math.floor(Math.random() * characters.length);
                result += characters.charAt(randomIndex);
            }
        }
        set.add(result);
        return result;
    }
    objectToString(obj) {
        // 处理基本数据类型
        if (typeof obj!== 'object' || obj === null) {
            return String(obj);
        }

        // 处理数组
        if (Array.isArray(obj)) {
            const elements = obj.map(item => this.objectToString(item)).join(', ');
            return `[${elements}]`;
        }

        // 处理普通对象
        const entries = Object.entries(obj);
        const keyValuePairs = entries.map(([key, value]) => `${key}=${this.objectToString(obj[key])}`).join(', ');
        return `{${keyValuePairs}}`;
    }

    //在当前页的页面总高
    async getTotalHeightInCurrentPage(rowIndex,map) {
        let result = 0;
        map.forEach((value, key) => {
                let split = key.split("+");
                let first = Number.parseFloat(split[0]);
                let last = Number.parseFloat(split[1]);
                if (rowIndex >= first && rowIndex <= last)
                    result = value;
            }
        )
        return result;
    }


    async calculatePagesHeight(workSheet) {
        let map = new Map();
        let rowBreakBeforeIndex = 0;
        for (let n = 0; n < workSheet.rowBreaks.length; n++) {
            let rowBreakIndex = workSheet.rowBreaks[n].id-1;
            let slice = workSheet._rows.slice(rowBreakBeforeIndex,rowBreakIndex+1);
            let rowTotalHeight = 0;
            for (let i = 0; i < slice.length; i++) {
                let rowCur = slice[i];
                rowTotalHeight+= rowCur.height;
            }
            map.set(rowBreakBeforeIndex+"+"+rowBreakIndex,rowTotalHeight);
            rowBreakBeforeIndex = rowBreakIndex+1;
        }
        if (ObjectUtils.isNotEmpty(workSheet.rowBreaks)) {
            let index = workSheet.rowBreaks[workSheet.rowBreaks.length-1].id-1;
            let splice = workSheet._rows.slice(index+1,workSheet._rows.length);
            let rowTotalHeight = 0;
            for (let i = 0; i < splice.length; i++) {
                let rowCur = splice[i];
                rowTotalHeight +=rowCur.height;
            }
            map.set((index+1)+"+"+(workSheet._rows.length-1),rowTotalHeight);
        }

        if (workSheet.rowBreaks.length == 0) {  //如果只有一页
            let sliceRows = workSheet._rows.slice(0,workSheet._rows.length);
            let rowTotalHeight = 0;
            for (let i = 0; i < sliceRows.length; i++) {
                let rowCur = sliceRows[i];
                rowTotalHeight +=rowCur.height;
            }
            map.set(0+"+"+(workSheet._rows.length-1),rowTotalHeight);
        }
        return map;
    }

    async dealWithWorkSheetToJsonData(worksheet,headLine) {
        let map = await this.calculatePagesHeight(worksheet);
        //转换cellData
        let styleMap = new Map();//存储 对象string----对象本身
        let styleObjectStringMap = new Map();//存储 对象string----对象styleId
        let styleIdSet = new Set();//用于判断生成的styleId 是否重复
        let cellData = {};
        for (let i = 0; i < worksheet._rows.length; i++) {
            let row = worksheet._rows[i];
            cellData[i] = {};
            for (let j = 0; j < row._cells.length; j++) {
                cellData[i][j] = {};
                let cell = row._cells[j];
                cellData[i][j]["v"] = cell.value;
                let styleObject = await this.convertCellStyle(cell.style);
                let objectString = this.objectToString(styleObject);
                if (styleMap.has(objectString)) {
                    let styleId = styleObjectStringMap.get(objectString);
                    cellData[i][j]["s"] = styleId;
                }else {
                    let newStyleId = await this.generateRandomString(6,styleIdSet);
                    cellData[i][j]["s"] = newStyleId;
                    styleMap.set(objectString,styleObject);
                    styleObjectStringMap.set(objectString,newStyleId);
                }
            }
        }
        //生成styles
        let styles = {};
        for (const [styleMapKey, value] of styleMap) {
            let styleId = styleObjectStringMap.get(styleMapKey);
            styles[styleId] = value
        }
        //将merge进行转换
        let mergeData = [];
        for (let mergesKey in worksheet._merges) {
            let merge = worksheet._merges[mergesKey];
            let mergeElement = {};
            mergeElement.startRow = merge.top-1;
            mergeElement.endRow = merge.bottom-1;
            mergeElement.startColumn = merge.left-1;
            mergeElement.endColumn = merge.right-1;
            mergeData.push(mergeElement);
        }
        //转换rowData
        let rowData = {};
        for (let i = 0; i < worksheet._rows.length; i++) {
            let row = worksheet._rows[i];
            let totalHeightPage = await this.getTotalHeightInCurrentPage(i,map);
            let ratio = (row.height/totalHeightPage);
            rowData[i] = {
                "h":11.69*110*ratio,
                // "h":(11.65-GljExcelEnum.A4Top/72-GljExcelEnum.A4Bottom/72)*96*ratio,
                // "h":await this.convertRowHeightToPx(row.height),
                // "hd"://hidden
                // "ah": 30,//auto height
                // "ia": ,//is current row self-adaptive to its content, use ah to set row height when true, else use h.
            };
        }
        //转换columnData
        let columnTotal = 0;
        for (let i = 0; i < worksheet._columns.length; i++) {
            let column = worksheet._columns[i];
            columnTotal +=column.width;
        }
        let columnData = {};
        for (let i = 0; i < worksheet._columns.length; i++) {
            let column = worksheet._columns[i];
            let differ = 0;
            if (headLine == "实体项目预算表(横)"||
                headLine == "措施项目预算表(横)"||
                headLine == "实体项目预算表(横-省站标准)"||
                headLine == "工程项目总价表-明细"||
                headLine == "单项工程费总价表-明细"
            ) {
                differ = (GljExcelEnum.A4WidthHorizontal-GljExcelEnum.A4LeftHorizontal-GljExcelEnum.A4RightHorizontal);
            }else {
                differ = (GljExcelEnum.A4Width-GljExcelEnum.A4Left-GljExcelEnum.A4Right);
            }
            columnData[i] = {
                //标准的PPI 为96px 每英寸
                "w": (column.width/columnTotal)*(differ*96/10),//width
                // "w": (column.width/columnTotal)*710,//width
                // "w": await this.convertColumnWidthToPx(column.width),//width
                "hd": 0  //hidden
            };
        }

        let excelDataTemplate = {};
        excelDataTemplate.id = "workbook-01";
        excelDataTemplate.sheetOrder = [
            "sheet1"
        ];
        excelDataTemplate.name = "universheet";
        excelDataTemplate.appVersion = "0.2.6";
        excelDataTemplate.locale = "zhCN";
        excelDataTemplate.styles = styles;
        excelDataTemplate.sheets = {};
        let sheet1 = {};
        excelDataTemplate.sheets.sheet1 = sheet1;
        sheet1.id = "sheet1";
        sheet1.cellData = cellData;
        sheet1.name = "Sheet1";
        sheet1.hidden = 0;
        sheet1.rowCount = worksheet._rows.length;
        sheet1.columnCount = worksheet._columns.length;
        sheet1.tabColor = "";
        sheet1.zoomRatio = 1;
        sheet1.freeze = {
            "startRow": -1,
            "startColumn": -1,
            "ySplit": 0,
            "xSplit": 0
        };
        sheet1.scrollTop = 0;
        sheet1.scrollLeft = 0;
        sheet1.defaultColumnWidth = 88;
        sheet1.defaultRowHeight = 30;
        sheet1.mergeData = mergeData;
        sheet1.rowData = rowData;
        sheet1.columnData = columnData;
        sheet1.showGridlines = 1;
        sheet1.rowHeader = {
            "width": 46,
            "hidden": 0
        };
        sheet1.columnHeader = {
            "height": 20,
            "hidden": 0
        };
        sheet1.selections = [
            "A1"
        ];
        sheet1.rightToLeft = 0;
        excelDataTemplate.headLine = headLine;
        excelDataTemplate.updateName = headLine;
        return excelDataTemplate;
    }

    //将px像素转化为行的单位 磅和  宽的单位 0.1英寸
    async convertColumnWidthToPx(width) {
        //A4纸的宽度总像素  (8.27 * ExcelEnum.screenPPI)   -GljExcelEnum.A4Left-GljExcelEnum.A4Right
        return width * (8.27 * GljExcelEnum.screenPPI)/(GljExcelEnum.A4Width);
        // return width;
        // static A4Height = (29.6/2.54)*72;//单位 磅;   839.06
        // static A4Width = (20.9/2.54)*10; //单位 0.1英寸   82.28
    }

    async convertRowHeightToPx(height) {
        //A4纸的高度总像素  (11.69英寸 * ExcelEnum.screenPPI)
        return await this.rowHeightToFixed(height*(11.69 * GljExcelEnum.screenPPI)/GljExcelEnum.A4Height);
    }

    async rowHeightToFixed(height) {
        return Number.parseFloat(height.toFixed(1));
    }

    async traverseTree(dataCs) {
        if (ObjectUtils.isNotEmpty(dataCs.children)) {
            for (let i = 0; i < dataCs.children.length; i++) {
                if (dataCs.children[i].awfType == 2) {   //说明为不可竞争措施项目
                    dataCs.children.splice(i,1);
                    await this.traverseTree(dataCs);
                }else {
                    await this.traverseTree(dataCs.children[i]);
                }
            }
        }
    }

    async switchWorkSheet(projectType, worksheet, args) {
        let {constructObj,workbook,lanMuName,id,ceping} = args;
        let constructId = constructObj.constructId;
        let singleId = constructObj.singleId;
        let unitId = constructObj.unitId;
        args.constructId = constructObj.constructId;
        args.singleId = constructObj.singleId;
        args.unitId = constructObj.unitId;

        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        args['precision'] = precision;
        let resultQuery = await this.service.gongLiaoJiProject.gljCommonService.getProjectSetting(args);
        let pricingMethod = resultQuery.get("PRICING_METHOD");//true为市场价组价
        args["pricingMethod"] = pricingMethod;
        if (projectType == ProjectLevelConstant.construct) {
            let constructProjectJBXX = await this.getconstructProjectJBXX(args);
            if (lanMuName == "省站标准") {
                switch (worksheet.name) {
                    //工程项目层级
                    case "封面"://end
                        await GljWriteExcelBySheetUtil.writeDataToProjectSheet1(constructProjectJBXX, worksheet, precision,args);
                        let headArgsQd1 = {};
                        headArgsQd1['headStartNum'] = 1;
                        headArgsQd1['headEndNum'] = 16;
                        headArgsQd1['titlePage'] = true;
                        await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1, args);
                        break;
                    case "封面_整数"://end
                        await GljWriteExcelBySheetUtil.writeDataToProjectSheet1ZhengShu(constructProjectJBXX, worksheet);
                        let headArgsQdIn = {};
                        headArgsQdIn['headStartNum'] = 1;
                        headArgsQdIn['headEndNum'] = 16;
                        headArgsQdIn['titlePage'] = true;
                        await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdIn, args);
                        break;
                    case "编制说明":
                        let constructBianZhi = constructProjectJBXX.filter(item => item.name=="编制说明")[0];
                        if (ObjectUtils.isNotEmpty(constructBianZhi) && ObjectUtils.isNotEmpty(constructBianZhi.remark)) {
                            let remark = await GljExcelUtil.removeTags(constructBianZhi.remark);
                            await GljWriteExcelBySheetUtil.writeDataToProjectSheet2(remark, worksheet);
                        }
                        let headArgsQd2 = {};
                        headArgsQd2['headStartNum'] = 1;
                        headArgsQd2['headEndNum'] = 16;
                        headArgsQd2['titlePage'] = true;
                        await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2, args);
                        break;
                    case "工程项目总价表"://end
                        let constructUnitSheet12List = await this.getProjectZjSheetData(args);
                        await GljExcelUtil.writeDataToSheet(constructUnitSheet12List,worksheet,null,1,null,args);
                        await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                        let functZj = function (data) {
                            let totalZj = 0;
                            let map = new Map();
                            for (let i = 0; i < data.length; i++) {
                                totalZj = NumberUtil.add(totalZj,data[i].projectCost);
                            }
                            // map.set(4,NumberUtil.numberScale2(totalZj));
                            map.set(4,totalZj);
                            return map;
                        }
                        await GljExcelUtil.fillTotalContent(worksheet,functZj(constructUnitSheet12List),"合　　计");
                        break;
                    case "工程项目总价表-明细"://end
                        let constructUnitSheetDetail = await this.getProjectZjSheetDetail(args);
                        await GljExcelUtil.writeDataToSheet(constructUnitSheetDetail,worksheet,null,1,null,args);
                        await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                        let functProjectZj = function (data) {
                            let totalZj = 0;
                            let map = new Map();
                            for (let i = 0; i < data.length; i++) {
                                totalZj = NumberUtil.add(totalZj,data[i].projectCost);
                            }
                            // map.set(4,NumberUtil.numberScale2(totalZj));
                            map.set(4,totalZj);
                            return map;
                        }
                        await GljExcelUtil.fillTotalContent(worksheet,functProjectZj(constructUnitSheetDetail),"合　　计");

                        break;
                    case "单项工程费总价表"://end
                        let constructUnitList = await this.getSingleZjSheetData(args);
                        await GljExcelUtil.writeDataToSheet(constructUnitList,worksheet,null,1,null,args);
                        await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                        let functSingleZj = function (data) {
                            let totalZj = 0;
                            let map = new Map();
                            for (let i = 0; i < data.length; i++) {
                                if (data[i].levelType != projectLevelConstant.unit) {
                                    continue;
                                }
                                totalZj = NumberUtil.add(totalZj,data[i].projectCost);
                            }
                            map.set(4,totalZj);
                            return map;
                        }
                        await GljExcelUtil.fillTotalContent(worksheet,functSingleZj(constructUnitList),"合　　计");
                        break;
                    case "单项工程费总价表-明细"://end
                        let constructSingleList = await this.getSingleZjSheetDetail(args);
                        await GljExcelUtil.writeDataToSheet(constructSingleList,worksheet,null,1,null,args);
                        // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                        await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                        let functSingleDetailZj = function (data) {
                            let totalZj = 0;
                            let map = new Map();
                            for (let i = 0; i < data.length; i++) {
                                if (data[i].levelType != projectLevelConstant.unit) {
                                    continue;
                                }
                                totalZj = NumberUtil.add(totalZj,data[i].projectCost);
                            }
                            map.set(4,totalZj);
                            return map;
                        }
                        await GljExcelUtil.fillTotalContent(worksheet,functSingleDetailZj(constructSingleList),"合　　计");
                        break;
                    case "单项工程三材汇总表"://end
                        let dataSanCai = await this.getSanCaiList(args);
                        await GljExcelUtil.writeDataToSheet(dataSanCai, worksheet,null,1,null,args);
                        let funct = function (data) {
                            let 钢材Hj = 0;
                            let 钢筋Hj = 0;
                            let 木材Hj = 0;
                            let 水泥Hj = 0;
                            let map = new Map();
                            for (let i = 0; i < data.length; i++) {
                                if (data[i].levelType != ProjectLevelConstant.unit) {
                                    continue;
                                }
                                钢材Hj = NumberUtil.add(钢材Hj,data[i].钢材);
                                钢筋Hj = NumberUtil.add(钢筋Hj,data[i].其中钢筋);
                                木材Hj = NumberUtil.add(木材Hj,data[i].木材);
                                水泥Hj = NumberUtil.add(水泥Hj,data[i].水泥);
                            }
                            map.set(4,钢材Hj);
                            map.set(5,钢筋Hj);
                            map.set(7,木材Hj);
                            map.set(8,水泥Hj);
                            return map;
                        }
                        await GljExcelUtil.fillTotalContentPrecision(worksheet,funct(dataSanCai),"合计",args.precision.COST_ANALYSIS.scNumber);
                        await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                        break;
                    case "安全生产、文明施工费汇总表"://end
                        let precision9 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId);
                        let constructSummary = await this.getconstructProjectSheet8List(args);
                        if(ObjectUtils.isNotEmpty(constructSummary)){
                            for(let item of constructSummary){
                                if(ObjectUtils.isNotEmpty(item.aqwmsgf)){
                                    item.aqwmsgf = NumberUtil.numberScale(item.aqwmsgf,precision9.COST_SUMMARY.awf);
                                }
                            }
                        }
                        await GljExcelUtil.writeDataToSheet(constructSummary,worksheet,null,1,null,args);
                        // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                        await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                        let functAwf = function (data) {
                            let totalZj = 0;
                            let map = new Map();
                            for (let i = 0; i < data.length; i++) {
                                if (data[i].levelType != projectLevelConstant.unit) {
                                    continue;
                                }
                                totalZj = NumberUtil.add(totalZj,data[i].aqwmsgf);
                            }
                            map.set(4,totalZj);
                            return map;
                        }
                        await GljExcelUtil.fillTotalContent(worksheet,functAwf(constructSummary),"合计");
                        break;
                }
            }
        }
        if (projectType == ProjectLevelConstant.unit) {
            let constructUnitJBXX = await this.getconstructProjectSingleUnitJBXX(args);
            if (ObjectUtils.isEmpty(ceping)) {
                if (lanMuName == "通用") {
                    switch (worksheet.name) {
                        //单位工程层级
                        case "封面"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitSheet1(constructUnitJBXX, worksheet, precision, args);
                            let headArgsQd1 = {};
                            headArgsQd1['headStartNum'] = 1;
                            headArgsQd1['headEndNum'] = 13;
                            headArgsQd1['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1, args);
                            break;
                        case "封面_整数"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitSheet1QuZheng(constructUnitJBXX, worksheet);
                            let headArgsQdZs = {};
                            headArgsQdZs['headStartNum'] = 1;
                            headArgsQdZs['headEndNum'] = 13;
                            headArgsQdZs['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdZs, args);
                            break;
                        case "编制说明":
                            let unitBzsm = constructUnitJBXX.filter(object => object.name == "编制说明")[0];
                            if (ObjectUtils.isNotEmpty(unitBzsm) && ObjectUtils.isNotEmpty(unitBzsm.remark)) {
                                let remark = await GljExcelUtil.removeTags(unitBzsm.remark);
                                await GljWriteExcelBySheetUtil.writeDataToProjectSheet4(remark, worksheet);
                            }
                            let headArgsQd2 = {};
                            headArgsQd2['headStartNum'] = 1;
                            headArgsQd2['headEndNum'] = 16;
                            headArgsQd2['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2, args);
                            break;
                        case "单位工程费用表"://end
                            let boolResultSingle = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            if ((ObjectUtils.isNotEmpty(boolResultSingle) && boolResultSingle)) {  //true 单专业  false 多专业汇总    为空默认为多专业
                                worksheet = workbook.getWorksheet("单位工程费用表(多专业取费)");//为单专业时采用这张表的格式
                                let orderNoBak = worksheet.orderNo;
                                workbook.removeWorksheet("单位工程费用表");
                                worksheet = await GljExcelUtil.addWorkSheet(workbook, worksheet, "单位工程费用表");
                                let constructUnitSheetMulti = await this.getUnitSheetSingleqfMajorType(args);
                                await GljExcelUtil.writeDataToSheet(constructUnitSheetMulti, worksheet, 3, null, null, args);
                                worksheet.orderNo = orderNoBak;
                                await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            } else {  //多专业走这里
                                // let constructUnitSheet5List = await this.getconstructUnitSheet5List(args);
                                let constructUnitSheet5List = await this.getconstructUnitSheetSummary(args);
                                await GljExcelUtil.writeDataToSheet(constructUnitSheet5List, worksheet, 3, null, null, args);
                                await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            }

                            let headArgsQd5 = {};
                            headArgsQd5['headStartNum'] = 1;
                            headArgsQd5['headEndNum'] = 4;
                            headArgsQd5['startPage'] = args['startPage'];
                            headArgsQd5['totalPage'] = args['totalPage'];
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5, args);
                            break;
                        case "单位工程费用表(多专业取费)"://end
                            let boolResult = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            if ((ObjectUtils.isNotEmpty(boolResult) && boolResult)) {  //true 单专业  false 多专业汇总
                                await GljExcelUtil.writeDataToSheet([], worksheet, 3, null, null, args);
                                let headArgsQdMulti = {};
                                headArgsQdMulti['headStartNum'] = 1;
                                headArgsQdMulti['headEndNum'] = 4;
                                await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdMulti, args);
                                break;
                            }
                            let constructUnitSheetMulti = await this.getconstructUnitSheet6List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetMulti, worksheet, 3, null, null, args);
                            let headArgsQdMulti = {};
                            headArgsQdMulti['headStartNum'] = 1;
                            headArgsQdMulti['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdMulti, args);
                            await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "实体项目预算表(竖)"://end
                            let precision6 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);

                            let deLists = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let data = (_.cloneDeep(deLists));
                            await this.setTreeDispNoShiTi(data);
                            data = data.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, data);
                            data = data.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项

                            for (let hengElement of data) {
                                if (ObjectUtils.isNotEmpty(hengElement.quantity)) {
                                    //精度设置
                                    if (hengElement.isDeResource === 1) {
                                        hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, precision6.EDIT.DERCJ.quantity);
                                    } else {
                                        hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, precision6.EDIT.DE.quantity);
                                    }
                                }
                                if (ObjectUtils.isNotEmpty(hengElement.price)) {
                                    hengElement.price = NumberUtil.numberScale(hengElement.price, precision6.EDIT.DE.price);
                                }
                                if (ObjectUtils.isNotEmpty(hengElement.baseJournalPrice)) {
                                    hengElement.baseJournalPrice = NumberUtil.numberScale(hengElement.baseJournalPrice, precision6.EDIT.DE.price);
                                }
                            }
                            await this.filterProperties(data, precision);
                            await GljExcelUtil.writeDataToSheet(data, worksheet, null, 1, null, {"pricingMethod": pricingMethod}, null, args);
                            let funct = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj = NumberUtil.add(totalNumberHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber),precision6.EDIT.DE.price));
                                    rTotalSumHj = NumberUtil.add(rTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum),precision6.EDIT.DE.price));
                                    cTotalSumHj = NumberUtil.add(cTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum),precision6.EDIT.DE.price));
                                    jTotalSumHj = NumberUtil.add(jTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum),precision6.EDIT.DE.price));
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funct(data), "合 计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            // await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "实体项目预算表(横)"://end
                            let precision7 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);

                            //该条定额下，类型为人工，单位为工日的人材机数据 数量合计
                            let deListsHeng = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataHeng = _.cloneDeep(deListsHeng);
                            await this.setTreeDispNoShiTi(dataHeng);
                            dataHeng = dataHeng.filter(item => item.type != "0");//将单位工程行去掉;
                            await this.addConversionStr(args, dataHeng);
                            dataHeng = dataHeng.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataHeng.length; i++) {
                                let hengElement = dataHeng[i];
                                hengElement.工日合计 = 0;
                                let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, hengElement.sequenceNbr, hengElement.type);
                                if (ObjectUtils.isNotEmpty(rcjLists)) {
                                    let required = rcjLists.filter(item => item.type == "人工费" && item.unit == "工日");
                                    for (let j = 0; j < required.length; j++) {
                                        if (hengElement.type == BranchProjectLevelConstant.top || BranchProjectLevelConstant.fb === hengElement.type || BranchProjectLevelConstant.zfb === hengElement.type) {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        } else {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        }
                                    }
                                }

                                if (ObjectUtils.isNotEmpty(hengElement.quantity)) {
                                    //精度设置
                                    if (hengElement.isDeResource === 1) {
                                        hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, 3);
                                    } else {
                                        hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, 3);
                                    }
                                }
                                if (ObjectUtils.isNotEmpty(hengElement.price)) {
                                    hengElement.price = NumberUtil.numberScale(hengElement.price, precision7.EDIT.DE.price);
                                }
                                if (ObjectUtils.isNotEmpty()) {
                                    hengElement.baseJournalPrice = NumberUtil.numberScale(hengElement.baseJournalPrice, precision7.EDIT.DE.price);
                                }
                            }
                            await this.filterProperties(dataHeng, precision);
                            await GljExcelUtil.writeDataToSheet(dataHeng, worksheet, null, 1, null, {"pricingMethod": pricingMethod}, null, args);
                            let headArgsQdH = {};
                            headArgsQdH['headStartNum'] = 1;
                            headArgsQdH['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdH, args);
                            let functHeng = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let grTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj = NumberUtil.add(totalNumberHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber),precision7.EDIT.DE.price));
                                    rTotalSumHj = NumberUtil.add(rTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum),precision7.EDIT.DE.price));
                                    cTotalSumHj = NumberUtil.add(cTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum),precision7.EDIT.DE.price));
                                    jTotalSumHj = NumberUtil.add(jTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum),precision7.EDIT.DE.price));
                                    if (data[i].isDeResource === 1) {
                                        grTotalSumHj = NumberUtil.add(grTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(ObjectUtils.isNotEmpty(data[i].工日合计) ? data[i].工日合计 : 0),3));
                                    } else {
                                        grTotalSumHj = NumberUtil.add(grTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(ObjectUtils.isNotEmpty(data[i].工日合计) ? data[i].工日合计 : 0),3));
                                    }
                                }
                                map.set(8, {"amount": totalNumberHj});
                                map.set(10, {"amount": rTotalSumHj});
                                map.set(11, {"amount": cTotalSumHj});
                                map.set(12, {"amount": jTotalSumHj});
                                //工日合计
                                map.set(13, {"amount": GljExcelUtil._roundAndPadCeping(Number(grTotalSumHj),3), "precisionReserve": true});
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functHeng(dataHeng), "合 计");
                            break;
                        case "实体项目预算表(自然单位)"://end
                            // 定义一个函数来提取数字和单位
                        function extractNumberAndUnit(str) {
                            if (ObjectUtils.isEmpty(str)) {
                                str = "";
                            }
                            // 使用正则表达式匹配数字和单位
                            const match = str.match(/^(-?\d+(\.\d+)?)\s*([a-zA-Z0-9\u3000-\u303F\uFF00-\uFFEF\u2000-\u206F\u4E00-\u9FFFA-Z]+)/);
                            if (match) {
                                const number = match[1]; // 数字部分
                                const unit = match[3]; // 单位部分
                                return {number, unit}; // 返回一个对象
                            } else {
                                return {number: 1, unit: str}; // 如果没有匹配到，返回 null
                            }
                        }

                            let deListsNature = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataNature = _.cloneDeep(deListsNature);
                            await this.setTreeDispNoShiTi(dataNature);
                            dataNature = dataNature.filter(item => item.type != "0");//将单位工程行去掉;
                            await this.addConversionStr(args, dataNature);
                            dataNature = dataNature.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataNature.length; i++) {
                                let natureElement = dataNature[i];
                                if (!GljExcelUtil.judgeIsDeType(natureElement)
                                    ) continue;
                                let unit = extractNumberAndUnit(natureElement.unit).unit;
                                natureElement.quantity = natureElement.quantity * (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.price = natureElement.price / (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.baseJournalPrice = natureElement.baseJournalPrice / (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.unit = unit;
                                //精度设置
                                if (natureElement.isDeResource === 1) {
                                    natureElement.quantity = NumberUtil.numberScale(natureElement.quantity, precision.EDIT.DERCJ.quantity);
                                } else {
                                    natureElement.quantity = NumberUtil.numberScale(natureElement.quantity, precision.EDIT.DE.quantity);
                                }
                                natureElement.price = NumberUtil.numberScale(natureElement.price, precision.EDIT.DE.price);
                                natureElement.baseJournalPrice = NumberUtil.numberScale(natureElement.baseJournalPrice, precision.EDIT.DE.price);

                            }
                            await this.filterProperties(dataNature, precision);
                            await GljExcelUtil.writeDataToSheet(dataNature, worksheet, null, 1, null, {"pricingMethod": pricingMethod});
                            let headArgsQdSt = {};
                            headArgsQdSt['headStartNum'] = 1;
                            headArgsQdSt['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdSt, args);
                            let functNature = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber;
                                    rTotalSumHj += pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum;
                                    cTotalSumHj += pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum;
                                    jTotalSumHj += pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum;
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functNature(dataNature), "合 计");
                            break;
                        case "措施项目预算表(竖)"://end
                            // let csLists = ProjectDomain.getDomain(constructId).csxmDomain.getDeAllTreeDepth(constructId,unitId);
                            let csLists = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCs = _.cloneDeep(csLists);
                            let treeCs = xeUtils.toArrayTree(dataCs, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            dataCs = await this.setDispNoAndPrice(treeCs[0], pricingMethod);
                            await this.dealBkjzcsxmData(dataCs);
                            await this.addConversionStr(args, dataCs);
                            dataCs = dataCs.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await GljExcelUtil.writeDataToSheet(dataCs, worksheet, null, 1, null, {"pricingMethod": pricingMethod});
                            let headArgsQdCs = {};
                            headArgsQdCs['headStartNum'] = 1;
                            headArgsQdCs['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdCs, args);
                            let funcHeng = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcHeng(dataCs), "合 计");
                            break;
                        case "措施项目预算表(横)"://end
                            //该条定额下，类型为人工，单位为工日的人材机数据 数量合计
                            // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                            let deCsHeng = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataHengCs = _.cloneDeep(deCsHeng);
                            let treeHengCs = xeUtils.toArrayTree(dataHengCs, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            dataHengCs = await this.setDispNoAndPrice(treeHengCs[0], pricingMethod);
                            await this.dealBkjzcsxmData(dataHengCs);
                            await this.addConversionStr(args, dataHengCs);
                            dataHengCs = dataHengCs.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataHengCs.length; i++) {
                                let hengElement = dataHengCs[i];
                                hengElement.工日合计 = 0;
                                let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, hengElement.sequenceNbr, hengElement.type);
                                if (ObjectUtils.isNotEmpty(rcjLists)) {
                                    let required = rcjLists.filter(item => item.type == "人工费" && item.unit == "工日");
                                    for (let j = 0; j < required.length; j++) {
                                        if (hengElement.type == BranchProjectLevelConstant.top || BranchProjectLevelConstant.fb === hengElement.type || BranchProjectLevelConstant.zfb === hengElement.type) {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        } else {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        }
                                    }
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataHengCs, worksheet, null, 1, null, {"pricingMethod": pricingMethod});
                            let headArgsQdHeng = {};
                            headArgsQdHeng['headStartNum'] = 1;
                            headArgsQdHeng['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdHeng, args);
                            let functHengCs = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let grTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                    grTotalSumHj += ObjectUtils.isNotEmpty(data[i].工日合计) ? data[i].工日合计 : 0;
                                }
                                map.set(8, {"amount": totalNumberHj});
                                map.set(10, {"amount": rTotalSumHj});
                                map.set(11, {"amount": cTotalSumHj});
                                map.set(12, {"amount": jTotalSumHj});
                                //工日合计
                                map.set(13, {"amount": GljExcelUtil._roundAndPadCeping(Number(grTotalSumHj),3), "precisionReserve": true});
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functHengCs(dataHengCs), "合 计");
                            break;
                        case "措施项目预算表(自然单位)"://end
                            // 定义一个函数来提取数字和单位
                            // let deListsNatureCs = ProjectDomain.getDomain(constructId).csxmDomain.getDeAllTreeDepth(constructId,unitId);
                            let deListsNatureCs = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataNatureCs = _.cloneDeep(deListsNatureCs);
                            let treeNatureCs = xeUtils.toArrayTree(dataNatureCs, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            dataNatureCs = await this.setDispNoAndPrice(treeNatureCs[0], pricingMethod);
                            await this.dealBkjzcsxmData(dataNatureCs);
                            await this.addConversionStr(args, dataNatureCs);
                            dataNatureCs = dataNatureCs.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataNatureCs.length; i++) {
                                let natureElement = dataNatureCs[i];
                                if (!GljExcelUtil.judgeIsDeType(natureElement)
                                ) continue;
                                let unit = extractNumberAndUnit(natureElement.unit).unit;
                                natureElement.quantity = natureElement.quantity * (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.price = NumberUtil.numberScale2(natureElement.price / (extractNumberAndUnit(natureElement.unit).number));
                                natureElement.baseJournalPrice = NumberUtil.numberScale2(natureElement.baseJournalPrice / (extractNumberAndUnit(natureElement.unit).number));
                                natureElement.unit = unit;
                            }
                            await GljExcelUtil.writeDataToSheet(dataNatureCs, worksheet, null, 1, null, {"pricingMethod": pricingMethod});
                            let headArgsQdNature = {};
                            headArgsQdNature['headStartNum'] = 1;
                            headArgsQdNature['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdNature, args);
                            let functNatureCs = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functNatureCs(dataNatureCs), "合 计");
                            break;
                        case "措施项目预算表(不含安文费)":
                            let csListsNotContainAwf = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCsNotContainAwf = _.cloneDeep(csListsNotContainAwf);
                            let treeNotContainAwf = xeUtils.toArrayTree(dataCsNotContainAwf, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.traverseTree(treeNotContainAwf[0]);
                            await this.resetPrice(treeNotContainAwf[0], pricingMethod);
                            dataCsNotContainAwf = xeUtils.toTreeArray(treeNotContainAwf);
                            await this.addConversionStr(args, dataCsNotContainAwf);
                            dataCsNotContainAwf = dataCsNotContainAwf.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await this.filterPropertiesCs(dataCsNotContainAwf, precision);

                            dataCsNotContainAwf = dataCsNotContainAwf.filter(item => !(item.type == BranchProjectLevelConstant.top || item.type == BranchProjectLevelConstant.zfb ||
                                item.type == BranchProjectLevelConstant.fb)
                            );

                            await GljExcelUtil.writeDataToSheet(dataCsNotContainAwf, worksheet, null, 1, null, args);
                            let headArgsNotContainAwf = {};
                            headArgsNotContainAwf['headStartNum'] = 1;
                            headArgsNotContainAwf['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsNotContainAwf, args);
                            let funcNotContainAwf = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].totalNumber) ? data[0].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[0].baseJournalTotalNumber) ? data[0].baseJournalTotalNumber : 0);
                                rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].rTotalSum) ? data[0].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].rdTotalSum) ? data[0].rdTotalSum : 0);
                                cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].cTotalSum) ? data[0].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].cdTotalSum) ? data[0].cdTotalSum : 0);
                                jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].jTotalSum) ? data[0].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].jdTotalSum) ? data[0].jdTotalSum : 0);
                                map.set(7, totalNumberHj);
                                map.set(9, rTotalSumHj);
                                map.set(10, cTotalSumHj);
                                map.set(11, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcNotContainAwf(dataCsNotContainAwf), "合　计");
                            break;
                        case "独立费表"://end
                            let constructUnitSheet13List = await this.getconstructUnitSheet13List(args);
                            let treeData = xeUtils.toArrayTree(constructUnitSheet13List, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            let toTreeArray = xeUtils.toTreeArray(treeData);
                            await GljExcelUtil.writeDataToSheet(toTreeArray, worksheet, 3, null, null, args);
                            let headArgsQd13 = {};
                            headArgsQd13['headStartNum'] = 1;
                            headArgsQd13['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13, args);
                            break;
                        case "单位工程人材机汇总表"://end
                            let constructUnitSheet8List = await this.getconstructUnitSheet8List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet8List, worksheet, 3, 1, null, args);
                            let headArgsQd = {};
                            headArgsQd['headStartNum'] = 1;
                            headArgsQd['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd, args);
                            let functRcj = function (data) {
                                let totalNumberHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (data[i].materialName != "小计") {
                                        continue;
                                    }
                                    totalNumberHj += data[i].totalMarket;
                                }
                                map.set(9, totalNumberHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functRcj(constructUnitSheet8List), "合计");
                            break;
                        case "单位工程人材机价差表"://end
                            let constructUnitSheet9List = await this.getconstructUnitSheet9List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet9List.data, worksheet, 3, 1, null, args);
                            const func = (constructUnitSheet9List) => {
                                let map = new Map();
                                map.set(10, constructUnitSheet9List.total);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, func(constructUnitSheet9List), "合计");
                            let headArgsQd9 = {};
                            headArgsQd9['headStartNum'] = 1;
                            headArgsQd9['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "小计");
                            break;
                        case "单位工程甲供材料表"://end
                            let resultQuery = await this.service.gongLiaoJiProject.gljCommonService.getProjectSetting(args);
                            let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
                            let pricingMethodLocal = ProjectDomain.getDomain(constructId).getRoot().pricingMethod;
                            let isGeneral = true;
                            if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                                isGeneral = false;
                            }
                            //更改模板的字段显示
                            //一般计税，非市场价组价，显示的就是不含税基期价，市场价组价，显示的是不含税市场价
                            //简易计税，非市场价组价，显示的是含税基期价，市场价组价，显示的是含税市场价
                            if (isGeneral) {
                                if (pricingMethod) { //为市场价组价
                                    await GljExcelUtil.updateSheetValue(worksheet, "不含税市场价", "不含税市场价");
                                } else {
                                    await GljExcelUtil.updateSheetValue(worksheet, "不含税市场价", "不含税基期价");
                                }
                            } else {
                                if (pricingMethod) { //为市场价组价
                                    await GljExcelUtil.updateSheetValue(worksheet, "含税市场价", "含税市场价");
                                } else {
                                    await GljExcelUtil.updateSheetValue(worksheet, "含税市场价", "含税基期价");
                                }
                            }
                            args.kind = 0;
                            args.levelType = 3;
                            let allData = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
                            if (ObjectUtils.isEmpty(allData)) {
                                allData = []
                            }
                            //1为甲供
                            let jiaGong = _.cloneDeep(allData.filter(item => item.ifDonorMaterial == 1));
                            for (let i = 0; i < jiaGong.length; i++) {
                                if (ObjectUtils.isNotEmpty(jiaGong[i].specification)) {
                                    jiaGong[i].materialName = jiaGong[i].materialName + "  " + jiaGong[i].specification;
                                }
                                jiaGong[i].unit = await this.getDisplayUnit(jiaGong[i]);

                                if (taxMethod === RcjCommonConstants.SIMPLE_REVERSE && pricingMethodLocal === 0) {
                                    jiaGong[i].marketPrice = jiaGong[i].baseJournalTaxPrice;
                                }
                                if (taxMethod === RcjCommonConstants.SIMPLE_REVERSE && pricingMethodLocal === 1) {
                                    jiaGong[i].marketPrice = jiaGong[i].marketTaxPrice;
                                }
                                if (taxMethod === RcjCommonConstants.GENERAL_FORWARD && pricingMethodLocal === 0) {
                                    jiaGong[i].marketPrice = jiaGong[i].baseJournalPrice;
                                }
                                if (taxMethod === RcjCommonConstants.GENERAL_FORWARD && pricingMethodLocal === 1) {
                                    jiaGong[i].marketPrice = jiaGong[i].marketPrice;
                                }
                                jiaGong[i].total = NumberUtil.multiply(NumberUtil.numberScale(jiaGong[i].marketPrice, args.precision.RCJ_COLLECT.marketPrice), NumberUtil.numberScale(jiaGong[i].donorMaterialNumber, args.precision.RCJ_COLLECT.donorMaterialNumber));
                            }
                            await GljExcelUtil.writeDataToSheet(jiaGong, worksheet, 3, 1, null, args);
                            let functNatureCl = function (data) {
                                let jgHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    jgHj += NumberUtil.numberScale(data[i].total, args.precision.RCJ_COLLECT.total);
                                }
                                map.set(6, jgHj);
                                return map;
                            };
                            await GljExcelUtil.fillTotalContent(worksheet, functNatureCl(jiaGong), "合计");
                            let headArgsJia = {};
                            headArgsJia['headStartNum'] = 1;
                            headArgsJia['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsJia, args);
                            break;
                        case "单位工程三材汇总表"://end
                            let sanCaiData = await this.service.gongLiaoJiProject.gljRcjCollectService.getScCountList(constructId, singleId, unitId);
                            let sanCaiDataBak = _.cloneDeep(sanCaiData);
                            await GljExcelUtil.writeDataToSheet(sanCaiDataBak, worksheet, 3, null, null, args);
                            let headArgsQd8 = {};
                            headArgsQd8['headStartNum'] = 1;
                            headArgsQd8['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8, args);
                            break;
                        case "单位工程主材表"://end
                            args.kind = 5;
                            let constructUnitSheetZc = await this.getconstructUnitSheet12List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetZc, worksheet, 3, null, null, args);
                            let headArgsZc = {};
                            headArgsZc['headStartNum'] = 1;
                            headArgsZc['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsZc, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "单位工程设备表"://end
                            args.kind = 4;
                            let constructUnitSheetSb = await this.getconstructUnitSheet12List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetSb, worksheet, 3, null, null, args);
                            let headArgsSb = {};
                            headArgsSb['headStartNum'] = 1;
                            headArgsSb['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsSb, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "预拌砼汇总表"://end
                            args.kind = 6;
                            let constructUnitSheet12List = await this.getconstructUnitSheet12List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet12List, worksheet, 3, null, null, args);
                            let headArgsTong = {};
                            headArgsTong['headStartNum'] = 1;
                            headArgsTong['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsTong, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "安全文明施工费明细表"://end
                            let constructSummary = await this.getAwfUnitData(args);
                            await GljExcelUtil.writeDataToSheet(constructSummary, worksheet, 3, null, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "单位工程预算表(竖)"://end
                            let deListsUnit = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataShu = (_.cloneDeep(deListsUnit));
                            await this.setTreeDispNoShiTi(dataShu);
                            dataShu = dataShu.filter(item => item.type != "0");//将单位工程行去掉

                            // let csListsUnit = ProjectDomain.getDomain(constructId).csxmDomain.getDeAllTreeDepth(constructId,unitId);
                            let csListsUnit = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            csListsUnit = _.cloneDeep(csListsUnit);
                            let treeCsShu = xeUtils.toArrayTree(csListsUnit, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            csListsUnit = await this.setDispNoAndPrice(treeCsShu[0], pricingMethod);
                            dataShu.push(...(csListsUnit));
                            await this.addConversionStr(args, dataShu);
                            dataShu = dataShu.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await GljExcelUtil.writeDataToSheet(dataShu, worksheet, null, 1, null, {"pricingMethod": pricingMethod});
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            let funcUnit = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnit(dataShu), "合计");
                            break;
                        case "分项工程人材机汇总表(实体)"://end
                            let dataShiTiSummary = await this.getRcjSummaryShiTi(args);
                            for (let i = 0; i < dataShiTiSummary.length; i++) {
                                let element = dataShiTiSummary[i];
                                if (ObjectUtils.isNotEmpty(element.type) && GljExcelUtil.judgeIsDeType(element)) {
                                    if (ObjectUtils.isNotEmpty(element.resQty) && NumberUtil.numberScale2(element.resQty) == 0) {
                                        element.resQty = null;
                                    }
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataShiTiSummary, worksheet, null, 1, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            let funcUnitShiTi = function (data) {
                                let totalHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (ObjectUtils.isNotEmpty(data[i].type) &&
                                        (data[i].type == BranchProjectLevelConstant.de
                                            || data[i].type == DeTypeConstants.DE_TYPE_RESOURCE
                                            || data[i].type == DeTypeConstants.DE_TYPE_USER_DE
                                            || data[i].type == DeTypeConstants.DE_TYPE_USER_RESOURCE
                                            || data[i].type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                                            || data[i].type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE))
                                    {
                                        totalHj += data[i].totalNumber;
                                    }
                                }
                                map.set(10, totalHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitShiTi(dataShiTiSummary), "合 计");
                            break;
                        case "分项工程人材机汇总表(措施)"://end
                            let dataCuoShiSummary = await this.getRcjSummaryCuoShi(args);
                            await GljExcelUtil.writeDataToSheet(dataCuoShiSummary, worksheet, null, 1, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            let funcUnitCuoShi = function (data) {
                                let totalHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalHj += data[i].totalNumber;
                                }
                                map.set(10, totalHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitCuoShi(dataCuoShiSummary), "合 计");
                            break;
                        case "措施项目计价表"://end
                            let deCsJiJia = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataHengJiJia = _.cloneDeep(deCsJiJia);
                            let arrayTree = xeUtils.toArrayTree(dataHengJiJia, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.statDingEPrcing(arrayTree[0], pricingMethod, precision);
                            await this.setTreeDispNo(arrayTree[0], 1);
                            dataHengJiJia = xeUtils.toTreeArray(arrayTree);
                            dataHengJiJia = dataHengJiJia.filter(item => (item.type == BranchProjectLevelConstant.top || item.type == BranchProjectLevelConstant.zfb ||
                                item.type == BranchProjectLevelConstant.fb || item.type == BranchProjectLevelConstant.qd))
                            await GljExcelUtil.writeDataToSheet(dataHengJiJia, worksheet, 3, 1, null, args);
                            let headArgsJiJia = {};
                            headArgsJiJia['headStartNum'] = 1;
                            headArgsJiJia['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsJiJia, args);
                            let funcUnitJi = function (data) {
                                let totalNumberHj = data.dingEPriceTotal;
                                let map = new Map();
                                map.set(5, Number(totalNumberHj));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitJi(arrayTree[0]), "合计");
                            break;
                        case "单位工程工程量计算书(实体)"://end
                            let deListsShiTi = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataShiTi = (_.cloneDeep(deListsShiTi));
                            dataShiTi = xeUtils.toTreeArray(dataShiTi);
                            await this.setTreeDispNoShiTi(dataShiTi);
                            dataShiTi = dataShiTi.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, dataShiTi);
                            dataShiTi = dataShiTi.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataShiTi.length; i++) {
                                let dataShiTiElement = dataShiTi[i];
                                if (GljExcelUtil.judgeIsDeType(dataShiTiElement)) {
                                    let expression = "";
                                    let result = await this.getQuantityDetails({
                                        constructId: constructId,
                                        unitId: unitId,
                                        deId: dataShiTiElement.sequenceNbr
                                    });
                                    result = result.filter(item => ObjectUtils.isNotEmpty(item.accumulateFlag) && ObjectUtils.isNotEmpty(item.mathFormula) && item.accumulateFlag == 1);
                                    for (let j = 0; j < result.length; j++) {
                                        if (j == result.length - 1) {
                                            expression += result[j].mathFormula;
                                        } else {
                                            expression += result[j].mathFormula + "+";
                                        }
                                    }
                                    if (ObjectUtils.isNotEmpty(expression)) {
                                        dataShiTiElement.quantityExpression += "=" + expression;
                                    }
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DE.quantity);
                                }
                                if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DERCJ.quantity);
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataShiTi, worksheet, null, null, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "单位工程工程量计算书(措施)"://end
                            let csListsCal = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCsCal = _.cloneDeep(csListsCal);
                            dataCsCal = xeUtils.toArrayTree(dataCsCal, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.setTreeDispNo(dataCsCal[0], 1);
                            await this.resetPrice(dataCsCal[0], pricingMethod);
                            dataCsCal = xeUtils.toTreeArray(dataCsCal);
                            await this.addConversionStr(args, dataCsCal);
                            dataCsCal = dataCsCal.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataCsCal.length; i++) {
                                let dataShiTiElement = dataCsCal[i];
                                if (dataShiTiElement.type == BranchProjectLevelConstant.qd) {
                                    dataShiTiElement.quantityExpression = null;
                                }
                                if (GljExcelUtil.judgeIsDeType(dataShiTiElement)) {
                                    let expression = "";
                                    let result = await this.getQuantityDetails({
                                        constructId: constructId,
                                        unitId: unitId,
                                        deId: dataShiTiElement.sequenceNbr
                                    });
                                    result = result.filter(item => ObjectUtils.isNotEmpty(item.accumulateFlag) && ObjectUtils.isNotEmpty(item.mathFormula) && item.accumulateFlag == 1);
                                    for (let j = 0; j < result.length; j++) {
                                        if (j == result.length - 1) {
                                            expression += result[j].mathFormula;
                                        } else {
                                            expression += result[j].mathFormula + "+";
                                        }
                                    }
                                    if (ObjectUtils.isNotEmpty(expression)) {
                                        dataShiTiElement.quantityExpression += "=" + expression;
                                    }
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DE.quantity);
                                }
                                if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DERCJ.quantity);
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataCsCal, worksheet, null, null, null, args);
                            let headArgsQdCal = {};
                            headArgsQdCal['headStartNum'] = 1;
                            headArgsQdCal['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdCal, args);
                            break;
                        case "主要材料价格表"://end
                            let constructUnitSheet14List = await this.getconstructUnitSheet14List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet14List, worksheet, 3, null, null, args);
                            let headArgsQd14 = {};
                            headArgsQd14['headStartNum'] = 1;
                            headArgsQd14['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd14, args);
                            break;
                        case "水电费明细表":
                            // let result = await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.getWaterElectricCost({
                            //     constructId: constructId,
                            //     singleId: singleId,
                            //     unitId: unitId
                            // });
                            let sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
                            let dataWrite = [];
                            if (ObjectUtils.isNotEmpty(sdfData) && ObjectUtils.isNotEmpty(sdfData[unitId])) {
                                sdfData = _.cloneDeep(sdfData[unitId]);
                                dataWrite = sdfData.waterElectricData.filter(item => ObjectUtils.isEmpty(item.children) && (ObjectUtils.isNotEmpty(item.totalCost) || ObjectUtils.isNotEmpty(item.waterCost) || ObjectUtils.isNotEmpty(item.electricCost)))
                            }
                            await GljExcelUtil.writeDataToSheet(dataWrite, worksheet, null, 1, null, args);
                            let funcUnitJiSd = function (data) {
                                let waterCostTotal = 0;
                                let electricCostTotal = 0;
                                let totalCostTotal = 0;
                                for (let i = 0; i < data.length; i++) {
                                    let datum = data[i];
                                    waterCostTotal = NumberUtil.add(waterCostTotal, datum.waterCost);
                                    electricCostTotal = NumberUtil.add(electricCostTotal, datum.electricCost);
                                    totalCostTotal = NumberUtil.add(totalCostTotal, datum.totalCost);
                                }
                                let map = new Map();
                                map.set(8, NumberUtil.numberScale2(waterCostTotal));
                                map.set(9, NumberUtil.numberScale2(electricCostTotal));
                                map.set(10, NumberUtil.numberScale2(totalCostTotal));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitJiSd(dataWrite), "合计");
                            let headSdf = {};
                            headSdf['headStartNum'] = 1;
                            headSdf['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headSdf, args);
                            break;
                        case "水电费明细表(独立设置)":
                            let sdfDataIndependent = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
                            let dataIndependent = [];
                            if (ObjectUtils.isNotEmpty(sdfDataIndependent) && ObjectUtils.isNotEmpty(sdfDataIndependent[unitId]) && sdfDataIndependent[unitId].customWaterElectricFlag === true) {
                                dataIndependent.push({
                                    "totalCost": GljExcelUtil._roundAndPad(sdfDataIndependent[unitId].customWaterElectric,precision.COST_SUMMARY.jqsdf),
                                });
                            }
                            await GljExcelUtil.writeDataToSheet(dataIndependent, worksheet, null, null, null, args);
                            let headIndependent = {};
                            headIndependent['headStartNum'] = 1;
                            headIndependent['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headIndependent, args);
                            break;
                        default:
                    }
                }
                if (lanMuName == "省站标准") {
                    switch (worksheet.name) {
                        //单位工程层级
                        case "封面(省站标准)"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitShengZhanCover(constructUnitJBXX, worksheet, precision, args);
                            let headArgsQd1 = {};
                            headArgsQd1['headStartNum'] = 1;
                            headArgsQd1['headEndNum'] = 15;
                            headArgsQd1['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1, args);
                            break;
                        case "封面(省站标准)_整数"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitShengZhanCoverQuZheng(constructUnitJBXX, worksheet);
                            let headArgsQdInteger = {};
                            headArgsQdInteger['headStartNum'] = 1;
                            headArgsQdInteger['headEndNum'] = 16;
                            headArgsQdInteger['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdInteger, args);
                            break;
                        case "单位工程造价汇总表(省站标准)"://end
                            let constructUnitSheetZhan = await this.getconstructUnitSheetShengZhan10(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetZhan, worksheet, null, 1, id, args);
                            let functZhan = function (data) {
                                let totalPrice = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let zcSbSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    totalPrice += Number(data[i].price);
                                    rTotalSumHj += Number(data[i].rgf);
                                    cTotalSumHj += Number(data[i].clf);
                                    jTotalSumHj += Number(data[i].jxf);
                                    zcSbSumHj += Number(data[i].zcSbf);
                                }
                                map.set(3, totalPrice);
                                map.set(4, rTotalSumHj);
                                map.set(5, cTotalSumHj);
                                map.set(7, jTotalSumHj);
                                map.set(8, zcSbSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functZhan(constructUnitSheetZhan), "合计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "单位工程造价汇总表(省站标准) (2)"://end
                            let constructUnitSheetMulti = await this.getconstructUnitSheetShengZhan11(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetMulti, worksheet, 3, null, id, args);
                            let headArgsQdMulti = {};
                            headArgsQdMulti['headStartNum'] = 1;
                            headArgsQdMulti['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdMulti, args);
                            // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                            await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "单位工程造价汇总表"://end
                            let constructUnitSheetSummary = await this.getconstructUnitSheetSummary(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetSummary, worksheet, 3, null, null, args);
                            let headArgsQdSummary = {};
                            headArgsQdSummary['headStartNum'] = 1;
                            headArgsQdSummary['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdSummary, args);
                            await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "单位工程费汇总表(省站标准)-多专业取费"://end
                            let boolResult = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            if ((ObjectUtils.isNotEmpty(boolResult) && !boolResult) || ObjectUtils.isEmpty(boolResult)) {  //true 单专业  false 多专业汇总
                                let arrayName = [];
                                let data = await this.getconstructUnitSheetMultiQuFei(args, arrayName);
                                await GljExcelUtil.writeDataToSheet(data, worksheet, 3, null, null, args);
                                let headArgsQdSummary = {};
                                headArgsQdSummary['headStartNum'] = 1;
                                headArgsQdSummary['headEndNum'] = 4;
                                await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdSummary, args);
                                let rowNumList = [];
                                for (let i = 0; i < arrayName.length; i++) {
                                    let nameElement = arrayName[i];
                                    let rowNum = await GljExcelUtil.findRowNumOnlySingleValueForPointRange(worksheet, nameElement, 1, 7);
                                    rowNumList.push(rowNum);
                                }
                                await GljExcelUtil.mergeRowsForPointNumListAndCol(worksheet, rowNumList, 1, 7);
                            }
                            break;
                        case "单位工程费汇总表(省站标准)-单专业取费"://end
                            let boolResultSingle = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            let result = []
                            if (ObjectUtils.isNotEmpty(boolResultSingle) && boolResultSingle) {  //true 单专业  false 多专业汇总
                                result = await this.getconstructUnitSheetSummary222(args);
                            }
                            await GljExcelUtil.writeDataToSheet(result, worksheet, 3, null, null, args);
                            let headArgsQd = {};
                            headArgsQd['headStartNum'] = 1;
                            headArgsQd['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd, args);
                            break;
                        case "实体项目预算表(竖-省站标准)"://end
                            let deLists = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let data = (_.cloneDeep(deLists));
                            await this.setTreeDispNoShiTi(data);
                            data = data.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, data);
                            data = data.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < data.length; i++) {
                                let dataShiTiElement = data[i];
                                if (dataShiTiElement.type == BranchProjectLevelConstant.de || dataShiTiElement.type == DeTypeConstants.DE_TYPE_RESOURCE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DE.quantity);
                                }
                                if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DERCJ.quantity);
                                }
                            }
                            await this.filterProperties(data, precision);
                            await GljExcelUtil.writeDataToSheet(data, worksheet, null, 1, null, args);
                            let funct = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber;
                                    rTotalSumHj += pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum;
                                    cTotalSumHj += pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum;
                                    jTotalSumHj += pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum;
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funct(data), "合　计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "实体项目预算表(横-省站标准)"://end
                            let deListsH = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataH = (_.cloneDeep(deListsH));
                            await this.setTreeDispNoShiTi(dataH);
                            dataH = dataH.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, dataH);
                            dataH = dataH.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await this.filterProperties(dataH, precision);
                            await GljExcelUtil.writeDataToSheet(dataH, worksheet, null, 1, null, args);
                            let functH = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber;
                                    rTotalSumHj += pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum;
                                    cTotalSumHj += pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum;
                                    jTotalSumHj += pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum;
                                }
                                map.set(11, totalNumberHj);
                                map.set(12, rTotalSumHj);
                                map.set(13, cTotalSumHj);
                                map.set(14, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functH(dataH), "合　计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "措施项目预算表(省站标准)"://end
                            let csLists = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCs = _.cloneDeep(csLists);
                            let tree = xeUtils.toArrayTree(dataCs, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.resetPrice(tree[0], pricingMethod);
                            dataCs = xeUtils.toTreeArray(tree);
                            await this.dealBkjzcsxmData(dataCs);
                            await this.addConversionStr(args, dataCs);
                            dataCs = dataCs.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await this.filterPropertiesCs(dataCs, precision);
                            await GljExcelUtil.writeDataToSheet(dataCs, worksheet, null, 1, null, args);
                            let headArgsQdCs = {};
                            headArgsQdCs['headStartNum'] = 1;
                            headArgsQdCs['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdCs, args);
                            let funcHeng = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].totalNumber) ? data[0].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[0].baseJournalTotalNumber) ? data[0].baseJournalTotalNumber : 0);
                                rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].rTotalSum) ? data[0].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].rdTotalSum) ? data[0].rdTotalSum : 0);
                                cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].cTotalSum) ? data[0].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].cdTotalSum) ? data[0].cdTotalSum : 0);
                                jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].jTotalSum) ? data[0].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].jdTotalSum) ? data[0].jdTotalSum : 0);
                                map.set(7, Number(totalNumberHj));
                                map.set(9, Number(rTotalSumHj));
                                map.set(10, Number(cTotalSumHj));
                                map.set(11, Number(jTotalSumHj));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcHeng(dataCs), "合　计");
                            break;
                        case "措施项目预算表(省站标准-不含安文费)":
                            let csListsNotContainAwf = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCsNotContainAwf = _.cloneDeep(csListsNotContainAwf);
                            let treeNotContainAwf = xeUtils.toArrayTree(dataCsNotContainAwf, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.traverseTree(treeNotContainAwf[0]);
                            await this.resetPrice(treeNotContainAwf[0], pricingMethod);
                            dataCsNotContainAwf = xeUtils.toTreeArray(treeNotContainAwf);
                            await this.addConversionStr(args, dataCsNotContainAwf);
                            dataCsNotContainAwf = dataCsNotContainAwf.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await this.filterPropertiesCs(dataCsNotContainAwf, precision);
                            await GljExcelUtil.writeDataToSheet(dataCsNotContainAwf, worksheet, null, 1, null, args);
                            let headArgsNotContainAwf = {};
                            headArgsNotContainAwf['headStartNum'] = 1;
                            headArgsNotContainAwf['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsNotContainAwf, args);
                            let funcNotContainAwf = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].totalNumber) ? data[0].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[0].baseJournalTotalNumber) ? data[0].baseJournalTotalNumber : 0);
                                rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].rTotalSum) ? data[0].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].rdTotalSum) ? data[0].rdTotalSum : 0);
                                cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].cTotalSum) ? data[0].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].cdTotalSum) ? data[0].cdTotalSum : 0);
                                jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].jTotalSum) ? data[0].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].jdTotalSum) ? data[0].jdTotalSum : 0);
                                map.set(7, Number(totalNumberHj));
                                map.set(9, Number(rTotalSumHj));
                                map.set(10, Number(cTotalSumHj));
                                map.set(11, Number(jTotalSumHj));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcNotContainAwf(dataCsNotContainAwf), "合　计");
                            break;
                        case "人工、材料、机械台班(用量、单价)汇总表(省站标准)"://end
                            let constructUnitSheetYongLiang = await this.getconstructUnitSheetYongLiang(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetYongLiang, worksheet, 3, 1, null, args);
                            let headArgsQdYongLiang = {};
                            headArgsQdYongLiang['headStartNum'] = 1;
                            headArgsQdYongLiang['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdYongLiang, args);
                            let numList = [];
                            let rj = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "人工");
                            let cl = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "材料");
                            let zc = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "主材");
                            let jx = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "机械");
                            let sb = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "设备");
                            numList.push(rj, cl, zc, jx, sb);
                            await GljExcelUtil.mergeRowsForPointNumList(worksheet, numList);
                            let funcHj = function (data) {
                                let totalMarket = 0;
                                let priceDifferTotal = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (data[i].materialName == "小计") {
                                        totalMarket += Number(data[i].totalMarket);
                                        priceDifferTotal += Number(data[i].priceDifferTotal);
                                    }
                                }
                                map.set(9, totalMarket);
                                map.set(10, priceDifferTotal);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcHj(constructUnitSheetYongLiang), "合计");
                            break;
                        default:
                    }
                }
            } else {
                if (lanMuName == "省站标准(测评响应)" || lanMuName == "通用(测评响应)") {
                    switch (worksheet.name) {
                        //单位工程层级
                        case "封面(省站标准)"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitShengZhanCover(constructUnitJBXX, worksheet, precision, args);
                            let headArgsQd1 = {};
                            headArgsQd1['headStartNum'] = 1;
                            headArgsQd1['headEndNum'] = 15;
                            headArgsQd1['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1, args);
                            break;
                        case "封面(省站标准)_整数"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitShengZhanCoverQuZheng(constructUnitJBXX, worksheet);
                            let headArgsQdInteger = {};
                            headArgsQdInteger['headStartNum'] = 1;
                            headArgsQdInteger['headEndNum'] = 16;
                            headArgsQdInteger['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdInteger, args);
                            break;
                        case "单位工程造价汇总表(省站标准)"://end
                            let constructUnitSheetZhan = await this.getconstructUnitSheetShengZhan10(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetZhan, worksheet, null, 1, id, args);
                            let functZhan = function (data) {
                                let totalPrice = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let zcSbSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    totalPrice += Number(data[i].price);
                                    rTotalSumHj += Number(data[i].rgf);
                                    cTotalSumHj += Number(data[i].clf);
                                    jTotalSumHj += Number(data[i].jxf);
                                    zcSbSumHj += Number(data[i].zcSbf);
                                }
                                map.set(3, totalPrice);
                                map.set(4, rTotalSumHj);
                                map.set(5, cTotalSumHj);
                                map.set(7, jTotalSumHj);
                                map.set(8, zcSbSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functZhan(constructUnitSheetZhan), "合计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "单位工程造价汇总表(省站标准) (2)"://end
                            let constructUnitSheetMulti = await this.getconstructUnitSheetShengZhan11(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetMulti, worksheet, 3, null, id, args);
                            let headArgsQdMulti = {};
                            headArgsQdMulti['headStartNum'] = 1;
                            headArgsQdMulti['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdMulti, args);
                            // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                            await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "单位工程造价汇总表"://end
                            let constructUnitSheetSummary = await this.getconstructUnitSheetSummary(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetSummary, worksheet, 3, null, null, args);
                            let headArgsQdSummary = {};
                            headArgsQdSummary['headStartNum'] = 1;
                            headArgsQdSummary['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdSummary, args);
                            await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "单位工程费汇总表(省站标准)-多专业取费"://end
                            let boolResult = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            if ((ObjectUtils.isNotEmpty(boolResult) && !boolResult) || ObjectUtils.isEmpty(boolResult)) {  //true 单专业  false 多专业汇总
                                let arrayName = [];
                                let data = await this.getconstructUnitSheetMultiQuFei(args, arrayName);
                                await GljExcelUtil.writeDataToSheet(data, worksheet, 3, null, null, args);
                                let headArgsQdSummary = {};
                                headArgsQdSummary['headStartNum'] = 1;
                                headArgsQdSummary['headEndNum'] = 4;
                                await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdSummary, args);
                                let rowNumList = [];
                                for (let i = 0; i < arrayName.length; i++) {
                                    let nameElement = arrayName[i];
                                    let rowNum = await GljExcelUtil.findRowNumOnlySingleValueForPointRange(worksheet, nameElement, 1, 7);
                                    rowNumList.push(rowNum);
                                }
                                await GljExcelUtil.mergeRowsForPointNumListAndCol(worksheet, rowNumList, 1, 7);
                            }
                            break;
                        case "单位工程费汇总表(省站标准)-单专业取费"://end
                            let boolResultSingle = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            let result = []
                            if (ObjectUtils.isNotEmpty(boolResultSingle) && boolResultSingle) {  //true 单专业  false 多专业汇总
                                result = await this.getconstructUnitSheetSummary222(args);
                            }
                            await GljExcelUtil.writeDataToSheet(result, worksheet, 3, null, null, args);
                            let headArgsQd = {};
                            headArgsQd['headStartNum'] = 1;
                            headArgsQd['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd, args);
                            break;
                        case "实体项目预算表(竖-省站标准)"://end
                            let deLists = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let data = (_.cloneDeep(deLists));
                            await this.setTreeDispNoShiTi(data);
                            data = data.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, data);
                            data = data.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < data.length; i++) {
                                let dataShiTiElement = data[i];
                                if (dataShiTiElement.type == BranchProjectLevelConstant.de || dataShiTiElement.type == DeTypeConstants.DE_TYPE_RESOURCE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DE.quantity);
                                }
                                if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DERCJ.quantity);
                                }
                            }
                            await this.filterProperties(data, precision);
                            await GljExcelUtil.writeDataToSheet(data, worksheet, null, 1, null, args);
                            let funct = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber;
                                    rTotalSumHj += pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum;
                                    cTotalSumHj += pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum;
                                    jTotalSumHj += pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum;
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funct(data), "合　计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "实体项目预算表(横-省站标准)"://end
                            let deListsH = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataH = (_.cloneDeep(deListsH));
                            await this.setTreeDispNoShiTi(dataH);
                            dataH = dataH.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, dataH);
                            dataH = dataH.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await this.filterProperties(dataH, precision);
                            await GljExcelUtil.writeDataToSheet(dataH, worksheet, null, 1, null, args);
                            let functH = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber;
                                    rTotalSumHj += pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum;
                                    cTotalSumHj += pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum;
                                    jTotalSumHj += pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum;
                                }
                                map.set(11, totalNumberHj);
                                map.set(12, rTotalSumHj);
                                map.set(13, cTotalSumHj);
                                map.set(14, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functH(dataH), "合　计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "措施项目预算表(省站标准)"://end
                            let csLists = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCs = _.cloneDeep(csLists);
                            let tree = xeUtils.toArrayTree(dataCs, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.resetPrice(tree[0], pricingMethod);
                            dataCs = xeUtils.toTreeArray(tree);
                            await this.dealBkjzcsxmData(dataCs);
                            await this.addConversionStr(args, dataCs);
                            dataCs = dataCs.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            dataCs = dataCs.filter(item => item.deName != "不可竞争措施项目" && item.deName != "可竞争措施项目");//过滤不可竞争/可竞争

                            if (ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod) {
                                dataCs = dataCs.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.totalNumber) && item.totalNumber > 0));//过滤计取项为0的
                            } else {
                                dataCs = dataCs.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.baseJournalTotalNumber) && item.baseJournalTotalNumber > 0));//过滤计取项为0的
                            }

                            await this.filterPropertiesCs(dataCs, precision);

                            if (ObjectUtils.isNotEmpty(dataCs)) {
                                let i = 1;
                                for (let item of dataCs) {
                                    if (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.deCode)) {
                                        item.deCode = i++;
                                    }
                                }
                            }

                            await GljExcelUtil.writeDataToSheet(dataCs, worksheet, null, 1, null, args);
                            let headArgsQdCs = {};
                            headArgsQdCs['headStartNum'] = 1;
                            headArgsQdCs['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdCs, args);
                            let funcHeng = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].totalNumber) ? data[0].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[0].baseJournalTotalNumber) ? data[0].baseJournalTotalNumber : 0);
                                rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].rTotalSum) ? data[0].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].rdTotalSum) ? data[0].rdTotalSum : 0);
                                cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].cTotalSum) ? data[0].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].cdTotalSum) ? data[0].cdTotalSum : 0);
                                jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].jTotalSum) ? data[0].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].jdTotalSum) ? data[0].jdTotalSum : 0);
                                map.set(7, Number(totalNumberHj));
                                map.set(9, Number(rTotalSumHj));
                                map.set(10, Number(cTotalSumHj));
                                map.set(11, Number(jTotalSumHj));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcHeng(dataCs), "合　计");
                            break;
                        case "措施项目预算表(省站标准-不含安文费)":
                            let csListsNotContainAwf = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCsNotContainAwf = _.cloneDeep(csListsNotContainAwf);
                            let treeNotContainAwf = xeUtils.toArrayTree(dataCsNotContainAwf, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.traverseTree(treeNotContainAwf[0]);
                            await this.resetPrice(treeNotContainAwf[0], pricingMethod);
                            dataCsNotContainAwf = xeUtils.toTreeArray(treeNotContainAwf);
                            await this.addConversionStr(args, dataCsNotContainAwf);
                            dataCsNotContainAwf = dataCsNotContainAwf.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            dataCsNotContainAwf = dataCsNotContainAwf.filter(item => item.deName != "不可竞争措施项目" && item.deName != "可竞争措施项目");//过滤不可竞争/可竞争

                            if (ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod) {
                                dataCsNotContainAwf = dataCsNotContainAwf.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.totalNumber) && item.totalNumber > 0));//过滤计取项为0的
                            } else {
                                dataCsNotContainAwf = dataCsNotContainAwf.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.baseJournalTotalNumber) && item.baseJournalTotalNumber > 0));//过滤计取项为0的
                            }

                            await this.filterPropertiesCs(dataCsNotContainAwf, precision);

                            if (ObjectUtils.isNotEmpty(dataCsNotContainAwf)) {
                                let i = 1;
                                for (let item of dataCsNotContainAwf) {
                                    if (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.deCode)) {
                                        item.deCode = i++;
                                    }
                                }
                            }

                            await GljExcelUtil.writeDataToSheet(dataCsNotContainAwf, worksheet, null, 1, null, args);
                            let headArgsNotContainAwf = {};
                            headArgsNotContainAwf['headStartNum'] = 1;
                            headArgsNotContainAwf['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsNotContainAwf, args);
                            let funcNotContainAwf1 = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].totalNumber) ? data[0].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[0].baseJournalTotalNumber) ? data[0].baseJournalTotalNumber : 0);
                                rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].rTotalSum) ? data[0].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].rdTotalSum) ? data[0].rdTotalSum : 0);
                                cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].cTotalSum) ? data[0].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].cdTotalSum) ? data[0].cdTotalSum : 0);
                                jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].jTotalSum) ? data[0].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].jdTotalSum) ? data[0].jdTotalSum : 0);
                                map.set(7, Number(totalNumberHj));
                                map.set(9, Number(rTotalSumHj));
                                map.set(10, Number(cTotalSumHj));
                                map.set(11, Number(jTotalSumHj));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcNotContainAwf1(dataCsNotContainAwf), "合　计");
                            break;
                        case "人工、材料、机械台班(用量、单价)汇总表(省站标准)"://end
                            let constructUnitSheetYongLiang = await this.getconstructUnitSheetYongLiang(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetYongLiang, worksheet, 3, 1, null, args);
                            let headArgsQdYongLiang = {};
                            headArgsQdYongLiang['headStartNum'] = 1;
                            headArgsQdYongLiang['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdYongLiang, args);
                            let numList = [];
                            let rj = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "人工");
                            let cl = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "材料");
                            let zc = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "主材");
                            let jx = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "机械");
                            let sb = await GljExcelUtil.findRowNumOnlySingleValue(worksheet, "设备");
                            numList.push(rj, cl, zc, jx, sb);
                            await GljExcelUtil.mergeRowsForPointNumList(worksheet, numList);
                            let funcHj = function (data) {
                                let totalMarket = 0;
                                let priceDifferTotal = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (data[i].materialName == "小计") {
                                        totalMarket += Number(data[i].totalMarket);
                                        priceDifferTotal += Number(data[i].priceDifferTotal);
                                    }
                                }
                                map.set(9, totalMarket);
                                map.set(10, priceDifferTotal);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcHj(constructUnitSheetYongLiang), "合计");
                            break;
                        //单位工程层级
                        case "封面"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitSheet1(constructUnitJBXX, worksheet, precision, args);
                            let headArgsQd11 = {};
                            headArgsQd11['headStartNum'] = 1;
                            headArgsQd11['headEndNum'] = 13;
                            headArgsQd11['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd11, args);
                            break;
                        case "封面_整数"://end
                            await GljWriteExcelBySheetUtil.writeDataToUnitSheet1QuZheng(constructUnitJBXX, worksheet);
                            let headArgsQdZs = {};
                            headArgsQdZs['headStartNum'] = 1;
                            headArgsQdZs['headEndNum'] = 13;
                            headArgsQdZs['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdZs, args);
                            break;
                        case "编制说明":
                            let unitBzsm = constructUnitJBXX.filter(object => object.name == "编制说明")[0];
                            if (ObjectUtils.isNotEmpty(unitBzsm) && ObjectUtils.isNotEmpty(unitBzsm.remark)) {
                                let remark = await GljExcelUtil.removeTags(unitBzsm.remark);
                                await GljWriteExcelBySheetUtil.writeDataToProjectSheet4(remark, worksheet);
                            }
                            let headArgsQd2 = {};
                            headArgsQd2['headStartNum'] = 1;
                            headArgsQd2['headEndNum'] = 16;
                            headArgsQd2['titlePage'] = true;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2, args);
                            break;
                        case "单位工程费用表"://end
                            let boolResultSingle1 = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            if ((ObjectUtils.isNotEmpty(boolResultSingle1) && boolResultSingle1)) {  //true 单专业  false 多专业汇总    为空默认为多专业
                                worksheet = workbook.getWorksheet("单位工程费用表(多专业取费)");//为单专业时采用这张表的格式
                                let orderNoBak = worksheet.orderNo;
                                workbook.removeWorksheet("单位工程费用表");
                                worksheet = await GljExcelUtil.addWorkSheet(workbook, worksheet, "单位工程费用表");
                                let constructUnitSheetMulti = await this.getUnitSheetSingleqfMajorType(args);
                                await GljExcelUtil.writeDataToSheet(constructUnitSheetMulti, worksheet, 3, null, null, args);
                                worksheet.orderNo = orderNoBak;
                                await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            } else {  //多专业走这里
                                // let constructUnitSheet5List = await this.getconstructUnitSheet5List(args);
                                let constructUnitSheet5List = await this.getconstructUnitSheetSummary(args);
                                await GljExcelUtil.writeDataToSheet(constructUnitSheet5List, worksheet, 3, null, null, args);
                                await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            }

                            let headArgsQd5 = {};
                            headArgsQd5['headStartNum'] = 1;
                            headArgsQd5['headEndNum'] = 4;
                            headArgsQd5['startPage'] = args['startPage'];
                            headArgsQd5['totalPage'] = args['totalPage'];
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5, args);
                            break;
                        case "单位工程费用表(多专业取费)"://end
                            let boolResult1 = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args);
                            if ((ObjectUtils.isNotEmpty(boolResult1) && boolResult1)) {  //true 单专业  false 多专业汇总
                                await GljExcelUtil.writeDataToSheet([], worksheet, 3, null, null, args);
                                let headArgsQdMulti = {};
                                headArgsQdMulti['headStartNum'] = 1;
                                headArgsQdMulti['headEndNum'] = 4;
                                await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdMulti, args);
                                break;
                            }
                            let constructUnitSheetMulti1 = await this.getconstructUnitSheet6List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetMulti1, worksheet, 3, null, null, args);
                            let headArgsQdMulti1 = {};
                            headArgsQdMulti1['headStartNum'] = 1;
                            headArgsQdMulti1['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdMulti1, args);
                            await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "实体项目预算表(竖)"://end
                            let precision6 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);

                            let deLists1 = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let data1 = (_.cloneDeep(deLists1));
                            await this.setTreeDispNoShiTi(data1);
                            data1 = data1.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, data1);
                            data1 = data1.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项

                            for (let hengElement of data1) {
                                if (ObjectUtils.isNotEmpty(hengElement.quantity)) {
                                    //精度设置
                                    if (hengElement.isDeResource === 1) {
                                        hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, precision6.EDIT.DERCJ.quantity);
                                    } else {
                                        hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, precision6.EDIT.DE.quantity);
                                    }
                                }
                                if (ObjectUtils.isNotEmpty(hengElement.price)) {
                                    hengElement.price = NumberUtil.numberScale(hengElement.price, precision6.EDIT.DE.price);
                                }
                                if (ObjectUtils.isNotEmpty(hengElement.baseJournalPrice)) {
                                    hengElement.baseJournalPrice = NumberUtil.numberScale(hengElement.baseJournalPrice, precision6.EDIT.DE.price);
                                }
                            }
                            await this.filterProperties(data1, precision);
                            await GljExcelUtil.writeDataToSheet(data1, worksheet, null, 1, null, args);
                            let funct1 = function (data1) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data1.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data1[i])) {
                                        continue;
                                    }
                                    totalNumberHj = NumberUtil.add(totalNumberHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data1[i].totalNumber : data1[i].baseJournalTotalNumber),precision6.EDIT.DE.price));
                                    rTotalSumHj = NumberUtil.add(rTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data1[i].rTotalSum : data1[i].rdTotalSum),precision6.EDIT.DE.price));
                                    cTotalSumHj = NumberUtil.add(cTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data1[i].cTotalSum : data1[i].cdTotalSum),precision6.EDIT.DE.price));
                                    jTotalSumHj = NumberUtil.add(jTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data1[i].jTotalSum : data1[i].jdTotalSum),precision6.EDIT.DE.price));
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funct1(data1), "合 计");
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            // await GljExcelUtil.mergeLastDataInSheet(worksheet);
                            break;
                        case "实体项目预算表(横)"://end
                            let precision7 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);

                            //该条定额下，类型为人工，单位为工日的人材机数据 数量合计
                            let deListsHeng = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataHeng = _.cloneDeep(deListsHeng);
                            await this.setTreeDispNoShiTi(dataHeng);
                            dataHeng = dataHeng.filter(item => item.type != "0");//将单位工程行去掉;
                            await this.addConversionStr(args, dataHeng);
                            dataHeng = dataHeng.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataHeng.length; i++) {
                                let hengElement = dataHeng[i];
                                hengElement.工日合计 = 0;
                                let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, hengElement.sequenceNbr, hengElement.type);
                                if (ObjectUtils.isNotEmpty(rcjLists)) {
                                    let required = rcjLists.filter(item => item.type == "人工费" && item.unit == "工日");
                                    for (let j = 0; j < required.length; j++) {
                                        if (hengElement.type == BranchProjectLevelConstant.top || BranchProjectLevelConstant.fb === hengElement.type || BranchProjectLevelConstant.zfb === hengElement.type) {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        } else {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        }
                                    }
                                }

                                // if (ObjectUtils.isNotEmpty(hengElement.quantity)) {
                                //     //精度设置
                                //     if (hengElement.isDeResource === 1) {
                                //         hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, 3);
                                //     } else {
                                //         hengElement.quantity = NumberUtil.numberScale(hengElement.quantity, 3);
                                //     }
                                // }
                                if (ObjectUtils.isNotEmpty(hengElement.price)) {
                                    hengElement.price = NumberUtil.numberScale(hengElement.price, precision7.EDIT.DE.price);
                                }
                                if (ObjectUtils.isNotEmpty()) {
                                    hengElement.baseJournalPrice = NumberUtil.numberScale(hengElement.baseJournalPrice, precision7.EDIT.DE.price);
                                }
                            }
                            await this.filterProperties(dataHeng, precision);
                            await GljExcelUtil.writeDataToSheet(dataHeng, worksheet, null, 1, null, args);
                            let headArgsQdH = {};
                            headArgsQdH['headStartNum'] = 1;
                            headArgsQdH['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdH, args);
                            let functHeng = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let grTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj = NumberUtil.add(totalNumberHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber),precision7.EDIT.DE.price));
                                    rTotalSumHj = NumberUtil.add(rTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum),precision7.EDIT.DE.price));
                                    cTotalSumHj = NumberUtil.add(cTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum),precision7.EDIT.DE.price));
                                    jTotalSumHj = NumberUtil.add(jTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum),precision7.EDIT.DE.price));
                                    if (data[i].isDeResource === 1) {
                                        grTotalSumHj = NumberUtil.add(grTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(ObjectUtils.isNotEmpty(data[i].工日合计) ? data[i].工日合计 : 0),3));
                                    } else {
                                        grTotalSumHj = NumberUtil.add(grTotalSumHj, GljExcelUtil._roundAndPadCeping(Number(ObjectUtils.isNotEmpty(data[i].工日合计) ? data[i].工日合计 : 0),3));
                                    }
                                }
                                map.set(8, {"amount": totalNumberHj});
                                map.set(10, {"amount": rTotalSumHj});
                                map.set(11, {"amount": cTotalSumHj});
                                map.set(12, {"amount": jTotalSumHj});
                                //工日合计
                                map.set(13, {"amount": GljExcelUtil._roundAndPadCeping(Number(grTotalSumHj),3), "precisionReserve": true});
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functHeng(dataHeng), "合 计");
                            break;
                        case "实体项目预算表(自然单位)"://end
                            // 定义一个函数来提取数字和单位
                        function extractNumberAndUnit(str) {
                            if (ObjectUtils.isEmpty(str)) {
                                str = "";
                            }
                            // 使用正则表达式匹配数字和单位
                            const match = str.match(/^(-?\d+(\.\d+)?)\s*([a-zA-Z0-9\u3000-\u303F\uFF00-\uFFEF\u2000-\u206F\u4E00-\u9FFFA-Z]+)/);
                            if (match) {
                                const number = match[1]; // 数字部分
                                const unit = match[3]; // 单位部分
                                return {number, unit}; // 返回一个对象
                            } else {
                                return {number: 1, unit: str}; // 如果没有匹配到，返回 null
                            }
                        }

                            let deListsNature = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataNature = _.cloneDeep(deListsNature);
                            await this.setTreeDispNoShiTi(dataNature);
                            dataNature = dataNature.filter(item => item.type != "0");//将单位工程行去掉;
                            await this.addConversionStr(args, dataNature);
                            dataNature = dataNature.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataNature.length; i++) {
                                let natureElement = dataNature[i];
                                if (!GljExcelUtil.judgeIsDeType(natureElement)
                                ) continue;
                                let unit = extractNumberAndUnit(natureElement.unit).unit;
                                natureElement.quantity = natureElement.quantity * (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.price = natureElement.price / (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.baseJournalPrice = natureElement.baseJournalPrice / (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.unit = unit;
                                //精度设置
                                if (natureElement.isDeResource === 1) {
                                    natureElement.quantity = NumberUtil.numberScale(natureElement.quantity, precision.EDIT.DERCJ.quantity);
                                } else {
                                    natureElement.quantity = NumberUtil.numberScale(natureElement.quantity, precision.EDIT.DE.quantity);
                                }
                                natureElement.price = NumberUtil.numberScale(natureElement.price, precision.EDIT.DE.price);
                                natureElement.baseJournalPrice = NumberUtil.numberScale(natureElement.baseJournalPrice, precision.EDIT.DE.price);

                            }
                            await this.filterProperties(dataNature, precision);
                            await GljExcelUtil.writeDataToSheet(dataNature, worksheet, null, 1, null, args);
                            let headArgsQdSt = {};
                            headArgsQdSt['headStartNum'] = 1;
                            headArgsQdSt['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdSt, args);
                            let functNature = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])
                                    ) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? data[i].totalNumber : data[i].baseJournalTotalNumber;
                                    rTotalSumHj += pricingMethod ? data[i].rTotalSum : data[i].rdTotalSum;
                                    cTotalSumHj += pricingMethod ? data[i].cTotalSum : data[i].cdTotalSum;
                                    jTotalSumHj += pricingMethod ? data[i].jTotalSum : data[i].jdTotalSum;
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functNature(dataNature), "合 计");
                            break;
                        case "措施项目预算表(竖)"://end
                            // let csLists = ProjectDomain.getDomain(constructId).csxmDomain.getDeAllTreeDepth(constructId,unitId);
                            let csLists1 = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCs1 = _.cloneDeep(csLists1);
                            let treeCs = xeUtils.toArrayTree(dataCs1, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            dataCs1 = await this.setDispNoAndPrice(treeCs[0], pricingMethod);
                            await this.dealBkjzcsxmData(dataCs1);
                            await this.addConversionStr(args, dataCs1);
                            dataCs1 = dataCs1.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            dataCs1 = dataCs1.filter(item => item.deName != "不可竞争措施项目" && item.deName != "可竞争措施项目");//过滤不可竞争/可竞争

                            if (ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod) {
                                dataCs1 = dataCs1.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.totalNumber) && item.totalNumber > 0));//过滤计取项为0的
                            } else {
                                dataCs1 = dataCs1.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.baseJournalTotalNumber) && item.baseJournalTotalNumber > 0));//过滤计取项为0的
                            }

                            if (ObjectUtils.isNotEmpty(dataCs1)) {
                                let i = 1;
                                for (let item of dataCs1) {
                                    if (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.deCode)) {
                                        item.deCode = i++;
                                    }
                                }
                            }

                            await GljExcelUtil.writeDataToSheet(dataCs1, worksheet, null, 1, null, args);
                            let headArgsQdCs1 = {};
                            headArgsQdCs1['headStartNum'] = 1;
                            headArgsQdCs1['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdCs1, args);
                            let funcHeng1 = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcHeng1(dataCs1), "合 计");
                            break;
                        case "措施项目预算表(横)"://end
                            //该条定额下，类型为人工，单位为工日的人材机数据 数量合计
                            // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                            let deCsHeng = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataHengCs = _.cloneDeep(deCsHeng);
                            let treeHengCs = xeUtils.toArrayTree(dataHengCs, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            dataHengCs = await this.setDispNoAndPrice(treeHengCs[0], pricingMethod);
                            await this.dealBkjzcsxmData(dataHengCs);
                            await this.addConversionStr(args, dataHengCs);
                            dataHengCs = dataHengCs.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            dataHengCs = dataHengCs.filter(item => item.deName != "不可竞争措施项目" && item.deName != "可竞争措施项目");//过滤不可竞争/可竞争

                            if (ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod) {
                                dataHengCs = dataHengCs.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.totalNumber) && item.totalNumber > 0));//过滤计取项为0的
                            } else {
                                dataHengCs = dataHengCs.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.baseJournalTotalNumber) && item.baseJournalTotalNumber > 0));//过滤计取项为0的
                            }

                            if (ObjectUtils.isNotEmpty(dataHengCs)) {
                                let i = 1;
                                for (let item of dataHengCs) {
                                    if (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.deCode)) {
                                        item.deCode = i++;
                                    }
                                }
                            }

                            for (let i = 0; i < dataHengCs.length; i++) {
                                let hengElement = dataHengCs[i];
                                hengElement.工日合计 = 0;
                                let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, hengElement.sequenceNbr, hengElement.type);
                                if (ObjectUtils.isNotEmpty(rcjLists)) {
                                    let required = rcjLists.filter(item => item.type == "人工费" && item.unit == "工日");
                                    for (let j = 0; j < required.length; j++) {
                                        if (hengElement.type == BranchProjectLevelConstant.top || BranchProjectLevelConstant.fb === hengElement.type || BranchProjectLevelConstant.zfb === hengElement.type) {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        } else {
                                            hengElement.工日合计 = NumberUtil.add(hengElement.工日合计, ObjectUtils.isNotEmpty(required[j].totalNumber) ? required[j].totalNumber : 0);
                                        }
                                    }
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataHengCs, worksheet, null, 1, null, args);
                            let headArgsQdHeng = {};
                            headArgsQdHeng['headStartNum'] = 1;
                            headArgsQdHeng['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdHeng, args);
                            let functHengCs = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let grTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                    grTotalSumHj += ObjectUtils.isNotEmpty(data[i].工日合计) ? data[i].工日合计 : 0;
                                }
                                map.set(8, {"amount": totalNumberHj});
                                map.set(10, {"amount": rTotalSumHj});
                                map.set(11, {"amount": cTotalSumHj});
                                map.set(12, {"amount": jTotalSumHj});
                                //工日合计
                                map.set(13, {"amount": GljExcelUtil._roundAndPadCeping(Number(grTotalSumHj),3), "precisionReserve": true});
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functHengCs(dataHengCs), "合 计");
                            break;
                        case "措施项目预算表(自然单位)"://end
                            // 定义一个函数来提取数字和单位
                            // let deListsNatureCs = ProjectDomain.getDomain(constructId).csxmDomain.getDeAllTreeDepth(constructId,unitId);
                            let deListsNatureCs = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataNatureCs = _.cloneDeep(deListsNatureCs);
                            let treeNatureCs = xeUtils.toArrayTree(dataNatureCs, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            dataNatureCs = await this.setDispNoAndPrice(treeNatureCs[0], pricingMethod);
                            await this.dealBkjzcsxmData(dataNatureCs);
                            await this.addConversionStr(args, dataNatureCs);
                            dataNatureCs = dataNatureCs.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            dataNatureCs = dataNatureCs.filter(item => item.deName != "不可竞争措施项目" && item.deName != "可竞争措施项目");//过滤不可竞争/可竞争

                            if (ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod) {
                                dataNatureCs = dataNatureCs.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.totalNumber) && item.totalNumber > 0));//过滤计取项为0的
                            } else {
                                dataNatureCs = dataNatureCs.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.baseJournalTotalNumber) && item.baseJournalTotalNumber > 0));//过滤计取项为0的
                            }

                            for (let i = 0; i < dataNatureCs.length; i++) {
                                let natureElement = dataNatureCs[i];
                                if (!GljExcelUtil.judgeIsDeType(natureElement)) continue;
                                let unit = extractNumberAndUnit(natureElement.unit).unit;
                                natureElement.quantity = natureElement.quantity * (extractNumberAndUnit(natureElement.unit).number);
                                natureElement.price = NumberUtil.numberScale2(natureElement.price / (extractNumberAndUnit(natureElement.unit).number));
                                natureElement.baseJournalPrice = NumberUtil.numberScale2(natureElement.baseJournalPrice / (extractNumberAndUnit(natureElement.unit).number));
                                natureElement.unit = unit;

                                natureElement.unit = ObjectUtils.isNotEmpty(natureElement.unit) ? natureElement.unit.replace("%", "项") : "";
                                if (natureElement.type == DeTypeConstants.DE_TYPE_DELIST) {
                                    natureElement.deCode = i+1;
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataNatureCs, worksheet, null, 1, null, args);
                            let headArgsQdNature = {};
                            headArgsQdNature['headStartNum'] = 1;
                            headArgsQdNature['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdNature, args);
                            let functNatureCs = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functNatureCs(dataNatureCs), "合 计");
                            break;
                        case "措施项目预算表(不含安文费)":
                            let csListsNotContainAwf1 = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCsNotContainAwf1 = _.cloneDeep(csListsNotContainAwf1);
                            let treeNotContainAwf1 = xeUtils.toArrayTree(dataCsNotContainAwf1, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.traverseTree(treeNotContainAwf1[0]);
                            await this.resetPrice(treeNotContainAwf1[0], pricingMethod);
                            dataCsNotContainAwf1 = xeUtils.toTreeArray(treeNotContainAwf1);
                            await this.addConversionStr(args, dataCsNotContainAwf1);
                            dataCsNotContainAwf1 = dataCsNotContainAwf1.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            dataCsNotContainAwf1 = dataCsNotContainAwf1.filter(item => item.deName != "不可竞争措施项目" && item.deName != "可竞争措施项目");//过滤不可竞争/可竞争

                            if (ObjectUtils.isNotEmpty(args.pricingMethod) && args.pricingMethod) {
                                dataCsNotContainAwf1 = dataCsNotContainAwf1.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.totalNumber) && item.totalNumber > 0));//过滤计取项为0的
                            } else {
                                dataCsNotContainAwf1 = dataCsNotContainAwf1.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.baseJournalTotalNumber) && item.baseJournalTotalNumber > 0));//过滤计取项为0的
                            }

                            if (ObjectUtils.isNotEmpty(dataCsNotContainAwf1)) {
                                let i = 1;
                                for (let item of dataCsNotContainAwf1) {
                                    if (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.deCode)) {
                                        item.deCode = i++;
                                    }
                                }
                            }

                            await this.filterPropertiesCs(dataCsNotContainAwf1, precision);

                            dataCsNotContainAwf1 = dataCsNotContainAwf1.filter(item => !(item.type == BranchProjectLevelConstant.top || item.type == BranchProjectLevelConstant.zfb ||
                                item.type == BranchProjectLevelConstant.fb)
                            );

                            await GljExcelUtil.writeDataToSheet(dataCsNotContainAwf1, worksheet, null, 1, null, args);
                            let headArgsNotContainAwf1 = {};
                            headArgsNotContainAwf1['headStartNum'] = 1;
                            headArgsNotContainAwf1['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsNotContainAwf1, args);
                            let funcNotContainAwf11 = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].totalNumber) ? data[0].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[0].baseJournalTotalNumber) ? data[0].baseJournalTotalNumber : 0);
                                rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].rTotalSum) ? data[0].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].rdTotalSum) ? data[0].rdTotalSum : 0);
                                cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].cTotalSum) ? data[0].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].cdTotalSum) ? data[0].cdTotalSum : 0);
                                jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[0].jTotalSum) ? data[0].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[0].jdTotalSum) ? data[0].jdTotalSum : 0);
                                map.set(7, totalNumberHj);
                                map.set(9, rTotalSumHj);
                                map.set(10, cTotalSumHj);
                                map.set(11, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcNotContainAwf11(dataCsNotContainAwf1), "合　计");
                            break;
                        case "独立费表"://end
                            let constructUnitSheet13List = await this.getconstructUnitSheet13List(args);
                            let treeData = xeUtils.toArrayTree(constructUnitSheet13List, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            let toTreeArray = xeUtils.toTreeArray(treeData);
                            await GljExcelUtil.writeDataToSheet(toTreeArray, worksheet, 3, null, null, args);
                            let headArgsQd13 = {};
                            headArgsQd13['headStartNum'] = 1;
                            headArgsQd13['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13, args);
                            break;
                        case "单位工程人材机汇总表"://end
                            let constructUnitSheet8List = await this.getconstructUnitSheet8List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet8List, worksheet, 3, 1, null, args);
                            let headArgsQd111 = {};
                            headArgsQd111['headStartNum'] = 1;
                            headArgsQd111['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd111, args);
                            let functRcj = function (data) {
                                let totalNumberHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (data[i].materialName != "小计") {
                                        continue;
                                    }
                                    totalNumberHj += data[i].totalMarket;
                                }
                                map.set(9, totalNumberHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, functRcj(constructUnitSheet8List), "合计");
                            break;
                        case "单位工程人材机价差表"://end
                            let constructUnitSheet9List = await this.getconstructUnitSheet9List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet9List.data, worksheet, 3, 1, null, args);
                            const func = (constructUnitSheet9List) => {
                                let map = new Map();
                                map.set(10, constructUnitSheet9List.total);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, func(constructUnitSheet9List), "合计");
                            let headArgsQd9 = {};
                            headArgsQd9['headStartNum'] = 1;
                            headArgsQd9['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "小计");
                            break;
                        case "单位工程甲供材料表"://end
                            let resultQuery = await this.service.gongLiaoJiProject.gljCommonService.getProjectSetting(args);
                            let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
                            let pricingMethodLocal = ProjectDomain.getDomain(constructId).getRoot().pricingMethod;
                            let isGeneral = true;
                            if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                                isGeneral = false;
                            }
                            //更改模板的字段显示
                            //一般计税，非市场价组价，显示的就是不含税基期价，市场价组价，显示的是不含税市场价
                            //简易计税，非市场价组价，显示的是含税基期价，市场价组价，显示的是含税市场价
                            if (isGeneral) {
                                if (pricingMethod) { //为市场价组价
                                    await GljExcelUtil.updateSheetValue(worksheet, "不含税市场价", "不含税市场价");
                                } else {
                                    await GljExcelUtil.updateSheetValue(worksheet, "不含税市场价", "不含税基期价");
                                }
                            } else {
                                if (pricingMethod) { //为市场价组价
                                    await GljExcelUtil.updateSheetValue(worksheet, "含税市场价", "含税市场价");
                                } else {
                                    await GljExcelUtil.updateSheetValue(worksheet, "含税市场价", "含税基期价");
                                }
                            }
                            args.kind = 0;
                            args.levelType = 3;
                            let allData = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
                            if (ObjectUtils.isEmpty(allData)) {
                                allData = []
                            }
                            //1为甲供
                            let jiaGong = _.cloneDeep(allData.filter(item => item.ifDonorMaterial == 1));
                            for (let i = 0; i < jiaGong.length; i++) {
                                if (ObjectUtils.isNotEmpty(jiaGong[i].specification)) {
                                    jiaGong[i].materialName = jiaGong[i].materialName + "  " + jiaGong[i].specification;
                                }
                                jiaGong[i].unit = await this.getDisplayUnit(jiaGong[i]);

                                if (taxMethod === RcjCommonConstants.SIMPLE_REVERSE && pricingMethodLocal === 0) {
                                    jiaGong[i].marketPrice = jiaGong[i].baseJournalTaxPrice;
                                }
                                if (taxMethod === RcjCommonConstants.SIMPLE_REVERSE && pricingMethodLocal === 1) {
                                    jiaGong[i].marketPrice = jiaGong[i].marketTaxPrice;
                                }
                                if (taxMethod === RcjCommonConstants.GENERAL_FORWARD && pricingMethodLocal === 0) {
                                    jiaGong[i].marketPrice = jiaGong[i].baseJournalPrice;
                                }
                                if (taxMethod === RcjCommonConstants.GENERAL_FORWARD && pricingMethodLocal === 1) {
                                    jiaGong[i].marketPrice = jiaGong[i].marketPrice;
                                }
                                jiaGong[i].total = NumberUtil.multiply(NumberUtil.numberScale(jiaGong[i].marketPrice, args.precision.RCJ_COLLECT.marketPrice), NumberUtil.numberScale(jiaGong[i].donorMaterialNumber, args.precision.RCJ_COLLECT.donorMaterialNumber));
                            }
                            await GljExcelUtil.writeDataToSheet(jiaGong, worksheet, 3, 1, null, args);
                            let functNatureCl = function (data) {
                                let jgHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    jgHj += NumberUtil.numberScale(data[i].total, args.precision.RCJ_COLLECT.total);
                                }
                                map.set(6, jgHj);
                                return map;
                            };
                            await GljExcelUtil.fillTotalContent(worksheet, functNatureCl(jiaGong), "合计");
                            let headArgsJia = {};
                            headArgsJia['headStartNum'] = 1;
                            headArgsJia['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsJia, args);
                            break;
                        case "单位工程三材汇总表"://end
                            let sanCaiData = await this.service.gongLiaoJiProject.gljRcjCollectService.getScCountList(constructId, singleId, unitId);
                            let sanCaiDataBak = _.cloneDeep(sanCaiData);
                            await GljExcelUtil.writeDataToSheet(sanCaiDataBak, worksheet, 3, null, null, args);
                            let headArgsQd8 = {};
                            headArgsQd8['headStartNum'] = 1;
                            headArgsQd8['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8, args);
                            break;
                        case "单位工程主材表"://end
                            args.kind = 5;
                            let constructUnitSheetZc = await this.getconstructUnitSheet12List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetZc, worksheet, 3, null, null, args);
                            let headArgsZc = {};
                            headArgsZc['headStartNum'] = 1;
                            headArgsZc['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsZc, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "单位工程设备表"://end
                            args.kind = 4;
                            let constructUnitSheetSb = await this.getconstructUnitSheet12List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheetSb, worksheet, 3, null, null, args);
                            let headArgsSb = {};
                            headArgsSb['headStartNum'] = 1;
                            headArgsSb['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsSb, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "预拌砼汇总表"://end
                            args.kind = 6;
                            let constructUnitSheet12List = await this.getconstructUnitSheet12List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet12List, worksheet, 3, null, null, args);
                            let headArgsTong = {};
                            headArgsTong['headStartNum'] = 1;
                            headArgsTong['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsTong, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "安全文明施工费明细表"://end
                            let constructSummary = await this.getAwfUnitData(args);
                            await GljExcelUtil.writeDataToSheet(constructSummary, worksheet, 3, null, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            await GljExcelUtil.cellHeJiMiddle(worksheet, "合计");
                            break;
                        case "单位工程预算表(竖)"://end
                            let deListsUnit = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataShu = (_.cloneDeep(deListsUnit));
                            await this.setTreeDispNoShiTi(dataShu);
                            dataShu = dataShu.filter(item => item.type != "0");//将单位工程行去掉

                            // let csListsUnit = ProjectDomain.getDomain(constructId).csxmDomain.getDeAllTreeDepth(constructId,unitId);
                            let csListsUnit = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            csListsUnit = _.cloneDeep(csListsUnit);
                            let treeCsShu = xeUtils.toArrayTree(csListsUnit, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            csListsUnit = await this.setDispNoAndPrice(treeCsShu[0], pricingMethod);
                            dataShu.push(...(csListsUnit));
                            await this.addConversionStr(args, dataShu);
                            dataShu = dataShu.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            await GljExcelUtil.writeDataToSheet(dataShu, worksheet, null, 1, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            let funcUnit = function (data) {
                                let totalNumberHj = 0;
                                let rTotalSumHj = 0;
                                let cTotalSumHj = 0;
                                let jTotalSumHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (!GljExcelUtil.judgeIsDeType(data[i])) {
                                        continue;
                                    }
                                    totalNumberHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].totalNumber) ? data[i].totalNumber : 0) : (ObjectUtils.isNotEmpty(data[i].baseJournalTotalNumber) ? data[i].baseJournalTotalNumber : 0);
                                    rTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].rTotalSum) ? data[i].rTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].rdTotalSum) ? data[i].rdTotalSum : 0);
                                    cTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].cTotalSum) ? data[i].cTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].cdTotalSum) ? data[i].cdTotalSum : 0);
                                    jTotalSumHj += pricingMethod ? (ObjectUtils.isNotEmpty(data[i].jTotalSum) ? data[i].jTotalSum : 0) : (ObjectUtils.isNotEmpty(data[i].jdTotalSum) ? data[i].jdTotalSum : 0);
                                }
                                map.set(8, totalNumberHj);
                                map.set(10, rTotalSumHj);
                                map.set(11, cTotalSumHj);
                                map.set(12, jTotalSumHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnit(dataShu), "合计");
                            break;
                        case "分项工程人材机汇总表(实体)"://end
                            let dataShiTiSummary = await this.getRcjSummaryShiTi(args);
                            for (let i = 0; i < dataShiTiSummary.length; i++) {
                                let element = dataShiTiSummary[i];
                                if (ObjectUtils.isNotEmpty(element.type) && GljExcelUtil.judgeIsDeType(element)) {
                                    if (ObjectUtils.isNotEmpty(element.resQty) && NumberUtil.numberScale2(element.resQty) == 0) {
                                        element.resQty = null;
                                    }
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataShiTiSummary, worksheet, null, 1, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            let funcUnitShiTi = function (data) {
                                let totalHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (ObjectUtils.isNotEmpty(data[i].type) &&
                                        (data[i].type == BranchProjectLevelConstant.de
                                            || data[i].type == DeTypeConstants.DE_TYPE_RESOURCE
                                            || data[i].type == DeTypeConstants.DE_TYPE_USER_DE
                                            || data[i].type == DeTypeConstants.DE_TYPE_USER_RESOURCE
                                            || data[i].type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                                            || data[i].type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE))
                                    {
                                        totalHj += data[i].totalNumber;
                                    }
                                }
                                map.set(10, totalHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitShiTi(dataShiTiSummary), "合 计");
                            break;
                        case "分项工程人材机汇总表(措施)"://end
                            let dataCuoShiSummary = await this.getRcjSummaryCuoShi(args);

                            // dataCuoShiSummary = dataCuoShiSummary.filter(item => item.type != DeTypeConstants.DE_TYPE_DELIST || (item.type == DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(item.totalNumber) && item.totalNumber > 0));//过滤计取项为0的
                            dataCuoShiSummary = dataCuoShiSummary.filter(item => item.type == BranchProjectLevelConstant.de
                                || item.type == DeTypeConstants.DE_TYPE_RESOURCE
                                || item.type == DeTypeConstants.DE_TYPE_USER_DE
                                || item.type == DeTypeConstants.DE_TYPE_USER_RESOURCE
                                || item.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                                || item.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE||item.type==BranchProjectLevelConstant.qd)
                            await GljExcelUtil.writeDataToSheet(dataCuoShiSummary, worksheet, null, 1, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            let funcUnitCuoShi = function (data) {
                                let totalHj = 0;
                                let map = new Map();
                                for (let i = 0; i < data.length; i++) {
                                    if (ObjectUtils.isNotEmpty(data[i].type) &&
                                        (data[i].type == BranchProjectLevelConstant.de
                                            || data[i].type == DeTypeConstants.DE_TYPE_RESOURCE
                                            || data[i].type == DeTypeConstants.DE_TYPE_USER_DE
                                            || data[i].type == DeTypeConstants.DE_TYPE_USER_RESOURCE
                                            || data[i].type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                                            || data[i].type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE))
                                    {
                                        totalHj += data[i].totalNumber;
                                    }
                                }
                                map.set(10, totalHj);
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitCuoShi(dataCuoShiSummary), "合 计");
                            break;
                        case "措施项目计价表"://end
                            let deCsJiJia = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataHengJiJia = _.cloneDeep(deCsJiJia);
                            let arrayTree = xeUtils.toArrayTree(dataHengJiJia, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.statDingEPrcing(arrayTree[0], pricingMethod, precision);
                            await this.setTreeDispNo(arrayTree[0], 1);
                            dataHengJiJia = xeUtils.toTreeArray(arrayTree);
                            dataHengJiJia = dataHengJiJia.filter(item => (item.type == BranchProjectLevelConstant.top || item.type == BranchProjectLevelConstant.zfb ||
                                item.type == BranchProjectLevelConstant.fb || item.type == BranchProjectLevelConstant.qd))
                            dataHengJiJia = dataHengJiJia.filter(item => item.type == BranchProjectLevelConstant.de
                                || item.type == DeTypeConstants.DE_TYPE_RESOURCE
                                || item.type == DeTypeConstants.DE_TYPE_USER_DE
                                || item.type == DeTypeConstants.DE_TYPE_USER_RESOURCE
                                || item.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                                || item.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE||item.type==BranchProjectLevelConstant.qd)
                            await GljExcelUtil.writeDataToSheet(dataHengJiJia, worksheet, 3, 1, null, args);
                            let headArgsJiJia = {};
                            headArgsJiJia['headStartNum'] = 1;
                            headArgsJiJia['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsJiJia, args);
                            let funcUnitJi = function (data) {
                                let totalNumberHj = data.dingEPriceTotal;
                                let map = new Map();
                                map.set(5, Number(totalNumberHj));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitJi(arrayTree[0]), "合计");
                            break;
                        case "单位工程工程量计算书(实体)"://end
                            let deListsShiTi = ProjectDomain.getDomain(constructId).deDomain.getDeAllTreeDepth(constructId, unitId);
                            let dataShiTi = (_.cloneDeep(deListsShiTi));
                            dataShiTi = xeUtils.toTreeArray(dataShiTi);
                            await this.setTreeDispNoShiTi(dataShiTi);
                            dataShiTi = dataShiTi.filter(item => item.type != "0");//将单位工程行去掉
                            await this.addConversionStr(args, dataShiTi);
                            dataShiTi = dataShiTi.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            for (let i = 0; i < dataShiTi.length; i++) {
                                let dataShiTiElement = dataShiTi[i];
                                if (GljExcelUtil.judgeIsDeType(dataShiTiElement)) {
                                    let expression = "";
                                    let result = await this.getQuantityDetails({
                                        constructId: constructId,
                                        unitId: unitId,
                                        deId: dataShiTiElement.sequenceNbr
                                    });
                                    result = result.filter(item => ObjectUtils.isNotEmpty(item.accumulateFlag) && ObjectUtils.isNotEmpty(item.mathFormula) && item.accumulateFlag == 1);
                                    for (let j = 0; j < result.length; j++) {
                                        if (j == result.length - 1) {
                                            expression += result[j].mathFormula;
                                        } else {
                                            expression += result[j].mathFormula + "+";
                                        }
                                    }
                                    if (ObjectUtils.isNotEmpty(expression)) {
                                        dataShiTiElement.quantityExpression += "=" + expression;
                                    }
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DE.quantity);
                                }
                                if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DERCJ.quantity);
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataShiTi, worksheet, null, null, null, args);
                            await GljExcelUtil.dealWithPage(worksheet, workbook, null, args);
                            break;
                        case "单位工程工程量计算书(措施)"://end
                            let csListsCal = (await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
                            (constructId, singleId, unitId, null, 1, 300000, true, null, [])).data;
                            let dataCsCal = _.cloneDeep(csListsCal);
                            dataCsCal = xeUtils.toArrayTree(dataCsCal, {
                                key: 'sequenceNbr',
                                parentKey: 'parentId',
                            });
                            await this.setTreeDispNo(dataCsCal[0], 1);
                            await this.resetPrice(dataCsCal[0], pricingMethod);
                            dataCsCal = xeUtils.toTreeArray(dataCsCal);
                            await this.addConversionStr(args, dataCsCal);
                            dataCsCal = dataCsCal.filter(item => item.isTempRemove != CommonConstants.COMMON_YES);//过滤掉临时删除项
                            dataCsCal = dataCsCal.filter(item => item.type == BranchProjectLevelConstant.de
                                || item.type == DeTypeConstants.DE_TYPE_RESOURCE
                                || item.type == DeTypeConstants.DE_TYPE_USER_DE
                                || item.type == DeTypeConstants.DE_TYPE_USER_RESOURCE
                                || item.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                                || item.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE||item.type==BranchProjectLevelConstant.qd);
                            for (let i = 0; i < dataCsCal.length; i++) {
                                let dataShiTiElement = dataCsCal[i];
                                if (dataShiTiElement.type == BranchProjectLevelConstant.qd) {
                                    dataShiTiElement.quantityExpression = null;
                                }
                                if (GljExcelUtil.judgeIsDeType(dataShiTiElement)) {
                                    let expression = "";
                                    let result = await this.getQuantityDetails({
                                        constructId: constructId,
                                        unitId: unitId,
                                        deId: dataShiTiElement.sequenceNbr
                                    });
                                    result = result.filter(item => ObjectUtils.isNotEmpty(item.accumulateFlag) && ObjectUtils.isNotEmpty(item.mathFormula) && item.accumulateFlag == 1);
                                    for (let j = 0; j < result.length; j++) {
                                        if (j == result.length - 1) {
                                            expression += result[j].mathFormula;
                                        } else {
                                            expression += result[j].mathFormula + "+";
                                        }
                                    }
                                    if (ObjectUtils.isNotEmpty(expression)) {
                                        dataShiTiElement.quantityExpression += "=" + expression;
                                    }
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DE.quantity);
                                }
                                if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                                    dataShiTiElement.quantity = NumberUtil.numberScale(dataShiTiElement.quantity, precision.EDIT.DERCJ.quantity);
                                }
                            }
                            await GljExcelUtil.writeDataToSheet(dataCsCal, worksheet, null, null, null, args);
                            let headArgsQdCal = {};
                            headArgsQdCal['headStartNum'] = 1;
                            headArgsQdCal['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQdCal, args);
                            break;
                        case "主要材料价格表"://end
                            let constructUnitSheet14List = await this.getconstructUnitSheet14List(args);
                            await GljExcelUtil.writeDataToSheet(constructUnitSheet14List, worksheet, 3, null, null, args);
                            let headArgsQd14 = {};
                            headArgsQd14['headStartNum'] = 1;
                            headArgsQd14['headEndNum'] = 4;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headArgsQd14, args);
                            break;
                        case "水电费明细表":
                            // let result = await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.getWaterElectricCost({
                            //     constructId: constructId,
                            //     singleId: singleId,
                            //     unitId: unitId
                            // });
                            let sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
                            let dataWrite = [];
                            if (ObjectUtils.isNotEmpty(sdfData) && ObjectUtils.isNotEmpty(sdfData[unitId])) {
                                sdfData = _.cloneDeep(sdfData[unitId]);
                                dataWrite = sdfData.waterElectricData.filter(item => ObjectUtils.isEmpty(item.children) && (ObjectUtils.isNotEmpty(item.totalCost) || ObjectUtils.isNotEmpty(item.waterCost) || ObjectUtils.isNotEmpty(item.electricCost)))
                            }
                            await GljExcelUtil.writeDataToSheet(dataWrite, worksheet, null, 1, null, args);
                            let funcUnitJiSd = function (data) {
                                let waterCostTotal = 0;
                                let electricCostTotal = 0;
                                let totalCostTotal = 0;
                                for (let i = 0; i < data.length; i++) {
                                    let datum = data[i];
                                    waterCostTotal = NumberUtil.add(waterCostTotal, datum.waterCost);
                                    electricCostTotal = NumberUtil.add(electricCostTotal, datum.electricCost);
                                    totalCostTotal = NumberUtil.add(totalCostTotal, datum.totalCost);
                                }
                                let map = new Map();
                                map.set(8, NumberUtil.numberScale2(waterCostTotal));
                                map.set(9, NumberUtil.numberScale2(electricCostTotal));
                                map.set(10, NumberUtil.numberScale2(totalCostTotal));
                                return map;
                            }
                            await GljExcelUtil.fillTotalContent(worksheet, funcUnitJiSd(dataWrite), "合计");
                            let headSdf = {};
                            headSdf['headStartNum'] = 1;
                            headSdf['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headSdf, args);
                            break;
                        case "水电费明细表(独立设置)":
                            let sdfDataIndependent = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
                            let dataIndependent = [];
                            if (ObjectUtils.isNotEmpty(sdfDataIndependent) && ObjectUtils.isNotEmpty(sdfDataIndependent[unitId]) && sdfDataIndependent[unitId].customWaterElectricFlag === true) {
                                dataIndependent.push({
                                    "totalCost": GljExcelUtil._roundAndPad(sdfDataIndependent[unitId].customWaterElectric,precision.COST_SUMMARY.jqsdf),
                                });
                            }
                            await GljExcelUtil.writeDataToSheet(dataIndependent, worksheet, null, null, null, args);
                            let headIndependent = {};
                            headIndependent['headStartNum'] = 1;
                            headIndependent['headEndNum'] = 5;
                            await GljExcelUtil.dealWithPage(worksheet, workbook, headIndependent, args);
                            break;
                        default:
                        }
                    }
                }
            }
        //填充工程项目名称
        if (projectType == ProjectLevelConstant.construct && !(worksheet.name.includes("封面"))) {
            //填充 项目名称
            let project = ProjectDomain.getDomain(constructId).getProjectById(constructId);
            await GljWriteExcelBySheetUtil.fillSheetProjectName(worksheet,project.name,"工程名称：");
            await GljWriteExcelBySheetUtil.fillSheetProjectName(worksheet,project.name,"项目名称：");
        }
        if (projectType == ProjectLevelConstant.unit && !(worksheet.name.includes("封面"))) {
            let unitProject = await ProjectDomain.getDomain(constructId).getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.sequenceNbr==unitId)[0];
            let single = await ProjectDomain.getDomain(constructId).getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE && item.sequenceNbr==unitProject.parentId);
            let sheetProjectName = "";
            if (ObjectUtils.isNotEmpty(single)) {
                sheetProjectName = single[0].name+"-"+unitProject.name;
            }else {
                sheetProjectName = unitProject.name;
            }
            await GljWriteExcelBySheetUtil.fillSheetProjectName(worksheet,sheetProjectName,"项目名称：");
            await GljWriteExcelBySheetUtil.fillSheetProjectName(worksheet,sheetProjectName,"工程名称：");
        }
        return worksheet;
    }


    async calDwgcfyHeji(unitDwgcfyHeji, worksheet) {
        let {total} = unitDwgcfyHeji;
        let totalZW = "含税工程造价：零元整";
        if (ObjectUtils.isNotEmpty(total) && total > 0) {
            let numToCny = NumberUtil.numToCny(total);
            totalZW = "含税工程造价：" + numToCny;
        }
        let heJiCell7 = GljExcelUtil.findValueCell(worksheet, "含税工程造价：零元整");
        let row7 = worksheet.getRow(heJiCell7.cell._row._number);
        //合并单元格
        worksheet.unMergeCells(row7.number, 1, row7.number, 5);
        worksheet.mergeCells(row7.number, 1, row7.number, 5);
        row7.getCell(1).value = totalZW;
        row7.getCell(1).style.alignment.horizontal = "center";
        row7.getCell(1).style.alignment.vertical = "middle";
    }

    async sheetMerge(worksheet, headEndNum, totalSS) {
        let dataNum = headEndNum + 1;
        if (worksheet.name.includes("B.0.12 进口设备材料货价及从属费用计算表")) {
            await this.sheetMergeFirstpage(dataNum, worksheet);     //先合并第一页数据
            // let rowBreaks = worksheet.rowBreaks;
            // if (ObjectUtils.isNotEmpty(rowBreaks) && rowBreaks.length > 0) {
            //     let start = 0;
            //     for (let j = 0; j < rowBreaks.length; j++) {
            //         let rowBreak = rowBreaks[j];
            //         let id = rowBreak.id;
            //         start = id + dataNum;
            //
            //         let hebinghangS = start;
            //         let hebinghangE = start;
            //         let hebingstr = worksheet.getRow(start).getCell(1).value == null || worksheet.getRow(start).getCell(1).value == undefined ? "" : worksheet.getRow(start).getCell(1).value;
            //         for (let j = start + 1; j <= id; j++) {
            //             let cell1 = worksheet.getRow(j).getCell(1);
            //             if (cell1.value != null) {
            //                 if (cell1.value === hebingstr) {
            //                     hebinghangE = j;
            //                 } else {
            //                     if (hebinghangE - hebinghangS > 0) {
            //                         if(cell1.value!==""){
            //                             //先合并，再重置start
            //                         worksheet.unMergeCells(hebinghangS, 1, hebinghangE, 1);
            //                         worksheet.mergeCells(hebinghangS, 1, hebinghangE, 1);
            //                         worksheet.unMergeCells(hebinghangS, 2, hebinghangE, 2);
            //                         worksheet.mergeCells(hebinghangS, 2, hebinghangE, 2);
            //                         worksheet.unMergeCells(hebinghangS, 3, hebinghangE, 3);
            //                         worksheet.mergeCells(hebinghangS, 3, hebinghangE, 3);
            //                         worksheet.unMergeCells(hebinghangS, 4, hebinghangE, 4);
            //                         worksheet.mergeCells(hebinghangS, 4, hebinghangE, 4);
            //                         worksheet.unMergeCells(hebinghangS, 5, hebinghangE, 5);
            //                         worksheet.mergeCells(hebinghangS, 5, hebinghangE, 5);
            //                         worksheet.unMergeCells(hebinghangS, 8, hebinghangE, 8);
            //                         worksheet.mergeCells(hebinghangS, 8, hebinghangE, 8);
            //                         worksheet.unMergeCells(hebinghangS, 9, hebinghangE, 9);
            //                         worksheet.mergeCells(hebinghangS, 9, hebinghangE, 9);
            //                         worksheet.unMergeCells(hebinghangS, 12, hebinghangE, 12);
            //                         worksheet.mergeCells(hebinghangS, 12, hebinghangE, 12);
            //                         worksheet.unMergeCells(hebinghangS, 13, hebinghangE, 13);
            //                         worksheet.mergeCells(hebinghangS, 13, hebinghangE, 13);
            //                         hebinghangS = j;
            //                         hebinghangE = j;
            //                         }
            //
            //                     } else {
            //                         hebinghangS = j;
            //                         hebingstr = "";
            //                     }
            //                 }
            //                 hebingstr = cell1.value;
            //             } else {
            //                 hebinghangS = j;
            //                 hebingstr = "";
            //             }
            //         }
            //         // start = rowBreak.id;
            //     }
            // }
        }
    }


    async sheetMergeFirstpage(dataNum, worksheet) {
        //没有分页
        let start = dataNum;
        let hebinghangS = start;
        let hebinghangE = start;
        let hebingstr = worksheet.getRow(start).getCell(1).value == null || worksheet.getRow(start).getCell(1).value == undefined ? "" : worksheet.getRow(start).getCell(1).value;
        for (let j = start + 1; j <= worksheet._rows.length; j++) {
            let cell1 = worksheet.getRow(j).getCell(1);
            let cell1Last = worksheet.getRow(j - 1).getCell(1);
            if (cell1.value != null) {
                if (cell1.value === hebingstr) {
                    hebinghangE = j;
                } else {
                    if (hebinghangE - hebinghangS > 0) {
                        if (cell1.value !== "编制人：") {
                            //先合并，再重置start
                            worksheet.unMergeCells(hebinghangS, 1, hebinghangE, 1);
                            worksheet.mergeCells(hebinghangS, 1, hebinghangE, 1);
                            worksheet.unMergeCells(hebinghangS, 2, hebinghangE, 2);
                            worksheet.mergeCells(hebinghangS, 2, hebinghangE, 2);
                            worksheet.unMergeCells(hebinghangS, 3, hebinghangE, 3);
                            worksheet.mergeCells(hebinghangS, 3, hebinghangE, 3);
                            worksheet.unMergeCells(hebinghangS, 4, hebinghangE, 4);
                            worksheet.mergeCells(hebinghangS, 4, hebinghangE, 4);
                            worksheet.unMergeCells(hebinghangS, 5, hebinghangE, 5);
                            worksheet.mergeCells(hebinghangS, 5, hebinghangE, 5);
                            worksheet.unMergeCells(hebinghangS, 8, hebinghangE, 8);
                            worksheet.mergeCells(hebinghangS, 8, hebinghangE, 8);
                            worksheet.unMergeCells(hebinghangS, 9, hebinghangE, 9);
                            worksheet.mergeCells(hebinghangS, 9, hebinghangE, 9);
                            worksheet.unMergeCells(hebinghangS, 12, hebinghangE, 12);
                            worksheet.mergeCells(hebinghangS, 12, hebinghangE, 12);
                            worksheet.unMergeCells(hebinghangS, 13, hebinghangE, 13);
                            worksheet.mergeCells(hebinghangS, 13, hebinghangE, 13);
                            hebinghangS = j;
                            hebinghangE = j;
                        }
                    } else {
                        hebinghangS = j;
                        hebingstr = "";
                    }
                }
                hebingstr = cell1.value;
            } else {
                hebinghangS = j;
                hebingstr = "";
            }
        }
    }


    traverseHeadLineList(headLineList, strCondition) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];
            if (element.headLine == strCondition) {
                return element;
            }
            if (element.hasOwnProperty("children")) {
                let result = this.traverseHeadLineList(element.children, strCondition);
                if (result != null) return result;
            }
        }
        return null;
    }

    //拿到list中 没有children的元素
    traverseGetHeadLineAndLeaf(headLineList, levelType, list) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];

            if (element.projectLevel == levelType && element.children == null) {
                element['selected'] = false;
                list.push(element);
            }
            if (element.projectLevel == levelType && element.children != null) {
                this.traverseGetHeadLineAndLeaf(element.children, levelType, list);
            }
        }
        return list;
    }

    async getconstructProjectJBXX(param) {
        //获取工程项目对象
        let construct = ProjectDomain.getDomain(param.constructId).getProjectById(param.constructId);
        let array = new Array();
        let constructName = {};
        constructName.name = "项目名称";
        constructName.remark = construct.name;
        array.push(constructName);
        // let constructCode = {};
        // constructCode.name = "项目编码";
        // constructCode.remark = construct.code;
        // array.push(constructCode);

        await this.getconstructJBXX(param.constructId, array);

        //获取编制说明
        let args1 = {};
        args1.constructId = param.constructId;
        args1.type = "12";
        args1.levelType = projectLevelConstant.construct;
        args1.unitId = null;
        const BZSM = await this.service.gongLiaoJiProject.gljOverviewService.getList(args1);
        let constructBzsm = {};
        constructBzsm.name = "编制说明";
        if (ObjectUtils.isNotEmpty(BZSM)) {
            constructBzsm.remark = BZSM.context;
        } else {
            constructBzsm.remark = null;
        }
        array.push(constructBzsm);

        //工程规模如果是0  展示为空
        let projectGuiMo = array.filter(item => item.name=="工程规模")[0];
        if (ObjectUtils.isNotEmpty(projectGuiMo.remark) && Number(projectGuiMo.remark) == 0) {
            projectGuiMo.remark = "";
        }
        //工程规模取整
        let guiMo = _.cloneDeep(array.filter(item => item.name=="工程规模")[0]);
        if (ObjectUtils.isNotEmpty(guiMo.remark)) {
            // guiMo.remark = NumberUtil.numberScale(Number(guiMo.remark),0);
            guiMo.remark = guiMo.remark;
        }else {
            guiMo.remark = "";
        }
        guiMo.name = "工程规模取整";

        //工程造价取整
        let gczj = _.cloneDeep(array.filter(item => item.name=="工程造价")[0]);
        gczj.remark = NumberUtil.numberScale(Number(gczj.remark),0);
        gczj.name = "工程造价取整";

        //造价指标取整
        let zjZb = _.cloneDeep(array.filter(item => item.name=="单方造价")[0]);
        zjZb.remark = NumberUtil.numberScale(Number(zjZb.remark),0);
        zjZb.name = "造价指标取整";

        array.push(guiMo,gczj,zjZb);
        return array;
    }


    async getconstructJBXX(constructId, array) {
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.COST_SUMMARY;

        //获取基本信息
        let args = {};
        args.constructId = constructId;
        args.type = FunctionTypeConstants.JBXX_KEY_TYPE_11;
        args.levelType = projectLevelConstant.construct;
        args.unitId = null;
        let JBXXArray = await this.service.gongLiaoJiProject.gljOverviewService.getList(args);
        JBXXArray = _.cloneDeep(JBXXArray)
        // for (let i = 0; i < JBXXArray.length; i++) {
        //     JBXXArray[i].remark = JBXXArray[i].context;
        // }
        //获取造价分析
        args.type = projectLevelConstant.construct;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        if (ObjectUtils.isNotEmpty(costAnalysiss) && ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisConstructVOList)) {
            array.push({
                name:"工程造价",
                remark:NumberUtil.numberScale(costAnalysiss.costAnalysisConstructVOList[0].projectCost,precision.je)
            });
            array.push({
                name:"单方造价",
                remark:NumberUtil.numberScale(costAnalysiss.costAnalysisConstructVOList[0].unitcost,precision.je)
            })

        }
        array.push(...JBXXArray);
        array.push({
            name:"编制日期",
            remark:DateTimeUtil.getCurrentDate()
        })

        let guiMo = array.filter(item => item.name=="工程规模")[0];
        if (ObjectUtils.isNotEmpty(guiMo.remark)) {   //为空值  在页面不显示
            guiMo.remark = GljExcelUtil._roundAndPadCeping(Number(guiMo.remark),precision1.COST_ANALYSIS.jzgm);
        }
    }

    async getconstructProjectSingleUnitJBXX(param) {
        let array = new Array();
        let arrayConstruct = [];
        await this.getconstructJBXX(param.constructId, arrayConstruct);
        //获取工程项目、单项、单位
        let unit = ProjectDomain.getDomain(param.constructId).getProjectById(param.unitId);
        let obj = {};
        obj.name = "单位名称";
        obj.remark = unit.name;
        array.push(obj);

        let single = ProjectDomain.getDomain(param.constructId).getProjectById(unit.parentId);
        let objSingle = {};
        objSingle.name = "单项名称";
        objSingle.remark = single.name;
        array.push(objSingle);

        //获取工程特征
        let args = {};
        args.constructId = param.constructId;
        args.type = FunctionTypeConstants.JBXX_KEY_TYPE_13;
        args.levelType = projectLevelConstant.unit;
        args.unitId = param.unitId;
        let JBXXArray = await this.service.gongLiaoJiProject.gljOverviewService.getList(args);
        JBXXArray = _.cloneDeep(JBXXArray)
        for (let i = 0; i < JBXXArray.length; i++) {
            JBXXArray[i].remark = JBXXArray[i].context;
        }
        array.push(...JBXXArray);
        array.push(arrayConstruct.filter(item => item.name =="建设单位")[0]);
        array.push(arrayConstruct.filter(item => item.name =="施工单位")[0]);
        //获取编制说明
        let args1 = {};
        args1.constructId = param.constructId;
        args1.type = "12";
        args1.levelType = projectLevelConstant.unit;
        args1.unitId = param.unitId;
        const BZSM = await this.service.gongLiaoJiProject.gljOverviewService.getList(args1);
        let constructBzsm = {};
        constructBzsm.name = "编制说明";
        if (ObjectUtils.isNotEmpty(BZSM)) {
            constructBzsm.remark = BZSM.context;
        } else {
            constructBzsm.remark = null;
        }
        array.push(constructBzsm);


        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        let precision = precision1.COST_SUMMARY;
        let precisionCost = precision1.COST_ANALYSIS;
        try {
            //获取造价分析
            let args3 = {};
            args3.type = projectLevelConstant.unit;
            args3.singleId = param.singleId;
            args3.constructId = param.constructId;
            args3.unitId = param.unitId;
            let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args3);
            if (ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisUnitVOList)) {
                array.push({
                    name:"工程造价",
                    remark:GljExcelUtil._roundAndPadCeping(Number(costAnalysiss.costAnalysisUnitVOList.filter(item => item.name=="工程总造价（小写）")[0].context),precision.je),
                });
                array.push({
                    name:"造价指标",
                    remark:GljExcelUtil._roundAndPadCeping(Number(costAnalysiss.costAnalysisUnitVOList.filter(item => item.name=="单方造价")[0].context),precision.je),
                });
            }
        } catch (e) {
            console.log("获取报表单位造价分析报错" + param.unitId);
        }

        let guiMoInit = array.filter(item => item.name=="工程规模")[0];
        guiMoInit.remark = NumberUtil.numberScale(Number(guiMoInit.remark),precisionCost.jzgm);

        //工程规模如果是0  展示为空
        let projectGuiMo = array.filter(item => item.name=="工程规模")[0];
        if (ObjectUtils.isNotEmpty(projectGuiMo.remark) && Number(projectGuiMo.remark) == 0) {
            projectGuiMo.remark = "";
        }

        //工程规模取整
        let guiMo = {};
        if (ObjectUtils.isNotEmpty(array.filter(item => item.name == "工程规模"))) {
            guiMo = _.cloneDeep(array.filter(item => item.name=="工程规模")[0]);
            if (ObjectUtils.isNotEmpty(guiMo.remark)) {
                // guiMo.remark = NumberUtil.numberScale(Number(guiMo.remark),0);
                guiMo.remark = guiMo.remark;
            }else {
                guiMo.remark = "";
            }
            guiMo.name = "工程规模取整";
        }
        //工程造价取整
        let gczj = {};
        if (ObjectUtils.isNotEmpty(array.filter(item => item.name=="工程造价"))) {
            gczj = _.cloneDeep(array.filter(item => item.name=="工程造价")[0]);
            gczj.remark = NumberUtil.numberScale(Number(gczj.remark),0);
            gczj.name = "工程造价取整";
        }
        //造价指标取整
        let zjZb = {};
        if (ObjectUtils.isNotEmpty(array.filter(item => item.name=="造价指标"))) {
            zjZb = _.cloneDeep(array.filter(item => item.name=="造价指标")[0]);
            zjZb.remark = NumberUtil.numberScale(Number(zjZb.remark),0);
            zjZb.name = "造价指标取整";
        }
        array.push(guiMo,gczj,zjZb);
        array.push({
            name:"编制日期",
            remark:DateTimeUtil.getCurrentDate()
        })
        return array;
    }


    async removeExtraZerosAndDot(numStr) {
        return numStr.toString().replace(/(\.0*|0+)$/, '');
    }

    async getconstructProjectSheet7List(param) {
        let Sheet4List = [];
        let heji = {};
        heji.total = "";

        let args = {};
        args.type = 1;
        args.levelType = projectLevelConstant.construct;
        args.code = 5;
        args.unitId = null;
        args.constructId = param.constructId;
        args.singleId = null;
        args.projectId = param.constructId;
        let otherProjectCostList = this.service.gongLiaoJiProject.gljOtherProjectCostService.getOtherProjectCostList(args);
        if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
            heji.total = otherProjectCostList[0].amount;
            // let filter = otherProjectCostList.filter(o => ObjectUtils.isNotEmpty(o.parentId));
            // if (ObjectUtils.isNotEmpty(filter)) {
            //     heji.total = filter.reduce((sum, item) => sum + Number(ObjectUtils.isEmpty(item.amount) ? 0 : item.amount), 0);
            //     for (let item of filter) {
            //         if (ObjectUtils.isNotEmpty(item) && item.amount > 0) {
            //             Sheet4List.push(item);
            //             if (ObjectUtils.isNotEmpty(item.children)) {
            //                 for (let item1 of item.children) {
            //                     if (ObjectUtils.isNotEmpty(item1) && item1.amount > 0) {
            //                         Sheet4List.push(item1);
            //                     }
            //                 }
            //             }
            //         }
            //     }
            // }

            otherProjectCostList.forEach(p => {
                if (ObjectUtils.isNotEmpty(p.amount) && p.amount > 0) {
                    let deepCopy1 = ConvertUtil.deepCopy(p);
                    Sheet4List.push(deepCopy1);
                }
            })
        }

        let zhongwen = 0;
        let count = 0;

        for (const p of Sheet4List) {
            if (ObjectUtils.isNotEmpty(p.dispNo)) {
                let isInt = Number.isFinite(+p.dispNo);
                if (isInt) {
                    count++;
                    p.dispNo = count.toString();
                } else {
                    zhongwen++;
                    count = 0;
                    p.dispNo = await this.toChineseCapital(zhongwen.toString());
                }
            }
        }
        param['projectQtfyjsbHeji'] = heji;

        return Sheet4List;
    }


    async toChineseCapital(numStr) {
        const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        const units = ['', '十', '百', '千', '万'];
        let str = numStr;
        let res = '';

        for (let i = 0; i < str.length; i++) {
            let num = parseInt(str[i]);
            if (str[0] === '0' && str.length > 1) {
                res += units[i];
            } else {
                res += chineseNumbers[num] + units[i];
            }
        }

        return res;
    }

    //计算定额组价的措施项汇总  设置并返回当前节点的定额组价 值
    async statDingEPrcing(csDataTree,pricingMethod,precision) {
        //2为定额组价
        if (ObjectUtils.isNotEmpty(csDataTree.children)) {
            if (csDataTree.pricingMethod == 2) {
                let total = 0;
                for (let i = 0; i < csDataTree.children.length; i++) {
                    let element = csDataTree.children[i];
                    let priceTotal = pricingMethod?(ObjectUtils.isNotEmpty(element.totalNumber)?element.totalNumber:0):(ObjectUtils.isNotEmpty(element.baseJournalTotalNumber)?element.baseJournalTotalNumber:0)
                    total = NumberUtil.add(total,priceTotal);
                }
                csDataTree.children = [];
                csDataTree.dingEPriceTotal = GljExcelUtil._roundAndPadCeping(total,precision.COST_SUMMARY.je);
                return total;
            }else if (csDataTree.pricingMethod == 1){ //计算公式组价
                csDataTree.dingEPriceTotal = "0.00";
                csDataTree.children = [];
                return 0;
            } else {
                //为分部节点 或子措施组价项
                let dingEPriceTotal = 0;
                for (let i = 0; i < csDataTree.children.length; i++) {
                    let total = await this.statDingEPrcing(csDataTree.children[i],pricingMethod,precision);
                    dingEPriceTotal = NumberUtil.add(dingEPriceTotal,total);
                }
                csDataTree.dingEPriceTotal = GljExcelUtil._roundAndPadCeping(dingEPriceTotal,precision.COST_SUMMARY.je);
                return dingEPriceTotal;
            }
        }
        csDataTree.dingEPriceTotal = "0.00";
        return csDataTree.dingEPriceTotal;
    }
    //只针对措施项目进行调用
    async setDispNoAndPrice(csDataTree,pricingMethod) {

        await this.resetPrice(csDataTree,pricingMethod);
        let dataJiJia = xeUtils.toTreeArray([csDataTree]);
        dataJiJia = dataJiJia.filter(item => !(item.type==BranchProjectLevelConstant.top||item.type==BranchProjectLevelConstant.zfb||
            item.type==BranchProjectLevelConstant.fb)
        );
        await this.setTreeDispNoShiTi(dataJiJia);
        let keyList = ["price","baseJournalPrice","RSum","RDSum","CSum","CDSum","JSum","JDSum","totalNumber",
            "baseJournalTotalNumber","rTotalSum","rdTotalSum","cTotalSum","cdTotalSum","jTotalSum","jdTotalSum"];
        for (let i = 0; i < dataJiJia.length; i++) {
            let element = dataJiJia[i];
            if (element.type != BranchProjectLevelConstant.fb && element.type != BranchProjectLevelConstant.zfb
                && element.type!=BranchProjectLevelConstant.top) {
                for (let csDataTreeKey in element) {
                    if (keyList.includes(csDataTreeKey)&& ObjectUtils.isEmpty(element[csDataTreeKey])) {  //将undefined置为0
                        element[csDataTreeKey] = NumberUtil.numberScale2(element[csDataTreeKey]);
                    }
                }
            }
        }
        return dataJiJia;
    }

    //将计算公式组价的金额置为0  并重新进行向上汇总
    async resetPrice(csDataTree,pricingMethod) {
        //2为定额组价
        if (csDataTree.pricingMethod == 2) {
            return {
                "heji":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.totalNumber)?csDataTree.totalNumber:0):(ObjectUtils.isNotEmpty(csDataTree.baseJournalTotalNumber)?csDataTree.baseJournalTotalNumber:0)),
                "rgf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.rTotalSum)?csDataTree.rTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.rdTotalSum)?csDataTree.rdTotalSum:0)),
                "clf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.cTotalSum)?csDataTree.cTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.cdTotalSum)?csDataTree.cdTotalSum:0)),
                "jxf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.jTotalSum)?csDataTree.jTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.jdTotalSum)?csDataTree.jdTotalSum:0)),
            };
        }else if (csDataTree.pricingMethod == 1){ //计算公式组价
            if (pricingMethod) {
                csDataTree.totalNumber = 0;
                csDataTree.rTotalSum = 0;
                csDataTree.cTotalSum = 0;
                csDataTree.jTotalSum = 0;
                csDataTree.price = 0;
            }else {
                csDataTree.baseJournalPrice = 0;
                csDataTree.baseJournalTotalNumber = 0;
                csDataTree.rdTotalSum = 0;
                csDataTree.cdTotalSum = 0;
                csDataTree.jdTotalSum = 0;
            }
            return {
                "heji":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.totalNumber)?csDataTree.totalNumber:0):(ObjectUtils.isNotEmpty(csDataTree.baseJournalTotalNumber)?csDataTree.baseJournalTotalNumber:0)),
                "rgf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.rTotalSum)?csDataTree.rTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.rdTotalSum)?csDataTree.rdTotalSum:0)),
                "clf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.cTotalSum)?csDataTree.cTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.cdTotalSum)?csDataTree.cdTotalSum:0)),
                "jxf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.jTotalSum)?csDataTree.jTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.jdTotalSum)?csDataTree.jdTotalSum:0)),
            };
        }
        if (csDataTree.pricingMethod == 3 || csDataTree.type == BranchProjectLevelConstant.top
            || csDataTree.type == BranchProjectLevelConstant.fb
            || csDataTree.type == BranchProjectLevelConstant.zfb
        ) {
            //为分部节点 或子措施组价项
            let hjTotal = 0;
            let rgfTotal = 0;
            let jxfTotal = 0;
            let clfTotal = 0;
            for (let i = 0; i < csDataTree.children.length; i++) {
                let totalObject = await this.resetPrice(csDataTree.children[i],pricingMethod);
                hjTotal+=Number(totalObject.heji);
                rgfTotal+=Number(totalObject.rgf);
                clfTotal+=Number(totalObject.clf);
                jxfTotal+=Number(totalObject.jxf);
            }
            if (pricingMethod) {
                csDataTree.totalNumber = NumberUtil.numberScale2(hjTotal);
                csDataTree.rTotalSum = NumberUtil.numberScale2(rgfTotal);
                csDataTree.cTotalSum = NumberUtil.numberScale2(clfTotal);
                csDataTree.jTotalSum = NumberUtil.numberScale2(jxfTotal);
            }else {
                csDataTree.baseJournalTotalNumber = NumberUtil.numberScale2(hjTotal);
                csDataTree.rdTotalSum = NumberUtil.numberScale2(rgfTotal);
                csDataTree.cdTotalSum = NumberUtil.numberScale2(clfTotal);
                csDataTree.jdTotalSum = NumberUtil.numberScale2(jxfTotal);
            }
        }
        return {
            "heji":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.totalNumber)?csDataTree.totalNumber:0):(ObjectUtils.isNotEmpty(csDataTree.baseJournalTotalNumber)?csDataTree.baseJournalTotalNumber:0)),
            "rgf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.rTotalSum)?csDataTree.rTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.rdTotalSum)?csDataTree.rdTotalSum:0)),
            "clf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.cTotalSum)?csDataTree.cTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.cdTotalSum)?csDataTree.cdTotalSum:0)),
            "jxf":(pricingMethod?(ObjectUtils.isNotEmpty(csDataTree.jTotalSum)?csDataTree.jTotalSum:0):(ObjectUtils.isNotEmpty(csDataTree.jdTotalSum)?csDataTree.jdTotalSum:0)),
        };
    }

    async setTreeDispNo(csDataTree,dispNo) {
        //大标题项 不给赋序号
        if (!(csDataTree.deName=="措施项目"||csDataTree.deName=="不可竞争措施项目"||csDataTree.deName=="可竞争措施项目"||csDataTree.type=="0"||csDataTree.type=="01")){
            csDataTree.dispNo = dispNo;
        }
        if (ObjectUtils.isNotEmpty(csDataTree.children)) {
            for (let i = 0; i < csDataTree.children.length; i++) {
                //对于以下的树 子项的序号赋值从1开始
                if (csDataTree.deName=="措施项目"||csDataTree.deName=="不可竞争措施项目"||csDataTree.deName=="可竞争措施项目"||csDataTree.type=="0"||csDataTree.type=="01"){
                    await this.setTreeDispNo(csDataTree.children[i],(i+1));
                }else {
                    await this.setTreeDispNo(csDataTree.children[i],dispNo+"."+(i+1));
                }

            }
        }
        let keyList = ["price","baseJournalPrice","RSum","RDSum","CSum","CDSum","JSum","JDSum","totalNumber",
            "baseJournalTotalNumber","rTotalSum","rdTotalSum","cTotalSum","cdTotalSum","jTotalSum","jdTotalSum"];
        if (csDataTree.type != BranchProjectLevelConstant.fb && csDataTree.type != BranchProjectLevelConstant.zfb
            && csDataTree.type!=BranchProjectLevelConstant.top) {
            for (let csDataTreeKey in csDataTree) {
                if (keyList.includes(csDataTreeKey)&& ObjectUtils.isEmpty(csDataTree[csDataTreeKey])) {  //将undefined置为0
                    csDataTree[csDataTreeKey] = NumberUtil.numberScale2(csDataTree[csDataTreeKey]);
                }
            }
        }
        return csDataTree;
    }

    async setTreeDispNoShiTi(ysDataList) {
        let filter = ysDataList.filter(item =>
            item.type==BranchProjectLevelConstant.de
            ||item.type==DeTypeConstants.DE_TYPE_RESOURCE
            ||item.type==DeTypeConstants.DE_TYPE_USER_DE
            ||item.type==DeTypeConstants.DE_TYPE_USER_RESOURCE
            ||item.type==DeTypeConstants.DE_TYPE_ANZHUANG_FEE
            ||item.type==DeTypeConstants.DE_TYPE_ZHUANSHI_FEE
        );
        let dispNo = 1;
        for (let i = 0; i < filter.length; i++) {
            let element = filter[i];
            element.dispNo = dispNo++;
        }
    }

    async getconstructProjectSheet8List(args) {
        let returnList = [];
        //获取造价分析
        args.type = projectLevelConstant.construct;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        if (ObjectUtils.isNotEmpty(costAnalysiss) && ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisConstructVOList)) {
            await this.getAQSCWMSGFData(returnList,costAnalysiss.costAnalysisConstructVOList[0],null,costAnalysiss.costAnalysisConstructVOList[0].sequenceNbr,null,null);
        }
        return returnList;
    }


    async getAQSCWMSGFData(returnList,tree,dispNo,constructId,singleId,unitId) {
        if (ObjectUtils.isNotEmpty(tree.childrenList)) {
            for (let i = 0; i < tree.childrenList.length; i++) {
                let childrenListElement = tree.childrenList[i]
                let dispNoChildren = ObjectUtils.isEmpty(dispNo)?(i+1):dispNo+"."+(i+1);
                returnList.push({
                    dispNo:dispNoChildren,
                    levelType:childrenListElement.levelType,
                    projectName:childrenListElement.projectName,
                    aqwmsgf:childrenListElement.aqwmsgf,
                });
                if (childrenListElement.levelType == projectLevelConstant.single) {
                    singleId = childrenListElement.sequenceNbr;
                }
                if (childrenListElement.levelType == projectLevelConstant.unit) {
                    unitId = childrenListElement.sequenceNbr;
                    //添加取费专业
                    let args = {};
                    args.constructId = constructId;
                    args.singleId = singleId;
                    args.unitId = unitId;
                    let result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getAWFSummary(args)
                    for (let k = 0; k < result.length; k++) {
                        let resultElement = result[k];
                        returnList.push({
                            projectName:resultElement.constructMajorTypeName,
                            aqwmsgf:resultElement.price,
                        });
                    }
                }
                await this.getAQSCWMSGFData(returnList,childrenListElement,dispNoChildren,constructId,singleId,unitId);
            }
        }
    }

    async getAwfUnitData(args) {

        let returnList = [];
        let result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getAWFSummary(args)
        let total = 0;
        for (let k = 0; k < result.length; k++) {
            let resultElement = result[k];
            total+=resultElement.price;
            returnList.push({
                dispNo:resultElement.dispNo,
                constructMajorTypeName:resultElement.constructMajorTypeName,
                qfBase:resultElement.instructions,
                calculateMoney:resultElement.calculateMoney,
                rate:resultElement.rate,
                price:resultElement.price,
            });
        }
        if (returnList.length != 0) {
            returnList.push({
                constructMajorTypeName:"合计",
                price:NumberUtil.numberScale2(total),
            });
        }
        return returnList;
    }

    async getSanCaiList(param) {
        let data = [];
        let projectTree = ProjectDomain.getDomain(param.constructId).getProjectTree();
        let tree = xeUtils.toArrayTree(projectTree,{
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        await this.getScRecursionData(data,tree[0],null,tree[0].sequenceNbr,null,null);
        return data;
    }

    async filterProperties(data,precision) {
        for (let i = 0; i < data.length; i++) {
            let dataShiTiElement = data[i];
            if (dataShiTiElement.type == BranchProjectLevelConstant.fb||dataShiTiElement.type == BranchProjectLevelConstant.zfb) {
                dataShiTiElement.quantity=null;
                dataShiTiElement.price=null;
                dataShiTiElement.baseJournalPrice=null;
            }
            if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                dataShiTiElement.quantity=NumberUtil.numberScale(dataShiTiElement.quantity,precision.EDIT.DERCJ.quantity);
                dataShiTiElement.rTotalSum = null;
                dataShiTiElement.rdTotalSum = null;
                dataShiTiElement.cTotalSum = null;
                dataShiTiElement.cdTotalSum = null;
                dataShiTiElement.jTotalSum = null;
                dataShiTiElement.jdTotalSum = null;
            }
        }
    }

    async filterPropertiesCs(data,precision) {
        for (let i = 0; i < data.length; i++) {
            let dataShiTiElement = data[i];
            // if (dataShiTiElement.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE) {
            //     dataShiTiElement.unit = "元";
            // }
            // if (dataShiTiElement.type == "03") {
            //     dataShiTiElement.unit = "项";
            // }

            if (dataShiTiElement.type == BranchProjectLevelConstant.fb||dataShiTiElement.type == BranchProjectLevelConstant.zfb) {
                dataShiTiElement.quantity=null;
                dataShiTiElement.price=null;
                dataShiTiElement.baseJournalPrice=null;
            }
            if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                dataShiTiElement.quantity=NumberUtil.numberScale(dataShiTiElement.quantity,precision.EDIT.DERCJ.quantity);
                dataShiTiElement.rTotalSum = null;
                dataShiTiElement.rdTotalSum = null;
                dataShiTiElement.cTotalSum = null;
                dataShiTiElement.cdTotalSum = null;
                dataShiTiElement.jTotalSum = null;
                dataShiTiElement.jdTotalSum = null;
            }
        }
    }


    async getScRecursionData(returnList,tree,dispNo,constructId,singleId,unitId) {
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precisionCost = precision1.COST_ANALYSIS;

        if (ObjectUtils.isNotEmpty(tree.children)) {
            for (let i = 0; i < tree.children.length; i++) {
                let childrenListElement = tree.children[i]
                let name = "";
                if (childrenListElement.type == ProjectLevelConstant.single) {
                    singleId = childrenListElement.sequenceNbr;
                    name = childrenListElement.name;
                }
                if (childrenListElement.type == ProjectLevelConstant.unit) {
                    unitId = childrenListElement.sequenceNbr;
                    name = "   "+childrenListElement.name
                }
                let result = await this.service.gongLiaoJiProject.gljRcjCollectService.getScCountList(constructId, singleId, unitId);
                let dispNoChildren = ObjectUtils.isEmpty(dispNo)?(i+1):dispNo+"."+(i+1);
                returnList.push({
                    name : name,
                    levelType:childrenListElement.type,
                    钢材 : NumberUtil.numberScale((result.filter(item => item.name=="钢材")[0]).scCount,precisionCost.scNumber),
                    其中钢筋 : NumberUtil.numberScale((result.filter(item => item.name=="其中：钢筋")[0]).scCount,precisionCost.scNumber),
                    木材 : NumberUtil.numberScale((result.filter(item => item.name=="木材")[0]).scCount,precisionCost.scNumber),
                    水泥 : NumberUtil.numberScale((result.filter(item => item.name=="水泥")[0]).scCount,precisionCost.scNumber),
                    dispNo : dispNoChildren,
                });
                await this.getScRecursionData(returnList,childrenListElement,dispNoChildren,constructId,singleId,unitId);
            }
        }
    }

    async getconstructProjectSheet9List(param) {
        let Sheet4List = [];

        let args = {};
        args.constructId = param.constructId;
        let summaryList = await this.service.gongLiaoJiProject.gljEstimateSummaryService.getEstimateSummaryList(args);

        if (ObjectUtils.isNotEmpty(summaryList)) {
            summaryList.forEach(o => {
                Sheet4List.push(o);
                if (o.name === "工程建设其他费用") {
                    //增加建设其他费的数据
                    let args1 = {};
                    args1.type = 1;
                    args1.levelType = projectLevelConstant.construct;
                    args1.code = 5;
                    args1.unitId = null;
                    args1.constructId = param.constructId;
                    args1.singleId = null;
                    args1.projectId = param.constructId;
                    let otherProjectCostList = this.service.gongLiaoJiProject.gljOtherProjectCostService.getOtherProjectCostList(args1);
                    if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
                        let otherProjectCostListCopy = ConvertUtil.deepCopy(otherProjectCostList);
                        for (let i = 1; i < otherProjectCostListCopy.length; i++) {
                            let p = otherProjectCostListCopy[i];
                            p.price = p.amount;
                            p.dispNo = p.code;
                            Sheet4List.push(p);
                        }
                    }
                }
            })
        }

        return Sheet4List;
    }

    async removeListElement(list,index) {
        if (ObjectUtils.isNotEmpty(list)) {
            if (index==list.length-1) return;
            for (let i = index; i < list.length; i++) {
                if (ObjectUtils.isNotEmpty(list[i].type) && list[i].type == DeTypeConstants.DE_TYPE_EMPTY) {
                    list.splice(i,1);
                    await this.removeListElement(list,i);
                    break;
                }
            }
        }
    }


    async addConversionStr(args,dataList) {

        let {sheetName} = args;
        if (sheetName.includes("措施")) {
            for (let i = 0; i < dataList.length; i++) {
                let element = dataList[i];
                //措施项清单不显示工程量及单价
                if (element.type == BranchProjectLevelConstant.qd) {
                    element.quantity = null;
                    element.price = null;
                    element.unit = "项";
                }
                if (element.type == BranchProjectLevelConstant.qd||GljExcelUtil.judgeIsDeType(element)) {
                    element.resQty = null;//措施项和定额都没有消耗量
                }
                if (element.awfType == 2 && GljExcelUtil.judgeIsDeType(element)) {  //表示为不可竞争措施项目里的定额
                    // element.unit = "元";
                }
            }
        }
        //将空定额行除去
        await this.removeListElement(dataList,0);
        //以下处理定额换算串拼接的问题
        let resultConversionCS = _.cloneDeep(await this.service.gongLiaoJiProject.gljConversionDeProcess.conversionRuleListAll({
            constructId:args.constructId,
            unitId:args.unitId
        }));
        for (let i = 0; i < dataList.length; i++) {
            let dataShiTiElement = dataList[i];
            if (GljExcelUtil.judgeIsDeType(dataShiTiElement)) {
                if (ObjectUtils.isNotEmpty(dataShiTiElement.displayType) && dataShiTiElement.displayType != "定") {

                    if (dataShiTiElement.displayType.includes("借")) {
                        dataShiTiElement.deCode = "借"+dataShiTiElement.deCode;
                    }
                    if (dataShiTiElement.displayType.includes("换")) {
                        let object = resultConversionCS.filter(item => item.sequenceNbr==dataShiTiElement.sequenceNbr);
                        let redArray = [];
                        if (ObjectUtils.isNotEmpty(object)) {
                            redArray = object[0].redArray;
                        }
                        if (ObjectUtils.isNotEmpty(redArray)) {
                            dataShiTiElement.deCode+="  ";
                            for (let j = 0; j < redArray.length-1; j++) {
                                let redArrayElement = redArray[j];
                                dataShiTiElement.deCode += redArrayElement+",";
                            }
                            dataShiTiElement.deCode += redArray[redArray.length-1];
                        }
                    }
                    if (dataShiTiElement.displayType.includes("调")) {
                        dataShiTiElement.deCode += "  "+"调";
                    }
                    if (dataShiTiElement.displayType.includes("换")) {
                        dataShiTiElement.deCode += "  "+"换";
                    }
                }
            }
            if (dataShiTiElement.type == DeTypeConstants.SUB_DE_TYPE_DE) {
                dataShiTiElement.deName = ObjectUtils.isNotEmpty(dataShiTiElement.specification)?dataShiTiElement.deName+"  "+dataShiTiElement.specification:dataShiTiElement.deName;
            }
        }
    }


    //处理不可竞争措施项目下的人工费、机械费、材料费合价
    async dealBkjzcsxmData(dataList) {
        if (ObjectUtils.isNotEmpty(dataList)) {
            let find = dataList.find(item => item.deName === "不可竞争措施项目" && item.type === BranchProjectLevelConstant.fb);
            if (ObjectUtils.isNotEmpty(find)) {
                find.bkjzcsxm = true;
                await this.dealBkjzcsxmData1(dataList, find.sequenceNbr);
            } else {
                let find = dataList.find(item => item.deName === "安全生产、文明施工费" && item.type === BranchProjectLevelConstant.qd);
                if (ObjectUtils.isNotEmpty(find)) {
                    find.bkjzcsxm = true;
                    await this.dealBkjzcsxmData1(dataList, find.sequenceNbr);
                }
            }
        }
        }

    async dealBkjzcsxmData1(dataList, parentId) {
        let list = dataList.filter(item => item.parentId === parentId);
        if (ObjectUtils.isNotEmpty(list)) {
            for (let item of list) {
                item.bkjzcsxm = true;
                await this.dealBkjzcsxmData1(dataList, item.sequenceNbr);
            }
        }
    }


    async getconstructProjectSheet10List(param) {
        let Sheet4List = [];

        let args = {};
        args.constructId = param.constructId;
        let summaryList = await this.service.gongLiaoJiProject.gljEstimateSummaryService.getEstimateSummaryList(args);

        if (ObjectUtils.isNotEmpty(summaryList)) {
            for (let item of summaryList) {
                if (ObjectUtils.isNotEmpty(item.price) && item.price > 0) {
                    Sheet4List.push(item);
                    if (item.name === "工程建设其他费用") {
                        //增加建设其他费的数据
                        let args1 = {};
                        args1.type = 1;
                        args1.levelType = projectLevelConstant.construct;
                        args1.code = 5;
                        args1.unitId = null;
                        args1.constructId = param.constructId;
                        args1.singleId = null;
                        args1.projectId = param.constructId;
                        let otherProjectCostList = this.service.gongLiaoJiProject.gljOtherProjectCostService.getOtherProjectCostList(args1);
                        if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
                            let otherProjectCostListCopy = ConvertUtil.deepCopy(otherProjectCostList);
                            for (let i = 1; i < otherProjectCostListCopy.length; i++) {
                                let p = otherProjectCostListCopy[i];
                                if (ObjectUtils.isNotEmpty(p.amount) && p.amount != "0" && p.amount != "0.00") {
                                    p.price = p.amount;
                                    p.dispNo = p.code;
                                    Sheet4List.push(p);
                                }
                            }
                        }
                    }
                }
            }
        }

        return Sheet4List;
    }

    async getconstructProjectSheet11List(param) {
        let Sheet4List = [];

        let args = {};
        args.constructId = param.constructId;
        let summaryList = await this.service.gongLiaoJiProject.gljEstimateSummaryService.getEstimateSummaryList(args);

        if (ObjectUtils.isNotEmpty(summaryList)) {
            for (let item of summaryList) {
                if (ObjectUtils.isNotEmpty(item.price) && item.price > 0) {
                    Sheet4List.push(item);
                    if (item.name === "工程建设其他费用") {
                        //增加建设其他费的数据
                        let args1 = {};
                        args1.type = 1;
                        args1.levelType = projectLevelConstant.construct;
                        args1.code = 5;
                        args1.unitId = null;
                        args1.constructId = param.constructId;
                        args1.singleId = null;
                        args1.projectId = param.constructId;
                        let otherProjectCostList = this.service.gongLiaoJiProject.gljOtherProjectCostService.getOtherProjectCostList(args1);
                        if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
                            let otherProjectCostListCopy = ConvertUtil.deepCopy(otherProjectCostList);
                            for (let i = 1; i < otherProjectCostListCopy.length; i++) {
                                let p = otherProjectCostListCopy[i];
                                if (ObjectUtils.isNotEmpty(p.amount) && p.amount != "0" && p.amount != "0.00") {
                                    p.price = p.amount;
                                    p.dispNo = p.code;
                                    p.unit = "万元";
                                    Sheet4List.push(p);
                                }
                            }
                        }
                    }
                }
            }
        }

        return Sheet4List;
    }


    async getconstructProjectSheet12List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 0;
        args.levelType = projectLevelConstant.construct;
        args.constructId = param.constructId;
        let list = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(list)) {
            Sheet4List = ConvertUtil.deepCopy(list);
        }

        return Sheet4List;
    }

    async getconstructProjectSheet13List(param) {
        let Sheet4List = [];

        let args = {};
        args.levelType = projectLevelConstant.construct;
        args.constructId = param.constructId;
        let adjustList =await this.service.gongLiaoJiProject.gljAdjustService.getGsAdjustList(args);

        if (ObjectUtils.isNotEmpty(adjustList)) {
            Sheet4List = ConvertUtil.deepCopy(adjustList);
        }


        return Sheet4List;
    }

    async getconstructProjectSheet14List(param) {
        let Sheet4List = [];

        let args = {};
        args.levelType = projectLevelConstant.construct;
        args.unitId = null;
        args.constructId = param.constructId;
        args.type = FunctionTypeConstants.SBGZF_KEY_TYPE_GW;
        let resultList = this.service.gongLiaoJiProject.gljEquipmentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            for (let item of resultList) {
                let otherMYArray = [];
                let otherRMBArra = [];
                let equipmentCostsCal = item.equipmentCostsCal;
                let otherMY = equipmentCostsCal.filter(o => o.costType === "其他（美元）");
                let otherRMB = equipmentCostsCal.filter(o => o.costType === "其他（人民币）");
                for (let i = 0; i < otherMY.length; i++) {
                    let myElement = otherMY[i];
                    let data = {};
                    data.dispNo = item.dispNo;
                    data.name = item.name;
                    data.unit = item.unit;
                    data.quantity = item.quantity;
                    data.fob_usd = item.fob_usd;
                    data.costNameMY = myElement.name;
                    data.costMoneyMY = myElement.price;
                    data.cif_usd = item.cif_usd;
                    data.cif_cny = item.cif_cny;
                    data.price = item.price;
                    data.totalPrice = item.totalPrice;
                    otherMYArray.push(data);
                }
                for (let i = 0; i < otherRMB.length; i++) {
                    let data1 = {};
                    if (ObjectUtils.isNotEmpty(otherMYArray[i])) {
                        data1 = otherMYArray[i];
                    }
                    data1.dispNo = item.dispNo;
                    data1.name = item.name;
                    data1.unit = item.unit;
                    data1.quantity = item.quantity;
                    data1.fob_usd = item.fob_usd;
                    data1.cif_usd = item.cif_usd;
                    data1.cif_cny = item.cif_cny;
                    data1.costNameRMB = otherRMB[i].name;
                    data1.costMoneyRMB = otherRMB[i].price;
                    data1.price = item.price;
                    data1.totalPrice = item.totalPrice;
                    otherRMBArra.push(data1);
                }
                if (ObjectUtils.isNotEmpty(otherRMBArra)) {
                    otherRMBArra.forEach(o => {
                        Sheet4List.push(o);
                    })
                } else {
                    otherMYArray.forEach(o => {
                        Sheet4List.push(o);
                    })
                }
            }
        }
        return Sheet4List;
    }

    async getconstructProjectSheet15List(param) {
        let Sheet4List = [];

        let args = {};
        args.levelType = 1;
        args.type = FunctionTypeConstants.SBGZF_KEY_TYPE_GN;
        args.unitId = null;
        args.constructId = param.constructId;

        let list = this.service.gongLiaoJiProject.gljEquipmentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(list)) {
            let filter = list.filter(o => ObjectUtils.isNotEmpty(o.quantity) && ObjectUtils.isNotEmpty(o.factoryPrice));
            if (ObjectUtils.isNotEmpty(filter)) {
                Sheet4List = ConvertUtil.deepCopy(filter);
            }
        }
        return Sheet4List;
    }


    async getconstructProjectSheet16List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 4;
        args.levelType = projectLevelConstant.construct;
        args.constructId = param.constructId;
        let list = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(list)) {
            Sheet4List = ConvertUtil.deepCopy(list);
        }

        return Sheet4List;
    }


    // async getconstructProjectSheet17List(param) {
    //     let Sheet4List = [];
    //
    //     let MY = 0;
    //     let RMB = 0;
    //     //国外采购设置数据
    //     let args = {};
    //     args.levelType = 1;
    //     args.constructId = param.constructId;
    //     args.unitId = null;
    //     args.type = "sbgzf01";
    //     let gwsbList = this.service.gongLiaoJiProject.gljEquipmentCostsService.getList(args);
    //     if (ObjectUtils.isNotEmpty(gwsbList)) {
    //         for (let item of gwsbList) {
    //             // 离岸价美元fob_usd  数量quantity  汇率
    //             if (ObjectUtils.isNotEmpty(item.fob_usd) && ObjectUtils.isNotEmpty(item.quantity)) {
    //                 MY = NumberUtil.multiply(item.fob_usd, item.quantity).toFixed(2);
    //             }
    //             if (ObjectUtils.isNotEmpty(item.exchangeRate)) {
    //                 RMB = NumberUtil.multiply(MY, item.exchangeRate).toFixed(2);
    //             }
    //         }
    //     }
    //
    //     //概算汇总数据
    //     let args1 = {};
    //     args1.constructId = param.constructId;
    //     let summaryList = await this.service.gongLiaoJiProject.gljEstimateSummaryService.getEstimateSummaryList(args1);
    //     if (ObjectUtils.isNotEmpty(summaryList)) {
    //         let deepCopy1 = ConvertUtil.deepCopy(summaryList);
    //         for (let item1 of deepCopy1) {
    //             if (item1.name === "工程费用" || item1.name === "设备购置费") {
    //                 item1.MY = MY;
    //                 item1.RMB = RMB;
    //             }
    //             Sheet4List.push(item1);
    //         }
    //     }
    //
    //     return Sheet4List;
    // }


    // async getconstructProjectSheet18List(param) {
    //     let Sheet4List = [];
    //
    //     let MY = 0;
    //     let RMB = 0;
    //     //国外采购设置数据
    //     let args = {};
    //     args.levelType = 1;
    //     args.constructId = param.constructId;
    //     args.unitId = null;
    //     args.type = "sbgzf01";
    //     let gwsbList = this.service.gongLiaoJiProject.gljEquipmentCostsService.getList(args);
    //     if (ObjectUtils.isNotEmpty(gwsbList)) {
    //         for (let item of gwsbList) {
    //             // 离岸价美元fob_usd  数量quantity  汇率
    //             if (ObjectUtils.isNotEmpty(item.fob_usd) && ObjectUtils.isNotEmpty(item.quantity)) {
    //                 MY = NumberUtil.multiply(item.fob_usd, item.quantity).toFixed(2);
    //             }
    //             if (ObjectUtils.isNotEmpty(item.exchangeRate)) {
    //                 RMB = NumberUtil.multiply(MY, item.exchangeRate).toFixed(2);
    //             }
    //         }
    //     }
    //
    //     //概算汇总数据
    //     let args1 = {};
    //     args1.constructId = param.constructId;
    //     let summaryList = await this.service.gongLiaoJiProject.gljEstimateSummaryService.getEstimateSummaryList(args1);
    //     if (ObjectUtils.isNotEmpty(summaryList)) {
    //         let deepCopy1 = ConvertUtil.deepCopy(summaryList);
    //         for (let item1 of deepCopy1) {
    //             if (item1.name === "工程费用" || item1.name === "设备购置费") {
    //                 item1.MY = MY;
    //                 item1.RMB = RMB;
    //             }
    //             Sheet4List.push(item1);
    //         }
    //     }
    //
    //     return Sheet4List;
    // }

    async getconstructSingleSheet1List(param) {
        let Sheet4List = [];
        let args = {};
        args.type = projectLevelConstant.single;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = null;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);

        if (ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisSingleVOList)) {
            let deepCopy1 = ConvertUtil.deepCopy(costAnalysiss.costAnalysisSingleVOList);
            await this.cacluCostAnalysiss(deepCopy1, Sheet4List, "1", false);
        }
        return Sheet4List;
    }


    async cacluCostAnalysiss(cacluData, result, parentDispNo, suffix) {
        if (ObjectUtils.isNotEmpty(cacluData)) {
            for (let i = 0; i < cacluData.length; i++) {
                let item = cacluData[i];
                item.dispNo = parentDispNo;
                if (suffix) {
                    let number = i + 1;
                    item.dispNo = parentDispNo + "." + number;
                }
                result.push(item);
                if (ObjectUtils.isNotEmpty(item.childrenList)) {
                    await this.cacluCostAnalysiss(item.childrenList, result, item.dispNo, true);
                }
            }
        }
    }



    async getconstructSingleSheet2List(param) {
        let Sheet4List = [];
        let args = {};
        // args.type = projectLevelConstant.single;
        args.constructId = param.constructId;
        // args.singleId = param.singleId;
        // args.unitId = param.unitId;
        let gsAdjust = await this.service.gongLiaoJiProject.gljAdjustService.getGsAdjustList(args);
        //TODO 筛选出该单项的数据，目前数据应该是整个工程
        if (ObjectUtils.isNotEmpty(gsAdjust)) {
            Sheet4List = ConvertUtil.deepCopy(gsAdjust);
        }

        return Sheet4List;
    }




    async calDERCJLIST(constructId, unitId, deData, childDeData, allDeNode, resultList, rcjList) {
        let childDeList = allDeNode.filter(o => o.parentId === childDeData.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_DE);
        if (ObjectUtils.isNotEmpty(childDeList)) {
            let childDeList1 = ConvertUtil.deepCopy(childDeList);
            for (let childDe of childDeList1) {
                //筛选主材费和设备费
                let deRcjList = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, childDe.sequenceNbr, DeTypeConstants.DE_TYPE_DE);
                if (ObjectUtils.isNotEmpty(deRcjList)) {
                    let deRcjList1 = ConvertUtil.deepCopy(deRcjList);
                    let zcsbList = deRcjList1.filter(o => o.type === "主材费" || o.type === "设备费");
                    if (ObjectUtils.isNotEmpty(zcsbList)) {
                        zcsbList.forEach(o => {
                            let find = rcjList.find(p => p.deCode === o.materialCode);
                            if (ObjectUtils.isNotEmpty(find)) {
                                //编码一致的人材机只进行数量汇总
                                find.quantity = NumberUtil.add(find.quantity, o.totalNumber);
                            } else {
                                o.deCode = o.materialCode;
                                o.deName = o.materialName;
                                o.quantity = o.totalNumber;
                                o.price = o.marketPrice;
                                o.totalNumber = o.total;  //合价
                                resultList.push(o);
                                rcjList.push(o);
                            }
                        })
                    }

                    let RGFLIST = deRcjList1.filter(o => o.type === "人工费");
                    let CLFLIST = deRcjList1.filter(o => o.type === "材料费");
                    let JXFLIST = deRcjList1.filter(o => o.type === "机械费");
                    let GRHJLIST = deRcjList1.filter(o => o.type === "人工费" && o.unit === "工日");
                    if (ObjectUtils.isNotEmpty(RGFLIST)) {
                        let toNumber = RGFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.rgf = NumberUtil.add(deData.rgf, toNumber);
                    }
                    if (ObjectUtils.isNotEmpty(CLFLIST)) {
                        let toNumber1 = CLFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.clf = NumberUtil.add(deData.clf, toNumber1);
                    }
                    if (ObjectUtils.isNotEmpty(JXFLIST)) {
                        let toNumber2 = JXFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.jxf = NumberUtil.add(deData.jxf, toNumber2);
                    }
                    if (ObjectUtils.isNotEmpty(GRHJLIST)) {
                        let toNumber3 = GRHJLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.grhj = NumberUtil.add(deData.grhj, toNumber3);
                    }
                }

                let child2DeList = allDeNode.filter(o => o.parentId === childDe.sequenceNbr);
                if (ObjectUtils.isNotEmpty(child2DeList)) {
                    //说明地下还有子集，迭代查询
                    await this.calDERCJLIST(constructId, unitId, deData, childDe, allDeNode, resultList, rcjList);
                }
            }
        }
    }







    async getUnitSheetSingleqfMajorType(param) {
        let precision4 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);

        let resultList = [];
        param.qfMajorType = "TOTAL";
        let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
        if (ObjectUtils.isNotEmpty(resultSummary)) {
            resultList.push(
                {
                    dispNo:NumberUtil.numberToChinese(1)+"、",
                    name:resultSummary[0].name,
                    price:NumberUtil.numberScale(resultSummary[0].price,precision4.COST_SUMMARY.je),
                }
            );
            for (let j = 1; j < resultSummary.length; j++) {
                resultList.push({
                    dispNo:resultSummary[j].dispNo,
                    name:resultSummary[j].name,
                    instructions:resultSummary[j].instructions,
                    rate:resultSummary[j].rate,
                    price:resultSummary[j].price,
                });
            }
            resultList.push({
                dispNo:NumberUtil.numberToChinese(2)+"、",
                name:"工程造价",
                price:NumberUtil.numberScale(resultSummary[0].price,precision4.COST_SUMMARY.je),
            });
            resultList.push({
                price:"含税工程造价："+NumberUtil.numToCny(NumberUtil.numberScale(resultSummary[0].price,precision4.COST_SUMMARY.je)),
            });
        }
        return resultList;
    }

    async getconstructUnitSheet6List(param) {
        let precision5 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);

        let resultList = [];
        let totalPrice = 0;//总的工程造价
        let result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(param);
        if (ObjectUtils.isNotEmpty(result) && ObjectUtils.isNotEmpty(result.itemList)) {
            for (let i = 0; i < result.itemList.length; i++) {
                let element = result.itemList[i];
                for (let elementKey in element) {
                    param.qfMajorType = elementKey;
                    let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
                    resultList.push({
                        dispNo:NumberUtil.numberToChinese(i+1)+"、",
                        name:element[elementKey],
                        price:NumberUtil.numberScale(resultSummary.filter(item => item.category=="工程造价")[0].price,precision5.COST_SUMMARY.je),
                    });
                    // totalPrice+= resultSummary.filter(item => item.category=="工程造价")[0].price;
                    totalPrice = NumberUtil.numberScale(NumberUtil.add(totalPrice,resultSummary.filter(item => item.category=="工程造价")[0].price),precision5.COST_SUMMARY.je);
                    for (let j = 0; j < resultSummary.length; j++) {
                        if (!resultSummary[j].whetherPrint) {
                            continue
                        }
                        if (ObjectUtils.isNotEmpty(resultSummary[j].rate)) {
                            let rateCal = null;
                            if (ObjectUtils.isNotEmpty(resultSummary[j].rate)) {
                                if (NumberUtil.countDecimalPlaces(resultSummary[j].rate) > 2) {
                                    rateCal = resultSummary[j].rate;
                                } else {
                                    rateCal = NumberUtil.numberScale(resultSummary[j].rate, precision5.COST_SUMMARY.SUMMARY.freeRate);
                                }
                            }

                            resultList.push({
                                dispNo: resultSummary[j].dispNo,
                                name: resultSummary[j].name,
                                instructions: resultSummary[j].instructions,
                                rate: rateCal,
                                price: NumberUtil.numberScale(resultSummary[j].price,precision5.COST_SUMMARY.je),
                            });
                        } else {
                            resultList.push({
                                dispNo: resultSummary[j].dispNo,
                                name: resultSummary[j].name,
                                instructions: resultSummary[j].instructions,
                                rate: resultSummary[j].rate,
                                price: NumberUtil.numberScale(resultSummary[j].price,precision5.COST_SUMMARY.je),
                            });
                        }
                    }
                }
            }
            resultList.push({
                dispNo:NumberUtil.numberToChinese(result.itemList.length+1)+"、",
                name:"工程造价",
                price:totalPrice,
            });
        }
        resultList.push({
            price:"含税工程造价："+NumberUtil.numToCny(totalPrice),
        });

        return resultList;
    }

    async getconstructUnitSheetMultiQuFei(param,arrayName) {
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        let resultList = [];
        let totalPrice = 0;//总的工程造价
        let result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(param);
        if (ObjectUtils.isNotEmpty(result) && ObjectUtils.isNotEmpty(result.itemList)) {
            for (let i = 0; i < result.itemList.length; i++) {
                let element = result.itemList[i];
                for (let elementKey in element) {
                    param.qfMajorType = elementKey;
                    let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
                    resultList.push({
                        name:element[elementKey],
                        price:GljExcelUtil._roundAndPadCeping(resultSummary.filter(item => item.category=="工程造价")[0].price,precision.COST_SUMMARY.je),
                    });
                    arrayName.push(element[elementKey]);
                    totalPrice+= NumberUtil.numberScale(resultSummary.filter(item => item.category=="工程造价")[0].price,precision.COST_SUMMARY.je);
                    for (let j = 0; j < resultSummary.length; j++) {
                        if (!resultSummary[j].whetherPrint) {
                            continue
                        }
                        resultList.push({
                            dispNo:resultSummary[j].dispNo,
                            code:resultSummary[j].code,
                            name:resultSummary[j].name,
                            instructions:resultSummary[j].instructions,
                            rate:resultSummary[j].rate,
                            price:GljExcelUtil._roundAndPadCeping(resultSummary[j].price,precision.COST_SUMMARY.je),
                        });
                    }
                }
            }
            resultList.push({
                name:"工程造价",
                price:GljExcelUtil._roundAndPadCeping(totalPrice,precision.COST_SUMMARY.je),
            });
            arrayName.push("工程造价");
        }

        return resultList;
    }

    async getconstructUnitSheetShengZhan10(param) {
        let resultList = [];
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        param.type = projectLevelConstant.single;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(param);
        let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(param.constructId, param.unitId);

        let unitcost = costAnalysiss.costAnalysisSingleVOList[0].childrenList.filter(item => item.sequenceNbr==param.unitId)[0];

        let rgf = NumberUtil.add(
            unitcost.ysrgf,
            unitcost.csrgf
        );
        let clf = NumberUtil.add(
            unitcost.ysclf,
            unitcost.csclf
        );
        let jxf = NumberUtil.add(
            unitcost.ysjxf,
            unitcost.csjxf
        );
        let zcsbf = NumberUtil.addParams(
            unitcost.yssbf,
            unitcost.yszcf,
            unitcost.cssbf,
            unitcost.cszcf,
        );
        resultList.push({
            name:unit.name,
            price:GljExcelUtil._roundAndPadCeping(unitcost.projectCost,precision.COST_SUMMARY.je),
            rgf:GljExcelUtil._roundAndPadCeping(rgf,precision.COST_SUMMARY.je),
            clf:GljExcelUtil._roundAndPadCeping(clf,precision.COST_SUMMARY.je),
            jxf:GljExcelUtil._roundAndPadCeping(jxf,precision.COST_SUMMARY.je),
            zcSbf:GljExcelUtil._roundAndPadCeping(zcsbf,precision.COST_SUMMARY.je),
        });
        return resultList;
    }

    async getconstructUnitSheetSummary(param) {
        let precision4 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);

        let resultList = [];
        let boolResult = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(param);
        if ((ObjectUtils.isNotEmpty(boolResult) && !boolResult)||ObjectUtils.isEmpty(boolResult)) {  //  true 单专业  false 多专业汇总
            //如果为多专业
            let result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(param);
            if (ObjectUtils.isNotEmpty(result) && ObjectUtils.isNotEmpty(result.itemList)) {
                let total = 0;
                for (let i = 0; i < result.itemList.length; i++) {
                    let element = result.itemList[i];
                    for (let elementKey in element) {
                        param.qfMajorType = elementKey;
                        let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
                        resultList.push({
                            dispNo:i+1,
                            name:element[elementKey],
                            price:GljExcelUtil._roundAndPadCeping(resultSummary.filter(item => item.category=="工程造价")[0].price,precision4.COST_SUMMARY.je),
                        });
                        total = NumberUtil.add(total,GljExcelUtil._roundAndPadCeping(Number(resultSummary.filter(item => item.category=="工程造价")[0].price),precision4.COST_SUMMARY.je));
                    }
                }
                resultList.push({
                    dispNo:result.itemList.length+1,
                    name:"工程造价合计",
                    price:GljExcelUtil._roundAndPadCeping(total,precision4.COST_SUMMARY.je),
                });
                resultList.push({
                    price:"含税工程造价："+NumberUtil.numToCny(GljExcelUtil._roundAndPadCeping(total,precision4.COST_SUMMARY.je)),
                });
            }
        }else {
            param.qfMajorType = "TOTAL";
            let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
            if (ObjectUtils.isNotEmpty(resultSummary)) {
                resultList.push({
                    dispNo:1,
                    name:resultSummary[0].name,
                    price:GljExcelUtil._roundAndPadCeping(resultSummary.filter(item => item.category=="工程造价")[0].price,precision4.COST_SUMMARY.je),
                });
                resultList.push({
                    dispNo:2,
                    name:"工程造价合计",
                    price:GljExcelUtil._roundAndPadCeping(resultSummary.filter(item => item.category=="工程造价")[0].price,precision4.COST_SUMMARY.je),
                });
                resultList.push({
                    price:"含税工程造价："+NumberUtil.numToCny(NumberUtil.numberScale(resultSummary.filter(item => item.category=="工程造价")[0].price,precision4.COST_SUMMARY.je)),
                });
            }
        }

        return resultList;
    }

    async getconstructUnitSheetSummary222(param) {
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        let resultList = [];
        param.qfMajorType = "TOTAL";
        let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
        if (ObjectUtils.isNotEmpty(resultSummary)) {
            for (let j = 0; j < resultSummary.length; j++) {
                if (!resultSummary[j].whetherPrint) {
                    continue
                }
                resultList.push({
                    dispNo:resultSummary[j].dispNo,
                    code:resultSummary[j].code,
                    name:resultSummary[j].name,
                    instructions:resultSummary[j].instructions,
                    rate:resultSummary[j].rate,
                    price:GljExcelUtil._roundAndPadCeping(resultSummary[j].price,precision.COST_SUMMARY.je),
                });
            }
        }
        return resultList;
    }

    async getconstructUnitSheetShengZhan11(param) {
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        let resultList = [];
        let cost0 = 0;
        let cost1 = 0;
        let cost2 = 0;
        let cost3 = 0;
        let cost4 = 0;
        let cost5 = 0;
        let cost6 = 0;
        let cost7 = 0;
        let cost8 = 0;
        let cost9 = 0;
        let cost10 = 0;
        let cost11 = 0;
        let cost12 = 0;
        let cost13 = 0;
        let cost14 = 0;
        let boolResult = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(param);
        if ((ObjectUtils.isNotEmpty(boolResult) && !boolResult)||ObjectUtils.isEmpty(boolResult)) {  //  true 单专业  false 多专业汇总
            // let result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(param);
            // if (ObjectUtils.isNotEmpty(result) && ObjectUtils.isNotEmpty(result.itemList)) {
            //     for (let i = 0; i < result.itemList.length; i++) {
            //         let element = result.itemList[i];
            //         for (let elementKey in element) {
            //             param.qfMajorType = elementKey;
            //             let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
            //             cost0 =NumberUtil.add(resultSummary.filter(item => item.category=="直接费")[0].price,cost0);
            //             cost1 =NumberUtil.add(resultSummary.filter(item => item.category=="人工费")[0].price,cost1);
            //             cost2 =NumberUtil.add(resultSummary.filter(item => item.category=="材料费")[0].price,cost2);
            //             cost3 =NumberUtil.add(resultSummary.filter(item => item.category=="机械费")[0].price,cost3);
            //             cost4 =NumberUtil.add(resultSummary.filter(item => item.category=="主材费")[0].price,cost4);
            //             cost5 =NumberUtil.add(resultSummary.filter(item => item.category=="设备费")[0].price,cost5);
            //             cost6 =NumberUtil.add(resultSummary.filter(item => item.name=="直接费中的人工费+机械费")[0].price,cost6);
            //             cost7 =NumberUtil.add(resultSummary.filter(item => item.category=="企业管理费")[0].price,cost7);
            //             cost8 =NumberUtil.add(resultSummary.filter(item => item.category=="利润")[0].price,cost8);
            //             cost9 =NumberUtil.add(resultSummary.filter(item => item.name=="价款调整")[0].price,cost9);
            //             cost10 =NumberUtil.add(resultSummary.filter(item => item.category=="独立费")[0].price,cost10);
            //             cost11 =NumberUtil.add(resultSummary.filter(item => item.category=="人材机价差")[0].price,cost11);
            //             cost12 =NumberUtil.add(resultSummary.filter(item => item.category=="安全文明施工费")[0].price,cost12);
            //             cost13 =NumberUtil.add(resultSummary.filter(item => item.category=="税金")[0].price,cost13);
            //             cost14 =NumberUtil.add(resultSummary.filter(item => item.category=="工程造价")[0].price,cost14);
            //         }
            //     }
            //     resultList.push({price:cost0.toFixed(precision.COST_SUMMARY.je)},{price:cost1.toFixed(precision.COST_SUMMARY.je)},{price:cost2.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost3.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost4.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost5.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost6.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost7.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost8.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost9.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost10.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost11.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost12.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost13.toFixed(precision.COST_SUMMARY.je)},
            //         {price:cost14.toFixed(precision.COST_SUMMARY.je)});
            //
            //     let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
            //     for (let i = 0; i < resultList.length; i++) {
            //         let object = resultSummary[i];
            //         resultList[i].jsjc = object.instructions;
            //         resultList[i].feeRate = object.rate;
            //     }
            //     resultList.push({
            //         price:"含税工程造价："+NumberUtil.numToCny(cost14),
            //     });
            // }
            param.qfMajorType = "TOTAL";
            let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
            if (ObjectUtils.isNotEmpty(resultSummary)) {
                resultSummary = resultSummary.filter(item => ObjectUtils.isNotEmpty(item.code));
                for (let i = 0; i < resultSummary.length; i++) {
                    let object = resultSummary[i];
                    if (!object.whetherPrint) {
                        continue
                    }
                    resultList.push({
                        dispNo:i+1,
                        code:object.code,
                        name:object.name,
                        price:GljExcelUtil._roundAndPadCeping(object.price,precision.COST_SUMMARY.je),
                        jsjc:object.instructions,
                        feeRate: object.rate
                    });
                }
                resultList.push({
                    price:"含税工程造价："+NumberUtil.numToCny(NumberUtil.numberScale(resultSummary.filter(item => item.category=="工程造价")[0].price,precision.COST_SUMMARY.je)),
                });
            }
        }else {
            param.qfMajorType = "TOTAL";
            let resultSummary = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param);
            if (ObjectUtils.isNotEmpty(resultSummary)) {
                for (let i = 1; i < resultSummary.length; i++) {
                    let object = resultSummary[i];
                    if (!object.whetherPrint) {
                        continue
                    }
                    resultList.push({
                        dispNo:i,
                        code:object.code,
                        name:object.name,
                        price:GljExcelUtil._roundAndPadCeping(object.price,precision.COST_SUMMARY.je),
                        jsjc:object.instructions,
                        feeRate: object.rate
                    });
                }
                resultList.push({
                    price:"含税工程造价："+NumberUtil.numToCny(NumberUtil.numberScale(resultSummary.filter(item => item.category=="工程造价")[0].price,precision.COST_SUMMARY.je)),
                });
            }
        }
        return resultList;
    }

    async getconstructUnitSheet7List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";

        let args = {};
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        args.constructMajorType = "2018-AZGC-GS"
        let costSummaryList11 = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(args);
        let costSummaryPrintList = costSummaryList11.filter(p => p.whetherPrint == 1);
        if (ObjectUtils.isNotEmpty(costSummaryPrintList)) {
            for (let map1 of costSummaryPrintList) {
                let data = {};
                data.name = map1.name;
                data.instructions = map1.instructions;
                data.rate = map1.rate;
                data.price = map1.price;
                if (data.name === "工程造价") {
                    heji.total = data.price;
                }
                resultList.push(data);
            }
        }
        param["unitDwgcfyzgcHeji"] = heji;

        return resultList;
    }

    async getRcjSummaryShiTi(param) {
        let resultQuery = await this.service.gongLiaoJiProject.gljCommonService.getProjectSetting(param);
        let pricingMethod = resultQuery.get("PRICING_METHOD");//true为市场价组价

        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let isGeneral = true;
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            isGeneral = false;
        }
        let allYsshuData = _.cloneDeep(ProjectDomain.getDomain(param.constructId).deDomain.getDeAllTreeDepth(param.constructId,param.unitId));
        let deLists = allYsshuData.filter(item => item.type == BranchProjectLevelConstant.de || item.type == DeTypeConstants.DE_TYPE_RESOURCE || item.type == DeTypeConstants.DE_TYPE_USER_DE
            || item.type == DeTypeConstants.DE_TYPE_USER_RESOURCE || item.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE || item.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE);
        allYsshuData = allYsshuData.filter(item => item.type !="05");
        for (let i = 0; i < deLists.length; i++) {
            //如果是补充定额人材机 或 定额人材机
            if (deLists[i].type==DeTypeConstants.DE_TYPE_USER_RESOURCE||deLists[i].type==DeTypeConstants.DE_TYPE_RESOURCE) {
                continue;
            }
            let rcjLists = _.cloneDeep(await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(param.constructId, param.unitId, deLists[i].sequenceNbr,deLists[i].type));
            if (ObjectUtils.isNotEmpty(rcjLists)) {
                for (let j = 0; j < rcjLists.length; j++) {
                    rcjLists[j].unit = await this.getDisplayUnit(rcjLists[j]);
                }
                allYsshuData.push(...rcjLists);
            }
        }
        await this.setTreeDispNoShiTi(allYsshuData);
        let arrayTree = xeUtils.toArrayTree(allYsshuData, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        let data = xeUtils.toTreeArray(arrayTree);
        await this.addConversionStr(param,data);
        data = data.filter(item => item.isTempRemove!=CommonConstants.COMMON_YES);//过滤掉临时删除项
        data = data.filter(item => item.type!="0");//将单位工程行去掉
        for (let i = 0; i < data.length; i++) {
            let datum = data[i];
            if (datum.type == BranchProjectLevelConstant.de || datum.type == DeTypeConstants.DE_TYPE_RESOURCE || datum.type == DeTypeConstants.DE_TYPE_USER_DE
                || datum.type == DeTypeConstants.DE_TYPE_USER_RESOURCE || datum.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE || datum.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE) {   //如果是定额类型 跳过
                continue;
            }
            if (ObjectUtils.isNotEmpty(datum.rcjId)||(ObjectUtils.isNotEmpty(datum.supplementRcjFlag) && datum.supplementRcjFlag)) {
                datum.quantity = datum.totalNumber;//人材机的totalNumber 表示工程量
                if (isGeneral) {  //一般
                    if (pricingMethod) { //市场价组价
                        datum.price = datum.marketPrice;
                        datum.totalNumber = datum.total;
                    }else {
                        datum.price = datum.baseJournalPrice;
                        datum.totalNumber = datum.baseJournalTotal;
                    }
                }else {
                    if (pricingMethod) {
                        datum.price = datum.marketTaxPrice;
                        datum.totalNumber = datum.totalTax;
                    }else {
                        datum.price = datum.baseJournalTaxPrice;
                        datum.totalNumber = datum.baseJournalTotalTax;
                    }
                }
                datum.deCode = datum.materialCode;
                datum.deName = datum.materialName;
            }
        }
        return data;
    }

    async getRcjSummaryCuoShi(param) {
        let resultQuery = await this.service.gongLiaoJiProject.gljCommonService.getProjectSetting(param);
        let pricingMethod = resultQuery.get("PRICING_METHOD");//true为市场价组价

        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let isGeneral = true;
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            isGeneral = false;
        }
        // let allYsshuData = _.cloneDeep(ProjectDomain.getDomain(param.constructId).csxmDomain.getDeTree(item => item.unitId === param.unitId));
        let allYsshuData = _.cloneDeep((await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch
        (param.constructId, param.singleId, param.unitId, null,1, 300000,true,null,[])).data);

        let deLists = allYsshuData.filter(item => item.type == BranchProjectLevelConstant.de || item.type == DeTypeConstants.DE_TYPE_RESOURCE || item.type == DeTypeConstants.DE_TYPE_USER_DE
            || item.type == DeTypeConstants.DE_TYPE_USER_RESOURCE || item.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE || item.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE);
        allYsshuData = allYsshuData.filter(item => item.type !="05");
        for (let i = 0; i < deLists.length; i++) {
            //如果是补充定额人材机 或 定额人材机
            if (deLists[i].type==DeTypeConstants.DE_TYPE_USER_RESOURCE||deLists[i].type==DeTypeConstants.DE_TYPE_RESOURCE) {
                continue;
            }
            let rcjLists = _.cloneDeep(await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(param.constructId, param.unitId, deLists[i].sequenceNbr,deLists[i].type));
            if (ObjectUtils.isNotEmpty(rcjLists)) {
                for (let j = 0; j < rcjLists.length; j++) {
                    rcjLists[j].unit = await this.getDisplayUnit(rcjLists[j]);
                }
                allYsshuData.push(...rcjLists);
            }
        }
        let arrayTree = xeUtils.toArrayTree(allYsshuData, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        await this.resetPrice(arrayTree[0],pricingMethod);
        let data = xeUtils.toTreeArray(arrayTree);
        await this.setTreeDispNoShiTi(data);
        await this.addConversionStr(param,data);
        data = data.filter(item => item.isTempRemove!=CommonConstants.COMMON_YES);//过滤掉临时删除项
        for (let i = 0; i < data.length; i++) {
            let datum = data[i];
            if (datum.type == BranchProjectLevelConstant.de || datum.type == DeTypeConstants.DE_TYPE_RESOURCE || datum.type == DeTypeConstants.DE_TYPE_USER_DE
                || datum.type == DeTypeConstants.DE_TYPE_USER_RESOURCE || datum.type == DeTypeConstants.DE_TYPE_ANZHUANG_FEE || datum.type == DeTypeConstants.DE_TYPE_ZHUANSHI_FEE) {   //如果是定额类型 跳过
                continue;
            }
            if (ObjectUtils.isNotEmpty(datum.rcjId)||(ObjectUtils.isNotEmpty(datum.supplementRcjFlag) && datum.supplementRcjFlag)) {
                datum.quantity = datum.totalNumber;//人材机的totalNumber 表示工程量
                if (isGeneral) {  //一般
                    if (pricingMethod) { //市场价组价
                        datum.price = datum.marketPrice;
                        datum.totalNumber = datum.total;
                    }else {
                        datum.price = datum.baseJournalPrice;
                        datum.totalNumber = datum.baseJournalTotal;
                    }
                }else {
                    if (pricingMethod) {
                        datum.price = datum.marketTaxPrice;
                        datum.totalNumber = datum.totalTax;
                    }else {
                        datum.price = datum.baseJournalTaxPrice;
                        datum.totalNumber = datum.baseJournalTotalTax;
                    }
                }
                datum.deCode = datum.materialCode;
                datum.deName = datum.materialName;
            }else {  //对于非人材机项的定额、措施清单等
                if (pricingMethod) {
                    datum.price = datum.price;
                    datum.totalNumber = datum.totalNumber;
                }else {
                    datum.price = datum.baseJournalPrice;
                    datum.totalNumber = datum.baseJournalTotalNumber;
                }
            }
        }
        return data;
    }

    async getconstructUnitSheet8List(param) {
        // 一般就是不含税合价  简易就是含税合价
        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let isGeneral = true;
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            isGeneral = false;
        }
        param.levelType = ProjectTypeConstants.PROJECT_TYPE_UNIT;
        param.kind = 1;
        let returnList = [];
        returnList.push({
            dispNo:"一",
            materialName:"人工",
        });
        let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let rgXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(result)) {
            for (let i = 0; i < result.length; i++) {
                returnList.push({
                    dispNo:i+1,
                    materialName:result[i].materialName,
                    unit:await this.getDisplayUnit(result[i]),
                    totalNumber:result[i].totalNumber,
                    baseJournalPrice:result[i].isDataTaxRate==0?"-":(isGeneral?result[i].baseJournalPrice:result[i].baseJournalTaxPrice),
                    marketPrice:result[i].isDataTaxRate==0?"-":(isGeneral?result[i].marketPrice:result[i].marketTaxPrice),
                    totalMarket:result[i].isDataTaxRate==0?"-":(isGeneral?result[i].total:result[i].totalTax),
                })
                rgXiaoJi += Number(isGeneral?result[i].total:result[i].totalTax);
            }
        }
        returnList.push({
            materialName:"小计",
            totalMarket:NumberUtil.numberScale2(rgXiaoJi),
        });
        //--------------------------------------
        returnList.push({
            dispNo:"二",
            materialName:"材料",
        });
        param.kind = 2;
        let clResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        //只要材料  不要主材
        clResult = ObjectUtils.isNotEmpty(clResult)?clResult.filter(item => item.kind!=5):[];
        let clXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(clResult)) {
            for (let i = 0; i < clResult.length; i++) {
                returnList.push({
                    dispNo:i+1,
                    materialName:clResult[i].materialName,
                    unit:await this.getDisplayUnit(clResult[i]),
                    totalNumber:clResult[i].totalNumber,
                    baseJournalPrice:clResult[i].isDataTaxRate==0?"-":(isGeneral?clResult[i].baseJournalPrice:clResult[i].baseJournalTaxPrice),
                    marketPrice:clResult[i].isDataTaxRate==0?"-":(isGeneral?clResult[i].marketPrice:clResult[i].marketTaxPrice),
                    totalMarket:clResult[i].isDataTaxRate==0?"-":(isGeneral?clResult[i].total:clResult[i].totalTax),
                })
                clXiaoJi+= Number(isGeneral?clResult[i].total:clResult[i].totalTax);
            }
        }
        returnList.push({
            materialName:"小计",
            totalMarket:NumberUtil.numberScale2(clXiaoJi),
        });
        //--------------------------------------
        returnList.push({
            dispNo:"三",
            materialName:"机械",
        });
        param.kind = 3;
        let jxResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let jxXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(jxResult)) {
            for (let i = 0; i < jxResult.length; i++) {
                returnList.push({
                    dispNo:i+1,
                    materialName:jxResult[i].materialName,
                    unit:await this.getDisplayUnit(jxResult[i]),
                    totalNumber:jxResult[i].totalNumber,
                    baseJournalPrice:jxResult[i].isDataTaxRate==0?"-":(isGeneral?jxResult[i].baseJournalPrice:jxResult[i].baseJournalTaxPrice),
                    marketPrice:jxResult[i].isDataTaxRate==0?"-":(isGeneral?jxResult[i].marketPrice:jxResult[i].marketTaxPrice),
                    totalMarket:jxResult[i].isDataTaxRate==0?"-":(isGeneral?jxResult[i].total:jxResult[i].totalTax),
                })
                jxXiaoJi+= Number(isGeneral?jxResult[i].total:jxResult[i].totalTax);
            }
        }
        returnList.push({
            materialName:"小计",
            totalMarket:NumberUtil.numberScale2(jxXiaoJi),
        });
        //--------------------------------------
        returnList.push({
            dispNo:"四",
            materialName:"主材",
        });
        param.kind = 5;
        let zcResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let zcXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(zcResult)) {
            for (let i = 0; i < zcResult.length; i++) {
                returnList.push({
                    dispNo:i+1,
                    materialName:zcResult[i].materialName,
                    unit:await this.getDisplayUnit(zcResult[i]),
                    totalNumber:zcResult[i].totalNumber,
                    baseJournalPrice:zcResult[i].isDataTaxRate==0?"-":(isGeneral?zcResult[i].baseJournalPrice:zcResult[i].baseJournalTaxPrice),
                    marketPrice:zcResult[i].isDataTaxRate==0?"-":(isGeneral?zcResult[i].marketPrice:zcResult[i].marketTaxPrice),
                    totalMarket:zcResult[i].isDataTaxRate==0?"-":(isGeneral?zcResult[i].total:zcResult[i].totalTax),
                })
                zcXiaoJi+= Number(isGeneral?zcResult[i].total:zcResult[i].totalTax);
            }
        }
        returnList.push({
            materialName:"小计",
            totalMarket:NumberUtil.numberScale2(zcXiaoJi),
        });
        //--------------------------------------
        returnList.push({
            dispNo:"五",
            materialName:"设备",
        });
        param.kind = 4;
        let sbResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let sbXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(sbResult)) {
            for (let i = 0; i < sbResult.length; i++) {
                returnList.push({
                    dispNo:i+1,
                    materialName:sbResult[i].materialName,
                    unit:await this.getDisplayUnit(sbResult[i]),
                    totalNumber:sbResult[i].totalNumber,
                    baseJournalPrice:sbResult[i].isDataTaxRate==0?"-":(isGeneral?sbResult[i].baseJournalPrice:sbResult[i].baseJournalTaxPrice),
                    marketPrice:sbResult[i].isDataTaxRate==0?"-":(isGeneral?sbResult[i].marketPrice:sbResult[i].marketTaxPrice),
                    totalMarket:sbResult[i].isDataTaxRate==0?"-":(isGeneral?sbResult[i].total:sbResult[i].totalTax),
                })
                sbXiaoJi+= Number(isGeneral?sbResult[i].total:sbResult[i].totalTax);
            }
        }
        returnList.push({
            materialName:"小计",
            totalMarket:NumberUtil.numberScale2(sbXiaoJi),
        });
        return returnList;
    }


    async getconstructUnitSheet9List(param) {
        let taxMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let isGeneral = true;
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            isGeneral = false;
        }
        param.levelType = ProjectTypeConstants.PROJECT_TYPE_UNIT;
        param.kind = 1;
        let returnList = [];
        let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let rgXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(result)) {
            result = result.filter(item => ObjectUtils.isNotEmpty(isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)&& ObjectUtils.isNotEmpty(isGeneral?item.marketPrice:item.marketTaxPrice) && ((isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)!=(isGeneral?item.marketPrice:item.marketTaxPrice)))
            if (ObjectUtils.isNotEmpty(result)) {
                returnList.push({
                    dispNo:"一",
                    materialName:"人工",
                });
                for (let i = 0; i < result.length; i++) {
                    let marketPrice= NumberUtil.numberScale((isGeneral?result[i].marketPrice:result[i].marketTaxPrice),param.precision.RCJ_COLLECT.marketPrice);
                    let baseJournalPrice = NumberUtil.numberScale((isGeneral?result[i].baseJournalPrice:result[i].baseJournalTaxPrice),param.precision.RCJ_COLLECT.baseJournalPrice);
                    let totalNumber =  NumberUtil.numberScale(result[i].totalNumber,param.precision.RCJ_COLLECT.totalNumber);
                    let priceDiffer = marketPrice- baseJournalPrice;
                    let priceDifferTotal =  NumberUtil.multiply((marketPrice-baseJournalPrice),totalNumber);

                    returnList.push({
                        dispNo:i+1,
                        materialName:ObjectUtils.isNotEmpty(result[i].specification)?result[i].materialName+"  "+result[i].specification:result[i].materialName,
                        unit:await this.getDisplayUnit(result[i]),
                        totalNumber:result[i].totalNumber,
                        baseJournalPrice:isGeneral?result[i].baseJournalPrice:result[i].baseJournalTaxPrice,
                        marketPrice:isGeneral?result[i].marketPrice:result[i].marketTaxPrice,
                        priceDiffer: priceDiffer,
                        priceDifferTotal:priceDifferTotal,
                    })
                    rgXiaoJi += Number(priceDifferTotal);
                }
                rgXiaoJi = NumberUtil.numberScale2(rgXiaoJi);
                returnList.push({
                    materialName:"小计",
                    priceDifferTotal:rgXiaoJi,
                });
            }
        }
        //--------------------------------------
        param.kind = 2;
        let clResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let clXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(clResult)) {
            clResult = clResult.filter(item => ObjectUtils.isNotEmpty(isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)&& ObjectUtils.isNotEmpty(isGeneral?item.marketPrice:item.marketTaxPrice) && ((isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)!=(isGeneral?item.marketPrice:item.marketTaxPrice)))
            if (ObjectUtils.isNotEmpty(clResult)) {
                returnList.push({
                    dispNo:"二",
                    materialName:"材料",
                });
                for (let i = 0; i < clResult.length; i++) {
                    let marketPrice= NumberUtil.numberScale((isGeneral?clResult[i].marketPrice:clResult[i].marketTaxPrice),param.precision.RCJ_COLLECT.marketPrice);
                    let baseJournalPrice = NumberUtil.numberScale((isGeneral?clResult[i].baseJournalPrice:clResult[i].baseJournalTaxPrice),param.precision.RCJ_COLLECT.baseJournalPrice);
                    let  totalNumber =  NumberUtil.numberScale(clResult[i].totalNumber,param.precision.RCJ_COLLECT.totalNumber);
                    let   priceDiffer = marketPrice- baseJournalPrice;
                    let priceDifferTotal =  NumberUtil.multiply((marketPrice-baseJournalPrice),totalNumber);
                    returnList.push({
                        dispNo:i+1,
                        materialName:ObjectUtils.isNotEmpty(clResult[i].specification)?clResult[i].materialName+"  "+clResult[i].specification:clResult[i].materialName,
                        unit:await this.getDisplayUnit(clResult[i]),
                        totalNumber:clResult[i].totalNumber,
                        baseJournalPrice:isGeneral?clResult[i].baseJournalPrice:clResult[i].baseJournalTaxPrice,
                        marketPrice:isGeneral?clResult[i].marketPrice:clResult[i].marketTaxPrice,
                        priceDiffer:priceDiffer ,
                        priceDifferTotal:priceDifferTotal,
                    });
                    clXiaoJi+= Number(priceDifferTotal);
                }
                clXiaoJi = NumberUtil.numberScale2(clXiaoJi);
                returnList.push({
                    materialName:"小计",
                    priceDifferTotal:clXiaoJi,
                });
            }
        }

        //--------------------------------------
        param.kind = 3;
        let jxResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let jxXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(jxResult)) {
            jxResult = jxResult.filter(item => ObjectUtils.isNotEmpty(isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)&& ObjectUtils.isNotEmpty(isGeneral?item.marketPrice:item.marketTaxPrice) && ((isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)!=(isGeneral?item.marketPrice:item.marketTaxPrice)))
            if (ObjectUtils.isNotEmpty(jxResult)) {
                returnList.push({
                    dispNo:"三",
                    materialName:"机械",
                });
                for (let i = 0; i < jxResult.length; i++) {
                    let marketPrice= NumberUtil.numberScale((isGeneral?jxResult[i].marketPrice:jxResult[i].marketTaxPrice),param.precision.RCJ_COLLECT.marketPrice);
                    let baseJournalPrice = NumberUtil.numberScale((isGeneral?jxResult[i].baseJournalPrice:jxResult[i].baseJournalTaxPrice),param.precision.RCJ_COLLECT.baseJournalPrice);
                    let totalNumber =  NumberUtil.numberScale(jxResult[i].totalNumber,param.precision.RCJ_COLLECT.totalNumber);
                    let priceDiffer = marketPrice- baseJournalPrice;
                    let priceDifferTotal =  NumberUtil.multiply((marketPrice-baseJournalPrice),totalNumber);
                    returnList.push({
                        dispNo:i+1,
                        materialName:ObjectUtils.isNotEmpty(jxResult[i].specification)?jxResult[i].materialName+"  "+jxResult[i].specification:jxResult[i].materialName,
                        unit:await this.getDisplayUnit(jxResult[i]),
                        totalNumber:jxResult[i].totalNumber,
                        baseJournalPrice:isGeneral?jxResult[i].baseJournalPrice:jxResult[i].baseJournalTaxPrice,
                        marketPrice:isGeneral?jxResult[i].marketPrice:jxResult[i].marketTaxPrice,
                        priceDiffer: priceDiffer,
                        priceDifferTotal:priceDifferTotal,
                    });
                    jxXiaoJi+= Number(priceDifferTotal);
                }
                jxXiaoJi = NumberUtil.numberScale2(jxXiaoJi);
                returnList.push({
                    materialName:"小计",
                    priceDifferTotal:jxXiaoJi,
                });
            }
        }
        //--------------------------------------
        param.kind = 4;
        let sbResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let sbXiaoJi = 0;
        if (ObjectUtils.isNotEmpty(sbResult)) {
            sbResult = sbResult.filter(item => ObjectUtils.isNotEmpty(isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)&& ObjectUtils.isNotEmpty(isGeneral?item.marketPrice:item.marketTaxPrice) && ((isGeneral?item.baseJournalPrice:item.baseJournalTaxPrice)!=(isGeneral?item.marketPrice:item.marketTaxPrice)))
            if (ObjectUtils.isNotEmpty(sbResult)) {
                returnList.push({
                    dispNo:"四",
                    materialName:"设备",
                });
                for (let i = 0; i < sbResult.length; i++) {
                    let marketPrice= NumberUtil.numberScale((isGeneral?sbResult[i].marketPrice:sbResult[i].marketTaxPrice),param.precision.RCJ_COLLECT.marketPrice);
                    let baseJournalPrice = NumberUtil.numberScale((isGeneral?sbResult[i].baseJournalPrice:sbResult[i].baseJournalTaxPrice),param.precision.RCJ_COLLECT.baseJournalPrice);
                    let totalNumber =  NumberUtil.numberScale(sbResult[i].totalNumber,param.precision.RCJ_COLLECT.totalNumber);
                    let priceDiffer = marketPrice- baseJournalPrice;
                    let priceDifferTotal =  NumberUtil.multiply((marketPrice-baseJournalPrice),totalNumber);
                    returnList.push({
                        dispNo:i+1,
                        materialName:ObjectUtils.isNotEmpty(sbResult[i].specification)?sbResult[i].materialName+"  "+sbResult[i].specification:sbResult[i].materialName,
                        unit:await this.getDisplayUnit(sbResult[i]),
                        totalNumber:sbResult[i].totalNumber,
                        baseJournalPrice:isGeneral?sbResult[i].baseJournalPrice:sbResult[i].baseJournalTaxPrice,
                        marketPrice:isGeneral?sbResult[i].marketPrice:sbResult[i].marketTaxPrice,
                        priceDiffer:priceDiffer ,
                        priceDifferTotal:priceDifferTotal,
                    });
                    sbXiaoJi+= Number(priceDifferTotal);
                }
                sbXiaoJi = NumberUtil.numberScale2(sbXiaoJi);
                returnList.push({
                    materialName:"小计",
                    priceDifferTotal:sbXiaoJi,
                });
            }
        }
        return {"total":NumberUtil.numberScale2(rgXiaoJi+clXiaoJi+jxXiaoJi+sbXiaoJi),"data":returnList};
    }


    async getconstructUnitSheetYongLiang(param) {
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        // 一般就是不含税合价  简易就是含税合价
        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let isGeneral = true;
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            isGeneral = false;
        }
        param.levelType = ProjectTypeConstants.PROJECT_TYPE_UNIT;
        param.kind = 1;
        let returnList = [];
        let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let rgXiaoJi = 0;
        let rgPriceDifferTotal = 0;
        if (ObjectUtils.isNotEmpty(result)) {
            returnList.push({
                materialName:"人工",
            });
            for (let i = 0; i < result.length; i++) {
                let priceDifferTotal = ((isGeneral?NumberUtil.numberScale(result[i].marketPrice,precision.RCJ_COLLECT.marketPrice):NumberUtil.numberScale(result[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice))
                    -(isGeneral?NumberUtil.numberScale(result[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):NumberUtil.numberScale(result[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice)))*NumberUtil.numberScale(result[i].totalNumber,precision.RCJ_COLLECT.totalNumber);
                returnList.push({
                    materialCode:result[i].materialCode,
                    materialName:ObjectUtils.isNotEmpty(result[i].specification)?result[i].materialName+"  "+result[i].specification:result[i].materialName,
                    unit:await this.getDisplayUnit(result[i]),
                    totalNumber:NumberUtil.numberScale(result[i].totalNumber,precision.RCJ_COLLECT.totalNumber),
                    baseJournalPrice:isGeneral?GljExcelUtil._roundAndPadCeping(result[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):GljExcelUtil._roundAndPadCeping(result[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice),
                    marketPrice:isGeneral?GljExcelUtil._roundAndPadCeping(result[i].marketPrice,precision.RCJ_COLLECT.marketPrice):GljExcelUtil._roundAndPadCeping(result[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice),
                    totalMarket:isGeneral?GljExcelUtil._roundAndPadCeping(result[i].total,precision.RCJ_COLLECT.total):GljExcelUtil._roundAndPadCeping(result[i].totalTax,precision.RCJ_COLLECT.totalTax),
                    priceDifferTotal:GljExcelUtil._roundAndPadCeping(priceDifferTotal,precision.RCJ_COLLECT.jchj),
                })
                rgXiaoJi += Number(isGeneral?result[i].total:result[i].totalTax);
                rgPriceDifferTotal+= priceDifferTotal;
            }
            rgXiaoJi = NumberUtil.numberScale2(rgXiaoJi);
            returnList.push({
                materialName:"小计",
                totalMarket:rgXiaoJi,
                priceDifferTotal:GljExcelUtil._roundAndPadCeping(rgPriceDifferTotal,2),
            });
        }
        //--------------------------------------
        param.kind = 2;
        let clResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        //只要材料  不要主材
        clResult = ObjectUtils.isNotEmpty(clResult)?clResult.filter(item => item.kind!=5):[];
        let clXiaoJi = 0;
        let clPriceDifferTotal = 0;
        if (ObjectUtils.isNotEmpty(clResult)) {
            returnList.push({
                materialName:"材料",
            });
            for (let i = 0; i < clResult.length; i++) {
                let priceDifferTotal = ((isGeneral?NumberUtil.numberScale(clResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):NumberUtil.numberScale(clResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice))
                    -(isGeneral?NumberUtil.numberScale(clResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):NumberUtil.numberScale(clResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice)))*NumberUtil.numberScale(clResult[i].totalNumber,precision.RCJ_COLLECT.totalNumber);
                returnList.push({
                    materialCode:clResult[i].materialCode,
                    materialName:ObjectUtils.isNotEmpty(clResult[i].specification)?clResult[i].materialName+"  "+clResult[i].specification:clResult[i].materialName,
                    unit:await this.getDisplayUnit(clResult[i]),
                    totalNumber:clResult[i].totalNumber,
                    baseJournalPrice:isGeneral?GljExcelUtil._roundAndPadCeping(clResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):GljExcelUtil._roundAndPadCeping(clResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice),
                    marketPrice:isGeneral?GljExcelUtil._roundAndPadCeping(clResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):GljExcelUtil._roundAndPadCeping(clResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice),
                    totalMarket:isGeneral?GljExcelUtil._roundAndPadCeping(clResult[i].total,precision.RCJ_COLLECT.total):GljExcelUtil._roundAndPadCeping(clResult[i].totalTax,precision.RCJ_COLLECT.totalTax),
                    priceDifferTotal:GljExcelUtil._roundAndPadCeping(priceDifferTotal,precision.RCJ_COLLECT.jchj),
                })
                clXiaoJi+= Number(isGeneral?clResult[i].total:clResult[i].totalTax);
                clPriceDifferTotal+= priceDifferTotal;
            }
            clXiaoJi = NumberUtil.numberScale2(clXiaoJi);
            returnList.push({
                materialName:"小计",
                totalMarket:clXiaoJi,
                priceDifferTotal:GljExcelUtil._roundAndPadCeping(clPriceDifferTotal,2),
            });
        }
        //--------------------------------------
        param.kind = 3;
        let jxResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let jxXiaoJi = 0;
        let jxPriceDifferTotal = 0;
        if (ObjectUtils.isNotEmpty(jxResult)) {
            returnList.push({
                materialName:"机械",
            });
            for (let i = 0; i < jxResult.length; i++) {
                let priceDifferTotal = ((isGeneral?NumberUtil.numberScale(jxResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):NumberUtil.numberScale(jxResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice))
                    -(isGeneral?NumberUtil.numberScale(jxResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):NumberUtil.numberScale(jxResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice)))*NumberUtil.numberScale(jxResult[i].totalNumber,precision.RCJ_COLLECT.totalNumber);
                returnList.push({
                    materialCode:jxResult[i].materialCode,
                    materialName:ObjectUtils.isNotEmpty(jxResult[i].specification)?jxResult[i].materialName+"  "+jxResult[i].specification:jxResult[i].materialName,
                    unit:await this.getDisplayUnit(jxResult[i]),
                    totalNumber:jxResult[i].totalNumber,
                    baseJournalPrice:isGeneral?GljExcelUtil._roundAndPadCeping(jxResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):GljExcelUtil._roundAndPadCeping(jxResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice),
                    marketPrice:isGeneral?GljExcelUtil._roundAndPadCeping(jxResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):GljExcelUtil._roundAndPadCeping(jxResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice),
                    totalMarket:isGeneral?GljExcelUtil._roundAndPadCeping(jxResult[i].total,precision.RCJ_COLLECT.total):GljExcelUtil._roundAndPadCeping(jxResult[i].totalTax,precision.RCJ_COLLECT.totalTax),
                    priceDifferTotal:GljExcelUtil._roundAndPadCeping(priceDifferTotal,precision.RCJ_COLLECT.jchj),
                })
                jxXiaoJi+= Number(isGeneral?jxResult[i].total:jxResult[i].totalTax);
                jxPriceDifferTotal+= priceDifferTotal;
            }
            jxXiaoJi = NumberUtil.numberScale2(jxXiaoJi);
            returnList.push({
                materialName:"小计",
                totalMarket:jxXiaoJi,
                priceDifferTotal:GljExcelUtil._roundAndPadCeping(jxPriceDifferTotal,2),
            });
        }

        //--------------------------------------
        param.kind = 5;
        let zcResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let zcXiaoJi = 0;
        let zcPriceDifferTotal = 0;
        if (ObjectUtils.isNotEmpty(zcResult)) {
            returnList.push({
                materialName:"主材",
            });
            for (let i = 0; i < zcResult.length; i++) {
                let priceDifferTotal = ((isGeneral?NumberUtil.numberScale(zcResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):NumberUtil.numberScale(zcResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice))
                    -(isGeneral?NumberUtil.numberScale(zcResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):NumberUtil.numberScale(zcResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice)))*NumberUtil.numberScale(zcResult[i].totalNumber,precision.RCJ_COLLECT.totalNumber);
                returnList.push({
                    materialCode:zcResult[i].materialCode,
                    materialName:ObjectUtils.isNotEmpty(zcResult[i].specification)?zcResult[i].materialName+"  "+zcResult[i].specification:zcResult[i].materialName,
                    unit:await this.getDisplayUnit(zcResult[i]),
                    totalNumber:zcResult[i].totalNumber,
                    baseJournalPrice:isGeneral?GljExcelUtil._roundAndPadCeping(zcResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):GljExcelUtil._roundAndPadCeping(zcResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice),
                    marketPrice:isGeneral?GljExcelUtil._roundAndPadCeping(zcResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):GljExcelUtil._roundAndPadCeping(zcResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice),
                    totalMarket:isGeneral?GljExcelUtil._roundAndPadCeping(zcResult[i].total,precision.RCJ_COLLECT.total):GljExcelUtil._roundAndPadCeping(zcResult[i].totalTax,precision.RCJ_COLLECT.totalTax),
                    priceDifferTotal:GljExcelUtil._roundAndPadCeping(priceDifferTotal,precision.RCJ_COLLECT.jchj),
                })
                zcXiaoJi+= Number(isGeneral?zcResult[i].total:zcResult[i].totalTax);
                zcPriceDifferTotal+= priceDifferTotal;
            }
            zcXiaoJi = NumberUtil.numberScale2(zcXiaoJi);
            returnList.push({
                materialName:"小计",
                totalMarket:zcXiaoJi,
                priceDifferTotal:GljExcelUtil._roundAndPadCeping(zcPriceDifferTotal,2),
            });
        }
        //--------------------------------------
        param.kind = 4;
        let sbResult =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        let sbXiaoJi = 0;
        let sbPriceDifferTotal = 0;
        if (ObjectUtils.isNotEmpty(sbResult)) {
            returnList.push({
                materialName:"设备",
            });
            for (let i = 0; i < sbResult.length; i++) {
                let priceDifferTotal = ((isGeneral?NumberUtil.numberScale(sbResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):NumberUtil.numberScale(sbResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice))
                    -(isGeneral?NumberUtil.numberScale(sbResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):NumberUtil.numberScale(sbResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice)))*NumberUtil.numberScale(sbResult[i].totalNumber,precision.RCJ_COLLECT.totalNumber);
                returnList.push({
                    materialCode:sbResult[i].materialCode,
                    materialName:ObjectUtils.isNotEmpty(sbResult[i].specification)?sbResult[i].materialName+"  "+sbResult[i].specification:sbResult[i].materialName,
                    unit:await this.getDisplayUnit(sbResult[i]),
                    totalNumber:sbResult[i].totalNumber,
                    baseJournalPrice:isGeneral?GljExcelUtil._roundAndPadCeping(sbResult[i].baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):GljExcelUtil._roundAndPadCeping(sbResult[i].baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice),
                    marketPrice:isGeneral?GljExcelUtil._roundAndPadCeping(sbResult[i].marketPrice,precision.RCJ_COLLECT.marketPrice):GljExcelUtil._roundAndPadCeping(sbResult[i].marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice),
                    totalMarket:isGeneral?GljExcelUtil._roundAndPadCeping(sbResult[i].total,precision.RCJ_COLLECT.total):GljExcelUtil._roundAndPadCeping(sbResult[i].totalTax,precision.RCJ_COLLECT.totalTax),
                    priceDifferTotal:GljExcelUtil._roundAndPadCeping(priceDifferTotal,precision.RCJ_COLLECT.jchj),
                })
                sbXiaoJi+= Number(isGeneral?sbResult[i].total:sbResult[i].totalTax);
                sbPriceDifferTotal+= priceDifferTotal;
            }
            sbXiaoJi = NumberUtil.numberScale2(sbXiaoJi);
            returnList.push({
                materialName:"小计",
                totalMarket:sbXiaoJi,
                priceDifferTotal:GljExcelUtil._roundAndPadCeping(sbPriceDifferTotal,2),
            });
        }
        return returnList;
    }

    async getDisplayUnit(item) {
        item = _.cloneDeep(item);
        if (item.materialCode.includes('QTCLF1')) {
            item.unit = '元'
        }

        if (item.isFyrcj == 0) {
            item.unit = '元'
        }
        return item.unit;
    }


    async getconstructUnitSheet11List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 5;
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        let resultList = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            Sheet4List = ConvertUtil.deepCopy(resultList);
        }

        return Sheet4List;
    }


    async getconstructUnitSheet12List(param) {
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        // 一般就是不含税合价  简易就是含税合价
        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let isGeneral = true;
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            isGeneral = false;
        }

        let returnList = [];
        param.levelType = projectLevelConstant.unit;
        let total = 0;
        let resultList = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        if (ObjectUtils.isNotEmpty(resultList)) {
            for (let i = 0; i < resultList.length; i++) {
                let element = resultList[i];
                total += isGeneral?NumberUtil.numberScale(element.total,precision.RCJ_COLLECT.total):NumberUtil.numberScale(element.totalTax,precision.RCJ_COLLECT.totalTax);
                returnList.push({
                    dispNo:i+1,
                    materialName:ObjectUtils.isNotEmpty(element.specification)?element.materialName+"  "+element.specification:element.materialName,
                    unit:element.unit,
                    totalNumber:NumberUtil.numberScale(element.totalNumber,precision.RCJ_COLLECT.totalNumber),
                    marketPrice:isGeneral?NumberUtil.numberScale(element.marketPrice,precision.RCJ_COLLECT.marketPrice):NumberUtil.numberScale(element.marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice),
                    marketPriceTotal:isGeneral?NumberUtil.numberScale(element.total,precision.RCJ_COLLECT.total):NumberUtil.numberScale(element.totalTax,precision.RCJ_COLLECT.totalTax),
                });
            }
            if (resultList.length != 0) {
                returnList.push({
                    materialName:"合计",
                    marketPriceTotal:total,
                });
            }
        }
        return returnList;
    }


    //获取工程量明细
    async getQuantityDetails(args) {
        let {constructId, unitId, deId} = args;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let deMap = unitQuantiesMap?.get(deId);
        if (ObjectUtils.isEmpty(deMap)) {
            let deLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
            if (ObjectUtils.isNotEmpty(deLine) && deLine.type !== DeTypeConstants.DE_TYPE_DEFAULT
                && deLine.type !== DeTypeConstants.DE_TYPE_EMPTY
                && deLine.type !== DeTypeConstants.DE_TYPE_FB
                && deLine.type !== DeTypeConstants.DE_TYPE_ZFB
            ) {
                await this.service.gongLiaoJiProject.gljInitDeService.initDeQuantities(deLine);
            }
            deMap = unitQuantiesMap?.get(deId);
        }
        if (ObjectUtils.isNotEmpty(deMap)){
            return deMap.quantities;
        }
        return [];
    }

    async getSingleZjSheetData(args) {
        let returnList = [];
        //获取造价分析
        args.type = projectLevelConstant.construct;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        if (ObjectUtils.isNotEmpty(costAnalysiss) && ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisConstructVOList)) {
            await this.getSingleUnitCostData(returnList,costAnalysiss.costAnalysisConstructVOList[0],null);
        }

        if (ObjectUtils.isNotEmpty(returnList)) {
            let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId);
            let precision = precision1.COST_SUMMARY;
            let precisionCost = precision1.COST_ANALYSIS;
            let returnListPrecision = _.cloneDeep(returnList);
            for (let item of returnListPrecision) {
                item.projectCost = NumberUtil.numberScale(item.projectCost,precision.je);
                item.unitcost = NumberUtil.numberScale(item.unitcost,precision.je);
                item.average = NumberUtil.numberScale(item.average,precisionCost.jzgm);
                item.awf = NumberUtil.numberScale(item.awf,precision.je);
                item.aqwmsgf = NumberUtil.numberScale(item.aqwmsgf,precision.je);
                item.glfLr = NumberUtil.numberScale(item.glfLr,precision.je);
                item.noPriceClf = NumberUtil.numberScale(item.noPriceClf,precision.je);
                item.rgf = NumberUtil.numberScale(item.rgf,precision.je);
                item.jxf = NumberUtil.numberScale(item.jxf,precision.je);
                item.sbf = NumberUtil.numberScale(item.sbf,precision.je);
                item.sj = NumberUtil.numberScale(item.sj,precision.je);
            }
            return returnListPrecision;
        } else {
            return returnList;
        }
    }

    async getSingleZjSheetDetail(args) {
        let returnList = [];
        //获取造价分析
        args.type = projectLevelConstant.construct;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        if (ObjectUtils.isNotEmpty(costAnalysiss) && ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisConstructVOList)) {
            await this.getSingleUnitCostData(returnList,costAnalysiss.costAnalysisConstructVOList[0],null);
        }

        if (ObjectUtils.isNotEmpty(returnList)) {
            let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId);
            let precision = precision1.COST_SUMMARY;
            let precisionCost = precision1.COST_ANALYSIS;
            let returnListPrecision = _.cloneDeep(returnList);
            for (let item of returnListPrecision) {
                item.projectCost = NumberUtil.numberScale(item.projectCost,precision.je);
                item.unitcost = NumberUtil.numberScale(item.unitcost,precision.je);
                item.average = NumberUtil.numberScale(item.average,precisionCost.jzgm);
                item.awf = NumberUtil.numberScale(item.awf,precision.je);
                item.aqwmsgf = NumberUtil.numberScale(item.aqwmsgf,precision.je);
                item.glfLr = NumberUtil.numberScale(item.glfLr,precision.je);
                item.noPriceClf = NumberUtil.numberScale(item.noPriceClf,precision.je);
                item.rgf = NumberUtil.numberScale(item.rgf,precision.je);
                item.jxf = NumberUtil.numberScale(item.jxf,precision.je);
                item.sbf = NumberUtil.numberScale(item.sbf,precision.je);
                item.sj = NumberUtil.numberScale(item.sj,precision.je);
            }
            return returnListPrecision;
        } else {
            return returnList;
        }
    }

    async getProjectZjSheetData(args) {
        let returnList = [];
        //获取造价分析
        args.type = projectLevelConstant.construct;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        if (ObjectUtils.isNotEmpty(costAnalysiss) && ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisConstructVOList)) {
            await this.getSingleCostData(returnList,costAnalysiss.costAnalysisConstructVOList[0],null);
        }

        if (ObjectUtils.isNotEmpty(returnList)) {
            let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId);
            let precision = precision1.COST_SUMMARY;
            let returnListPrecision = _.cloneDeep(returnList);
            for (let item of returnListPrecision) {
                item.projectCost = NumberUtil.numberScale(item.projectCost,precision.je);
                item.unitcost = NumberUtil.numberScale(item.unitcost,precision.je);
                item.average = NumberUtil.numberScale(item.average,precision1.COST_ANALYSIS.jzgm);
                item.costProportion = NumberUtil.numberScale(item.costProportion,precision1.COST_ANALYSIS.je);
            }
            return returnListPrecision;
        } else {
            return returnList;
        }
    }

    async getSingleCostData(returnList,tree,dispNo) {
        if (ObjectUtils.isNotEmpty(tree.childrenList)) {
            for (let i = 0; i < tree.childrenList.length; i++) {
                let childrenListElement = tree.childrenList[i]
                if (childrenListElement.levelType == projectLevelConstant.unit) {
                    continue;
                }
                let dispNoChildren = ObjectUtils.isEmpty(dispNo)?(i+1):dispNo+"."+(i+1);
                returnList.push({
                    dispNo:dispNoChildren,
                    levelType:childrenListElement.levelType,
                    projectName:childrenListElement.projectName,
                    projectCost:childrenListElement.projectCost,
                    average:childrenListElement.average,//工程规模
                    unitcost:childrenListElement.unitcost,//造价指标
                    costProportion:childrenListElement.costProportion,//造价占比
                    rgf:NumberUtil.numberScale2(childrenListElement.ysrgf+childrenListElement.csrgf),
                    jxf:NumberUtil.numberScale2(childrenListElement.ysjxf+childrenListElement.csjxf),
                    noPriceClf:NumberUtil.numberScale2(childrenListElement.yszcf),
                    sbf:NumberUtil.numberScale2(childrenListElement.yssbf+childrenListElement.cssbf),
                    glfLr:NumberUtil.numberScale2(childrenListElement.qyglf+childrenListElement.lr),
                    awf:childrenListElement.aqwmsgf,
                    aqwmsgf:childrenListElement.aqwmsgf,
                    sj:childrenListElement.sj,
                });
                await this.getSingleCostData(returnList,childrenListElement,dispNoChildren);
            }
        }
    }

    async getSingleUnitCostData(returnList,tree,dispNo) {
        if (ObjectUtils.isNotEmpty(tree.childrenList)) {
            for (let i = 0; i < tree.childrenList.length; i++) {
                let childrenListElement = tree.childrenList[i]
                let name = "";
                if (childrenListElement.levelType == projectLevelConstant.unit) {
                    name = "   "+childrenListElement.projectName;
                }
                if (childrenListElement.levelType == projectLevelConstant.single) {
                    name = childrenListElement.projectName;
                }

                let dispNoChildren = ObjectUtils.isEmpty(dispNo)?(i+1):dispNo+"."+(i+1);
                returnList.push({
                    dispNo:dispNoChildren,
                    levelType:childrenListElement.levelType,
                    projectName:name,
                    projectCost:childrenListElement.projectCost,
                    average:childrenListElement.average,//工程规模
                    unitcost:childrenListElement.unitcost,//造价指标
                    costProportion:childrenListElement.costProportion,//造价占比
                    rgf:NumberUtil.numberScale2(childrenListElement.ysrgf+childrenListElement.csrgf),
                    jxf:NumberUtil.numberScale2(childrenListElement.ysjxf+childrenListElement.csjxf),
                    noPriceClf:NumberUtil.numberScale2(childrenListElement.yszcf),
                    sbf:NumberUtil.numberScale2(childrenListElement.yssbf+childrenListElement.cssbf),
                    glfLr:NumberUtil.numberScale2(childrenListElement.qyglf+childrenListElement.lr),
                    awf:childrenListElement.aqwmsgf,
                    aqwmsgf:childrenListElement.aqwmsgf,
                    sj:childrenListElement.sj,
                });
                await this.getSingleUnitCostData(returnList,childrenListElement,dispNoChildren);
            }
        }
    }

    async getProjectZjSheetDetail(args) {
        let returnList = [];
        //获取造价分析
        args.type = projectLevelConstant.construct;
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        if (ObjectUtils.isNotEmpty(costAnalysiss) && ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisConstructVOList)) {
            await this.getSingleCostData(returnList,costAnalysiss.costAnalysisConstructVOList[0],null);
        }

        if (ObjectUtils.isNotEmpty(returnList)) {
            let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId);
            let precision = precision1.COST_SUMMARY;
            let returnListPrecision = _.cloneDeep(returnList);
            for (let item of returnListPrecision) {
                item.projectCost = NumberUtil.numberScale(item.projectCost,precision.je);
                item.unitcost = NumberUtil.numberScale(item.unitcost,precision.je);
                item.awf = NumberUtil.numberScale(item.awf,precision.je);
                item.aqwmsgf = NumberUtil.numberScale(item.aqwmsgf,precision.je);
                item.glfLr = NumberUtil.numberScale(item.glfLr,precision.je);
                item.noPriceClf = NumberUtil.numberScale(item.noPriceClf,precision.je);
                item.rgf = NumberUtil.numberScale(item.rgf,precision.je);
                item.jxf = NumberUtil.numberScale(item.jxf,precision.je);
                item.sbf = NumberUtil.numberScale(item.sbf,precision.je);
                item.sj = NumberUtil.numberScale(item.sj,precision.je);
            }
            return returnListPrecision;
        } else {
            return returnList;
        }
    }

    async getconstructUnitSheet13List(param) {
        let Sheet4List = [];


        let heji = {};
        heji.total = "";

        let args = {};
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        const dlf = await this.service.gongLiaoJiProject.gljIndependentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(dlf.list)) {
            Sheet4List = ConvertUtil.deepCopy(dlf.list);
            let filter = Sheet4List.filter(o => !o.dispNo.includes("."));
            if (ObjectUtils.isNotEmpty(filter)) {
                heji.total = filter.reduce((sum, item) => sum + Number(ObjectUtils.isEmpty(item.totalPrice) ? 0 : item.totalPrice), 0);
            }
        }
        Sheet4List.push({
            dispNo:"",
            name:"合    计",
            unit:"",
            quantity:"",
            price:"",
            totalPrice:heji.total,
        });
        return Sheet4List;
    }


    async getconstructUnitSheet14List(param) {
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(param.constructId);
        // 一般就是不含税合价  简易就是含税合价
        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let isGeneral = true;
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            isGeneral = false;
        }
        let returnList = [];

        param.kind = 7;
        param.levelType = projectLevelConstant.unit;
        let resultList = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
        if (ObjectUtils.isNotEmpty(resultList)) {
            for (let i = 0; i < resultList.length; i++) {
                let element = resultList[i];
                let object = {
                    dispNo:i+1,
                    materialCode:element.materialCode,
                    materialName:element.materialName,
                    specification:element.specification,
                    unit:await this.getDisplayUnit(element),
                    totalNumber:GljExcelUtil._roundAndPadCeping(element.totalNumber,precision.RCJ_COLLECT.totalNumber),
                    baseJournalPrice:element.isDataTaxRate==0?"-":(isGeneral?GljExcelUtil._roundAndPadCeping(element.baseJournalPrice,precision.RCJ_COLLECT.baseJournalPrice):GljExcelUtil._roundAndPadCeping(element.baseJournalTaxPrice,precision.RCJ_COLLECT.baseJournalTaxPrice)),
                    marketPrice:element.isDataTaxRate==0?"-":(isGeneral?GljExcelUtil._roundAndPadCeping(element.marketPrice,precision.RCJ_COLLECT.marketPrice):GljExcelUtil._roundAndPadCeping(element.marketTaxPrice,precision.RCJ_COLLECT.marketTaxPrice)),
                    marketPriceTotal:element.isDataTaxRate==0?"-":(isGeneral?GljExcelUtil._roundAndPadCeping(element.total,precision.RCJ_COLLECT.total):GljExcelUtil._roundAndPadCeping(element.totalTax,precision.RCJ_COLLECT.totalTax)),
                }
                returnList.push(object);
            }
        }
        return returnList;
    }


    getProjectRootPath() {
        // let relativePath = __filename;
        // let index = relativePath.indexOf("pricing-cs");
        // let prefix = relativePath.substring(0,index);
        return UtilsPs.getExtraResourcesDir();
        // return prefix+"pricing-cs";
    }

    async showSheetStyleSample() {

        let excelPath = this.getProjectRootPath() + "\\excelTemplate\\unit\\sample.xlsx";
        let sheetName = "表1-6 分部分项工程量清单与计价表";
        let worksheet = await GljExcelUtil.read(excelPath, sheetName);
        let result = await GljExcelUtil.findCellStyleList(worksheet);
        return ResponseData.success(result);
    }




    async exportSingleSheetExcel(args) {
        let {lanMuName,params,startPage,totalPage} = args;
        let headLine = await this.traverseParams(params);
        const dialogOptions = {
            title: '导出excel',
            defaultPath: headLine,
            filters: [{name: 'excel文件', extensions: ['xlsx']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.exportExcelSingleSheet(lanMuName,params,filePath,startPage,totalPage);
            return true;
        }else {
            return false;
        }
    }

    async traverseParams(params) {
        if (ObjectUtils.isNotEmpty(params) && params.selected) {
            return params.headLine;
        }
        if (ObjectUtils.isNotEmpty(params) && ObjectUtils.isNotEmpty(params.childrenList)) {
            for (let i = 0; i < params.childrenList.length; i++) {
                let result = await this.traverseParams(params.childrenList[i]);
                if (ObjectUtils.isNotEmpty(result)) return result;
            }
        }
        return ;
    }


    async exportExcelSingleSheet(lanMuName,params,filePath,startPage,totalPage) {
        let constructIs2022= true;
        let construct = ProjectDomain.getDomain(params.id).getProjectById(params.id);
        if (ObjectUtils.isEmpty(construct.exportConfig)) {  //导出设置的配置
            construct.exportConfig = new GljExportConfig(null,null,"excelWithLevel");
        }
        let taxCalculationMethodPath = "";
        let taxCalculationMethod = construct.projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxCalculationMethodPath = "简易";
        }else {
            taxCalculationMethodPath = "一般";
        }
        let project = await this.initWorkBook(ProjectTypeConstants.PROJECT_TYPE_PROJECT,lanMuName,taxCalculationMethodPath);
        let single = await this.initWorkBook(ProjectTypeConstants.PROJECT_TYPE_SINGLE,lanMuName,taxCalculationMethodPath);
        let unit = await this.initWorkBook(ProjectTypeConstants.PROJECT_TYPE_UNIT,lanMuName,taxCalculationMethodPath);

        let fileDir = this.getProjectRootPath()+"\\excelTemplate\\export\\"+params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        args['startPage'] = startPage;
        args['totalPage'] = totalPage;
        args['containsTaxCalculation'] = construct.exportConfig.containsTaxCalculation;
        args['printPageFootAndEyeBrow'] = construct.exportConfig.exportPageEyeBrowPageFoot;
        await this.parseParams(params,project,single,unit,fileDir,args,taxCalculationMethodPath,lanMuName,workBookList);

        if (construct.exportConfig.batchExport=="excelWithLevel") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                await workBookList[i].sheet.xlsx.writeFile(workBookList[i].filename);
            }
        }else if (construct.exportConfig.batchExport=="excelWithAll") { //所有sheet到一个excel中

            await this.createDirectory(workBookList[0].fileDir);
            //合成一个大excel文件
            //先对workBookList[0]._worksheets 按照 worksheets的顺序进行重排  worksheets的属性orderNo 始终是有序的
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                //按照worksheets的元素排列  确定在_worksheets 中当前的索引  及id相同的对应索引 进行位置交换 使当前索引上的sheet是想要的对应id的sheet
                let indexCur = await this.getIndexIn_worksheets(i+1,workBookList[0].sheet);
                let indexId = await this.getIndexOfSameId(workBookList[0].sheet.worksheets[i].id,workBookList[0].sheet);
                [workBookList[0].sheet._worksheets[indexCur], workBookList[0].sheet._worksheets[indexId]] = [workBookList[0].sheet._worksheets[indexId],workBookList[0].sheet._worksheets[indexCur]];
            }

            //更新表名 增加格式 【单位工程名称】
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                let worksheet1 = workBookList[0].sheet.worksheets[i];
                let levelName = workBookList[0].filename.replace(workBookList[0].fileDir+"\\","").replace(".xlsx","");
                worksheet1.name = worksheet1.name+"【"+levelName+"】";
            }
            for (let i = 1; i < workBookList.length; i++) {
                let bookElement = workBookList[i].sheet;
                for (let j = 0; j < bookElement.worksheets.length; j++) {
                    let worksheet = bookElement.worksheets[j];
                    if (worksheet != null) {
                        let levelName = workBookList[i].filename.replace(workBookList[i].fileDir+"\\","").replace(".xlsx","");
                        worksheet.name =worksheet.name+"【"+levelName+"】";
                        workBookList[0].sheet._worksheets.push(worksheet);
                    }
                }
            }
            //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
            //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
            let orderNo = 0;
            let map = new Map();//键为sheet名称  值为重复的累加次数
            for (let i = 0; i < workBookList[0].sheet._worksheets.length; i++) {
                let worksheetSam = workBookList[0].sheet._worksheets[i];
                if (worksheetSam != null) {
                    let replace = worksheetSam.name.replace(/【.*?】/g, '');
                    let levelName = worksheetSam.name.replace(replace,'').replace("【","").replace("】","");
                    if (map.has(replace)) {
                        map.set(replace, map.get(replace)+1);
                    }else {
                        map.set(replace,1);
                    }
                    let wholeChar = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    if (wholeChar.length > 31) {
                        levelName = levelName.substring(0,levelName.length-(wholeChar.length-32));//去掉】  则名称多延长一位
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace);
                    }else {
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    }
                    worksheetSam.id = ++orderNo;
                    worksheetSam.orderNo = orderNo;
                }
            }
            await workBookList[0].sheet.xlsx.writeFile(workBookList[0].fileDir+"\\"+construct.constructName+".xlsx");
        }else if (construct.exportConfig.batchExport=="excelWithSingleSheet") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                let sheet = workBookList[i].sheet;
                for (let j = 0; j < sheet.worksheets.length; j++) {
                    let worksheet = sheet.worksheets[j];
                    let workbook = new ExcelJS.Workbook();
                    await GljExcelOperateUtil.addWorkSheet(workbook,worksheet,worksheet.name);
                    await workbook.xlsx.writeFile(workBookList[i].fileDir+"\\"+worksheet.name+".xlsx");
                }
            }
        }
        workBookList[0].sheet.xlsx.writeFile(filePath);
        function deleteDirectory(dirPath) {
            if (fs.existsSync(dirPath)) {
                fs.readdirSync(dirPath).forEach(file => {
                    const filePath = path.join(dirPath, file);

                    if (fs.lstatSync(filePath).isDirectory()) {
                        deleteDirectory(filePath); // 递归删除子目录
                    } else {
                        fs.unlinkSync(filePath); // 删除文件
                    }
                });

                fs.rmdirSync(dirPath); // 删除空目录
                console.log('目录删除成功');
            } else {
                console.log('目录不存在');
            }
        }
        deleteDirectory(fileDir);
    }

    // 创建目录
    async createDirectory(directoryPath) {
        if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, { recursive: true });
            console.log('目录已创建');
        } else {
            console.log('目录已存在');
        }
    }

    async getIndexIn_worksheets(order,workbook) {
        let index = 0;
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i]!=null) {
                index++;
                if (index == order) {
                    return i;
                }
            }
        }
    }

    async getIndexOfSameId(idParam,workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i]!=null && workbook._worksheets[i].id == idParam) {
                return i;
            }
        }
    }




    async saveSelected(args) {

        let {constructObj,itemLevel,headLineList,lanMuName,params} = args;
        headLineList = ObjectUtils.isNotEmpty(headLineList) ? headLineList : [];

        let headLineListInit = _.cloneDeep(headLineList);

        let constructSingleUnitList = ProjectDomain.getDomain(constructObj.constructId).getProjectTree().map(obj => obj.sequenceNbr);
        headLineList = headLineList.filter(p => !constructSingleUnitList.includes(p));

        let projectLevel = "";
        if (itemLevel == 1) {
            projectLevel = "project"
        } else if (itemLevel == 2) {
            projectLevel = "single";
        } else if (itemLevel == 3) {
            projectLevel = "unit"
        }

        lanMuName = await this.dealLanMuNameCeping(lanMuName);

        let reportSheetObject = _.cloneDeep(ExportSheetNameEnum);
        let reportSheetList = reportSheetObject[lanMuName].filter(p => p.projectLevel === projectLevel);
        let reportSheetIds = reportSheetList.map(obj => obj.id);
        let reportSheetIds1 = [];
        if (itemLevel == 3) {
            for (let i = 0; i < reportSheetIds.length; i++) {
                let item = reportSheetIds[i];
                if (lanMuName.includes("测评响应")) {
                    item = await this.appendUnitIdCeping(constructObj.unitId, item);
                } else {
                    item = await this.appendUnitId(constructObj.unitId, item);
                }
                reportSheetIds1.push(item);
            }
        } else {
            reportSheetIds1 = reportSheetIds.map(obj => obj);
        }
        let deleteList = reportSheetIds1.filter(value => !headLineList.includes(value));

        if (ObjectUtils.isNotEmpty(params)) {  //导出窗口的勾选
            await this.traverseParamsExport(params,lanMuName);
        }else {
            let {constructId,singleId,unitId} = constructObj;
            let object = null;
            if (itemLevel == 1) {
                object  = ProjectDomain.getDomain(constructId).getProjectById(constructId);
            }else if (itemLevel == 2) {
                object  = ProjectDomain.getDomain(constructId).getProjectById(singleId);
            }else if (itemLevel == 3) {
                object  = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            }

            if (constructObj.type === 0) {
                //外面勾选，项目单位二选一
                await this.updateProjectReportView(object, headLineList, deleteList);
            } else {
                //既要处理项目，又要处理单位
                let projectReportView = ProjectDomain.getDomain(constructId).getProjectById(constructId);
                let projectHeadLineList = headLineListInit.filter(p => typeof p === 'number');
                let reportSheetObject1 = _.cloneDeep(ExportSheetNameEnum);
                let reportSheetList1 = reportSheetObject1[lanMuName].filter(p => p.projectLevel === "project");
                let reportSheetIds1 = reportSheetList1.map(obj => obj.id);
                let deleteList = reportSheetIds1.filter(value => !projectHeadLineList.includes(value));
                await this.updateProjectReportView(projectReportView, projectHeadLineList, deleteList);

                let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
                for(let item of unitProjects){
                    headLineListInit = headLineListInit.filter(p => !constructSingleUnitList.includes(p));
                    let headLineListUnit = headLineListInit.filter(p => String(p).includes(String(item.sequenceNbr)));

                    let reportSheetList = reportSheetObject[lanMuName].filter(p => p.projectLevel === "unit");
                    let reportSheetIds = reportSheetList.map(obj => obj.id);
                    let reportSheetIds1 = [];
                    for (let i = 0; i < reportSheetIds.length; i++) {
                        let itemId = reportSheetIds[i];
                        if (lanMuName.includes("测评响应")) {
                            itemId = await this.appendUnitIdCeping(item.sequenceNbr, itemId);
                        } else {
                            itemId = await this.appendUnitId(item.sequenceNbr, itemId);
                        }
                        reportSheetIds1.push(itemId);
                    }
                    let deleteList = reportSheetIds1.filter(value => !headLineList.includes(value));
                    await this.updateProjectReportView(item, headLineListUnit, deleteList);
                }
            }

            // for (let i = 0; i < object.reportViewObject[lanMuName].length; i++) {
            //     if (ObjectUtils.isNotEmpty(headLineList) && headLineList.includes(object.reportViewObject[lanMuName][i].headLine)) {
            //         object.reportViewObject[lanMuName][i].selected = true;
            //     }else {
            //         object.reportViewObject[lanMuName][i].selected = false;
            //     }
            // }
        }
        return true;
    }


    async updateProjectReportView(object, headLineList, deleteList) {
        if (ObjectUtils.isEmpty(object.reportViewObject)) {
            object.reportViewObject = {};
            object.reportViewObject.headLineList = headLineList;
        } else {
            if (ObjectUtils.isNotEmpty(object.reportViewObject.headLineList)) {
                if (ObjectUtils.isNotEmpty(deleteList)) {
                    for (let deleteId of deleteList) {
                        object.reportViewObject.headLineList = object.reportViewObject.headLineList.filter(item => item !== deleteId);
                    }
                }

                if (ObjectUtils.isNotEmpty(headLineList)) {
                    for (let item of headLineList) {
                        if (!object.reportViewObject.headLineList.includes(item)) {
                            object.reportViewObject.headLineList.push(item);
                        }
                    }
                }
            } else {
                object.reportViewObject.headLineList = headLineList;
            }
        }
    }


    async traverseParamsExport(params,lanMuName,args ={}) {
        if (ObjectUtils.isNotEmpty(params.childrenList)) {
            for (let i = 0; i < params.childrenList.length; i++) {
                let paramElement = params.childrenList[i];
                //如果为总工程层级
                if (paramElement.projectLevel != null && paramElement.projectLevel == "project") {
                    args["constructId"] =params.id;
                    ProjectDomain.getDomain(params.id).getProjectById(params.id);
                    let reportElement = object.reportViewObject[lanMuName].filter(item => item.headLine==paramElement.headLine)[0];
                    if (paramElement.selected) {
                        reportElement.selected = true;
                    }else {
                        reportElement.selected = false;
                    }
                }
                if (paramElement.projectLevel != null && paramElement.projectLevel == "single") {
                    args["singleId"] =params.id;
                    let object  = ProjectDomain.getDomain(args.constructId).getProjectById(args.singleId);
                    let reportElement = object.reportViewObject[lanMuName].filter(item => item.headLine==paramElement.headLine)[0];
                    if (paramElement.selected) {
                        reportElement.selected = true;
                    }else {
                        reportElement.selected = false;
                    }
                }
                if (paramElement.projectLevel != null && paramElement.projectLevel == "unit") {
                    args["unitId"] =params.id;
                    let object  = ProjectDomain.getDomain(args.constructId).getProjectById(args.unitId);
                    let reportElement = object.reportViewObject[lanMuName].filter(item => item.headLine==paramElement.headLine)[0];
                    if (paramElement.selected) {
                        reportElement.selected = true;
                    }else {
                        reportElement.selected = false;
                    }
                }
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList!=null);//含有子节点的节点
        if (filter != null) {
            for (let i = 0; i < filter.length; i++) {
                await this.traverseParamsExport(filter[i],lanMuName,args);
            }
        }
    }


    async exportConfiguration(args) {
        let {constructId, exportPageEyeBrowPageFoot, containsTaxCalculation, batchExport} = args;
        let projectObjById = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        if (ObjectUtils.isEmpty(projectObjById.exportConfig)) {  //给默认值
            projectObjById.exportConfig = new GljExportConfig(null, null, "excelWithLevel");
        }
        if (ObjectUtils.isNotEmpty(exportPageEyeBrowPageFoot) && ObjectUtils.isNotEmpty(projectObjById.exportConfig)) {
            projectObjById.exportConfig.exportPageEyeBrowPageFoot = exportPageEyeBrowPageFoot;
        }
        if (ObjectUtils.isNotEmpty(containsTaxCalculation) && ObjectUtils.isNotEmpty(projectObjById.exportConfig)) {
            projectObjById.exportConfig.containsTaxCalculation = containsTaxCalculation;
        }
        if (ObjectUtils.isNotEmpty(batchExport) && ObjectUtils.isNotEmpty(projectObjById.exportConfig)) {
            projectObjById.exportConfig.batchExport = batchExport;
        }
        return;
    }

}


GljExportQueryService
    .toString = () => '[class GljExportQueryService]';
module
    .exports = GljExportQueryService;
