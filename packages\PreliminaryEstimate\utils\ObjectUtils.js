const _ = require('lodash');

class ObjectUtils{

    constructor() {
        function isType(type) {
            return function(obj) {
                return Object.prototype.toString.call(obj) === "[object " + type + "]";
            };
        }
        this.isObject = isType("Object");
        this.isString = isType("String")
        this.isArray = Array.isArray || isType("Array")
        this.isFunction = isType("Function")
        this.isUndefined = isType("Undefined")
        this.isNull = isType("Null")
        this.isNumber = isType("Number")
    }

    isEmpty(obj){
        return this.isNull(obj)
            || this.isUndefined(obj)
            || (this.isArray(obj) && obj.length === 0)
            || (this.isString(obj) && obj === '')
    }

    isNotEmpty(obj){
        return !this.isEmpty(obj);
    }

    is_Undefined(obj){
        return this.isUndefined(obj)

    }

    toJsonString(obj){
        return JSON.stringify(obj,(key, value) => typeof value === 'undefined' ? null : value);
    }

    updatePropertyValue(obj, propertyKey, newValue) {
        for (let key in obj) {
            if (typeof obj[key] === 'object') {
                if (Array.isArray(obj[key])) {
                    // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
                    obj[key].forEach((item) => {
                        this.updatePropertyValue(item, propertyKey, newValue);
                    });
                } else {
                    // 如果属性的值是对象，则递归调用更新函数
                    this.updatePropertyValue(obj[key], propertyKey, newValue);
                }
            } else if (key === propertyKey && !this.isEmpty(obj[key])) {
                // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
                obj[key] = newValue;
            }
        }
    }

     compareJSON(json1, json2) {
        var obj1 = JSON.parse(json1);
        var obj2 = JSON.parse(json2);

        return this.deepEqual(obj1, obj2);
    }

     deepEqual(obj1, obj2) {
         obj1 = obj1 === undefined ? null :obj1;
         obj2 = obj2 === undefined ? null :obj2;

        if (obj1 === obj2) {
            return true;
        }
        if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null) {
            return false;
        }

        var keys1 = Object.keys(obj1);
        var keys2 = Object.keys(obj2);

        if (keys1.length !== keys2.length) {
            return false;
        }

        for (var key of keys1) {
            if (!keys2.includes(key) || !this.deepEqual(obj1[key], obj2[key])) {
                return false;
            }
        }
        return true;
    }

    /**
     * 对象2的属性从对象1取值
     * @param obj1
     * @param obj2
     */
    copyProp(obj1, obj2) {
        // 判断 obj1 是否为 Map 对象
        const isObj1Map = obj1 instanceof Map;
        // 使用 for...in 循环遍历对象2的属性
        for (let key in obj2) {
            // 检查对象2的属性是否是对象自身的属性而不是继承的属性
            if (obj2.hasOwnProperty(key)) {
                // 如果 obj1 是普通对象，则检查它是否有相应的属性
                // 如果 obj1 是 Map 对象，则检查它是否有相应的键
                if (isObj1Map ? obj1.has(key) : obj1.hasOwnProperty(key)) {
                    obj2[key] = isObj1Map ? obj1.get(key) : obj1[key];
                }
            }
        }
        return obj2;
    }

    /**
     * 去重对象中中undefined 字段
     * @param obj
     */
    removeUndefined(obj) {
        for (let key in obj) {
            if (this.isEmpty(obj[key])) {
                delete obj[key];
            }
        }
        return obj
    }

    /**
     * 将对象1的所有属性赋值给对象2
     * @param obj1
     * @param obj2
     */
    copyPropAll(obj1, obj2) {
        // 使用for...in循环遍历对象2的属性
        for (var key in obj1) {
            // 检查对象2的属性是否是对象自身的属性而不是继承的属性
            if (obj1.hasOwnProperty(key)) {
                obj2[key] = obj1[key];
            }
        }
        return obj2;
    }

    /**
     * 判断是否是数字字符串
     * @param numStr
     * @returns {boolean}
     */
    isNumberStr(numStr){
        const regex = /^[-+]?\d+(\.\d+)?$/;
        return regex.test(numStr);
    }

    /**
     * 判断是否是汉字
     * @param str
     * @returns {boolean}
     */
    isChinese(str) {
        return /^[\u4e00-\u9fff]+$/.test(str);
    }

    /**
     * 对象递归转map
     * @param projectInfo
     * @returns {*}
     */
    convertToMap(projectInfo) {
        const map = new Map();
        for (const [key, value] of Object.entries(projectInfo)) {
            if (typeof value === 'object' && value !== null) {
                map.set(key, this.convertToMap(value));
            } else {
                map.set(key, value);
            }
        }
        return map;
    }


    /**
     * 对象递归转map
     * @param projectInfo
     * @returns {*}
     */
    convertObjectToMap(obj) {
        // 判断是否已经是 Map 类型
        if (obj instanceof Map) {
            return obj; // 如果是 Map 类型,直接返回
        } else {
            // 如果不是 Map 类型,则将对象转换为 Map
            return new Map(Object.entries(obj));
        }
    }

    /**
     * 将map转为array
     * @param map1
     * @returns {[]}
     */
    convertMapToArrayAndObject(map1) {
        let result = [];
        for (let [key1, value1] of map1) {
            let obj = {};
            for (const [key2, value2] of value1) {
                obj[key2] = value2;
            }
            result.push(obj);
        }
        return result;
    }

    /**
     * 将map转为array
     * @param map1
     * @returns {[]}
     */
    convertMapToArrayAndString(map1) {
        let result = [];
        for (let [key1, value1] of map1) {
            result.push(value1);
        }
        return result;
    }


    //用于stringify不支持es6中set map特殊类型
    stringifyComplexObject(obj) {
        if (obj instanceof Map) {
          // 如果当前对象是Map，则转换为对象字面量
          const objLiteral = {};
          obj.forEach((value, key) => {
            // 递归处理Map中的每个值，以支持嵌套Map
            objLiteral[key] = this.stringifyComplexObject(value);
          });
          return objLiteral;
        } else if (Array.isArray(obj)) {
          // 如果当前对象是数组，则递归处理数组中的每个元素
          return obj.map(value => this.stringifyComplexObject(value));
        } else if (typeof obj === 'object' && obj !== null) {
          // 如果当前对象是普通对象，则递归处理对象的每个属性
          const clonedObj = {};
          for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
              clonedObj[key] = this.stringifyComplexObject(obj[key]);
            }
          }
          return clonedObj;
        } else {
          // 如果当前对象是原始类型，直接返回
          return obj;
        }
    }

    /**
     * map startkey
     * @param map
     * @param startString
     * @returns {*}
     */
    getMapWithKeysStartingWith(map, startString) {
        const result = new Map();
        for (const [key, value] of map) {
            if (this.isNotEmpty(key) && key.startsWith(startString)) {
                result.set(key, value);
            }
        }
        return result;
    }

    /**
     * map startkey
     * @param map
     * @param startString
     * @returns {*}
     */
    getMapWithKeysStartingWith2(map, startString) {
        const result = [];
        for (const [key, value] of map) {
            if (this.isNotEmpty(key) && key.startsWith(startString)) {
                result.push(value);
            }
        }
        return result;
    }

    /**
     * map startkey
     * @param map
     * @param startString
     * @returns {*}
     */
    getMapWithKeysStartingWith3(map, startString) {
        const result = [];
        for (const [key, value] of map) {
            if (this.isNotEmpty(key) && !key.startsWith(startString)) {
                result.push(value);
            }
        }
        return result;
    }

    /**
     * 你可以使用 Object.keys() 遍历目标对象的属性，并结合 in 运算符和值判断来检查一个对象是否包含另一个对象的属性且有值。
     * @param target
     * @param source
     * @returns
     */
    hasPropertiesWithValue(target, source) {
        return Object.keys(source).every(key => key in target && target[key] !== undefined && target[key] !== null);
    }

    /**
     * 过滤函数
     * @param data
     * @param conditions
     * @param logicalOperator
     * @returns {*}
     */
    filterData(data, conditions, logicalOperator) {
        const formula = this.buildFormula(conditions, logicalOperator);
        return data.filter(item => {
            // 判断当前对象是否包含指定的字段的值
            if (this.hasPropertiesWithValue(item, conditions)) {
                // 使用 eval 计算公式
                return eval(formula);
            }
        });
    }

    /**
     * 插入新元素
     * @param data
     * @returns {*}
     */
    insertElement(data, itemLogicalOperator){
        // 新元素的定义
        let newElement = {
            operator: 'operator',
            value: itemLogicalOperator
        };
        // 计算元素数量
        let n = data.length;
        // 在每对相邻元素之间插入新元素
        for (let i = 1; i < n; i++) {
            data.splice(i * 2 - 1, 0, { ...newElement });
        }
        return data;
    }

    // 将条件转换为公式
    buildFormula(conditions, logicalOperator) {
        const operator = logicalOperator === 'AND' ? ' && ' : ' || ';
        const formulas = [];

        for (let field of Object.keys(conditions)) {
            const fieldConditions = conditions[field];
            if (this.isEmpty(fieldConditions) || fieldConditions.length < 1) {
                continue
            }
            formulas.push(this.buildItemFormula(field, fieldConditions));
        }

        return formulas.filter(item => item !== "()").join(operator);
    }

    /**
     * 将条件转换为公式
     * @param field
     * @param fieldConditions
     * @param itemLogicalOperator
     */
    buildItemFormula(field, fieldConditions) {
        const operator = 'AND' === 'AND' ? ' && ' : ' || ';
        let fieldFormulas = ''
        fieldConditions.map(condition => {
            let { operator: op, value } = condition;
            if (op === 'operator') {
                let operator = value === 'AND' ? ' && ' : ' || ';
                fieldFormulas = fieldFormulas + ` ${operator} `
            }else if (op === 'like') {
                fieldFormulas = fieldFormulas + `item["${field}"].includes("${value}")`;
            }else if (typeof value === 'string') {
                fieldFormulas = fieldFormulas + `item["${field}"] ${op} "${value}"`;
            } else {
                fieldFormulas = fieldFormulas + `item["${field}"] ${op} ${value}`;
            }
        });
        return `(${fieldFormulas})`;
    }

    /**
     * 将中文符号转换成英文符号
     */
    chineseChar2englishChar(chineseChar) {
        // 将单引号‘’转换为'，双引号“”转换为"
        let str = chineseChar.replace(/[’|‘]/g, "'").replace(/[“|”]/g, "\"");
        // 将中括号【】转换为[]，大括号｛｝转换为{}
        str = str.replace(/【/g, "[").replace(/】/g, "]").replace(/｛/g, "{").replace(/｝/g, "}");
        str = str.replace(/（/g, '(').replace(/）/g, ')');
        // 将中文逗号，转换为英文逗号，中文冒号转换为英文冒号
        str = str.replace(/，/g, ",").replace(/：/g, ":");
        // 将《转换为<，将》转换为>
        str = str.replace(/《/g, "<").replace(/》/g, ">");
        // 将句号。转换为.，问号？转换为?
        str = str.replace(/。/g, ".").replace(/？/g, "?");
        // 将感叹号！转换为!
        str = str.replace(/！/g, "!");
        // 将人民币符号￥转换为$
        str = str.replace(/￥/g, "$");
        return str;
    }

    // 计算交集的函数，接收 idKey 参数
    getIntersection(arr1, arr2, idKey) {
        return arr1.filter(item1 =>
            arr2.some(item2 => item1[idKey] === item2[idKey])
        );
    }

    // 计算并集的函数，接收 idKey 参数
    getUnion(arr1, arr2, idKey) {
        return [...arr1, ...arr2.filter(item2 =>
            !arr1.some(item1 => item1[idKey] === item2[idKey])
        )];
    }

    /**
     * 对象深拷贝
     */
    cloneDeep(obj) {
        return _.cloneDeep(obj);
    }

}

module.exports = {
    ObjectUtils: new ObjectUtils()
}