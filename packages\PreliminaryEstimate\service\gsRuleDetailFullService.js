const {Service} = require('../../../core');
const {GsConversionListItem} = require("../models/GsConversionListItem");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {GsUnitProject} = require("../models/GsUnitProject");
const { Snowflake } = require("../utils/Snowflake");
const {StandardConvertMod} = require("../enums/ConversionSourceEnum");
const { ConvertUtil } = require('../utils/ConvertUtils');

/**
 * 标准换算 service
 * @class
 */
class GsRuleDetailFullService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    // 标准换算列表
    async initStandardConvertList(args) {
        const {
            standardDeId,
            fbFxDeId,
            constructId,
            unitId,
        } = args;

        let rawLine = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, fbFxDeId);
        if (!rawLine || !standardDeId) return [];
        const list = await this.service.PreliminaryEstimate.gsBaseRuleDetailFullService.getRuleDetailByDe(rawLine.deCode, rawLine.deName, rawLine.libraryCode);

        let rcjIds = list.filter((v) => v.rcjId != "0").map((v) => v.rcjId);
        let rcjObjs = await this.service.PreliminaryEstimate.gsBaseRcjService.getRcjListByRcjIdList(rcjIds);
        let rcjObjMap = new Map();
        rcjObjs.forEach((v) => rcjObjMap.set(v.sequenceNbr, v));

        let conversionList = list?.map((v, index) => {
            const conversionListItem = new GsConversionListItem();
            return Object.assign(conversionListItem, v, {
                isSelect: false,
                value: v.defaultValue,
                selectValue: v.defaultValue,
                index,
                // kind3 前端渲染KEY
                selectedRule: v.kind === "3" ? v.defaultValue : null,
                // kind2 前端渲染KEY
                ruleInfo: v.kind === "2" ? v.relation : null,
                // kind1 前端渲染KEY
                selected: v.kind === "1" ? false : null,
                defaultValue:
                    v.kind === "1" ? "-" : v.kind === "2" ? v.relation : v.defaultValue,
                selectedRuleGroup: v.topGroupType,
                currentRcjCode: rcjObjMap.get(v.rcjId)?.materialCode,
                currentRcjLibraryCode: rcjObjMap.get(v.rcjId)?.libraryCode,
                defaultRcjCode: rcjObjMap.get(v.rcjId)?.materialCode,
                defaultRcjLibraryCode: rcjObjMap.get(v.rcjId)?.libraryCode,
            });
        });
        return conversionList;
    }

    /**
     * 初始化换算数据
     * @returns {Promise<(*&{sequenceNbr: *})[]>}
     */
    async initDef() {
        return this.getDefPiaoZhunHuanSuan.map((v) => {
            return {
                ...v,
                // 设置统一换算的主键id, 后续该id将记录在换算信息以及人材机下挂规则中.BS需要该字段。
                sequenceNbr: Snowflake.nextId(),
            };
        });
    }

    /**
     * 标准换算中 默认换算的模板数据
     * 根据产品要求。只保留R/C/J 三类
     * @return {[{val: number, sort: number, type: string},{val: number, sort: number, type: string},{val: number, sort: number, type: string},{val: number, sort: number, type: string}]}
     */
    getDefPiaoZhunHuanSuan = Object.freeze([
        { sort: 1, type: "人工费", val: 1 },
        { sort: 2, type: "材料费", val: 1 },
        { sort: 3, type: "机械费", val: 1 },
        // 转测演示提出 主材不要了
        //{"sort": 4, type: "主材费", "val": 1},
        { sort: 5, type: "单价", val: 1 },
    ]);

    // 换算数据列表
    async getDefDonversion(constructId, unitId, deId) {
        let de = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
        let rcjList = await this.service.PreliminaryEstimate.gsProjectCommonService.getAllRcjByDeId(constructId, unitId, deId);
        if ((rcjList.length < 1 && de.type !== "03") || (de.type === "06")) {
            return [];
        }
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        return unitConversion[deId]?.defaultConcersions;
    }

    // 换算数据列表
    async setDefDonversion(constructId, unitId, deId, defaultConcersions) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        let deConversion = unitConversion[deId];
        deConversion.defaultConcersions = defaultConcersions;
    }

    // 标准换算列表
    async getStandardConvertList(constructId, unitId, deId, defaultConcersions) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        return unitConversion[deId];
    }

    // 标准换算列表
    async getStandardConvertListAll(constructId, unitId) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        unitConversion = unitConversion?unitConversion: {};
        return Object.values(unitConversion);
    }

    // 标准换算列表
    async getStandardConvert(constructId, unitId, deId) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        let deConversion = unitConversion[deId];
        if (ObjectUtils.isEmpty(deConversion)) {
            let deModel = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
            await this.service.PreliminaryEstimate.gsInitDeService.initDeConversion(deModel);
            deConversion = unitConversion[deId];
        }
        return deConversion;
    }

    // 标准换算列表
    async setStandardConvert(constructId, unitId, de) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        unitConversion[de.sequenceNbr] = de;
    }

    // 切换标准换算模式 使用默认值或当前值
    async switchConversionMod(constructId, unitId, deId, standardConvertMod) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        let deConversion = unitConversion[deId];
        deConversion.standardConvertMod = standardConvertMod;
    }

    // 切换主材换算模式
    async switchConversionMainMatMod(constructId, unitId, deId, mainMatConvertMod) {
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversion = conversion[unitId];
        let deConversion = unitConversion[deId];
        deConversion.mainMatConvertMod = mainMatConvertMod;
    }

    /**
     * 标准换算替换定额
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async replaceDe(constructId, unitId, deId, isSaveConversionInfo=false) {
        // 子级定额标准换算
        let deLine = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);
        let childDeConversionMap = new Map();
        for (let child of deLine.children) {
            let childDeConversion = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(constructId, unitId, child.sequenceNbr);
            childDeConversionMap.set(child.standardId, childDeConversion);
        }

        let de = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        let deRow = await this.service.PreliminaryEstimate.gsDeService.replaceDe(constructId, unitId, de.standardId, deId);
        let replaceDe = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(constructId, unitId, deRow.sequenceNbr);
        replaceDe.originDefaultConcersions = ConvertUtil.deepCopy(de.originDefaultConcersions)
        replaceDe.defaultConcersions = ConvertUtil.deepCopy(de.defaultConcersions)
        replaceDe.originConversionList = ConvertUtil.deepCopy(de.originConversionList)
        replaceDe.conversionList = ConvertUtil.deepCopy(de.conversionList)
        replaceDe.standardConvertMod = de.standardConvertMod;
        if (isSaveConversionInfo) {
            replaceDe.conversionInfo = ConvertUtil.deepCopy(de.conversionInfo)
            for (let child of deLine.children) {
                let childDe = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(constructId, unitId, child.sequenceNbr);
                let oldChildDe = childDeConversionMap.get(child.standardId)
                if (ObjectUtils.isEmpty(oldChildDe)) {
                    continue
                }
                childDe.originDefaultConcersions = ConvertUtil.deepCopy(oldChildDe.originDefaultConcersions);
                childDe.defaultConcersions = ConvertUtil.deepCopy(oldChildDe.defaultConcersions)
                childDe.originConversionList = ConvertUtil.deepCopy(oldChildDe.originConversionList)
                childDe.conversionList = ConvertUtil.deepCopy(oldChildDe.conversionList)
                childDe.standardConvertMod = oldChildDe.standardConvertMod;
                childDe.conversionInfo = oldChildDe.conversionInfo?ConvertUtil.deepCopy(oldChildDe.conversionInfo):[]
                for (let conversion of childDe.conversionInfo) {
                    if (ObjectUtils.isNotEmpty(conversion.fbFxDeId) && conversion.fbFxDeId !== childDe.deId) {
                        conversion.fbFxDeId = childDe.deId
                    }
                }
            }
        }
        return deRow;
    }

    /**
     * 标准换算 定额人材机初始化
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async revertDe(constructId, unitId, deId) {
        // 定额
        let deLine = await this.service.PreliminaryEstimate.gsProjectCommonService.findDeByDeId(constructId, unitId, deId);

        // 原始定额
        let originalLine = await this.service.PreliminaryEstimate.gsBaseDeService.getDeAndRcj(deLine.standardId);

        // 子级定额

    }

    /**
     * 人材机还原
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async revertRcj(constructId, unitId, deId) {
        // 定额

    }

}

GsRuleDetailFullService.toString = () => '[class GsRuleDetailFullService]';
module.exports = GsRuleDetailFullService;
