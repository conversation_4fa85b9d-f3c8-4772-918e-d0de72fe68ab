/**
 * 单项层级造价分析
 */
export class GljCostAnalysisSingleVO {

    public projectCost: number;  // 总工程造价
    public jzgcProjectCost: number;  // 建筑工程造价
    public zsgcProjectCost: number;  // 装饰工程造价
    public azgcProjectCost: number;  // 安装工程造价
    public szgcProjectCost: number;  // 市政工程造价
    public yllhProjectCost: number;  // 园林绿化工程
    // public fxjzProjectCost: number;  // 房修建筑工程
    // public fxazProjectCost: number;  // 房修安装工程
    public fggcProjectCost: number;  // 仿古建筑工程
    public xsgcProjectCost: number;  // 古建（明清）修缮工程
    public yhgcProjectCost: number;  // 市政设施维修养护工程

    public unitcost: number; //单方造价(元/m、元/㎡)
    public budgetzjf: number; // 预算书-直接费 合计
    public ysrgf: number;   // 其中-人工费
    public ysclf: number;   // 其中-材料费
    public ysjxf: number;   // 其中-机械费
    public yssbf: number;   // 其中-设备费
    public yszcf: number;   // 其中-主材费

    public csxmf: number;   // 措施项目费
    public csrgf: number;   // 措施-人工费
    public csclf: number;   // 措施-材料费
    public csjxf: number;   // 措施-机械费
    public cssbf: number;   // 措施-设备费
    public cszcf: number;   // 措施-主材费

    public jktz: number;    // 价款调整

    public qyglf: number;  // 企业管理费
    public gf: number;  // 规费
    public lr: number;  // 利润
    public dlf: number; // 独立费
    public aqwmsgf: number; // 安全生产、文明施工费
    public sj: number;  // 税金
    public costProportion: number;  // 造价占比
    public average: number;  //工程规模

    public levelType: number;  //工程项目：1   单项：2   单位：3
    public dispNo: string;  //序号
    public projectName: string;  //项目名称
    public sequenceNbr: string;
    public childrenList: GljCostAnalysisSingleVO[];

    constructor(projectCost: number, jzgcProjectCost: number, zsgcProjectCost: number, azgcProjectCost: number, szgcProjectCost: number,
                yllhProjectCost: number, fggcProjectCost: number, xsgcProjectCost: number, yhgcProjectCost: number,
                unitcost: number, budgetzjf: number, ysrgf: number, ysclf: number, ysjxf: number, yssbf: number, yszcf: number,
                csxmf: number, csrgf: number, csclf: number, csjxf: number, cssbf: number, cszcf: number,
                jktz: number, qyglf: number, gf: number, lr: number, dlf: number, aqwmsgf: number, sj: number, costProportion: number, average: number,
                levelType: number, dispNo: string, projectName: string, sequenceNbr: string, childrenList: GljCostAnalysisSingleVO[]) {

        this.projectCost = projectCost;
        this.jzgcProjectCost = jzgcProjectCost;
        this.zsgcProjectCost = zsgcProjectCost;
        this.azgcProjectCost = azgcProjectCost;
        this.szgcProjectCost = szgcProjectCost;
        this.yllhProjectCost = yllhProjectCost;
        this.fggcProjectCost = fggcProjectCost;
        this.xsgcProjectCost = xsgcProjectCost;
        this.yhgcProjectCost = yhgcProjectCost;

        this.unitcost = unitcost;
        this.budgetzjf = budgetzjf;
        this.ysrgf = ysrgf;
        this.ysclf = ysclf;
        this.ysjxf = ysjxf;
        this.yssbf = yssbf;
        this.yszcf = yszcf;

        this.csxmf = csxmf;
        this.csrgf = csrgf;
        this.csclf = csclf;
        this.csjxf = csjxf;
        this.cssbf = cssbf;
        this.cszcf = cszcf;

        this.jktz = jktz;
        this.qyglf = qyglf;
        this.gf = gf;
        this.lr = lr;
        this.dlf = dlf;
        this.aqwmsgf = aqwmsgf;
        this.sj = sj;
        this.costProportion = costProportion;
        this.average = average;

        this.levelType = levelType;
        this.dispNo = dispNo;
        this.projectName = projectName;
        this.sequenceNbr = sequenceNbr;
        this.childrenList = childrenList;
    }
}
