const {ResponseData} = require("../../../common/ResponseData");
const {Controller} = require("../../../core");

/**
 * 概算汇总
 */
class GsEstimateSummaryController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 初始化概算汇总列表
     * @param args
     */
    defaultEstimateSummary(args) {
        return ResponseData.success(this.service.PreliminaryEstimate.gsEstimateSummaryService.defaultEstimateSummary(args));
    }

    /**
     * 获取概算汇总列表
     * @param args
     */
    async getEstimateSummaryList(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsEstimateSummaryService.getEstimateSummaryList(args));
    }

    /**
     * 获取计算后概算汇总
     * @param args
     * @returns {ResponseData}
     */
    countEstimateSummary(args) {
        return ResponseData.success(this.service.PreliminaryEstimate.gsEstimateSummaryService.countEstimateSummary(args));
    }

    /**
     * 插入概算汇总
     */
    async addEstimateSummary(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsEstimateSummaryService.addEstimateSummary(args));
    }

    /**
     * 删除概算汇总
     */
    async deleteEstimateSummary(args) {
        return await this.service.PreliminaryEstimate.gsEstimateSummaryService.deleteEstimateSummary(args);
    }

    /**
     * 保存或者修改概算汇总
     * @param args
     */
    async saveEstimateSummary(args) {
        return await this.service.PreliminaryEstimate.gsEstimateSummaryService.saveEstimateSummary(args);
    }

    /**
     * 导入概算汇总
     * @param args
     * @returns {ResponseData}
     */
    async importEstimateSummary(args) {
        return await this.service.PreliminaryEstimate.gsEstimateSummaryService.importEstimateSummary(args);
    }

    /**
     * 导出概算汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportEstimateSummary(args) {
        return await this.service.PreliminaryEstimate.gsEstimateSummaryService.exportEstimateSummary(args);
    }

    /**
     * 上移下移概算汇总
     * @param args
     * @returns {*}
     */
    async moveUpAndDownEstimateSummary(args) {
        return await this.service.PreliminaryEstimate.gsEstimateSummaryService.moveUpAndDownEstimateSummary(args);
    }

}

GsEstimateSummaryController.toString = () => '[class GsEstimateSummaryController]';
module.exports = GsEstimateSummaryController;