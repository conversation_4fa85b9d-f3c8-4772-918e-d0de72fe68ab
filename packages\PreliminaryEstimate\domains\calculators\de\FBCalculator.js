const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const DeTypeConstants = require("../../../constants/DeTypeConstants");
const {FBRules, fbCalculatorBaseFn} = require("./FBCalculatorCodes");
const LogUtil = require("../../../core/tools/logUtil");
const {NumberUtil} = require("../../../utils/NumberUtil");
const EE = require('../../../../../core/ee');
const DeUtils = require("../../utils/DeUtils");

/**
 * 分部分项计算器
 */
class FBCalculator extends CalculateEngine{
    static SPLITOR = "_";
    constructId;
    deRowId;
    unitId;
    precision;
    ctx;
    FBList = []
    constructor(constructId, unitId,deRowId,ctx) {
        super(ctx);
        this.ctx = ctx;
        this.constructId = constructId;
        this.unitId = unitId;
        this.deRowId = deRowId;
        this.relateQDIds = [];
    }
    static  qdMap = [
        // "RSum",
        // "CSum",
        // "JSum",
        "sTotalSum",
        "zTotalSum",
        "rTotalSum",
        "cTotalSum",
        "jTotalSum",
        "rdTotalSum",
        "cdTotalSum",
        "jdTotalSum",
        "sdTotalSum",
        "zdTotalSum",
        "totalNumber",
        "baseJournalTotalNumber",
    ];
    static  defaultRowMap = [
        "totalNumber"
    ];


    static getInstance({constructId,unitId,deRowId},ctx){
        return new FBCalculator(constructId,unitId,deRowId,ctx);
    }
    render()
    {
        for(let deRow of this.FBList) {
            for (let key of FBCalculator.qdMap) {
                let digital = DeUtils.getDePrecision(key, this.precision);
                if(ObjectUtils.isEmpty(digital)){
                    digital = 2;
                }
                let columnKey = key + "_" + deRow.sequenceNbr;
                deRow[key] = NumberUtil.numberFormat(this.parser(key + "_" + deRow.sequenceNbr),digital);
                this.instanceMap[columnKey] = deRow[key];
                LogUtil.renderLogger("FBCalculator :" + deRow.sequenceNbr + "-------------key :" + key + "-------------value :" + deRow[key]);
            }
            
            deRow.updateDate = Date.now();
        }
    }

    async prepare() {
        const {service} = EE.app;
        this.precision = service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(this.constructId);
    }

    async analyze() {
        await this.prepare();
        this.preload(fbCalculatorBaseFn);
        this.buildRules();
        await this.render()
    }

    getValue({type,kind,column})
    {
        let currentDe = this.ctx.deMap.getNodeById(this.deRowId);
        let value;
        switch (type) {
            case `parent`:{
                if (typeof column == 'function') {
                    value = column({ de: currentDe});
                } else {
                    value = currentDe[column];
                }
                let digital = DeUtils.getDePrecision(column, this.precision);
                if(ObjectUtils.isNotEmpty(digital)) {
                    value = NumberUtil.numberFormat(value, digital);
                }
                break;
            }

            default:{
                value = {type,kind,column};
                break;
            }
        }
        return value;
    }

    _getDeDepth(de,deList,types) {

    }
    buildRules() {
        let rules = {};
        let root = this.ctx.deMap.getAllNodes().find(item => item.type === DeTypeConstants.DE_TYPE_DEFAULT && item.unitId === this.unitId);
        let map = new Map();
        this.buildAnZhunagFee(root,map,root.sequenceNbr);
        this.buildSubTotalRules(root,rules,map);
        //console.log(rules);
        // let rules = {};
        // let currentDe = this.ctx.deMap.getNodeById(this.deRowId);
        // this.buildParentRules(rules,currentDe);
        this.loadRules(rules);
    }

    buildAnZhunagFee(root,map,topRowId){
        //没有子级就过滤
        if(ObjectUtils.isEmpty(root.children)) return;
        //查看子级是否又安装费
        for(let de of root.children){
            if(de.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtils.isNotEmpty(de.calculateMethod) && de.calculateMethod === 0){
                let mapArr  = [];
                if(map.has(topRowId)){
                    mapArr  = map.get(topRowId);
                }else{
                    map.set(topRowId,mapArr);
                }
                mapArr.push(de);
            }
            if(de.type === DeTypeConstants.DE_TYPE_ZFB || de.type === DeTypeConstants.DE_TYPE_FB){
                topRowId = de.sequenceNbr;
            }
            this.buildAnZhunagFee(de,map,topRowId)
        }
    }

    buildSubTotalRules(row,rules,map)
    {
        this.FBList.push(row);
        let totalNumber = "0";
        let baseJournalTotalNumber = "0";
        let rTotalSum = "0";
        let cTotalSum = "0";
        let jTotalSum = "0";
        let zTotalSum = "0";
        let sTotalSum = "0";
        let rdTotalSum = "0";
        let cdTotalSum = "0";
        let jdTotalSum = "0";
        let sdTotalSum = "0";
        let zdTotalSum = "0";
        for (let subRow of row.children)
        {
            if(subRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtils.isNotEmpty(subRow.calculateMethod)){
                continue;
            }
            totalNumber += "+" + this.getTotalNumberKey(subRow.sequenceNbr);
            baseJournalTotalNumber += "+" + this.getBaseJournalTotalNumber(subRow.sequenceNbr);
            rTotalSum += "+" + this.getRTotalSum(subRow.sequenceNbr);
            cTotalSum += "+" + this.getCTotalSum(subRow.sequenceNbr);
            jTotalSum += "+" + this.getJTotalSum(subRow.sequenceNbr);
            zTotalSum += "+" + this.getZTotalSum(subRow.sequenceNbr);
            sTotalSum += "+" + this.getSTotalSum(subRow.sequenceNbr);
            rdTotalSum += "+" + this.getRdTotalSum(subRow.sequenceNbr);
            cdTotalSum += "+" + this.getCdTotalSum(subRow.sequenceNbr);
            jdTotalSum += "+" + this.getJdTotalSum(subRow.sequenceNbr);
            sdTotalSum += "+" + this.getSdTotalSum(subRow.sequenceNbr);
            zdTotalSum += "+" + this.getZdTotalSum(subRow.sequenceNbr);
            if(subRow.type === DeTypeConstants.DE_TYPE_ZFB || subRow.type === DeTypeConstants.DE_TYPE_FB) {
                this.buildSubTotalRules(subRow, rules, map);
            }
            else
            {
                rules[this.getTotalNumberKey(subRow.sequenceNbr)] = FBRules["totalNumber"].mathFormula;
                rules[this.getBaseJournalTotalNumber(subRow.sequenceNbr)] = FBRules["baseJournalTotalNumber"].mathFormula;
                rules[this.getRTotalSum(subRow.sequenceNbr)] = FBRules["rTotalSum"].mathFormula;
                rules[this.getCTotalSum(subRow.sequenceNbr)] = FBRules["cTotalSum"].mathFormula;
                rules[this.getJTotalSum(subRow.sequenceNbr)] = FBRules["jTotalSum"].mathFormula;
                rules[this.getZTotalSum(subRow.sequenceNbr)] = FBRules["zTotalSum"].mathFormula;
                rules[this.getSTotalSum(subRow.sequenceNbr)] = FBRules["sTotalSum"].mathFormula;

                rules[this.getRdTotalSum(subRow.sequenceNbr)] = FBRules["rdTotalSum"].mathFormula;
                rules[this.getCdTotalSum(subRow.sequenceNbr)] = FBRules["cdTotalSum"].mathFormula;
                rules[this.getJdTotalSum(subRow.sequenceNbr)] = FBRules["jdTotalSum"].mathFormula;
                rules[this.getSdTotalSum(subRow.sequenceNbr)] = FBRules["sdTotalSum"].mathFormula;
                rules[this.getZdTotalSum(subRow.sequenceNbr)] = FBRules["zdTotalSum"].mathFormula;
            }
        }
        if(map.has(row.sequenceNbr)){
            let anzhuangList =  map.get(row.sequenceNbr);
            for(let anzhuang of anzhuangList){
                totalNumber += "+" + this.getTotalNumberKey(anzhuang.sequenceNbr);
                baseJournalTotalNumber += "+" + this.getBaseJournalTotalNumber(anzhuang.sequenceNbr);

                rTotalSum += "+" + this.getRTotalSum(anzhuang.sequenceNbr);
                cTotalSum += "+" + this.getCTotalSum(anzhuang.sequenceNbr);
                jTotalSum += "+" + this.getJTotalSum(anzhuang.sequenceNbr);
                zTotalSum += "+" + this.getZTotalSum(anzhuang.sequenceNbr);
                sTotalSum += "+" + this.getSTotalSum(anzhuang.sequenceNbr);
                rdTotalSum += "+" + this.getRdTotalSum(anzhuang.sequenceNbr);
                cdTotalSum += "+" + this.getCdTotalSum(anzhuang.sequenceNbr);
                jdTotalSum += "+" + this.getJdTotalSum(anzhuang.sequenceNbr);
                sdTotalSum += "+" + this.getSdTotalSum(anzhuang.sequenceNbr);
                zdTotalSum += "+" + this.getZdTotalSum(anzhuang.sequenceNbr);

                rules[this.getTotalNumberKey(anzhuang.sequenceNbr)] = FBRules["totalNumber"].mathFormula;
                rules[this.getBaseJournalTotalNumber(anzhuang.sequenceNbr)] = FBRules["baseJournalTotalNumber"].mathFormula;
                rules[this.getRTotalSum(anzhuang.sequenceNbr)] = FBRules["rTotalSum"].mathFormula;
                rules[this.getCTotalSum(anzhuang.sequenceNbr)] = FBRules["cTotalSum"].mathFormula;
                rules[this.getJTotalSum(anzhuang.sequenceNbr)] = FBRules["jTotalSum"].mathFormula;
                rules[this.getZTotalSum(anzhuang.sequenceNbr)] = FBRules["zTotalSum"].mathFormula;
                rules[this.getSTotalSum(anzhuang.sequenceNbr)] = FBRules["sTotalSum"].mathFormula;
                rules[this.getRdTotalSum(anzhuang.sequenceNbr)] = FBRules["rdTotalSum"].mathFormula;
                rules[this.getCdTotalSum(anzhuang.sequenceNbr)] = FBRules["cdTotalSum"].mathFormula;
                rules[this.getJdTotalSum(anzhuang.sequenceNbr)] = FBRules["jdTotalSum"].mathFormula;
                rules[this.getSdTotalSum(anzhuang.sequenceNbr)] = FBRules["sdTotalSum"].mathFormula;
                rules[this.getZdTotalSum(anzhuang.sequenceNbr)] = FBRules["zdTotalSum"].mathFormula;
            }
        }
        rules[this.getTotalNumberKey(row.sequenceNbr)] = totalNumber;
        rules[this.getBaseJournalTotalNumber(row.sequenceNbr)] = baseJournalTotalNumber;
        rules[this.getRTotalSum(row.sequenceNbr)] = rTotalSum;
        rules[this.getCTotalSum(row.sequenceNbr)] = cTotalSum;
        rules[this.getJTotalSum(row.sequenceNbr)] = jTotalSum;
        rules[this.getZTotalSum(row.sequenceNbr)] = zTotalSum;
        rules[this.getSTotalSum(row.sequenceNbr)] = sTotalSum;
        rules[this.getRdTotalSum(row.sequenceNbr)] = rdTotalSum;
        rules[this.getCdTotalSum(row.sequenceNbr)] = cdTotalSum;
        rules[this.getJdTotalSum(row.sequenceNbr)] = jdTotalSum;
        rules[this.getSdTotalSum(row.sequenceNbr)] = sdTotalSum;
        rules[this.getZdTotalSum(row.sequenceNbr)] = zdTotalSum;

    }
    buildParentRules(rules,currentDe) {
        if(ObjectUtils.isNotEmpty(currentDe) && currentDe.type !== DeTypeConstants.DE_TYPE_DEFAULT) {
            let parent = this.ctx.deMap.getNodeById(currentDe.parentId);
            if (ObjectUtils.isNotEmpty(currentDe)
                && (currentDe.type === DeTypeConstants.DE_TYPE_ZFB || currentDe.type === DeTypeConstants.DE_TYPE_FB)
                && currentDe.type !== DeTypeConstants.DE_TYPE_DEFAULT && ObjectUtils.isNotEmpty(currentDe.children)) {
                let totalNumberRule = "0";//单价
                let baseJournalTotalNumber = "0";//单价
                let rSum = "0";//人 基数 单价
                let rTotalSum = "0";//人 基数 合价
                let cSum = "0";//材 基数 单价
                let cTotalSum = "0";//材 基数 合价
                let jSum = "0";//机 基数 单价
                let jTotalSum = "0";//机 基数 合价
                let sSum = "0";//设备 基数 单价
                let sTotalSum = "0";//设备 基数 合价
                let zSum = "0";//主材 基数 单价
                let zTotalSum = "0";//主材 基数 合价
                let rdSum = "0";//人 基数 单价
                let rdTotalSum = "0";//人 基数 合价
                let cdSum = "0";//材 基数 单价
                let cdTotalSum = "0";//材 基数 合价
                let jdSum = "0";//机 基数 单价
                let jdTotalSum = "0";//机 基数 合价
                let sdSum = "0";//设备 基数 单价
                let sdTotalSum = "0";//设备 基数 合价
                let zdSum = "0";//主材 基数 单价
                let zdTotalSum = "0";//主材 基数 合价
                for (let subDeRow of currentDe.children) {
                    rules[this.getTotalNumberKey(subDeRow.sequenceNbr)] = FBRules['totalNumber'].mathFormula;
                    rules[this.getBaseJournalTotalNumber(subDeRow.sequenceNbr)] = FBRules['baseJournalTotalNumber'].mathFormula;
                    rules[this.getRSum(subDeRow.sequenceNbr)] = FBRules['RSum'].mathFormula;
                    rules[this.getJSum(subDeRow.sequenceNbr)] = FBRules['JSum'].mathFormula;
                    rules[this.getCSum(subDeRow.sequenceNbr)] = FBRules['CSum'].mathFormula;
                    rules[this.getSSum(subDeRow.sequenceNbr)] = FBRules['SSum'].mathFormula;
                    rules[this.getZSum(subDeRow.sequenceNbr)] = FBRules['ZSum'].mathFormula;
                    rules[this.getRdSum(subDeRow.sequenceNbr)] = FBRules['RDSum'].mathFormula;
                    rules[this.getCdSum(subDeRow.sequenceNbr)] = FBRules['CDSum'].mathFormula;
                    rules[this.getJdSum(subDeRow.sequenceNbr)] = FBRules['JDSum'].mathFormula;
                    rules[this.getSdSum(subDeRow.sequenceNbr)] = FBRules['SDSum'].mathFormula;
                    rules[this.getZdSum(subDeRow.sequenceNbr)] = FBRules['ZDSum'].mathFormula;
                    rSum += "+" + this.getRSum(subDeRow.sequenceNbr);
                    cSum += "+" + this.getCSum(subDeRow.sequenceNbr);
                    jSum += "+" + this.getJSum(subDeRow.sequenceNbr);
                    sSum += "+" + this.getSSum(subDeRow.sequenceNbr);
                    zSum += "+" + this.getZSum(subDeRow.sequenceNbr);
                    cdSum += "+" + this.getCdSum(subDeRow.sequenceNbr);
                    jdSum += "+" + this.getJdSum(subDeRow.sequenceNbr);
                    sdSum += "+" + this.getSdSum(subDeRow.sequenceNbr);
                    zdSum += "+" + this.getZdSum(subDeRow.sequenceNbr);

                    rules[this.getRTotalSum(subDeRow.sequenceNbr)] = FBRules['rTotalSum'].mathFormula;
                    rules[this.getCTotalSum(subDeRow.sequenceNbr)] = FBRules['cTotalSum'].mathFormula;
                    rules[this.getJTotalSum(subDeRow.sequenceNbr)] = FBRules['JTotalSum'].mathFormula;
                    rules[this.getSTotalSum(subDeRow.sequenceNbr)] = FBRules['STotalSum'].mathFormula;
                    rules[this.getZTotalSum(subDeRow.sequenceNbr)] = FBRules['ZTotalSum'].mathFormula;
                    rules[this.getRdTotalSum(subDeRow.sequenceNbr)] = FBRules['rdTotalSum'].mathFormula;
                    rules[this.getCdTotalSum(subDeRow.sequenceNbr)] = FBRules['cdTotalSum'].mathFormula;
                    rules[this.getJdTotalSum(subDeRow.sequenceNbr)] = FBRules['jdTotalSum'].mathFormula;
                    rules[this.getSdTotalSum(subDeRow.sequenceNbr)] = FBRules['sdTotalSum'].mathFormula;
                    rules[this.getZdTotalSum(subDeRow.sequenceNbr)] = FBRules['zdTotalSum'].mathFormula;

                    rTotalSum += "+" + this.getRTotalSum(subDeRow.sequenceNbr);
                    cTotalSum += "+" + this.getCTotalSum(subDeRow.sequenceNbr);
                    jTotalSum += "+" + this.getJTotalSum(subDeRow.sequenceNbr);
                    sTotalSum += "+" + this.getSTotalSum(subDeRow.sequenceNbr);
                    zTotalSum += "+" + this.getZTotalSum(subDeRow.sequenceNbr);
                    rdTotalSum += "+" + this.getRdTotalSum(subDeRow.sequenceNbr);
                    cdTotalSum += "+" + this.getCdTotalSum(subDeRow.sequenceNbr);
                    jdTotalSum += "+" + this.getJdTotalSum(subDeRow.sequenceNbr);
                    sdTotalSum += "+" + this.getSdTotalSum(subDeRow.sequenceNbr);
                    zdTotalSum += "+" + this.getZdTotalSum(subDeRow.sequenceNbr);

                    totalNumberRule += "+" + this.getTotalNumberKey(subDeRow.sequenceNbr);
                    baseJournalTotalNumber += "+" + this.getBaseJournalTotalNumber(subDeRow.sequenceNbr);
                }
                //人材机基数
                rules[this.getRSum(currentDe.sequenceNbr)] = rSum;//当前定额下人的基数汇总
                rules[this.getCSum(currentDe.sequenceNbr)] = cSum;//当前定额下材的基数汇总
                rules[this.getJSum(currentDe.sequenceNbr)] = jSum;//当前定额下机的基数汇总
                rules[this.getSSum(currentDe.sequenceNbr)] = sSum;//当前定额下材的基数汇总
                rules[this.getZSum(currentDe.sequenceNbr)] = zSum;//当前定额下机的基数汇总
                rules[this.getRdSum(currentDe.sequenceNbr)] = rdSum;//当前定额下人的基数汇总
                rules[this.getCdSum(currentDe.sequenceNbr)] = cdSum;//当前定额下材的基数汇总
                rules[this.getJdSum(currentDe.sequenceNbr)] = jdSum;//当前定额下机的基数汇总
                rules[this.getSdSum(currentDe.sequenceNbr)] = sdSum;//当前定额下材的基数汇总
                rules[this.getZdSum(currentDe.sequenceNbr)] = zdSum;//当前定额下机的基数汇总

                rules[this.getRTotalSum(currentDe.sequenceNbr)]  = rTotalSum;//当前定额下人的基数汇总
                rules[this.getCTotalSum(currentDe.sequenceNbr)]  = cTotalSum;//当前定额下材的基数汇总
                rules[this.getJTotalSum(currentDe.sequenceNbr)]  = jTotalSum;//当前定额下机的基数汇总
                rules[this.getSTotalSum(currentDe.sequenceNbr)]  = sTotalSum;//当前定额下材的基数汇总
                rules[this.getZTotalSum(currentDe.sequenceNbr)]  = zTotalSum;//当前定额下机的基数汇总
                rules[this.getRdTotalSum(currentDe.sequenceNbr)]  = rdTotalSum;//当前定额下人的基数汇总
                rules[this.getCdTotalSum(currentDe.sequenceNbr)]  = cdTotalSum;//当前定额下材的基数汇总
                rules[this.getJdTotalSum(currentDe.sequenceNbr)]  = jdTotalSum;//当前定额下机的基数汇总
                rules[this.getSdTotalSum(currentDe.sequenceNbr)]  = sdTotalSum;//当前定额下材的基数汇总
                rules[this.getZdTotalSum(currentDe.sequenceNbr)]  = zdTotalSum;//当前定额下机的基数汇总
                //合价
                rules[this.getTotalNumberKey(currentDe.sequenceNbr)] = totalNumberRule;
                rules[this.getBaseJournalTotalNumber(currentDe.sequenceNbr)] = baseJournalTotalNumber;

                this.loadRules(rules);
                this.relateQDIds.push(currentDe.sequenceNbr);
                this.render();
                this.buildParentRules(rules, parent);
            }

        }
    }

    getRuntimeValue({type,kind,column},param) {
        let value = 0;
        let key = param.split(FBCalculator.SPLITOR)[1];
        let item = this.ctx.deMap.getNodeById(key);
        switch (type) {
            case `item`: {
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
            }
        }
        if(ObjectUtils.isEmpty(value)){
            value = 0;
        }
        let columnKey = param.split(FBCalculator.SPLITOR)[0];
        let digital = DeUtils.getDePrecision(columnKey, this.precision);
        if(ObjectUtils.isNotEmpty(digital)) {
            value = NumberUtil.numberFormat(value, digital);
        }
        return value;
    }
    getRSum = (sequenceNbr) => {
        return "RSum_" + sequenceNbr;
    }
    getJSum = (sequenceNbr) => {
        return "JSum_" + sequenceNbr;
    }
    getCSum = (sequenceNbr) => {
        return "CSum_" + sequenceNbr;
    }
    getSSum = (sequenceNbr) => {
        return "SSum_" + sequenceNbr;
    }
    getZSum = (sequenceNbr) => {
        return "ZSum_" + sequenceNbr;
    }

    getTotalNumberKey = (sequenceNbr) => {
        return "totalNumber_" + sequenceNbr;
    }

    getRTotalSum = (sequenceNbr) => {
        return "rTotalSum_" + sequenceNbr;
    }
    getCTotalSum = (sequenceNbr) => {
        return "cTotalSum_" + sequenceNbr;
    }
    getJTotalSum = (sequenceNbr) => {
        return "jTotalSum_" + sequenceNbr;
    }
    getSTotalSum = (sequenceNbr) => {
        return "sTotalSum_" + sequenceNbr;
    }
    getZTotalSum = (sequenceNbr) => {
        return "zTotalSum_" + sequenceNbr;
    }
    getRdTotalSum = (sequenceNbr) => {
        return "rdTotalSum_" + sequenceNbr;
    }
    getCdTotalSum = (sequenceNbr) => {
        return "cdTotalSum_" + sequenceNbr;
    }
    getJdTotalSum = (sequenceNbr) => {
        return "jdTotalSum_" + sequenceNbr;
    }
    getSdTotalSum = (sequenceNbr) => {
        return "sdTotalSum_" + sequenceNbr;
    }
    getZdTotalSum = (sequenceNbr) => {
        return "zdTotalSum_" + sequenceNbr;
    }
    getBaseJournalTotalNumber = (sequenceNbr) => {
        return "baseJournalTotalNumber_" + sequenceNbr;
    }
    getCdSum = (sequenceNbr) => {
        return "CDSum_" + sequenceNbr;
    }
    getJdSum = (sequenceNbr) => {
        return "JDSum_" + sequenceNbr;
    }
    getSdSum = (sequenceNbr) => {
        return "SDSum_" + sequenceNbr;
    }
    getZdSum = (sequenceNbr) => {
        return "ZDSum_" + sequenceNbr;
    }
    getRdSum = (sequenceNbr) => {
        return "RSum_" + sequenceNbr;
    }
}
module.exports = {FBCalculator}
