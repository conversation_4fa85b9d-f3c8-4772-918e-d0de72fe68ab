const MathItemHandler = require("./mathItemHandler");
const DeCommonConstants = require("../../constants/DeCommonConstants");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. H XXXX n 新增材料，其消耗量为n
 */
class H$xxx$VMathHandler extends MathItemHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 4;
        let mathSubArr = mathItem.math.split(/\s+/);
        mathItem.addRCJCode = mathSubArr[1];
        mathItem.parseMath = mathSubArr[2];
        mathItem.operator = this.mathOperator(mathItem.parseMath);
    }

    async activeRCJ() {
        let item = this.mathItem;
        let initResQty = 0;
        if(DeCommonConstants.isCodeTZ(item.addRCJCode)){
            let parseMath = item.parseMath || "";
            initResQty = parseMath.replace(/[+*\/-]/, "");
        }
        let rcj = await this.addNewRCJ(this.rule.libraryCode, item.addRCJCode, initResQty);
        item.activeRCJs = [rcj];
    }
}

module.exports = H$xxx$VMathHandler;