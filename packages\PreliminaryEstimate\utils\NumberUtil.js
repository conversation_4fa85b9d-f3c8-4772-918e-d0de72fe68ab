const {ObjectUtils} = require("./ObjectUtils");
const Decimal = require('decimal.js');
const math = require('mathjs');
const {ObjectUtil} = require("../../../common/ObjectUtil"); // 引入mathjs库
class NumberUtil {
    constructor() {
        this.retain = 2;
    }

    /**
     * 数字格式化方法
     * @param number
     * @param decimalPlaces
     * @return {number}
     */
    numberFormat(number, decimalPlaces) {
        const parsedNumber = parseFloat(number);

        // 检查输入是否为有效数值
        if (isNaN(parsedNumber)) {
            throw new Error('输入无效的数字');
        }

        // 使用toFixed方法将数字保留指定位数小数
        const formattedNumber = parsedNumber.toFixed(decimalPlaces);
        return parseFloat(formattedNumber);
    }

    /**
     * 对num向下取整并保留scale位小数
     * @param number
     * @param scale
     */
    roundDown(number, scale) {
        if (!number || !ObjectUtils.isNumber(number)) {
            return 0.00;
        }
        if (ObjectUtil.isNumber(scale)) {
            return  Number(new Decimal(number).toFixed(scale, Decimal.ROUND_DOWN));
        }else{
            return Number(new Decimal(number).toFixed(this.retain, Decimal.ROUND_DOWN));
        }
    }

    /**
     * 保留decimalPlaces位数
     * @param number
     * @param decimalPlaces
     * @return {number}
     */
    numberScale(number, decimalPlaces) {
        if (!number || !ObjectUtils.isNumber(number)) {
            return 0.00;
        }
        if (ObjectUtils.isNumber(decimalPlaces)) {
            return Number(new Decimal(number).toFixed(decimalPlaces));
        } else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 保留2位数
     * @param number
     * @return {number}
     */
    numberScale2(number) {
        if (ObjectUtils.isNumber(number)) {
            return this.numberScale(number, 2);
        } else {
            return this.numberScale(Number(number), 2);
        }
    }


    numberScale9(number) {
        if (ObjectUtils.isNumber(number)) {
            return this.numberScale(number, 9);
        } else {
            return this.numberScale(Number(number), 9);
        }
    }

    /**
     * 保留三位小数
     * 小数点后不足位数，自动补0
     * @param number
     * @returns {string}
     */
    numberScale3(number) {
        // if (ObjectUtils.isNumber(number)) {
        //     return this.numberScale(number, 3);
        // } else {
        //     return this.numberScale(Number(number), 3);
        // }
        if(ObjectUtil.isEmpty(number)){
            return new Decimal(0).toFixed(3);
        }
        if (!ObjectUtil.isNumber(number)) {
            return new Decimal(parseFloat(number)).toFixed(3);
            throw new Error(`${number} is not a number`);
        }
        return new Decimal(number).toFixed(3);
    }

    /**
     * 保留4位数
     * @param number
     * @return {number}
     */
    numberScale4(number) {
        return this.numberScale(number, 4);
    }


    /**
     * 保留6位数
     * @param number
     * @return {number}
     */
    numberScale6(number) {
        return this.numberScale(number, 6);
    }

    add(a, b) {
        if (a === null || a === undefined) a = 0;
        if (b === null || b === undefined) b = 0;
        // return a + b;

        a = new Decimal(a);
        b = new Decimal(b);
        return a.plus(b).toNumber();  // .toNumber()或导致最后一位小数精度丢失  (a.plus(b)).mul(1000).toNumber())/1000;  //也会丢失精度
    }

    addParams(...args) {
        const sum = args.reduce((accumulator, current) => {
            if (current === null || current === undefined) current = 0;
            const decimalValue = new Decimal(current);
            return accumulator.plus(decimalValue);
        }, new Decimal(0));

        return sum.toNumber();
    }


    subtract(a, b) {
        if (a === null || a === undefined) a = 0;
        if (b === null || b === undefined) b = 0;
        // return a - b;

        a = new Decimal(a);
        b = new Decimal(b);
        return a.minus(b).toNumber();
    }

    multiply(a, b) {
        if (a === null || a === undefined) a = 0;
        if (b === null || b === undefined) b = 0;
        // return a * b;

        try {
            a = new Decimal(a);
            b = new Decimal(b);
        } catch (e) {
            return null;
        }

        return a.times(b).toNumber();
    }

    multiplyParams(...args) {
        const result = args.reduce((accumulator, currentValue) => {
            if (currentValue === null || currentValue === undefined) {
                currentValue = 0;
            }

            const decimalValue = new Decimal(currentValue);
            return accumulator.times(decimalValue);
        }, new Decimal(1));
        return result.toNumber();
    }

    multiplyToString(a, b, decimalPlaces) {
        if (a === null || a === undefined || a === '/') a = 0;
        if (b === null || b === undefined || b === '/') b = 0;

        let result = new Decimal(a).times(b);
        if (decimalPlaces !== undefined) {
            result = result.toFixed(decimalPlaces);
        }
        if (result == -0.00) {
            result = new Decimal(0).toFixed(decimalPlaces);
        }
        return result;
    }

    /**
     * 除100， 可用于求百分比
     *
     * @param data
     * @return
     */
    divide100(data) {
        if (ObjectUtils.isEmpty(data)) {
            data = 0;
        }
        return this.divide(data, 100);

    }


    divide(a, b) {
        if (a === null || a === undefined) a = 0;
        if (b === null || b === undefined) b = 0;
        if (b === 0) return 0;
        if (b === '') return 0;
        //return a / b;

        a = new Decimal(a);
        b = new Decimal(b);
        return a.dividedBy(b).toNumber();
    }

    /**
     * 金额小写转大写
     * @param n
     * @returns {string|string}
     */
    numToCny(n) {
        const fraction = ['角', '分'];
        const digit = [
            '零', '壹', '贰', '叁', '肆',
            '伍', '陆', '柒', '捌', '玖'
        ];
        const unitMap = [
            ['元', '万', '亿'],
            ['', '拾', '佰', '仟']
        ];

        const zeroFilter = ['零仟零佰零拾', '零仟零佰', '零佰零拾', '零仟', '零佰', '零拾'];

        if (n === "") {
            return "";
        }
        if (n === 0) {
            return "零整";
        }

        let num = Math.abs(n);
        let s = '';
        fraction.forEach((item, index) => {
            //这里保留五位小数  是因为 2097.12 * 10 * Math.pow(10, 1) = 209711.99999999997
            s += (digit[Math.floor(this.numberScale(n * 10 * Math.pow(10, index),5)) % 10] + item).replace(/零./, '');
        });
        s = s || '整';
        num = Math.floor(num);
        for (let i = 0; i < unitMap[0].length && num > 0; i++) {
            let p = '';
            for (let j = 0; j < unitMap[1].length && num > 0; j++) {
                p = digit[num % 10] + unitMap[1][j] + p;
                num = Math.floor(num / 10);
            }
            s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unitMap[0][i] + s;

            for (let zeroObj of zeroFilter) {
                if (s.includes(zeroObj)) {
                    s = s.replace(zeroObj, "零");
                    break;
                }
            }
            if (s.includes("零万")) {
                s = s.replace("零万", "");
            }
        }
        return n < 0 ? `负${s}` : s;
    }

    /**
     * 将数字金额转换为中文金额    80291517364.3  捌佰零贰亿玖仟壹佰伍拾壹万柒仟叁佰陆拾肆元点叁角整
     * @param money
     * @returns {string}
     */
    changeNumMoneyToChinese(money) {
        let cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
        let cnIntRadice = ['', '拾', '佰', '仟'];
        let cnIntUnits = ['', '万', '亿', '兆'];
        let cnDecUnits = ['角', '分', '毫', '厘'];
        let cnInteger = '整';
        let cnIntLast = '元';
        let maxNum = 999999999999999.9999;
        let IntegerNum, DecimalNum;
        let ChineseStr = '';
        let parts;
        let Symbol = '';

        if (money === "") {
            return "";
        }
        money = parseFloat(money);
        if (money >= maxNum) {
            alert('超出最大处理数字');
            return "";
        }
        if (money === 0) {
            // ChineseStr = cnNums[0] + cnIntLast + cnInteger;
            ChineseStr = cnNums[0] + cnInteger;
            return ChineseStr;
        }
        if (money < 0) {
            money = -money;
            Symbol = "负 ";
        }
        money = money.toString();
        if (money.indexOf(".") == -1) {
            IntegerNum = money;
            DecimalNum = '';
        } else {
            parts = money.split(".");
            IntegerNum = parts[0];
            DecimalNum = parts[1].substr(0, 4);
        }
        if (parseInt(IntegerNum, 10) > 0) {
            let zeroCount = 0;
            let IntLen = IntegerNum.length;
            for (let i = 0; i < IntLen; i++) {
                let n = IntegerNum.substr(i, 1);
                let p = IntLen - i - 1;
                let q = Math.floor(p / 4);
                let m = p % 4;
                if (n == "0") {
                    zeroCount++;
                } else {
                    if (zeroCount > 0) {
                        ChineseStr += cnNums[0];
                    }
                    zeroCount = 0;
                    ChineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
                }
                if (m == 0 && zeroCount < 4) {
                    ChineseStr += cnIntUnits[q];
                }
            }
            ChineseStr += cnIntLast;
        }
        if (DecimalNum !== '') {
            ChineseStr += '点';
            for (let i = 0; i < DecimalNum.length; i++) {
                ChineseStr += cnNums[parseInt(DecimalNum.charAt(i))] + cnDecUnits[i];
            }
        }
        if (ChineseStr === '') {
            ChineseStr += cnNums[0] + cnIntLast;
        }
        // ChineseStr += cnInteger;
        return Symbol + ChineseStr;
    }


    /**
     * 数字转中文
     * @param num
     * @return {*}
     */
    numberToChinese(num) {
        const chineseNumber = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
        const units = ['', '十', '百', '千', '万', '亿'];
        const str = num.toString();
        const reg = /(?<=(^|[^一-十]))十/g;

        return str
            .replace(reg, '一十')
            .replace(/\d/g, match => chineseNumber[parseInt(match)])
            .split('')
            .map((char, index) => {
                const unitIndex = (str.length - index - 1) % 4;

                if (char === '一' && unitIndex === 1) {
                    return units[unitIndex];
                } else {
                    return char + units[unitIndex];
                }
            })
            .join('');
    }


    /**
     *
     * @param number    比较的值
     * @param lowerBound 下限
     * @param upperBound  上限
     * @return {boolean}
     */
    isBetween(number, lowerBound, upperBound) {
        return number > lowerBound && number <= upperBound;
    }


    /**
     * 数字转中文 特殊业务需要用到
     * @param str
     * @return {[]}
     */
    numToChByBusiness(str) {

        /**
         *    "1~9,11~12,14,13.1,13.3~13.12"
         */

            //let result = {};
        let classLevel2 = [];

        const parts = str.split(",");
        for (const part of parts) {
            //包含~的元素
            if (part.includes("~")) {
                let item = part.split("~");
                //判断是否有小数
                if (item[0].includes(".")) {
                    //13.3~13.12
                    for (const itemElement of item) {
                        //有第三节的
                        let key = "第" + this.numberToChinese(parseInt(item[0])) + "章";
                        const containsKey = classLevel2.some(obj => obj.hasOwnProperty(key));
                        if (!containsKey) {
                            classLevel2.push({[key]: []});
                        }
                        let start = item[0].split(".")[1];
                        let end = item[1].split(".")[1];
                        const numbers = Array.from({length: parseInt(end) - parseInt(start) + 1}, (_, index) => parseInt(start) + index);
                        let array = [];
                        const value = classLevel2.find(obj => obj.hasOwnProperty(key))[key];
                        for (const number of numbers) {
                            value.push(this.numberToChinese(parseInt(number)) + "、");
                        }
                    }

                } else {
                    const numbers = Array.from({length: parseInt(item[1]) - parseInt(item[0]) + 1}, (_, index) => parseInt(item[0]) + index);
                    for (const number of numbers) {
                        classLevel2.push({["第" + this.numberToChinese(parseInt(number)) + "章"]: []});
                    }
                }
            } else {
                // 14,13.1
                const item = part.split(".");
                if (item.length > 1) {
                    //有第三节的
                    let key = "第" + this.numberToChinese(parseInt(item[0])) + "章";
                    const containsKey = classLevel2.some(obj => obj.hasOwnProperty(key));
                    if (!containsKey) {
                        classLevel2.push({[key]: []});
                    }
                    const value = classLevel2.find(obj => obj.hasOwnProperty(key))[key];
                    value.push(this.numberToChinese(parseInt(item[1])) + "、");
                } else {
                    //只有第二章
                    classLevel2.push({["第" + this.numberToChinese(parseInt(item[0])) + "章"]: []});
                }
            }
        }
        if (!ObjectUtils.isEmpty(classLevel2)) {
            classLevel2 = [...new Set(classLevel2)]
        }
        return classLevel2;
    }

    /**
     * 定义一个函数来计算数学公式的值
     * @param formula eg:"a * b + c"
     * @param model eg:{a: 2, b: 3, c: 5}
     * @returns {any}
     */
    calculateFormula(formula, model) {
        // 将公式中的变量名替换为对应的值
        let replacedFormula = formula.replace(/([a-zA-Z]+)/g, function (match) {
            // 检查变量是否在模型对象中存在，如果存在则替换为对应的值，否则保持不变
            return model.hasOwnProperty(match) ? model[match] : match;
        });

        // 使用eval函数动态计算替换后的表达式的值
        return eval(replacedFormula);
    }

    /**
     * 生成计算数学公式
     * @param formula eg:"a * b + c"
     * @param model eg:{a: 2, b: 3, c: 5}
     * @returns {any}
     */
    getReplacedFormula(formula, model) {
        // 将公式中的变量名替换为对应的值
        let replacedFormula = formula.replace(/([a-zA-Z]+)/g, function (match) {
            // 检查变量是否在模型对象中存在，如果存在则替换为对应的值，否则保持不变
            return model.hasOwnProperty(match) ? model[match] : match;
        });
        return replacedFormula;
    }

    /**
     *  处理‰符号的计算  如："[(money - 0) * 4‰] * zyRate * floatRate";
     * @param expr
     * @param variables
     * @returns {*}
     */
    calculateExpressionWithPerMille(expr, variables) {
        // 用变量值替换表达式中的变量名
        for (let varName in variables) {
            varName = varName.toString()
            expr = expr.replace(new RegExp(varName, 'g'), variables[varName]);
        }

        // 将表达式中的数字加‰替换成乘以0.001的形式
        expr = expr.replace(/(\d+)\s*‰/g, '$1*0.001');


        // 将‰替换为 * 0.001
        let rr = expr.replace(/\d+‰/g, (_, match) => `${match} * 0.001`);

        // 替换变量为它们的值
        for (let varName in variables) {
            varName = varName.toString()
            rr = expr.replace(new RegExp(varName, 'g'),  parseFloat(variables[varName]));
        }
        // 计算表达式的值
        // try {
        //     let result = eval(expr);
        //     return result;
        // } catch (error) {
        //     console.error("Error evaluating expression:", error);
        //     return null;
        // }
        return rr;
    }

    /**
     *  计算结果并和并公式
     * @param formula
     * @param model
     * @returns {string}
     */
    computationalFormula(formula, model) {
        try {
            // // 示例用法
            // let expr = "[(money - 0) * 4‰] * zyRate * floatRate";
            // let variables = {
            //     money: 1000,
            //     zyRate: 0.8,
            //     floatRate: 0.15
            // };

            let formula2 = this.calculateExpressionWithPerMille(formula, model);
            formula2 = formula2.replace(/{/g, "[").replace(/}/g, "]");  // 替换大括号为方括号

            // 使用 math.js 的 parse 和 evaluate 方法来解析和计算表达式
            const parsedExpr = math.parse(formula2);
            let result = parsedExpr.evaluate(model);
            let equation = this.getReplacedFormula(formula, model)
            if (ObjectUtils.isNotEmpty(result._data)) {
                if (Array.isArray(result._data) && result._data.length === 1) {
                    result = result._data[0];
                }
            }
            return equation + "=" + this.numberScale3(result);
        } catch (error) {
            // 如果表达式解析或计算过程中发生错误，抛出异常
            throw new Error(`Error evaluating expression: ${error.message}`);
        }
    }

    /**
     * 使用正则匹配连续不等式
     * @param inequality
     * @returns {string|*}
     */
    convertInequality(inequality) {
        let newInequality = inequality.replace(/≤/g, "<=").replace(/≥/g, ">=");
        // 使用正则表达式匹配连续不等式的格式
        const match = newInequality.match(/^(\d+)\s*([<>]=?)\s*n\s*([<>]=?)\s*(\d+)$/);
        if (match) {
            const [, lowerBound, lowerOp, upperOp, upperBound] = match;
            const lowerCondition = `${lowerBound} ${lowerOp} n`;
            const upperCondition = `n ${upperOp} ${upperBound}`;
            return `(${lowerCondition}) && (${upperCondition})`;
        } else {
            return newInequality;
        }
    }

    /**
     * 根据n获取公式
     * @param n
     * @param formualMap
     * @returns {string|null}
     * @private
     */
    getFormulaByN(n, formualMap) {
        for (const [key, value] of formualMap.entries()) {
            // 将str字符串解析为一个数学表达式
            // let expression = this.convertInequality(key).replace(/n/g, n);
            // console.log("key====>", key);
            // 将str字符串解析为一个数学表达式，并判断value的范围
            let expression = this.parseAndCheckRange(n, key);
            console.log("expression1====>", expression);
            if (eval(expression)) {
                console.log("value1====>", value);
                return value;
            }
        }
        return null; // 如果没有找到匹配的公式,返回 null
    }

    /**
     * 将str字符串解析为一个数学表达式，并判断value的范围
     * @param value
     * @param expression
     * @returns {boolean|*}
     */
    parseAndCheckRange(value, expression) {
        let newInequality = expression.replace(/≤/g, "<=").replace(/≥/g, ">=");
        let match = newInequality.split(" ");
        if (!match || match.length < 4) {
            return newInequality;
            // throw new Error('Invalid inequality format');
        }
        let parsed = this.parseInequality(match);
        let lowerBound = parsed.lowerBound;
        let lowerOp = parsed.lowerOp;
        let upperBound = parsed.upperBound;
        let upperOp = parsed.upperOp;

        switch (lowerOp) {
            case '<':
                lowerBound += Number.EPSILON;
                break;
            case '<=':
                break;
            default:
                throw new Error('Invalid lower bound operator');
        }

        switch (upperOp) {
            case '>':
                upperBound -= Number.EPSILON;
                break;
            case '<=':
                break;
            default:
                throw new Error('Invalid upper bound operator');
        }
        return lowerBound <= value && value <= upperBound;
    }

    /**
     * 解析为一个数学表达式
     * @param match
     * @returns {{upperOp: string, lowerOp, upperBound: number, lowerBound: number}}
     */
    parseInequality(match) {
        // let match = inequality.match(/^-?\d+(\.\d+)?([<=>]=?|-)(-?\d+(\.\d+)?)?$/i);
        const lowerBound = parseFloat(match[0]);
        const lowerOp = match[1];
        let upperBound;
        let upperOp = '=';

        if (match[4]) {
            upperBound = parseFloat(match[4]);
            upperOp = match[3] || '=';
        } else if (lowerOp === '-') {
            upperBound = lowerBound;
        }
        return {lowerBound, lowerOp, upperBound, upperOp};
    }


    /**
     * 根据key获取公式
     * @param key
     * @param formualMap
     * @returns {string}
     * @private
     */
    getFormulaByKey(key, formualMap) {
        for (const [key2, value] of Object.entries(formualMap)) {
            if (key === key2) {
                return value;
            }
        }
        return null; // 如果没有找到匹配的公式,返回 null
    }


    /**
     ** 乘法函数，用来得到精确的乘法结果
     ** 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
     ** 调用：accMul(arg1,arg2)
     ** 返回值：arg1乘以 arg2的精确结果
     **/
    accMul(arg1, arg2) {
        var m = 0,
            s1 = arg1.toString(),
            s2 = arg2.toString();
        try {
            m += s1.split(".")[1].length;
        } catch (e) {}
        try {
            m += s2.split(".")[1].length;
        } catch (e) {}
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
    }

    /**
     ** 加法函数，用来得到精确的加法结果
     ** 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
     ** 调用：accAdd(arg1,arg2)
     ** 返回值：arg1加上arg2的精确结果
     **/
    accAdd(arg1, arg2) {
        var r1, r2, m, c;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        c = Math.abs(r1 - r2);
        m = Math.pow(10, Math.max(r1, r2));
        if (c > 0) {
            var cm = Math.pow(10, c);
            if (r1 > r2) {
                arg1 = Number(arg1.toString().replace(".", ""));
                arg2 = Number(arg2.toString().replace(".", "")) * cm;
            } else {
                arg1 = Number(arg1.toString().replace(".", "")) * cm;
                arg2 = Number(arg2.toString().replace(".", ""));
            }
        } else {
            arg1 = Number(arg1.toString().replace(".", ""));
            arg2 = Number(arg2.toString().replace(".", ""));
        }
        return (arg1 + arg2) / m;
    }

    /**
   * 四舍五入，小数点默认保留两位有效数字
   * 小数点后不足位数，自动补0
   * @param num
   */
  roundHalfUp(num) {
    if (!ObjectUtil.isNumber(num)) {
      throw new Error(`${num} is not a number`);
    }
    return new Decimal(num).toFixed(2);
  }

    /**
     * 次方解析
     * @param formula
     * @returns {*}
     */
    replacePowerOperations(formula) {
        // 替换括号中的次方运算，例如 (a + b)^2
        formula = formula.replace(/(\([^\)]+\))\^(\d+)/g, (match, p1, p2) => {
            return `Math.pow(${p1}, ${p2})`;
        });
        // 替换变量的次方运算，例如 a^2
        formula = formula.replace(/(\w+)\^(\d+)/g, (match, p1, p2) => {
            return `Math.pow(${p1}, ${p2})`;
        });
        return formula;
    }

    /**
     * 四则运算
     */
    calculateAndValidate(expr){
        if(ObjectUtils.isNumber(expr)){
            return expr;
        }
        //基础字符及语法检查
        if(!/^[\d\s+\-*().]+$/.test(expr)){
            throw new Error('输入无效的表达式');
        }
        //禁止连续运算符号
        if(/[+\-*/]{2,}/.test(expr)){
            throw new Error('输入无效的连续运算符表达式');
        }
        //括号匹配检查
        let validate = true;
        const stack = [];
        for(const char of expr){
            if(char === '('){
                stack.push(char);
            }else if (char === ')'){
                if(!stack.length) validate = false;
                stack.pop();
            }
        }
        if(stack.length){
            validate = false;
        }
        if(!validate){
            throw new Error('输入无效的括号表达式');
        }
        try{
            return math.evaluate(expr);
        }catch{
            throw new Error('输入无效的值表达式');
        }
    }
}

module.exports = {
    NumberUtil: new NumberUtil()
};
