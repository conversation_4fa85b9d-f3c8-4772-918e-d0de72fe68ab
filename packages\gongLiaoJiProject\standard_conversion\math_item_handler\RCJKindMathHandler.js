const RCJPriceMathHandler = require("./RCJPriceMathHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");


/**
 * 单条规则处理math:根据人材机类型过滤activeRCJ
 */
class RCJKindMathHandler extends RCJPriceMathHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 1;
        let mathSubs = mathItem.math.split(/\s+/);
        mathItem.parseMath = mathSubs[2];
        mathItem.operator = this.mathOperator(mathItem.parseMath);
        mathItem.activeRCJKind = mathSubs[1].split('-').map(item => {
            const num = Number(item);
            return isNaN(num) ? item : num;
        });
    }

    dealShowMath(){

    }

    /**
     * 准备数据：根据不同规则准备定额、人材机、规则的取消处理等
     */
    async prepare() {
        await super.prepare();
        if (ObjectUtils.isNotEmpty(this.mathItem.activeRCJs)) {
            let deCodeMath = this.conversionService.mathAfterCalculation(this.mathItem.parseMath, this.showMathDigits);
            let mathsArrTmp = this.mathItem.activeRCJs.map(rcj => `H ${rcj.materialCode} ${rcj.materialCode} ${deCodeMath}`);
            this.showMath = mathsArrTmp.join(',');

            let VMath = this.oriMath.split(/\s+/)[2];
            let mathsArrTmp22 = this.mathItem.activeRCJs.map(rcj => `H ${rcj.materialCode} ${rcj.materialCode} ${VMath}`);
            this.oriMath = mathsArrTmp22.join(',');
        }
    }
}

module.exports = RCJKindMathHandler;