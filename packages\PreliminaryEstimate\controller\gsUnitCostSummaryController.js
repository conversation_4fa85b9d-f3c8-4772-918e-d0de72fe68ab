const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");

class GsUnitCostSummaryController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取默认费用汇总
     * @returns {ResponseData}
     */
    defaultUnitCostSummary(args) {
        const res = this.service.PreliminaryEstimate.gsUnitCostSummaryService.defaultUnitCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 获取费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async getUnitCostSummaryList(args) {
        const res = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummaryList(args);
        return ResponseData.success(res);
    }

    /**
     * 获取局部费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async getUnitPartCostSummaryList(args) {
        let params = {
            constructId: args.constructId,
            unitId: args.unitId,
            singleId: args.singleId
        }

        let costSummaryMajorMenuList = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getCostSummaryMajorMenuList(params)
        if (ObjectUtils.isEmpty(costSummaryMajorMenuList) || costSummaryMajorMenuList.itemList?.length <= 1) {
            args.isCostSummary = true;
            const res = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice2(args)
            return ResponseData.success(res);
        }
        if (ObjectUtils.isEmpty(args.constructMajorType)) {
            // 局部多专业汇总
            const res = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getPartCostSummaryMajorsTotal(args);
            return ResponseData.success(res);
        }
        const res = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice2(args)
        return ResponseData.success(res);
    }

    /**
     * 导出局部费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitPartCostSummary(args) {
        const res = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.exportUnitPartCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 获取计算后费用汇总
     * @param args
     * @returns {ResponseData}
     */
    countUnitCostSummary(args) {
        const res = this.service.PreliminaryEstimate.gsUnitCostSummaryService.countUnitCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 插入费用汇总
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async addCostSummary(args) {
        const res = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.addCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 删除费用汇总
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteCostSummary(args) {
        return await this.service.PreliminaryEstimate.gsUnitCostSummaryService.deleteCostSummary(args);
    }

    /**
     * 保存或者修改费用汇总
     * @param args
     * @returns {*}
     */
    async saveCostSummary(args) {
        return await this.service.PreliminaryEstimate.gsUnitCostSummaryService.saveCostSummary(args);
    }

    /**
     * 获取工程专业下拉框
     * @param args
     * @returns {ResponseData}
     */
    async getConstructMajorTypeEnum(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getConstructMajorTypeEnum(args));
    }

    /**
     * 获取费用汇总左侧树
     * @param args
     * @returns {ResponseData}
     */
    async getCostSummaryMajorMenuList(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getCostSummaryMajorMenuList(args));
    }

    /**
     * 多专业汇总-多专业汇总补充工程专业
     * @param args
     * @returns {ResponseData}
     */
    async supplyCostSummaryMajors(args) {
        return await this.service.PreliminaryEstimate.gsUnitCostSummaryService.supplyCostSummaryMajors(args);
    }

    /**
     * 导入费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async importUnitCostSummary(args) {
        return await this.service.PreliminaryEstimate.gsUnitCostSummaryService.importUnitCostSummary(args);
    }

    /**
     * 导出费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitCostSummary(args) {
        return await this.service.PreliminaryEstimate.gsUnitCostSummaryService.exportUnitCostSummary(args);
    }

    /**
     * 获取费用汇总模板列表
     * @param args
     * @returns {ResponseData}
     */
    getCostSummaryTemplate(args){
        const res = this.service.PreliminaryEstimate.gsUnitCostSummaryService.getCostSummaryTemplate(args);
        return ResponseData.success(res);
    }

    /**
     * 恢复默认模板路径
     * @param args
     * @returns {ResponseData}
     */
    async restoreDefaultTemplatePath(args){
        const res = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.restoreDefaultTemplatePath(args);
        return ResponseData.success(res);
    }

    /**
     * 选择费用汇总模板
     * @param args
     */
    async selectCostSummaryTemplate(args){
        const res = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.selectCostSummaryTemplate(args);
        return ResponseData.success(res);
    }

    /**
     * 根据模板名称获取数据
     */
    getTemplateData(args){
        const res = this.service.PreliminaryEstimate.gsUnitCostSummaryService.getTemplateData(args);
        return ResponseData.success(res);
    }


    /**
     * 获取费用汇总应用范围
     * @param args
     */
    async scopeOfApplicationsCostSummary(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.scopeOfApplicationsCostSummary(args));
    }

    /**
     * 批量应用费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async batchApplicationsCostSummary(args) {
        return await this.service.PreliminaryEstimate.gsUnitCostSummaryService.batchApplicationsCostSummary(args);
    }

    /**
     * 多专业汇总-多专业汇总后的总金额合计
     * @param args
     * @returns {ResponseData}
     */
    async getCostSummaryMajorsTotal(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getCostSummaryMajorsTotal(args));
    }

    /**
     * 多专业汇总-多专业汇总后的总金额合计-修改
     * @param args
     * @returns {ResponseData}
     */
    async updateCostSummaryMajorsTotal(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.updateCostSummaryMajorsTotal(args));
    }

    /**
     * 获取费用汇总或局部汇总，是否是多专业标识
     * @param args
     * @returns {ResponseData}
     */
    async getIsSingleMajorFlag(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getIsSingleMajorFlag(args));
    }

    /**
     * 设置费用汇总或局部汇总，是否是多专业标识
     * @param args
     * @returns {Promise<*>}
     */
    async setIsSingleMajorFlag(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.setIsSingleMajorFlag(args));
    }

    /**
     * 获取当前单位的取费专业
     * @param args
     * @returns {ResponseData}
     */
    async getQfMajorTypeByUnit(args) {
        return ResponseData.success(await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getQfMajorTypeByUnit(args));
    }




}

GsUnitCostSummaryController.toString = () => '[class GsUnitCostSummaryController]';
module.exports = GsUnitCostSummaryController;