const StandardDeModel = require('./StandardDeModel');
const DeTypeConstants = require('../../../constants/DeTypeConstants');
const CommonConstants = require("../../../constants/CommonConstants");
const RcjCommonConstants = require('../../../constants/RcjCommonConstants');

class ResourceModel extends StandardDeModel
{
  /***人材机 只在概算中有实现
   static DE_TYPE_DEFAULT_RESOURCE = "05 ";
   **/
  qdId;
  kind;
  ifDonorMaterial =RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
  isDeResource = CommonConstants.COMMON_NO;//定额行人材机
  isDeCompensation = CommonConstants.COMMON_NO;//是否定额调差人材机，默认否
  ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;//不锁定
  ifProvisionalEstimate = RcjCommonConstants.IF_PROVISIONAL_ESTIMATE;//非暂估
  isFyrcj = RcjCommonConstants.IS_FYRCJ;//非费用人材机
  materialQt = RcjCommonConstants.MATERIAL_QT_NON;//非其他人材机
  constructor(constructId,unitId,sequenceNbr,parentId,type) {
    super(constructId,unitId,sequenceNbr,parentId,type);
  }

}
ResourceModel.toString = () => 'ResourceModel';
module.exports = ResourceModel;