
const Kind3TypeBRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeBRuleHandler");
const Kind2RuleHandler = require("../standard_conversion/rule_handler/Kind2RuleHandler");
const Kind3RuleHandler = require("../standard_conversion/rule_handler/Kind3RuleHandler");
const Kind3TypeCRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeCRuleHandler");
const GsKind3TypeCRuleHandler = require("../standard_conversion/rule_handler/GsKind3TypeCRuleHandler");
const Kind4RuleHandler = require("../standard_conversion/rule_handler/Kind4RuleHandler");
const Kind0RuleHandler = require("../standard_conversion/rule_handler/Kind0RuleHandler");
const Kind1RuleHandler = require("../standard_conversion/rule_handler/Kind1RuleHandler");
const ConversionService = require("../standard_conversion/util/ConversionService");
const EE = require("../../../core/ee");
const Kind5RuleHandler = require("./rule_handler/Kind5RuleHandler");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ConversionInfoUtil} = require("../standard_conversion/util/ConversionInfoUtil");
const ProjectDomain = require("../domains/ProjectDomain");
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const WildcardMap = require("../core/container/WildcardMap");
const CommonConstants = require("../constants/CommonConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");

class ConversionInfoStrategy{
    static UNITE_RULE_KIND = "4";

    constructor() {
        this.service = EE.app.service;
        this.app = EE.app;
        this.commonService = this.service.PreliminaryEstimate.gsProjectCommonService;
    }

    async init(constructId, singleId, unitId, deId, cancelLockNumber) {
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.deId = deId;
        this.cancelLockNumber = !!cancelLockNumber;

        this.unitProject = await this.commonService.getUnit(constructId, unitId);

        this.deLine = await this.commonService.findDeByDeId(constructId, unitId, deId);
        this.isCsxmDe = !!this.deLine.isCsxmDe;
        this.de = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        this.deAdjustmentCoefficient = this.deLine.parent?.adjustmentCoefficient || 1;
        this.deOrCsxmDomain = this.isCsxmDe ? ProjectDomain.getDomain(this.constructId).csxmDomain : ProjectDomain.getDomain(this.constructId).deDomain;
        this.deUpDateObj = {
            // 换算信息
            redArray: [],
            blackArray: [],
            nameSuffixArray: [],
            addedDes: [],
            deTypes: []
        }

        this.rcjCacheMaps = this._createRcjCacheMap();

        this.conversionService = new ConversionService();

        // 精度
        this.precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        this.jd = {
            resQty: this.precision.DETAIL.RCJ.resQty, // 人材机消耗量精度
            totalNumber: this.precision.DETAIL.RCJ.totalNumber, //人材机数量精度
            quantity: this.precision.EDIT.DE.quantity, //定额工程量精度
            deRcjPrice: this.precision.EDIT.DERCJ.price, //定额人材机单价
        }
    }

    async prepare(){
        let params = {
            constructId: this.constructId,
            singleId: this.singleId,
            unitId: this.unitId,
            unitProject: this.unitProject,
            de: this.deLine,
            adjustmentCoefficient: this.deAdjustmentCoefficient,
            ...this.rcjCacheMaps
        };
        this.subDeRcj = await this.conversionService.deInitialDes(params);
        this.deInitialRcjs = await this.conversionService.deInitialRcjs(params);
        this._dealTempRemoveAndLockNumber(this.deInitialRcjs);
        await this._delDeAddByRule();
    }

    /**
     * 执行标准换算
     */
    async execute(){
        let conversionInfos = this.de.conversionInfo || [];

        // if(ObjectUtil.isEmpty(conversionInfos)){ //此处取消为空的判断，是因为最后一条换算信息取消后，费用记取可能需要重新执行
        //     return;
        // }
        await this.prepare();
        let a0 = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(this.constructId, this.unitId, this.deId);
        for(let rule of conversionInfos){
            //标准换算、统一换算规则处理
            if(rule.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE){
                for(let r of rule.children){
                    let handler = this.getRuleHandler(this, r);
                    await handler.execute();
                }
            }else{
                // 人材机修改生成换算信息处理
                let handler = this.getRuleHandler(this, rule);
                await handler.execute();
            }

        }
        let a2 = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(this.constructId, this.unitId, this.deId);
        await this.after();
        let a3 = await this.service.PreliminaryEstimate.gsRuleDetailFullService.getStandardConvert(this.constructId, this.unitId, this.deId);

        return this.deUpDateObj.addedDes;
    }


    /**
     * 标准换算执行后计算，其他材料/机械、单价构成、......
     */
    async after(){

        this._upDateDeInfo();

        // 调整系数处理
        this._dealDeAdjustmentCoefficient();

        // TODO 将处理后的人材机替换单位工程中对应定额人材机
        // let rcjs =this.constructProjectRcjs.filter(rcj => rcj.deId != this.de.sequenceNbr);
        // rcjs.push(...this.deInitialRcjs);
        // this.constructProjectRcjs = rcjs;
        //
        // for (let item of this.de.conversionInfo) {
        //     item.mathHandlers = []
        // }

        // let rcjs = ProjectDomain.getDomain(this.constructId).resourceDomain.getResource(WildcardMap.generateKey(this.unitId, this.de.sequenceNbr) + WildcardMap.WILDCARD);

        let isConversion = true;// 标准换算的定额类型，需要特殊处理
        if(this.isCsxmDe){
            // 重新计算人材机
            await ProjectDomain.getDomain(this.constructId).csxmDomain.notify(this.deLine);
            DeTypeCheckUtil.checkAllDeType(this.deLine, ProjectDomain.getDomain(this.constructId).csxmDomain.ctx,isConversion);
        }else{
            await ProjectDomain.getDomain(this.constructId).deDomain.notify(this.deLine);
            DeTypeCheckUtil.checkAllDeType(this.deLine, ProjectDomain.getDomain(this.constructId).deDomain.ctx,isConversion);
        }

        // 修改单价调差
        // await this._updateDePrice();

        // 重新计算费用汇总
        try {
            await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                constructId: this.constructId,
                singleId: this.singleId,
                unitId: this.unitId,
                constructMajorType: this.deLine.libraryCode
            });
            // await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
            //     constructId: this.constructId,
            //     singleId: this.singleId,
            //     unitId: this.unitId,
            //     qfMajorType: null
            // });
        } catch (error) {
            console.error("捕获到异常:", error);
        }

        // 处理人材机冻结状态：冻结的人材机不参与标准换算
        // this._dealFreeze();
    }

    _createRcjCacheMap(){
        let tempDeleteFlagMap = new Map();
        let rcjPriceMap = new Map();
        let rcjLockNumMap = new Map();
        let deRcjs = ProjectDomain.getDomain(this.constructId).resourceDomain.getResource(WildcardMap.generateKey(this.unitId, this.de.sequenceNbr) + WildcardMap.WILDCARD);

        // 人材机处理
        for(let r of deRcjs){
            // if(deModel.isTempRemove = CommonConstants.COMMON_YES ){
            //     resource.isTempRemove = CommonConstants.COMMON_YES;
            //     resource.changeResQty = resource.resQty;
            //     resource.resQty = 0;
            // }
            // 临时删除材料处理
            if(r.isTempRemove == CommonConstants.COMMON_YES){
                tempDeleteFlagMap.set(r.materialCode, {
                    ...r
                });
            }

            if(r.isNumLock && !rcjLockNumMap.has(r.materialCode)){
                rcjLockNumMap.set(r.materialCode, {
                    ...r
                });
            }

            rcjPriceMap.set(r.materialCode, {
                ...r
            });

        }

        return {
            tempDeleteFlagMap,
            rcjPriceMap,
            rcjLockNumMap
        }
    }

    _dealTempRemoveAndLockNumber(deInitialRcjs){
        for(let rcj of deInitialRcjs){
            // 锁定数量的处理要放在临时删除标记处理之前
            this.dealLockNumberOneRcj(rcj);
            this.dealTempRemoveOneRcj(rcj);
        }
    }

    dealTempRemoveOneRcj(rcj){
        let {tempDeleteFlagMap} = this.rcjCacheMaps;

        // 处理临时删除标记
        if(this.deLine.isTempRemove == 1 || tempDeleteFlagMap.has(rcj.materialCode)){
            let tempRemoveRcjTmp = tempDeleteFlagMap.get(rcj.materialCode);
            rcj.isTempRemove = tempRemoveRcjTmp.isTempRemove;
            rcj.isFirstTempRemove = tempRemoveRcjTmp.isFirstTempRemove;
            // 消耗量如何处理
            rcj.changeResQty = rcj.isNumLock ? tempRemoveRcjTmp.changeResQty : rcj.resQty;
            rcj.resQty = 0;
        }
    }

    dealLockNumberOneRcj(rcj){
        let {rcjLockNumMap} = this.rcjCacheMaps;
        if(rcjLockNumMap.has(rcj.materialCode)){
            let lockRcjTmp = rcjLockNumMap.get(rcj.materialCode);
            rcj.resQtyConversionLock = lockRcjTmp.resQtyForNumLockAndDeQuantityZero || rcj.resQty;
            rcj.isNumLock = lockRcjTmp.isNumLock;
            rcj.resQty = lockRcjTmp.resQtyForNumLockAndDeQuantityZero || lockRcjTmp.resQty;
            rcj.numLockNum = lockRcjTmp.numLockNum;
            rcj.totalNumber = lockRcjTmp.totalNumber;

            rcjLockNumMap.delete(rcj.materialCode);
        }
    }

    _dealDeAdjustmentCoefficient(){
        if(this.deAdjustmentCoefficient == 1){
            return;
        }

        let rcjs = ProjectDomain.getDomain(this.constructId).resourceDomain.getResource(WildcardMap.generateKey(this.unitId, this.de.sequenceNbr) + WildcardMap.WILDCARD);

        for(let rcj of rcjs){
            if(rcj.isNumLock){
                if(rcj.hasOwnProperty("resQtyConversionLock")){
                    rcj.resQtyConversionLock = rcj.resQtyConversionLock * this.deAdjustmentCoefficient;
                }
            }else{
                let destResQty = (rcj.isTempRemove == CommonConstants.COMMON_YES) ? rcj.changeResQty * this.deAdjustmentCoefficient : rcj.resQty * this.deAdjustmentCoefficient;
                this.conversionService.updateTempRemoveRCJResQty(rcj, destResQty);
            }
        }
    }

    _upDateDeInfo(){
        //换算信息
        this._resetDeName();

        this.de.redArray = this.deUpDateObj.redArray;
        this.de.codeSuffixHistory = this.deUpDateObj.redArray;
        this.de.blackArray = this.deUpDateObj.blackArray;
        this.de.nameSuffixHistory = this.deUpDateObj.nameSuffixArray;

        let nameSuffix = this.deUpDateObj.nameSuffixArray.join(" ");

        this.deLine.deName = `${this.deLine.deName} ${nameSuffix}`;
        this.de.deName = this.deLine.deName;
    }

    _resetDeName() {
        let de = this.de;
        let deLine = this.deLine;
        if (
            Array.isArray(de.nameSuffixHistory) &&
            de.nameSuffixHistory.length > 0
        ) {
            // 恢复名称
            for (let history of de.nameSuffixHistory) {
                if(ObjectUtil.isEmpty(history)){
                    continue;
                }
                history = history.trim();
                deLine.deName = deLine.deName.replace(history, "").trim();
            }
        }
    }

    async _delDeAddByRule(){
        let de = this.de;
        if (ObjectUtil.isEmpty(de.addByRuleDeIds)){
            return;
        }

        // 当恢复换算默认值时，删除上次新增定额
        for ( let deRuleIdObj of de.addByRuleDeIds) {
            let addDeId = deRuleIdObj.deId;
            let deAddByRule = await this.commonService.findDeByDeId(this.constructId, this.unitId, addDeId);
            if(ObjectUtil.isNotEmpty(deAddByRule)) {
                ProjectDomain.getDomain(this.constructId).deDomain.removeRowRelatedDatas(deAddByRule)
            }
        }

        de.addByRuleDeIds = [];
    }

    getRuleHandler(ctx, rule){
        if(rule.kind == "1"){
            return new Kind1RuleHandler(ctx, rule);
        }

        if(rule.kind == "2"){
            return new Kind2RuleHandler(ctx, rule);
        }

        if(rule.kind == "3"){
            if(rule.type == "b") {
                return new Kind3TypeBRuleHandler(ctx, rule);
            }if(rule.type == "c") {
                if (this.deLine.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ
                    || this.deLine.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                    return new GsKind3TypeCRuleHandler(ctx, rule);
                } else {
                    return new Kind3TypeCRuleHandler(ctx, rule);
                }
            }else{
                return new Kind3RuleHandler(ctx, rule);
            }
        }

        if(rule.kind == "4"){
            return new Kind4RuleHandler(ctx, rule);
        }

        if(rule.kind == "0"){
            return new Kind0RuleHandler(ctx, rule);
        }

        if(rule.kind == "5"){
            return new Kind5RuleHandler(ctx, rule);
        }
    }

    _donorMaterialNumberRefresh(idParams) {
        let rcjs = this.deInitialRcjs.find(rcj => rcj.ifDonorMaterial == 1);
        if(ObjectUtil.isNotEmpty(rcjs)){
            this.service.rcjProcess.donorMaterialNumberRefresh(idParams.constructId, idParams.singleId, idParams.unitId);
        }

        return combine.sort((a, b) => a.index - b.index);
    }

    _formatRuleByUniteRules(unitRules, de) {

        if(ObjectUtils.isEmpty(unitRules)){
            return [];
        }

        const typeMaps = new Map([
            ["人工费", "R"],
            ["机械费", "J"],
            ["材料费", "C"],
            ["单价", ""]
        ]);

        return unitRules.map((unitRule, index) => {
            const type = typeMaps.get(unitRule.type);

            // 拼接 R*n C*n J*n
            let math = type + "*" + unitRule.val;
            // 默认的规则用 定额id + def +类型标识
            // 注释该seqNo, 直接使用主键newAlgorithm.sequenceNbr; //let seqNo = deId + "def" + type;
            return {
                sequenceNbr: unitRule.sequenceNbr,
                type: "0",
                kind: unitRule.kind || ConversionInfoStrategy.UNITE_RULE_KIND,
                math: math,
                relation: math,
                defaultValue: 1,
                selectedRule: unitRule.val,
                fbFxDeId: de.sequenceNbr, // 分部分项或措施项目定额id; ps:标准换算中有fbFxDeId,这里在统一换算中也加上,用于BS端在处理ysf文件时,通过deId+ruleId反查出operatingRecord.
                index: 999999 + index,
                libraryCode: de.libraryCode,
                isUniteRule: true,
            };
        });
    }

    _formatRuleByConversionList(conversionList){
        if(ObjectUtils.isEmpty(conversionList)){
            return [];
        }

        conversionList.forEach((r) => {
            r.isUniteRule = false;
            r.fbFxDeId = r.deId;
        });

        return conversionList;
    }

    _conversionSnapshot(de, currentRules) {
        // 查询列表时会初始化 如果这时未初始化 直接报错
        de.conversionList = de.conversionList || [];

        de.conversionList.forEach((target)=> {
            const srcRule = currentRules.find((r) => r.sequenceNbr == target.sequenceNbr);
            if(!srcRule){
                return;
            }

            if (typeof srcRule.selected == "string") {
                target.value = srcRule.selected;
            } else {
                target.selected = srcRule.selected;
            }
            target.index = srcRule.index;
            target.clpb = srcRule.clpb;
            target.selectedRule = srcRule.selectedRule;
            if(srcRule.clpb?.detailsCode){
                target.currentRcjCode =  srcRule.clpb.detailsCode;
                target.currentRcjLibraryCode = srcRule.clpb.libraryCode;
                target.rcjId = srcRule.clpb.standardId;
                target.ruleInfo = srcRule.clpb.details + " " + (ObjectUtils.isEmpty(srcRule.clpb.specification) ? "" : srcRule.clpb.specification);
                target.selectedRuleGroup = srcRule.clpb.groupName;
                target.topGroupType = srcRule.clpb.groupName;
            }
        });

        de.conversionList = de.conversionList.sort((a, b) => a.index - b.index);
    }

    async _deInitialRcjs(unitProject, de) {
        let rcjs = [];

        // if(ObjectUtils.isEmpty(de.jointStandardRcj)){
        //     return [];
        // }

        // TODO 二级定额处理
        let deRcjRelationList = await this.service.PreliminaryEstimate.gsBaseDeRcjRelationService.getDeRcjRelationByDeId(de.standardId);

        let codes = deRcjRelationList.map(r => r.materialCode);

        for (let i = 0; i < codes.length; i++) {
            let code = codes[i];
            // TODO 缓存处理
            let rcjMemory = await this.service.PreliminaryEstimate.gsRcjService.getRcjMemory(this.constructId, this.unitId);
            let rcj = ObjectUtil.cloneDeep(UnitRcjCacheUtil.getByCode(unitProject,code));
            if(rcj){
                rcj.deId = this.de.sequenceNbr;
                rcjs.push(rcj);
            }
        }

        let deRcjs = this.constructProjectRcjs.filter(f => f.deId === this.de.sequenceNbr);
        let deRcjIds = deRcjs.map(item => item.sequenceNbr)
        // 恢复-删除
        for (let rcj of deRcjs) {
            if (!this.deLine.initChildIds.includes(rcj.sequenceNbr)) {
                let param = {isConversionDeal: true}
                await this.service.PreliminaryEstimate.gsRcjService.deleteRcjByCodeData(this.deId, this.constructId, this.unitId, rcj.sequenceNbr, true, param);
            }
        }
        // 恢复-新增
        let insertRcjIds = this.deLine.initChildIds.filter(item => !(deRcjIds.includes(item)));
        for (let insertRcjId of insertRcjIds) {
            let conversion = this.de.conversionInfo?.find(item => item.rcjId === insertRcjId && item.rcjType === 'del')
            if (ObjectUtils.isNotEmpty(this.de.lastConversionInfo)) {
                conversion = this.de.lastConversionInfo?.find(item => item.rcjId === insertRcjId && item.rcjType === 'del');
            }
            if (conversion) {
                let param = {
                    isConversionDeal: true,
                    sequenceNbr: conversion.rcjId
                };
                let baseRcjModel = await this.service.PreliminaryEstimate.gsBaseRcjService.getRcjBySequenceNbr(conversion.originalRcjId);
                await this.service.PreliminaryEstimate.gsRcjService.addRcjData(this.deId, baseRcjModel, this.constructId, this.singleId, this.unitId, this.deId, "", param);
                this.constructProjectRcjs = ProjectDomain.getDomain(this.constructId).getResourceDomain().getResource(WildcardMap.generateKey(this.unitId) + WildcardMap.WILDCARD);
                let replaceRcj = this.constructProjectRcjs.find(item => item.sequenceNbr === conversion.rcjId)
                ObjectUtils.copyProp(conversion.rcj, replaceRcj);
            }
        }
        // 恢复-替换
        let conversionReplaces = this.de.conversionInfo?.filter(item => item.rcjType === 'replace')
        if (ObjectUtils.isNotEmpty(this.de.lastConversionInfo)) {
            conversionReplaces = this.de.lastConversionInfo?.filter(item => item.rcjType === 'replace');
            conversionReplaces = conversionReplaces?conversionReplaces:[]
        }
        for (let conversionReplace of conversionReplaces) {
            let replaceRcj = this.constructProjectRcjs.find(item => item.sequenceNbr === conversionReplace.rcjId)
            ObjectUtils.copyProp(conversionReplace.rcj, replaceRcj);
        }
        // 恢复-消耗量
        let conversionUpdateQtys = this.de.conversionInfo?.filter(item => item.rcjType === 'updateQty')
        if (ObjectUtils.isNotEmpty(this.de.lastConversionInfo)) {
            conversionUpdateQtys = this.de.lastConversionInfo?.filter(item => item.rcjType === 'updateQty');
            conversionUpdateQtys = conversionUpdateQtys?conversionUpdateQtys:[]
        }
        for (let conversionUpdateQty of conversionUpdateQtys) {
            this.constructProjectRcjs.map(item => {
                if (item.sequenceNbr === conversionUpdateQty.rcjId) {
                    item.resQty = conversionUpdateQty.lastResQty
                }
            });
            this.constructProjectRcjs.find(item => item.sequenceNbr === conversionUpdateQtys[0].rcjId);
        }

        this.constructProjectRcjs = ProjectDomain.getDomain(de.constructId).resourceDomain.getResource(WildcardMap.generateKey(de.unitId) + WildcardMap.WILDCARD);
        return rcjs;
    }
}

module.exports = ConversionInfoStrategy;
