const {ObjectUtils} = require("../../../utils/ObjectUtils");
const qdCalculatorBaseFn = {
    //清单工程量
    "quantity": () => {
        return {
            "type": "item",
            "column": "quantity"
        };
    },
    //清单工程量
    "totalNumber": () => {
        return {
            "type": "item",
            "column": "totalNumber"
        };
    },
     //子级消耗量
    "resQty": () => {
        return {
            "type": "item",
            "column": "resQty"
        };
    },
    //子级市场价
    "price": () => {
        return {
            "type": "item",
            "column":"price"
        };
    },
    "baseJournalPrice": () => {
        return {
            "type": "item",
            "column":"baseJournalPrice"
        };
    },
    "marketPrice": () => {
        return {
            "type": "item",
            "column":"marketPrice"
        };
    },
    "JSum": () => {
        return {
            "type": "item",
            "column":"JSum"
        };
    },"CSum": () => {
        return {
            "type": "item",
            "column":"CSum"
        };
    },"RSum": () => {
        return {
            "type": "item",
            "column":"RSum"
        };
    },"SSum": () => {
        return {
            "type": "item",
            "column":"SSum"
        };
    },"ZSum": () => {
        return {
            "type": "item",
            "column":"ZSum"
        };
    },
    "JDSum": () => {
        return {
            "type": "item",
            "column":"JDSum"
        };
    },"CDSum": () => {
        return {
            "type": "item",
            "column":"CDSum"
        };
    },"RDSum": () => {
        return {
            "type": "item",
            "column":"RDSum"
        };
    },"SDSum": () => {
        return {
            "type": "item",
            "column":"SDSum"
        };
    },"ZDSum": () => {
        return {
            "type": "item",
            "column":"ZDSum"
        };
    },
    "rTotalSum": () => {
        return {
            "type": "item",
            "column":"rTotalSum"
        };
    },"cTotalSum": () => {
        return {
            "type": "item",
            "column":"cTotalSum"
        };
    },"jTotalSum": () => {
        return {
            "type": "item",
            "column":"jTotalSum"
        };
    },"sTotalSum": () => {
        return {
            "type": "item",
            "column":"sTotalSum"
        };
    },"zTotalSum": () => {
        return {
            "type": "item",
            "column":"zTotalSum"
        };
    },
    "rdTotalSum": () => {
        return {
            "type": "item",
            "column":"rdTotalSum"
        };
    },"cdTotalSum": () => {
        return {
            "type": "item",
            "column":"cdTotalSum"
        };
    },"jdTotalSum": () => {
        return {
            "type": "item",
            "column":"jdTotalSum"
        };
    },"zdTotalSum": () => {
        return {
            "type": "item",
            "column":"zdTotalSum"
        };
    },"sdTotalSum": () => {
        return {
            "type": "item",
            "column":"sdTotalSum"
        };
    },
    //子级市场价
    "dePrice": () => {
        return {
            "type": "item",
            "column":"dePrice"
        };
    }
}



const QDRules = {
    "RSum":{
        "name":"人工费单价",
        "mathFormula":"RSum*1",
    },
    "CSum":{
        "name":"材料费单价",
        "mathFormula":"CSum*1",
    },
    "JSum":{
        "name":"机械费单价",
        "mathFormula":"JSum*1",
    },
    "SSum":{
        "name":"设备费单价",
        "mathFormula":"SSum*1",
    },
    "ZSum":{
        "name":"主材费单价",
        "mathFormula":"ZSum*1",
    },
    "rTotalSum":{
        "name":"人工费合价",
        "mathFormula":"rTotalSum*1",
    },
    "cTotalSum":{
        "name":"材料费合价",
        "mathFormula":"cTotalSum*1",
    },
    "jTotalSum":{
        "name":"机械费合价",
        "mathFormula":"jTotalSum*1",
    },
    "sTotalSum":{
        "name":"设备费合价",
        "mathFormula":"sTotalSum*1",
    },
    "zTotalSum":{
        "name":"主材费合价",
        "mathFormula":"zTotalSum*1",
    },
    "marketPrice":{
        "name":"市场价",
        "mathFormula":"marketPrice*1",
    },
    "quantity":{
        "name":"市场价",
        "mathFormula":"quantity*1",
    },
    "totalNumber":{
        "name":"市场价",
        "mathFormula":"totalNumber*1",
    },
    "price":{
        "name":"市场价",
        "mathFormula":"price*1",
    },
    "baseJournalPrice":{
        "name":"市场价",
        "mathFormula":"baseJournalPrice*1",
    },
    "resQty":{
        "name":"消耗量",
        "mathFormula":"resQty*1",
    },
    "total":{
        "name":"合价",
        "mathFormula":"quantity*price",//合计数量 = 单价(子级单价*子级市场价)*工程量
    },
    "RDSum":{
        "name":"人工费单价",
        "mathFormula":"RDSum*1",
    },
    "CDSum":{
        "name":"材料费单价",
        "mathFormula":"CDSum*1",
    },
    "JDSum":{
        "name":"机械费单价",
        "mathFormula":"JDSum*1",
    },
    "SDSum":{
        "name":"设备费单价",
        "mathFormula":"SDSum*1",
    },
    "ZDSum":{
        "name":"主材费单价",
        "mathFormula":"ZDSum*1",
    },
    "rdTotalSum":{
        "name":"人工费合价",
        "mathFormula":"rdTotalSum*1",
    },
    "cdTotalSum":{
        "name":"材料费合价",
        "mathFormula":"cdTotalSum*1",
    },
    "jdTotalSum":{
        "name":"机械费合价",
        "mathFormula":"jdTotalSum*1",
    },
    "zdTotalSum":{
        "name":"主材费合价",
        "mathFormula":"zdTotalSum*1",
    },
    "sdTotalSum":{
        "name":"设备费合价",
        "mathFormula":"sdTotalSum*1",
    },
    "dePrice":{
        "name":"市场价",
        "mathFormula":"dePrice*1",
    }
}
module.exports = {QDRules,qdCalculatorBaseFn}