const RCJPriceMathHandler = require("./RCJPriceMathHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");

/**
 * 处理JSON规则，包括定额规则、人材机规则
 */
class JSONMathHandler extends RCJPriceMathHandler{

    constructor(ruleCtx, mathStr, mathJson) {
        super(ruleCtx, mathJson.math);
        this.mathJson = mathJson;
        this.parseMath = undefined;
    }

    dealShowMath(){
    }

    /**
     * 准备数据：根据不同规则准备定额、人材机、规则的取消处理等
     */
    async prepare() {
        this.setParseMath();
        await super.prepare();
        this.oriMath = this.mathItem.math;
    }

    setParseMath(){
        throw new Error("由子类实现");
    }

    analysisMath() {
        let mathItem = this.mathItem;

        let newMath = this.parseMath;
        let firstCharacter = newMath.charAt(0);

        if(ObjectUtils.isEmpty(this.mathJson.dst)){
            mathItem.parseMath = newMath;
            mathItem.math = newMath;
            this.showMath = this.conversionService.mathAfterCalculation(newMath, this.showMathDigits);
            mathItem.operator = this.mathOperator(mathItem.parseMath);
            super.analysisMath();
        }else if("RCJ".includes(this.mathJson.dst)){
            if("+-*/".includes(firstCharacter)){
                mathItem.math = this.mathJson.dst + newMath;
                this.showMath = this.mathJson.dst + this.conversionService.mathAfterCalculation(newMath, this.showMathDigits);
                super.analysisMath();
                return;
            }else{
                throw new Error("JSONDEMathHandler: 运算符错误");
            }
        }else{
            mathItem.parseMath = newMath;
            mathItem.operator = this.mathOperator(mathItem.parseMath);
            mathItem.activeRCJCodes.push(this.mathJson.dst);
            mathItem.math = `H${this.mathJson.dst} ${this.mathJson.dst} ${newMath}`;
            this.showMath = `H${this.mathJson.dst} ${this.mathJson.dst} ` + this.conversionService.mathAfterCalculation(newMath, this.showMathDigits);
        }
    }

    async activeRCJ() {
        let item = this.mathItem;
        if(ObjectUtils.isNotEmpty(item.activeRCJCodes)){

            // 此处item.activeRCJCodes应该只有一个元素
            for(let activeCode of item.activeRCJCodes){
                let rcjs = this.findActiveRCJByCode(activeCode);
                if(ObjectUtils.isEmpty(rcjs)){
                    let rcj = await this.addNewRCJ(this.rule.libraryCode, activeCode);
                    item.activeRCJs.push(rcj);
                }else{
                    if(activeCode != rcjs[0].materialCode) {
                        this.notStandardActiveRcjCodes.push([activeCode, rcjs[0].materialCode]);
                    }
                    item.activeRCJs.push(rcjs[0]);
                }
            }
        }else{
            await super.activeRCJ();
        }
    }

    mathReplaceAllDeal(str, replaceK, replaceV) {
        let reg = new RegExp(`\\b${replaceK}\\b`, "g")
        return str.replace(reg, replaceV)
    }
}

module.exports = JSONMathHandler;