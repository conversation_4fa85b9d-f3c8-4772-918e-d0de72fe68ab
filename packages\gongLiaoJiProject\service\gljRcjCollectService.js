const { Service } = require('../../../core');
const { ResponseData } = require('../utils/ResponseData');
const { ObjectUtils } = require('../utils/ObjectUtils');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const gsRcjCollectType = require('../jsonData/glj_rcj_collect_type.json');
const gsFyde = require('../jsonData/gs_fyde.json');
const { Snowflake } = require('../../../electron/utils/Snowflake');
const { ConvertUtil } = require('../utils/ConvertUtils');
const { TreeList } = require('../../../electron/model/TreeList');
const { GljConstructProjectRcj } = require('../models/GljConstructProjectRcj');
const ProjectDomain = require('../domains/ProjectDomain');
const WildcardMap = require('../core/container/WildcardMap');
const { NumberUtil } = require('../utils/NumberUtil');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const GljRcjTypeEnum = require('../enums/GljRcjTypeEnum');
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const { ParamUtils } = require('../../../core/core/lib/utils/ParamUtils');
const EE = require('../../../core/ee');
const DeTypeConstants = require('../constants/DeTypeConstants');
const ResourceConstants = require('../constants/ResourceConstants');
const UtilsPs = require("../../../core/ps");
const {dialog} = require("electron");
const {FileUtils} = require("../utils/FileUtils");
const XLSX = require("xlsx");
const CommonConstants = require('../constants/CommonConstants');
const DeCommonConstants = require("../constants/DeCommonConstants");
const gsRcjCollectScType = require('../jsonData/glj_rcj_collect_sc_type.json');
const shareCostJson = require("../jsonData/glj_shareCost.json");
const RcjTypeEnum = require("../../../electron/enum/RcjTypeEnum");
const {MainMaterialSetting} = require("../models/MainMaterialSetting");
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const xeUtils = require("xe-utils");
const ProjectTaxCalculationConstants = require("../constants/ProjectTaxCalculationConstants");

class GljRcjCollectService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取菜单栏数据
   * @param args
   * @return {Promise<*|ResponseData>}
   */
  async getRcjCellectMenuData(args) {
    let { constructId, singleId, unitId, levelType } = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let result;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
    }
    let treeList = new TreeList();
    if (ProjectTypeConstants.PROJECT_TYPE_PROJECT === levelType) {
      result = objMap.get(FunctionTypeConstants.PROJECT_MENU);
      if (ObjectUtils.isEmpty(result)) {
        let gsRcjCollectTypeCopy = ConvertUtil.deepCopy(gsRcjCollectType);
        gsRcjCollectTypeCopy.menu.splice(7, 2);
        gsRcjCollectTypeCopy.menu = gsRcjCollectTypeCopy.menu.filter(item => item.code!="14");
        result = gsRcjCollectTypeCopy.menu;
        businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap.set(FunctionTypeConstants.PROJECT_MENU, result));
      }
      treeList.rcjList = result;
      return treeList;
    }
    if (ProjectTypeConstants.PROJECT_TYPE_SINGLE === levelType) {
      result = objMap.get(FunctionTypeConstants.SINGLE_MENU);
      if (ObjectUtils.isEmpty(result)) {
        let gsRcjCollectTypeCopy = ConvertUtil.deepCopy(gsRcjCollectType);
        gsRcjCollectTypeCopy.menu.splice(7, 2);
        gsRcjCollectTypeCopy.menu = gsRcjCollectTypeCopy.menu.filter(item => item.code!="14");
        result = gsRcjCollectTypeCopy.menu;
        businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap.set(FunctionTypeConstants.SINGLE_MENU, result));
      }
      treeList.rcjList = result;
      return treeList;
    }
    //objMap.get(FunctionTypeConstants.UNIT_MENU + singleId + FunctionTypeConstants.SEPARATOR + unitId);
    if (ProjectTypeConstants.PROJECT_TYPE_UNIT === levelType) {
      result = objMap.get(FunctionTypeConstants.UNIT_MENU  + unitId);
      if (ObjectUtils.isEmpty(result)) {
        let gsRcjCollectTypeCopy = ConvertUtil.deepCopy(gsRcjCollectType);
        result = gsRcjCollectTypeCopy.menu;
        businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap.set(FunctionTypeConstants.UNIT_MENU  + unitId, result));
      }
      treeList.rcjList = result;
      return treeList;
    }
  }


  /**
   * 处理历史文件没有调价材料表菜单
   * @param constructId
   * @param unitProjects
   * @returns {Promise<void>}
   */
  async calUnitRcjCollectMenuData(constructId, unitProjects) {
    let objMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(objMap)) {
      return;
    }
    for (let unit of unitProjects) {
      let unitMenuData = objMap.get(FunctionTypeConstants.UNIT_MENU + unit.sequenceNbr);
      if (ObjectUtils.isEmpty(unitMenuData)) {
        return;
      }
      let gljRcjCollectMenu = ConvertUtil.deepCopy(gsRcjCollectType);
      let taiojiaList = unitMenuData.filter(item => item.code == 14);
      if (ObjectUtils.isEmpty(taiojiaList)) {
        let findOne = gljRcjCollectMenu.menu.find(item1 => item1.code == 14);
        if (ObjectUtils.isNotEmpty(findOne)) {
          unitMenuData.push(findOne);
        }
      }
    }
  }


  /**
   *  获取rcj分类设置
   */
  async getRcjCellectTypeData(args) {
    let gsRcjCollectTypeCopy = ConvertUtil.deepCopy(gsRcjCollectType);
    delete gsRcjCollectTypeCopy.menu;
    return gsRcjCollectTypeCopy;
  }


  /**
   *  新增、修改rcj汇总菜单
   * @param arg
   * @return {Promise<void>}
   */
  async saveRcjCellectMenuData(args) {
    let { constructId, singleId, unitId, name, code, expenseType, matchingType, supplyType } = args;
    let levelType = 3;
    this.getRcjCellectMenuData({ constructId, singleId, unitId, levelType });
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    let arrayObj = objMap.get(FunctionTypeConstants.UNIT_MENU + unitId);

    let obj = arrayObj.find(item => item.code === code);
    let id = Snowflake.nextId();
    if (ObjectUtils.isEmpty(obj)) {
      arrayObj.push({
        'code': id,
        'name': name,
        'original': 0,
        'expenseType': expenseType,
        'matchingType': matchingType,
        'supplyType': supplyType
      });
      return ResponseData.success(id);
    }
    obj.name = name;
    obj.expenseType = expenseType;
    obj.matchingType = matchingType;
    obj.supplyType = supplyType;
    return ResponseData.success(code);
  }


  /**
   *  删除rcj汇总菜单
   * @param arg
   * @return {Promise<void>}
   */
  async delRcjCellectMenuData(args) {
    let { constructId, singleId, unitId, code } = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let list = businessMap.get(FunctionTypeConstants.RCJ_COLLECT).get(FunctionTypeConstants.UNIT_MENU  + unitId);
    let num = list.findIndex(obj => obj.code === code);
    if (num != -1) {
      list.splice(num, 1);
    }
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    objMap.delete(FunctionTypeConstants.UNIT_DATA_SORT + singleId + FunctionTypeConstants.SEPARATOR + unitId + FunctionTypeConstants.SEPARATOR + code);
    return ResponseData.success();
  }


  /**
   *  菜单各个类型顺序保存
   * @param args
   * @returns {Promise<void>}
   */
  async saveRcjCellectMenuSort(args) {
    let { levelType, constructId, singleId, unitId, data, kind } = args;
    let  resultData=new Array()
    for (let arrayElement of data) {
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        let tempcol3 = arrayElement.materialCode.replace( /#\d+/g, '')
            .concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
            .concat('-')
            .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
            .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
        resultData.push(tempcol3);

        // resultData.push(arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
        //   .concat('-')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
        //     .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
        //     .concat(!ObjectUtils.isEmpty(this._getMarketPrice(arrayElement)) ? this._getMarketPrice(arrayElement) : ''));
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
        resultData.push(arrayElement.materialCode.replace( /#\d+/g, '')
            .concat(arrayElement.materialName)
            .concat(arrayElement.kind)
            .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
            .concat(arrayElement.unit)
            .concat(this._getBaseJournalPrice(arrayElement))
            // .concat(arrayElement.markSum)
        );
      }
    }

    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
      businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      objMap.set(FunctionTypeConstants.PROJECT_DATA_SORT + FunctionTypeConstants.SEPARATOR + kind, resultData);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      objMap.set(FunctionTypeConstants.SINGLE_DATA_SORT + singleId + FunctionTypeConstants.SEPARATOR + kind, resultData);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      objMap.set(FunctionTypeConstants.UNIT_DATA_SORT  + unitId + FunctionTypeConstants.SEPARATOR + kind, resultData);
    }
    return ResponseData.success();
  }


  /**
   *  菜单各个类型顺序保存 共享版
   * @param args
   * @returns {Promise<void>}
   */
  async saveRcjCellectMenuSortShare(args) {
    let { levelType, constructId,singleId, unitId, sequenceNbrArray, kind ,moveType, upOrDownLine} = args;
    let  data = sequenceNbrArray;
    let  resultData=new Array()
    let  allData=await this.getRcjCellectData(args);
    let judgmentResult =  this.judgmentContinuous(allData,data);
    if(judgmentResult=== true){
      allData=this.sortRCcjMoveData(allData, data,moveType, upOrDownLine);
    }
    if(judgmentResult  === false){
      data.length = 1
      allData=this.sortRCcjMoveData(allData, data,moveType, upOrDownLine);
    }

    for (let arrayElement of allData) {
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        resultData.push(arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
            .concat('-')
            .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
            .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
        );

        // resultData.push(arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
        //   .concat('-')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
        //     .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
        //     .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
        //     .concat(!ObjectUtils.isEmpty(this._getMarketPrice(arrayElement)) ? this._getMarketPrice(arrayElement) : ''));
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
        resultData.push(arrayElement.materialCode.replace( /#\d+/g, '')
                .concat(arrayElement.materialName)
                .concat(arrayElement.kind)
                .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
                .concat(arrayElement.unit)
                .concat(this._getBaseJournalPrice(arrayElement))
            // .concat(arrayElement.markSum)
        );
      }
    }

    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
      businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      objMap.set(FunctionTypeConstants.PROJECT_DATA_SORT + FunctionTypeConstants.SEPARATOR + kind, resultData);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      objMap.set(FunctionTypeConstants.SINGLE_DATA_SORT + singleId + FunctionTypeConstants.SEPARATOR + kind, resultData);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      objMap.set(FunctionTypeConstants.UNIT_DATA_SORT  + unitId + FunctionTypeConstants.SEPARATOR + kind, resultData);
    }
    return ResponseData.success();
  }


  /**
   *  判定连续
   */
  judgmentContinuous(allData, data) {
    let numArr = new Array();
    let dataMap=data.map(obj=>obj.sequenceNbr);
    for (let i = 0; i < allData.length; i++) {
      if( dataMap.includes(allData[i].sequenceNbr)){
        numArr.push(i);
      }
    }
    if (numArr.length < 2) {
      return true;
    }
    // 遍历数组，从第二个元素开始（索引为1）
    for (let i = 1; i < numArr.length; i++) {
      // 如果当前元素与前一个元素的差不是1，则不连续
      if (numArr[i] - numArr[i - 1] !== 1) {
        return false;
      }
    }
    return true;
  }

  /**
   * 排序
   * @param allData
   * @param data
   */
  sortRCcjMoveData(allData, data, moveType, upOrDownSequenceNbr) {
    let numArr = [];
    let midData = [];
    let filterData = [];
    let dataMap = data.map(obj => obj.sequenceNbr);

    for (let i = 0; i < allData.length; i++) {
      if (dataMap.includes(allData[i].sequenceNbr)) {
        numArr.push(i);
      }
    }
    allData.forEach(item => {
      if (dataMap.includes(item.sequenceNbr)) {
        midData.push(item);
      } else {
        filterData.push(item);
      }
    });

    let result = [];
    let targetIndex = -1;

    // 找到 upOrDownSequenceNbr 对应的 index
    if (upOrDownSequenceNbr !== null && upOrDownSequenceNbr !== undefined) {
      for (let i = 0; i < filterData.length; i++) {
        if (filterData[i].sequenceNbr === upOrDownSequenceNbr) {
          targetIndex = i;
          break;
        }
      }
    }

    if (moveType === "up") {
      let step;
      if (targetIndex === -1) {
        // 如果没找到 upOrDownSequenceNbr，则移动到最前面
        step = 0;
      } else {
        step = targetIndex;
      }

      let frontData = filterData.slice(0, step);
      let backData = filterData.slice(step, filterData.length);
      result = frontData.concat(midData).concat(backData);
    }

    if (moveType === "down") {
      let step;
      if (targetIndex === -1) {
        // 如果没找到 upOrDownSequenceNbr，则移动到最后面
        step = filterData.length;
      } else {
        step = targetIndex + 1;
      }

      let frontData = filterData.slice(0, step);
      let backData = filterData.slice(step, filterData.length);
      result = frontData.concat(midData).concat(backData);
    }

    return result;
  }




  /**
   * 导出局部rcj汇总
   * @param args
   */
  async exportPartRcjCellectData(args) {
    let {constructId, unitId, rowList} = args;

    // 存放局部汇总文件的路径
    const exportDir = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\glj\\局部汇总';
    FileUtils.ensurePathExists(exportDir)
    let count = FileUtils.countDirectories(exportDir);
    let options = {
      title: '保存文件',
      defaultPath: exportDir + '\\局部人材机汇总' + count, // 默认保存路径或者模版获取路径
      filters: [
        {name: '云算房', extensions: ['xlsx']} // 可选的文件类型
      ]
    };
    //计税方式
    let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let filePath = dialog.showSaveDialogSync(null, options);
    if (filePath) {
      if (!filePath.endsWith('xlsx')) {
        filePath += 'xlsx';
      }

      let supplyType = ConvertUtil.deepCopy(gsRcjCollectType).supplyType;
      let i = 1;
      // 提取需要的字段
      let filteredData = rowList.map(item => ({
        '序号': i++,
        '名称': item.materialName,
        '单位': item.unit,
        '数量': item.totalNumber,
        '含税基期价(元)': item.baseJournalTaxPrice,
        '含税市场价(元)': item.marketTaxPrice,
        '市场价合计(元)': item.totalTax,
      }));
      i = 1;
      if (taxMethod === 1) {
        filteredData = rowList.map(item => ({
          '序号': i++,
          '名称': item.materialName,
          '单位': item.unit,
          '数量': item.totalNumber,
          '不含税基期价(元)': item.baseJournalPrice,
          '不含税市场价(元)': item.marketPrice,
          '市场价合计(元)': item.total,
        }));
      }

      // 转换数据为工作表
      const worksheet = XLSX.utils.json_to_sheet(filteredData);
      // 创建工作簿并添加工作表
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
      // 写入Excel文件
      XLSX.writeFile(workbook, filePath);
      return ResponseData.success(filePath);
    }
  }

  /**
   * 局部rcj汇总
   * @param args
   * @returns {Promise<void>}
   */
  async getPartRcjCellectData(args) {
    let partDeList = args.deLists
    partDeList.push(...args.csxmDeList);
    let partDeRowIds = partDeList.filter(item=> item.type !== '0' && item.isSelected === 1).map(item => item.deRowId)
    let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(args.constructId, args.unitId);
    let params = {
      constructId: args.constructId,
      singleId: unit.parentId,
      unitId: args.unitId,
      kind: 0,
      levelType: 3,
      partDeRowIds: partDeRowIds,
    }
    let rcjCollect = await this.getRcjCellectData(params);
    rcjCollect = rcjCollect?rcjCollect.filter(item => item.isDeCompensation !== 1):[];
    return rcjCollect;
  }


  /**
   * 工程/单位 rcj汇总查询
   * @param constructId
   * @param singleId
   * @param unitId
   * @param kind  人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土 7 主要材料.设备
   */
  async getRcjCellectData(args) {
    let { levelType, kind, constructId, singleId, unitId, sort , isShowAnnotations, partDeRowIds} = args;
    let constructRcjArray = new Array();
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjList = null;
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      let idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3 && item.scopeFlag === true  ).map(item=>item.sequenceNbr);
      if (ObjectUtils.isNotEmpty(args.moveType)) {
        idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3).map(item=>item.sequenceNbr);
      }
      if(ObjectUtils.isEmpty(idArray)){
        return   null  ;
      }
      rcjList=ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item=>idArray.includes(item.unitId))
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getSingleUnitAll(constructId, singleId);
      let idArray = unitList.map(item => item.sequenceNbr)
      if(ObjectUtils.isEmpty(idArray)){
        return null  ;
      }
      rcjList=ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item=>idArray.includes(item.unitId))
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
      rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    }
    if (ObjectUtils.isNotEmpty(rcjList)) {
      if (partDeRowIds !== undefined && (ObjectUtils.isNotEmpty(partDeRowIds) || partDeRowIds.length >= 0)) {
        rcjList = rcjList.filter(item => partDeRowIds.includes(item.deRowId));
      }
      for (let constructProjectRcj of rcjList) {
        if (constructProjectRcj.totalNumber !== 0) {
          let constructProjectRcj1 = new GljConstructProjectRcj();
          ConvertUtil.setDstBySrc(constructProjectRcj, constructProjectRcj1);
          constructProjectRcj1.standardId = constructProjectRcj.rcjId;
          constructProjectRcj1.kindSc = constructProjectRcj.kindSc;
          constructProjectRcj1.transferFactor = constructProjectRcj.transferFactor;
          constructProjectRcj1.scCount = constructProjectRcj.scCount;
          constructProjectRcj1.remark = constructProjectRcj.remark;
          constructProjectRcj1.taxRateInit = constructProjectRcj.taxRateInit;
          constructRcjArray.push(constructProjectRcj1);
        }
      }
    } else {
      return null;
    }
    // 二次解析
    let ts1 = rcjList.filter(i => i.markSum === 1);
    if (!ObjectUtils.isEmpty(ts1)) {
      for (let t of ts1) {
        // let ts2 = rcjMingxi.filter(i => i.rcjId === t.sequenceNbr);
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          for (let t1 of ts2) {
            if ( ObjectUtil.isNotEmpty(t1.totalNumber) && t1.totalNumber !== 0) {
              let constructProjectRcj = new GljConstructProjectRcj();
              ConvertUtil.setDstBySrc(t1, constructProjectRcj);
              constructProjectRcj.standardId = t.rcjId;
              constructProjectRcj.kindSc = t1.kindSc;
              constructProjectRcj.transferFactor = t1.transferFactor;
              constructProjectRcj.scCount = t1.scCount;
              constructProjectRcj.remark = t1.remark;
              constructProjectRcj.taxRateInit = t1.taxRateInit;
              constructRcjArray.push(constructProjectRcj);
            }
          }
        }
      }
    }
    //
    constructRcjArray.forEach(item => {
      if (item.materialCode in gsFyde) {
        item.edit = 1;
      }
    });

    //分组处理并重新封装
    let array1 = new Array();
    if(ObjectUtils.isNotEmpty(args.kindSc) && args.kindSc){
      this.groupHandleKindSc(constructRcjArray, array1, levelType, constructId);
    } else {
      await  this.groupHandle(constructRcjArray, array1, levelType,constructId);
    }
    //处理 查询 主要材料 设备表逻辑
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT && kind == 7) {
      return null;
    }
    //标记主材 设备表
    await this.majorEquipmentHandle(array1, constructId, singleId, unitId);
    //处理 查询 主要材料 设备表逻辑
    if (kind === 7) {
      //主要材料设备查询
      array1 = array1.filter(i => i.mainMaterial == 1);
    }
    // 人材机汇总- 人材机来源
    // this.constructProjectRcjIsCostDe(constructId, singleId, unitId, array1);
    //处理批注
    this.rcjAnnotations(singleId, array1, levelType, isShowAnnotations);
    //字段值规整
    this.columnValueInit(array1);
    //处理市场价 定额价不一致时 颜色问题
    this.constructProjectRcjColour(array1);
    this.coloring(array1, businessMap, unitId, levelType);
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      array1.forEach(item=>{
        this._materialDispose(item);
      });
    }
    let  result  = array1;
    if (kind != 0 && kind != 7) {
      if (kind == 1 || kind == 3 || kind == 4 || kind == 5 || kind == 6) {
        result= array1.filter(i => i.kind == kind);
      } else if (kind == 2) {
        result=array1.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);
      } else if (kind == 8) {
        result= array1.filter(i => i.ifProvisionalEstimate == 1);
      } else if (kind == 9) {
        result=  array1.filter(i => i.ifDonorMaterial == 1);
      } else if (kind == 10) {
        result= null;
      } else if (kind == 14) {
        let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        result = array1.filter(i => !ObjectUtils.isEmpty(i.standardId) && i.baseJournalPrice != i.marketPrice);
        if (taxCalculationMethod == ProjectTaxCalculationConstants.TAX_MODE_0) {
          result = array1.filter(i => !ObjectUtils.isEmpty(i.standardId) && i.baseJournalTaxPrice != i.marketTaxPrice);
        }
      } else if (kind > 10) {
        //根据类型筛选返回
        let arrayObj;
        let rcjMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
        if (ObjectUtils.isEmpty(rcjMap)) {
          return null;
        }
        arrayObj = rcjMap.get(FunctionTypeConstants.UNIT_MENU  + unitId);
        if (ObjectUtils.isEmpty(arrayObj)) {
          return null;
        }
        let obj = arrayObj.find(item => item.code === kind);
        if (ObjectUtils.isEmpty(obj)) {
          return null;
        }
        let mergedArray = obj.expenseType.concat(obj.matchingType);
        //过滤expenseType
        let ts7 = result.filter(item => mergedArray.includes(String(item.kind)));
        //过滤supplyType
        let ts8 = ts7.filter(item => obj.supplyType.includes(String(item.ifDonorMaterial)));
        result = ts8;
      } else {
        result= null;
      }
    }

    //自定义排序处理
    let result2 = [];
    if(ObjectUtils.isNotEmpty(result)){
      result2 = result.filter(item=>item.isDeCompensation !== CommonConstants.COMMON_YES);
    }
    result2 = await this.customCollectSort(result2, levelType, businessMap, kind, singleId, unitId);
    let result3 = this.rcjCollectSort(result2,sort);
    // 保存排序
    if (ObjectUtils.isNotEmpty(sort)) {
      await this.saveRcjCellectMenuSort({levelType, constructId, singleId, unitId, data: result3, kind });
    }
    return result3;

  }



  rcjCollectSort(list,sort){
    if(ObjectUtils.isEmpty(sort)|| ObjectUtils.isEmpty(list)){
      return  list ;
    }
    let  ll = [];
    let before=list.filter(item=>{
      if(!((item.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || item.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) && item.markSum === RcjCommonConstants.MARKSUM_JX)){
        return   item;
      }
    });
    let after=list.filter(item=>{
      if((item.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || item.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) &&item.markSum === RcjCommonConstants.MARKSUM_JX){
        return   item;
      }
    });

    before.sort((a, b) => {
      return  this.sortDetail(a,b,sort);
    });
    after.sort((a, b) => {
      return  this.sortDetail(a,b,sort);
    });

    return  ll.concat(before).concat(after);
  }

  sortDetail2 (a,b,sort){
    let   aMap=new Map( Object.entries(a) );
    let   bMap=new Map( Object.entries(b) );

    if(sort.order === RcjCommonConstants.ORDER_ASC){
      if(typeof aMap.get(sort.field) ==='string'){
        return aMap.get(sort.field).localeCompare(bMap.get(sort.field)) ;
      }
      if(typeof aMap.get(sort.field) === "number"){
        return aMap.get(sort.field) - bMap.get(sort.field);
      }

    }
    if(sort.order === RcjCommonConstants.ORDER_DESC){
      if(typeof aMap.get(sort.field) ==='string'){
        let starStr =bMap.get(sort.field);
        if(ObjectUtils.isEmpty(bMap.get(sort.field))){
          starStr ='';
        }
        return starStr.localeCompare(aMap.get(sort.field)) ;
      }
      if(typeof aMap.get(sort.field) === "number"){
        return bMap.get(sort.field) - aMap.get(sort.field);
      }

    }

  }

  sortDetail(a, b, sort) {
    let aMap = new Map(Object.entries(a));
    let bMap = new Map(Object.entries(b));
    const aValue = aMap.get(sort.field);
    const bValue = bMap.get(sort.field);
    // 处理升序排序
    if (sort.order === RcjCommonConstants.ORDER_ASC) {
      if (aValue === null || aValue === undefined || aValue === '') {
        return -1; // a为空，排在前面
      }
      if (bValue === null || bValue === undefined || bValue === '') {
        return 1; // b为空，排在后面
      }
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return aValue.localeCompare(bValue);
      }
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return aValue - bValue;
      }
    }
    // 处理降序排序
    if (sort.order === RcjCommonConstants.ORDER_DESC) {
      if (aValue === null || aValue === undefined || aValue === '') {
        return 1; // a为空，排在后面
      }
      if (bValue === null || bValue === undefined || bValue === '') {
        return -1; // b为空，排在前面
      }
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return bValue.localeCompare(aValue);
      }
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return bValue - aValue;
      }
    }
    // 如果两个值都相同，则返回0
    return 0;
  }


  /**
   * 处理编码#
   * @param constructProjectRcj 人材机对象
   */
  _materialDispose(constructProjectRcj){

    if (constructProjectRcj.hasOwnProperty("materialCode")){
      if (constructProjectRcj.materialCode.includes("#")){
        constructProjectRcj.materialCodeUnit = constructProjectRcj.materialCode;
        constructProjectRcj.materialCode = constructProjectRcj.materialCode.
        substring(0,constructProjectRcj.materialCode.lastIndexOf("#"));
      }
    }
  }


  coloring(result, businessMap, unitId, levelType) {
    //上色
    let rcjMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isNotEmpty(rcjMap)) {
      result.forEach((value) => {
        let colorMap = rcjMap.get(FunctionTypeConstants.UNIT_COLOR + FunctionTypeConstants.SEPARATOR + value.materialCode);
        if (ObjectUtils.isNotEmpty(colorMap) && colorMap.size !== 0) {
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            let   iterator=colorMap.entries();
            for(let i =0 ;  i<colorMap.size ;i++){
              let   currentElement =iterator.next();
              let sequnceNbr = currentElement .value[0];
              let color = currentElement .value[1];
              if(value.unitIdSet.has(sequnceNbr) ){
                value.rcjColor = color;
                break;
              }
              // if(value.unitIdSet.has(sequnceNbr) ){
              //   break  ;
              // }
            }
          }
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            let   iterator=colorMap.entries();
            for(let i =0 ;  i<colorMap.size ;i++){
              let   currentElement =iterator.next();
              let sequnceNbr = currentElement .value[0];
              let color = currentElement .value[1];
              if(value.unitIdSet.has(sequnceNbr) ){
                value.rcjColor = color;
                break;
              }
            }
          }
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            let color = colorMap.get(unitId);
            value.rcjColor = color;
          }
        }
      });
    }
    return result;
  }


  /**
   * 验证能否创建分类汇总
   * @param args
   * @returns {Promise<boolean|null>}
   */
  async verifyRcjCellectMenu(args) {
    let { constructId, unitId, matchingType , expenseType ,supplyType} = args;
    let constructRcjArray = new Array();
    let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let   rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    if (ObjectUtils.isNotEmpty(rcjList)) {
      for (let constructProjectRcj of rcjList) {
        if (constructProjectRcj.totalNumber !== 0) {
          let constructProjectRcj1 = new GljConstructProjectRcj();
          ConvertUtil.setDstBySrc(constructProjectRcj, constructProjectRcj1);
          constructProjectRcj1.standardId = constructProjectRcj.rcjId;
          constructRcjArray.push(constructProjectRcj1);
        }
      }
    } else {
      return null;
    }
    // 二次解析
    let ts1 = rcjList.filter(i => i.markSum === 1);
    if (!ObjectUtils.isEmpty(ts1)) {
      for (let t of ts1) {
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          for (let t1 of ts2) {
            if ( ObjectUtil.isNotEmpty(t1.totalNumber) && t1.totalNumber !== 0) {
              let constructProjectRcj = new GljConstructProjectRcj();
              ConvertUtil.setDstBySrc(t1, constructProjectRcj);
              constructProjectRcj.standardId = t.rcjId;
              constructRcjArray.push(constructProjectRcj);
            }
          }
        }
      }
    }
    constructRcjArray.forEach(item => {
      if (item.materialCode in gsFyde) {
        item.edit = 1;
      }
    });
    //分组处理并重新封装
    let array1 = new Array();
    await this.groupHandle(constructRcjArray, array1,  ProjectTypeConstants.PROJECT_TYPE_UNIT,constructId);
    let mergedArray = expenseType.concat(matchingType);
    //过滤expenseType
    let ts7 = array1.filter(item => mergedArray.includes(String(item.kind)));
    //过滤supplyType
    let ts8 = ts7.filter(item => supplyType.includes(String(item.ifDonorMaterial)));
    if (ObjectUtils.isNotEmpty(ts8)) {
      return true;
    }
    return false;
  }


  /**
   * 分组处理并重新封装
   * @param constructRcjArray
   * @param array1
   * @returns {Promise<void>}
   */
  async groupHandle(constructRcjArray, array1, levelType,constructId) {
    let  taxMethod=ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId).RCJ_COLLECT;
    //拼接相同材料
    if (!ObjectUtils.isEmpty(constructRcjArray)) {
      for (let arrayElement of constructRcjArray) {
        if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
          arrayElement.tempcol = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
              .concat('-')
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
              .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.marketPrice) ? arrayElement.marketPrice : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.taxRate) ? arrayElement.taxRate : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.marketTaxPrice) ? arrayElement.marketTaxPrice : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.ifProvisionalEstimate) ? arrayElement.ifProvisionalEstimate : '')
          arrayElement.tempcol2 = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
              .concat('-')
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
              .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
          ;
          arrayElement.tempcol3 = arrayElement.materialCode.replace( /#\d+/g, '')
              .concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
              .concat('-')
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
              .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')

          // .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
          // .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
          // .concat(!ObjectUtils.isEmpty(this._getMarketPrice(arrayElement)) ? this._getMarketPrice(arrayElement) : '');
        }
        if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
          arrayElement.tempcol = arrayElement.materialCode
              .concat(arrayElement.materialName)
              .concat(arrayElement.kind)
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(arrayElement.unit)
              .concat(this._getBaseJournalPrice(arrayElement));
          //.concat(arrayElement.markSum);
          arrayElement.tempcol3 = arrayElement.materialCode.replace( /#\d+/g, '')
              .concat(arrayElement.materialName)
              .concat(arrayElement.kind)
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(arrayElement.unit)
              .concat(this._getBaseJournalPrice(arrayElement));
          //.concat(arrayElement.markSum);
        }

      }
    } else {
      return null;
    }

    //分组
    const grouped = constructRcjArray.reduce((accumulator, currentValue) => {
      // 将分组作为对象的 key，相同分组的项放入同一个数组
      (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
      return accumulator;
    }, {});
    let grouped2;
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      grouped2 = constructRcjArray.reduce((accumulator, currentValue) => {
        // 如果当前tempcol的key还不存在，则初始化为0
        accumulator[currentValue.tempcol2] = (accumulator[currentValue.tempcol2] || 0) + 1;
        return accumulator;
      }, {});
    }

    //循环分组之后的人材机
    for (let group in grouped) {
      if (grouped.hasOwnProperty(group)) {
        let groupedElement = grouped[group][0];
        let number = 0;
        let donorMaterialNumber = 0;
        let updateFalg =0 ;
        let lock = 0;
        let set = new Set();
        let unitIdSet = new Set();
        let deInfoSet = new Set();
        let scCount = 0;
        let unitIds =[] ;
        grouped[group].forEach(item => {
          number = NumberUtil.add( number, NumberUtil.numberScale(item.totalNumber, precision.totalNumber) );
          let donorMaterialNumberTmp =item.totalNumber;
          if(item.updateFalg ===1 ){
            updateFalg = item.updateFalg;
            donorMaterialNumberTmp =ObjectUtils.isEmpty(item.donorMaterialNumber)? 0:item.donorMaterialNumber;
          }
          //  单位工程内，只取一个，单项与项目下，则进行累加
          let unitId=unitIds.find(item2 => item.unitId === item2);
          if(ObjectUtils.isEmpty(unitId)){
            donorMaterialNumber = NumberUtil.add(donorMaterialNumber,  NumberUtil.numberScale(donorMaterialNumberTmp, precision.donorMaterialNumber));
            unitIds.push(groupedElement.unitId );
          }
          if(item.ifLockStandardPrice===1){
            lock=1;
          }
          //number2 = NumberUtil.add(number2, ObjectUtils.isEmpty(item.donorMaterialNumber)? 0:item.donorMaterialNumber);
          if (!ObjectUtils.isEmpty(item.sourcePrice)) {
            set.add(item.sourcePrice);
          }
          if (ObjectUtils.isNotEmpty(item.unitId)) {
            unitIdSet.add(item.unitId);
          }
          if (ObjectUtils.isNotEmpty(item.deId)) {
            deInfoSet.add({deId: item.deId});
          }

          if ((ObjectUtils.isEmpty(groupedElement.kindSc) || groupedElement.kindSc === "空") && ObjectUtils.isNotEmpty(item.kindSc) && item.kindSc !== "空") {
            groupedElement.kindSc = item.kindSc;
            groupedElement.transferFactor = item.transferFactor;
          }

          if (ObjectUtils.isNotEmpty(groupedElement.kindSc) && groupedElement.kindSc !== "空" &&
              ObjectUtils.isNotEmpty(item.kindSc) && item.kindSc !== "空" && item.kindSc === groupedElement.kindSc) {
            scCount = NumberUtil.add(scCount, item.scCount);
          }
        });
        groupedElement.totalNumber = number;
        groupedElement.scCount = NumberUtil.numberScale(scCount, precision.totalNumber);

        if(  updateFalg === 0  && groupedElement.ifDonorMaterial !=RcjCommonConstants.DEFAULT_IFDONORMATERIAL){
          groupedElement.donorMaterialNumber = number;
        }else{
            groupedElement.donorMaterialNumber = donorMaterialNumber;
        }
        // if(  (ObjectUtils.isEmpty(groupedElement.updateFalg)? 0 :groupedElement.updateFalg )=== 1  && groupedElement.ifDonorMaterial ===1){
        //   groupedElement.donorMaterialNumber = 0;
        // }
        groupedElement.rcjDetailsDTOs = null;
        groupedElement.type = this.getRcjTypeEnumDescByCode(groupedElement.kind);
        groupedElement.total = NumberUtil.multiplyJd(number, groupedElement.marketPrice, precision.totalNumber, precision.marketPrice, null)
        groupedElement.totalTax = NumberUtil.multiplyJd(number, groupedElement.marketTaxPrice, precision.totalNumber, precision.marketTaxPrice, null)
        groupedElement.priceDifferenc = NumberUtil.subtractJd(this._getMarketPrice(groupedElement), this._getBaseJournalPrice(groupedElement), precision.marketPrice, precision.baseJournalPrice, null);
        groupedElement.priceDifferencSum = NumberUtil.multiplyJd(groupedElement.priceDifferenc, groupedElement.totalNumber, precision.jc, precision.totalNumber, null);
        groupedElement.ifLockStandardPrice =lock;
        groupedElement.unitIdSet =unitIdSet;
        groupedElement.deInfoSet =deInfoSet;
        if (ObjectUtils.isNotEmpty(grouped2)) {
          if (grouped2[groupedElement.tempcol2] > grouped[group].length) {
            groupedElement.marketPriceDiff = RcjCommonConstants.MARKETPRICEDIFF;
          }
        }
        if (set.size > 1) {
          groupedElement.sourcePrice = null;
        }
        array1.push(groupedElement);
      }
    }
  }

  /**
   * 分组处理并重新封装
   * @param constructRcjArray
   * @param array1
   * @returns {Promise<void>}
   */
  async groupHandleKindSc(constructRcjArray, array1, levelType, constructId) {
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId).RCJ_COLLECT;
    //拼接相同材料
    if (!ObjectUtils.isEmpty(constructRcjArray)) {
      for (let arrayElement of constructRcjArray) {
        if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
          arrayElement.tempcol = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
            .concat('-')
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
              .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.marketPrice) ? arrayElement.marketPrice : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.taxRate) ? arrayElement.taxRate : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.marketTaxPrice) ? arrayElement.marketTaxPrice : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kindSc) ? arrayElement.kindSc : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.ifProvisionalEstimate) ? arrayElement.ifProvisionalEstimate : '')
          ;
          arrayElement.tempcol2 = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
            .concat('-')
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
              .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
          ;
          arrayElement.tempcol3 = arrayElement.materialCode.replace( /#\d+/g, '')
              .concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
              .concat('-')
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
              .concat(!ObjectUtils.isEmpty(this._getBaseJournalPrice(arrayElement)) ? this._getBaseJournalPrice(arrayElement) : '')
          // .concat(!ObjectUtils.isEmpty(arrayElement.markSum) ? arrayElement.markSum : '')
          // .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial) ? arrayElement.ifDonorMaterial : '')
          // .concat(!ObjectUtils.isEmpty(this._getMarketPrice(arrayElement)) ? this._getMarketPrice(arrayElement) : '');
        }
        if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
          arrayElement.tempcol = arrayElement.materialCode
              .concat(arrayElement.materialName)
              .concat(arrayElement.kind)
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(arrayElement.unit)
              .concat(this._getBaseJournalPrice(arrayElement))
          //.concat(arrayElement.markSum);
          arrayElement.tempcol3 = arrayElement.materialCode.replace( /#\d+/g, '')
              .concat(arrayElement.materialName)
              .concat(arrayElement.kind)
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(arrayElement.unit)
              .concat(this._getBaseJournalPrice(arrayElement))
          //.concat(arrayElement.markSum);
        }

      }
    } else {
      return null;
    }

    //分组
    const grouped = constructRcjArray.reduce((accumulator, currentValue) => {
      // 将分组作为对象的 key，相同分组的项放入同一个数组
      (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
      return accumulator;
    }, {});
    let grouped2;
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      grouped2 = constructRcjArray.reduce((accumulator, currentValue) => {
        // 如果当前tempcol的key还不存在，则初始化为0
        accumulator[currentValue.tempcol2] = (accumulator[currentValue.tempcol2] || 0) + 1;
        return accumulator;
      }, {});
    }

    //循环分组之后的人材机
    for (let group in grouped) {
      if (grouped.hasOwnProperty(group)) {
        let groupedElement = grouped[group][0];
        let number = 0;
        let number2 = 0;
        let lock = 0;
        let set = new Set();
        let unitIdSet = new Set();
        let scCount = 0;
        grouped[group].forEach(item => {
          number = NumberUtil.add(number, item.totalNumber);
          if(item.ifLockStandardPrice===1){
            lock=1;
          }
          //number2 = NumberUtil.add(number2, ObjectUtils.isEmpty(item.donorMaterialNumber)? 0:item.donorMaterialNumber);
          if (!ObjectUtils.isEmpty(item.sourcePrice)) {
            set.add(item.sourcePrice);
          }
          if (ObjectUtils.isNotEmpty(item.unitId)) {
            unitIdSet.add(item.unitId);
          }

          if ((ObjectUtils.isEmpty(groupedElement.kindSc) || groupedElement.kindSc === "空") && ObjectUtils.isNotEmpty(item.kindSc) && item.kindSc !== "空") {
            groupedElement.kindSc = item.kindSc;
            groupedElement.transferFactor = item.transferFactor;
          }

          if (ObjectUtils.isNotEmpty(groupedElement.kindSc) && groupedElement.kindSc !== "空" &&
              ObjectUtils.isNotEmpty(item.kindSc) && item.kindSc !== "空" && item.kindSc === groupedElement.kindSc) {
            scCount = NumberUtil.add(scCount, item.scCount);
          }
        });
        groupedElement.totalNumber = NumberUtil.numberScale(number, precision.totalNumber);
        groupedElement.scCount = NumberUtil.numberScale(scCount, precision.totalNumber);

        if(  (ObjectUtils.isEmpty(groupedElement.updateFalg)? 0 :groupedElement.updateFalg )=== 0  && groupedElement.ifDonorMaterial ==1){
          groupedElement.donorMaterialNumber = NumberUtil.numberScale(number, precision.donorMaterialNumber);
        }
        // if(  (ObjectUtils.isEmpty(groupedElement.updateFalg)? 0 :groupedElement.updateFalg )=== 1  && groupedElement.ifDonorMaterial ===1){
        //   groupedElement.donorMaterialNumber = 0;
        // }
        groupedElement.rcjDetailsDTOs = null;
        groupedElement.type = this.getRcjTypeEnumDescByCode(groupedElement.kind);
        groupedElement.total = NumberUtil.multiplyJd(number, groupedElement.marketPrice, precision.totalNumber, precision.marketPrice, null);
        groupedElement.totalTax = NumberUtil.multiplyJd(number, groupedElement.marketTaxPrice, precision.totalNumber, precision.marketTaxPrice, null)
        groupedElement.priceDifferenc = NumberUtil.subtractJd(this._getMarketPrice(groupedElement), this._getBaseJournalPrice(groupedElement), precision.marketPrice, precision.baseJournalPrice, null);
        groupedElement.priceDifferencSum = NumberUtil.multiplyJd(groupedElement.priceDifferenc, groupedElement.totalNumber, precision.jc, precision.totalNumber, null);
        groupedElement.ifLockStandardPrice =lock;
        groupedElement.unitIdSet =unitIdSet;
        if (ObjectUtils.isNotEmpty(grouped2)) {
          if (grouped2[groupedElement.tempcol2] > grouped[group].length) {
            groupedElement.marketPriceDiff = RcjCommonConstants.MARKETPRICEDIFF;
          }
        }
        if (set.size > 1) {
          groupedElement.sourcePrice = null;
        }
        array1.push(groupedElement);
      }
    }
  }

  /**
   * 根据code获取RcjTypeEnum的desc
   * @param rcjTypeEnumCode
   * @return {RcjTypeEnum.desc|null}
   */
  getRcjTypeEnumDescByCode(rcjTypeEnumCode) {
    for (let enumKey in GljRcjTypeEnum) {
      if (GljRcjTypeEnum[enumKey].code == rcjTypeEnumCode) {
        return GljRcjTypeEnum[enumKey].desc;
      }
    }
    return null;
  }

  /**
   * 处理 查询 主要材料 设备表逻辑
   * @param array1
   * @returns {Promise<any[]|*>}
   */
  async majorEquipmentHandle(array1, constructId, singleId, unitId) {
    let array = new Array();

    if (ObjectUtils.isNotEmpty(unitId)) {
      let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
      let objMap = businessMap.get(FunctionTypeConstants.MAIN_MATERIAL_SETTING);
      let mainMaterialSetting = objMap.get(unitId);
      if (ObjectUtils.isEmpty(mainMaterialSetting)) {
        return null;
      }

      if (mainMaterialSetting.type == 0) {
        //主材list
        let zhucaiList = [];
        //设备list
        let shebeiList = [];
        //自动设置
        switch (mainMaterialSetting.pattern) {
          case 1:
            //1:代表 方式一 合计数量前多少
            //主材list
            zhucaiList = array1.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);

            //过滤和合计数量 前 多少
            if (!ObjectUtils.isEmpty(zhucaiList)) {
              zhucaiList.sort((a, b) => b.totalNumber - a.totalNumber);
              if (zhucaiList.length <= mainMaterialSetting.proportion) {
                array = zhucaiList;
              } else {
                array = zhucaiList.slice(0, mainMaterialSetting.proportion);
              }
            }
            //设备list
            shebeiList = array1.filter(i => i.kind == 4);
            //过滤和合计数量 前 多少
            if (!ObjectUtils.isEmpty(shebeiList)) {
              shebeiList.sort((a, b) => b.totalNumber - a.totalNumber);
              if (shebeiList.length <= mainMaterialSetting.proportion) {
                array = array.concat(shebeiList);
              } else {
                array = array.concat(shebeiList.slice(0, mainMaterialSetting.proportion));
              }
            }

            break;
          case 2:
            //2:代表 方式二 单价前多少
            //主材list
            zhucaiList = array1.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);

            //过滤单价前 多少
            if (!ObjectUtils.isEmpty(zhucaiList)) {
              zhucaiList.sort((a, b) => b.marketPrice - a.marketPrice);
              if (zhucaiList.length <= mainMaterialSetting.proportion) {
                array = zhucaiList;
              } else {
                array = zhucaiList.slice(0, mainMaterialSetting.proportion);
              }
            }

            //设备list
            shebeiList = array1.filter(i => i.kind == 4);

            //过滤单价前 多少
            if (!ObjectUtils.isEmpty(shebeiList)) {
              shebeiList.sort((a, b) => b.marketPrice - a.marketPrice);
              if (shebeiList.length <= mainMaterialSetting.proportion) {
                array = array.concat(shebeiList);
              } else {
                array = array.concat(shebeiList.slice(0, mainMaterialSetting.proportion));
              }
            }

            break;
          case 3:
            //3:代表 方式三 取所有主材和设备
            //主材list
            zhucaiList = array1.filter(i => i.kind == 4 || i.kind == 5);
            if (!ObjectUtils.isEmpty(zhucaiList)) {
              array = zhucaiList;
            }

            // //设备list
            // shebeiList = array1.filter(i => i.kind == 4);

            // if (!ObjectUtils.isEmpty(shebeiList)) {
            //   array = array.concat(shebeiList);
            // }

            break;
        }
      } else if (mainMaterialSetting.type == 1) {

        let materialCodeList = mainMaterialSetting.materialCodeList;
        //手动设置
        if (!ObjectUtils.isEmpty(materialCodeList)) {
          //手动设置 选择的材料编码
          for (let rcjListElement of array1) {
            if (materialCodeList.includes(rcjListElement.materialCode)) {
              array.push(rcjListElement);
            }
          }
        }
      }

      if (!ObjectUtils.isEmpty(array)) {
        for (let array1Element of array) {
          //代表是主要材料 list
          array1Element.mainMaterial = 1;
          if (ObjectUtils.isEmpty(mainMaterialSetting.cancelOutputMaterialCodeList)) {
            //代表 输出报表 默认输出
            array1Element.output = 1;
          } else {
            let cancelOutputMaterialCodeList = mainMaterialSetting.cancelOutputMaterialCodeList;
            if (cancelOutputMaterialCodeList.includes(array1Element.materialCode)) {
              //代表 输出报表 不输出
              array1Element.output = 0;
            } else {
              array1Element.output = 1;
            }
          }
        }
      }

      // let ts7 = array1.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);
      // if (!ObjectUtils.isEmpty(ts7)) {
      //   ts7.sort((a, b) => b.totalNumber - a.totalNumber);
      //   if (ts7.length <= 50) {
      //     array = ts7;
      //   } else {
      //     array = ts7.slice(0, 50);
      //   }
      // }
      // let ts8 = array1.filter(i => i.kind == 4);
      // if (!ObjectUtils.isEmpty(ts8)) {
      //   ts8.sort((a, b) => b.totalNumber - a.totalNumber);
      //   if (ts8.length <= 50) {
      //     array = array.concat(ts8);
      //   } else {
      //     array = array.concat(ts8.slice(0, 50));
      //   }
      // }

    }

    array = array.filter(i=>i.mainMaterial ==1);
    return array;
  }

  /**
   * 人材机是汇总排序
   * @param array1
   * @returns {Promise<*|null>}
   */
  async customCollectSort(array1, levelType, businessMap, kind, singleId, unitId) {
    // array1  =this.kindFilter(array1,kind);
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    let result= null;
    if(ObjectUtils.isEmpty(array1)){
      return   result;
    }
    //机械费 组合
    array1.sort((a, b) => a.kind - b.kind || a.materialCode.localeCompare(b.materialCode));
    let  resultMemory ;
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      resultMemory = objMap.get(FunctionTypeConstants.PROJECT_DATA_SORT + FunctionTypeConstants.SEPARATOR + kind);
    }
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      resultMemory = objMap.get(FunctionTypeConstants.SINGLE_DATA_SORT + singleId + FunctionTypeConstants.SEPARATOR + kind);
    }
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      resultMemory = objMap.get(FunctionTypeConstants.UNIT_DATA_SORT + unitId + FunctionTypeConstants.SEPARATOR + kind);
    }

    if(  ObjectUtils.isEmpty(result)||result.length!==array1.length )
    {   // 非父级材料_非补充
      let ts_z_fbc = array1.filter(i => !(i.levelMark === '1' || i.levelMark === '2') && i.supplementRcjFlag !== 1);
      // 非父级材料_补充
      let ts_z_bc = array1.filter(i => !(i.levelMark === '1' || i.levelMark === '2') && i.supplementRcjFlag === 1);
      // 父级材料
      let ts_f = array1.filter(i => i.levelMark === '1' || i.levelMark === '2');
      // 父级_不勾选
      let ts_f_0 = ts_f.filter(i => i.markSum === 0);
      // 父级_勾选
      let ts_f_1 = ts_f.filter(i => i.markSum === 1);
      result = ts_z_fbc
          .concat(ts_z_bc)
          .concat(ts_f_0)
          .concat(ts_f_1)
      ;
    }

    // 自定义排序校验重置
    if (ObjectUtils.isNotEmpty(resultMemory)) {
      let rcj = array1.map(item => item.tempcol3)
      let isUnique = this.areListsMutuallyUnique(rcj, resultMemory);
      if (!isUnique) {
        if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
          objMap.delete(FunctionTypeConstants.PROJECT_DATA_SORT + FunctionTypeConstants.SEPARATOR + kind);
        }
        if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
          objMap.delete(FunctionTypeConstants.SINGLE_DATA_SORT + singleId + FunctionTypeConstants.SEPARATOR + kind);
        }
        if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
          objMap.delete(FunctionTypeConstants.UNIT_DATA_SORT + unitId + FunctionTypeConstants.SEPARATOR + kind);
        }
        resultMemory = undefined
      }
    }
    //使用自定义排序
    if (ObjectUtils.isNotEmpty(resultMemory)) {
      let   resultArray  =new Array();
      resultMemory.forEach(code => {
        let  data=array1.find(item =>item.tempcol3 === code);
        if(ObjectUtils.isNotEmpty(data)){
          resultArray.push(data) ;
        }
      });
      result =resultArray;
    }


    return result;

  }


  /**
   *  若预算价与市场价不一致则数据行底部颜色变化展示，并在价格来源标识”自行询价”
   *  若干市场价高于预算价则市场价数值标红展示，若低于预算价则标绿展示
   * @param rcjList
   */
  constructProjectRcjColour(rcjList) {
    rcjList.forEach(rcj => {
      if (this._getBaseJournalPrice(rcj) != this._getMarketPrice(rcj)) {
        //rcj.sourcePrice = "自行询价";
        if (this._getMarketPrice(rcj) > this._getBaseJournalPrice(rcj)) {
          rcj.colour = 'red';
        } else {
          rcj.colour = 'green';
        }
      }
    });
  }

  /**
   * 处理批注
   * @param singleId
   * @param rcjList
   * @param levelType
   * @param isShowAnnotations
   */
  rcjAnnotations(singleId, rcjList, levelType, isShowAnnotations) {
    rcjList.forEach(rcj => {
      if (ObjectUtils.isNotEmpty(rcj.annotationsSingleObj)) {
        rcj.annotationsSingle = rcj.annotationsSingleObj[singleId]?.annotationsSingle
        rcj.isShowAnnotationsSingle = rcj.annotationsSingleObj[singleId]?.isShowAnnotationsSingle
      }
      if (ObjectUtils.isNotEmpty(isShowAnnotations)) {
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            rcj.isShowAnnotations = isShowAnnotations;
          }else if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            rcj.isShowAnnotationsSingle = isShowAnnotations;
          }else if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            rcj.isShowAnnotationsPro = isShowAnnotations;
          }
      }
    });
  }

  /**
   * 处理批注
   * @param rcjList
   */
  columnValueInit(rcjList) {
    rcjList.forEach(rcj => {
      if (ObjectUtils.isEmpty(rcj.ifProvisionalEstimate)) {
        rcj.ifProvisionalEstimate = 0
      }
    });
  }

  /**
   * 增加rcj汇总颜色
   * @param args
   * @return {Promise<void>}
   */
  async saveRcjCellectColor(args) {
    let { constructId, singleId, unitId, code, color, levelType, rcjList} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(rcjMap)) {
      rcjMap = new Map();
      businessMap.set(FunctionTypeConstants.RCJ_COLLECT, rcjMap);
    }

    for (let rcj of rcjList) {
      let materialCode = rcj.materialCodeUnit? rcj.materialCodeUnit : rcj.materialCode
      let colorMap = rcjMap.get(FunctionTypeConstants.UNIT_COLOR + FunctionTypeConstants.SEPARATOR + materialCode);
      if (ObjectUtils.isEmpty(colorMap)) {
        colorMap = new Map();
        rcjMap.set(FunctionTypeConstants.UNIT_COLOR + FunctionTypeConstants.SEPARATOR + materialCode, colorMap);
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
        let unitIdSet = Array.from(rcj.unitIdSet)
        for (let id of unitIdSet) {
          colorMap.set(id, color);
        }
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getSingleUnitAll(constructId, singleId);
        let idArray = unitList.map(item => item.sequenceNbr)
        let unitIdSet = Array.from(rcj.unitIdSet).filter(item => idArray.includes(item))
        for (let id of unitIdSet) {
          colorMap.set(id, color);
        }
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
        colorMap.set(unitId, color);
      }
    }

    return ResponseData.success();
  }

  /**
   * rcj汇总颜色删除
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async delRcjCellectColor(args) {
    let { constructId, unitId, levelType, code, rcjList} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);

    for (let rcj of rcjList) {
      let materialCode = rcj.materialCodeUnit? rcj.materialCodeUnit : rcj.materialCode
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
        rcjMap?.delete(FunctionTypeConstants.UNIT_COLOR + FunctionTypeConstants.SEPARATOR + materialCode);
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        rcjMap?.delete(FunctionTypeConstants.UNIT_COLOR + FunctionTypeConstants.SEPARATOR + materialCode);
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
        let colorMap = rcjMap.get(FunctionTypeConstants.UNIT_COLOR + FunctionTypeConstants.SEPARATOR + materialCode);
        colorMap?.delete(unitId);
      }
    }
    return ResponseData.success();
  }

  /**
   * 人材机汇总修改rcj
   * @param args
   * @returns {Promise<void>}
   */
  async updateRcjCellect(args) {
    let levelType = args.levelType;
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      return ResponseData.success(await this.updateUnitRcjCellect(args));
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      return ResponseData.success(await this.updateProjectRcjCellect(args));
    }
    if(  levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE){
      return ResponseData.success(await this.updateProjectRcjCellect(args));
    }
    return ResponseData.success();
  }

  /**
   * 批量删除批注
   * @param args
   * @returns {Promise<void>}
   */
  async batchDeleteAnnotations(args) {
    let levelType = args.levelType;
    if (Number(levelType) === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      return ResponseData.success(await this.batchDeleteUnitAnnotations(args));
    }
    if (Number(levelType) === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      return ResponseData.success(await this.batchDeleteSingleAnnotations(args));
    }
    if (Number(levelType) === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      return ResponseData.success(await this.batchDeleteProjectAnnotations(args));
    }
    return ResponseData.success();
  }

  /**
   * 取消排序
   * @param args
   * @returns {Promise<void>}
   */
  async removeRcjCellectSort(args) {
    let {constructId, singleId, unitId, levelType, kind} = args
    let objMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      objMap.delete(FunctionTypeConstants.PROJECT_DATA_SORT + FunctionTypeConstants.SEPARATOR + kind);
    }
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      objMap.delete(FunctionTypeConstants.SINGLE_DATA_SORT + singleId + FunctionTypeConstants.SEPARATOR + kind);
    }
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      objMap.delete(FunctionTypeConstants.UNIT_DATA_SORT + unitId + FunctionTypeConstants.SEPARATOR + kind);
    }
    return ResponseData.success();
  }


  /**
   * 是否排序
   * @param args
   * @returns {Promise<void>}
   */
  async isRcjCellectSort(args) {
    let {constructId, singleId, unitId, levelType, kind} = args
    let reuslt = false;
    let resultMemory;
    let objMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      resultMemory = objMap.get(FunctionTypeConstants.PROJECT_DATA_SORT + FunctionTypeConstants.SEPARATOR + kind);
    }
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      resultMemory = objMap.get(FunctionTypeConstants.SINGLE_DATA_SORT + singleId + FunctionTypeConstants.SEPARATOR + kind);
    }
    if (ObjectUtils.isNotEmpty(objMap) && levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      resultMemory = objMap.get(FunctionTypeConstants.UNIT_DATA_SORT + unitId + FunctionTypeConstants.SEPARATOR + kind);
    }
    if (ObjectUtils.isNotEmpty(resultMemory)) {
      reuslt = true;
    }
    return ResponseData.success(reuslt);
  }

    /**
   * 单位工程-批量删除批注
   * @param args
   * @returns {Promise<void>}
   */
  async batchDeleteUnitAnnotations(args) {
    let {constructId, unitId, isAllUnit} = args;
    let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    if (isAllUnit) {
      rcjKey = WildcardMap.WILDCARD;
    }
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    for (let constructProjectRcj of constructProjectRcjs) {
      constructProjectRcj.annotations = '';
      let pbs = constructProjectRcj.pbs?constructProjectRcj.pbs:[];
      for (let pb of pbs) {
        pb.annotations = '';
      }
      if (isAllUnit === true) {
        constructProjectRcj.annotationsSingleObj = {};
        constructProjectRcj.annotationsPro = '';
      }
    }

  }


  /**
   * 工程项目-批量删除批注
   * @param args
   * @returns {Promise<void>}
   */
  async batchDeleteProjectAnnotations(args) {
    let {constructId} = args;
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.WILDCARD);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    for (let constructProjectRcj of constructProjectRcjs) {
      constructProjectRcj.annotationsPro = '';
      let pbs = constructProjectRcj.pbs?constructProjectRcj.pbs:[];
      for (let pb of pbs) {
        pb.annotationsPro = '';
      }
    }
  }


  /**
   * 单项-批量删除批注
   * @param args
   * @returns {Promise<void>}
   */
  async batchDeleteSingleAnnotations(args) {
    let {constructId, singleId} = args;
    let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getSingleUnitAll(constructId, singleId);
    let idArray = unitList.map(item => item.sequenceNbr)
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item=>idArray.includes(item.unitId))
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    for (let constructProjectRcj of constructProjectRcjs) {
      constructProjectRcj.annotationsSingleObj = {};
    }
  }

  /**
   * 人材机汇总编辑入参精度统一处理
   * @param precisionAll
   * @param constructProjectRcj
   * @returns {Promise<void>}
   * @private
   */
  _rcjCollectionPrecision(precisionAll, constructProjectRcj) {
    let precision = precisionAll.RCJ_COLLECT;
    // 不含税市场价
    if (ObjectUtil.isNotEmpty(constructProjectRcj.marketPrice)) {
      constructProjectRcj.marketPrice = NumberUtil.numberScale(Number(constructProjectRcj.marketPrice), precision.marketPrice);
    }
    // 含税市场价
    if (ObjectUtil.isNotEmpty(constructProjectRcj.marketTaxPrice)) {
      constructProjectRcj.marketTaxPrice = NumberUtil.numberScale(Number(constructProjectRcj.marketTaxPrice), precision.marketTaxPrice);
    }
    // 税率
    if (ObjectUtil.isNotEmpty(constructProjectRcj.taxRate)) {
      constructProjectRcj.taxRate = NumberUtil.numberScale(Number(constructProjectRcj.taxRate), precision.taxRate);
    }
    // 甲供价
    if (ObjectUtil.isNotEmpty(constructProjectRcj.donorMaterialPrice)) {
      constructProjectRcj.donorMaterialPrice = NumberUtil.numberScale(Number(constructProjectRcj.donorMaterialPrice), precision.donorMaterialPrice);
    }
    // 数量
    if (ObjectUtil.isNotEmpty(constructProjectRcj.totalNumber)) {
      constructProjectRcj.totalNumber = NumberUtil.numberScale(Number(constructProjectRcj.totalNumber), precision.totalNumber);
    }
    // 甲供数量
    if (ObjectUtil.isNotEmpty(constructProjectRcj.donorMaterialNumber)) {
      constructProjectRcj.donorMaterialNumber = NumberUtil.numberScale(Number(constructProjectRcj.donorMaterialNumber), precision.donorMaterialNumber);
    }
    // 三材系数
    if (ObjectUtil.isNotEmpty(constructProjectRcj.transferFactor)) {
      constructProjectRcj.transferFactor = NumberUtil.numberScale(Number(constructProjectRcj.transferFactor), precision.transferFactor);
    }
  }

  /**
   * 单位工程人材机修改
   *  甲供数量、价格字段与乙供共用，乙供状态下前端不展示
   * @param args
   * @returns {Promise<void>}
   */
  async updateUnitRcjCellect(args) {
    let { constructId, singleId, unitId, sequenceNbr, levelType } = args;
    let {
      materialName,
      specification,
      marketPrice,
      sourcePrice,
      ifDonorMaterial,
      donorMaterialNumber,
      producer,
      manufactor,
      brand,
      deliveryLocation,
      qualityGrade,
      kind,
      ifLockStandardPrice,
      markSum,
      remark,
      totalNumber,
      annotations,
      isShowAnnotations,
      highlight,
      kindSc,
      transferFactor,
      supplyTime,
      donorMaterialPrice,
      marketTaxPrice,
      taxRate,
      ifProvisionalEstimate,
      recoverTaxRate
    } = args.constructProjectRcj;
    taxRate = ObjectUtils.isNotEmpty(taxRate) ? Number(taxRate) : taxRate;
    recoverTaxRate = ObjectUtils.isEmpty(recoverTaxRate) ? false : recoverTaxRate;
    let unitBj = args.constructProjectRcj.unit;
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    //
    Array.prototype.push.apply(constructRcjArray, constructProjectRcjs);
    rcjListHasDetail.forEach(item => {
      Array.prototype.push.apply(rcjDetailList, item.pbs);
    });
    Array.prototype.push.apply(constructRcjArray, rcjDetailList)
    let dx = constructRcjArray.find(i => i.sequenceNbr === sequenceNbr);

    constructRcjArray=constructRcjArray.filter(item=> (item.totalNumber !== 0 && item.isTempRemove !== CommonConstants.COMMON_YES)
        ||  ObjectUtils.isNotEmpty(marketPrice)
        ||  ObjectUtils.isNotEmpty(marketTaxPrice)
        ||  ObjectUtils.isNotEmpty(taxRate)
    );
    if (ObjectUtils.isEmpty(dx)) {
      return null;
    }
    let t = new GljConstructProjectRcj();
    ConvertUtil.setDstBySrc(dx, t);
    let updateSC = false;
    let  deRowIds = new Set();
    if (!ObjectUtils.isEmpty(t)) {
      for (let constructRcjArrayElement of constructRcjArray) {
        let  baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
        if (constructRcjArrayElement.materialCode === t.materialCode
            && constructRcjArrayElement.materialName === t.materialName
            && constructRcjArrayElement.specification === t.specification
            && constructRcjArrayElement.unit === t.unit
            //&& constructRcjArrayElement.dePrice === t.dePrice
            //&& (constructRcjArrayElement.kind==4 ||constructRcjArrayElement.kind==5? true : this._getBaseJournalPrice(constructRcjArrayElement) === this._getBaseJournalPrice(t))
            && this.isPrincipalMaterial(constructRcjArrayElement,baseRCJ,t)
        ) {
          let ifChangeMaterialCode = false;
          let  updateMemory =false ;
          if (!ObjectUtils.isEmpty(materialName)) {
            constructRcjArrayElement.materialName = materialName;
            ifChangeMaterialCode = true;
          }
          if (!ObjectUtils.is_Undefined(specification)) {
            if (specification !== '') {
              constructRcjArrayElement.specification = specification;
            } else {
              constructRcjArrayElement.specification = null;
            }
            ifChangeMaterialCode = true;

          }

          if (!ObjectUtils.isEmpty(unitBj)) {
            constructRcjArrayElement.unit = unitBj;
            updateMemory = true;
            ifChangeMaterialCode = true;
          }

          //类型
          if (!ObjectUtils.isEmpty(kind)) {
            let  baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
            if( kind ===2
                && (constructRcjArrayElement.kind === 4 || constructRcjArrayElement.kind === 5)
                && (constructRcjArrayElement.supplementRcjFlag!==RcjCommonConstants.SUPPLEMENT_RCJ_FLAG)
                && (baseRCJ.kind !== 4 && baseRCJ.kind !== 5)
            ){
              if(taxMethod === RcjCommonConstants.SIMPLE_REVERSE){
                constructRcjArrayElement.baseJournalPrice=constructRcjArrayElement.baseJournalPriceOriginalReverse;
                constructRcjArrayElement.baseJournalTaxPrice=constructRcjArrayElement.baseJournalTaxPriceOriginalReverse;
              }
              if(taxMethod === RcjCommonConstants.GENERAL_FORWARD){
                constructRcjArrayElement.baseJournalPrice=constructRcjArrayElement.baseJournalPriceOriginalForward;
                constructRcjArrayElement.baseJournalTaxPrice=constructRcjArrayElement.baseJournalTaxPriceOriginalForward;
              }

            }
            constructRcjArrayElement.kind = kind;
            for (let key in RcjTypeEnum) {
              if (RcjTypeEnum[key].code === kind) {
                constructRcjArrayElement.type= RcjTypeEnum[key].desc;
              }
            }
            if(constructRcjArrayElement.kind === 4 || constructRcjArrayElement.kind === 5) {
              if(constructRcjArrayElement.marketPrice !== constructRcjArrayElement.baseJournalPrice){
                marketPrice = constructRcjArrayElement.marketPrice;
              }
              if(constructRcjArrayElement.marketTaxPrice !== constructRcjArrayElement.baseJournalTaxPrice){
                marketTaxPrice = constructRcjArrayElement.marketTaxPrice;
              }
            }
            deRowIds.add(constructRcjArrayElement.deRowId);
            await this.service.gongLiaoJiProject.gljRcjService.rcjDiffSwitchType(constructRcjArrayElement);
            await ProjectDomain.getDomain(constructId).getResourceDomain().notify(constructRcjArrayElement);
            ifChangeMaterialCode = true;

          }

          if (!ObjectUtils.isEmpty(markSum)) {
            constructRcjArrayElement.markSum = markSum;
            constructRcjArrayElement.sourcePrice = "";
            let rcj = constructProjectRcjs.find(item => item.sequenceNbr === constructRcjArrayElement.sequenceNbr);
            if(markSum===0){
              let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
              rcj.baseJournalPrice = baseRCJ.baseJournalPrice;
              rcj.baseJournalTaxPrice = baseRCJ.baseJournalTaxPrice;
              rcj.marketPrice = rcj.baseJournalPrice;
              rcj.marketTaxPrice = rcj.baseJournalTaxPrice;
              rcj.sourcePrice = "";
            }
            let rcjDetais = rcj.pbs;
            if (ObjectUtils.isNotEmpty(rcjDetais)) {
              rcjDetais.forEach(item =>{
                item.markSum = markSum;
                if(markSum===0  ) {
                  item.constructId =rcj.constructId;
                  item.unitId =rcj.unitId;
                  item.marketPriceCp = item.marketPrice;
                  item.marketTaxPriceCp = item.marketTaxPrice;
                  item.sourcePriceCp = item.sourcePrice;
                  item.marketPrice= item.baseJournalPrice;
                  item.marketTaxPrice= item.baseJournalTaxPrice;
                  item.sourcePrice =null ;
                }
                if(markSum===1 ) {
                  // item.marketPrice= ObjectUtils.isEmpty(item.marketPriceCp )?  item.marketPrice :item.marketPriceCp ;
                  // item.marketTaxPrice= ObjectUtils.isEmpty(item.marketTaxPriceCp )?  item.marketPrice :item.marketTaxPriceCp ;
                  // item.sourcePrice =  ObjectUtils.isEmpty(item.marketPriceCp )?  item.sourcePrice : item.sourcePriceCp;
                }
              });
            }
            if(markSum===1){
              ifDonorMaterial =0;
              //二次解析汇总后清除载价信息
              rcj.isExecuteLoadPrice = null;
              rcj.highlight = null;
            }
            await this.parentMaterialPrice(rcjDetailList, rcj);
            deRowIds.add(constructRcjArrayElement.deRowId);
            updateMemory = true ;
            //同步到de
            await this.updateDeRcjHandle(constructRcjArrayElement);
          }

          //甲供
          if (ObjectUtils.isNotEmpty(ifDonorMaterial)) {
            constructRcjArrayElement.ifDonorMaterial = ifDonorMaterial;
            if (ifDonorMaterial === RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
              constructRcjArrayElement.donorMaterialNumber =null;
              constructRcjArrayElement.donorMaterialPrice =null;
              constructRcjArrayElement.updateFalg = 0  ;
            }
            if (ifDonorMaterial === RcjCommonConstants.IFDONORMATERIAL) {
              if (taxMethod === 1) {
                constructRcjArrayElement.donorMaterialPrice = constructRcjArrayElement.marketPrice;
              }
              if (taxMethod === 0) {
                constructRcjArrayElement.donorMaterialPrice = constructRcjArrayElement.marketTaxPrice;
              }
            }
            if ( ObjectUtils.isEmpty(constructRcjArrayElement.donorMaterialNumber)  && ifDonorMaterial != RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
              constructRcjArrayElement.donorMaterialNumber =  totalNumber;
            }
            updateMemory = true ;
          }

          if (ObjectUtils.isNotEmpty(donorMaterialNumber) && constructRcjArrayElement.ifDonorMaterial != RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
            donorMaterialNumber =Number(donorMaterialNumber);
            constructRcjArrayElement.donorMaterialNumber =  donorMaterialNumber;
            if(totalNumber != constructRcjArrayElement.donorMaterialNumber){
              constructRcjArrayElement.updateFalg = 1  ;
            }else {
              constructRcjArrayElement.updateFalg = 0  ;
            }
            updateMemory = true ;
          }
          if(ObjectUtils.isNotEmpty(donorMaterialPrice)){
            constructRcjArrayElement.donorMaterialPrice =donorMaterialPrice;
            if(constructRcjArrayElement.marketPrice !== donorMaterialPrice){
              constructRcjArrayElement.highlight = null;
            }
            if (taxMethod === 1){
              marketPrice = donorMaterialPrice ;
            }
            if (taxMethod === 0){
              marketTaxPrice = donorMaterialPrice ;
            }
            updateMemory = true ;
          }

          // 修改含税市场价
          if (ObjectUtil.isNotEmpty(marketPrice) &&　 constructRcjArrayElement.marketPrice!== marketPrice) {
            constructRcjArrayElement.marketPrice = marketPrice;
            constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
            constructRcjArrayElement.highlight = highlight;
            await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,donorMaterialPrice,1);
            //this.service.gongLiaoJiProject.gljRcjService.calculateTax(constructRcjArrayElement,1);
            deRowIds.add(constructRcjArrayElement.deRowId);
            updateMemory =true ;
          }

          if (ObjectUtil.isNotEmpty(marketTaxPrice) && constructRcjArrayElement.marketTaxPrice !== marketTaxPrice) {
            constructRcjArrayElement.marketTaxPrice = marketTaxPrice;
            constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
            constructRcjArrayElement.highlight = highlight;
            await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,donorMaterialPrice,0);
            //this.service.gongLiaoJiProject.gljRcjService.calculateTax(constructRcjArrayElement,0);
            deRowIds.add(constructRcjArrayElement.deRowId);
            updateMemory =true ;
          }

          // 修改税率
          if (!ObjectUtils.isEmpty(taxRate)  || taxRate ==='') {
            constructRcjArrayElement.taxRate = taxRate ===''? 0 : taxRate
            constructRcjArrayElement.isUpdateTaxRate = true;
            if(recoverTaxRate){
              constructRcjArrayElement.isUpdateTaxRate = false;
            }
            this.service.gongLiaoJiProject.gljRcjService.calculateTax(constructRcjArrayElement, taxMethod);
            deRowIds.add(constructRcjArrayElement.deRowId);
            updateMemory = true ;
          }

          // 是否暂估
          if (ifLockStandardPrice === RcjCommonConstants.IFLOCKPRICE_DEFAULT && constructRcjArrayElement.ifProvisionalEstimate === 1) {
            ifProvisionalEstimate = 0
          }
          if (!ObjectUtils.isEmpty(ifProvisionalEstimate)) {
            constructRcjArrayElement.ifProvisionalEstimate = ifProvisionalEstimate;
            if (ifProvisionalEstimate === 1) {
              constructRcjArrayElement.lastSourcePrice = constructRcjArrayElement.sourcePrice;
              constructRcjArrayElement.sourcePrice = "";
              ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE;
            } else {
              constructRcjArrayElement.sourcePrice = constructRcjArrayElement.lastSourcePrice;
              if(ObjectUtils.isEmpty(ifLockStandardPrice)) {
                ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
              }
            }
            updateMemory = true ;
          }

          if (!ObjectUtils.isEmpty(kindSc)) {
            constructRcjArrayElement.kindSc = kindSc;
            if (kindSc === "空") {
              constructRcjArrayElement.transferFactor = null;
              transferFactor = null;
              constructRcjArrayElement.scCount = null;
            }
            updateSC = true;
          }
          if (!ObjectUtils.isEmpty(transferFactor)) {
            constructRcjArrayElement.transferFactor = transferFactor;
            constructRcjArrayElement.scCount = NumberUtil.multiply(constructRcjArrayElement.totalNumber,constructRcjArrayElement.transferFactor);
            updateSC = true;
          }

          if (!ObjectUtils.isEmpty(ifLockStandardPrice)) {
            constructRcjArrayElement.ifLockStandardPrice = ifLockStandardPrice;
            await this.updateDeRcjHandle(constructRcjArrayElement);
            updateMemory = true ;
          }
          if(updateMemory){
            this.updateMemoryRcj(constructRcjArrayElement,unitId);
          }

          //修改编码
          if (ifChangeMaterialCode) {
            //if( !((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark)?constructRcjArrayElement.levelMark: ResourceConstants.LEVEL_MARK_NONE_PB)!==  ResourceConstants.LEVEL_MARK_NONE_PB && constructRcjArrayElement.markSum === RcjCommonConstants.MARKSUM_JX)){
            if( !((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark)?constructRcjArrayElement.levelMark: ResourceConstants.LEVEL_MARK_NONE_PB)!==  ResourceConstants.LEVEL_MARK_NONE_PB )){
              await this.changeMaterialCodeMemory(constructRcjArrayElement, false, constructRcjArray);
              this.service.gongLiaoJiProject.gljRcjService.processingMarketPrice(constructRcjArrayElement);
            }
            //if(constructRcjArrayElement.markSum === RcjCommonConstants.MARKSUM_JX && constructRcjArrayElement.hasOwnProperty('levelMark') && constructRcjArrayElement.levelMark!== ResourceConstants.LEVEL_MARK_NONE_PB){
            else{
              await this.parentMaterialCodeChangeMemory(constructProjectRcjs , constructRcjArrayElement,false);
            }
            //修改父级编码
            //修改的子
            if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
              let t1 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);

              await this.parentMaterialCodeChangeMemory(constructProjectRcjs , t1,false);
            }
            await this.updateDeRcjHandle(constructRcjArrayElement);
          }

          //产地
          if (!ObjectUtils.is_Undefined(producer)) {
            constructRcjArrayElement.producer = producer;
          }
          //厂家
          if (!ObjectUtils.is_Undefined(manufactor)) {
            constructRcjArrayElement.manufactor = manufactor;
          }
          //品牌
          if (!ObjectUtils.is_Undefined(brand)) {
            constructRcjArrayElement.brand = brand;
          }
          //送达地点
          if (!ObjectUtils.is_Undefined(deliveryLocation)) {
            constructRcjArrayElement.deliveryLocation = deliveryLocation;
          }
          //质量等级
          if (!ObjectUtils.is_Undefined(qualityGrade)) {
            constructRcjArrayElement.qualityGrade = qualityGrade;
          }

          //修改备注
          if (ObjectUtils.isNotEmpty(remark) || remark === "") {
            constructRcjArrayElement.remark = remark;
          }

          //修改批注
          if (ObjectUtils.isNotEmpty(annotations) || annotations === "") {
            constructRcjArrayElement.annotations = annotations;
          }
          //修改批注 是否展示
          if (ObjectUtils.isNotEmpty(isShowAnnotations)) {
            constructRcjArrayElement.isShowAnnotations = isShowAnnotations;
          }
          //修改供应时间
          if (ObjectUtils.isNotEmpty(supplyTime)) {
            constructRcjArrayElement.supplyTime = supplyTime;
          }

        }
      }
    }

    if(ObjectUtil.isNotEmpty(deRowIds)){
      for( let deRowId of deRowIds ){
        await  ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId, unitId,deRowId  },false);
      }
    }
    try {
      await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        qfMajorType: null
      });

      //联动计算装饰超高人材机数量
      await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
      if (ObjectUtil.isNotEmpty(deRowIds)) {
        for (let deRowId of deRowIds) {
          //联动计取安装费
          await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "delete");
        }
      }
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: singleId,
        constructId: constructId
      });
      //若三材发生变化，联动计算建设其他费钢筋数量
      if(updateSC){
        await this.service.gongLiaoJiProject.gljRcjCollectService.updateOtherProjectScGJ(constructId);
      }
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }

  /**
   * 子级材料 联动父级材料 变动
   * @param constructProjectRcjList 父级材料集合
   * @param rcjDetailList 子级材料集合
   * @param rcj 被影响的父级材料 对象
   * @param maxMaterialCode 最大值
   */
  async parentMaterialCodeChange(constructProjectRcjs, rcjDetailList, rcj,isSingle) {
    let constructRcjArray = new  Array();
    if(ObjectUtils.isNotEmpty(rcjDetailList)){
      rcjDetailList.forEach(item=>constructRcjArray.push(item));
    }
    constructProjectRcjs.forEach(item=>constructRcjArray.push(item));
    let  max=this.getMaxNumber(constructRcjArray,rcj);
    let rcjDetailsTemp = [];
    let { service } = EE.app;
    // if (rcj.levelMark === ResourceConstants.LEVEL_MARK_PB_CL && rcj.markSum ===RcjCommonConstants.MARKSUM_JX ) {
    //   let baseClpbList = await service.gongLiaoJiProject.gljBaseRcjService.getClpb(rcj);
    //   if (!ObjectUtils.isEmpty(baseClpbList)) {
    //     rcjDetailsTemp = rcjDetailsTemp.concat(ConvertUtil.deepCopy(baseClpbList));
    //   }
    // }
    // if (rcj.levelMark === ResourceConstants.LEVEL_MARK_PB_JX  && rcj.markSum ===RcjCommonConstants.MARKSUM_JX) {
    //   let baseJxpbList = await service.gongLiaoJiProject.gljBaseRcjService.getJxpb(rcj);
    //   if (!ObjectUtils.isEmpty(baseJxpbList)) {
    //     rcjDetailsTemp = rcjDetailsTemp.concat(ConvertUtil.deepCopy(baseJxpbList));
    //   }
    // }
    if (ObjectUtils.isEmpty(rcjDetailsTemp)) {
      return;
    }
    //let filter = rcjDetailList.filter(i => i.parentId === rcj.sequenceNbr);
    let filter =rcj.pbs;
    //1.撤回编码
    if ( this.estimate2ListSame(filter, rcjDetailsTemp)  && await this.estimateChangeMaterialCode(rcj)) {
      if (rcj.materialCode.includes('#')) {
        rcj.materialCode = rcj.materialCode.substring(0, rcj.materialCode.lastIndexOf('#'));
      }
    }
    //2.修改编码
    else {
      //判断已有 列表里是否有 同样的
      let materialCode = await this.findMaterialCodeSameRcjList(constructProjectRcjs, rcj, rcjDetailList);

      if (ObjectUtils.isNotEmpty(materialCode)) {
        rcj.materialCode = materialCode;
      } else {
        if (rcj.materialCode.includes('#')) {
          if( isSingle &&  ObjectUtils.isNotEmpty(constructRcjArray.find(item=>item.materialCode ===rcj.materialCode && item.sequenceNbr !==rcj.sequenceNbr))){
            rcj.materialCode= rcj.materialCode.replace( /#\d+/g, '')+ '#' + max;
          }
        }else{
          rcj.materialCode = rcj.materialCode + '#' + max;
        }
      }

    }
  }

  /**
   * 获取序号
   * @param constructRcjArray
   */
  getMaxNumber(constructRcjArray,rcj){
    //确定 #数字到位置
    let constructRcjArrayFilter=constructRcjArray.filter(item =>{
      if(  item.materialCode.replace( /#\d+/g, '')===rcj.materialCode.replace( /#\d+/g, '') ) {
        return   item ;
      }
    });
    let map = constructRcjArrayFilter.map(i => i.materialCode);
    let from = Array.from(new Set(map));
    let array = new Array();
    let releaseId= new Map();
    for (let fromElement of from) {
      if (fromElement.includes('#')) {
        //let a = fromElement.replace( /#\d+/g, '')
        let s = fromElement.substring(fromElement.lastIndexOf('#') + 1);
        array.push(s);
        releaseId.set(s,true);
      }
    }
    let max = 1;
    if (!ObjectUtils.isEmpty(array)) {
      max = Math.max.apply(null, array) + 1;
    }
    for (let i=1 ;  i<=releaseId.size;i++){
      if(ObjectUtils.isEmpty( releaseId.get(i+""))){
        max = i;
        break ;
      }
    }
    return  max
  }

  /**
   * 判断 父级子材料 和 库中子材料是否一致
   * @param rcjDetailList
   * @param rcjDetailsTemp
   */
  estimate2ListSame(rcjDetailList, rcjDetailsTemp) {
    if (ObjectUtils.isEmpty(rcjDetailsTemp)) {
      return false;
    }
    let materialCodeList = rcjDetailList.map(i => i.materialCode);
    let materialCodeList1 = rcjDetailsTemp.map(i => i.materialCode);
    return  this.areListsContentEqual(materialCodeList, materialCodeList1);
  }

  /**
   * 判断两个list 除排序外 是否一致
   * @param list1
   * @param list2
   * @returns {Promise<boolean>}
   */
  areListsContentEqual(list1, list2) {
    if (list1.length !== list2.length) {
      return false;
    }
    // 复制并排序两个列表
    const sortedList1 = list1.slice().sort();
    const sortedList2 = list2.slice().sort();
    // 逐个比较排序后的元素
    for (let i = 0; i < sortedList1.length; i++) {
      if (sortedList1[i] !== sortedList2[i]) {
        return false;
      }
    }
    return true;
  }

  /**
   * 查询相同 父级 编码code 材料
   * @param constructProjectRcjList 父级list
   * @param rcj  人材机对象
   * @rcjDetailList  子级list
   */
  async findMaterialCodeSameRcjList(constructProjectRcjList, rcj, rcjDetailList) {
    //父级 材料对应子级
    let filter = rcj.pbs;
    for (let constructProjectRcjListElement of constructProjectRcjList) {
      let filter1 = rcjDetailList.filter(i => i.parentId === constructProjectRcjListElement.sequenceNbr);
      if (await this.estimate2ListSame(filter, filter1)
          && constructProjectRcjListElement.materialName === rcj.materialName
          && constructProjectRcjListElement.specification === rcj.specification
          && constructProjectRcjListElement.unit === rcj.unit
          && constructProjectRcjListElement.dePrice === rcj.dePrice
          && constructProjectRcjListElement.kind === rcj.kind
          && constructProjectRcjListElement.sequenceNbr !== rcj.sequenceNbr
      ) {
        return constructProjectRcjListElement.materialCode;
      }
    }
    return null;
  }

  /**
   * 获取两个list中最大的 #号之后的数字
   * @param constructProjectRcjList
   * @param rcjDetailList
   */
  async getMaxMaterialCode(constructProjectRcjList, rcjDetailList) {
    ParamUtils.getPatram();
    let map;
    let map1;
    if (!ObjectUtils.isEmpty(constructProjectRcjList)) {
      map = constructProjectRcjList.map(i => i.materialCode);
    }

    if (!ObjectUtils.isEmpty(rcjDetailList)) {
      map1 = rcjDetailList.map(i => i.materialCode);
    }

    let mergedArray = [];

    if (map) {
      mergedArray = mergedArray.concat(map);
    }

    if (map1) {
      mergedArray = mergedArray.concat(map1);
    }
    let from = Array.from(new Set(mergedArray));
    let array = new Array();
    for (let fromElement of from) {
      if (fromElement.includes('#')) {
        let s = fromElement.substring(fromElement.lastIndexOf('#') + 1);
        array.push(s);
      }
    }
    let max = 1;
    if (!ObjectUtils.isEmpty(array)) {
      max = Math.max.apply(null, array) + 1;
    }

    return max;
  }


  /**
   * 计算子材料到父材料的 定额价 市场价
   * @param unit 单位工程
   * @param rcj 父级材料对象
   */
  async parentMaterialPrice(rcjDetailList, rcj) {
    let ts = rcjDetailList.filter(i => i.parentId === rcj.sequenceNbr);
    if (!ObjectUtils.isEmpty(ts)) {
      let baseJournalPrice = 0;
      let marketPrice = 0;
      for (let t of ts) {
        baseJournalPrice = NumberUtil.add(baseJournalPrice, NumberUtil.multiply(this._getBaseJournalPrice(t), t.resQty));
        marketPrice = NumberUtil.add(marketPrice, NumberUtil.multiply(t.marketPrice, t.resQty));
      }
      this._setBaseJournalPrice(rcj, NumberUtil.numberScale2(baseJournalPrice))
      this._setMarketPrice(rcj, NumberUtil.numberScale2(marketPrice))
    } else {
      rcj.baseJournalPrice = 0;
      rcj.marketPrice = 0;
      rcj.total = 0;
      rcj.totalTax = 0;
      rcj.baseJournalTotal = 0;
      rcj.baseJournalTotalTax = 0;
    }
    if(this._getBaseJournalPrice(rcj) === this._getMarketPrice(rcj)){
      rcj.sourcePrice="";
    }else {
      if(rcj.sourcePrice==""){
        rcj.sourcePrice="自行询价";
      }
    }

  }


  /**
   * 判断修改之后的人材机是否修改到 原来本身的数据
   * @param constructRcjArrayElement
   * @returns {boolean}
   */
  async estimateChangeMaterialCode(constructRcjArrayElement) {
    let  taxMethod=ProjectDomain.getDomain(constructRcjArrayElement.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let baseRCJ ;
    if(constructRcjArrayElement.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
      let businessMap = ProjectDomain.getDomain(constructRcjArrayElement.constructId).functionDataMap;
      let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
      // 放入用戶rcj
      if(ObjectUtils.isEmpty(rcjUserList)||rcjUserList.size===0){
        rcjUserList = new Array();
        businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ,rcjUserList);
      }
      let rcjUser=rcjUserList.find(item => constructRcjArrayElement.materialCode.replace( /#\d+/g, '')  === item.materialCode && item.unitId ===constructRcjArrayElement.unitId)
      if(ObjectUtil.isNotEmpty(rcjUser)){
        if (constructRcjArrayElement.materialCode.replace( /#\d+/g, '') === rcjUser.materialCode
            && constructRcjArrayElement.materialName === rcjUser.materialName
            && constructRcjArrayElement.specification === rcjUser.specification
            && constructRcjArrayElement.unit === rcjUser.unit
        && (
          this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(constructRcjArrayElement)? true : (taxMethod===1?constructRcjArrayElement.baseJournalPrice===rcjUser.baseJournalPrice :constructRcjArrayElement.baseJournalTaxPrice===rcjUser.baseJournalTaxPrice)
            )
            && constructRcjArrayElement.kind === rcjUser.kind
        ){
          return  true;
        }
      }
      return  false;
    }


    if (ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark) || ObjectUtils.isEmpty(constructRcjArrayElement.rcjPbsId)){
      baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
      if (constructRcjArrayElement.materialCode.replace( /#\d+/g, '') === baseRCJ?.materialCode
          && constructRcjArrayElement.materialName === baseRCJ?.materialName
          && constructRcjArrayElement.specification === baseRCJ?.specification
          && constructRcjArrayElement.unit === baseRCJ?.unit
          && constructRcjArrayElement.kind === baseRCJ?.kind
          && (
          this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(constructRcjArrayElement)? true : (taxMethod===1?constructRcjArrayElement.baseJournalPrice===baseRCJ.baseJournalPrice :constructRcjArrayElement.baseJournalTaxPrice===baseRCJ.baseJournalTaxPrice)
          )
      ){
        return  true;
      }
    }
    if (ObjectUtils.isEmpty(constructRcjArrayElement.levelMark) &&  constructRcjArrayElement.kind  !==3) {
      baseRCJ =  await this.service.gongLiaoJiProject.gljBaseRcjService.getOneClpb({libraryCode:constructRcjArrayElement.libraryCode,sequenceNbr:constructRcjArrayElement.rcjPbsId});
      if (constructRcjArrayElement.materialCode.replace( /#\d+/g, '') === baseRCJ.materialCode
          && constructRcjArrayElement.materialName === baseRCJ.materialName
          && constructRcjArrayElement.specification === baseRCJ.specification
          && constructRcjArrayElement.unit === baseRCJ.unit
          && constructRcjArrayElement.kind === baseRCJ.kind
          && (
          this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(constructRcjArrayElement)? true : (taxMethod===1?constructRcjArrayElement.baseJournalPrice===baseRCJ.baseJournalPrice :constructRcjArrayElement.baseJournalTaxPrice===baseRCJ.baseJournalTaxPrice)
          )
      ){
        return  true;
      }
    }
    if (ObjectUtils.isEmpty(constructRcjArrayElement.levelMark) &&  constructRcjArrayElement.kind  ===3) {
      baseRCJ =  await this.service.gongLiaoJiProject.gljBaseRcjService.getOneJxpb({libraryCode:constructRcjArrayElement.libraryCode,sequenceNbr:constructRcjArrayElement.rcjPbsId});
      if (constructRcjArrayElement.materialCode.replace( /#\d+/g, '') === baseRCJ.materialCode
          && constructRcjArrayElement.materialName === baseRCJ.materialName
          && constructRcjArrayElement.specification === baseRCJ.specification
          && constructRcjArrayElement.unit === baseRCJ.unit
          && constructRcjArrayElement.kind === baseRCJ.kind
          //&& constructRcjArrayElement.dePrice === baseRCJ.dePrice
          //&& (constructRcjArrayElement.kind==4 ||constructRcjArrayElement.kind==5? true : constructRcjArrayElement.dePrice === baseRCJ.dePrice)
          && (
          this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(constructRcjArrayElement)? true : (taxMethod===1?constructRcjArrayElement.baseJournalPrice===baseRCJ.baseJournalPrice :constructRcjArrayElement.baseJournalTaxPrice===baseRCJ.baseJournalTaxPrice)
          )
      ){
        return  true;
      }
    }
    return false;
  }

  /**
   * 获取已有对象编码
   * @param constructProject 修改对象
   * @param list 已有人材机
   * @returns {string|null}
   */
  async getAlreadyExistMaterialCode(constructProject, list) {
    for (let listElement of list) {
      if (listElement.sequenceNbr != constructProject.sequenceNbr) {
        if (this.getRcjDataBy_22_12(constructProject, listElement)) {
          return listElement.materialCode;
        }
      }
    }

    return null;
  }

  /**
   * 根据22/12定额册寻找 五要素一样数据
   * @param constructRcjArrayElement
   * @param t
   * @param taxCalculationMethod
   * @returns {boolean}
   */
  getRcjDataBy_22_12(constructRcjArrayElement, t) {

    if (constructRcjArrayElement.materialCode === t.materialCode
        && constructRcjArrayElement.materialName === t.materialName
        && constructRcjArrayElement.specification === t.specification
        && constructRcjArrayElement.unit === t.unit
        && constructRcjArrayElement.priceBaseJournalTax === t.priceBaseJournalTax
        && constructRcjArrayElement.markSum === t.markSum) {
      return true;
    }
    if (constructRcjArrayElement.materialCode === t.materialCode
        && constructRcjArrayElement.materialName === t.materialName
        && constructRcjArrayElement.specification === t.specification
        && constructRcjArrayElement.unit === t.unit
        && constructRcjArrayElement.priceBaseJournal === t.priceBaseJournal
        && constructRcjArrayElement.markSum === t.markSum) {
      return true;
    }
    if (constructRcjArrayElement.materialCode === t.materialCode
        && constructRcjArrayElement.materialName === t.materialName
        && constructRcjArrayElement.specification === t.specification
        && constructRcjArrayElement.unit === t.unit
        && constructRcjArrayElement.dePrice === t.dePrice
        && constructRcjArrayElement.markSum === t.markSum) {
      return true;
    }
    return false;
  }

  /**
   * 工程项目人材机修改
   * @param args
   * @returns {Promise<void>}
   */
  async updateProjectRcjCellect(args) {
    let { constructId,levelType,singleId } = args;
    let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.constructId == constructId && item.type == 3);
    let unitIds = unitProjects.map(item=>item.sequenceNbr);
    if(levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE){
       unitIds = this.checkoutParent(unitProjects , singleId);
    }
    let constructProjectRcjList = args.constructProjectRcjList;
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.WILDCARD);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }

    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    let taxMethod = ProjectDomain.getDomain(args.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

    Array.prototype.push.apply(constructRcjArray, constructProjectRcjs);
    rcjListHasDetail.forEach(item => {
      Array.prototype.push.apply(rcjDetailList, item.pbs);
    });
    Array.prototype.push.apply(constructRcjArray, rcjDetailList);
    constructRcjArray=constructRcjArray.filter(item=>
        item.totalNumber !== 0 && unitIds.includes(item.unitId)
    );
    let dealedData = [];
    let  deRowIds = new Set();
    let updateSC = false;
    for (let element of constructProjectRcjList) {
      let sequenceNbr = element.sequenceNbr;
      let materialName = element.materialName;
      let specification = element.specification;
      let unitBj = element.unit;
      let marketPrice = element.marketPrice;
      let marketTaxPrice = element.marketTaxPrice;
      let kind = element.kind;
      let ifProvisionalEstimate = element.ifProvisionalEstimate;
      let ifDonorMaterial = element.ifDonorMaterial;
      let ifLockStandardPrice = element.ifLockStandardPrice;
      let markSum = element.markSum;
      let donorMaterialNumber = element.donorMaterialNumber;
      //产地
      let producer = element.producer;
      //厂家
      let manufactor = element.manufactor;
      //品牌
      let brand = element.brand;
      //送达地点
      let deliveryLocation = element.deliveryLocation;
      //质量等级
      let qualityGrade = element.qualityGrade;
      //总数量
      let totalNumber = element.totalNumber;
      //批注
      let annotationsPro = element.annotationsPro;
      //批注显示
      let isShowAnnotationsPro = element.isShowAnnotationsPro;
      //批注
      let annotationsSingle = element.annotationsSingle;
      //批注显示
      let isShowAnnotationsSingle = element.isShowAnnotationsSingle;
      let sourcePrice = element.sourcePrice;
      let highlight = element.highlight;

      //三材种类
      let kindSc = element.kindSc;
      //三材系数
      let transferFactor = element.transferFactor;
      //备注
      let remark = element.remark;

      let supplyTime = element.supplyTime;
      //甲供价格
      let donorMaterialPrice = element.donorMaterialPrice;
      //税率
      let taxRate = element.taxRate;
      //查询修改 对象
      let dx = constructRcjArray.find(i => i.sequenceNbr === sequenceNbr);
      if (ObjectUtils.isEmpty(dx)) {
        return null;
      }
      let t = new GljConstructProjectRcj();
      ConvertUtil.setDstBySrc(dx, t);


      if (!ObjectUtils.isEmpty(t)) {
        for (let constructRcjArrayElement of constructRcjArray) {
          if (constructRcjArrayElement.materialCode === t.materialCode
              && constructRcjArrayElement.materialName === t.materialName
              && constructRcjArrayElement.specification === t.specification
              && constructRcjArrayElement.unit === t.unit
              && this._getBaseJournalPrice(constructRcjArrayElement) === this._getBaseJournalPrice(t)
              && this._getMarketPrice(constructRcjArrayElement) === this._getMarketPrice(t)
              && constructRcjArrayElement.markSum === t.markSum
              && constructRcjArrayElement.ifDonorMaterial === t.ifDonorMaterial
              // && constructRcjArrayElement.ifLockStandardPrice === t.ifLockStandardPrice
          ) {
            let ifChangeMaterialCode = false;
            let updateMemory = false;
            if (!ObjectUtils.isEmpty(materialName)) {
              constructRcjArrayElement.materialName = materialName;
              //ifChangeMaterialCode = true;
            }

            if (!ObjectUtils.is_Undefined(specification)) {
              if (specification != '') {
                constructRcjArrayElement.specification = specification;
              } else {
                constructRcjArrayElement.specification = null;
              }
              if (constructRcjArrayElement.isDeResource === 1) {
                let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                let de = deDomain.getDeById(constructRcjArrayElement.deId);
                de.specification = constructRcjArrayElement.specification;
                await deDomain.updateDe(de);
              }
              ifChangeMaterialCode = true;
            }

            if (!ObjectUtils.isEmpty(unitBj)) {
              constructRcjArrayElement.unit = unitBj;
              ifChangeMaterialCode = true;
            }

            if(!ObjectUtils.is_Undefined(donorMaterialPrice)){
              // if(constructRcjArrayElement.marketPrice !== donorMaterialPrice){
              //   constructRcjArrayElement.highlight = null;
              // }
              constructRcjArrayElement.donorMaterialPrice =donorMaterialPrice;
              updateMemory = true ;
            }

            // 修改税率  价格与税率使用输入值，后端不进行计算
            if (!ObjectUtils.isEmpty(taxRate)  || taxRate ==='') {
              constructRcjArrayElement.taxRate = taxRate ===''? 0 : taxRate
              //this.service.gongLiaoJiProject.gljRcjService.calculateTax(constructRcjArrayElement, taxMethod);
              deRowIds.add(constructRcjArrayElement.deRowId);
              updateMemory = true ;
            }

            if (ObjectUtil.isNotEmpty(marketTaxPrice)) {
              constructRcjArrayElement.marketTaxPrice = marketTaxPrice;
              updateMemory = true ;
            }
            // 修改含税市场价
            if (ObjectUtil.isNotEmpty(marketPrice)) {
              if (!dealedData.includes(constructRcjArrayElement.sequenceNbr)) {
                constructRcjArrayElement.marketPrice = marketPrice;
                constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
                constructRcjArrayElement.highlight = highlight;
                await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,donorMaterialPrice,3);
                //this.service.gongLiaoJiProject.gljRcjService.calculateTax(constructRcjArrayElement,1);
                updateMemory =true ;
                //修改的子
                if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
                  let t2 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
                  deRowIds.add(t2.deRowId);
                  await this.parentMaterialPrice(rcjDetailList, t2);
                } else {
                  deRowIds.add(constructRcjArrayElement.deRowId);
                }
                dealedData.push(constructRcjArrayElement.sequenceNbr);
              }
            }

            // if (ObjectUtil.isNotEmpty(marketTaxPrice)) {
            //   if (!dealedData.includes(constructRcjArrayElement.sequenceNbr)) {
            //     constructRcjArrayElement.marketTaxPrice = marketTaxPrice;
            //     constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
            //     constructRcjArrayElement.highlight = highlight;
            //     await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,donorMaterialPrice,0);
            //    // this.service.gongLiaoJiProject.gljRcjService.calculateTax(constructRcjArrayElement,0);
            //     updateMemory =true ;
            //     //修改的子
            //     if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
            //       let t2 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
            //       deRowIds.add(t2.deRowId);
            //       await this.parentMaterialPrice(rcjDetailList, t2);
            //     } else {
            //       deRowIds.add(constructRcjArrayElement.deRowId);
            //     }
            //     dealedData.push(constructRcjArrayElement.sequenceNbr);
            //   }
            // }

            //三材和三材系数
            if (!ObjectUtils.isEmpty(kindSc)) {
              constructRcjArrayElement.kindSc = kindSc;
              if (kindSc === "空") {
                constructRcjArrayElement.transferFactor = "";
                transferFactor = null;
                constructRcjArrayElement.scCount = null;
              }
              updateSC = true;
            }
            if (!ObjectUtils.isEmpty(transferFactor)) {
              constructRcjArrayElement.transferFactor = transferFactor;
              constructRcjArrayElement.scCount = NumberUtil.multiply(constructRcjArrayElement.totalNumber,constructRcjArrayElement.transferFactor);
              updateSC = true;
            }
            if (!ObjectUtils.isEmpty(remark) || remark === "") {
              constructRcjArrayElement.remark = remark;
            }

            //类型
            if (!ObjectUtils.isEmpty(kind)) {
              constructRcjArrayElement.kind = kind;
              for (let key in RcjTypeEnum) {
                if (RcjTypeEnum[key].code === kind) {
                  constructRcjArrayElement.type= RcjTypeEnum[key].desc;
                }
              }
              //材料
              if (kind == 2 || kind == 5) {
                constructRcjArrayElement.taxRemoval = constructRcjArrayElement.taxRemovalBackUp;
                //设备
              } else if (kind == 4) {
                constructRcjArrayElement.taxRemoval = RcjCommonConstants.SBFTAXREMOVAL;
              }
              ifChangeMaterialCode = true;
              //this.changeMaterialCode(constructRcjArrayElement,ts,max);
            }

            // 是否暂估
            if (ifLockStandardPrice === RcjCommonConstants.IFLOCKPRICE_DEFAULT && constructRcjArrayElement.ifProvisionalEstimate === 1) {
              ifProvisionalEstimate = 0
            }
            if (!ObjectUtils.isEmpty(ifProvisionalEstimate)) {
              constructRcjArrayElement.ifProvisionalEstimate = ifProvisionalEstimate;
              if (ifProvisionalEstimate === 1) {
                constructRcjArrayElement.lastSourcePrice = constructRcjArrayElement.sourcePrice;
                constructRcjArrayElement.sourcePrice = "";
                ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE
              } else {
                constructRcjArrayElement.sourcePrice = constructRcjArrayElement.lastSourcePrice;
                if(ObjectUtils.isEmpty(ifLockStandardPrice)){
                  ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT
                }

              }
            }

            if (!ObjectUtils.isEmpty(markSum)) {
              constructRcjArrayElement.markSum = markSum;

              constructRcjArrayElement.markSum = markSum;
              let rcjDetais = constructProjectRcjs.find(item => item.sequenceNbr === constructRcjArrayElement.sequenceNbr).pbs;
              if (ObjectUtils.isNotEmpty(rcjDetais)) {
                rcjDetais.forEach(item => item.markSum === markSum);
              }
              if(markSum===1){
                ifDonorMaterial =0;
              }

              let  unitAllMemory= this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId,null);
              if(ObjectUtils.isNotEmpty(unitAllMemory)){
                let  rcjMemorys = unitAllMemory.filter(item=>item.materialCode ===constructRcjArrayElement.materialCode);
                if(ObjectUtils.isNotEmpty(rcjMemorys)){
                  rcjMemorys.forEach(item=>{
                    item.markSum= constructRcjArrayElement.markSum;
                  });
                }
              }
              //同步到de
              if (constructRcjArrayElement.isDeResource === 1) {
                let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                let de = deDomain.getDeById(constructRcjArrayElement.deId);
                de.markSum = constructRcjArrayElement.markSum;
                await deDomain.updateDe(de);
              }
            }

            if (ObjectUtils.isNotEmpty(ifDonorMaterial)) {
              ifDonorMaterial= Number(ifDonorMaterial);
              constructRcjArrayElement.ifDonorMaterial = ifDonorMaterial;
              if (ifDonorMaterial == RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
                constructRcjArrayElement.donorMaterialNumber =null;
                constructRcjArrayElement.updateFalg = 0  ;
              }
              if(ifDonorMaterial  === 1 &&  ifDonorMaterial!== constructRcjArrayElement.ifDonorMaterial)
              {
                constructRcjArrayElement.donorMaterialPrice =null;
              }
              if ( ObjectUtils.isEmpty(constructRcjArrayElement.donorMaterialNumber)  && ifDonorMaterial != RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
                constructRcjArrayElement.donorMaterialNumber =  totalNumber;
              }
              updateMemory = true ;
            }

            if (ObjectUtils.isNotEmpty(donorMaterialNumber) && constructRcjArrayElement.ifDonorMaterial != RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
              donorMaterialNumber =Number(donorMaterialNumber);
              constructRcjArrayElement.donorMaterialNumber =  donorMaterialNumber;
              if(totalNumber != constructRcjArrayElement.donorMaterialNumber){
                constructRcjArrayElement.updateFalg = 1  ;
              }else {
                constructRcjArrayElement.updateFalg = 0  ;
              }
              updateMemory = true ;
            }

            if (!ObjectUtils.isEmpty(ifLockStandardPrice)) {
              constructRcjArrayElement.ifLockStandardPrice = ifLockStandardPrice;
              if (constructRcjArrayElement.isDeResource === 1) {
                let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                let de = deDomain.getDeById(constructRcjArrayElement.deId);
                de.ifLockStandardPrice = constructRcjArrayElement.ifLockStandardPrice;
                await deDomain.updateDe(de);
              }
              updateMemory = true ;
            }

            if (!ObjectUtils.is_Undefined(producer)) {
              constructRcjArrayElement.producer = producer;
            }

            if (!ObjectUtils.is_Undefined(manufactor)) {
              constructRcjArrayElement.manufactor = manufactor;
            }

            if (!ObjectUtils.is_Undefined(brand)) {
              constructRcjArrayElement.brand = brand;
            }

            if (!ObjectUtils.is_Undefined(deliveryLocation)) {
              constructRcjArrayElement.deliveryLocation = deliveryLocation;
            }

            if (!ObjectUtils.is_Undefined(qualityGrade)) {
              constructRcjArrayElement.qualityGrade = qualityGrade;
            }

            if(updateMemory){
                this.updateMemoryRcj(constructRcjArrayElement,null);
            }

            //修改编码
            if (ifChangeMaterialCode) {
              //if( !((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark)?constructRcjArrayElement.levelMark: ResourceConstants.LEVEL_MARK_NONE_PB)!==  ResourceConstants.LEVEL_MARK_NONE_PB && constructRcjArrayElement.markSum === RcjCommonConstants.MARKSUM_JX)){
              if( !((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark)?constructRcjArrayElement.levelMark: ResourceConstants.LEVEL_MARK_NONE_PB)!==  ResourceConstants.LEVEL_MARK_NONE_PB )){
                await this.changeMaterialCodeMemory(constructRcjArrayElement, false, constructRcjArray);
                //this.service.gongLiaoJiProject.gljRcjService.processingMarketPrice(constructRcjArrayElement);
              } else {
                await this.parentMaterialCodeChangeMemory(constructProjectRcjs , constructRcjArrayElement,false);
              }
              // if(constructRcjArrayElement.markSum === RcjCommonConstants.MARKSUM_JX && constructRcjArrayElement.hasOwnProperty('levelMark') && constructRcjArrayElement.levelMark!== ResourceConstants.LEVEL_MARK_NONE_PB){
              //   await this.parentMaterialCodeChangeMemory(constructProjectRcjs , constructRcjArrayElement,false);
              // }
              //修改父级编码
              //修改的子
              if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
                let t1 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
                await this.parentMaterialCodeChangeMemory(constructProjectRcjs, t1,false);
              }
              if (constructRcjArrayElement.isDeResource === 1) {
                let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                let de = deDomain.getDeById(constructRcjArrayElement.deId);
                de.deCode = constructRcjArrayElement.materialCode;
                await deDomain.updateDe(de);
              }
            }
            //修改批注
            if (ObjectUtils.isNotEmpty(annotationsPro) || annotationsPro === "") {
              constructRcjArrayElement.annotationsPro = annotationsPro;
            }
            //修改批注 是否展示
            if (ObjectUtils.isNotEmpty(isShowAnnotationsPro)) {
              constructRcjArrayElement.isShowAnnotationsPro = isShowAnnotationsPro;
            }
            //修改批注-单项
            if (ObjectUtils.isNotEmpty(annotationsSingle) || annotationsSingle === "") {
              constructRcjArrayElement.annotationsSingle = annotationsSingle;
              if (ObjectUtils.isEmpty(constructRcjArrayElement.annotationsSingleObj)) {
                constructRcjArrayElement.annotationsSingleObj = {}
              }
              let isShow = constructRcjArrayElement.annotationsSingleObj[singleId]?.isShowAnnotationsSingle
              constructRcjArrayElement.annotationsSingleObj[singleId] = {annotationsSingle, isShowAnnotationsSingle: isShow};
            }
            //修改批注 是否展示
            if (ObjectUtils.isNotEmpty(isShowAnnotationsSingle)) {
              constructRcjArrayElement.isShowAnnotationsSingle = isShowAnnotationsSingle;
              if (ObjectUtils.isEmpty(constructRcjArrayElement.annotationsSingleObj)) {
                constructRcjArrayElement.annotationsSingleObj = {}
              }
              let anno = constructRcjArrayElement.annotationsSingleObj[singleId]?.annotationsSingle
              constructRcjArrayElement.annotationsSingleObj[singleId] = {annotationsSingle: anno, isShowAnnotationsSingle};
            }
            //供应时间
            if (ObjectUtils.isNotEmpty(supplyTime)) {
              constructRcjArrayElement.supplyTime = supplyTime;
            }

          }
        }
      }

    }
    if(ObjectUtil.isNotEmpty(deRowIds)){
      for( let deRowId of deRowIds ){
        let  de=ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === deRowId);
        if(ObjectUtil.isNotEmpty(de)){
          let unitId = de.unitId;
          await  ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId,unitId,deRowId  },false);
        }
      }
    }
    for (let i = 0; i < unitIds.length; i++){
      try {
        await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
          unitId: unitIds[i],
          singleId: null,
          constructId: constructId
        });
      } catch (error) {
        console.error("捕获到异常:", error);
      }
      try {
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          unitId:  unitIds[i],
          qfMajorType: null
        });
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }

    //若三材发生变化，联动计算建设其他费钢筋数量
    if (updateSC) {
      await this.service.gongLiaoJiProject.gljRcjCollectService.updateOtherProjectScGJ(constructId);
    }

  }

  /**
   *  无价差
   * @param args
   * @returns {Promise<boolean>}
   */
  async updateRcjCellectNoPriceDifference(args) {
    let sequenceNbrs = args.sequenceNbrs;
    let { scope, constructId, singleId, unitId, levelType , rcjList} = args;
    let operatorRcj = [];
    if ('1' === scope) {
      sequenceNbrs = rcjList.map(item => item.sequenceNbr);
    }
    if (ProjectTypeConstants.PROJECT_TYPE_PROJECT === levelType) {
      let rcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.WILDCARD);
      await this.updatePriceDifference(constructId,scope, rcjs, sequenceNbrs, operatorRcj);
    }

    if (ProjectTypeConstants.PROJECT_TYPE_UNIT === levelType) {
      let rcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
      await this.updatePriceDifference( constructId,scope, rcjs, sequenceNbrs, operatorRcj);
    }

    if (ProjectTypeConstants.PROJECT_TYPE_SINGLE === levelType) {
      let units = [];
      units = await this.service.gongLiaoJiProject.gljProjectService.calProjectSingleUnits(constructId, singleId, units);
      if (ObjectUtils.isNotEmpty(units)) {
        let rcjs = [];
        for (let item of units) {
          let rcjsL = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(item) + WildcardMap.WILDCARD);
          if (ObjectUtils.isNotEmpty(rcjsL)) {
            rcjs = rcjs.concat(rcjsL);
          }
        }
        await this.updatePriceDifference(constructId, scope, rcjs, sequenceNbrs, operatorRcj);
      }
    }
    let uniqueUnitIds = [...new Set(operatorRcj.map(item => item.unitId))];
    for (let unitId of uniqueUnitIds) {
      // 自动计算=各种记取+费用汇总通知
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
    }
    return ResponseData.success();
  }





  /**
   * 无价差数据处理
   * @param type
   * @param rcjList
   * @param operatorRcj
   * @returns {ResponseData}
   */
  async updatePriceDifference(constructId,scope, rcjAllList, sequenceNbrs, operatorRcj) {
    let rcjDetailAllList = new Array();
    let rcjList = new Array();
    let rcjDetailList = new Array();
    let rcjs = new Array();
    let rcjDetails = new Array();
    let scopeAll = new Array();
    let  deRowIds = new Set();
   // let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    rcjAllList.forEach(item => {
      if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO) {
        item.pbs.forEach(item => rcjDetailAllList.push(item));
      }
    });

    if (scope === RcjCommonConstants.SCOPE_ALL) {
      rcjAllList.forEach((rcj) => {
        let flag = this._getMarketPrice(rcj) !== this._getBaseJournalPrice(rcj)
        if (rcj.ifLockStandardPrice === RcjCommonConstants.IFLOCKPRICE_DEFAULT && flag) {
          scopeAll.push(rcj.sequenceNbr);
        }
      })

      rcjDetailAllList.forEach((rcj) => {
        let flag = this._getMarketPrice(rcj) !== this._getBaseJournalPrice(rcj)
        if (rcj.ifLockStandardPrice === RcjCommonConstants.IFLOCKPRICE_DEFAULT && flag) {
          scopeAll.push(rcj.sequenceNbr);
        }
      });
      sequenceNbrs = scopeAll;
    }

    sequenceNbrs.forEach(item => {
      rcjDetailAllList.find(rcjDetail => {
        if (rcjDetail.sequenceNbr === item) {
          rcjDetailList.push(rcjDetail);
        }
      });
      rcjAllList.find(rcj => {
        if (rcj.sequenceNbr === item) {
          rcjList.push(rcj);
        }
      });
    });
    //找出所有符合要求 rcj
    rcjList.forEach(item => {
      rcjAllList.forEach(rcj => {
        if (rcj.materialCode === item.materialCode
            && rcj.materialName === item.materialName
            && rcj.specification === item.specification
            && rcj.unit === item.unit
            && this._getBaseJournalPrice(rcj) === this._getBaseJournalPrice(item)
            && rcj.markSum === item.markSum
            && rcj.ifDonorMaterial === item.ifDonorMaterial
            && rcj.ifProvisionalEstimate === item.ifProvisionalEstimate
            && this._getMarketPrice(rcj) === this._getMarketPrice(item)
            && rcj.ifLockStandardPrice === item.ifLockStandardPrice) {
          rcjs.push(rcj);
        }
      });
      rcjDetailAllList.forEach(rcj => {
        if (rcj.materialCode === item.materialCode
            && rcj.materialName === item.materialName
            && rcj.specification === item.specification
            && rcj.unit === item.unit
            && this._getBaseJournalPrice(rcj) === this._getBaseJournalPrice(item)
            && rcj.markSum === item.markSum
            && rcj.ifDonorMaterial === item.ifDonorMaterial
            && rcj.ifProvisionalEstimate === item.ifProvisionalEstimate
            && this._getMarketPrice(rcj) === this._getMarketPrice(item)
            && rcj.ifLockStandardPrice === item.ifLockStandardPrice) {
          rcjDetails.push(rcj);
        }
      });
    })
    // 找出所有符合要求 rcj  明细
    rcjDetailList.forEach(item => {
      rcjAllList.forEach(rcj => {
        if (rcj.materialCode === item.materialCode
            && rcj.materialName === item.materialName
            && rcj.specification === item.specification
            && rcj.unit === item.unit
            && this._getBaseJournalPrice(rcj) === this._getBaseJournalPrice(item)
            && rcj.markSum === item.markSum
            && rcj.ifDonorMaterial === item.ifDonorMaterial
            && rcj.ifProvisionalEstimate === item.ifProvisionalEstimate
            && this._getMarketPrice(rcj) === this._getMarketPrice(item)
            && rcj.ifLockStandardPrice === item.ifLockStandardPrice) {
          rcjs.push(rcj);
        }
      });
      rcjDetailAllList.forEach(rcj => {
        if (rcj.materialCode === item.materialCode
            && rcj.materialName === item.materialName
            && rcj.specification === item.specification
            && rcj.unit === item.unit
            && this._getBaseJournalPrice(rcj) === this._getBaseJournalPrice(item)
            && rcj.markSum === item.markSum
            && rcj.ifDonorMaterial === item.ifDonorMaterial
            && rcj.ifProvisionalEstimate === item.ifProvisionalEstimate
            && this._getMarketPrice(rcj) === this._getMarketPrice(item)
            && rcj.ifLockStandardPrice === item.ifLockStandardPrice) {
          rcjDetails.push(rcj);
        }
      })
    })
    operatorRcj.push(...ConvertUtil.deepCopy(rcjs));
    for (let rcj of rcjs) {
      if (rcj.ifLockStandardPrice === RcjCommonConstants.IFLOCKPRICE) {
        continue;
      }
      if(this._getMarketPrice(rcj) !== this._getBaseJournalPrice(rcj)){
        rcj.sourcePrice = ''
        rcj.highlight = false;
      }
      this._setMarketPrice(rcj, this._getBaseJournalPrice(rcj))

      rcj.donorMaterialPrice = null;
      //修改内存
      this.updateMemoryRcj(rcj,rcj.unitId);
      deRowIds.add(rcj.deRowId);
      ProjectDomain.getDomain(rcj.constructId).getResourceDomain().createResource(rcj.unitId, rcj.deRowId, rcj)
    }
    //rcjDetails.forEach((rcjDetail) => {
    for (let i = 0; i < rcjDetails.length; i++) {
      let  rcjDetail=rcjDetails[i];
      if (rcjDetail.ifLockStandardPrice === RcjCommonConstants.IFLOCKPRICE) {
        continue;
      }

      if(this._getMarketPrice(rcjDetail) !== this._getBaseJournalPrice(rcjDetail)){
        rcjDetail.sourcePrice = ''
      }
      this._setMarketPrice(rcjDetail, this._getBaseJournalPrice(rcjDetail))

      rcjDetail.donorMaterialPrice = null;
      let rcj = rcjAllList.find(item => item.sequenceNbr === rcjDetail.parentId);
      deRowIds.add(rcj.deRowId);
      await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialPrice(rcjDetailAllList, rcj);
      ProjectDomain.getDomain(rcj.constructId).getResourceDomain().createResource(rcj.unitId, rcj.deId, rcj)
    }

    if(ObjectUtil.isNotEmpty(deRowIds)){
      for( let deRowId of deRowIds ){
        let  de=ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === deRowId);
        if(ObjectUtil.isNotEmpty(de)){
          let unitId = de.unitId;
          await  ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId,unitId,deRowId  },false);
        }
      }
    }
    return ResponseData.success();
  }

  async getRCJProjectTreeMenu(args) {
    let { constructId } = args;
    let result=ProjectDomain.getDomain(constructId).getProjectTree();
    return ResponseData.success(result) ;
  }

  async updateRCJProjectTreeMenu(args) {
    let { constructId,sequenceNbrs ,type } = args;
    let result=ProjectDomain.getDomain(constructId).getProjectTree();
    if(type  ===  RcjCommonConstants.MEUN_TYPE_INIT){
      result.forEach(item=>
      {
        item.scopeFlag = true
        ProjectDomain.getDomain(constructId).updateProject(item);
      }) ;
    }
    if(type  ===  RcjCommonConstants.MEUN_TYPE_UPDATE){
      result.forEach(item=> {
        if(sequenceNbrs.includes(item.sequenceNbr)){
          item.scopeFlag = true
        }else {
          item.scopeFlag = false ;
        }
        ProjectDomain.getDomain(constructId).updateProject(item);
      }) ;
    }
    return ResponseData.success() ;
  }

  /**
   * 鼠标右键查询关联定额树结构
   * @param args
   * @return {Promise<ResponseData>}
   */
  async getRelationTree(args) {
    let { constructId,singleId,rcj } = args;
    let projectTree=ProjectDomain.getDomain(constructId).getProjectTree();
    let rcjKey = WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    let  idSet =new  Set();
    var pattern = /#\d+/g;
    let taxMethod = ProjectDomain.getDomain(args.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

    if (rcj.materialCode?.includes('QTCLF1')) {
      rcj.unit = '%'
    }
    if (rcj.isFyrcj === 0
      &&　 (rcj.materialCode.replace( /#\d+/g, '') !== 'RGFTZ'&& rcj.materialCode.replace( /#\d+/g, '') !== 'JXFTZ' && rcj.materialCode.replace( /#\d+/g, '') !== 'CLFTZ' )
    ) {
      rcj.unit = '%'
    }

    rcjList.forEach(item => {
      if( item.materialCode.replace(pattern, '')=== rcj.materialCode
          && item.materialName === rcj.materialName
          && item.specification === rcj.specification
          && item.unit === rcj.unit
          && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcj)
          && item.markSum === rcj.markSum
          && this._getMarketPrice(item) === this._getMarketPrice(rcj)
          && String(item.ifDonorMaterial) === String(rcj.ifDonorMaterial)
      ){
        idSet.add(item.unitId) ;
      }
      if(item.levelMark  != RcjCommonConstants.LEVELMARK_ZERO  &&  ObjectUtils.isNotEmpty(item.pbs)   && item.markSum === RcjCommonConstants.MARKSUM_JX  ){
        item.pbs.forEach(item2 => {
          if(item.materialCode.replace(pattern, '') === rcj.materialCode
              && item2.materialName === rcj.materialName
              && item2.specification === rcj.specification
              && item2.unit === rcj.unit
              && this._getBaseJournalPrice(item2) === this._getBaseJournalPrice(rcj)
              && item2.markSum === rcj.markSum
              && this._getMarketPrice(item2) === this._getMarketPrice(rcj)
          ){
            idSet.add(item.unitId) ;
          }
        });
      }

    });
    // let result = projectTree.filter(item =>{
    //   return  idSet.has(item.sequenceNbr) || item.type !=3
    // })
    let result = this.filterTreeWithParents(projectTree, idSet);
    if(ObjectUtils.isNotEmpty(singleId) && ObjectUtils.isNotEmpty(result)){
      let  resultFilterArray =new  Array();
      for( let  unitId of idSet){
        let  filterArray =new  Array();
        let   curObj =result.find( item=>item.sequenceNbr === unitId);
       let  curSingleId= this.parentCollect(curObj,result,filterArray);
       if(curSingleId ===singleId){
         resultFilterArray.push(...filterArray);
       }
      }
      // result = resultFilterArray;
      result = resultFilterArray.filter((item, index, self) =>
          index === self.findIndex(t => (t.sequenceNbr === item.sequenceNbr))
      );
      // 如果是单项工程，去掉工程项目层级
      result = result.filter(item => item.type !== ProjectTypeConstants.PROJECT_TYPE_PROJECT)
    }
    return ResponseData.success(result) ;
  }

  /**
   * 从扁平树结构中过滤出指定节点及其所有父级节点
   * @param {Array} projectTree - 扁平的树结构数组
   * @param {Array} idSet - 需要过滤的节点sequenceNbr数组
   * @returns {Array} - 过滤后的树结构数组
   */
  filterTreeWithParents(projectTree, idSet) {
    if (ObjectUtils.isEmpty(projectTree) || ObjectUtils.isEmpty(idSet)) {
      return [];
    }
    // 创建节点映射，便于快速查找
    const nodeMap = {};
    projectTree.forEach(node => {
      nodeMap[node.sequenceNbr] = node;
    });
    // 存储需要保留的节点ID集合
    const keepIds = new Set();
    // 递归查找所有父节点
    function findAllParents(id) {
      if (!id || keepIds.has(id)) return;

      keepIds.add(id);
      const node = nodeMap[id];
      if (node && node.parentId) {
        findAllParents(node.parentId);
      }
    }
    // 对每个目标ID，查找其自身和所有父节点
    idSet.forEach(id => {
      findAllParents(id);
    });

    // 过滤出需要保留的节点
    return projectTree.filter(node => keepIds.has(node.sequenceNbr));
  }

  /**
   * 鼠标右键 查询关联定额
   * @param args
   * @return {Promise<ResponseData>}
   */
  async getRelationDe(args) {
    let {constructId, unitId,rcj} = args;
    let precision =  this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let deTree=  ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId ).map(obj =>({
      code : obj.deCode,
      name :obj.deName,
      sequenceNbr :obj.sequenceNbr,
      deRowId :obj.deRowId,
      parentId :obj.parentId,
      type :obj.type,
      quantity :obj.quantity,
      topFlag: false
    }))
    let taxMethod = ProjectDomain.getDomain(args.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    var pattern = /#\d+/g;
    let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    let  deRowIds =new  Set();
    if(rcj.isFyrcj === 0
        &&　 (rcj.materialCode.replace( /#\d+/g, '') !== 'RGFTZ'&& rcj.materialCode.replace( /#\d+/g, '') !== 'JXFTZ' && rcj.materialCode.replace( /#\d+/g, '') !== 'CLFTZ' )
    ){
      rcj.unit = '%'
    }
    rcjList.forEach(item => {
      let deLine = ProjectDomain.getDomain(constructId).deDomain.getDeById(item.deRowId);
      let isCsxmDe = 0;
      if(ObjectUtils.isEmpty(deLine)){
        deLine = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(item.deRowId);
        isCsxmDe = 1;
      }
      if( item.materialCode.replace(pattern, '')=== rcj.materialCode.replace(pattern, '')
          && item.materialName === rcj.materialName
          && item.specification === rcj.specification
          && item.unit === rcj.unit
          && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcj)
          && item.markSum === rcj.markSum
          && this._getMarketPrice(item) === this._getMarketPrice(rcj)
      ){
          let   num = NumberUtil.numberScale(item.totalNumber, precision.DETAIL.RCJ.totalNumber);
        deRowIds.add({deRowId:item.deRowId,totalNumber:num,isCsxmDe: isCsxmDe}) ;
      }
      if(item.levelMark  != RcjCommonConstants.LEVELMARK_ZERO  &&  ObjectUtils.isNotEmpty(item.pbs)   && item.markSum === RcjCommonConstants.MARKSUM_JX  ){
        item.pbs.forEach(item2 => {
          if( item2.materialCode.replace(pattern, '')=== rcj.materialCode
              && item2.materialName === rcj.materialName
              && item2.specification === rcj.specification
              && item2.unit === rcj.unit
              && this._getBaseJournalPrice(item2) === this._getBaseJournalPrice(rcj)
              && item2.markSum === rcj.markSum
              && this._getMarketPrice(item2) === this._getMarketPrice(rcj)
          ){
            let   num =  NumberUtil.numberScale(item2.totalNumber, precision.DETAIL.RCJ.totalNumber)
            deRowIds.add({deRowId:item.deRowId,totalNumber:num,isCsxmDe: isCsxmDe}) ;
          }
        });
      }
    });
    deRowIds = Array.from(deRowIds);
    deRowIds.sort((a, b) => {
      return a.isCsxmDe - b.isCsxmDe;
    });
    let allDeRowIds= new Set();
    let  allDeMap=new Map();
    //向上查询所有deRowId/ 找到所有de子分部
    let zfbs = new Set();
    let dfsMap=new  Map();
    deRowIds.forEach(item => {
      //动态多叉哈弗曼
      this.findAllDe(deTree,item,allDeRowIds,zfbs,allDeMap,dfsMap)
    });
    //子分部收集器
    let   allDeArray=Array.from( allDeMap.values());
    allDeArray.forEach(item=>item.totalNumber = NumberUtil.numberScale(item.totalNumber, 5))
    //let zfbAndSub=new Array();
    let SubZFbArray=new Array();
    zfbs.forEach(item => {
      //向上收集
      let zfb =allDeArray.find(item2=>item2.sequenceNbr === item);
      let  name=this.findFb(allDeArray,zfb);
      SubZFbArray.push({name:name ,totalNumber:zfb.totalNumber, topFlag: true});
      //向下收集
      this.findFBDe(allDeArray,item,SubZFbArray);
      //zfbAndSub.push(SubZFbArray)
    });

    return    ResponseData.success(SubZFbArray) ;
  }

  /**
   *  动态多叉哈弗曼，查询所有deRowId并统计totalNumber
   * @param deTree
   * @param parentId
   * @param allDeRowIds
   */
  findAllDe(deTree,de,allDeRowIds,zfbs,allDeMap,dfsMap){
    if(de.deRowId  === 0){
      return   null ;
    }
    let  result=deTree.find(item => item.sequenceNbr ===de.deRowId)
    allDeRowIds.add(result.deRowId);
    let  deValue=allDeMap.get(result.sequenceNbr);
    if(ObjectUtils.isNotEmpty(de.totalNumber)){
      if(ObjectUtils.isEmpty(deValue)){
        result.totalNumber = de.totalNumber ;
        deValue=result;
        allDeMap.set(result.sequenceNbr,result);
        dfsMap.set(result.sequenceNbr,[{id :de.subId,totalNumber: de.totalNumber}]) ;
      }else{
        let dfsArray=dfsMap.get(result.sequenceNbr);
        if(ObjectUtils.isNotEmpty(de.subId) && ObjectUtils.isNotEmpty(dfsArray.find(item => item.id  ===de.subId))){
          let  sub= dfsArray.findLast(item => item.id  ===de.subId);
          deValue.totalNumber =deValue.totalNumber- sub.totalNumber + de.totalNumber;
          dfsArray.push({id :de.subId,totalNumber: de.totalNumber});
          dfsMap.set(result.sequenceNbr,dfsArray) ;
        }else{
          deValue.totalNumber = deValue.totalNumber + de.totalNumber;
          dfsArray.push({id :de.subId,totalNumber: de.totalNumber});
          dfsMap.set(result.sequenceNbr,dfsArray) ;
        }
      }
    }
    let  parent=deTree.find(item => item.sequenceNbr ===result.parentId);
    if(ObjectUtils.isNotEmpty(parent)){
        if(parent.type  ===DeTypeConstants.DE_TYPE_DEFAULT && result.type === DeTypeConstants.DE_TYPE_ZFB  ){
            result.type = DeTypeConstants.DE_TYPE_FB ;
        }
      if( result.type  !==DeTypeConstants.DE_TYPE_ZFB   &&   parent.type  ===DeTypeConstants.DE_TYPE_ZFB  || (result.type  !==DeTypeConstants.DE_TYPE_ZFB && parent.type  ===DeTypeConstants.DE_TYPE_FB)||(result.type  !==DeTypeConstants.DE_TYPE_FB && parent.type  ===DeTypeConstants.DE_TYPE_DEFAULT)){
        zfbs.add(parent.sequenceNbr);
      }
    }

    this.findAllDe(deTree, {deRowId:result.parentId,totalNumber: deValue.totalNumber,subId:result.sequenceNbr},allDeRowIds,zfbs,allDeMap,dfsMap);
  }

  /**
   * 查询所有zfb 定额
   * @param deArray
   * @param nbr
   * @param SubFbArray
   * @returns {null}
   */
  findFBDe(deArray,nbr,SubFbArray){
    let  results=deArray.filter(item => item.parentId === nbr);
    if(ObjectUtils.isEmpty(results)){
      return  null
    }
    results.forEach(item =>{
      SubFbArray.push(item);
      this.findFBDe(deArray,item.sequenceNbr ,SubFbArray);
    });
  }

  /**
   * 收集分部信息
   * @param allDeArray
   * @param sequenceNbr
   */
  findFb(allDeArray,zfb){
    if(ObjectUtils.isEmpty(zfb)){
      return '';
    }
    let  parentFb=allDeArray.find(item =>item.sequenceNbr===zfb.parentId);
    if(zfb.type==='01'){
      return  ObjectUtils.isEmpty(zfb.name)?' ':zfb.name ;
    }
    let  feName= this.findFb(allDeArray, parentFb );
    return   feName+'/'+ (ObjectUtils.isEmpty(zfb.name)?'':zfb.name)
  }

  /**
   * 人材机汇总——过滤——查询颜色
   * @param args
   * @returns {Promise<void>}
   */
  async getFiltrationColor(args) {
    let {constructId, unitId,levelType} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    let  keys=Array.from(rcjMap.keys()).filter(item => item.includes('UNIT_COLOR'));
    let  colorSet = new Set();
    keys.forEach(key => {
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
        let colorArr=Array.from(rcjMap.get(key).values()) ;
        colorArr.forEach(item =>colorSet.add(item));
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
        let color=rcjMap.get(key).get(unitId)
        if(ObjectUtils.isNotEmpty(color)){
          colorSet.add(color);
        }
      }
    });
    return   ResponseData.success(Array.from(colorSet)) ;
  }


  async temporaryDataSave(args) {
    let { constructId, temporaryData} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
    }
    let temporaryMap=objMap.get(FunctionTypeConstants.TEMPORARY_DATA);
    if (ObjectUtils.isEmpty(temporaryMap)) {
      temporaryMap = new Map();
    }
    temporaryMap.set(temporaryData.sequenceNbr  ,temporaryData)
    objMap.set(FunctionTypeConstants.TEMPORARY_DATA,temporaryMap)
    businessMap.set(FunctionTypeConstants.RCJ_COLLECT,objMap)
    return   ResponseData.success() ;
  }

  async temporaryDataGet(args) {
    let { constructId} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
    }
    let   resultMap= objMap.get(FunctionTypeConstants.TEMPORARY_DATA);
    if (ObjectUtils.isEmpty(resultMap)) {
      return   ResponseData.success(null) ;
    }
    let  resultList=Array.from(resultMap.values());
    return   ResponseData.success(resultList) ;
  }


  async temporaryDataDel(args) {
    let { constructId} = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
    }
    let   resultList= objMap.set(FunctionTypeConstants.TEMPORARY_DATA,null);
    return   ResponseData.success(resultList) ;
  }

  /**
   * 单位工程——合并相似材料-查询
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getUnitMergeMaterials(args) {
    let { levelType, kind, constructId, singleId, unitId, sort , isShowAnnotations,code,name,userDefined} = args;
    let List =await  this.getRcjCellectData(args);
    let resultList = new Array();
    List.forEach(item=>{
      let constructProjectRcj = new GljConstructProjectRcj();
      ConvertUtil.setDstBySrc(item, constructProjectRcj);
      resultList.push(constructProjectRcj);
    })
    let   alldata=new Array();
    if(ObjectUtils.isNotEmpty(resultList)){
      resultList = resultList.filter(item=>item.isDeCompensation !== CommonConstants.COMMON_YES);
    }
    if(ObjectUtils.isNotEmpty(code)){
      //分组
      const grouped = resultList.reduce((accumulator, currentValue) => {
        // 将分组作为对象的 key，相同分组的项放入同一个数组
        (accumulator[currentValue.materialCode.replace( /#\d+/g, '')] = accumulator[currentValue.materialCode.replace( /#\d+/g, '')] || []).push(currentValue);
        return accumulator;
      }, {});
      //循环分组之后的人材机
      for (let group in grouped) {
        if (grouped.hasOwnProperty(group)) {
          if( grouped[group].length>=2){
            let constructProjectRcj = new GljConstructProjectRcj();
            constructProjectRcj.sequenceNbr =Snowflake.nextId();
            constructProjectRcj.parent ='0'  ;
            constructProjectRcj.name =grouped[group][0].materialCode.replace( /#\d+/g, '') ;
            grouped[group].forEach(item => {
              item.parent=constructProjectRcj.sequenceNbr;
              alldata.push(item);
            })
            alldata.push(constructProjectRcj);
          }
        }
      }

    }
    if(ObjectUtils.isNotEmpty(name)){
      //分组
      const grouped = resultList.reduce((accumulator, currentValue) => {
        // 将分组作为对象的 key，相同分组的项放入同一个数组
        (accumulator[currentValue.materialName] = accumulator[currentValue.materialName] || []).push(currentValue);
        return accumulator;
      }, {});

      //循环分组之后的人材机
      for (let group in grouped) {
        if (grouped.hasOwnProperty(group)) {
          if( grouped[group].length>=2){
            let constructProjectRcj = new GljConstructProjectRcj();
            constructProjectRcj.sequenceNbr =Snowflake.nextId();
            constructProjectRcj.parent ='0'  ;
            constructProjectRcj.name =grouped[group][0].materialName ;
            grouped[group].forEach(item => {
              item.parent=constructProjectRcj.sequenceNbr;
              alldata.push(item);
            })
            alldata.push(constructProjectRcj);
          }
        }
      }

    }
    if(ObjectUtils.isNotEmpty(userDefined)){
      resultList =resultList.filter(item=>{
        return  item.materialName.includes( userDefined)|| ( ObjectUtils.isNotEmpty(item.specification ) && item.specification.includes(userDefined))
      } )
      if(resultList.length>=2){
        let constructProjectRcj = new GljConstructProjectRcj();
        constructProjectRcj.sequenceNbr =Snowflake.nextId();
        constructProjectRcj.parent ='0'  ;
        constructProjectRcj.name =userDefined ;
        resultList.forEach(item=>{
          item.parent=constructProjectRcj.sequenceNbr;
          alldata.push(item);
        });

        alldata.push(constructProjectRcj);
      }
    }
    return   ResponseData.success(alldata) ;
  }


  /**
   * 工程项目——合并相似材料-查询
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getProjectMergeMaterials(args) {
    let { levelType, kind, constructId, singleId, unitId, sort , isShowAnnotations,code,name} = args;
    let List =await this.getRcjCellectData(args);
    List = List.filter(item=>item.isDeCompensation !== CommonConstants.COMMON_YES);
    //List =List.filter(item => item.isDeResource !== CommonConstants.COMMON_YES && item.isDeCompensation !== CommonConstants.COMMON_YES);
    let resultList = new Array();
    List.forEach(item=>{
      let constructProjectRcj = new GljConstructProjectRcj();
      ConvertUtil.setDstBySrc(item, constructProjectRcj);
      resultList.push(constructProjectRcj);
    })
    let   alldata=new Array();
    if(ObjectUtils.isNotEmpty(code)){
      //分组
      const grouped = resultList.reduce((accumulator, currentValue) => {
        // 将分组作为对象的 key，相同分组的项放入同一个数组
        (accumulator[currentValue.materialCode.replace( /#\d+/g, '')+ currentValue.markSum] = accumulator[currentValue.materialCode.replace( /#\d+/g, '')+ currentValue.markSum] || []).push(currentValue);
        return accumulator;
      }, {});
      //循环分组之后的人材机
      for (let group in grouped) {
        if (grouped.hasOwnProperty(group)) {
          if( grouped[group].length>=2){
            let constructProjectRcj = new GljConstructProjectRcj();
            constructProjectRcj.sequenceNbr =Snowflake.nextId();
            constructProjectRcj.parent ='0'  ;
            constructProjectRcj.name =grouped[group][0].materialCode.replace( /#\d+/g, '') ;
            grouped[group].forEach(item => {
              item.parent=constructProjectRcj.sequenceNbr;
              alldata.push(item);
            })
            alldata.push(constructProjectRcj);
          }
        }
      }

    }
    if(ObjectUtils.isNotEmpty(name)){
      //分组
      const grouped = resultList.reduce((accumulator, currentValue) => {
        // 将分组作为对象的 key，相同分组的项放入同一个数组
        (accumulator[currentValue.materialName] = accumulator[currentValue.materialName] || []).push(currentValue);
        return accumulator;
      }, {});

      //循环分组之后的人材机
      for (let group in grouped) {
        if (grouped.hasOwnProperty(group)) {
          if( grouped[group].length>=2){
            let constructProjectRcj = new GljConstructProjectRcj();
            constructProjectRcj.sequenceNbr =Snowflake.nextId();
            constructProjectRcj.parent ='0'  ;
            constructProjectRcj.name =grouped[group][0].materialName ;
            grouped[group].forEach(item => {
              item.parent=constructProjectRcj.sequenceNbr;
              alldata.push(item);
            })
            alldata.push(constructProjectRcj);
          }
        }
      }

    }
    let result = null;
    if(ObjectUtils.isNotEmpty(alldata)){
      result = alldata.filter(item=>item.isDeCompensation !== CommonConstants.COMMON_YES);
    }
    return   ResponseData.success(result) ;
  }

  /**
   * 单位工程——合并相似材料-保存
   * @param args
   * @returns {Promise<void>}
   */
  async saveMergeMaterials(args){
    let { levelType,constructId,singleId, unitId,rcjs} = args;
    let constructRcjArray = new Array();
    let rcjList = null;
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      let idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3 && item.scopeFlag === true  ).map(item=>item.sequenceNbr);
      if(ObjectUtils.isEmpty(idArray)){
        return   null  ;
      }
      rcjList=ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item=>idArray.includes(item.unitId))
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
      rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.constructId === constructId && item.type === 3);
      let unitIds = this.checkoutParent(unitProjects , singleId);
      rcjList=ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item=>unitIds.includes(item.unitId))
    }

    if (ObjectUtils.isNotEmpty(rcjList)) {
      for (let constructProjectRcj of rcjList) {
        if (constructProjectRcj.totalNumber !== 0) {
          let constructProjectRcj1 = new GljConstructProjectRcj();
          ConvertUtil.setDstBySrc(constructProjectRcj, constructProjectRcj1);
          constructProjectRcj1.standardId = constructProjectRcj.rcjId;
          constructRcjArray.push(constructProjectRcj1);
        }
      }
    } else {
      return null;
    }
    // 二次解析
    let ts1 = rcjList.filter(i => i.markSum === 1);
    if (!ObjectUtils.isEmpty(ts1)) {
      for (let t of ts1) {
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          for (let t1 of ts2) {
            if ( ObjectUtil.isNotEmpty(t1.totalNumber) && t1.totalNumber !== 0) {
              let constructProjectRcj = new GljConstructProjectRcj();
              ConvertUtil.setDstBySrc(t1, constructProjectRcj);
              constructProjectRcj.standardId = t.rcjId;
              constructRcjArray.push(constructProjectRcj);
            }
          }
        }
      }
    }
    for (let rcjArray of rcjs) {
      let rcjTarget = rcjArray.find(item => item.mergeTarget === true);
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        let originRcjTarget=constructRcjArray.find(item=>item.sequenceNbr === rcjTarget.sequenceNbr);
        rcjTarget.materialCode=originRcjTarget.materialCode;
      }
      if((rcjTarget.levelMark === ResourceConstants.LEVEL_MARK_PB_CL  ||rcjTarget.levelMark === ResourceConstants.LEVEL_MARK_PB_JX )&& rcjTarget.markSum === RcjCommonConstants.MARKSUM_JX ){
        rcjTarget=ConvertUtil.deepCopy(rcjList.find(item =>item.sequenceNbr ===rcjTarget.sequenceNbr));
      }
      for (let rcj of rcjArray) {
        if (rcj.mergeTarget === false) {
          let replaceRcjs;
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            replaceRcjs = constructRcjArray.filter(item => {
              return item.materialCode.replace( /#\d+/g, '') === rcj.materialCode
                  && item.materialName === rcj.materialName
                  && item.specification === rcj.specification
                  && item.unit === rcj.unit
                  && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcj)
                 //  && item.dePrice === rcj.dePrice
                  && item.markSum === rcj.markSum
                  && item.ifDonorMaterial === rcj.ifDonorMaterial
                  && item.ifProvisionalEstimate === rcj.ifProvisionalEstimate
            });
          }
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            replaceRcjs = constructRcjArray.filter(item => {
              return item.materialCode === rcj.materialCode
                  && item.materialName === rcj.materialName
                  && item.specification === rcj.specification
                  && item.unit === rcj.unit
                  && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcj)
                  //&& item.dePrice === rcj.dePrice
                  && item.markSum === rcj.markSum
            });
          }

          if (ObjectUtils.isNotEmpty(replaceRcjs)) {
            for (let replaceRcj of replaceRcjs) {
              if (!replaceRcj.hasOwnProperty('levelMark') ||  ObjectUtils.isEmpty(replaceRcj.levelMark)  ) {
                let  parent=constructRcjArray.find(item=>item.sequenceNbr ===replaceRcj.parentId);
                rcjTarget.levelMark=RcjCommonConstants.LEVELMARK_ZERO;
                await this.service.gongLiaoJiProject.gljRcjService.replaceRcjData(parent.deId, rcjTarget, constructId, RcjCommonConstants.RCJ_MERGE_REPLACE, parent.unitId, parent.deId, replaceRcj.sequenceNbr, replaceRcj.parentId , null, {});
              } else {
                await this.service.gongLiaoJiProject.gljRcjService.replaceRcjData(replaceRcj.deId, rcjTarget, constructId, RcjCommonConstants.RCJ_MERGE_REPLACE, replaceRcj.unitId, replaceRcj.deId, replaceRcj.sequenceNbr,null, null, {mergeFlag:true});
              }
            }
          }
        }

      }
    }

    return   ResponseData.success() ;
  }


  async saveMergeMaterials2(args) {
    let {levelType, constructId, singleId, unitId, rcjs} = args;
    let constructRcjArray = new Array();  // 所有的父级、子集材料
    // 获取该项目层级的人材机
    let rcjList = null;
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
      let idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3 && item.scopeFlag === true).map(item => item.sequenceNbr);
      if (ObjectUtils.isEmpty(idArray)) {
        return null;
      }
      rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item => idArray.includes(item.unitId))
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
      let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
      rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
    }
    if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
      let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.constructId === constructId && item.type === 3);
      let unitIds = this.checkoutParent(unitProjects, singleId);
      rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item => unitIds.includes(item.unitId))
    }

    // 获取所有的父级人材机
    if (ObjectUtils.isNotEmpty(rcjList)) {
      for (let constructProjectRcj of rcjList) {
        if (constructProjectRcj.totalNumber !== 0) {
          let constructProjectRcj1 = new GljConstructProjectRcj();
          ConvertUtil.setDstBySrc(constructProjectRcj, constructProjectRcj1);
          constructProjectRcj1.standardId = constructProjectRcj.rcjId;
          constructRcjArray.push(constructProjectRcj1);
        }
      }
    } else {
      return null;
    }

    // 二次解析
    let ts1 = rcjList.filter(i => i.markSum === 1); // 获取二次解析的材料
    if (ObjectUtils.isNotEmpty(ts1)) {
      for (let t of ts1) {
        let ts2 = t.pbs;
        let parent = rcjs.find(item => item.sequenceNbr === t.sequenceNbr);
        if (ObjectUtils.isNotEmpty(ts2)) {
          for (let t1 of ts2) {
            if (ObjectUtil.isNotEmpty(t1.totalNumber) && t1.totalNumber !== 0) {
              let constructProjectRcj = new GljConstructProjectRcj();
              ConvertUtil.setDstBySrc(t1, constructProjectRcj);
              constructProjectRcj.standardId = t.rcjId;
              constructRcjArray.push(constructProjectRcj);
            }
            // let child = rcjs.find(item => item.sequenceNbr === t1.sequenceNbr);
            // // 当既勾选了父，又勾选了子时，只取父
            // if(ObjectUtil.isNotEmpty(child) && ObjectUtil.isNotEmpty(parent)){
            //
            // } else {
            //   let constructProjectRcj = new GljConstructProjectRcj();
            //   ConvertUtil.setDstBySrc(t1, constructProjectRcj);
            //   constructProjectRcj.standardId = t.rcjId;
            //   constructRcjArray.push(constructProjectRcj);
            // }
            // // 当仅勾选了子的时候，只取子
            // if(ObjectUtil.isEmpty(parent) && ObjectUtil.isNotEmpty(child)){
            //   let constructProjectRcj = new GljConstructProjectRcj();
            //   ConvertUtil.setDstBySrc(t1, constructProjectRcj);
            //   constructProjectRcj.standardId = t.rcjId;
            //   constructRcjArray.push(constructProjectRcj);
            // }

          }
        }
      }
    }

    for (let rcjArray of rcjs) {
      // 获取目标材料
      let rcjTarget = rcjArray.find(item => item.mergeTarget === true);

      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        let originRcjTarget = constructRcjArray.find(item => item.sequenceNbr === rcjTarget.sequenceNbr);
        rcjTarget.materialCode = originRcjTarget.materialCode;
      }

      if ((rcjTarget.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || rcjTarget.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) && rcjTarget.markSum === RcjCommonConstants.MARKSUM_JX) {
        rcjTarget = ConvertUtil.deepCopy(rcjList.find(item => item.sequenceNbr === rcjTarget.sequenceNbr));
      }

      for (let rcj of rcjArray) {
        if (rcj.mergeTarget === false) {
          let replaceRcjs;
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT || levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            replaceRcjs = constructRcjArray.filter(item => {
              return item.materialCode.replace(/#\d+/g, '') === rcj.materialCode
                  && item.materialName === rcj.materialName
                  && item.specification === rcj.specification
                  && item.unit === rcj.unit
                  && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcj)
                  //  && item.dePrice === rcj.dePrice
                  && item.markSum === rcj.markSum
                  && item.ifDonorMaterial === rcj.ifDonorMaterial
                  && item.ifProvisionalEstimate === rcj.ifProvisionalEstimate
            });
          }
          if (levelType === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            replaceRcjs = constructRcjArray.filter(item => {
              return item.materialCode === rcj.materialCode
                  && item.materialName === rcj.materialName
                  && item.specification === rcj.specification
                  && item.unit === rcj.unit
                  && this._getBaseJournalPrice(item) === this._getBaseJournalPrice(rcj)
                  //&& item.dePrice === rcj.dePrice
                  && item.markSum === rcj.markSum
            });
          }

          if (ObjectUtils.isNotEmpty(replaceRcjs)) {
            for (let replaceRcj of replaceRcjs) {
              // 获取父集的子级
              if (!replaceRcj.hasOwnProperty('levelMark') || ObjectUtils.isEmpty(replaceRcj.levelMark)) {
                let parent = constructRcjArray.find(item => item.sequenceNbr === replaceRcj.parentId);
                rcjTarget.levelMark = RcjCommonConstants.LEVELMARK_ZERO;
                await this.service.gongLiaoJiProject.gljRcjService.replaceRcjData(parent.deId, rcjTarget, constructId, RcjCommonConstants.RCJ_MERGE_REPLACE, parent.unitId, parent.deId, replaceRcj.sequenceNbr, replaceRcj.parentId, null, {});
              } else {
                await this.service.gongLiaoJiProject.gljRcjService.replaceRcjData(replaceRcj.deId, rcjTarget, constructId, RcjCommonConstants.RCJ_MERGE_REPLACE, replaceRcj.unitId, replaceRcj.deId, replaceRcj.sequenceNbr, null, null, {mergeFlag: true});
              }
            }
          }
        }
      }
    }
    return ResponseData.success();
  }


  /**
   * 单位人材机汇总-市场价系数调整
   * @returns {Promise<void>}
   */
  async marketPriceAdjustUnit(args) {
    let {constructId, unitId, rcjList, coefficient} = args;
    let unitData = ProjectDomain.getDomain(constructId).getProjectTree().find(o => o.sequenceNbr === unitId);
    let singleId = unitData.parentId;

    let calculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;   //计税方式
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId).RCJ_COLLECT;
    if (ObjectUtils.isNotEmpty(rcjList)) {
      let rcjListUnitAll1 = ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item => unitId === item.unitId);
      let rcjListUnitAll = [];
      let rcjListUnitPbs = [];
      if (ObjectUtils.isNotEmpty(rcjListUnitAll1)) {
        rcjListUnitAll1.forEach(p => {
          rcjListUnitAll.push(p);
          if (ObjectUtils.isNotEmpty(p.pbs)) {
            p.pbs.forEach(q => {
              rcjListUnitAll.push(q);
              rcjListUnitPbs.push(q.sequenceNbr);
            })
          }
        });
      }

      for (let rcj of rcjList) {
        let rcjListUnitSame = rcjListUnitAll.filter(item => item.materialCode === rcj.materialCode
            && item.materialName === rcj.materialName
            && item.specification === rcj.specification
            && item.unit === rcj.unit
            && item.dePrice === rcj.dePrice
            && item.markSum === rcj.markSum
            && item.marketPrice == rcj.marketPrice);

        if (ObjectUtils.isNotEmpty(rcjListUnitSame)) {
          let rcjUpdate = rcjListUnitSame[0];
          let marketPrice = NumberUtil.multiplyJd(this._getMarketPrice(rcjUpdate), Number(coefficient), precision.marketPrice, precision.marketPriceAdjustUnit, null);
          let sourcePrice = marketPrice == rcjUpdate.dePrice ? "" : "自行询价";
          // if (rcjListUnitPbs.includes(rcj.sequenceNbr)) {
          //   let parentRcj = rcjListUnitAll1.find(o => o.sequenceNbr === rcjUpdate.parentId);
          //   //二级人材机
          //   let param = {
          //     constructId,
          //     singleId,
          //     unitId,
          //     deId: parentRcj.deId,
          //     rcjDetailId: rcjUpdate.sequenceNbr,
          //     constructRcj: {
          //       marketPrice: Number(marketPrice)
          //     },
          //   }
          //   await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(param);
          // } else {
          //   let param = {
          //     constructId,
          //     singleId,
          //     unitId,
          //     deId: rcjUpdate.deId,
          //     rcjDetailId: rcjUpdate.sequenceNbr,
          //     constructRcj: {
          //       marketPrice: Number(marketPrice),
          //       sourcePrice: sourcePrice
          //     },
          //   }
          //   await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(param);
          // }


          if (rcjListUnitPbs.includes(rcj.sequenceNbr)) {
            let parentRcj = rcjListUnitAll1.find(o => o.sequenceNbr === rcjUpdate.parentId);
            //二级人材机
            if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
              let param = {
                constructId,
                singleId,
                unitId,
                levelType: 3,
                sequenceNbr: rcjUpdate.sequenceNbr,
                constructProjectRcj: {
                  marketTaxPrice: Number(marketPrice),
                  donorMaterialPrice: null
                },
              }
              await this.service.gongLiaoJiProject.gljRcjCollectService.updateRcjCellect(param);
            } else {
              let param = {
                constructId,
                singleId,
                unitId,
                levelType: 3,
                sequenceNbr: rcjUpdate.sequenceNbr,
                constructProjectRcj: {
                  marketPrice: Number(marketPrice),
                  donorMaterialPrice: null
                },
              }
              await this.service.gongLiaoJiProject.gljRcjCollectService.updateRcjCellect(param);
            }
          } else {
            if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
              let param = {
                constructId,
                singleId,
                unitId,
                levelType: 3,
                sequenceNbr: rcjUpdate.sequenceNbr,
                constructProjectRcj: {
                  marketTaxPrice: Number(marketPrice),
                  donorMaterialPrice: null,
                  sourcePrice: sourcePrice
                },
              }
              await this.service.gongLiaoJiProject.gljRcjCollectService.updateRcjCellect(param);
            } else {
              let param = {
                constructId,
                singleId,
                unitId,
                levelType: 3,
                sequenceNbr: rcjUpdate.sequenceNbr,
                constructProjectRcj: {
                  marketPrice: Number(marketPrice),
                  donorMaterialPrice: null,
                  sourcePrice: sourcePrice
                },
              }
              await this.service.gongLiaoJiProject.gljRcjCollectService.updateRcjCellect(param);
            }
          }
        }
      }
    }

    return  ResponseData.success() ;

  }


  /**
   * 项目人材机汇总-市场价系数调整&应用
   * @returns {Promise<void>}
   */
  async marketPriceAdjustConstructApply(args) {
    let {constructId, rcjList, coefficient, apply} = args;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId).RCJ_COLLECT;
    if (apply) {
      if (ObjectUtils.isNotEmpty(rcjList)) {
        for (let rcj of rcjList) {
          let rcjListUnitAll = ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item => rcj.unitId === item.unitId);
          let rcjListUnitSame = rcjListUnitAll.filter(item => item.materialCode === rcj.materialCode
              && item.materialName === rcj.materialName
              && item.specification === rcj.specification
              && item.unit === rcj.unit
              && item.dePrice === rcj.dePrice
              && item.markSum === rcj.markSum
          );
          // && item.marketPrice == rcj.marketPrice

          if (ObjectUtils.isNotEmpty(rcjListUnitSame)) {
            for (let rcjUpdate of rcjListUnitSame) {
              let unitData = ProjectDomain.getDomain(constructId).getProjectTree().find(o => o.sequenceNbr === rcjUpdate.unitId);
              let singleId = unitData.parentId;
              let marketPrice = NumberUtil.multiplyJd(rcjUpdate.marketPrice, Number(coefficient), precision.marketPrice, precision.marketPriceAdjustUnit, null);
              let sourcePrice = rcjUpdate.marketPrice === rcjUpdate.dePrice ? "" : "自行询价";
              let param = {
                constructId,
                singleId,
                unitId:rcjUpdate.unitId,
                deId: rcjUpdate.deId,
                rcjDetailId: rcjUpdate.sequenceNbr,
                constructRcj: {
                  marketPrice: Number(marketPrice),
                  sourcePrice: sourcePrice
                },
              }
              await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(param);
            }
          }
        }
      }
    } else {
      if (ObjectUtils.isNotEmpty(rcjList)) {
        for (let rcj of rcjList) {
          let unitData = ProjectDomain.getDomain(constructId).getProjectTree().find(o => o.sequenceNbr === rcj.unitId);
          let singleId = unitData.parentId;
          let marketPrice = NumberUtil.multiplyJd(rcj.marketPrice, Number(coefficient), precision.marketPrice, precision.marketPriceAdjustUnit, null);
          let sourcePrice = rcj.marketPrice === rcj.dePrice ? "" : "自行询价";
          let param = {
            constructId,
            singleId,
            unitId: rcj.unitId,
            deId: rcj.deId,
            rcjDetailId: rcj.sequenceNbr,
            constructRcj: {
              marketPrice: Number(marketPrice),
              sourcePrice: sourcePrice
            },
          }
          await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(param);
        }
      }
    }

    return ResponseData.success();
  }


  /**
   * 内存管理  修改
   * @param constructRcjArrayElement 对象
   * @param isSingle
   * @param constructRcjArray
   */
  async changeMaterialCodeMemory(constructRcjArrayElement, isSingle ,constructRcjArray) {
    let  taxMethod=ProjectDomain.getDomain(constructRcjArrayElement.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    // if(constructRcjArrayElement.supplement_de_rcj_flag=== RcjCommonConstants.SUPPLEMENT_DE_RCJ_FLAG){
    //   return null
    // }
    // 1. 查询内存中序号，找出最大
    let businessMap =  ProjectDomain.getDomain(constructRcjArrayElement.constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap)) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    let  max =1 ;
    let unitAllMemory = new Array() ;
    let unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructRcjArrayElement.constructId+ FunctionTypeConstants.SEPARATOR + constructRcjArrayElement.unitId );
    let rcjDetailList = new Array();
    if(ObjectUtils.isNotEmpty(unitMemory)){
      let rcjListHasDetail = unitMemory.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
      for (let t of rcjListHasDetail) {
        let ts2 = t.pbs;
        if (!ObjectUtils.isEmpty(ts2)) {
          ts2.forEach(item => {
                item.parentId = t.sequenceNbr;
                rcjDetailList.push(item);
              }
          );
        }
      }
      unitAllMemory= unitMemory.concat(rcjDetailList);
      max = this.getMaxNumber(unitAllMemory,constructRcjArrayElement);
    }else{
      objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructRcjArrayElement.constructId+ FunctionTypeConstants.SEPARATOR + constructRcjArrayElement.unitId , new Array());
      unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructRcjArrayElement.constructId+ FunctionTypeConstants.SEPARATOR + constructRcjArrayElement.unitId );
    }
    let   constructRcjArrayFilter=unitAllMemory.filter(item=>{
      if(  item.materialCode.replace( /#\d+/g, '')===constructRcjArrayElement.materialCode.replace( /#\d+/g, '') ) {
        return   item ;
      }
    });
    // 2. 判断 constructRcjArrayElement 是否进行还原（标准库、补充）
    //判断修改之后的人材机是否修改到 原来本身的数据
    if (await this.estimateChangeMaterialCode(constructRcjArrayElement)) {
      //if (constructRcjArrayElement.materialCode.includes('#')) {
      // 2.1. 还原
      if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
        constructRcjArrayElement.materialCode = constructRcjArrayElement.materialCode.replace(/#\d+/g, '') ;
        let parentRcj = constructRcjArray.find(item => item.sequenceNbr === constructRcjArrayElement.parentId)
        parentRcj.updateCalcuTotalNumber = true;
        ProjectDomain.getDomain(parentRcj.constructId).getResourceDomain().createResource(parentRcj.unitId, parentRcj.deId, parentRcj,false)
        await  ProjectDomain.getDomain(parentRcj.constructId).getResourceDomain().notify(parentRcj);
        //await this.updateSubMemory(constructRcjArrayFilter, constructRcjArray, unitMemory,constructRcjArrayElement,constructRcjArrayElement.materialCode.replace(/#\d+/g, '')  )
      }
      constructRcjArrayElement.materialCode = constructRcjArrayElement.materialCode.replace(/#\d+/g, '') ;
      // }
    } else {
      //2.2. 不还原
      let  code = null;
      // 2.2.1  将内存中对象code 赋值给 code
      constructRcjArrayFilter.forEach(item=>{
        if(
            item.materialName === constructRcjArrayElement.materialName
            && item.specification === constructRcjArrayElement.specification
            && item.unit === constructRcjArrayElement.unit
            //&& (constructRcjArrayElement.kind==4 ||constructRcjArrayElement.kind==5 ? true : item.dePrice === constructRcjArrayElement.dePrice)
            && (
            this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(constructRcjArrayElement)? true : (taxMethod===1?constructRcjArrayElement.baseJournalPrice===item.baseJournalPrice :constructRcjArrayElement.baseJournalTaxPrice===item.baseJournalTaxPrice)
            )
            && item.kind === constructRcjArrayElement.kind
        ){
          code =item.materialCode;
        }
      });
      if(ObjectUtils.isNotEmpty(code)){
        if(!code.includes("#")){
          code= code+ '#' + max;
        }
        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
          await this.updateSubMemory(constructRcjArrayFilter, constructRcjArray, unitMemory,constructRcjArrayElement,code )
        }else{
          await this.updateMemory(constructRcjArrayFilter, constructRcjArray,unitMemory ,constructRcjArrayElement,code,unitAllMemory,isSingle);
        }
      }else {
        let updateCode = constructRcjArrayElement.materialCode.replace( /#\d+/g, '') + '#' + max;
        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
          await this.updateSubMemory(constructRcjArrayFilter, constructRcjArray, unitMemory,constructRcjArrayElement,updateCode )
        }else{
          await this.updateMemory(constructRcjArrayFilter, constructRcjArray,unitMemory ,constructRcjArrayElement,updateCode,unitAllMemory,isSingle);
        }
      }
      let constructId=constructRcjArrayElement.constructId;
      let unitId=constructRcjArrayElement.unitId;
      let deRowId=constructRcjArrayElement.deRowId;
      await  ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId, unitId,deRowId  },false);
    }
  }

  /**
   *   sub更新内存
   * @param constructRcjArrayFilter
   * @param constructRcjArray
   * @param unitMemory
   * @param constructRcjArrayElement
   * @param updateCode
   * @returns {Promise<void>}
   */
  async updateSubMemory(constructRcjArrayFilter, constructRcjArray, unitMemory,constructRcjArrayElement,updateCode) {
    let   max= 1;
    let parentRcj = constructRcjArray.find(item => item.sequenceNbr === constructRcjArrayElement.parentId)
    max = this.getMaxNumber(unitMemory,parentRcj);
    let memoryRcjs = unitMemory.filter(item => item.materialCode.replace(/#\d+/g, '') === parentRcj.materialCode.replace(/#\d+/g, ''));
    let userRcjBeforeArray = await this.findAlikeParentRcjArray(constructRcjArray, parentRcj);
    let  memoryRcjBefore=await this.findAlikeParentRcj(memoryRcjs, parentRcj);
    let subMemoryRcjBefore = constructRcjArrayFilter.find(item => item.materialCode === constructRcjArrayElement.materialCode);
    //计算 消耗量
    constructRcjArrayElement.materialCode =updateCode ;
    parentRcj.updateCalcuTotalNumber = true;
    ProjectDomain.getDomain(parentRcj.constructId).getResourceDomain().createResource(parentRcj.unitId, parentRcj.deId, parentRcj,false)
    await  ProjectDomain.getDomain(parentRcj.constructId).getResourceDomain().notify(parentRcj);
    let  memoryRcjAfter =await this.findAlikeParentRcj(memoryRcjs, parentRcj);
    //  既在使用中存在多个，并且内存中存在相同父，插入一个新的内存，在父修改中进行修改父code
    let subMemoryRcjAfter = constructRcjArrayFilter.find(item => item.materialCode === constructRcjArrayElement.materialCode);

    // let   this.findAlikeRcjArray(rcjArray, constructRcjArrayElement);
    if ( ObjectUtils.isEmpty(memoryRcjAfter)) {
      if(ObjectUtils.isEmpty(memoryRcjBefore)){
        // constructRcjArrayElement.marketPrice= subMemoryRcjAfter.marketPrice ;
        // constructRcjArrayElement.resQty= subMemoryRcjAfter.resQty ;
        // constructRcjArrayElement.markSum=subMemoryRcjAfter.markSum ;
        // constructRcjArrayElement.ifDonorMaterial=subMemoryRcjAfter.ifDonorMaterial ;
        // constructRcjArrayElement.donorMaterialNumber=subMemoryRcjAfter.donorMaterialNumber ;
        parentRcj.materialCode=parentRcj.materialCode.replace(/#\d+/g, '')+"#"+max;
        unitMemory.push(ConvertUtil.deepCopy(parentRcj));
      }else {
        if(userRcjBeforeArray.length>1){
          // constructRcjArrayElement.marketPrice= memoryRcj.marketPrice ;
          // constructRcjArrayElement.resQty= memoryRcj.resQty ;
          // constructRcjArrayElement.markSum=memoryRcj.markSum ;
          // constructRcjArrayElement.ifDonorMaterial=memoryRcj.ifDonorMaterial ;
          // constructRcjArrayElement.donorMaterialNumber=memoryRcj.donorMaterialNumber ;
          parentRcj.materialCode=parentRcj.materialCode.replace(/#\d+/g, '')+"#"+max;
          unitMemory.push(ConvertUtil.deepCopy(parentRcj));
        }else {
          if(subMemoryRcjBefore.materialCode.includes("#")){
            constructRcjArrayElement.materialCode = subMemoryRcjBefore.materialCode ;
          }else {
            subMemoryRcjBefore.materialCode = constructRcjArrayElement.materialCode  ;
          }
          subMemoryRcjBefore.materialName = constructRcjArrayElement.materialName;
          subMemoryRcjBefore.specification = constructRcjArrayElement.specification;
          subMemoryRcjBefore.unit = constructRcjArrayElement.unit;
          subMemoryRcjBefore.dePrice = constructRcjArrayElement.dePrice;
          subMemoryRcjBefore.kind = constructRcjArrayElement.kind;
          subMemoryRcjBefore.marketPrice = constructRcjArrayElement.marketPrice;
          subMemoryRcjBefore.resQty = constructRcjArrayElement.resQty;
          subMemoryRcjBefore.markSum = constructRcjArrayElement.markSum;
          subMemoryRcjBefore.ifDonorMaterial = constructRcjArrayElement.ifDonorMaterial;
          subMemoryRcjBefore.donorMaterialNumber = constructRcjArrayElement.donorMaterialNumber;

        }
      }
    }
    if ( ObjectUtils.isNotEmpty(memoryRcjAfter)) {
      parentRcj.materialCode=memoryRcjAfter.materialCode;
      constructRcjArrayElement.marketPrice=subMemoryRcjAfter.marketPrice ;
      constructRcjArrayElement.resQty=subMemoryRcjAfter.resQty ;
      constructRcjArrayElement.markSum=subMemoryRcjAfter.markSum ;
      constructRcjArrayElement.ifDonorMaterial=subMemoryRcjAfter.ifDonorMaterial ;
      constructRcjArrayElement.donorMaterialNumber=subMemoryRcjAfter.donorMaterialNumber ;
    }

  }


  /**
   *   sub更新内存
   * @param constructRcjArrayFilter
   * @param constructRcjArray
   * @param unitMemory
   * @param constructRcjArrayElement
   * @param updateCode
   * @param unitAllMemory
   * @param isSingle
   * @returns {Promise<void>}
   */
  async updateMemory(constructRcjArrayFilter, constructRcjArray,unitMemory ,constructRcjArrayElement,updateCode,unitAllMemory,isSingle) {
    let memoryRcjs = unitAllMemory.filter(item => item.materialCode.replace(/#\d+/g, '') === constructRcjArrayElement.materialCode.replace(/#\d+/g, ''));
    let userRcjBeforeArray =  this.findAlikeRcjArray(constructRcjArray, constructRcjArrayElement);
    let  memoryRcjBefore= this.findAlikeRcjBefore(memoryRcjs, constructRcjArrayElement);

    let  memoryRcjAfter =await this.findAlikeRcj(memoryRcjs, constructRcjArrayElement);
    //  既在使用中存在多个，并且内存中存在相同父，插入一个新的内存，在父修改中进行修改父code
    if ( ObjectUtils.isEmpty(memoryRcjAfter)) {
      if(ObjectUtils.isEmpty(memoryRcjBefore)){
        constructRcjArrayElement.materialCode =updateCode ;
        unitMemory.push(ConvertUtil.deepCopy(constructRcjArrayElement));
      }else {
        if(userRcjBeforeArray.length>1 &&  isSingle){
          constructRcjArrayElement.materialCode =updateCode ;
          unitMemory.push(ConvertUtil.deepCopy(constructRcjArrayElement));
        }else {
          let memoryRcj = constructRcjArrayFilter.find(item => item.materialCode === constructRcjArrayElement.materialCode);
          if(!memoryRcj.materialCode.includes("#")){
            memoryRcj.materialCode = updateCode;
            constructRcjArrayElement.materialCode= memoryRcj.materialCode;
          }
          memoryRcj.materialName = constructRcjArrayElement.materialName;
          memoryRcj.specification = constructRcjArrayElement.specification;
          memoryRcj.unit = constructRcjArrayElement.unit;
          memoryRcj.kind = constructRcjArrayElement.kind;
          memoryRcj.markSum = constructRcjArrayElement.markSum;
          memoryRcj.ifDonorMaterial = constructRcjArrayElement.ifDonorMaterial;
          memoryRcj.donorMaterialNumber = constructRcjArrayElement.donorMaterialNumber;

          memoryRcj.marketPrice= constructRcjArrayElement.marketPrice;
          memoryRcj.marketTaxPrice= constructRcjArrayElement.marketTaxPrice;
          memoryRcj.baseJournalPrice= constructRcjArrayElement.baseJournalPrice;
          memoryRcj.baseJournalTaxPrice= constructRcjArrayElement.baseJournalTaxPrice;
          memoryRcj.isDataTaxRate = constructRcjArrayElement.isDataTaxRate ;
          memoryRcj.taxRate= constructRcjArrayElement.taxRate;
          memoryRcj.ifProvisionalEstimate= constructRcjArrayElement.ifProvisionalEstimate;
          memoryRcj.isUpdateTaxRate= constructRcjArrayElement.isUpdateTaxRate;
        }
      }
    }
    if ( ObjectUtils.isNotEmpty(memoryRcjAfter)) {
      constructRcjArrayElement.materialCode =memoryRcjAfter.materialCode ;

      constructRcjArrayElement.markSum=memoryRcjAfter.markSum ;
      constructRcjArrayElement.ifDonorMaterial=memoryRcjAfter.ifDonorMaterial ;
      constructRcjArrayElement.donorMaterialNumber=memoryRcjAfter.donorMaterialNumber ;

      constructRcjArrayElement.marketPrice= memoryRcjAfter.marketPrice;
      constructRcjArrayElement.marketTaxPrice= memoryRcjAfter.marketTaxPrice;
      constructRcjArrayElement.baseJournalPrice= memoryRcjAfter.baseJournalPrice;
      constructRcjArrayElement.baseJournalTaxPrice= memoryRcjAfter.baseJournalTaxPrice;
      constructRcjArrayElement.isDataTaxRate = memoryRcjAfter.isDataTaxRate ;
      constructRcjArrayElement.taxRate= memoryRcjAfter.taxRate;
      constructRcjArrayElement.ifProvisionalEstimate= memoryRcjAfter.ifProvisionalEstimate;
      constructRcjArrayElement.isUpdateTaxRate= memoryRcjAfter.isUpdateTaxRate;
    }

  }


  /**
   * 内存管理 子级材料 联动父级材料 变动
   * @param constructProjectRcjs2 父级材料集合
   * @param  subUpdate
   * @param rcj 被影响的父级材料 对象
   * @param isSingle
   */
  async parentMaterialCodeChangeMemory(constructProjectRcjs2, rcj, isSingle) {
    //1.內存獲取
    let businessMap =  ProjectDomain.getDomain(rcj.constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap)) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    let  max =1 ;
    let unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + rcj.constructId+ FunctionTypeConstants.SEPARATOR + rcj.unitId );
    if(ObjectUtils.isNotEmpty(unitMemory)){
      max = this.getMaxNumber(unitMemory,rcj);
    }else{
      objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + rcj.constructId+ FunctionTypeConstants.SEPARATOR + rcj.unitId , new Array());
      unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + rcj.constructId+ FunctionTypeConstants.SEPARATOR + rcj.unitId );
    }
    let    revocationFlag=await this.parentRcjRevocation(unitMemory,rcj);
    //5.撤回编码
    if ( ( revocationFlag ||  rcj.markSum ===RcjCommonConstants.MARKSUM_BJX ) && await this.estimateChangeMaterialCode(rcj)) {
      if (rcj.materialCode.includes('#')) {
        rcj.materialCode = rcj.materialCode.replace(/#\d+/g, '');
        let  findRcj = constructProjectRcjs2.find(item=>item.materialCode === rcj.materialCode && item.sequenceNbr !== rcj.sequenceNbr);
        if(ObjectUtils.isNotEmpty(findRcj) && rcj.markSum != findRcj.markSum && ObjectUtils.isNotEmpty(rcj.pbs)){
          rcj.markSum = findRcj.markSum ;
          rcj.pbs.forEach(item=>{
            item.markSum =findRcj.markSum
            this.service.gongLiaoJiProject.gljRcjService.processingMarketPrice(item);
          }  );
          await this.service.gongLiaoJiProject.gljRcjCollectService.updateUnitRcjCellect({
            constructId: rcj.constructId,
            unitId: rcj.unitId,
            constructProjectRcj: { markSum: rcj.markSum},
            sequenceNbr: rcj.sequenceNbr
          });
        }
      }
    }
    //6.修改编码
    else {
      // let userRcjArray = await this.findAlikeParentRcjArray(constructProjectRcjs2, rcj);
      //  变更后内存存在的
      let afterMemoryRcj = await this.findAlikeParentRcj(unitMemory, rcj);
      let  beforeMemoryRcj=this.findAlikeRcjBefore(unitMemory, rcj)
      let  beforeUseRcjArray=this.findAlikeRcjArray(constructProjectRcjs2, rcj)
      //
      if (ObjectUtils.isNotEmpty(afterMemoryRcj)) {
        rcj.materialCode =afterMemoryRcj.materialCode;
        rcj.markSum =afterMemoryRcj.markSum;
        if(ObjectUtils.isNotEmpty(rcj.pbs)){
          rcj.pbs.forEach(item => item.markSum = afterMemoryRcj.markSum);
        }
      }else{
        if(ObjectUtils.isNotEmpty(beforeMemoryRcj)){
          if(beforeUseRcjArray.length >1 || (rcj.supplementRcjFlag === 1 && beforeUseRcjArray.length >=1 &&　!rcj.materialCode.includes("#")) ){
            rcj.materialCode = rcj.materialCode.replace(/#\d+/g, '') + '#' + max;
            let constructProjectRcj = ConvertUtil.deepCopy(rcj);
            unitMemory.push(constructProjectRcj);
          }else {
            beforeMemoryRcj.materialName = rcj.materialName;
            beforeMemoryRcj.specification = rcj.specification;
            beforeMemoryRcj.unit = rcj.unit;
            beforeMemoryRcj.dePrice = rcj.dePrice;
            beforeMemoryRcj.kind = rcj.kind;
            beforeMemoryRcj.marketPrice = rcj.marketPrice;
            beforeMemoryRcj.markSum = rcj.markSum;
            beforeMemoryRcj.ifDonorMaterial = rcj.ifDonorMaterial;
            beforeMemoryRcj.donorMaterialNumber = rcj.donorMaterialNumber;
            beforeMemoryRcj.isUpdateTaxRate = rcj.isUpdateTaxRate;
          }

        }else{
          rcj.materialCode = rcj.materialCode.replace(/#\d+/g, '') + '#' + max;
          let constructProjectRcj = ConvertUtil.deepCopy(rcj);
          unitMemory.push(constructProjectRcj);
        }
      }
    }
  }

  /**
   *
   */
  async parentRcjRevocation( unitMemory,rcj){
    //2.數據獲取
    let rcjDetailList = new Array();
    let rcjListHasDetail = unitMemory.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    //3.數據組裝
    let constructRcjArray = new  Array();
    if(ObjectUtils.isNotEmpty(rcjDetailList)){
      rcjDetailList.forEach(item=>constructRcjArray.push(item));
    }
    unitMemory.forEach(item=>constructRcjArray.push(item));
    //4.原始數據獲取
    let rcjDetailsTemp = [];
    let { service } = EE.app;
    if (rcj.levelMark === ResourceConstants.LEVEL_MARK_PB_CL && rcj.markSum ===RcjCommonConstants.MARKSUM_JX ) {
      let baseClpbList = await service.gongLiaoJiProject.gljBaseRcjService.getClpb(rcj);
      if (!ObjectUtils.isEmpty(baseClpbList)) {
        rcjDetailsTemp = rcjDetailsTemp.concat(ConvertUtil.deepCopy(baseClpbList));
      }
    }
    // if (rcj.levelMark === ResourceConstants.LEVEL_MARK_PB_JX  && rcj.markSum ===RcjCommonConstants.MARKSUM_JX) {
    //   let baseJxpbList = await service.gongLiaoJiProject.gljBaseRcjService.getJxpb(rcj);
    //   if (!ObjectUtils.isEmpty(baseJxpbList)) {
    //     rcjDetailsTemp = rcjDetailsTemp.concat(ConvertUtil.deepCopy(baseJxpbList));
    //   }
    // }
    let filter =rcj.pbs;
    return  this.estimate2ListSame(filter, rcjDetailsTemp)
  }




  async findAlikeParentRcj(memoryRcjArray, rcj) {
    for (let i = 0 ; i < memoryRcjArray.length ; ++i) {
      let memoryRcj=memoryRcjArray[i];
      if (ObjectUtils.isEmpty(memoryRcj.pbs)) {
        continue;
      }
      if (await this.estimate2ListSame(rcj.pbs, memoryRcj.pbs)
          && memoryRcj.materialName === rcj.materialName
          && memoryRcj.specification === rcj.specification
          && memoryRcj.unit === rcj.unit
          && (rcj.kind==4 ||rcj.kind==5? true : memoryRcj.dePrice === rcj.dePrice)
          && memoryRcj.kind === rcj.kind
      ) {
        return memoryRcj;
      }
    }
    return null;
  }

  async findAlikeParentRcjArray(rcjArray, rcj) {
    let   result =new Array()
    for (let i = 0 ; i < rcjArray.length ; ++i) {
      let memoryRcj=rcjArray[i];
      if (ObjectUtils.isEmpty(memoryRcj.pbs)) {
        continue;
      }
      if (await this.estimate2ListSame(rcj.pbs, memoryRcj.pbs)
          && memoryRcj.materialName === rcj.materialName
          && memoryRcj.specification === rcj.specification
          && memoryRcj.unit === rcj.unit
          && (rcj.kind==4 ||rcj.kind==5? true : memoryRcj.dePrice === rcj.dePrice)
          && memoryRcj.kind === rcj.kind
      ) {
        result.push(memoryRcj);
      }
    }
    return result;
  }


  findAlikeRcjArray(rcjArray, rcj) {
    let   result =new Array()
    for (let i = 0 ; i < rcjArray.length ; ++i) {
      let memoryRcj=rcjArray[i];
      if (
          memoryRcj.materialCode === rcj.materialCode
      ) {
        result.push(memoryRcj);
      }
    }
    return result;
  }

  async findAlikeRcj(memoryRcjArray, rcj) {
    let  taxMethod=ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    for (let i = 0 ; i < memoryRcjArray.length ; ++i) {
      let memoryRcj=memoryRcjArray[i];
      if (
          memoryRcj.materialCode.replace(/#\d+/g, '') === rcj.materialCode.replace(/#\d+/g, '')
          && memoryRcj.materialName === rcj.materialName
          && memoryRcj.specification === rcj.specification
          && memoryRcj.unit === rcj.unit
          // && (rcj.kind==4 ||rcj.kind==5? true : memoryRcj.dePrice === rcj.dePrice)
          && (
          this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(rcj)? true : (taxMethod===1?memoryRcj.baseJournalPrice===rcj.baseJournalPrice :memoryRcj.baseJournalTaxPrice===rcj.baseJournalTaxPrice)
          )
          && memoryRcj.kind === rcj.kind
      ) {
        return memoryRcj;
      }
    }
    return null;
  }
  findAlikeRcjBefore(memoryRcjArray, rcj) {
    for (let i = 0 ; i < memoryRcjArray.length ; ++i) {
      let memoryRcj=memoryRcjArray[i];
      if (
          memoryRcj.materialCode === rcj.materialCode
      ) {
        return memoryRcj;
      }
    }
    return null;
  }



  /**
   * 项目人材机汇总-三材汇总
   * @returns {Promise<void>}
   */

  async getScList(args) {
    let resultList = [];
    let {constructId, singleId, unitId, scNumber} = args;

    if (ObjectUtils.isNotEmpty(unitId)) {
      args.levelType = 3;
    } else {
      args.levelType = 1;
      args.kindSc = true;
    }

    args.kind = "0";
    args.isShowAnnotations = false;
    let rcjhzList = await this.getRcjCellectData(args);
    let scListAll = [];
    if (ObjectUtils.isNotEmpty(rcjhzList)) {
      scListAll = rcjhzList.filter(o => ObjectUtils.isNotEmpty(o.kindSc));
    }
    let gsRcjCollectScTypeList = ConvertUtil.deepCopy(gsRcjCollectScType);
    gsRcjCollectScTypeList = gsRcjCollectScTypeList.filter(o=>o.name !== "空");
    let gsRcjCollectScTypeListNoGangjin = gsRcjCollectScTypeList.filter(o => o.name !== "钢筋");
    let gsRcjCollectScTypeGangjin = gsRcjCollectScTypeList.find(o => o.name === "钢筋");

    // 小数点精度
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let transferFactor = precision.RCJ_COLLECT.transferFactor;
    scNumber = precision.COST_ANALYSIS.scNumber;

    for (let item of gsRcjCollectScTypeListNoGangjin) {
      let scCount = 0;
      item.parentId = 0;
      item.sequenceNbr = Snowflake.nextId();
      item.scType = 0;
      resultList.push(item);
      let scListAllType = scListAll.filter(q => q.kindSc === item.name);
      if (ObjectUtils.isNotEmpty(scListAllType)) {
        for (let item1 of scListAllType) {
          let deepCopy = ConvertUtil.deepCopy(item1);
          deepCopy.parentId = item.sequenceNbr;
          // deepCopy.scCount = item1.scCount;
          deepCopy.scCount = NumberUtil.multiply(NumberUtil.numberScale(deepCopy.totalNumber, scNumber), NumberUtil.numberScale(Number(deepCopy.transferFactor), transferFactor));
          deepCopy.scType = 1;
          scCount = NumberUtil.add(scCount,deepCopy.scCount);
          resultList.push(deepCopy);
        }
      }

      if (item.name === "钢材") {
        let gjScount = 0;
        gsRcjCollectScTypeGangjin.sequenceNbr = Snowflake.nextId();
        gsRcjCollectScTypeGangjin.parentId = item.sequenceNbr;
        gsRcjCollectScTypeGangjin.scType = 0;
        resultList.push(gsRcjCollectScTypeGangjin);
        let scListAllType = scListAll.filter(q => q.kindSc === gsRcjCollectScTypeGangjin.name);
        if (ObjectUtils.isNotEmpty(scListAllType)) {
          for (let item1 of scListAllType) {
            let deepCopy = ConvertUtil.deepCopy(item1);
            deepCopy.parentId = gsRcjCollectScTypeGangjin.sequenceNbr;
            // deepCopy.scCount = item1.scCount;
            deepCopy.scCount = NumberUtil.multiply(NumberUtil.numberScale(deepCopy.totalNumber, scNumber), NumberUtil.numberScale(Number(deepCopy.transferFactor), transferFactor));
            deepCopy.scType = 1;
            gjScount = NumberUtil.add(NumberUtil.numberScale(gjScount, scNumber), NumberUtil.numberScale(deepCopy.scCount, scNumber));
            resultList.push(deepCopy);
          }
        }
        gsRcjCollectScTypeGangjin.scCount = gjScount;
        scCount = NumberUtil.add(NumberUtil.numberScale(scCount, scNumber),NumberUtil.numberScale(gsRcjCollectScTypeGangjin.scCount, scNumber));
      }
      item.scCount = scCount;
    }

    return resultList;
  }



  /**
   * 三材汇总钢筋查询
   * @returns {Promise<void>}
   */
  async getScCountGJ(args) {
    let scList = await this.getScList(args);
    let find = scList.find(o => o.name === "钢筋" && o.scType === 0);
    let gjScountList = new Array();
    if (ObjectUtils.isNotEmpty(find)) {
      gjScountList.push(find);
    }
    return gjScountList;
  }


  async updateOtherProjectScGJ(constructId) {
    try {
      // let args = {};
      // args.constructId = constructId;
      // let scList = await this.getScList(args);
      // let find = scList.find(o => o.name === "钢筋" && o.scType === 0);
      // if (ObjectUtils.isNotEmpty(find)) {
      //   //todo 调用建设其他费更新钢筋数量
      //   // await this.service.gongLiaoJiProject.gljOtherProjectCostService.countOtherProjectCostCode({
      //   //   projectId: constructId
      //   // });
      // }
    } catch (error) {
      console.error("更新三材计算建设其他费钢筋捕获到异常:", error);
    }
  }

  /**
   *
   * @param constructRcjArrayElement
   * @param taxMethod
   * @param donorMaterialPrice
   * @param type     一般计税  1   简易 0  不计算为 3
   * @returns {Promise<void>}
   */
  async marketPriceUpdate(constructRcjArrayElement,taxMethod,donorMaterialPrice,type){
    if(ObjectUtils.isEmpty(donorMaterialPrice)){
      constructRcjArrayElement.donorMaterialPrice = null;
    }
    if(type !==3){
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(constructRcjArrayElement,type);
    }

    // 主材和设备，修改市场价，同步定额价
    constructRcjArrayElement.kind = constructRcjArrayElement.kind? Number(constructRcjArrayElement.kind): constructRcjArrayElement.kind


    // 标准编码段为BCRGF、BCJXF、BCCLF、BCZCF、BCSBF 的人材机市场价=定额价
    // if (constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG)
    //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX)
    //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL)
    //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC)
    //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB)
    // ) {
    //   constructRcjArrayElement.baseJournalPrice = constructRcjArrayElement.marketPrice;
    //   constructRcjArrayElement.baseJournalTaxPrice = constructRcjArrayElement.marketTaxPrice;
    // }
    // 市场价来源判定
    if (constructRcjArrayElement.marketTaxPrice === constructRcjArrayElement.baseJournalTaxPriceOriginalReverse && taxMethod === RcjCommonConstants.SIMPLE_REVERSE  ) {
      constructRcjArrayElement.sourcePrice = '';
    }
    if (constructRcjArrayElement.marketPrice === constructRcjArrayElement.baseJournalPriceOriginalForward && taxMethod === RcjCommonConstants.GENERAL_FORWARD ) {
      constructRcjArrayElement.sourcePrice = '';
    }

  }


  /**
   * 三材汇总造价分析查询
   * @returns {Promise<void>}
   */
  async getScCountList(constructId, singleId, unitId) {
    let args = {};
    args.constructId = constructId;
    args.singleId = singleId;
    args.unitId = unitId;
    let scList = [];

    // 小数点精度
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let scNumber = precision.COST_ANALYSIS.scNumber;
    args.scNumber = scNumber;

    if (ObjectUtils.isNotEmpty(singleId) && ObjectUtils.isEmpty(unitId)) {
      //查询单项 1、先查询单项下的单位  2、再把每个单位合计起来
      let singleProject = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === singleId
          && item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE);

      let projectElement = singleProject[0];
      let scList1= ConvertUtil.deepCopy(gsRcjCollectScType);
      scList = scList1.filter(o=>o.name !== "空");
      for(let item of scList){
        item.scType = 0;
        item.scCount = 0;
      }
      scList = await this.cal123(constructId, singleId, projectElement, scList, scNumber);
    } else {
      scList = await this.getScList(args);
    }

    let filter = scList.filter(o => o.scType === 0);
    let deepCopy = ConvertUtil.deepCopy(filter);
    for (let item of deepCopy) {
      item.name = item.name === "钢筋" ? "其中：钢筋" : item.name;
    }
    return deepCopy;
  }


  async cal123(constructId, singleId, projectElement, scList, scNumber) {
    if (ObjectUtils.isNotEmpty(projectElement.children)) {
      for (let item of projectElement.children) {
        if (ObjectUtils.isNotEmpty(item.children)) {
          await this.cal123(constructId, singleId, item, scList, scNumber);
        } else {
          let unitId = item.sequenceNbr;
          let args = {constructId, singleId, unitId, scNumber};
          let scList1 = await this.getScList(args);
          let filter = scList1.filter(o => o.scType === 0);
          let deepCopy = ConvertUtil.deepCopy(filter);
          for (let itemSC of scList) {
            let find = deepCopy.find(o => o.name === itemSC.name);
            if (ObjectUtils.isNotEmpty(find)) {
              itemSC.scCount = NumberUtil.add(NumberUtil.numberScale(itemSC.scCount, scNumber), NumberUtil.numberScale(find.scCount, scNumber));
            }
          }
        }
      }
    }
    return scList;
  }


  async   getStockPrice (args) {
    let { levelType, kind, constructId, unitId } = args;
    let    results= await this.getRcjCellectData(args);
    let   resultArray=[];
    results.forEach(item=>{
      let { materialCode, materialName, specification, unit, marketPrice } =  item;
      resultArray.push({ materialCode, materialName, specification, unit, marketPrice}) ;
    })
    return  resultArray;
  }


  /**
   * 获取基期价
   * @param rcj
   * @returns {Promise<*>}
   * @private
   */
  _getBaseJournalPrice(rcj) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      return rcj.baseJournalTaxPrice
    } else {
      return rcj.baseJournalPrice
    }
  }

  /**
   * 设置基期价
   * @param rcj
   * @param marketPrice
   * @private
   */
  _setBaseJournalPrice(rcj, baseJournalPrice) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      rcj.baseJournalTaxPrice = baseJournalPrice
    } else {
      rcj.baseJournalPrice = baseJournalPrice
    }
  }


  /**
   * 获取市场价
   * @param rcj
   * @returns {Promise<*>}
   * @private
   */
  _getMarketPrice(rcj) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      return rcj.marketTaxPrice
    } else {
      return rcj.marketPrice
    }
  }

  /**
   * 设置市场价
   * @param rcj
   * @param marketPrice
   * @private
   */
  _setMarketPrice(rcj, marketPrice) {
    let taxMethod = ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if (taxMethod === 0) {
      rcj.marketTaxPrice = marketPrice
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj,0);
    } else {
      rcj.marketPrice = marketPrice
      this.service.gongLiaoJiProject.gljRcjService.calculateTax(rcj,1);
    }
  }

  /**
   * 获取直接分摊费
   * @returns {Promise<void>}
   */

  async getShareCost(args) {
    let { constructId, unitId ,deId } = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
      businessMap.set(FunctionTypeConstants.RCJ_SHARE_COST,objMap);
    }
    let  shareCostUnit= objMap.get(unitId);
    if (ObjectUtils.isEmpty(shareCostUnit)) {
      shareCostUnit = new Map();
      objMap.set(unitId,shareCostUnit);
    }
    let  shareCost= shareCostUnit.get(deId);
    if(ObjectUtils.isEmpty(shareCost)){
      shareCost = ConvertUtil.deepCopy(shareCostJson);
      shareCostUnit.set(deId, shareCost);
    }
    return  shareCost
  }

  /**
   * 保存直接分摊费
   *  type 1 人工费     2 材料费   3机械费
   * @returns {Promise<void>}
   */

  async saveShareCost(args) {
    let { constructId, unitId ,deId,type,ratio,totalPrice } = args;
    let   deRowId =deId;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let deModel = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST);
    let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
      businessMap.set(FunctionTypeConstants.RCJ_SHARE_COST,objMap);
    }
    let  shareCostUnit= objMap.get(unitId);
    if (ObjectUtils.isEmpty(shareCostUnit)) {
      shareCostUnit = new Map();
      objMap.set(unitId,shareCostUnit);
    }
    let  shareCost= shareCostUnit.get(deId);
    if(ObjectUtils.isEmpty(shareCost)){
      shareCost = ConvertUtil.deepCopy(shareCostJson);
      shareCostUnit.set(deId, shareCost);
    }
    ratio = NumberUtil.numberScale(Number(ratio), precision.DETAIL.RCJ.ftRate);
    totalPrice = NumberUtil.numberScale(totalPrice, precision.EDIT.CSXM.calculationBase);
    let taxRate= NumberUtil.divide(ratio,100);
    if(type ===1){
      let rg=shareCost.find(item=>  item.name ==='人工费');
      rg.ratio = ratio;
      rg.value = NumberUtil.multiply( totalPrice,taxRate);
      deModel.RSum = rg.value;
      deModel.RDSum  = rg.value;
    }
    if(type ===2) {
      let cl=shareCost.find(item => item.name === '材料费');
      cl.ratio = ratio ;
      cl.value = NumberUtil.multiply( totalPrice,taxRate);
      deModel.CSum = cl.value;
      deModel.CDSum  = cl.value;
    }
    if(type ===3) {
      let jx = shareCost.find(item => item.name === '机械费');
      jx.ratio =ratio ;
      jx.value =NumberUtil.multiply( totalPrice,taxRate);
      deModel.JSum = jx.value;
      deModel.JDSum = jx.value;
    }
    await deDomain.updateDe(deModel);
    await  ProjectDomain.getDomain(constructId).getCsxmDomain().notify({ constructId, unitId,deRowId  },false);
    try {
      await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        qfMajorType: deModel.costFileCode
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
  }

  /**
   * 清理直接分摊费
   * @returns {Promise<void>}
   */

  async dropShareCost(de) {
    let businessMap = ProjectDomain.getDomain(de.constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
      businessMap.set(FunctionTypeConstants.RCJ_SHARE_COST,objMap);
    }
    let  shareCostUnit= objMap.get(de.unitId);
    if (ObjectUtils.isEmpty(shareCostUnit)) {
      shareCostUnit = new Map();
      objMap.set(de.unitId,shareCostUnit);
    }
    let shareCost = ConvertUtil.deepCopy(shareCostJson);
    shareCostUnit.set(de.deRowId, shareCost);
    try {
      await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: de.constructId,
        unitId: de.unitId,
        qfMajorType: de.costFileCode
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
  }

  async updateShareCost(constructId,unitId,deId,totalNumber,baseJournalTotalNumber) {
    let  pricingMethod=ProjectDomain.getDomain(constructId).getRoot().pricingMethod ;
    let  totalPrice  = pricingMethod === 1?totalNumber :baseJournalTotalNumber ;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST);
    if (ObjectUtils.isEmpty(objMap)) {
      objMap = new Map();
      businessMap.set(FunctionTypeConstants.RCJ_SHARE_COST,objMap);
    }
    let  shareCostUnit= objMap.get(unitId);
    if (ObjectUtils.isEmpty(shareCostUnit)) {
      shareCostUnit = new Map();
      objMap.set(unitId,shareCostUnit);
    }
    let  shareCost= shareCostUnit.get(deId);
    if(ObjectUtils.isEmpty(shareCost)){
      shareCost = ConvertUtil.deepCopy(shareCostJson);
      shareCostUnit.set(deId, shareCost);
    }else{
        let rg=shareCost.find(item=>  item.name ==='人工费');
        let rgTaxRate= NumberUtil.divide( rg.ratio,100);
        rg.value = NumberUtil.numberScale2(NumberUtil.multiply( totalPrice,rgTaxRate));

        let cl=shareCost.find(item => item.name === '材料费');
        let clTaxRate= NumberUtil.divide( cl.ratio,100);
        cl.value = NumberUtil.numberScale2(NumberUtil.multiply( totalPrice,clTaxRate));

        let jx = shareCost.find(item => item.name === '机械费');
        let jxTaxRate= NumberUtil.divide( jx.ratio,100);
        jx.value = NumberUtil.numberScale2(NumberUtil.multiply( totalPrice,jxTaxRate));
    }

  }

  async updateDeRcjHandle(constructRcjArrayElement){
     if (constructRcjArrayElement.isDeResource === 1) {
       let deDomain = ProjectDomain.getDomain(constructRcjArrayElement.constructId).getDeDomain();
       let de = deDomain.getDeById(constructRcjArrayElement.deId);
       de.deCode = constructRcjArrayElement.materialCode;
       de.deName = constructRcjArrayElement.materialName;
       de.ifLockStandardPrice = constructRcjArrayElement.ifLockStandardPrice;
       de.ifProvisionalEstimate = constructRcjArrayElement.ifProvisionalEstimate;
       de.markSum = constructRcjArrayElement.markSum;
       de.specification = constructRcjArrayElement.specification;
       de.deResourceKind = constructRcjArrayElement.kind;
       await deDomain.updateDe(de);
     }

  }
  /**
   * 主材设备判定
   * @param constructRcjArrayElement
   * @param baseRCJ
   * @param t
   * @returns {boolean}
   */
  isPrincipalMaterial(constructRcjArrayElement, baseRCJ,t) {
    if (this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(constructRcjArrayElement)) {
      return true;
    }
    return this._getBaseJournalPrice(constructRcjArrayElement) === this._getBaseJournalPrice(t)
  }

  updateMemoryRcj(constructRcjArrayElement,unitId){
    let  unitAllMemory=this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructRcjArrayElement.constructId,unitId);
    //处理内存
    if(ObjectUtils.isNotEmpty(unitAllMemory)){
      let  rcjMemorys = unitAllMemory.filter(item=>item.materialCode ===constructRcjArrayElement.materialCode);
      if(ObjectUtils.isNotEmpty(rcjMemorys)){
        rcjMemorys.forEach(item=>{
          item.sourcePrice= constructRcjArrayElement.sourcePrice;
          item.ifLockStandardPrice= constructRcjArrayElement.ifLockStandardPrice;
          item.ifDonorMaterial= constructRcjArrayElement.ifDonorMaterial;
          item.donorMaterialNumber= constructRcjArrayElement.donorMaterialNumber;
          item.donorMaterialPrice= constructRcjArrayElement.donorMaterialPrice;
          item.markSum= constructRcjArrayElement.markSum;
          item.unit= constructRcjArrayElement.unit;
          //工料机价格字段
          item.marketPrice= constructRcjArrayElement.marketPrice;
          item.marketTaxPrice= constructRcjArrayElement.marketTaxPrice;
          item.baseJournalPrice= constructRcjArrayElement.baseJournalPrice;
          item.baseJournalTaxPrice= constructRcjArrayElement.baseJournalTaxPrice;
          item.isDataTaxRate = constructRcjArrayElement.isDataTaxRate ;
          item.taxRate= constructRcjArrayElement.taxRate;
          item.ifProvisionalEstimate= constructRcjArrayElement.ifProvisionalEstimate;
          item.isFyrcj = constructRcjArrayElement.isFyrcj ;
        });
      }
    }

  }


  /**
   * 导入项目后单位工程费率恢复
   * @param mainMaterialSettingMap
   */
  transUnitMainMaterialSetting(mainMaterialSettingMap) {
    mainMaterialSettingMap = ObjectUtils.convertObjectToMap(mainMaterialSettingMap);
    for (let [key, value] of mainMaterialSettingMap) {
      mainMaterialSettingMap.set(key, ObjectUtils.copyProp(value, new MainMaterialSetting()))
    }
    return mainMaterialSettingMap;
  }


  /**
   * 获取单位工程设置主要材料
   * @param args
   * @returns {Promise<*>}
   */
  async getUnitMainMaterialSetting(args) {
    let businessMap = ProjectDomain.getDomain(args.constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.MAIN_MATERIAL_SETTING);

    let mainMaterialSetting = new MainMaterialSetting();
    //设置方式  0 自动设置
    mainMaterialSetting.type = 0;
    //自动设置 方式
    mainMaterialSetting.pattern = 1;
    //数量
    mainMaterialSetting.proportion = 50;

    if (ObjectUtils.isEmpty(objMap)) {
      //兼容老文件不存在map情况
      objMap = new Map();
      objMap.set(args.unitId, mainMaterialSetting);
      businessMap.set(FunctionTypeConstants.MAIN_MATERIAL_SETTING, objMap);
      return mainMaterialSetting;
    } else {
      if (ObjectUtils.isNotEmpty(objMap.get(args.unitId))) {
        return objMap.get(args.unitId);
      } else {
        return mainMaterialSetting;
      }
    }
  }

  /**
   * 修改 单位工程设置主要材料
   * @param args
   * @returns {Promise<void>}
   */
  async updateUnitMainMaterialSetting(args) {

    //应用范围
    let appliedRange = args.appliedRange;
    if (ObjectUtils.isEmpty(appliedRange)) {
      return;
    }

    let unitProjects = ProjectDomain.getDomain(args.constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);

    let unitArray = new Array();
    if (appliedRange == 1) {
      //当前单位工程
      unitArray.push(args.unitId);

    } else if (appliedRange == 2) {
      //同专业单位工程
      let unit = unitProjects.find(o => o.sequenceNbr === args.unitId);
      let unitList = unitProjects.filter(p => p.constructMajorType === unit.constructMajorType);

      for (let unitListKey of unitList) {
        unitArray.push(unitListKey.sequenceNbr);
      }
    } else if (appliedRange == 3) {
      //整个工程项目
      for (let unitListKey of unitProjects) {
        unitArray.push(unitListKey.sequenceNbr);
      }
    }

    if (ObjectUtils.isEmpty(unitArray)) {
      return;
    }

    let businessMap = ProjectDomain.getDomain(args.constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.MAIN_MATERIAL_SETTING);
    for (let unitArrayElement of unitArray) {
      let mainMaterialSetting = new MainMaterialSetting();

      //设置方式  0 自动设置   1手动设置
      let type = args.type;
      if (type == 0) {
        //自动设置
        mainMaterialSetting.type = type;
        mainMaterialSetting.pattern = args.pattern;
        mainMaterialSetting.proportion = args.proportion;

      } else if (type == 1) {
        //手动设置
        mainMaterialSetting.type = type;
        mainMaterialSetting.materialCodeList = args.materialCodeList;
      }

      objMap.set(unitArrayElement,mainMaterialSetting);
    }


    /*let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
    let mainMaterialSetting = unit.mainMaterialSetting;
    //设置方式  0 自动设置   1手动设置
    let type = args.type;
    if (type ==0){
        //自动设置
        mainMaterialSetting.type =type;
        mainMaterialSetting.pattern =args.pattern;
        mainMaterialSetting.proportion =args.proportion;

    }else if (type ==1) {
        //手动设置
        mainMaterialSetting.type =type;
        mainMaterialSetting.materialCodeList =args.materialCodeList;
    }*/

  }

    /**
     * 人材机汇总替换
     * @param constructId
     * @param unitId
     * @param rcj
     * @param newRcj
     * @returns {Promise<void>}
     */
    async updateRcjByCollect(args) {
      let {constructId, singleId, unitId, rcj, newRcj} = args
      let factor = newRcj.conversionCoefficient
      // 替换单位下符合条件人材机(预算书和措施项目)
      let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
      let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjKey);
      let oldRcjList = rcjList.filter(item => item.materialCode === rcj.materialCode)
      for (let oldRcj of oldRcjList) {
        if (oldRcj.isDeResource !== CommonConstants.COMMON_YES) {
          await this.service.gongLiaoJiProject.gljRcjService.replaceRcjData(oldRcj.deId, newRcj, constructId, singleId, unitId, oldRcj.deId, oldRcj.sequenceNbr,null, factor, {});
        }
      }
      // 替换定额人材机-预算书
      let yssDeList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
      let yssDeRcjList = yssDeList.filter(item => item.deCode === rcj.materialCode && item.isDeResource === CommonConstants.COMMON_YES);
      for (let yssDeRcj of yssDeRcjList) {
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        // let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: deStandardId});
        if (ZSFeeConstants.ZS_RCJ_LIST.includes(newRcj.materialCode)) {
          return ResponseData.fail("错误，存在循环引用！");
        }
        let deRow = await deDomain.appendDeResource(constructId, unitId, newRcj.sequenceNbr, yssDeRcj.sequenceNbr);
        await deDomain.extendQuantity(constructId, unitId, deRow.sequenceNbr);
      }

      // 替换定额人材机-措施项目
      let csxmDeList = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId);
      let csxmDeRcjList = csxmDeList.filter(item => item.deCode === rcj.materialCode && item.isDeResource === CommonConstants.COMMON_YES);
      for (let csxmDeRcj of csxmDeRcjList) {
        await this.service.gongLiaoJiProject.gljStepItemCostService.replaceFromIndexPage(constructId, singleId, unitId, newRcj.sequenceNbr, csxmDeRcj.sequenceNbr, 1, undefined, undefined, undefined, undefined);
      }
      return true
    }

  /**
   * 处理isCostDe，人材机不能被编辑问题
   * @param constructId
   * @param singleId
   * @param unitId
   * @param rcjList
   * @returns {Promise<void>}
   */
  async constructProjectRcjIsCostDe(constructId, singleId, unitId, rcjList) {
    for (let rcjItem of rcjList) {
      for (let deInfo of rcjItem.deInfoSet) {
        let de = ProjectDomain.getDomain(constructId).deDomain.getDe(item => item.unitId === unitId && item.sequenceNbr === deInfo.deId);
        if (!de) {
          de = ProjectDomain.getDomain(constructId).csxmDomain.getDe(item => item.unitId === unitId && item.sequenceNbr === deInfo.deId);
        }
        if (ObjectUtils.isNotEmpty(de)) {
          deInfo.isCostDe = de.isCostDe
          deInfo.isCsxmDe = de.isCsxmDe
        }
      }
    }
  }

  /**
   * 两个字符串的list，判断是否相互唯一存在
   * @param list1
   * @param list2
   * @returns {boolean}
   */
  areListsMutuallyUnique(list1, list2) {
    // 首先检查两个列表的长度是否相同
    if (list1.length !== list2.length) {
      return false;
    }
    // 创建一个对象来记录每个字符串的出现次数
    const countMap = {};
    // 遍历第一个列表，记录每个字符串的出现次数
    for (const str of list1) {
      countMap[str] = (countMap[str] || 0) + 1;
    }
    // 遍历第二个列表，减少每个字符串的出现次数
    for (const str of list2) {
      if (!countMap[str]) {
        return false; // 如果字符串不在第一个列表中，返回 false
      }
      countMap[str]--;
    }
    // 检查所有字符串的出现次数是否为零
    for (const key in countMap) {
      if (countMap[key] !== 0) {
        return false;
      }
    }
    return true;
  }
    checkoutParent(unitProjects , singleId){
      let  unitIds =new Array();
      unitProjects.forEach(item =>{
        this.parentRecursion(item,item,singleId,unitIds)
      });
      return  unitIds;
    }
    parentRecursion(itemOrg,item,singleId,unitIds){
      if(ObjectUtils.isEmpty(item) ){
        return null ;
      }
      if( singleId === item.sequenceNbr){
        unitIds.push(itemOrg.sequenceNbr)
        return null ;
      }
      this.parentRecursion(itemOrg,item.parent,singleId,unitIds);
    }

    parentCollect(curObj,result,filterArray){
      filterArray.push(curObj);
      let  curSingleId =null;
      let   parentObj=result.find(item => item.sequenceNbr === curObj.parentId);
      if(parentObj.type !==1){
         curSingleId = this.parentCollect(parentObj,result,filterArray)
      }else{
        filterArray.push(parentObj);
        curSingleId = curObj.sequenceNbr;
      }
       return curSingleId;

    }

  /**
   * 来源分析查询
   * @param args
   * @returns {Promise<null>}
   */
    async getRcjCellectAnalyseData(args) {
    let { levelType, constructId, singleId,rcj} = args;
    let allData =new Array ();
    let idArray  = new Array();
    if(ObjectUtils.isEmpty(rcj.materialCode)){
      return {} ;
    }
    let projects = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 2 || item.type === 3);
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
          idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3 && item.scopeFlag === true  ).map(item=>item.sequenceNbr);
        if (ObjectUtils.isNotEmpty(args.moveType)) {
          idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3).map(item=>item.sequenceNbr);
        }
      }
      if (levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getSingleUnitAll(constructId, singleId);
         idArray = unitList.map(item => item.sequenceNbr)
      }
      if(ObjectUtils.isEmpty(idArray)){
        return null  ;
      }
      for(let id of idArray){
        args.unitId = id;
        args.levelType = ProjectTypeConstants.PROJECT_TYPE_UNIT;
        let  unitRcj=await this.getRcjCellectData(args);
        if(ObjectUtils.isNotEmpty(unitRcj)){
          allData.push(...unitRcj);
        }
      }
    allData =allData.filter(item => {
      if(item.materialCode.replace( /#\d+/g, '') === rcj.materialCode.replace( /#\d+/g, '')
        && item.materialName === rcj.materialName
        && item.specification === rcj.specification
        && (item.unit === RcjCommonConstants.UNIT_PERCENT? RcjCommonConstants.UNIT_YUAN:item.unit)  === rcj.unit
        && item.kind === rcj.kind
        && item.baseJournalPrice === rcj.baseJournalPrice
        && item.baseJournalTaxPrice === rcj.baseJournalTaxPrice
        && item.ifDonorMaterial === rcj.ifDonorMaterial
        && item.marketPrice === rcj.marketPrice
        && item.marketTaxPrice === rcj.marketTaxPrice
        && item.ifProvisionalEstimate === rcj.ifProvisionalEstimate

      ){
        let current=projects.find(item2 =>item2.sequenceNbr === item.unitId );
        if(ObjectUtils.isNotEmpty(current)){
          let  unitName =current.name;
          item.unitName = this.concatParentName(projects,current,unitName);
          if(levelType === ProjectTypeConstants.PROJECT_TYPE_SINGLE){
            item.unitName = item.unitName.split('/').slice(1).join('/')
          }
        }
         return true ;
      }
    });
      return  allData;
    }

    concatParentName(projects,current,unitName){
        let parent=projects.find(item =>item.sequenceNbr === current.parentId );
        if(ObjectUtils.isNotEmpty(parent)){
          unitName =parent.name.concat('/',unitName);
          unitName= this.concatParentName(projects,parent,unitName);
        }
     return unitName
    }


  /**
     * 单位工程人材机汇总 下拉修改
     * @param args
     * @returns {Promise<void>}
     */
    async unitRcjBatchUpdate(args){
        let {constructId, singleId, unitId,column,value,idList} = args;
        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        if (ObjectUtils.isEmpty(unit) || ObjectUtils.isEmpty(column) || ObjectUtils.isEmpty(idList) || ObjectUtils.isEmpty(value) ){
            return ;
        }

        let cs ={} ;
        cs.constructId = unit.constructId;
        cs.singleId = unit.spId;
        cs.unitId = unit.sequenceNbr;
        cs.levelType = ProjectTypeConstants.PROJECT_TYPE_UNIT;

        for (let id of idList) {
            cs.sequenceNbr = id;
            let constructProjectRcj = {};
            constructProjectRcj[column] = value;
            cs.constructProjectRcj = constructProjectRcj;
            await this.updateRcjCellect(cs);
        }

    }


  /**
   *  新插入人材机处理,并返回处理后数据（目前用于跨代位复制）
   * @param constructId
   * @param unitId
   * @param rcj
   * @returns {Promise<void>}
   */
  async rcjAndMemoryHandle(constructId,unitId,rcj){
    //人材机数据去除#
    rcj.materialCode= rcj.materialCode.replace(/#\d+/g, '');
    if(ObjectUtils.isNotEmpty(rcj.pbs)){
      rcj.pbs.forEach(item2=>{item2.materialCode= item2.materialCode.replace(/#\d+/g, '')});
    }
    // 工程项目人材机获取
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    Array.prototype.push.apply(constructRcjArray, constructProjectRcjs);
    rcjListHasDetail.forEach(item => {
      Array.prototype.push.apply(rcjDetailList, item.pbs);
    });
    Array.prototype.push.apply(constructRcjArray, rcjDetailList);
    // constructRcjArray=this.service.PreliminaryEstimate.gsRcjService.getAllRcj(rcj);
    //内存数据获取
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap) || objMap.size===0) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    // 1.补充与普通rcj分开处理
    if(rcj.supplementRcjFlag ===1){
      //1.1.补充人材机 元数据处理，如果不存在父子相同，则放入
      let rcjUserList = this.service.gongLiaoJiProject.gljRcjService.getUserRcj(constructId,unitId);
      let  existRcj = this.findAlikeRcjAndPbs(rcjUserList,rcj);
      //1.2. 补充人材机内存数据处理，元数据不存在，则直接放入，元数据存在则内存无需操作
      if(ObjectUtils.isEmpty(existRcj)){
        let  memoryArray=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
        if (ObjectUtils.isEmpty(memoryArray) ) {
          objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId , new Array());
          memoryArray=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
        }
        let   result= this.findRcjBycode(memoryArray,rcj);
        if(ObjectUtils.isNotEmpty(result)){
          let max =1 ;
          max = this.getMaxNumber(memoryArray,rcj);
          rcj.materialCode =  rcj.materialCode + '#' + max;
        }
        rcjUserList.push(rcj);
        memoryArray.push(rcj);
      }else{
        rcj.materialCode = existRcj.materialCode;
      }
      if( ObjectUtils.isNotEmpty(rcj.pbs)){
        for (let i = 0; i < rcj.pbs.length; i++) {
          let  pb=rcj.pbs[i]
          await this.changeMaterialCodeMemory(pb, false, constructRcjArray);
          this.service.gongLiaoJiProject.gljRcjService.processingMarketPrice(pb);
        }
        await this.parentMaterialCodeChangeMemory(constructProjectRcjs , rcj,false);
      }

    }else {
      //2.普通人材机处理
      if( ObjectUtils.isNotEmpty(rcj.pbs)){
        constructRcjArray.push(rcj);

        for (let i = 0; i < rcj.pbs.length; i++) {
          let  pb=rcj.pbs[i]
          await this.changeMaterialCodeMemory(pb, false, constructRcjArray);
          this.service.gongLiaoJiProject.gljRcjService.processingMarketPrice(pb);
        }
        await this.parentMaterialCodeChangeMemory(constructProjectRcjs , rcj,false);
      } else {
        await this.changeMaterialCodeMemory(rcj, false, constructRcjArray);
      }
    }
    return   rcj
  }

  findRcjBycode(memoryRcjArray, rcj) {
    for (let i = 0 ; i < memoryRcjArray.length ; ++i) {
      let memoryRcj=memoryRcjArray[i];
      if (
        memoryRcj.materialCode.replace(/#\d+/g, '') === rcj.materialCode.replace(/#\d+/g, '')
      ) {
        return memoryRcj;
      }
    }
    return null;
  }

  findAlikeRcjAndPbs(memoryRcjArray, rcj) {
    let  taxMethod=ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    for (let i = 0 ; i < memoryRcjArray.length ; ++i) {
      let memoryRcj=memoryRcjArray[i];
      if (
        memoryRcj.materialCode.replace(/#\d+/g, '') === rcj.materialCode.replace(/#\d+/g, '')
        &&  this.findAlikPbs(rcj.pbs, memoryRcj.pbs,taxMethod)
        && memoryRcj.materialName === rcj.materialName
        && memoryRcj.specification === rcj.specification
        && memoryRcj.unit === rcj.unit
        // && (rcj.kind==4 ||rcj.kind==5? true : memoryRcj.dePrice === rcj.dePrice)
        && (
          this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(rcj)? true : (taxMethod===1?memoryRcj.baseJournalPrice===rcj.baseJournalPrice :memoryRcj.baseJournalTaxPrice===rcj.baseJournalTaxPrice)
        )
        && memoryRcj.kind === rcj.kind
      ) {
        return memoryRcj;
      }
    }
    return null;
  }

  findAlikPbs(rcjPbs ,memoryRcjPbs,taxMethod){
    let  flag =false ;
    if(ObjectUtils.isEmpty(rcjPbs)){
      return  true ;
    }
    rcjPbs.forEach(item =>{
      let  memoryRcjPb= memoryRcjPbs.find(item2=>item2.materialCode.replace(/#\d+/g, '') ===item.materialCode.replace(/#\d+/g, '')
        && item2.materialName === item.materialName
        && item2.specification === item.specification
        && item2.unit === item.unit
        //&& (item2.kind==4 ||item.kind==5? true : item2.dePrice === item.dePrice)
        && (
          this.service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(item2)? true : (taxMethod===1?memoryRcjPb.baseJournalPrice===item2.baseJournalPrice :memoryRcjPb.baseJournalTaxPrice===item2.baseJournalTaxPrice)
        )
        && item2.kind === item.kind
        && rcjPbs.length === memoryRcjPbs.length
      );
      if(ObjectUtils.isNotEmpty(memoryRcjPb)){
        flag =true;
      }
    });
    return  flag;

  }


}

GljRcjCollectService.toString = () => '[class gljRcjCollectService]';
module.exports = GljRcjCollectService;

