const {Service} = require('../../../core');
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {GsQuantitiesModel} = require("../models/GsQuantitiesModel");
const {GsConversionModel} = require("../models/GsConversionModel");
const { ObjectUtil } = require('../../../common/ObjectUtil');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {StandardConvertMod} = require("../enums/ConversionSourceEnum");

class GsInitDeService extends Service {

    constructor(ctx) {
        super(ctx);

    }

    /**
     * 定额初始化
     */
    async init(deModel) {

        // 工程量初始化
        await this.initDeQuantities(deModel);
        // 费率信息初始化
        await this.initDeFree(deModel);
        // 标准换算初始化
        await this.initDeConversion(deModel);
        // 定额费用代码 DE_COST_CODE
        await this.initDeCostCode(deModel);
    }

    /**
     * 删除定额
     * @param deModel
     * @returns {Promise<void>}
     */
    async remove(deModel) {
        // 删除工程量明细
        await this.removeQuantities(deModel);
        // 删除标准换算
        await this.removeConversion(deModel);
        //删除 定额费用代码
        await this.removeDeCostCode(deModel);
    }

    /**
     * 标准换算初始化
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeConversion(deModel) {
        let {constructId, unitId, sequenceNbr} = deModel;
        let deConversion = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        if (ObjectUtil.isEmpty(deConversion)) {
            deConversion = {};
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_CONVERSION, deConversion);
        }
        if (ObjectUtil.isEmpty(deConversion[unitId])) {
            deConversion[unitId] = {};
        }
        if (ObjectUtil.isEmpty(deConversion[unitId][sequenceNbr])) {
            deConversion[unitId][sequenceNbr] = {};
        }

        let gsConversionModel = new GsConversionModel();
        gsConversionModel.constructId = constructId;
        gsConversionModel.unitId = unitId;
        gsConversionModel.sequenceNbr = sequenceNbr;
        gsConversionModel.deId = sequenceNbr;
        gsConversionModel.type = deModel.type;
        gsConversionModel.standardId = deModel.standardId;
        gsConversionModel.mainMatConvertMod  = false;
        gsConversionModel.standardConvertMod = StandardConvertMod.Current;
        gsConversionModel.isConversion = false;
        let params = {
            constructId: constructId,
            unitId: unitId,
            standardDeId: deModel.standardDeId,
            fbFxDeId: deModel.sequenceNbr,
        }
        let conversionList = await this.service.PreliminaryEstimate.gsRuleDetailFullService.initStandardConvertList(params);
        gsConversionModel.conversionList = conversionList;
        gsConversionModel.originConversionList = ConvertUtil.deepCopy(conversionList);

        let defaultConcersions = await this.service.PreliminaryEstimate.gsRuleDetailFullService.initDef();
        gsConversionModel.defaultConcersions = defaultConcersions;
        gsConversionModel.originDefaultConcersions = ConvertUtil.deepCopy(defaultConcersions);
        deConversion[unitId][sequenceNbr] = gsConversionModel;
    }

    /**
     * 工程量初始化
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeQuantities(deModel) {
        let gsQuantitiesModel = new GsQuantitiesModel();
        gsQuantitiesModel.quotaListId = deModel.sequenceNbr;
        gsQuantitiesModel.unitId = deModel.unitId;
        gsQuantitiesModel.constructId = deModel.constructId;
        if (ObjectUtil.isNotEmpty(deModel.deCode)) {
            await this.service.PreliminaryEstimate.gsQuantitiesService.initDatas(gsQuantitiesModel);
        } else {
            gsQuantitiesModel.quantities = []
        }
        let quantitiesMap = ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        if(ObjectUtil.isNotEmpty(quantitiesMap)){
            let unitQuantiesMap = quantitiesMap.get(deModel.unitId)
            if (deModel.isAppendBaseDe === true && ObjectUtils.isNotEmpty(unitQuantiesMap?.get(deModel.sequenceNbr)?.quantities)) {
            } else {
                gsQuantitiesModel.zmVariableRuleList = unitQuantiesMap?.get(deModel.sequenceNbr)?.zmVariableRuleList
                unitQuantiesMap?.set(deModel.sequenceNbr, gsQuantitiesModel);
            }
        }
        deModel.isAppendBaseDe = false;
    }

    /**
     * 费率信息初始化
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeFree(deModel) {

        let unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(deModel.constructId, deModel.unitId);
        let baseDeLibraryModel = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(unitProject.constructMajorType);
        if(ObjectUtil.isEmpty(deModel.costFileCode)){
            // 取费专业名称
            deModel.costMajorName = baseDeLibraryModel.projectType
            // 取费文件id
            deModel.costFileCode = unitProject.constructMajorType
        }
    }

    /**
     * 初始化定额费用代码
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeCostCode(deModel) {
        let {constructId, unitId, sequenceNbr} = deModel
        let deCostCode = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.DE_COST_CODE);
        if (ObjectUtil.isEmpty(deCostCode)) {
            deCostCode = {};
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.DE_COST_CODE, deCostCode);
        }
        if (ObjectUtil.isEmpty(deCostCode[unitId])) {
            deCostCode[unitId] = {};
        }
        if (ObjectUtil.isEmpty(deCostCode[unitId][sequenceNbr])) {
            deCostCode[unitId][sequenceNbr] = {
                constructId: constructId,
                unitId: unitId,
                deId: sequenceNbr,
                priceCodes: [
                    {
                        "code": "GCLMXHJ",
                        "price": 0
                    }
                ]
            };
        }
    }

    /**
     * 删除定额标准换算
     * @param deModel
     * @returns {Promise<void>}
     */
    async removeConversion(deModel) {
        let {constructId, unitId, sequenceNbr} = deModel
        // 删除标准换算
        let deConversion = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        if (ObjectUtil.isNotEmpty(deConversion) && ObjectUtil.isNotEmpty(deConversion[unitId]) && ObjectUtil.isNotEmpty(deConversion[unitId][sequenceNbr])) {
            // 删除 sequenceNbr 属性
            delete deConversion[unitId][sequenceNbr];
        }
    }

    /**
     * 删除定额工程量明细
     * @param deModel
     * @returns {Promise<void>}
     */
    async removeQuantities(deModel) {
        // 删除工程量明细
        let quantitiesMap = ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap?.get(deModel.unitId)
        unitQuantiesMap?.delete(deModel.sequenceNbr);
        // 删除子定额工程量明细
        for (let children of deModel.children) {
            unitQuantiesMap?.delete(children.sequenceNbr);
        }
    }

    /**
     * 删除定额费用代码
     * @param deModel
     * @returns {Promise<void>}
     */
    async removeDeCostCode(deModel) {
        let {constructId, unitId, sequenceNbr} = deModel
        // 删除定额说明信息
        let deCostCode = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.DE_COST_CODE);
        if (ObjectUtil.isNotEmpty(deCostCode) && ObjectUtil.isNotEmpty(deCostCode[unitId]) && ObjectUtil.isNotEmpty(deCostCode[unitId][sequenceNbr])) {
            // 删除 sequenceNbr 属性
            delete deCostCode[unitId][sequenceNbr];
        }
    }

}
GsInitDeService.toString = () => '[class GsInitDeService]';
module.exports = GsInitDeService;
