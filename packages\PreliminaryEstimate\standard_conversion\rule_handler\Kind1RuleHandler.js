const RuleHandler = require("./ruleHandler");

class Kind1RuleHandler extends RuleHandler{

    /**
     * 准备数据：根据不同规则准备定额、人材机、规则的取消处理等
     */
    async prepare(){
        await super.prepare()
        this.excludeMaterialCodes = this._analysisExcludeMaterialCodes();
    }

    deCodeUpdateInfo() {
        let redSubArray = [];
        for (let handler of this.rule.mathHandlers) {
                redSubArray.push(handler.oriMath);
        }
        return {redStr: redSubArray.join(","), blackStr: null}
    }

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = this.rule.relation;
    }


    deNameUpdateInfo(rule){
        return rule.relation;
    }

    _analysisExcludeMaterialCodes(){
        return this.rule.excludeMaterialCodes?.split(",");
    }
}

module.exports = Kind1RuleHandler;
