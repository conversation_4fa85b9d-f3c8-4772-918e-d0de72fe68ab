'use strict';

const {BaseAnwenRate2022} = require("../models/BaseAnwenRate2022");
const {Service, Log} = require('../../../core');
const { AdjacentRoadsNumEnum,agetCodeByDesc } = require('../enums/AdjacentRoadsNumEnum');
const { EngineeringLocationEnum,egetCodeByDesc } = require('../enums/EngineeringLocationEnum');
const { FloorSpaceEnum,fgetCodeByDesc } = require('../enums/FloorSpaceEnum');
const { MunicipalEngineeringCostEnum,mgetCodeByDesc } = require('../enums/MunicipalEngineeringCostEnum');

/**
 * 示例服务
 * @class
 */
class BaseAnwenRateService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 查询安文费
     */
    async queryByLibraryCode(qfName,libraryCode,engineeringLocation,adjacentRoadsNum,floorSpace,municipalEngineeringCost,unitIs2022) {
        let result = await this.app.db.gongLiaoJiProject.manager.getRepository(unitIs2022?BaseAnwenRate2022:BaseAnwenRate2022).find({
            where:{
                libraryCode:libraryCode,
                projectLocation:engineeringLocation,//工程所在地
                roadSurfaceNum:adjacentRoadsNum,//临路面数
                floorSpace:floorSpace  //建筑面积
            }
        });
        //针对市政工程的查询
        let feeFilesBySHIZHENG = [];
        if (unitIs2022) {
            feeFilesBySHIZHENG = await this.service.baseFeeFileRelationService.getFeeFilesByLibraryCode("2022-SZGC-DEK",unitIs2022);
        }else {
            feeFilesBySHIZHENG = await this.service.baseFeeFileRelationService.getFeeFilesByLibraryCode("2012-SZGC-DEK",unitIs2022);
        }
        if (feeFilesBySHIZHENG.map(item => item.qfName).includes(qfName)) {
            let unitProject = result.find((item ) => item.municipalConstructionCost === municipalEngineeringCost);
            return  unitProject;
        }
        return result[0];
    }

    /**
     * 仅通过libraryCode查询安文费
     */
    async queryAnwenByLibraryCodeGlj(libraryCode) {
        let result = await this.app.db.gongLiaoJiProject.manager.getRepository(BaseAnwenRate2022).find({
            where: {
                delFlag: "0",
                libraryCode: libraryCode
            }
        });
        return result;
    }

    /**
     * 通过libraryCode、qfCode查询安文费
     */
    async queryAnwenByLibraryCodeGlj1(libraryCode,qfCode) {
        let result = await this.app.db.gongLiaoJiProject.manager.getRepository(BaseAnwenRate2022).find({
            where: {
                delFlag: "0",
                libraryCode: libraryCode,
                qfCode:qfCode
            }
        });
        return result;
    }

    /**
     * 查询安文费
     */
    async queryByLibraryCodeGlj(libraryCode, engineeringLocation, adjacentRoadsNum, floorSpace, municipalConstructionCost) {
        //转数据
        engineeringLocation = egetCodeByDesc(engineeringLocation);
        adjacentRoadsNum = agetCodeByDesc(adjacentRoadsNum);
        floorSpace = fgetCodeByDesc(floorSpace);
        municipalConstructionCost = mgetCodeByDesc(municipalConstructionCost);
        let result = await this.app.db.gongLiaoJiProject.manager.getRepository(BaseAnwenRate2022).find({
            where: {
                delFlag: "0",
                libraryCode: libraryCode,
                projectLocation:engineeringLocation,//工程所在地
                roadSurfaceNum:adjacentRoadsNum,//临路面数
                floorSpace:floorSpace,  //建筑面积
                municipalConstructionCost:municipalConstructionCost  //市政工程造价
            }
        });
        return result[0];
    }


    /**
     * 查询安文费
     */
    async queryByLibraryCodeGlj1(libraryCode, qfCode, engineeringLocation, adjacentRoadsNum, floorSpace, municipalConstructionCost) {
        //转数据
        engineeringLocation = egetCodeByDesc(engineeringLocation);
        adjacentRoadsNum = agetCodeByDesc(adjacentRoadsNum);
        floorSpace = fgetCodeByDesc(floorSpace);
        municipalConstructionCost = mgetCodeByDesc(municipalConstructionCost);
        let result = await this.app.db.gongLiaoJiProject.manager.getRepository(BaseAnwenRate2022).find({
            where: {
                delFlag: "0",
                libraryCode: libraryCode,
                projectLocation:engineeringLocation,//工程所在地
                roadSurfaceNum:adjacentRoadsNum,//临路面数
                floorSpace:floorSpace,  //建筑面积
                qfCode:qfCode, //取费专业
                municipalConstructionCost:municipalConstructionCost  //市政工程造价
            }
        });
        return result[0];
    }

}

BaseAnwenRateService.toString = () => '[class BaseAnwenRateService]';
module.exports = BaseAnwenRateService;
