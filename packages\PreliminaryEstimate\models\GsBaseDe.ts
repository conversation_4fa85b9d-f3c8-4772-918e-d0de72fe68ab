const {Tree, TreeNode,arrayToTree } = require("../core/tools/ConstructTreeMap.js")
import {Column} from "typeorm";
import {GsBaseModel} from "./GsBaseModel";
import {Entity} from "typeorm";

/**
 * 定额表
 */
@Entity({name: "gs_base_de"})
export class GsBaseDe extends GsBaseModel {
    @Column({ name: "library_code", nullable: true })
    public libraryCode: string; // 定额册编码

    @Column({ name: "library_name", nullable: true })
    public libraryName: string; // 定额库名称

    @Column({ name: "classlevel01", nullable: true })
    public classlevel01: string; // 一级分类

    @Column({ name: "classlevel02", nullable: true })
    public classlevel02: string; // 二级分类

    @Column({ name: "classlevel03", nullable: true })
    public classlevel03: string; // 三级分类

    @Column({ name: "classlevel04", nullable: true })
    public classlevel04: string; // 四级分类

    @Column({ name: "classlevel05", nullable: true })
    public classlevel05: string; // 五级分类
    @Column({ name: "classlevel_split_concat", nullable: true })
    public  classlevelSplitConcat: string; // 五级分类

    @Column({ name: "de_code", nullable: true })
    public deCode: string; // 定额编码

    @Column({ name: "de_name", nullable: true })
    public deName: string; // 定额名称

    @Column({ name: "unit", nullable: true })
    public unit: string; // 单位

    @Column("decimal",{ name: "res_qty", nullable: true })
    public resQty: number; // 含量

    @Column({ name: "quantity", nullable: true })
    public quantity: string; // 工程量

    @Column("decimal",{ name: "price", nullable: true })
    public price: number; // 单价

    @Column("decimal",{ name: "total", nullable: true })
    public total: number; // 合价

    @Column({ name: "project_type", nullable: true })
    public projectType: string; // 取费专业

    @Column({ name: "sort_no", nullable: true })
    public sortNo: number; // 排序

    @Column({ name: "agency_code", nullable: true })
    public agencyCode: string; // 机构代码

    @Column({ name: "product_code", nullable: true })
    public productCode: string; // 产品代码

    @Column({ name: "is_exist_de", nullable: true })
    public isExistDe: number; // 是否存在下级定额(0 不存在 1 存在)

    @Column({ name: "value", nullable: true })
    public value: number; // 是否存在下级定额(0 不存在 1 存在)

    constructor(sequenceNbr: string, recUserCode: string, recStatus: string, recDate: string, extend1: string, extend2: string, extend3: string, description: string, libraryCode: string, libraryName: string, classlevel01: string, classlevel02: string, classlevel03: string, classlevel04: string, classlevel05: string, deCode: string, deName: string, unit: string, resQty: number, quantity: string, price: number, total: number, projectType: string, sortNo: number, agencyCode: string, productCode: string, isExistDe: number, value: number,classlevelSplitConcat:string) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.libraryCode = libraryCode;
        this.libraryName = libraryName;
        this.classlevel01 = classlevel01;
        this.classlevel02 = classlevel02;
        this.classlevel03 = classlevel03;
        this.classlevel04 = classlevel04;
        this.classlevel05 = classlevel05;
        this.classlevelSplitConcat = classlevelSplitConcat;
        this.deCode = deCode;
        this.deName = deName;
        this.unit = unit;
        this.resQty = resQty;
        this.quantity = quantity;
        this.price = price;
        this.total = total;
        this.projectType = projectType;
        this.sortNo = sortNo;
        this.agencyCode = agencyCode;
        this.productCode = productCode;
        this.isExistDe = isExistDe;
        this.value = value;
    }
}