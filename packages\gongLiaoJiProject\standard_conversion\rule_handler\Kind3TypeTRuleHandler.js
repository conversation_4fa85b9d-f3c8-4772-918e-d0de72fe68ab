const Kind3RuleHandler = require("./Kind3RuleHandler");

class Kind3TypeTRuleHandler extends Kind3RuleHandler {
    constructor(strategyCtx, rule) {
        super(strategyCtx, rule);
        this.ifConversionStringReset = true;
    }
    deCodeUpdateInfo() {
        let redSubArray = [];
        for (let handler of this.rule.mathHandlers) {
            let parseMath = handler.mathItem.parseMath;
            let result = handler.showMath || this.mathAfterCalculation(parseMath,this.showMathDigits);
            redSubArray.push(result);
        }
        return {redStr: "["+redSubArray.join(",")+"]", blackStr: null}
    }

}
module.exports = Kind3TypeTRuleHandler;
