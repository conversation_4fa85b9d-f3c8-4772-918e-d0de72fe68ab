const { Controller } = require('../../../core');
const UtilsPs = require('../../../core/ps');
const { execFile } = require('child_process');
const { ResponseData } = require('../utils/ResponseData');
const path = require('path');

/**
 * 调起远程软件接口
 */
class GljRemoteAssistanceController extends Controller {


  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  async triggerAssistance() {
    execFile(path.join(UtilsPs.getExtraResourcesDir(), 'toDesk', 'ToDesk.exe'), [], (error, stdout, stderr) => {
      if (error) {
        console.error(`toDesk调起失败: ${error.message}`);
        return ResponseData.success(false);
      }
      if (stderr) {
        console.error(`toDesk调起错误: ${stderr}`);
        return ResponseData.success(false);
      }
      console.log(`toDesk调起: ${stdout}`);
      return ResponseData.success(true);
    });
  }

  async triggerHardwareManual() {
    execFile(path.join(UtilsPs.getExtraResourcesDir(), 'hardwareManualUtil', '小新实用五金手册2009.exe'), [], (error, stdout, stderr) => {
      if (error) {
        console.error(`五金工具调起失败: ${error.message}`);
        return ResponseData.success(false);
      }
      if (stderr) {
        console.error(`五金工具调起错误: ${stderr}`);
        return ResponseData.success(false);
      }
      console.log(`五金工具调起: ${stdout}`);
      return ResponseData.success(true);
    });
  }

}

GljRemoteAssistanceController.toString = () => '[class GljRemoteAssistanceController]';
module.exports = GljRemoteAssistanceController;