const GljRcjTypeEnum = Object.freeze(Object.fromEntries([
    ['TYPE1', {code: 1, desc: '人工费'}],
    ['TYPE2', {code: 2, desc: '材料费'}],
    ['TYPE3', {code: 3, desc: '机械费'}],
    ['TYPE4', {code: 4, desc: '设备费'}],
    ['TYPE5', {code: 5, desc: '主材费'}],
    ['TYPE6', {code: 6, desc: '商砼'}],
    ['TYPE7', {code: 7, desc: '砼'}],
    ['TYPE8', {code: 8, desc: '浆'}],
    ['TYPE9', {code: 9, desc: '商浆'}],
    ['TYPE10', {code: 10, desc: '配比'}],


    ['Rengong', {code: 1, desc: '人工费'}],
    ['Cailiao', {code: 2, desc: '材料费'}],
    ['Jixie', {code: 3, desc: '机械费'}],
    ['Shebei', {code: 4, desc: '设备费'}],
    ['<PERSON><PERSON><PERSON>', {code: 5, desc: '主材费'}],
    ['Shangtong', {code: 6, desc: '商砼'}],
    ['Tong', {code: 7, desc: '砼'}],
    ['Jiang', {code: 8, desc: '浆'}],
    ['Shangjiang', {code: 9, desc: '商浆'}],
    ['Peibi', {code: 10, desc: '配比'}]
]));

module.exports = GljRcjTypeEnum;