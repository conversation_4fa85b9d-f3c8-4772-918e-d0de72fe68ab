const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");

/**
 * 取费文件
 */
class GljBaseFeeFileController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 查询全量施工组织措施类别数据
     * @return {Promise<*>}
     */
    async queryMeasureTypeData(arg){
        const result = await this.service.gongLiaoJiProject.gljBaseCslbService.queryAllCslb();
        return ResponseData.success(result);
    }

    /**
     * 取费专业  15种-定额
     */
    async queryBaseFeeData(){
        const result = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();
        return  ResponseData.success(result);
    }

    /**
     * 取费专业  14种-费用汇总
     * @returns {Promise<ResponseData>}
     */
    async queryBaseFeeFileData(){
        const result = await this.service.gongLiaoJiProject.baseFeeFileService.queryBaseFeeFileData();
        return  ResponseData.success(result);
    }

    /**
     * 获取单位工程的默认取费专业
     * @returns {Promise<ResponseData>}
     */
    async queryBaseFeeFileProjectData(){
        const result = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFileProject();
        return  ResponseData.success(result);
    }

}

GljBaseFeeFileController.toString = () => '[class GljBaseFeeFileController]';
module.exports = GljBaseFeeFileController;