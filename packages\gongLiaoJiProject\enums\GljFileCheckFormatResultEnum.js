const GljFileCheckFormatResultEnum = Object.freeze(Object.fromEntries([
    ['SUCCESS', {code: 0, msg: "正确"}],
    ['LACK_PROJECT', {code: 1, msg: "缺失工程项目"}],
    ['LACK_SINGLE_PROJECT', {code: 2, msg: "缺失单项工程"}],
    ['LACK_UNIT_PROJECT', {code: 3, msg: "缺失单位工程"}],
    ['LACK_UNIT_PROJECT_DETAIL', {code: 4, msg: "缺失单位工程子目"}],
    ['NAME_ERROR', {code: 5, msg: "名称不匹配"}],
    ['LACK_PACKAGE_FIELD', {code: 6, msg: "缺失子目项"}],
    ['LACK_FIELD', {code: 7, msg: "必须项"}],
    ['INDEX_ERROR', {code: 8, msg: "顺序错误"}],
    ['LACK_SINGLE_UNIT', {code: 9, msg: "单项工程不完整"}],
    //['LACK_PROJECT_COUNT', {code: 10, msg: "缺失工程项目总价表"}],
    ['MISSING', {code: 10, msg: "缺失"}],
    // sheet名称不标准
    ['NOT_STANDARD', {code: 11, msg: "非法数据清单"}],
    ['ANALYSIS_FILE_FAIL', {code: 1, msg: "文件解析失败"}]
]));

module.exports = GljFileCheckFormatResultEnum


