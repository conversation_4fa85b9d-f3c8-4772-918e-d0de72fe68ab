const BaseDomain = require('../../../domains/core/BaseDomain');
const PropertyUtil = require('../../../domains/utils/PropertyUtil');
const path = require('node:path');
const fsp = require('fs').promises;
const fs = require('fs');
const ProjectDomain = require('../../../domains/ProjectDomain');
const {ObjectUtils} = require('../../../utils/ObjectUtils');
const {Snowflake} = require('../../../utils/Snowflake');
const {replacer} = require('mathjs');
const CommonConstants = require("../../../constants/CommonConstants");
const LogUtil = require('../logUtil');
const EE = require("../../../../../core/ee");
const {PricingFileFindUtils} = require("../../../../../electron/utils/PricingFileFindUtils");
const JSZip = require("jszip");
const FileOperatorType = require("../../../constants/FileOperatorType");
const {CryptoUtils} = require("../../../../../electron/utils/CrypUtils");
const AppContext = require("../../container/APPContext");
const {ConvertUtil} = require("../../../../PreliminaryEstimate/utils/ConvertUtils");
const deepCopy =require("lodash")
const WildcardMap = require("../../container/WildcardMap");
const {NumberUtil} = require("../../../../../electron/utils/NumberUtil");
const RcjCommonConstants = require("../../../constants/RcjCommonConstants");
const GljPrecisionSetting = require("../../../enums/GljPrecisionSetting");
const ProjectTypeConstants = require("../../../constants/ProjectTypeConstants");
class YGLJOperator {

    static directory = "";
    static fileEXT = ".ygs";
    static fileSavingSet = new Set();//暂时处理，用于保存正在保存的文件

    static async saveByFullPath(projectDomain, savePath) {
        let {service} = EE.app;

        projectDomain.getRoot().path = savePath;

        let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomain));
        jsonObj.path = savePath;
        jsonObj.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
        // return await service.ysfHandlerService.creatYsfFile(jsonObj);
        return await YGLJOperator.writeFileWithPath1(jsonObj);

        // return await YGLJOperator.writeFileWithPath(ObjectUtils.toJsonString(jsonObj),savePath);
    }


    static async saveAsByFullPath(projectDomain, savePath, newConstructId, constructName) {
        let {service} = EE.app;

        let projectDomainCopy = ConvertUtil.deepCopy(projectDomain);
        projectDomainCopy.getRoot().constructName = constructName;
        projectDomainCopy.getRoot().name = constructName;
        projectDomainCopy.getRoot().path = savePath;

        let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomainCopy));
        jsonObj.path = savePath;
        jsonObj.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;

        let data = JSON.stringify(jsonObj);
        let oldConstructId = projectDomainCopy.getRoot().sequenceNbr;
        // 替换constructid
        console.log("----old:" + oldConstructId + ",new:" + newConstructId + "----")
        const regex = new RegExp(oldConstructId, 'g');
        data = data.replace(regex, newConstructId);
        // 尝试解析 JSON 数据
        let  jsonObj1 = JSON.parse(data);

        // return await service.ysfHandlerService.creatYsfFile(jsonObj);
        return await YGLJOperator.writeFileWithPath1(jsonObj1);

        // return await YGLJOperator.writeFileWithPath(ObjectUtils.toJsonString(jsonObj),savePath);
    }

    /**
     * 切换计税方式
     * @param projectDomain
     * @param savePath
     * @returns {Promise<*>}
     */
    static async saveByTax(projectDomain, savePath, taxCalculationMethod) {
        let {service} = EE.app;

        let fileNameWithExtension = savePath.substring(savePath.lastIndexOf("\\") + 1);
        let constructName = fileNameWithExtension.substring(0, fileNameWithExtension.lastIndexOf("."));
        let project=deepCopy.clone(projectDomain);
        let deIdList = await this.baseDataTaxHandle(project,taxCalculationMethod,projectDomain);
        let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(project));
        jsonObj.path = savePath;
        jsonObj.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
        jsonObj.ProjectTree.find(item => item.type ===1).taxCalculationMethod = taxCalculationMethod
        jsonObj.ProjectTree.find(item => item.type ===1).projectTaxCalculation.taxCalculationMethod = taxCalculationMethod
        jsonObj.ProjectTree.find(item => item.type ===1).isSwitchTax = true
        jsonObj.ProjectTree.find(item => item.type ===1).name = constructName
        jsonObj.ProjectTree.find(item => item.type ===1).constructName = constructName
        jsonObj.ProjectTree.find(item => item.type ===1).path = savePath

        let data = JSON.stringify(jsonObj);
        //  获取项目id
        let oldConstructId = await YGLJOperator.getFirstConstructId(data);
        // 随机生成一个sequence替换constructid,避免重复
        let newConstructId = Snowflake.nextId();
        // 替换constructid
        console.log("----old:" + oldConstructId + ",new:" + newConstructId + "----")
        const regex = new RegExp(oldConstructId, 'g');
        data = data.replace(regex, newConstructId);
        // 尝试解析 JSON 数据
        let parsedData = JSON.parse(data);

        if (ObjectUtils.isNotEmpty(deIdList)) {
            //有费用人材机的定额需要重新计算
            let projectDomainTax = await YGLJOperator.destructuringFile(parsedData);
            let unitProjects = projectDomainTax.getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
            for (let item of unitProjects) {
                let deList = projectDomain.deDomain.getDeTree(p => p.unitId === item.sequenceNbr && deIdList.includes(p.sequenceNbr));
                let csxmList = projectDomain.csxmDomain.getDeTree(p => p.unitId === item.sequenceNbr && deIdList.includes(p.sequenceNbr));
                //todo 重新计算人材机、预算书、措施项目、自动计取
                let startTimeDe = new Date().getTime();
                await service.gongLiaoJiProject.gljProjectService.calDeCsxmProjectDomain(newConstructId, item, deList, csxmList, projectDomainTax);
                let endTimeDe = new Date().getTime();
                console.log("----------定额计算时间: " + (endTimeDe - startTimeDe) / 1000 + " 秒");
            }
            projectDomainTax.getRoot().path = savePath;
            parsedData = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomainTax));
            parsedData.path = savePath;
            parsedData.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
            AppContext.removeContext(newConstructId);
        }

        return await YGLJOperator.writeFileWithPath1(parsedData);
    }

    static async save(projectDomain, savePath) {
        let rootProject = projectDomain.getRoot();
        //为了打开文件，需要将路径补全
        let prePath = YGLJOperator.getFilePath(rootProject.name, savePath);
        rootProject.path = prePath;
        rootProject.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
        let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomain));
        jsonObj.path = savePath;
        jsonObj.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
        // return await service.ysfHandlerService.creatYsfFile(jsonObj);
        return await YGLJOperator.writeFileWithPath1(jsonObj);

        // return await YGLJOperator.writeFile(rootProject.name,ObjectUtils.toJsonString(jsonObj),savePath);
    }

    /**
     * 准备写入内容
     * @param projectDomain
     * @param sectionsArray
     */
    static prepareContent(projectDomain, sectionsArray = []) {
        let content = new Map();

        if (ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_PROJECT)) {
            content.set(ProjectDomain.KEY_PROJECT_TREE, projectDomain.ctx.treeProject.getAllNodes().map(item => PropertyUtil.filterObjectProperties(item, BaseDomain.avoidProperty)));
        }
        if (ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_DE)) {
            content.set(ProjectDomain.KEY_DE_TREE, projectDomain.ctx.deMap.getAllNodes().map(item => PropertyUtil.filterObjectProperties(item, BaseDomain.avoidProperty)));
        }

        if (ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_CSXM)) {
            //todo 保存获取csxmMap
            content.set(ProjectDomain.KEY_CSXM_TREE, projectDomain.ctx.csxmMap.getAllNodes().map(item => PropertyUtil.filterObjectProperties(item, BaseDomain.avoidProperty)));
        }

        if (ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_RESOURCE)) {
            content.set(ProjectDomain.KEY_RESOURCES, projectDomain.ctx.resourceMap.asArray().map(item => PropertyUtil.filterObjectProperties(item[1], BaseDomain.avoidProperty)));
        }
        if (ObjectUtils.isEmpty(sectionsArray) || sectionsArray.includes(CommonConstants.MODULE_TYPE_FUNCTION_DATA)) {
            content.set(ProjectDomain.KEY_FUNCTION_DATA_MAP, (PropertyUtil.mapToJson(projectDomain.functionDataMap)));
        }


        return content;
    }

    /**
     *
     * @param fileName
     * @param savePath
     * @returns {string}
     */
    static getFilePath(fileName, savePath) {
        const filePath = path.join(savePath, fileName + FileOperatorType.File_TYPE_YGS);
        const cwd = process.cwd();
        return path.resolve(cwd, filePath);
    }

    /**
     * 写入文件
     */
    static async writeFile(fileName, content, savePath) {
        let absolutePath = YGLJOperator.getFilePath(fileName, savePath);
        await YGLJOperator.writeFileWithPath(content, absolutePath);
        return absolutePath;
    }

    static async writeFileWithPath(content, absolutePath) {
        const writeStream = fs.createWriteStream(absolutePath, {encoding: 'utf8'});
        if (YGLJOperator.fileSavingSet.has(absolutePath)) {
            // console.error("文件正在保存中，请稍后再试");
            //直接放弃本地保存
            return absolutePath;
        }
        YGLJOperator.fileSavingSet.add(absolutePath);
        // 这里假设dataSource是一个数组或可迭代对象，每个元素是要写入的一行文本
        //writeStream.write(`概算文件:\n`);
        // for (const line of content) {
        // writeStream.write(`${line}`); // 写入一行文本，并添加换行符
        // }
        writeStream.write(content);
        // 监听'finish'事件，当所有数据都被写入底层资源时触发
        return new Promise((resolve, reject) => {
            writeStream.on('finish', () => {
                YGLJOperator.fileSavingSet.delete(absolutePath);
                console.log(`文件写入${absolutePath}完成`);
                resolve(absolutePath);
            });

            // 监听'error'事件，处理写入过程中可能出现的错误
            writeStream.on('error', (err) => {
                YGLJOperator.fileSavingSet.delete(absolutePath);
                console.error('写入文件时发生错误:', err);
                reject(err);
            });

            // 结束写入流（如果数据源不是流，这一步是必要的）
            writeStream.end();
        });

    }


    static async writeFileWithPath1(obj) {
        if (!obj instanceof ProjectDomain) {
            throw new Error("参数有误");
        }
        obj.fileCode = Snowflake.nextId();

        let objSource = ConvertUtil.deepCopy(obj);

        let data = await this.toJsonYsfString(obj);
        data = CryptoUtils.encryptAESData(data);
        // 创建一个新的压缩包实例
        const zip = new JSZip();
        // 添加JSON数据到压缩包中
        zip.file('file.json', data);
        // 生成压缩包
        await zip.generateAsync({type: 'nodebuffer'}).then(function (content) {
            // 将压缩包数据写入磁盘并将后缀名改为ysf
            fs.writeFileSync(obj.path, content);
        }).catch(function (error) {
            console.error('创建压缩包时发生错误:', error);
        });

        // 在最后一步更新文件hash值  放在前面可能会引起hash之后的文件被修改
        // let projectDomain = ProjectDomain.getDomain(obj.ProjectTree[0].sequenceNbr);
        // let data1 = await this.toJsonYsfString(ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomain)));// ObjectUtils.toJsonString(obj);
        delete objSource.path;
        delete objSource.biddingType;
        delete objSource.fileCode;
        let objSourceStr = await this.toJsonYsfString(objSource);
        CryptoUtils.objectHash(objSourceStr, obj.ProjectTree[0].sequenceNbr, true);

        return obj;
    }

    static async toJsonYsfString(obj) {
        return JSON.stringify(obj, (key, value) => {
            return typeof value === 'undefined' ? null : value;
        });
    }


    /**
     *
     * @param filePath
     */
    static async openFile(filePath, changeConstructId = false) {
        let projectDomain;

        try {
            if (YGLJOperator.fileSavingSet.has(filePath)) {
                console.log("文件正在保存中，请稍后再试");
                return projectDomain;
            }
            // 使用 fs.promises.readFile 读取文件内容
            // let data = await fsp.readFile(filePath, 'utf8');
            let data1 = await PricingFileFindUtils.getProjectObjByPath(filePath);
            let data = JSON.stringify(data1);

            if (changeConstructId) {
                //  获取项目id
                let oldConstructId = await YGLJOperator.getFirstConstructId(data);
                // 随机生成一个sequence替换constructid,避免重复
                let newConstructId = Snowflake.nextId();
                // 替换constructid
                console.log("----old:" + oldConstructId + ",new:" + newConstructId + "----")
                const regex = new RegExp(oldConstructId, 'g');
                data = data.replace(regex, newConstructId);
            }
            // 尝试解析 JSON 数据
            const parsedData = JSON.parse(data);

            // 调用另一个异步函数并等待其完成
            projectDomain = await YGLJOperator.destructuringFile(parsedData);

        } catch (error) {
            if (error.code === 'ENOENT') {
                console.error('文件不存在:', filePath);
            } else if (error.name === 'SyntaxError') {
                console.error('解析 JSON 数据时出错:', error);
            } else {
                console.error('读取文件或解析 JSON 时出错:', error);
            }
            // 如果发生错误，您可能希望抛出错误或返回 null/undefined
            // throw error; // 或者返回 null/undefined，取决于您的错误处理策略
        }
        // 返回 projectDomain 的值
        return projectDomain;
    }

    static async saveByFullPath(projectDomain, savePath) {
        let {service} = EE.app;

        projectDomain.getRoot().path = savePath;

        let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomain));
        jsonObj.path = savePath;
        jsonObj.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
        // return await service.ysfHandlerService.creatYsfFile(jsonObj);
        return await YGLJOperator.writeFileWithPath1(jsonObj);

        // return await YGLJOperator.writeFileWithPath(ObjectUtils.toJsonString(jsonObj),savePath);
    }

    /**
     * 获取项目ID
     * @param {*} jsonString
     * @returns
     */
    static async getFirstConstructId(jsonString) {
        const parsedData = JSON.parse(jsonString);
        return await ProjectDomain.getImportProjectId(parsedData);
    }

    /**
     * 获取项目ID
     * @param {*} jsonString
     * @returns
     */
    static async getConstructRoot(jsonString) {
        const parsedData = JSON.parse(jsonString);
        return await ProjectDomain.getImportProjectRoot(parsedData);
    }

    /**
     *
     * @param parsedData
     * @param parsedData
     */
    static async destructuringFile(parsedData) {
        return await ProjectDomain.importProject(parsedData);
    }

    static async baseDataTaxHandle(project,taxCalculationMethod,projectDomain){
        let deIdList = [];
        project.ctx =  deepCopy.clone(projectDomain.ctx);
        project.ctx.resourceMap  =new WildcardMap();
        projectDomain.ctx.resourceMap.getValues('*').forEach(item =>{
            let resource=deepCopy.clone(item);
            resource.sourcePrice = '自行询价';
            resource.highlight = false;
            if ( taxCalculationMethod === RcjCommonConstants.SIMPLE_REVERSE ) {
                if( NumberUtil.numberScale(  resource.marketTaxPrice ,GljPrecisionSetting.DETAIL.PTRCJZS.marketTaxPrice) === NumberUtil.numberScale(resource.baseJournalTaxPriceOriginalForward,GljPrecisionSetting.DETAIL.PTRCJZS.marketTaxPrice) ){
                    resource.sourcePrice = '';
                    resource.marketTaxPrice = resource.baseJournalTaxPriceOriginalReverse;
                    if(resource.isFyrcj){
                        deIdList.push(resource.deId);
                    }
                    this.calculateTax(resource,taxCalculationMethod);
                }

            }
            if (taxCalculationMethod === RcjCommonConstants.GENERAL_FORWARD ) {
                if(NumberUtil.numberScale( resource.marketPrice ,GljPrecisionSetting.DETAIL.PTRCJZS.marketPrice) === NumberUtil.numberScale(resource.baseJournalPriceOriginalReverse,GljPrecisionSetting.DETAIL.PTRCJZS.marketPrice) ){
                    resource.sourcePrice = '';
                    resource.marketPrice = resource.baseJournalPriceOriginalForward;
                    if(resource.isFyrcj){
                        deIdList.push(resource.deId);
                    }
                    this.calculateTax(resource,taxCalculationMethod);
                }
            }

            project.ctx.resourceMap.set(WildcardMap.generateKey(resource.unitId,resource.deId,resource.sequenceNbr),resource);
            });

        return deIdList;
    }

    static calculateTax(rcj,type){

        //let  taxMethod=ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        if(rcj.isDataTaxRate===0){
            return null;
        }
        if(type ===1){
            this.taxHandle(rcj);
        }
        if(type ===0){
            this.taxHandleReverse(rcj);
        }
        rcj.total = NumberUtil.multiply(rcj.marketPrice, rcj.totalNumber);
        rcj.totalTax =  NumberUtil.multiply(rcj.marketTaxPrice, rcj.totalNumber);
        rcj.baseJournalTotal =  NumberUtil.multiply(rcj.baseJournalPrice, rcj.totalNumber);
        rcj.baseJournalTotalTax =  NumberUtil.multiply(rcj.baseJournalTaxPrice, rcj.totalNumber);
    }

    static taxHandle(rcj){
        let {service} = EE.app;
        if(rcj.isDataTaxRate===2){
            rcj.marketTaxPrice =rcj.marketPrice;
        }
        if(rcj.isDataTaxRate===1){
            let taxRate= NumberUtil.divide( NumberUtil.numberScale(rcj.taxRate,GljPrecisionSetting.DETAIL.RCJ.taxRate),100)+1;
            let  marketPrice=NumberUtil.numberScale(rcj.marketPrice,GljPrecisionSetting.DETAIL.PTRCJZS.marketPrice);
            rcj.marketTaxPrice = NumberUtil.multiply(marketPrice ,taxRate);
        }
        if(service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(rcj) ){
            rcj.baseJournalPrice =  rcj.marketPrice;
            rcj.baseJournalTaxPrice = rcj.marketTaxPrice;
        }
    }
    static taxHandleReverse(rcj){
        let {service} = EE.app;
        if(rcj.isDataTaxRate===2){
            rcj.marketPrice =rcj.marketTaxPrice;
        }
        if(rcj.isDataTaxRate===1){
            let taxRate= NumberUtil.divide( NumberUtil.numberScale(rcj.taxRate,GljPrecisionSetting.DETAIL.RCJ.taxRate),100)+1;
            let  marketTaxPrice=NumberUtil.numberScale(rcj.marketTaxPrice,GljPrecisionSetting.DETAIL.PTRCJZS.marketPrice);
            rcj.marketPrice = NumberUtil.divide( marketTaxPrice,taxRate) ;
        }
        if(service.gongLiaoJiProject.gljRcjService.rcjDiffEstimate(rcj) ){
            rcj.baseJournalPrice =  rcj.marketPrice;
            rcj.baseJournalTaxPrice = rcj.marketTaxPrice;
        }
    }

}

module.exports = YGLJOperator;