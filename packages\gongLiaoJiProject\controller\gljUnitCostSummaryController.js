const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require("../domains/ProjectDomain");
const CommonConstants = require("../constants/CommonConstants");
const {dialog} = require("electron");
const fs = require("fs");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const YGLJOperator = require("../core/tools/fileOperator/YGLJOperator");
const AppContext = require("../core/container/APPContext");
const FileOperator = require("../core/tools/fileOperator/FileOperator");
const FileOperatorType = require("../constants/FileOperatorType");
const CostDeMatchConstants = require('../constants/CostDeMatchConstants');
const DeTypeConstants = require("../constants/DeTypeConstants");
const GljAppController = require("./gljAppController");

class GljUnitCostSummaryController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取默认费用汇总
     * @returns {ResponseData}
     */
    async defaultUnitCostSummary(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 获取费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async getUnitCostSummaryList(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(args);
        return ResponseData.success(res);
    }

    /**
     * 局部汇总-获取定额 专业列表
     * @param args
     * @returns {ResponseData}
     */
    async getCostSummaryMajorByDeList(args) {
        let majorList = await await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorByDeList(args);
        return ResponseData.success(majorList);
    }

    /**
     * 关闭局部费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async closePartCostSummary(args) {
        await await this.service.gongLiaoJiProject.gljUnitCostSummaryService.closePartCostSummary(args);
        return ResponseData.success(true);
    }

    /**
     * 获取局部费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async getUnitPartCostSummaryList(args) {
        let {constructId, singleId, unitId, deLists, csxmDeList} = args;
        let params = {
            constructId: args.constructId,
            unitId: args.unitId,
            singleId: args.singleId
        }
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let isPartSingleMajorFlag = unitProject.isPartSingleMajorFlag
        let qfPartMajorType = unitProject.qfPartMajorType
        args.isPartSingleMajorFlag = isPartSingleMajorFlag
        args.qfPartMajorType = qfPartMajorType
        let result = [];
        args.qfMajorType = args.constructMajorType
        let jbhzInfo = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getPartCostSummaryInfo(constructId);
        let {jbhzConstructId, jbhzFilePath} = jbhzInfo;

        let contextMap = AppContext.getAllContexts();   //当前窗口打开的项目
        if (!contextMap.has(jbhzConstructId)) {
            await this.service.gongLiaoJiProject.gljUnitCostSummaryService.createTempConstruct(args);
        }
        let jbhzArgs = ObjectUtils.cloneDeep(args);
        jbhzArgs.constructId = jbhzConstructId;
        if (ObjectUtils.isEmpty(jbhzArgs?.constructMajorType)) {
            // 局部多专业汇总
            result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getPartCostSummaryMajorsTotal(jbhzArgs);
        }else {
            jbhzArgs.isCostSummary = true;
            // result = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice2(jbhzArgs)
            let param = {
                constructId:jbhzConstructId,
                singleId,
                unitId,
            }
            if (isPartSingleMajorFlag === true) {
                param.qfMajorType = "TOTAL";
            }else {
                param.qfMajorType = args.qfMajorType
            }
            result = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(param)
        }

        return ResponseData.success(result);
    }


    /**
     * 局部汇总生成新单位工程
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async exportPartUnitProject(args) {
        let {constructId, singleId, unitId, deLists, csxmDeList} = args;

        // 获取工程项目
        let projectObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);

        // 查询单位工程
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        args.isPartSingleMajorFlag = unitProject.isPartSingleMajorFlag;
        args.qfPartMajorType = unitProject.qfPartMajorType;
        args.qfMajorType = unitProject.constructMajorType;
        args.unitName = unitProject.name;

        let jbhzInfo = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getPartCostSummaryInfo(constructId);
        let {jbhzConstructId, jbhzFilePath} = jbhzInfo;

        let result = {};
        // 当前窗口打开的项目
        let contextMap = AppContext.getAllContexts();
        if (!contextMap.has(jbhzConstructId)) {
            // 导出项目
            let options = {
                title: '保存文件',
                defaultPath: jbhzFilePath + '\\' + projectObj.name + '-' + unitProject.name,  // 默认保存路径或者模版获取路径
                filters: [
                    {name: '云算房', extensions: [FileOperatorType.File_TYPE_YSFG]}  // 可选的文件类型
                ]
            };
            let filePath = dialog.showSaveDialogSync(null, options);

            if(ObjectUtils.isNotEmpty(filePath)){
                // 重新生成工程项目文件
                args.filePath = filePath;
                await this.service.gongLiaoJiProject.gljUnitCostSummaryService.createTempConstruct(args);

                // 保存工程项目
                let projectDomain = AppContext.getContext(jbhzConstructId);
                let operate = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
                await operate.saveByFullPath(projectDomain, filePath);
                result = ResponseData.success(filePath);
            }
        } else {
            result = ResponseData.fail('写入文件时发生错误');
        }
        return ResponseData.success(result);
    }

    /**
     * 导出局部费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitPartCostSummary(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.exportUnitPartCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 获取计算后费用汇总
     * @param args
     * @returns {ResponseData}
     */
    countUnitCostSummary(args) {
        const res = this.service.gongLiaoJiProject.gljUnitCostSummaryService.countUnitCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 插入费用汇总
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async addCostSummary(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.addCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 删除费用汇总
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteCostSummary(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.deleteCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 保存或者修改费用汇总
     * @param args
     * @returns {*}
     */
    async saveCostSummary(args) {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.saveCostSummary(args);
    }

    /**
     * 获取工程专业下拉框
     * @param args
     * @returns {ResponseData}
     */
    async getConstructMajorTypeEnum(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getConstructMajorTypeEnum(args));
    }

    /**
     * 获取费用汇总左侧树
     * @param args
     * @returns {ResponseData}
     */
    async getCostSummaryMajorMenuList(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(args));
    }

    /**
     * 多专业汇总-多专业汇总补充工程专业
     * @param args
     * @returns {ResponseData}
     */
    async supplyCostSummaryMajors(args) {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.supplyCostSummaryMajors(args);
    }

    /**
     * 导入费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async importUnitCostSummary(args) {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.importUnitCostSummary(args);
    }

    /**
     * 导出费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitCostSummary(args) {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.exportUnitCostSummary(args);
    }

    /**
     * 获取费用汇总应用范围
     * @param args
     */
    async scopeOfApplicationsCostSummary(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.scopeOfApplicationsCostSummary(args));
    }

    /**
     * 批量应用费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async batchApplicationsCostSummary(args) {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.batchApplicationsCostSummary(args);
    }

    /**
     * 获取安文费明细汇总
     * @param args
     * @returns {Promise<*>}
     */
    async getAWFSummary(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getAWFSummary(args));
    }

    /**
     * 获取费用汇总或局部汇总，是否是多专业标识
     * @param args
     * @returns {Promise<*>}
     */
    async getIsSingleMajorFlag(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args));
    }

    /**
     * 设置费用汇总或局部汇总，是否是多专业标识
     * @param args
     * @returns {Promise<*>}
     */
    async setIsSingleMajorFlag(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.setIsSingleMajorFlag(args));
    }

    /**
     * 获取当前单位的取费专业
     * @param args
     * @returns {Promise<*>}
     */
    async getQfMajorTypeByUnit(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getQfMajorTypeByUnit(args));
    }

}

GljUnitCostSummaryController.toString = () => '[class GljUnitCostSummaryController]';
module.exports = GljUnitCostSummaryController;