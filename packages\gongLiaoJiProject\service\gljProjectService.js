const {Service} = require("../../../core");
const ProjectDomain = require("../domains/ProjectDomain");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ResponseData} = require("../utils/ResponseData");
const ProjectLevelConstant = require("../constants/ProjectLevelConstant");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {Snowflake} = require("../utils/Snowflake");
const xeUtils = require("xe-utils");
const WildcardMap = require("../core/container/WildcardMap");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const ProjectModel = require("../domains/projectProcessor/models/ProjectModel");
const LabelConstants = require("../constants/LabelConstants");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const fs = require('fs');
const CommonConstants = require("../constants/CommonConstants");
const DeTypeConstants = require("../constants/DeTypeConstants");
const {NumberUtil} = require("../utils/NumberUtil");
const {GljExcelUtil} = require("../utils/GljExcelUtil");
const GljFileCheckFormatResultEnum = require("../enums/GljFileCheckFormatResultEnum");
const {GljExcelSheetList} = require("../utils/GljExcelSheetList");
const {BaseFeeFileProject2022} = require("../models/BaseFeeFileProject");
const FileCheckFormatResultEnum = require("../../../electron/enum/FileCheckFormatResultEnum");
const XLSX = require('xlsx');
const {GljExcelImportAnalyzingUtil} = require("../utils/GljExcelImportAnalyzingUtil");
const StandardDeModel = require("../domains/deProcessor/models/StandardDeModel");
const {BaseRcj2022} = require("../models/BaseRcj2022");
const {BaseDeLibrary2022} = require("../models/BaseDeLibrary2022");
const {BaseDe2022} = require("../models/BaseDe2022");
const {BaseDeAwfRelation2022} = require("../models/BaseDeAwfRelation2022");
const {ResourceCalculator} = require("../domains/calculators/resource/ResourceCalculator");
const {DeCalculator} = require("../domains/calculators/de/DeCalculator");
const {QDCalculator} = require("../domains/calculators/de/QDCalculator");
const {FBCalculator} = require("../domains/calculators/de/FBCalculator");
const PropertyUtil = require('../domains/utils/PropertyUtil');
const BaseDomain = require('../domains/core/BaseDomain');
const TaxCalculationMethodEnum = require("../enums/TaxCalculationMethodEnum");
const RcjTypeEnum = require("../enums/RcjTypeEnum");

/**
 * 工程项目  service
 */
class GljProjectService extends Service {

    constructor(ctx) {
        super(ctx);
    }


    // 拖拽项目结构(调整位置、调整顺序、批量复制、批量删除)
    async gljDragDropProjectStructure(param) {
        let constructId = param.id;
        let constructTree = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        let oldConstructFlatMap = await this.flatConstructTreeToMapByObj(constructTree);
        // 1. 工程项目处理
        let constructObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        return await this.dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj);
    }

    /**
     * 工程项目重新赋值
     * @param param 前端的树
     * @param oldConstructFlatMap 内存中的数据平铺
     * @param constructObj  内存中的数据
     * @returns {Promise<ResponseData>}
     */
    async dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj) {
        let constructId = constructObj.sequenceNbr;
        let projectTree = ProjectDomain.getDomain(constructId).getProjectTree().filter(o => o.type !== ProjectLevelConstant.construct); //复制前当前所有单位
        // 新增两个bak熟悉用于保存处理后的数据
        constructObj.singleProjectsBak = [];
        constructObj.unitProjectArrayBak = [];

        // 2. 单项工程处理
        if (ObjectUtils.isNotEmpty(param.children)) {
            for (const item of param.children) {
                if (item.type === ProjectLevelConstant.single) {
                    await this._editSingleProjectStructure(param, item, oldConstructFlatMap, constructObj, true, constructObj);
                } else if (item.type === ProjectLevelConstant.unit) {
                    await this._editUnitStructure(param, param.id, item, oldConstructFlatMap, constructObj, true, constructObj);
                }
            }
        } else {
            constructObj.children = [];
        }

        //处理后的数据回填
        if (ObjectUtils.isNotEmpty(constructObj.singleProjectsBak)) {
            constructObj.children = constructObj.singleProjectsBak;
        } else if (ObjectUtils.isNotEmpty(constructObj.unitProjectArrayBak)) {
            constructObj.children = constructObj.unitProjectArrayBak;
        }
        await this.calProjectTree(constructObj);        //编辑机构节点到树结构

        let newProjectIdList = await this.calAllUnitList(constructObj.children, []);

        // if (ObjectUtils.isNotEmpty(projectTree)) {
        //     projectTree = projectTree.sort((a, b) => b.type - a.type);
        // }
        for (let o of projectTree) {
            if (!newProjectIdList.includes(o.sequenceNbr)) {
                //说明旧数据被删除了(单位/单项)
                await ProjectDomain.getDomain(constructId).removeProject(o.sequenceNbr);
            }
        }

        // let projectTreeList = [];        //复制前的单项/单位集合
        // if (ObjectUtils.isNotEmpty(projectTree)) {
        //     projectTree.forEach(p => {
        //         projectTreeList.push(p.sequenceNbr);
        //     });
        // }
        // await this.calProjectTreeNode(constructId, constructObj, projectTreeList);
        // await this.doCostCodeOtherProject(constructId);        //编辑完后刷新费用汇总和建设其他费  该接口是复制出来的项目，不用刷新费用汇总

        delete constructObj.singleProjectsBak;
        return ResponseData.success();
    }


    /**
     * 编辑完后刷新费用汇总和建设其他费
     * @param constructId
     * @returns {Promise<void>}
     */
    async doCostCodeOtherProject(constructId) {
        let projectTree1 = ProjectDomain.getDomain(constructId).getProjectTree().filter(o => o.type === ProjectLevelConstant.unit); //复制后当前所有单位
        if (ObjectUtils.isNotEmpty(projectTree1)) {
            for (let ppp of projectTree1) {
                try {
                    //费用汇总
                    await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                        constructId: constructId,
                        unitId: ppp.sequenceNbr,
                        qfMajorType: ppp.qfMajorType
                    });
                } catch (error) {
                    console.error("捕获到异常:", error);
                }
            }
        }
    }


    async calProjectTree(constructObj) {
        if (ObjectUtils.isNotEmpty(constructObj.children)) {
            let index = 0;
            for (let item of constructObj.children) {
                if (ObjectUtils.isNotEmpty(item.unitProjects)) {
                    item.children = item.unitProjects;
                } else if (ObjectUtils.isNotEmpty(item.subSingleProjects)) {
                    item.children = item.subSingleProjects;
                } else {
                    item.children = [];
                }
                item.index = index++;
                item = await this.calProjectTree(item);
            }
        }
        return constructObj;
    }


    async calProjectTreeNode(constructId, constructObj1, projectTreeList) {
        let constructObj = ConvertUtil.deepCopy(constructObj1);
        if (ObjectUtils.isNotEmpty(constructObj.children)) {
            for (let p of constructObj.children) {
                p.parent = constructObj;
                // ProjectDomain.getDomain(constructId).ctx.treeProject.nodeMap.set(p.sequenceNbr, p); // 将节点添加到节点映射表中
                ProjectDomain.getDomain(constructId).updateProject(p);
                await this.calProjectTreeNode(constructId, p, projectTreeList);
            }
        }
    }


    async calAllUnitList(singleProjectsBak, idList) {
        if (ObjectUtils.isNotEmpty(singleProjectsBak)) {
            for (const p of singleProjectsBak) {
                idList.push(p.sequenceNbr);
                if (ObjectUtils.isNotEmpty(p.children)) {
                    await this.calAllUnitList(p.children, idList);
                }
            }
        }
        return idList;
    }

    async _editSingleProjectStructure(constructParam, singleParam, oldConstructFlatMap, parent, ifParentIsConstruct, parentSame) {
        let oldSingle = oldConstructFlatMap.get(singleParam.id);
        let newSingle = oldSingle;
        let constructId = constructParam.id;

        if (!ObjectUtils.isEmpty(singleParam.copyFromId)) {
            let newSingle = await ConvertUtil.deepCopy(oldSingle);
            newSingle.sequenceNbr = Snowflake.nextId();
            newSingle.parentId = parent.sequenceNbr;
            // newSingle.constructId = constructId;
            newSingle.name = singleParam.name;
            newSingle.scopeFlag = true;

            //复制过来的校验名称是否有重复
            await this.repeatInitSingleName(constructId, ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleParam.id);

            newSingle.childrenCopy = newSingle.children;
            newSingle.children = [];
            //添加单项
            let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newSingle.parentId);
            ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newSingle, parentNode);

            //重新赋值单项下的单位id和单项spId
            await this.repeatInitSingleItemId(constructId, newSingle);
            //重新计算取费表单项的值
            await this.dealSingleQfData(constructId, oldSingle, newSingle);
            if (ifParentIsConstruct) {
                parent.singleProjectsBak.push(newSingle);
            } else {
                parent.subSingleProjectsBak.push(newSingle);
            }
        } else {
            if (ObjectUtils.isEmpty(oldSingle)) {// 单项不存在
                let ProjectModel = {
                    name: singleParam.name,
                    parentId: singleParam.parentId,
                    type: ProjectLevelConstant.single
                };
                let arg = {
                    constructId: constructId,
                    ProjectModel: ProjectModel
                };
                newSingle = await this.addSingleUnit(arg);

                // if (ifParentIsConstruct) { // 新增单项
                //     newSingle = this.service.singleProjectService.addSingleProject(arg, false);
                // } else { // 新增子单项
                //     newSingle = this.service.singleProjectService.addSubSingleProject(arg, false);
                // }
            }

            //拖拽后如果重名前端修改名称
            newSingle.name = singleParam.name;
            newSingle.parentId = parent.sequenceNbr;

            if (ifParentIsConstruct) {
                parent.singleProjectsBak.push(newSingle);
            } else {
                parent.subSingleProjectsBak.push(newSingle);
            }

            // 新增两个bak熟悉用于保存处理后的数据
            newSingle.unitProjectsBak = new Array();
            newSingle.subSingleProjectsBak = new Array();
            // newSingle.projectName = singleParam.name;

            if (ObjectUtils.isNotEmpty(singleParam.children)) {
                for (const item of singleParam.children) {
                    if (item.type === ProjectLevelConstant.single) {
                        await this._editSingleProjectStructure(constructParam, item, oldConstructFlatMap, newSingle, false, parentSame);
                    } else if (item.type === ProjectLevelConstant.unit) {
                        await this._editUnitStructure(constructParam, newSingle.sequenceNbr, item, oldConstructFlatMap, newSingle, false, parentSame);
                    }
                }
            }

            //处理后的数据回填
            newSingle.unitProjects = newSingle.unitProjectsBak;
            newSingle.subSingleProjects = newSingle.subSingleProjectsBak;
            delete newSingle.unitProjectsBak;
            delete newSingle.subSingleProjectsBak;
        }
    }


    async dealSingleQfData(constructId, oldSingle, newSingle) {
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let oldSingleFeeKey = WildcardMap.generateKey(oldSingle.sequenceNbr, FunctionTypeConstants.SINGLE_QFB);
        let oldSingleFeeY = singleQfbMap.get(oldSingleFeeKey);
        let oldSingleFee = await ConvertUtil.deepCopy(oldSingleFeeY);
        oldSingleFee.singleId = newSingle.sequenceNbr;
        let newSingleFeeKey = WildcardMap.generateKey(newSingle.sequenceNbr, FunctionTypeConstants.SINGLE_QFB);
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(newSingleFeeKey, oldSingleFee));

        let singleQfbMap1 = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
        let oldSingleFeeKey1 = WildcardMap.generateKey(oldSingle.sequenceNbr, FunctionTypeConstants.SINGLE_FLSM);
        let oldSingleFee1Y = singleQfbMap1.get(oldSingleFeeKey1);
        let oldSingleFee1 = await ConvertUtil.deepCopy(oldSingleFee1Y);
        oldSingleFee1.singleId = newSingle.sequenceNbr;
        let newSingleFeeKey1 = WildcardMap.generateKey(newSingle.sequenceNbr, FunctionTypeConstants.SINGLE_FLSM);
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_FLSM, singleQfbMap1.set(newSingleFeeKey1, oldSingleFee1));

        let itemElementMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
        if (ObjectUtils.isEmpty(itemElementMap)) {
            itemElementMap = new Map();
        } else {
            for (const [key, value] of itemElementMap) {
                if (key.includes(oldSingle.sequenceNbr)) {
                    let newKey = ConvertUtil.deepCopy(key).replace(oldSingle.sequenceNbr, newSingle.sequenceNbr);
                    itemElementMap.set(newKey, value);
                }
            }
            await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, itemElementMap);
        }
    }

    async addSingleUnit(args) {
        let {ProjectModel: argsModel, constructId} = args;
        argsModel.sequenceNbr = Snowflake.nextId();
        if (ObjectUtil.isEmpty(constructId) && argsModel.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            constructId = argsModel.sequenceNbr;
        }
        console.log("constructId-->:" + constructId + "type--->" + argsModel.type + "id=====》" + argsModel.sequenceNbr);
        let newProject = new ProjectModel(argsModel.sequenceNbr, argsModel.type, null);
        newProject.init(argsModel);
        newProject.type = argsModel.type;
        newProject.parentId = argsModel.parentId;
        this.do4Type(argsModel, newProject);
        await ProjectDomain.getDomain(constructId).createProject(newProject);
        // return ResponseData.success(ProjectDomain.filter4ProjectTree(newProject));
        return newProject;
    }


    async repeatInitSingleName(constructId, ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleId) {
        let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
        if (ifParentIsConstruct) {
            //单项
            // let singleProjects = parent.singleProjects;
            let singleProjects = projectTree.filter(o => o.parentId === parent.sequenceNbr);
            if (ObjectUtils.isNotEmpty(singleProjects)) {
                let singleUnitNameList = singleProjects.map(obj => obj.name);
                let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.name);
                newSingle.name = singleNewName;
            }
        } else {
            //子单项
            // let newVar = oldConstructFlatMap.get(singleId);
            // let subSingleProjects = newVar.subSingleProjects;
            let subSingleProjects = projectTree.filter(o => o.parentId === singleId);
            if (ObjectUtils.isNotEmpty(subSingleProjects)) {
                let singleUnitNameList = subSingleProjects.map(obj => obj.name);
                let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.name);
                newSingle.name = singleNewName;
            }
        }
    }

    //单位和单项本层级有重复名称时重新计算名称
    async repeatInitSingleNameCal(singleUnitNameList, oldName) {
        let newName = oldName;
        if (singleUnitNameList.includes(newName)) {
            newName = newName + "_1";
            while (singleUnitNameList.includes(newName)) {
                let lastIndex = newName.lastIndexOf("_");
                let count = newName.slice(lastIndex + 1);
                // const regex = "^[1-9]*$";
                let number = parseInt(count);
                number = number + 1;
                newName = newName.slice(0, -1) + number;
            }
        }
        return newName;
    }

    async repeatInitSingleItemId(constructId, newSingle) {
        let unitProjects = newSingle.childrenCopy.filter(o => o.type === ProjectLevelConstant.unit);
        if (ObjectUtils.isNotEmpty(unitProjects)) {
            //单项下有单位
            for (let i = 0; i < unitProjects.length; i++) {
                let item = unitProjects[i];
                let oldUnitId = item.sequenceNbr;
                item.parentId = newSingle.sequenceNbr;
                item.sequenceNbr = Snowflake.nextId();
                item.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
                item.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());

                //重新赋值分部分项等数据的unitId
                await this.repeatInitUnitItemId(item, oldUnitId, constructId);

                // newSingle.children.push(item);
                //添加单位节点
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(item.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(item, parentNode);
            }
        } else if (ObjectUtils.isNotEmpty(newSingle.subSingleProjects)) {
            //单项下有子单项
            for (let i = 0; i < newSingle.subSingleProjects.length; i++) {
                let item = newSingle.subSingleProjects[i];
                let oldSingle = await ConvertUtil.deepCopy(item);
                item.sequenceNbr = Snowflake.nextId();
                item.parentId = newSingle.sequenceNbr
                item.childrenCopy = item.children;

                //添加单项节点
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(item.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(item, parentNode);

                //重新计算取费表单项的值
                await this.dealSingleQfData(constructId, oldSingle, item);
                await this.repeatInitSingleItemId(constructId, item);
            }
        }
    }

    async repeatInitUnitItemId(newUnit, oldUnitId, constructId) {
        let oldUnit = ProjectDomain.getDomain(constructId).getProjectById(oldUnitId);

        //拷贝预算书定额数据
        let oldIdNewIdMap = await this.repeatInitUnitYssDe(constructId, oldUnit, newUnit);
        //处理措施项目定额数据
        let oldIdNewIdCsxmMap = await this.repeatInitUnitCsxmDe(constructId, oldUnit, newUnit);
        //拷贝预算书人和措施项目材机数据
        let oldIdNewIdRcjMap = await this.repeatInitUnitYssResource(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap);
        //拷贝预算书定额数据下的initChildCodes替换新的人材机id
        await this.repeatInitUnitYssDeInitChildCodes(constructId, newUnit, oldIdNewIdRcjMap);

        //拷贝functionMap数据
        let functionDataMap = ProjectDomain.getDomain(constructId).functionDataMap;

        //复制单位新建局部汇总map
        // let objMap = functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL);
        // if (ObjectUtils.isEmpty(objMap)) {
        //     functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL, new Map());
        // }

        for (let item of functionDataMap) {
            if (ObjectUtils.isNotEmpty(item)) {
                if (item[0] === FunctionTypeConstants.JBXX_KEY) {
                    //基本信息
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);

                            let lastCommaIndex = key.lastIndexOf("-");
                            let result = key.substring(lastCommaIndex + 1);
                            if (result === FunctionTypeConstants.JBXX_KEY_TYPE_12) {

                            } else {
                                for (let jbxxItem of newVar) {
                                    if (jbxxItem.name === "工程名称") {
                                        jbxxItem.remark = newUnit.name;
                                    }
                                }
                            }

                            // await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_DLF_KEY) {
                    //独立费
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);

                            let oldIdNewIdDlfMap = new Map();
                            for (let item of newVar) {
                                let oldId = item.sequenceNbr;
                                item.sequenceNbr = Snowflake.nextId();
                                oldIdNewIdDlfMap.set(oldId, item.sequenceNbr);
                                if (ObjectUtils.isNotEmpty(item.parentId)) {
                                    item.parentId = oldIdNewIdDlfMap.get(item.parentId);
                                }
                            }

                            // await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_COST_SUMMARY) {
                    //费用汇总
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            await this.copyFunctionDataMap(constructId, newVar);
                            for (let item of newVar) {
                                item.unitId = newUnit.sequenceNbr;
                            }
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_COST_CODE) {
                    //费用代码
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_QFB) {
                    //单位取费表
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            newVar.unitId = newUnit.sequenceNbr;
                            await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB).set(keyNew, newVar);


                            //导入单位后同步到单项(这个只是上一层级的单项取费数据)
                            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
                            let freeKeySingle = WildcardMap.generateKey(newUnit.parentId, FunctionTypeConstants.SINGLE_QFB);
                            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle)) ? singleQfbMap.get(freeKeySingle) : {"childFreeRate": new Map()};
                            if (ObjectUtils.isEmpty(freeRateSingleModel.childFreeRate.get(newVar.qfCode))) {
                                freeRateSingleModel.childFreeRate.set(newVar.qfCode, newVar);
                                ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

                                //再迭代处理多层级单项的取费数据
                                let singleProject = ProjectDomain.getDomain(constructId).getProjectById(newUnit.parentId);
                                await this.service.gongLiaoJiProject.gljCommonService.dealLevelSingleFeeImport(constructId, singleProject.parentId, newVar);
                            }

                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_FLSM) {
                    //单位取费表
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            newVar.unitId = newUnit.sequenceNbr;
                            await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.RCJ_MEMORY) {
                    //人材机缓存
                    let itemElementMap = item[1];
                    let oldVar = itemElementMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId + FunctionTypeConstants.SEPARATOR + oldUnitId);
                    if (ObjectUtils.isNotEmpty(oldVar)) {
                        let newVar = await ConvertUtil.deepCopy(oldVar);
                        // await this.copyFunctionDataMap(constructId, newVar);
                        let keyNew = FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId + FunctionTypeConstants.SEPARATOR + newUnit.sequenceNbr;
                        for (let itemVar of newVar) {
                            itemVar.unitId = newUnit.sequenceNbr;
                            itemVar.sequenceNbr = Snowflake.nextId();
                            let newVar2 = oldIdNewIdMap.get(itemVar.parentId);
                            let newVar3 = oldIdNewIdRcjMap.get(itemVar.parentId);
                            itemVar.parentId = ObjectUtils.isNotEmpty(newVar2) ? newVar2 : newVar3;
                            itemVar.deRowId = oldIdNewIdMap.get(itemVar.deRowId);
                            itemVar.deId = oldIdNewIdMap.get(itemVar.deId);
                        }
                        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_MEMORY).set(keyNew, newVar);
                    }
                } else if (item[0] === FunctionTypeConstants.PROJECT_USER_RCJ) {
                    //补充人材机编码
                    let itemElementList = item[1];
                    if (ObjectUtils.isNotEmpty(itemElementList)) {
                        let newVar1 = await ConvertUtil.deepCopy(itemElementList);
                        for (let item of newVar1) {
                            item.unitId = newUnit.sequenceNbr;
                            if (oldIdNewIdMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdMap.get(item.deRowId);
                                item.deId = oldIdNewIdMap.get(item.deRowId);
                                item.parentId = oldIdNewIdMap.get(item.deRowId);
                            } else if (oldIdNewIdCsxmMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdCsxmMap.get(item.deRowId);
                                item.deId = oldIdNewIdCsxmMap.get(item.deRowId);
                                item.parentId = oldIdNewIdCsxmMap.get(item.deRowId);
                            }

                            if (oldIdNewIdRcjMap.get(item.sequenceNbr)) {
                                item.sequenceNbr = oldIdNewIdRcjMap.get(item.sequenceNbr);
                            }
                        }
                        itemElementList = itemElementList.concat(newVar1);
                    } else {
                        itemElementList = [];
                    }
                    ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, itemElementList);
                } else if (item[0] === FunctionTypeConstants.UNIT_CONVERSION) {
                    //定额标准换算
                    let itemElementMap = item[1];
                    let oldUnitConversionMap = itemElementMap?.get(oldUnitId);
                    if (ObjectUtils.isNotEmpty(oldUnitConversionMap)) {
                        for (let key of oldUnitConversionMap.keys()) {
                            let newDeId;
                            if (oldIdNewIdMap.has(key)) {
                                newDeId = oldIdNewIdMap.get(key);
                            } else if (oldIdNewIdCsxmMap.has(key)) {
                                newDeId = oldIdNewIdCsxmMap.get(key);
                            }

                            let deConversionMapCopy = ConvertUtil.deepCopy(oldUnitConversionMap.get(key));
                            deConversionMapCopy.constructId = constructId;
                            deConversionMapCopy.unitId = newUnit.sequenceNbr;
                            deConversionMapCopy.deId = newDeId;
                            deConversionMapCopy.sequenceNbr = newDeId;
                            let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
                            let unitConversionMap = conversionMap?.get(newUnit.sequenceNbr);
                            if (ObjectUtils.isEmpty(unitConversionMap)) {
                                let unitConversionMapNew = new Map();
                                unitConversionMapNew.set(newDeId, deConversionMapCopy);
                                conversionMap.set(newUnit.sequenceNbr, unitConversionMapNew);
                            } else {
                                unitConversionMap.set(newDeId, deConversionMapCopy);
                            }
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.YSH_GCL_EXP_NOTIFY) {
                    //定额引用工程规模的数据
                    let itemElementMap = item[1];
                    let oldGCLList = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                    if (ObjectUtils.isNotEmpty(oldGCLList)) {
                        for (let item of oldGCLList) {
                            item.unitId = newUnit.sequenceNbr;
                            if (oldIdNewIdMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdMap.get(item.deRowId);
                            } else if (oldIdNewIdCsxmMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdCsxmMap.get(item.deRowId);
                            }
                        }
                        itemElementMap.set(newUnit.sequenceNbr, oldGCLList);
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY, itemElementMap);
                    }
                } else if (item[0] === FunctionTypeConstants.MAIN_MATERIAL_SETTING) {
                    //单位级别人材机主要材料设置数据
                    let itemElementMap = item[1];
                    let oldMainSettingObj = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                    if (ObjectUtils.isNotEmpty(oldMainSettingObj)) {
                        itemElementMap.set(newUnit.sequenceNbr, oldMainSettingObj);
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.MAIN_MATERIAL_SETTING, itemElementMap);
                    }
                // } else if(item[0] === FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL){
                //     //单位级别局部汇总
                //     let itemElementMap = item[1];
                //     let oldMainSettingObj = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                //     itemElementMap.set(newUnit.sequenceNbr, oldMainSettingObj);
                //     await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL, itemElementMap);
                // } else if (item[0] === FunctionTypeConstants.RCJ_COLLECT) {
                //     let itemElementMap = item[1];
                //     if (ObjectUtils.isEmpty(itemElementMap)) {
                //         itemElementMap = new Map();
                //     }
                //
                //     for (const [key, value] of itemElementMap) {
                //         if (key.includes(oldUnitId)) {
                //             let newKey = ConvertUtil.deepCopy(key).replace(oldUnitId, newUnit.sequenceNbr);
                //             itemElementMap.set(newKey, value);
                //         }
                //     }
                //     await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, itemElementMap);
                } else if (item[0] === FunctionTypeConstants.UNIT_DE_CONTENT) {
                    let itemElementMap = item[1];
                    if (itemElementMap.get(oldUnit.sequenceNbr)) {
                        let deXinxiShuomingMap = itemElementMap.get(oldUnit.sequenceNbr);
                        let deXinxiShuomingMapNow = ConvertUtil.deepCopy(deXinxiShuomingMap);
                        for (let key of deXinxiShuomingMapNow.keys()) {
                            if (oldIdNewIdMap.has(key)) {
                                let deXinxi = deXinxiShuomingMapNow.get(key);
                                deXinxi.deId = oldIdNewIdMap.get(key);
                                deXinxiShuomingMapNow.delete(key);
                                deXinxiShuomingMapNow.set(oldIdNewIdMap.get(key), deXinxi);
                            }
                        }
                        itemElementMap.set(newUnit.sequenceNbr, deXinxiShuomingMapNow);
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA) {
                    let itemElementObj = item[1];
                    if (ObjectUtils.isNotEmpty(itemElementObj) && itemElementObj[oldUnitId]) {
                        let itemElementObjElement = itemElementObj[oldUnitId];
                        let itemElementObjElementCopy = ConvertUtil.deepCopy(itemElementObjElement);
                        itemElementObj[newUnit.sequenceNbr] = itemElementObjElementCopy;
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA, itemElementObj);
                    }
                } else if (item[0] === FunctionTypeConstants.RCJ_COLLECT) {
                    //单位人材机汇总排序、涂色
                    let itemElementMap = item[1];
                    if (ObjectUtils.isEmpty(itemElementMap)) {
                        itemElementMap = new Map();
                    }
                    if (ObjectUtils.isNotEmpty(itemElementMap)) {
                        for (const [key, value] of itemElementMap) {
                            if (ObjectUtils.isNotEmpty(value)) {
                                if (value instanceof Map) {
                                    //单位人材机涂色
                                    if (ObjectUtils.isNotEmpty(value.get(oldUnit.sequenceNbr))) {
                                        let unitColorUnit = value.get(oldUnit.sequenceNbr);
                                        let unitColorUnitCopy = ConvertUtil.deepCopy(unitColorUnit);
                                        value.set(newUnit.sequenceNbr, unitColorUnitCopy);
                                    }
                                } else if (value instanceof Array || value instanceof Object) {
                                    if (key.includes(oldUnit.sequenceNbr)) {
                                        //单位人材机排序、表格列设置
                                        let keyUnitNew = key.replace(oldUnit.sequenceNbr, newUnit.sequenceNbr);
                                        let valueUnitCopy = ConvertUtil.deepCopy(value);
                                        itemElementMap.set(keyUnitNew, valueUnitCopy);
                                    }
                                    if (key.includes(oldUnit.parentId)) {
                                        //单项人材机排序、表格列设置
                                        let keySingleNew = key.replace(oldUnit.parentId, newUnit.parentId);
                                        let valueSingleCopy = ConvertUtil.deepCopy(value);
                                        itemElementMap.set(keySingleNew, valueSingleCopy);
                                    }
                                }
                            }
                        }
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, itemElementMap);
                    }
                } else if (item[0] === FunctionTypeConstants.YSH_TABLELIST || item[0] === FunctionTypeConstants.UNIT_BGLSZ || item[0] === FunctionTypeConstants.DLF_TABLELIST
                    || item[0] === FunctionTypeConstants.SC_TABLELIST) {
                    //表格列设置
                    let itemElementMap = item[1];
                    for (const [key, value] of itemElementMap) {
                        if (key.includes(oldUnit.sequenceNbr)) {
                            let tableListKey = key.replace(oldUnit.sequenceNbr, newUnit.sequenceNbr);
                            itemElementMap.set(tableListKey, ConvertUtil.deepCopy(value));
                            await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.TABLE_SETTING_CACHE, itemElementMap);
                        }
                    }
                }
            }
        }

        //拷贝计取缓存
        await this.repeatInitUnitJiqu(constructId, oldUnit, newUnit);

        //拷贝定额工程量明细
        await this.repeatInitUnitQuantities(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap);
    }


    /**
     * 拷贝预算书定额数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssDe(constructId, oldUnit, newUnit) {
        let oldDeAll = ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.getNodeById(oldUnit.defaultDeId);
        let oldDeAllCopy = await ConvertUtil.deepCopy(oldDeAll);

        oldDeAllCopy.sequenceNbr = newUnit.defaultDeId;
        oldDeAllCopy.deRowId = newUnit.defaultDeId;
        this.updatePropertyValue(oldDeAllCopy, 'unitId', newUnit.sequenceNbr);
        ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.addNode(oldDeAllCopy);
        let oldIdNewIdMap = new Map();
        oldIdNewIdMap = await this.copyDeData(constructId, oldDeAllCopy, oldIdNewIdMap);
        return oldIdNewIdMap;
    }


    /**
     * 拷贝预算书定额数据,定额类型的initChildCodes数据修改id
     * @param constructId
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssDeInitChildCodes(constructId, newUnit, oldIdNewIdRcjMap) {
        let deList = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === newUnit.sequenceNbr && item.type === DeTypeConstants.DE_TYPE_DE);
        let csxmList = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === newUnit.sequenceNbr && item.type === DeTypeConstants.DE_TYPE_DE);

        if (ObjectUtils.isNotEmpty(deList)) {
            for (let item of deList) {
                let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(item.sequenceNbr);
                if (ObjectUtils.isNotEmpty(de.initChildCodes)) {
                    de.initChildCodes.forEach(o => {
                        o.sequenceNbr = oldIdNewIdRcjMap.get(o.sequenceNbr);
                    });
                }
            }
        }
        if (ObjectUtils.isNotEmpty(csxmList)) {
            for (let item of csxmList) {
                let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(item.sequenceNbr);
                if (ObjectUtils.isNotEmpty(de.initChildCodes)) {
                    de.initChildCodes.forEach(o => {
                        o.sequenceNbr = oldIdNewIdRcjMap.get(o.sequenceNbr);
                    });
                }
            }
        }
    }


    /**
     * 拷贝措施项目定额数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitCsxmDe(constructId, oldUnit, newUnit) {
        let oldDeAll = ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.getNodeById(oldUnit.defaultCsxmId);
        let oldDeAllCopy = await ConvertUtil.deepCopy(oldDeAll);

        oldDeAllCopy.sequenceNbr = newUnit.defaultCsxmId;
        oldDeAllCopy.deRowId = newUnit.defaultCsxmId;
        this.updatePropertyValue(oldDeAllCopy, 'unitId', newUnit.sequenceNbr);
        ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.addNode(oldDeAllCopy);
        let oldIdNewIdMap = new Map();
        oldIdNewIdMap = await this.copyCsxmData(constructId, oldDeAllCopy, oldIdNewIdMap);
        return oldIdNewIdMap;
    }


    /**
     * 拷贝预算书人材机数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssResource(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap) {
        let oldIdNewIdRcjMap = new Map();
        let rcjKey = WildcardMap.generateKey(oldUnit.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let item of rcjList) {
                let itemNew = await ConvertUtil.deepCopy(item);
                this.updatePropertyValue(itemNew, 'unitId', newUnit.sequenceNbr);
                let deRowIdOld = itemNew.deRowId;
                if (oldIdNewIdMap.has(deRowIdOld)) {
                    itemNew.deRowId = oldIdNewIdMap.get(deRowIdOld);
                } else if (oldIdNewIdCsxmMap.has(deRowIdOld)) {
                    itemNew.deRowId = oldIdNewIdCsxmMap.get(deRowIdOld);
                }
                itemNew.deId = itemNew.deRowId;
                itemNew.parentId = itemNew.deRowId;
                itemNew.sequenceNbr = Snowflake.nextId();

                //处理人材机单项批注
                if (ObjectUtils.isNotEmpty(itemNew.annotationsSingleObj)) {
                    if (oldUnit.parentId != newUnit.parentId) {
                        let annotationsSingleObjElement = itemNew.annotationsSingleObj[oldUnit.parentId];
                        if (ObjectUtils.isNotEmpty(annotationsSingleObjElement)) {
                            itemNew.annotationsSingleObj[newUnit.parentId] = ConvertUtil.deepCopy(annotationsSingleObjElement);
                        }
                    }
                }

                if (ObjectUtils.isNotEmpty(itemNew.pbs)) {
                    itemNew.pbs.forEach(m => {
                        m.parentId = itemNew.sequenceNbr;
                    });
                }

                oldIdNewIdRcjMap.set(item.sequenceNbr, itemNew.sequenceNbr);
                ProjectDomain.getDomain(constructId).resourceDomain.ctx.resourceMap.set(WildcardMap.generateKey(newUnit.sequenceNbr, itemNew.deRowId, itemNew.sequenceNbr), itemNew);
            }
        }
        return oldIdNewIdRcjMap;
    }


    /**
     * 拷贝计取缓存数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<void>}
     */
    async repeatInitUnitJiqu(constructId, oldUnit, newUnit) {
        let gsCacheOld = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + oldUnit.sequenceNbr);
        if (ObjectUtils.isNotEmpty(gsCacheOld)) {
            let gsCacheOldCopy = await ConvertUtil.deepCopy(gsCacheOld);
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + newUnit.sequenceNbr, gsCacheOldCopy);
        }
        let ysCacheOld = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + oldUnit.sequenceNbr);
        if (ObjectUtils.isNotEmpty(ysCacheOld)) {
            let ysCacheOldCopy = await ConvertUtil.deepCopy(ysCacheOld);
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + newUnit.sequenceNbr, ysCacheOldCopy);
        }
    }

    /**
     * 拷贝定额工程量明细
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @param oldIdNewIdMap
     * @returns {Promise<void>}
     */
    async repeatInitUnitQuantities(constructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap) {
        //处理定额工程量明细
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let oldUnitQuantiesMap = quantitiesMap.get(oldUnit.sequenceNbr);
        if (ObjectUtils.isNotEmpty(oldUnitQuantiesMap)) {
            let oldMapCopy = await ConvertUtil.deepCopy(oldUnitQuantiesMap);
            let keysToDelete = [];

            oldMapCopy.forEach((value, oldkey) => {
                if (oldIdNewIdMap.has(oldkey)) {
                    keysToDelete.push(oldkey);
                    let newkey = oldIdNewIdMap.get(oldkey);
                    value.constructId = constructId;
                    value.unitId = newUnit.sequenceNbr;
                    value.quotaListId = newkey;
                    if (ObjectUtils.isNotEmpty(value.quantities)) {
                        value.quantities.forEach(o => {
                            o.quotaListId = newkey;
                        });
                    }
                    oldMapCopy.set(newkey, value);
                } else if (oldIdNewIdCsxmMap.has(oldkey)) {
                    keysToDelete.push(oldkey);
                    let newkey = oldIdNewIdCsxmMap.get(oldkey);
                    value.constructId = constructId;
                    value.unitId = newUnit.sequenceNbr;
                    value.quotaListId = newkey;
                    if (ObjectUtils.isNotEmpty(value.quantities)) {
                        value.quantities.forEach(o => {
                            o.quotaListId = newkey;
                        });
                    }
                    oldMapCopy.set(newkey, value);
                }
            });
            keysToDelete.forEach(key => {
                oldMapCopy.delete(key);
            });
            quantitiesMap.set(newUnit.sequenceNbr, oldMapCopy);
        }
    }


    async copyFunctionDataMap(constructId, obj) {
        if (Array.isArray(obj)) {
            //Array
            if (ObjectUtils.isNotEmpty(obj)) {
                for (let o of obj) {
                    o.prentId = obj.sequenceNbr;
                    o.parentId = obj.sequenceNbr;
                    o = await this.copyFunctionDataMap(constructId, o);
                }
            }
        } else {
            //object
            obj.sequenceNbr = Snowflake.nextId();
            if (ObjectUtils.isNotEmpty(obj.children)) {
                for (let o of obj.children) {
                    o = await this.copyFunctionDataMap(constructId, o);
                }
            }
        }
        return obj;
    }


    async copyDeData(constructId, oldDeAllCopy, oldIdNewIdMap) {
        if (ObjectUtils.isNotEmpty(oldDeAllCopy.children)) {
            for (const o of oldDeAllCopy.children) {
                let oldId = o.sequenceNbr;
                o.sequenceNbr = Snowflake.nextId();
                o.deRowId = o.sequenceNbr;
                o.parentId = oldDeAllCopy.sequenceNbr;
                oldIdNewIdMap.set(oldId, o.sequenceNbr);
                ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.nodeMap.set(o.sequenceNbr, o); // 将节点添加到节点映射表中

                if (ObjectUtils.isNotEmpty(o.children)) {
                    oldIdNewIdMap = await this.copyDeData(constructId, o, oldIdNewIdMap);
                }
            }
        }
        return oldIdNewIdMap;
    }

    async copyCsxmData(constructId, oldDeAllCopy, oldIdNewIdMap) {
        if (ObjectUtils.isNotEmpty(oldDeAllCopy.children)) {
            for (const o of oldDeAllCopy.children) {
                let oldId = o.sequenceNbr;
                o.sequenceNbr = Snowflake.nextId();
                o.deRowId = o.sequenceNbr;
                o.parentId = oldDeAllCopy.sequenceNbr;
                oldIdNewIdMap.set(oldId, o.sequenceNbr);
                ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.nodeMap.set(o.sequenceNbr, o); // 将节点添加到节点映射表中

                if (ObjectUtils.isNotEmpty(o.children)) {
                    oldIdNewIdMap = await this.copyCsxmData(constructId, o, oldIdNewIdMap);
                }
            }
        }
        return oldIdNewIdMap;
    }


    updatePropertyValue(obj, propertyKey, newValue) {
        for (let key in obj) {
            if (key == "parent" || key == "prev" || key == "next") {
                continue;
            }

            if (typeof obj[key] === 'object') {
                if (Array.isArray(obj[key])) {
                    // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
                    obj[key].forEach((item) => {
                        this.updatePropertyValue(item, propertyKey, newValue);
                    });
                } else {
                    // 如果属性的值是对象，则递归调用更新函数
                    this.updatePropertyValue(obj[key], propertyKey, newValue);
                }
            } else if (key === propertyKey && !ObjectUtils.isEmpty(obj[key])) {
                // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
                obj[key] = newValue;
            }
        }
    }


    async _editUnitStructure(constructParam, singleId, unitParam, oldConstructFlatMap, parent, ifParentIsConstruct, constructObj) {

        let oldUnit = oldConstructFlatMap.get(unitParam.id);
        let newUnit = oldUnit;

        let constructId = constructParam.id;
        // let constructObj = PricingFileFindUtils.getProjectObjById(constructId);

        if (!ObjectUtils.isEmpty(unitParam.copyFromId)) {
            //代表是复制的单位
            let newUnit = await ConvertUtil.deepCopy(oldUnit);
            let oldUnitId = newUnit.sequenceNbr;
            newUnit.sequenceNbr = Snowflake.nextId();
            newUnit.parentId = singleId;
            newUnit.name = unitParam.name;
            if (ifParentIsConstruct) {
                newUnit.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
                newUnit.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());

                //添加单位
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newUnit.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newUnit, parentNode);

                //重新赋值分部分项等数据的unitId和spId
                await this.repeatInitUnitItemId(newUnit, oldUnitId, constructId);
                constructObj.unitProjectArrayBak.push(newUnit);
            } else {
                //重新赋值单位名称（有重复的话+1）
                await this.repeatInitUnitName(constructId, singleId, newUnit);
                newUnit.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
                newUnit.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());

                //添加单位
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newUnit.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newUnit, parentNode);

                //重新赋值分部分项等数据的unitId和spId
                await this.repeatInitUnitItemId(newUnit, oldUnitId, constructId);
                parent.unitProjectsBak.push(newUnit);
            }
        } else {
            if (ObjectUtils.isEmpty(newUnit)) {// 单位不存在
                let ProjectModel = {
                    name: unitParam.name,
                    parentId: unitParam.parentId,
                    type: ProjectLevelConstant.unit,
                    constructMajorType: unitParam.libraryCode,
                    deLibrary: unitParam.libraryCode
                };
                let arg = {
                    constructId: constructId,
                    ProjectModel: ProjectModel
                };
                newUnit = await this.addSingleUnit(arg);

                // let libraryCode = unitParam.libraryCode;
                // if (!libraryCode && unitParam.constructMajorType) {
                //     libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, true).filter(f => f.defaultDeFlag === 1)[0].libraryCode
                // }
                // let secondInstallationProjectName = constructParam.secondInstallationProjectName ? constructParam.secondInstallationProjectName : unitParam.secondInstallationProjectName;
                // newUnit = await this.initUnit(constructObj, unitParam.parentId, unitParam.id, unitParam.name, unitParam.constructMajorType, null, true, libraryCode, secondInstallationProjectName);
            } else { // 单位存在
                let oldConstructMajorType = newUnit.constructMajorType;

                newUnit.name = unitParam.name;
                newUnit.constructMajorType = unitParam.constructMajorType;
                newUnit.secondInstallationProjectName = unitParam.secondInstallationProjectName;
                newUnit.constructId = constructId;
                newUnit.parentId = singleId;

                // ConstructOperationUtil.updateUnitName(newUnit, unitParam.name);
                // ConstructOperationUtil.updateUnitMajorType(newUnit, unitParam.constructMajorType);

                if (ObjectUtils.isNotEmpty(unitParam.constructMajorType)) {
                    // let libraryCode = newUnit.libraryCode || unitParam.libraryCode;
                    // if (!libraryCode && unitParam.constructMajorType) {
                    //     libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, true).filter(f => f.defaultDeFlag === 1)[0].libraryCode
                    // }
                    // newUnit.mainDeLibrary = libraryCode;
                    //
                    // if (oldConstructMajorType != unitParam.constructMajorType) {
                    //     //保存取费文件
                    //     newUnit = await this.service.baseFeeFileService.initFeeFile(newUnit);
                    // }
                }
            }
            // 将处理后的unit加入父级中
            if (ifParentIsConstruct) {
                constructObj.unitProjectArrayBak.push(newUnit);
            } else {
                parent.unitProjectsBak.push(newUnit);
            }
        }
    }

    async repeatInitUnitName(constructId, singleId, newUnit) {
        // let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
        // let unitProjects = singleProject.unitProjects;
        let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
        let unitProjects = projectTree.filter(o => o.parentId === singleId);
        if (ObjectUtils.isNotEmpty(unitProjects)) {
            let singleUnitNameList = unitProjects.map(obj => obj.name);
            let unitNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newUnit.name);
            newUnit.name = unitNewName;
        }
    }


    /**
     * 平铺工程项目，生成map，每一级对象在map中key为其自身的sequenceNbr
     * @param constructObj
     * @return {Map<any, any>}
     */
    flatConstructTreeToMapByObj(constructObj) {
        let treeMap = new Map();
        constructObj.levelType = 1;
        treeMap.set(constructObj.sequenceNbr, constructObj);

        if (ObjectUtils.isNotEmpty(constructObj.children)) {
            constructObj.children.forEach(o => {
                treeMap.set(o.sequenceNbr, o);
                if (ProjectLevelConstant.single === o.type) {
                    let singleTreeMap = this.flatConstructTreeToMapByObj(o);
                    for (let [key, value] of singleTreeMap.entries()) {
                        treeMap.set(key, value);
                    }
                }
            });
        }

        // let singleProjects = constructObj.singleProjects;
        // if (ObjectUtils.isEmpty(singleProjects)) {
        //     if(ObjectUtils.isNotEmpty(constructObj.unitProjectArray)) {
        //         let unitProjectArray = constructObj.unitProjectArray;
        //         for (let i in unitProjectArray) {
        //             let unitProject = unitProjectArray[i];
        //             unitProject.levelType = 3;
        //             treeMap.set(unitProject.sequenceNbr, unitProject);
        //         }
        //     }
        //     if(ObjectUtils.isNotEmpty(constructObj.unitProject)){
        //         constructObj.unitProject.levelType = 3;
        //         treeMap.set(constructObj.unitProject.sequenceNbr, constructObj.unitProject);
        //     }
        // }else{
        //     for (let i in singleProjects) {
        //         singleProjects[i].parentId = constructObj.sequenceNbr;
        //         let singleTreeMap = this._flatSingleTreeToMap(singleProjects[i]);
        //         for (let [key, value] of singleTreeMap.entries()) {
        //             treeMap.set(key, value);
        //         }
        //     }
        // }

        return treeMap;
    }


    async batchModifyName(args) {
        let constructFlatMap = new Map;

        let projectTree = ProjectDomain.getDomain(args.constructId).getProjectTree(); //复制前当前所有单位
        for (let itemProject of projectTree) {
            constructFlatMap.set(itemProject.sequenceNbr, itemProject);
        }

        for (let item of args.data) {
            let node = constructFlatMap.get(item.id);
            if (node.type == ConstantUtil.CONSTRUCT_LEVEL_TYPE) {
                let project = ProjectDomain.getDomain(args.constructId).getProjectById(item.id);
                project.name = item.name;
                let list = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(null, FunctionTypeConstants.JBXX_KEY_TYPE_11));
                for (let itemJbxx of list) {
                    if (itemJbxx.name === "项目名称") {
                        itemJbxx.remark = item.name;
                    }
                }
                ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(node.sequenceNbr, FunctionTypeConstants.JBXX_KEY_TYPE_11), list);
            } else if (node.type == ConstantUtil.SINGLE_LEVEL_TYPE) {
                let project = ProjectDomain.getDomain(args.constructId).getProjectById(item.id);
                project.name = item.name;
            } else if (node.type == ConstantUtil.UNIT_LEVEL_TYPE) {
                let project = ProjectDomain.getDomain(args.constructId).getProjectById(item.id);
                project.name = item.name;
                let list = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(this.getDataMapKey(node.sequenceNbr, FunctionTypeConstants.JBXX_KEY_TYPE_11));
                for (let itemJbxx of list) {
                    if (itemJbxx.name === "工程名称") {
                        itemJbxx.remark = item.name;
                    }
                }
                ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(this.getDataMapKey(node.sequenceNbr, FunctionTypeConstants.JBXX_KEY_TYPE_11), list);
            }
        }
    }

    /**
     * 11 基本信息 12 编制说明 13 特征
     * @param {*} unitId
     * @param {*} type
     * @returns
     */
    getDataMapKey(unitId, type) {

        if (ObjectUtils.isEmpty(unitId)) {
            unitId = "0";//保持key风格一致性
        }
        return "JBXX-" + unitId + "-" + type;
    }


    async calProjectSingleUnits(constructId, singleId, units) {
        let projectTreeList = ProjectDomain.getDomain(constructId).getProjectTree();
        let filter = projectTreeList.filter(o => o.parentId === singleId);
        if (ObjectUtils.isNotEmpty(filter)) {
            for (let item of filter) {
                if (item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    units.push(item.sequenceNbr)
                } else if (item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
                    units = await this.calProjectSingleUnits(constructId, item.sequenceNbr, units);
                }
            }
        }
        return units;
    }


    // 检查文件是否存在
    checkFileExistence(filePath) {
        try {
            fs.accessSync(filePath, fs.constants.F_OK);
            return true;
        } catch (err) {
            return false;
        }
    }



    /**
     * 历史文件没有保存小数点，先保留小数点，再重新计算
     * @param constructId
     * @param unitProjects
     */
    async repeatCalPrecision(constructId, unitProjects) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(projectDomain.getRoot().sequenceNbr);
        if (ObjectUtil.isEmpty(projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING))) {
            projectDomain.functionDataMap.set(FunctionTypeConstants.PROJECT_PRECISION_SETTING, precision1);
        }

        for (let item of unitProjects) {
            let startTimeUnit = new Date().getTime();
            let deList = projectDomain.deDomain.getDeTree(p => p.unitId === item.sequenceNbr);
            let csxmList = projectDomain.csxmDomain.getDeTree(p => p.unitId === item.sequenceNbr);

            //todo 重新计算独立费
            await this.calDlf(constructId, item, precision1.UNIT_DLF);

            //todo 重新计算工程量明细
            if (ObjectUtil.isNotEmpty(deList)) {
                for (let deRow of deList) {
                    if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
                        && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB)) {
                        await this.service.gongLiaoJiProject.gljQuantitiesService.recaculateQuantity(constructId, item.sequenceNbr, deRow.sequenceNbr);
                    }
                }
            }

            //todo 重新计算取费表
            await this.calFeeRateUnit(constructId, item, precision1.FREE_RATE);

            //todo 重新计算人材机、预算书、措施项目、自动计取
            let startTimeDe = new Date().getTime();
            await this.calDeCsxm(constructId, item, deList, csxmList);
            let endTimeDe = new Date().getTime();


            //todo 重新计算费用计取、费用汇总
            await this.calAutoCostMathCostCodePrice(constructId, item);

            let endTimeUnit = new Date().getTime();
            console.log("----------定额计算时间: " + (endTimeDe - startTimeDe)/1000 + " 秒");
            console.log("----------整个单位计算时间: " + (endTimeUnit - startTimeUnit)/1000 + " 秒");
        }
    }


    /**
     *   重新计算de
     * @param constructId
     * @param unitProjects
     */
    async calProjectDeCsxm(constructId, unitProjects) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(projectDomain.getRoot().sequenceNbr);
        if (ObjectUtil.isEmpty(projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING))) {
            projectDomain.functionDataMap.set(FunctionTypeConstants.PROJECT_PRECISION_SETTING, precision1);
        }
        for (let item of unitProjects) {
            let deList = projectDomain.deDomain.getDeTree(p => p.unitId === item.sequenceNbr);
            let csxmList = projectDomain.csxmDomain.getDeTree(p => p.unitId === item.sequenceNbr);
            //todo 重新计算人材机、预算书、措施项目、自动计取
            let startTimeDe = new Date().getTime();
            await this.calDeCsxm(constructId, item, deList, csxmList);
            let endTimeDe = new Date().getTime();
            console.log("----------定额计算时间: " + (endTimeDe - startTimeDe)/1000 + " 秒");
        }
    }




    async calDlf(constructId, unit, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let unitDlfKey = await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unit.sequenceNbr);
        let list = projectDomain.functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY).get(unitDlfKey);
        await this.service.gongLiaoJiProject.gljIndependentCostsService.caculatorTreeTotal(list, null, precision);

        for (let item of list) {
            //通知费用汇总变动消息
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: unit.parentId,
                unitId: unit.sequenceNbr,
                qfMajorType: item.costMajorCode
            });
        }
    }


    async calFeeRateConstruct(constructId, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        //项目级别
        let projectFee = projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        if (ObjectUtils.isNotEmpty(projectFee.childFreeRate)) {
            for (let [key, value] of projectFee.childFreeRate) {
                if (ObjectUtils.isEmpty(value.manageFeeRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.manageFeeRateUpdate = true;
                }

                if (ObjectUtils.isEmpty(value.profitRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.profitRateUpdate = true;
                }

                if (ObjectUtils.isEmpty(value.taxRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.taxRateUpdate = true;
                }

                if (ObjectUtils.isEmpty(value.anwenRateUpdate)) {
                    //旧文件无manageFeeRateUpdate，默认true
                    value.anwenRateUpdate = true;
                }
            }
        }
    }

    async calFeeRateUnit(constructId, unit, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);

        //重新计算单位级别
        let unitFeeMap = projectDomain.functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isNotEmpty(unitFeeMap)) {
            for (let [key, value] of unitFeeMap) {
                if (key.includes(unit.sequenceNbr)) {
                    if (ObjectUtils.isEmpty(value.manageFeeRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.manageFeeRateUpdate = true;
                    }

                    if (ObjectUtils.isEmpty(value.profitRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.profitRateUpdate = true;
                    }

                    if (ObjectUtils.isEmpty(value.taxRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.taxRateUpdate = true;
                    }

                    if (ObjectUtils.isEmpty(value.anwenRateUpdate)) {
                        //旧文件无manageFeeRateUpdate，默认true
                        value.anwenRateUpdate = true;
                    }
                }
            }
        }


        //重新计算单项级别
        let singleId = unit.parentId;
        let singleFeeMap = projectDomain.functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        if (ObjectUtils.isNotEmpty(singleFeeMap)) {
            for (let [key, value] of singleFeeMap) {
                if (key.includes(singleId)) {
                    if (ObjectUtils.isNotEmpty(value.childFreeRate)) {
                        for (let [key1, value1] of value.childFreeRate) {
                            if (ObjectUtils.isEmpty(value1.manageFeeRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.manageFeeRateUpdate = true;
                            }

                            if (ObjectUtils.isEmpty(value1.profitRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.profitRateUpdate = true;
                            }

                            if (ObjectUtils.isEmpty(value1.taxRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.taxRateUpdate = true;
                            }

                            if (ObjectUtils.isEmpty(value1.anwenRateUpdate)) {
                                //旧文件无manageFeeRateUpdate，默认true
                                value1.anwenRateUpdate = true;
                            }
                        }
                    }
                }
            }
        }

        //重新计算项目级别
        await this.calFeeRateConstruct(constructId, precision);
    }

    async calDeCsxm(constructId, unit, deList, csxmList) {

        let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        deBaseDomain.notifyAll(constructId, unit.sequenceNbr);

        let rcjDeKey = WildcardMap.generateKey(unit.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let rcj of rcjList) {
                await ProjectDomain.getDomain(constructId).getResourceDomain().notifyRCj(rcj);
            }
        }

        // if (ObjectUtil.isNotEmpty(deList)) {
        //     for (let deRow of deList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             // await projectDomain.deDomain.updateQuantityCopy(constructId, unitId, deRowId, deRow.originalQuantity);
        //             try {
        //                 await ProjectDomain.getDomain(constructId).deDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算预算书定额错误");
        //                 console.log(deRow);
        //             }
        //             // await projectDomain.deDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
        // if (ObjectUtil.isNotEmpty(csxmList)) {
        //     for (let deRow of csxmList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB && deRow.type !== DeTypeConstants.DE_TYPE_DELIST)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             try {
        //                 await ProjectDomain.getDomain(constructId).csxmDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算措施项目定额错误");
        //                 console.log(deRow);
        //             }
        //             // await projectDomain.csxmDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
    }

    async calDeCsxmProjectDomain(constructId, unit, deList, csxmList, projectDomain) {
        let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        deBaseDomain.notifyAll(constructId, unit.sequenceNbr);

        let rcjDeKey = WildcardMap.generateKey(unit.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let rcj of rcjList) {
                await ProjectDomain.getDomain(constructId).getResourceDomain().notifyRCj(rcj);
            }
        }

        // if (ObjectUtil.isNotEmpty(deList)) {
        //     for (let deRow of deList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             // await projectDomain.deDomain.updateQuantityCopy(constructId, unitId, deRowId, deRow.originalQuantity);
        //             try {
        //                 await projectDomain.deDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算预算书定额错误");
        //                 console.log(deRow);
        //             }
        //
        //             // await projectDomain.deDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
        // if (ObjectUtil.isNotEmpty(csxmList)) {
        //     for (let deRow of csxmList) {
        //         if (ObjectUtil.isNotEmpty(deRow) && (deRow.type !== DeTypeConstants.DE_TYPE_EMPTY && deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT
        //             && deRow.type !== DeTypeConstants.DE_TYPE_FB && deRow.type !== DeTypeConstants.DE_TYPE_ZFB && deRow.type !== DeTypeConstants.DE_TYPE_DELIST)) {
        //             let unitId = deRow.unitId;
        //             let deRowId = deRow.sequenceNbr;
        //             try {
        //                 await projectDomain.csxmDomain.notify({
        //                     constructId,
        //                     unitId,
        //                     deRowId
        //                 }, true);
        //             } catch (e) {
        //                 console.log("---------------计算措施项目定额错误");
        //                 console.log(deRow);
        //             }
        //
        //             // await projectDomain.csxmDomain.updateQuantity(constructId, unitId, deRowId, deRow.originalQuantity);
        //         }
        //     }
        // }
    }


    async calAutoCostMathCostCodePrice(constructId, unit) {
        try {
            //费用计取
            this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
                unitId: unit.sequenceNbr,
                singleId: null,
                constructId: constructId
            });

            //费用汇总
            this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                unitId: unit.sequenceNbr,
                qfMajorType: unit.qfMajorType
            });
        } catch (error) {
            console.error("捕获到异常:", error);
        }
    }

    async importUnitExcelCheck(args){
        let result = [];
        for(let filePath of args.excels){
            let excelName = filePath.substring(filePath.lastIndexOf("\\") + 1);
            let unitTemp = {
                "children": [],
                "directory": true,
                "filePath": filePath,
                "fileType": 1,
                "level": 2,
                "levelType": 3,
                "name": excelName.substring(0, excelName.lastIndexOf(".")),
            }

            await this._checkUnit(unitTemp,filePath, args.constructId);
            result.push(unitTemp)
        }

        return result;
    }

    async _checkUnit(unitNodeCur,filePath, constructId){
        let excelVer = filePath.toLowerCase().endsWith(".xlsx") ? GljExcelUtil.VER_2007 : GljExcelUtil.VER_2003;
        const options = {version: excelVer};
        const workbook = await GljExcelUtil.readToWorkBook(filePath, options); //XLSX.readFile(filePath);
        const readSheetNames = GljExcelUtil.getSheetNames(workbook, options);

        let sheetResult = [];
        let sheet8Name = null;
        let args = {};
        args['isContainsRequiredSheets'] = false;
        if (readSheetNames.includes(GljExcelSheetList.STXMYSB) &&
            readSheetNames.includes(GljExcelSheetList.CSXMYSB) &&
            readSheetNames.includes(GljExcelSheetList.FXGCRCJHZB_ST) &&
            readSheetNames.includes(GljExcelSheetList.FXGCRCJHZB_CS)
        ) {
            //必须传的sheet页
            args['isContainsRequiredSheets'] = true;
        }

        for (let i = 0; i < readSheetNames.length; i++) {
            const sheetName = readSheetNames[i];
            const temp = this._generateTreeNode(sheetName,unitNodeCur.name, null, this.sheetLevelType,
                filePath, null, this.fileTypeNumber, null, sheetName, null);
            temp.code = GljFileCheckFormatResultEnum.SUCCESS.code;

            const sheetNameByFileName = this._findSheetNameByFileNameAndReturnFlag(sheetName,args);

            if (ObjectUtils.isEmpty(sheetNameByFileName)) {
                temp.code = GljFileCheckFormatResultEnum.NOT_STANDARD.code;
                temp.errorMsg = {[GljFileCheckFormatResultEnum.NOT_STANDARD.code]: GljFileCheckFormatResultEnum.NOT_STANDARD.msg};
            }
            if (sheetName.includes(GljExcelSheetList.CSXMYSB)) {
                sheet8Name = sheetName;
            }

            //必传的sheet页必须有数据，无数据返回错误
            await this.checkSheetRequiredData(readSheetNames, filePath, options, workbook, sheetName, args);

            sheetResult.push(temp);
        }



        if (!args['isContainsRequiredSheets']) {
            unitNodeCur.code = GljFileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code;
            unitNodeCur.errorMsg = {[GljFileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code]: GljFileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.msg};
        }

        let constructDeStandard =  ProjectDomain.getDomain(constructId).getProjectById(constructId).deStandardReleaseYear;
        // 设置默认工程专业
        let defaultMajorType = {};
        if(ObjectUtils.isNotEmpty(sheet8Name)){
            let contents = await GljExcelUtil.readSheetContentByWorkBook(workbook, sheet8Name, options);
            contents = contents || [];
            let rows = [];

            for (let r of contents) {
                let fxCode = r.get(2);
                if (ObjectUtils.isEmpty(fxCode) || !/^[A-Z]/.test(fxCode)) {
                    continue;
                }

                rows.push({
                    kind: ConstantUtil.QD_KIND,
                    name: r.get(2),
                    fxCode
                })
            }

            if (ObjectUtils.isNotEmpty(rows)) {
                defaultMajorType = await this._getUnitDefaultMajorType(rows, constructDeStandard);
            } else {
                //查询不出来专业，说明定额无数据
                unitNodeCur.code = GljFileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code;
                unitNodeCur.errorMsg = {[GljFileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code]: GljFileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.msg};
            }
        }

        if (ObjectUtils.isNotEmpty(defaultMajorType.libraryCode)) {
            unitNodeCur.deLibrary = defaultMajorType.libraryCode;
            unitNodeCur.libraryCode = defaultMajorType.libraryCode;
            unitNodeCur.constructMajorType = defaultMajorType.libraryCode;
            unitNodeCur.defaultMajorType = defaultMajorType.libraryCode;

            unitNodeCur.qfMajorType = defaultMajorType.qfCode;
            unitNodeCur.qfMajorName = defaultMajorType.qfName;
            unitNodeCur.qfMainMajorType = defaultMajorType.qfCode;
            unitNodeCur.qfPartMajorType = defaultMajorType.qfCode;
        }
        unitNodeCur.children = sheetResult;
    }


    async checkSheetRequiredData(readSheetNames, filePath, options, workbook, sheetName, args) {
        //必传的sheet页必须有数据，无数据返回错误

        if (sheetName.includes(GljExcelSheetList.STXMYSB)) {
            const rows = GljExcelUtil.readSheetContentByWorkBook(workbook, sheetName, options)
            let dwgcjgclbList = await GljExcelImportAnalyzingUtil._readStxmysb(rows, sheetName);
            if (ObjectUtils.isEmpty(dwgcjgclbList)) {
                args['isContainsRequiredSheets'] = false;
            }
        }
        if (sheetName.includes(GljExcelSheetList.CSXMYSB)) {
            const rows = GljExcelUtil.readSheetContentByWorkBook(workbook, sheetName, options)
            let dwgcjgclbList = await GljExcelImportAnalyzingUtil._readCsxmysb(rows, sheetName);
            if (ObjectUtils.isEmpty(dwgcjgclbList)) {
                args['isContainsRequiredSheets'] = false;
            }
        }
        if (sheetName.includes(GljExcelSheetList.FXGCRCJHZB_ST)) {
            const rows = GljExcelUtil.readSheetContentByWorkBook(workbook, sheetName, options)
            let dwgcjgclbList = await GljExcelImportAnalyzingUtil._readFxgcrcjhzbSt(rows, sheetName);
            if (ObjectUtils.isEmpty(dwgcjgclbList)) {
                args['isContainsRequiredSheets'] = false;
            }
        }

        if (sheetName.includes(GljExcelSheetList.FXGCRCJHZB_CS)) {
            const rows = GljExcelUtil.readSheetContentByWorkBook(workbook, sheetName, options)
            let dwgcjgclbList = await GljExcelImportAnalyzingUtil._readFxgcrcjhzbCs(rows, sheetName);
            if (ObjectUtils.isEmpty(dwgcjgclbList)) {
                args['isContainsRequiredSheets'] = false;
            }
        }
    }

    _generateTreeNode(name,patentName, level, levelType, filePath, relativePath, fileType, directory, sheetName, parent){
        let node = {
            biddingType: null,
            children: null,
            code: 0,
            constructMajorType: null,
            directory,
            errorMsg: null,
            filePath,
            fileType,
            id: null,
            level,
            levelType: levelType || 0,
            libraryCode: null,
            name,
            parentId: null,
            relativePath,
            secondInstallationProjectName: null,
            sheetName,
            patentName,
            parent
        };
        if(parent){
            parent.children = parent.children || [];
            parent.children.push(node);
        }

        return node;
    }

    _findSheetNameByFileNameAndReturnFlag(fileName, args) {
        // 根据sheet的sheet名找到标准格式的sheet名
        if (fileName.includes(GljExcelSheetList.FM)) {
            return GljExcelSheetList.FM;
        } else if (fileName.includes(GljExcelSheetList.BZSM)) {
            return GljExcelSheetList.BZSM;
        } else if (fileName.includes(GljExcelSheetList.STXMYSB)) {
            // args['isContainsRequiredSheets'] = true;
            return GljExcelSheetList.STXMYSB;
        } else if (fileName.includes(GljExcelSheetList.CSXMYSB)) {
            // args['isContainsRequiredSheets'] = true;
            return GljExcelSheetList.CSXMYSB;
        } else if (fileName.includes(GljExcelSheetList.DLFB)) {
            return GljExcelSheetList.DLFB;
        } else if (fileName.includes(GljExcelSheetList.DWGCJGCLB)) {
            return GljExcelSheetList.DWGCJGCLB;
        } else if (fileName.includes(GljExcelSheetList.DWGCSCHZB)) {
            return GljExcelSheetList.DWGCSCHZB;
        } else if (fileName.includes(GljExcelSheetList.FXGCRCJHZB_ST)) {
            // args['isContainsRequiredSheets'] = true;
            return GljExcelSheetList.FXGCRCJHZB_ST;
        } else if (fileName.includes(GljExcelSheetList.FXGCRCJHZB_CS)) {
            // args['isContainsRequiredSheets'] = true;
            return GljExcelSheetList.FXGCRCJHZB_CS;
        } else if (fileName.includes(GljExcelSheetList.DWGCGCLJSS_ST)) {
            return GljExcelSheetList.DWGCGCLJSS_ST;
        } else if (fileName.includes(GljExcelSheetList.DWGCGCLJSS_CS)) {
            return GljExcelSheetList.DWGCGCLJSS_CS;
        } else if (fileName.includes(GljExcelSheetList.SDFMXB)) {
            return GljExcelSheetList.SDFMXB;
        } else if (fileName.includes(GljExcelSheetList.SDFMXB_DLSZ)) {
            return GljExcelSheetList.SDFMXB_DLSZ;
        }
        // 修改返回值为 null，用于判断是否是标准的 sheet 名字
        return null;
    }


    async analysisImportUnits(params) {
        if(ObjectUtils.isEmpty(params.units)){
            return 200;
        }

        let constructProject = ProjectDomain.getDomain(params.constructId).getProjectById(params.constructId);
        let singleProject =  ProjectDomain.getDomain(params.constructId).getProjectById(params.singleId);
        let parentParam = {
            biddingType: constructProject.biddingType,
            constructId: params.constructId,
            sequenceNbr: params.singleId
        }

        let unitSortNo = 1;
        if(ObjectUtils.isNotEmpty(params.singleId)){
            unitSortNo = singleProject.unitProjects?.length || 1;
        }else{
            unitSortNo = constructProject.unitProjectArray?.length || 1;
        }

        let unitsTmp = [];
        for (let i = 0; i < params.units.length; i++) {
            let oriUnit = params.units[i];
            if (ObjectUtils.isNotEmpty(oriUnit.errorMsg)) {
                continue;
            }
            // 如果传入的定额标准为空，则默认取工程项目定额标准
            oriUnit.deStandardId = oriUnit.deStandardId || constructProject.deStandardId;
            let unitSwap = await this._readUnit(oriUnit, parentParam, unitSortNo++);
            unitsTmp.push(unitSwap);
        }
        if(unitsTmp.length > 0){
            let units = await this.convertUnitProject(unitsTmp, params.constructId, params.singleId, constructProject)

            for (let j = 0; j < units.length; j++) {
                let projectModel = units[j];

                try {
                    if (projectModel.qfMajorTypeMoneyMap) {
                        let qfMajorTypeMoneyMap = new Map(Object.entries(projectModel.qfMajorTypeMoneyMap));
                        for (let qfMajorType of Array.from(qfMajorTypeMoneyMap.keys())) {
                            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                                constructId: params.constructId,
                                unitId: projectModel.sequenceNbr,
                                singleId: params.singleId,
                                constructMajorType: qfMajorType
                            });
                        }
                    }
                } catch (error) {
                    console.error("捕获到异常:", error);
                }
            }
        }

        return 200;
    }


    async _readUnit(oriUnit, single, sortNo, bizTree) {
        let unit = {};

        unit.sequenceNbr = oriUnit.id;
        unit.deStandardId = oriUnit.deStandardId || bizTree.deStandardId;
        unit.sortNo = sortNo;
        unit.name = oriUnit.name;
        unit.biddingType = single.biddingType;
        unit.constructMajorType = oriUnit.constructMajorType;
        unit.defaultMajorType = oriUnit.defaultMajorType;
        // unit.secondInstallationProjectName = oriUnit.secondInstallationProjectName;
        unit.parentId = single.sequenceNbr;
        unit.constructId = single.constructId;
        unit.libraryCode = oriUnit.libraryCode;
        unit.mainDeLibrary = oriUnit.libraryCode;
        unit.qfMainMajorType = oriUnit.qfMainMajorType;
        unit.qfPartMajorType = oriUnit.qfPartMajorType;
        unit.qfMajorType = oriUnit.qfMajorType;
        unit.qfMajorName = oriUnit.qfMajorName;

        // 主专业处理, 此处oriUnit.secondInstallationProjectName是主专业
        // if(ObjectUtils.isNotEmpty(oriUnit.secondInstallationProjectName)){
        //     //设置定额标准发布年份
        //     let deStandard = await this.service.baseListDeStandardService.quotaStandardById(unit.deStandardId);
        //
        //     let is22 = (deStandard.releaseYear == ConstantUtil.DE_STANDARD_22);
        //     if(is22) {
        //
        //         let result = await this.app.appDataSource.getRepository(BaseSecondaryMajor2022).findOne({
        //             where: {
        //                 libraryCode: oriUnit.libraryCode,
        //                 majorName: oriUnit.secondInstallationProjectName
        //             }
        //         });
        //         unit.secondInstallationProjectName = result.cslbName;
        //         unit.majorName = oriUnit.secondInstallationProjectName;
        //
        //     }else{
        //         unit.secondInstallationProjectName = oriUnit.secondInstallationProjectName;
        //         unit.majorName = oriUnit.secondInstallationProjectName;
        //     }
        // }

        if (ObjectUtils.isEmpty(oriUnit.children)) {
            return unit;
        }

        // 获取单位工程xlsx路径
        let excelPath = oriUnit.children[0].filePath;
        let excelVer = excelPath.toLowerCase().endsWith(".xlsx") ? GljExcelUtil.VER_2007 : GljExcelUtil.VER_2003;
        let options = {version: excelVer};
        const wb = await GljExcelUtil.readToWorkBook(excelPath, options)
        for (let i = 0; i < oriUnit.children.length; i++) {
            const sheetParam = oriUnit.children[i];
            const sheetName = sheetParam.name;
            const rows = GljExcelUtil.readSheetContentByWorkBook(wb, sheetName, options)

            if (sheetName.includes(GljExcelSheetList.FM)) {
                // let fm = await GljExcelImportAnalyzingUtil._readFm(rows, sheetName);
                // unit[GljExcelSheetList.FM] = fm;
            } else if (sheetName.includes(GljExcelSheetList.BZSM)) {
                // let bzsmList = await GljExcelImportAnalyzingUtil._readBzsm(rows, sheetName,GljExcelSheetList.BZSM);
                // unit[GljExcelSheetList.BZSM] = bzsmList;
            }else if (sheetName.includes(GljExcelSheetList.STXMYSB)) {
                let stxmysbList = await GljExcelImportAnalyzingUtil._readStxmysb(rows, sheetName,GljExcelSheetList.STXMYSB);
                let stxmysbList1=[];
                for(let item of stxmysbList){
                    if (ObjectUtils.isNotEmpty(item.序号)) {
                        if (item.序号 === "实体项目预算表" || item.序号 === "序号" || item.序号.includes("工程名称：") || item.序号.includes("计税")) {
                            continue;
                        }
                    }
                    if (ObjectUtils.isNotEmpty(item.单位) && item.单位==="单位") {
                        continue;
                    }
                    stxmysbList1.push(item);
                }
                unit[GljExcelSheetList.STXMYSB] = stxmysbList1;
            } else if (sheetName.includes(GljExcelSheetList.CSXMYSB)) {
                let csxmysbList = await GljExcelImportAnalyzingUtil._readCsxmysb(rows, sheetName,GljExcelSheetList.CSXMYSB);
                let csxmysbList1=[];
                for(let item of csxmysbList){
                    if (ObjectUtils.isNotEmpty(item.序号)) {
                        if (item.序号 === "措施项目预算表" || item.序号 === "序号" || item.序号.includes("工程名称：") || item.序号.includes("计税")) {
                            continue;
                        }
                    }
                    if (ObjectUtils.isNotEmpty(item.单位) && item.单位==="单位") {
                        continue;
                    }
                    csxmysbList1.push(item);
                }
                unit[GljExcelSheetList.CSXMYSB] = csxmysbList1;
            } else if (sheetName.includes(GljExcelSheetList.DLFB)) {
                let dlfbList = await GljExcelImportAnalyzingUtil._readDlfb(rows, sheetName);
                unit[GljExcelSheetList.DLFB] = dlfbList;
            } else if (sheetName.includes(GljExcelSheetList.DWGCJGCLB)) {
                let dwgcjgclbList = await GljExcelImportAnalyzingUtil._readDwgcjgclb(rows, sheetName);
                unit[GljExcelSheetList.DWGCJGCLB] = dwgcjgclbList;
            } else if (sheetName.includes(GljExcelSheetList.DWGCSCHZB)) {
                let dwgcschzbList = await GljExcelImportAnalyzingUtil._readDwgcschzb(rows, sheetName);
                unit[GljExcelSheetList.DWGCSCHZB] = dwgcschzbList;
            } else if (sheetName.includes(GljExcelSheetList.FXGCRCJHZB_ST)) {
                let fxgcrcjhzbStList = await GljExcelImportAnalyzingUtil._readFxgcrcjhzbSt(rows, sheetName);
                let fxgcrcjhzbStList1=[];
                for(let item of fxgcrcjhzbStList){
                    if (ObjectUtils.isNotEmpty(item.序号)) {
                        if (item.序号 === sheetName || item.序号 === "序号" || item.序号.includes("工程名称：")) {
                            continue;
                        }
                    }
                    if (ObjectUtils.isNotEmpty(item.单位) && item.单位==="单位") {
                        continue;
                    }
                    fxgcrcjhzbStList1.push(item);
                }
                unit[GljExcelSheetList.FXGCRCJHZB_ST] = fxgcrcjhzbStList1;
            } else if (sheetName.includes(GljExcelSheetList.FXGCRCJHZB_CS)) {
                let fxgcrcjhzbCsList = await GljExcelImportAnalyzingUtil._readFxgcrcjhzbCs(rows, sheetName);
                let fxgcrcjhzbCsList1=[];
                for(let item of fxgcrcjhzbCsList){
                    if (ObjectUtils.isNotEmpty(item.序号)) {
                        if (item.序号 === sheetName || item.序号 === "序号" || item.序号.includes("工程名称：")) {
                            continue;
                        }
                    }
                    if (ObjectUtils.isNotEmpty(item.单位) && item.单位==="单位") {
                        continue;
                    }
                    fxgcrcjhzbCsList1.push(item);
                }
                unit[GljExcelSheetList.FXGCRCJHZB_CS] = fxgcrcjhzbCsList1;
            } else if (sheetName.includes(GljExcelSheetList.DWGCGCLJSS_ST)) {
                let dwgcgcljssStList = await GljExcelImportAnalyzingUtil._readDwgcgcljssSt(rows, sheetName);
                unit[GljExcelSheetList.DWGCGCLJSS_ST] = dwgcgcljssStList;
            } else if (sheetName.includes(GljExcelSheetList.DWGCGCLJSS_CS)) {
                let dwgcgcljssCsList = await GljExcelImportAnalyzingUtil._readDwgcgcljssCs(rows, sheetName);
                unit[GljExcelSheetList.DWGCGCLJSS_CS] = dwgcgcljssCsList;
            } else if (sheetName.includes(GljExcelSheetList.SDFMXB_DLSZ)) {
                let sdfmxbDlszList = await GljExcelImportAnalyzingUtil._readSdfmxbDlsz(rows, sheetName);
                unit[GljExcelSheetList.SDFMXB_DLSZ] = sdfmxbDlszList;
            } else if (sheetName.includes(GljExcelSheetList.SDFMXB)) {
                let sdfmxbList = await GljExcelImportAnalyzingUtil._readSdfmxb(rows, sheetName);
                unit[GljExcelSheetList.SDFMXB] = sdfmxbList;
            }
        }

        return unit;
    }

    /**
     * 单位工程
     * @param units
     * @param singleProject
     * @returns {Promise<any[]>}
     */
    async convertUnitProject(units, constructId, singleId, constructProject) {
        let unitProjects = new Array();
        if (!ObjectUtils.isEmpty(units)) {
            for (let i = 0; i < units.length; i++) {
                let unit = units[i];
                let unitProject = new ProjectModel();
                ConvertUtil.setDstBySrc(unit, unitProject)
                await this.repeatInitUnitName(constructId, singleId, unitProject);
                unitProject.deLibrary = unit.libraryCode;
                unitProject.libraryCode = unit.libraryCode;
                unitProject.qfMajorType = unit.qfMajorType;
                unitProject.qfMajorName = unit.qfMajorName;
                unitProject.sequenceNbr = Snowflake.nextId();
                unitProject.type = ProjectTypeConstants.PROJECT_TYPE_UNIT;
                unitProject.parentId = singleId;
                unitProject.scopeFlag = true;
                unitProject.isPartSingleMajorFlag = false;
                unitProject.isSingleMajorFlag = false;
                let prentProject = ProjectDomain.getDomain(constructId).getProjectById(ObjectUtils.isNotEmpty(singleId)?singleId:constructId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(unitProject, prentProject);
                unitProject.defaultDeId = ProjectDomain.getDomain(constructId).deDomain.initDefaultDE(constructId, unitProject.sequenceNbr)?.sequenceNbr;
                unitProject.defaultCsxmId = ProjectDomain.getDomain(constructId).csxmDomain.initDefaultDE(constructId, unitProject.sequenceNbr)?.sequenceNbr;
                await this.service.gongLiaoJiProject.gljInitUnitProjectService.init(unitProject, constructId);
                // 编制说明 ---单位层级
                // await this.service.constructProjectService.initProjectOrUnitBZSM(3, unitProject);
                let qfMajorTypeMoneyMap = unitProject.qfMajorTypeMoneyMap;
                //封面
                // await this.addDataFm(unit[GljExcelSheetList.FM], constructId, unitProject);
                // //编制说明
                // await this.addDataBzsm(unit[GljExcelSheetList.BZSM], constructId, unitProject);
                //实体项目预算表
                // await this.addDataStxmysb(unit[GljExcelSheetList.STXMYSB], constructId, unitProject);
                //措施项目预算表
                // await this.addDataCsxmysb(unit[GljExcelSheetList.CSXMYSB], constructId, unitProject);
                //独立费表
                await this.addDataDlf(unit[GljExcelSheetList.DLFB], constructId, unitProject);
                //分项工程人材机汇总表(实体)
                await this.addDataFxgcrcjhzbSt(unit[GljExcelSheetList.FXGCRCJHZB_ST], constructId, unitProject);
                //分项工程人材机汇总表(措施)
                await this.addDataFxgcrcjhzbCs(unit[GljExcelSheetList.FXGCRCJHZB_CS], constructId, unitProject);
                //单位工程工程量计算式(实体)
                await this.addDataDwgcgcljssSt(unit[GljExcelSheetList.DWGCGCLJSS_ST], constructId, unitProject);
                //单位工程工程量计算式(措施)
                await this.addDataDwgcgcljssCs(unit[GljExcelSheetList.DWGCGCLJSS_CS], constructId, unitProject);
                //单位工程甲供材料表
                // await this.addDataDwgcjgclb(unit[GljExcelSheetList.DWGCJGCLB], constructId, unitProject);
                // //单位工程三材汇总表
                // await this.addDataDwgcschzb(unit[GljExcelSheetList.DWGCSCHZB], constructId, unitProject);
                //水电费
                await this.addDataSdf(unit[GljExcelSheetList.SDFMXB], constructId, unitProject);
                //水电费-独立设置
                await this.addDataSdfDlsz(unit[GljExcelSheetList.SDFMXB_DLSZ], constructId, unitProject);
                unitProject.qfMajorTypeMoneyMap = qfMajorTypeMoneyMap;
                unitProjects.push(unitProject);
            }

        }

        return unitProjects;
    }


    async _getUnitDefaultMajorType(zjcjQds = [], constructDeStandard) {
        let baseRcjDao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDeAwfRelation2022);
        let dataBaseBaseFeeFileProject = await baseRcjDao.find({
            where: {deCode: zjcjQds[0].fxCode}
        });
        return ObjectUtils.isNotEmpty(dataBaseBaseFeeFileProject) ? dataBaseBaseFeeFileProject[0] : {};
    }


    /**
     *
     * @param args
     * @return {{msg: string, code: number, data: null}|{importFileUrl: *, fileLevelTreeNodes: *[], sequenceNbr}}
     */
    async uploadUnitCheckToConstruct(args) {

        let importFileUrl = args.importFileUrl;
        let unitNameXlsx = args.unitNameXlsx;
        let unitName = args.unitName;
        let constructId = args.constructId;
        let cloudStorageUrl = args.cloudStorageUrl;
        let fileTreeNode = args.fileTreeNode;
        let singleName = args.upParentFileName;
        let unitFileUrl = args.unitFileUrl;

        // 1. 创建返回结果参数集
        let result = { code: 0, msg: '', data: null };

        try {            // 拷贝一份url，用于获取单项node结点

            let {singleNode, unitNodeCur} = this._findCurUnitNodeAndParent(fileTreeNode, unitFileUrl);

            // 12. 修改导入单位工程节点的信息及单项工程节点的信息
            let targetList = [];
            // 遍历该单项工程获取下面所有缺失单位工程项目集合
            this._findLackUnits(singleNode, targetList);

            unitNodeCur.code = FileCheckFormatResultEnum.SUCCESS.code;
            unitNodeCur.errorMsg = null;

            fs.copyFileSync(importFileUrl, unitNodeCur.filePath + "\\" + unitNameXlsx)

            const workbook = XLSX.readFile(importFileUrl);
            const readSheetNames = workbook.SheetNames;

            let sheetResult = [];
            let isHaveSheet6 = false;
            let isHaveSheet7 = false;
            let sheet8Index = null;

            for (let i = 0; i < readSheetNames.length; i++) {
                const sheetName = readSheetNames[i];
                const temp = this._generateTreeNode(sheetName,unitNodeCur.name, null, this.sheetLevelType,
                     unitNodeCur.filePath + "\\" + unitNameXlsx, unitNodeCur.relativePath + "\\" + unitNameXlsx, this.fileTypeNumber, null, sheetName, unitNodeCur);
                temp.code = FileCheckFormatResultEnum.SUCCESS.code;

                const sheetNameByFileName = this._findSheetNameByFileName(sheetName);

                if (ObjectUtils.isEmpty(sheetNameByFileName)) {
                    temp.code = FileCheckFormatResultEnum.NOT_STANDARD.code;
                    temp.errorMsg = {[FileCheckFormatResultEnum.NOT_STANDARD.code]: FileCheckFormatResultEnum.NOT_STANDARD.msg};
                }

                if (sheetName.includes(GljExcelSheetList.SHEET6_1) && sheetName.includes(GljExcelSheetList.SHEET6_2)) {
                    temp.sheetName = GljExcelSheetList.SHEET6;
                    isHaveSheet6 = true;
                } else if (sheetName.includes(GljExcelSheetList.SHEET7_1) && sheetName.includes(GljExcelSheetList.SHEET7_2)) {
                    temp.sheetName = GljExcelSheetList.SHEET7;
                    isHaveSheet7 = true;
                    sheet8Index = i;
                } else if (sheetName.includes(GljExcelSheetList.SHEET8_1) && sheetName.includes(GljExcelSheetList.SHEET8_2)) {
                    if (sheet8Index === null) {
                        sheet8Index = i;
                    }
                }

                sheetResult.push(temp);
            }

            // 如果缺失1-6,把1-6加到1-7/1-8前面
            if (!isHaveSheet6) {
                // sheet6
                let sheet6 = this._generateTreeNode(GljExcelSheetList.SHEET6, unitNodeCur.name, null, this.sheetLevelType, null, null, this.fileTypeNumber, null, ExcelSheetList.SHEET6, unitNodeCur );
                sheet6.code = FileCheckFormatResultEnum.LACK_PACKAGE_FIELD.code;
                sheet6.errorMsg = {[FileCheckFormatResultEnum.LACK_PACKAGE_FIELD.code]:  FileCheckFormatResultEnum.LACK_PACKAGE_FIELD.msg};

                // set 单位不完整 getParentNode
                unitNodeCur.code = FileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code;
                unitNodeCur.errorMsg = {[FileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code]:FileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.msg};

                // 如果缺失1-6,把1-6加到1-7/1-8前面
                if (sheet8Index !== null) {
                    sheetResult.splice(sheet8Index, 0, sheet6);
                } else {
                    // 没找到1-7/1-8
                    sheetResult.push(sheet6);
                }
            }
            // 如果缺失1-7，吧1-7加到1-8前面
            if (!isHaveSheet7) {
                // sheet7
                let sheet7 = this._generateTreeNode(GljExcelSheetList.SHEET7, unitNodeCur.name, null, this.sheetLevelType, null, null, this.fileTypeNumber, null, ExcelSheetList.SHEET7, unitNodeCur);

                sheet7.code = FileCheckFormatResultEnum.LACK_PACKAGE_FIELD.code;
                sheet7.errorMsg = {[FileCheckFormatResultEnum.LACK_PACKAGE_FIELD.code]: FileCheckFormatResultEnum.LACK_PACKAGE_FIELD.msg};


                // set 单位不完整 getParentNode
                unitNodeCur.code = FileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code;
                unitNodeCur.errorMsg = {[FileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.code]: FileCheckFormatResultEnum.LACK_UNIT_PROJECT_DETAIL.msg};

                // 如果缺失1-7，吧1-7加到1-8前面
                if (sheet8Index !== null) {
                    // 如果加了1-6，那把1-7加到1-6后面
                    if (!isHaveSheet6) {
                        sheet8Index++;
                    }
                    sheetResult.splice(sheet8Index, 0, sheet7);
                } else {
                    // 没找到1-7/1-8
                    sheetResult.push(sheet7);
                }
            }


            unitNodeCur.children = sheetResult;

            // 修改单项工程项目树节点信息
            if (targetList.length === 1) {
                // 修改单项工程项目树节点信息
                singleNode.code = FileCheckFormatResultEnum.SUCCESS.code;
                singleNode.errorMsg = null;
            }
            // 封装返回值
            return {
                sequenceNbr: constructId,
                importFileUrl,
                fileLevelTreeNodes: [fileTreeNode]
            };
        } catch (error) {
            // 处理异常
            console.log(error)

            result.code = FileCheckFormatResultEnum.ANALYSIS_FILE_FAIL.code;
            result.msg = FileCheckFormatResultEnum.ANALYSIS_FILE_FAIL.msg;
        }

        return result;
    }

    _findCurUnitNodeAndParent(fileTreeNode, unitFileUrl) {
        let singleNode = null;
        let unitNodeCur = null;

        let nextNodes = fileTreeNode.children;
        for (let node of nextNodes) {

            if(node.filePath === unitFileUrl){
                singleNode = fileTreeNode;
                unitNodeCur = node;
                break;
            }

            if(node.levelType <= ConstantUtil.UNIT_LEVEL_TYPE && ObjectUtils.isNotEmpty(node.children)){
                let res = this._findCurUnitNodeAndParent(node, unitFileUrl);
                if(res.unitNodeCur != null){
                    singleNode = res.singleNode;
                    unitNodeCur = res.unitNodeCur;
                    break;
                }
            }
        }
        return {singleNode, unitNodeCur};
    }

    _findLackUnits(fileTreeNode, targetList) {
        for (let node of fileTreeNode.children) {
            if (node.levelType === this.unitLevelType && node.code === FileCheckFormatResultEnum.LACK_UNIT_PROJECT.code) {
                targetList.push(node);
                continue
            }
            if (node.children.length !== 0) {
                this._findLackUnits(node, targetList);
            }
        }
    }

    async addDataFm(dlfList, constructId, unitProject) {

    }

    async addDataBzsm(dlfList, constructId, unitProject) {

    }

    async addDataStxmysb(dlfList, constructId, unitProject) {

    }
    async addDataCsxmysb(dlfList, constructId, unitProject) {

    }

    async addDataDlf(dlfList, constructId, unitProject) {
        if (ObjectUtils.isEmpty(dlfList) || dlfList.length <= 1) {
            return;
        }

        let argSave = {};
        let calSave = false;

        let parentMap = new Map();
        let parentId = null;
        let dlfListFun = [];
        for (let obj of dlfList) {
            let dlfObj = {};
            let length = obj.序号.split('.').length - 1;
            dlfObj.dispNo = obj.序号;
            dlfObj.sequenceNbr = Snowflake.nextId();
            dlfObj.name = obj.费用名称;
            dlfObj.unit = obj.单位;
            dlfObj.quantity = obj.数量;
            dlfObj.price = obj.单价;
            dlfObj.totalPrice = obj.合价;

            if (length === 0) {
                parentId = dlfObj.sequenceNbr;
                dlfObj.parentId = null;
                dlfObj.levelType = 1;
                parentMap.set(length, parentId);
            } else if (length === 1) {
                parentId = dlfObj.sequenceNbr;
                dlfObj.parentId = parentMap.get(length - 1);
                dlfObj.levelType = 2;
                dlfObj.costMajorCode = unitProject.qfMajorType;
                dlfObj.costMajorName = unitProject.qfMajorName;
                parentMap.set(length, parentId);
            } else if (length === 2) {
                parentId = dlfObj.sequenceNbr;
                dlfObj.parentId = parentMap.get(length - 1);
                dlfObj.levelType = 3;
                dlfObj.costMajorCode = unitProject.qfMajorType;
                dlfObj.costMajorName = unitProject.qfMajorName;
                parentMap.set(length, parentId);
            }

            if (!calSave && ObjectUtils.isNotEmpty(dlfObj.unit) && dlfObj.levelType >= 2) {
                calSave = true;
                let iCosts = ConvertUtil.deepCopy(dlfObj);
                argSave.constructId = constructId;
                argSave.unitId = unitProject.sequenceNbr;
                argSave.levelType = dlfObj.levelType;
                argSave.operateType = "edit";
                argSave.iCosts = iCosts;
            }

            dlfListFun.push(dlfObj);
        }

        for (let obj1 of dlfListFun) {
            let filter = dlfListFun.filter(p => ObjectUtils.isNotEmpty(p.parentId) && p.parentId == obj1.sequenceNbr);
            if (ObjectUtils.isEmpty(filter)) {
                obj1.isCalculateAwf = true;
            }
        }

        let unitDlfKey = await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unitProject.sequenceNbr);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY).set(unitDlfKey, dlfListFun);
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        await this.calDlf(constructId, unitProject, precision1.UNIT_DLF);

        if (ObjectUtils.isNotEmpty(argSave.unitId)) {
            //触发计算
            await this.service.gongLiaoJiProject.gljIndependentCostsService.save(argSave);
        }
    }

    async addDataDwgcjgclb(dlfList, constructId, unitProject) {

    }
    async addDataDwgcschzb(dlfList, constructId, unitProject) {

    }

    async addDataSdf(sdfList, constructId, unitProject) {
        if (ObjectUtils.isNotEmpty(sdfList)) {
            const sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
            let waterElectric = ObjectUtils.isNotEmpty(sdfData) ? sdfData[unitProject.sequenceNbr] : null;
            if (ObjectUtils.isNotEmpty(waterElectric)) {
                let projectMajorMap = new Map();
                let waterElectricData = waterElectric.waterElectricData;
                if (ObjectUtils.isNotEmpty(sdfList)) {
                    for (let i = 0; i < sdfList.length; i++) {
                        let dataExcelElement = sdfList[i];
                        if (ObjectUtils.isNotEmpty(projectMajorMap.get(dataExcelElement.专业名称))) {
                            let newVar = projectMajorMap.get(dataExcelElement.专业名称);
                            projectMajorMap.set(dataExcelElement.专业名称, newVar+1);
                        } else {
                            projectMajorMap.set(dataExcelElement.专业名称, 0);
                        }

                        let dataProjectList = waterElectricData.filter(o => o.projectMajor === dataExcelElement.专业名称 && ObjectUtils.isNotEmpty(o.parentId));
                        let newVar1 = projectMajorMap.get(dataExcelElement.专业名称);
                        let dataProjectElement = dataProjectList[newVar1];

                        dataProjectElement.waterRate = dataExcelElement.水费;
                        dataProjectElement.electricRate = dataExcelElement.电费;
                        dataProjectElement.totalRate = dataExcelElement.水电费;
                        dataProjectElement.waterCost = dataExcelElement.水费1;
                        dataProjectElement.electricCost = dataExcelElement.电费1;
                        dataProjectElement.totalCost = dataExcelElement.水电费1;
                        if (newVar1 === 0 && dataExcelElement.专业名称 === "建筑工程") {
                            dataProjectElement.selectOptionFlag = 1;
                            waterElectric.waterElectricCost = dataExcelElement.水电费1;
                            waterElectric.totalWaterCost = dataExcelElement.水费1;
                            waterElectric.totalElectricCost = dataExcelElement.水电费1;
                        }
                    }
                }
            }
        }
    }

    async addDataSdfDlsz(sdfDlszList, constructId, unitProject) {
        if (ObjectUtils.isNotEmpty(sdfDlszList) && ObjectUtils.isNotEmpty(sdfDlszList[0]) && ObjectUtils.isNotEmpty(sdfDlszList[0].水电费)) {
            const sdfData = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA);
            let waterElectric = ObjectUtils.isNotEmpty(sdfData) ? sdfData[unitProject.sequenceNbr] : null;
            if (ObjectUtils.isNotEmpty(waterElectric)) {
                waterElectric.customWaterElectricFlag = true;
                waterElectric.customWaterElectric = sdfDlszList[0].水电费;
            }
        }
    }

    async addDataFxgcrcjhzbSt(dlfList, constructId, unitProject) {
        if (ObjectUtils.isEmpty(dlfList) || dlfList.length <= 1) {
            return;
        }

        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        let unitId = unitProject.sequenceNbr;
        let rootDe = deDomain.getRoot(unitProject.sequenceNbr);
        let fbParentNode = rootDe;//按顺序，最近的一个就是父级
        let deParentNode = rootDe;//定额也可能直接挂在root下
        let rcjParentNode = null; //最新插入的定额就是人材机的

        let allDeNode = []
        for (let deItem of dlfList) {
            let deModel = this.convertItemToDe(constructId, unitProject.sequenceNbr, deItem);
            if (ObjectUtils.isEmpty(deModel.sortNo) && ObjectUtils.isEmpty(deModel.unit)) {
                // 分部项 - 分部分项数据
                deParentNode = await this._addFBData(deModel, constructId, unitProject, fbParentNode, deDomain);
                fbParentNode = deParentNode;
            } else if (ObjectUtils.isNotEmpty(deModel.sortNo) && ObjectUtils.isNotEmpty(deModel.deCode)) {
                // 定额项
                rcjParentNode = await this._addDeData(deModel, constructId, unitProject, deParentNode,deDomain);
                fbParentNode = rootDe; // 遇到定额，下一个分部直接挂在主节点之下
                allDeNode.push(rcjParentNode);
            } else if (ObjectUtils.isEmpty(deModel.sortNo) && ObjectUtils.isNotEmpty(deModel.deCode)) {
                if(rcjParentNode.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
                    rcjParentNode.type = DeTypeConstants.DE_TYPE_USER_DE;
                    rcjParentNode.deResourceKind = null;
                }
                // 人材机项
                let rcj = await this._addRcjData(deItem, constructId, unitProject, rcjParentNode);
                // 更新initChildCodes,
                if(ObjectUtils.isNotEmpty(rcj) && ObjectUtils.isNotEmpty(rcjParentNode.initChildCodes)){   
                    for(let initCode of rcjParentNode.initChildCodes){
                        if(initCode.code == deDomain._fixCode(rcj.materialCode)){
                            initCode.sequenceNbr = rcj.sequenceNbr;
                        }
                    }
                }
            }
        }

        //调用notify

        let ctx = deDomain.ctx;
        for(let deRow of allDeNode){
            //人材机 重新计算...                
            let rc = ResourceCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId:deRow.sequenceNbr}, ctx);
            await rc.analyze();

            let dc = DeCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId:deRow.sequenceNbr}, ctx);
            await dc.analyze();
            //同步取费文件
            try {
                // 同步取费文件
                await this.service.gongLiaoJiProject.gljBaseFreeRateService.addFreeFileByDe(deRow,constructId,unitId);
            
            } catch (error) {
                console.error("捕获到异常:", error);
            }
        }
       
        let fc = FBCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId: rootDe.sequenceNbr},ctx);
        await fc.analyze();
        
    }
    async addDataFxgcrcjhzbCs(dlfList, constructId, unitProject) {
        if (ObjectUtils.isEmpty(dlfList) || dlfList.length <= 1) {
            return;
        }
        //基本信息
        let deDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        let unitId = unitProject.sequenceNbr;
        let rootDe = deDomain.getRoot(unitProject.sequenceNbr);
        let fbParentNode = rootDe;//按顺序，最近的一个就是父级
        let qdParentNode = rootDe;
        let deParentNode = null;//定额也可能直接挂在root下
        let rcjParentNode = null; //最新插入的定额就是人材机的
        let initDes = deDomain.getDes(item=>item.unitId === unitId);

        let allDeNode = [];
        let allQdNode = [];
        for (let csItem of dlfList) {
            let deModel = this.convertItemToDe(constructId, unitProject.sequenceNbr, csItem);
            if (ObjectUtils.isEmpty(deModel.sortNo) && ObjectUtils.isEmpty(deModel.quantity)) {
                if(deModel.deName == '措施项目' && ObjectUtils.isEmpty(deModel.price)){
                    continue;
                }                
                // 分部分项， 项，此时为03定额
                if(ObjectUtils.isNotEmpty(deModel.price) ){
                    let  existedCsxmQd = initDes.find(item=>item.deName == deModel.deName && item.type == DeTypeConstants.DE_TYPE_DELIST);
                    if(ObjectUtils.isNotEmpty(existedCsxmQd)){
                        deParentNode = existedCsxmQd;
                        existedCsxmQd.deCode = deModel.deCode;
                    }else{
                        if(deModel.deName.indexOf('安全文明施工费') > -1){
                           let  awfDe = initDes.find(item=>item.awfType === 2 && item.type == DeTypeConstants.DE_TYPE_DELIST);
                            if(ObjectUtils.isNotEmpty(awfDe)){
                                deParentNode = awfDe;
                                awfDe.deCode = deModel.deCode;
                                continue;
                            }
                        }
                        deParentNode = await this._addQDData(deModel, constructId, unitProject, qdParentNode,deDomain);
                    }
                    fbParentNode = rootDe; // 遇到定额，下一个分部直接挂在主节点之下
                    allQdNode.push(deParentNode);
                }else{
                    let  existedCsxmDe = initDes.find(item=>item.deName == deModel.deName && [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(item.type))
                    if(ObjectUtils.isNotEmpty(existedCsxmDe)){
                        qdParentNode = existedCsxmDe;
                        existedCsxmDe.deCode = deModel.deCode;
                    }else{
                        // 分部项 - 分部分项数据
                        qdParentNode = await this._addFBData(deModel, constructId, unitProject, fbParentNode, deDomain);
                    }
                    fbParentNode = qdParentNode;
                }
            } else if (ObjectUtils.isNotEmpty(deModel.sortNo) && ObjectUtils.isNotEmpty(deModel.deCode)) {
                // 定额项  如果是安全文明施工方，不需要添加此定额
                if(ObjectUtils.isNotEmpty(deParentNode.awfType) && deParentNode.awfType === 2){
                    continue;
                }
                rcjParentNode = await this._addDeData(deModel, constructId, unitProject, deParentNode,deDomain);
                fbParentNode = rootDe; // 遇到定额，下一个分部直接挂在主节点之下
                allDeNode.push(rcjParentNode);
            } else if (ObjectUtils.isEmpty(deModel.sortNo) && ObjectUtils.isNotEmpty(deModel.deCode)) {
                if(rcjParentNode.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
                    rcjParentNode.type = DeTypeConstants.DE_TYPE_USER_DE;
                    rcjParentNode.deResourceKind = null;
                }
                // 人材机项 需要补充
               let rcj = await this._addRcjData(csItem, constructId, unitProject, rcjParentNode);
                // 更新initChildCodes,
                if(ObjectUtils.isNotEmpty(rcj) && ObjectUtils.isNotEmpty(rcjParentNode.initChildCodes)){   
                    for(let initCode of rcjParentNode.initChildCodes){
                        if(initCode.code == deDomain._fixCode(rcj.materialCode)){
                            initCode.sequenceNbr = rcj.sequenceNbr;
                        }
                    }
                }
            }
        }

        //调用notify

        let ctx = deDomain.ctx;
        for(let deRow of allDeNode){
            //人材机 重新计算...                
            let rc = ResourceCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId:deRow.sequenceNbr}, ctx);
            await rc.analyze();

            let dc = DeCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId:deRow.sequenceNbr}, ctx);
            await dc.analyze();
            //同步取费文件
            try {
                // 同步取费文件
                await this.service.gongLiaoJiProject.gljBaseFreeRateService.addFreeFileByDe(deRow,constructId,unitId);
            
            } catch (error) {
                console.error("捕获到异常:", error);
            }
        }

        for(let qdRow of allQdNode){
            let qc = QDCalculator.getInstance({constructId: constructId, unitId: unitId,deRowId:qdRow.sequenceNbr}, ctx);
            await qc.analyze();
        }
       
        let fc = FBCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId: rootDe.sequenceNbr},ctx);
        await fc.analyze();
    }

    async addDataDwgcgcljssSt(dlfList, constructId, unitProject) {
        if(ObjectUtils.isEmpty(dlfList)){
            return;
        }
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        let functionDataMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let unitId = unitProject.sequenceNbr;
        let allDes = deDomain.getDes(item=>item.unitId == unitId);
        this._updateDeQuantity(constructId,unitId,dlfList,allDes,functionDataMap);
    }

    async addDataDwgcgcljssCs(dlfList, constructId, unitProject) {
        if(ObjectUtils.isEmpty(dlfList)){
            return;
        }
        let deDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        let functionDataMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let unitId = unitProject.sequenceNbr;
        let allDes = deDomain.getDes(item=>item.unitId == unitId);
        this._updateDeQuantity(constructId,unitId,dlfList,allDes,functionDataMap);
    }

    _updateDeQuantity(constructId,unitId,dlfList,allDes,functionDataMap){

        let findDeIds = [];
        let quantitiesMap = functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap?.get(unitId)

        for(let deItem of dlfList){
            let deModel = this.convertItemToDe(constructId,unitId,deItem);
            let cnType = this.extractChineseCharacters(deModel.deCode);
            let realCode = this._getRealCode(deModel.deCode,cnType);
            let tempFindDe = null;
            for(let de of allDes){
                if(de.deName == deModel.deName){
                    //不相等不行啊
                    if((ObjectUtil.isEmpty(de.deCode) && ObjectUtil.isEmpty(realCode))
                    ||de.deCode === realCode){
                        if(!findDeIds.includes(de.sequenceNbr)){
                            tempFindDe = de;
                            findDeIds.push(de.sequenceNbr);
                            break;
                        }else{
                            continue;
                        }
                    }
                }
            }
            if(ObjectUtils.isNotEmpty(tempFindDe)&&ObjectUtils.isNotEmpty(deModel.quantityExpression)){
                let quantityExpression = deModel.quantityExpression;
                if(quantityExpression.indexOf(ConstantUtil.GCLMXHJ)>-1){
                    let mathFormula = quantityExpression;
                    quantityExpression = ConstantUtil.GCLMXHJ;
                    let quotaLine = unitQuantiesMap.get(tempFindDe.sequenceNbr);
                    let pointLine = quotaLine.quantities[1];
                    pointLine.mathFormula = mathFormula.split('=')[1];
                    this.updateQuantityDetail(constructId,unitId,tempFindDe.sequenceNbr,pointLine);
                }
                tempFindDe.quantityExpression = tempFindDe.originalQuantity = quantityExpression;
            }
        }
    }

    async updateQuantityDetail(constructId,unitId, deId, quotaLine){
        quotaLine.value
        let param = {
            constructId:constructId,
            singleId:null,
            unitId:unitId,
            type:1,
            quotaListId:deId,
            pointLine:quotaLine
        }
        await this.service.gongLiaoJiProject.gljQuantitiesService.updateQuantityData(param);
    }

    convertItemToDe(constructId,unitId,item){
        let newModel = new StandardDeModel(constructId,unitId,Snowflake.nextId(),null,DeTypeConstants.DE_TYPE_EMPTY);
        newModel.sortNo = item.序号;
        newModel.deCode = item.定额编号;
        newModel.deName = item.子目名称;
        newModel.unit = item.单位;

        if(ObjectUtils.isNotEmpty(item.单价) 
            && (ObjectUtils.isNumber(item.单价)||ObjectUtils.isNumberStr(item.单价) )){
            newModel.price = eval(item.单价);
        }else{
            newModel.price =  null;
        }
        if(ObjectUtils.isNotEmpty(item.工程量) 
            && (ObjectUtils.isNumber(item.工程量)||ObjectUtils.isNumberStr(item.工程量) )){
            newModel.quantity = eval(item.工程量);
        }else{
            newModel.quantity = null;
        }
        if(ObjectUtils.isNotEmpty(item.工程量表达式)){
            newModel.quantityExpression = item.工程量表达式;
        }
        if(ObjectUtils.isNotEmpty(item.合价)){
            newModel.totalPrice = eval(item.合价) || 0;
        }
        return newModel;
    }


    async _addQDData(deModel,constructId, unitProject, parentNode, deDomain){
        deModel.type = DeTypeConstants.DE_TYPE_DELIST;
        deModel.parentId = parentNode.sequenceNbr;
        deModel = await deDomain.createDeRow(deModel,null,true);
        return deModel;
    }
    // 添加分部分项数据
    async _addFBData(deModel, constructId, unitProject, parentNode,deDomain) {
        let type = parentNode.type === DeTypeConstants.DE_TYPE_DEFAULT?DeTypeConstants.DE_TYPE_FB:DeTypeConstants.DE_TYPE_ZFB;
        deModel.type = type;
        deModel.parentId = parentNode.sequenceNbr;
        deModel = await deDomain.createDeRow(deModel,null,true);
        return deModel;
    }

    _getRealCode(deCode,cnType){

        if(cnType === '借' || cnType === '调'){
            let realCode = deCode.replace(cnType, '').trim();
            return realCode;
        }else{
            return deCode;
        }
    }

    // 添加定额数据
    async _addDeData(deModel, constructId, unitProject, parentNode,deDomain) {
        //基础信息汇总
        let deCode = deModel.deCode;
        let cnType = this.extractChineseCharacters(deCode);
        let realDeCode = this._getRealCode(deCode,cnType);
        let unitId = unitProject.sequenceNbr;
        let constructMajorType = unitProject.constructMajorType;
        let correctMatch = false;
        //补充数据
        let type = DeTypeConstants.DE_TYPE_DE;
        deModel.type = type;
        deModel.parentId = parentNode.sequenceNbr;

        // 查找对应的定额基础数据
        let deItems = await this.service.gongLiaoJiProject.gljBaseDeService.getDeAndRcjByDeCode(constructId, unitId, realDeCode);
        if (ObjectUtils.isNotEmpty(deItems)) {
            let originDeItem = deItems[0];
            for(let deItem of deItems){
                if(cnType === '借'){
                    if(deModel.deName.indexOf(deItem.deName) !== -1){
                        originDeItem = deItem;
                        break;
                    }
                }else{
                    if(deItem.libraryCode === constructMajorType){
                        originDeItem = deItem;
                        break;
                    }
                }
            }            
            PropertyUtil.copyProperties(originDeItem, deModel, [...BaseDomain.baseDeToDeAvoidProperty,'quantity','deName']);
            deModel.standardId = originDeItem.sequenceNbr;
            deModel.remark = originDeItem.libraryName;//费用专业不同，备注啊
            //处理classiflevel1  专业   字段
            let classlevel01 = deModel.classlevel01?deModel.classlevel01.replace('工程','').replace('项目',''):'';
            deModel.classiflevel1 = deDomain._removeChapterPrefix(classlevel01) +"-"+deDomain._removeChapterPrefix(deModel.classlevel02);
            //处理initChildCodes;
            let initChildCodes = [];
            for(let rcj of originDeItem.rcjList){
                initChildCodes.push({code:rcj.materialCode,sequenceNbr:null,resQty:rcj.initResQty});
            }
            deModel.initChildCodes = initChildCodes;
            //施工组织类别
            let cslbItems = await this.service.gongLiaoJiProject.gljBaseCslbService.getByCode(originDeItem.cslbCode);
            if(ObjectUtils.isNotEmpty(cslbItems)){
                deModel.measureType = cslbItems[0].cslbName;
            }
            correctMatch = true;
        }else{
            let rcjItems = await this.service.gongLiaoJiProject.gljBaseRcjService.getRcjByCode(constructId, unitId, deCode);
            if(ObjectUtils.isNotEmpty(rcjItems)){
                let originRcjItem = rcjItems[0];
                for(let rcjItem of rcjItems){
                    if(rcjItem.libraryCode === constructMajorType){
                        originRcjItem == rcjItem;
                    }
                }
                PropertyUtil.copyProperties(originRcjItem, deModel, [...BaseDomain.avoidProperty,"sequenceNbr"]);
                deModel.standardId = originRcjItem.sequenceNbr;
                deModel.deResourceKind = originRcjItem.kind;
                deModel.isDeResource = CommonConstants.COMMON_YES;
                deModel.type = DeTypeConstants.DE_TYPE_RESOURCE;
                correctMatch = true;
            }
        }
        //未匹配，可以判断为补充定额
        if(!correctMatch){
            deModel.type = DeTypeConstants.DE_TYPE_USER_RESOURCE;
            deModel.deResourceKind = 2; //默认材料费
            if(deModel.unit === '工日'){

                deModel.deResourceKind = 1;
            }
            if(['台班','台次'].includes(deModel.unit)){
                deModel.deResourceKind =3;
            }
            //默认的施工类别
            let baseFeeFileRelation = await this.service.gongLiaoJiProject.baseFeeFileService.getFeeFileRelationByQfCode(unitProject.qfMajorType);
            if(ObjectUtils.isNotEmpty(baseFeeFileRelation)){
                deModel.measureType = baseFeeFileRelation.cslbName;
            }
        }        
        deModel = await deDomain.createDeRow(deModel,null,true);
        return deModel;
    }

    // 添加人材机数据
    async _addRcjData(item, constructId, unitProject, rcjParentNode) {
        //TODO rjcParentNode 是定额数据
        let  rcj = null ;
        let rcjCode = item.定额编号;
        if (rcjCode.includes("#")) {
            rcjCode = rcjCode.split("#")[0];
        }
        let baseRcjDao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseRcj2022);
        let findOne = await baseRcjDao.findOne({where: {libraryCode: unitProject.libraryCode, materialCode: rcjCode}});

        //处理定额initChildCodes
        let exitDe = await this._addRcjCheckDe(constructId, rcjParentNode.sequenceNbr);
        if(ObjectUtils.isEmpty(exitDe)){
            return ;
        }

        if (ObjectUtils.isNotEmpty(findOne)) {
            //添加标准人材机（当前单位专业）
            findOne.materialName = item.子目名称;
            findOne.unit = item.单位;
            rcj = await this._addRcj(constructId, unitProject, rcjParentNode, item, findOne);
        } else {
            let baseRcjDao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseRcj2022);
            let findList = [];
            if (!item.定额编号.includes("#")) {
                findList = await baseRcjDao.find({where: {materialCode: rcjCode, materialName: item.子目名称}});
            } else {
                findList = await baseRcjDao.find({where: {materialCode: rcjCode}});
            }

            if (ObjectUtils.isNotEmpty(findList)) {
                let element = findList[0];
                element.materialName = item.子目名称;
                element.unit = item.单位;
                rcj =  await this._addRcj(constructId, unitProject, rcjParentNode, item, element);
            } else {
                //补充人材机
                let detail = {};
                detail.materialCode = item.定额编号;
                detail.materialName = item.子目名称;
                detail.specification = null;
                detail.unit = item.单位;
                detail.resQty = item.消耗量;
                detail.dePrice = item.单价;
                detail.marketPrice = item.单价;
                detail.marketTaxPrice = item.单价;
                detail.kind = RcjTypeEnum.TYPE2.code;

                if (item.定额编号.includes("补充人工")) {
                    detail.kind = RcjTypeEnum.TYPE1.code;
                } else if (item.定额编号.includes("补充材料")) {
                    detail.kind = RcjTypeEnum.TYPE2.code;
                } else if (item.定额编号.includes("补充机械")) {
                    detail.kind = RcjTypeEnum.TYPE3.code;
                } else if (item.定额编号.includes("补充设备")) {
                    detail.kind = RcjTypeEnum.TYPE4.code;
                } else if (item.定额编号.includes("补充主材")) {
                    detail.kind = RcjTypeEnum.TYPE5.code;
                } else if (item.定额编号.includes("补充商砼")) {
                    detail.kind = RcjTypeEnum.TYPE6.code;
                } else if (item.定额编号.includes("补充砼")) {
                    detail.kind = RcjTypeEnum.TYPE7.code;
                } else if (item.定额编号.includes("补充商浆")) {
                    detail.kind = RcjTypeEnum.TYPE9.code;
                } else if (item.定额编号.includes("补充浆")) {
                    detail.kind = RcjTypeEnum.TYPE8.code;
                } else if (item.定额编号.includes("补充配比")) {
                    detail.kind = RcjTypeEnum.TYPE10.code;
                }
                let singleId = constructId === unitProject.parentId ? null : unitProject.parentId;
               rcj = await this.service.gongLiaoJiProject.gljRcjService.supplementRcjData(rcjParentNode.sequenceNbr, constructId, singleId, unitProject.sequenceNbr, rcjParentNode.sequenceNbr, detail, rcjParentNode.sequenceNbr, null, null)
            }
        }
        return rcj;
    }


    async _addRcjCheckDe(constructId, deId) {
        let de = ProjectDomain.getDomain(constructId).deDomain.getDeById(deId);
        if (ObjectUtils.isEmpty(de)) {
            de = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(deId);
            if (ObjectUtils.isNotEmpty(de)) {
                if (undefined === de.initChildCodes || null === de.initChildCodes) {
                    de.initChildCodes = [];
                    ProjectDomain.getDomain(constructId).csxmDomain.updateDe(de);
                }
                return deId;
            } else {
                return null;
            }
        } else {
            if (undefined === de.initChildCodes || null === de.initChildCodes) {
                de.initChildCodes = [];
                ProjectDomain.getDomain(constructId).deDomain.updateDe(de);
            }
            return deId;
        }
    }

    async _addRcj(constructId, unitProject, rcjParentNode, item, rcjModel) {
        let addRcj = await this.service.gongLiaoJiProject.gljRcjService.addRcjData(rcjParentNode.sequenceNbr, rcjModel, constructId, null, unitProject.sequenceNbr, rcjParentNode.sequenceNbr, rcjModel.sequenceNbr, {});
        addRcj.originalQty = ObjectUtils.isEmpty(item.消耗量) ? 0 :  Number(eval(item.消耗量));
        let args = {};
        args.constructId = constructId;
        args.singleId = constructId === unitProject.parentId ? null : unitProject.parentId;
        args.unitId = unitProject.sequenceNbr;
        args.deId = rcjParentNode.sequenceNbr;
        args.rcjDetailId = addRcj.sequenceNbr;
        let constructRcj = {};
        constructRcj.resQty = "1*" + item.消耗量;
        constructRcj.unit = item.单位;
        constructRcj.materialName = item.子目名称;
        // constructRcj.totalNumber = item.工程量;

        let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod === TaxCalculationMethodEnum.GENERAL.code) {
            constructRcj.marketPrice = item.单价;
        } else {
            constructRcj.marketTaxPrice = item.单价;
        }

        args.constructRcj = constructRcj;
        await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail(args);
        return addRcj;
    }


    // 提取字符串中的汉字
    extractChineseCharacters(str) {
        if (ObjectUtils.isEmpty(str)) {
            return '';
        }
        // 匹配汉字的正则表达式
        let chineseRegex = /[\u4e00-\u9fa5]/g;
        let matches = str.match(chineseRegex);
        return matches ? matches.join('') : '';
    }

}

GljProjectService.toString = () => '[class GljProjectService]';
module.exports = GljProjectService;
