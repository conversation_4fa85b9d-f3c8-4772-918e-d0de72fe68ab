const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {FreeRateProjectModel} = require("../models/FreeRateProjectModel");
const {FreeRateModel} = require("../models/FreeRateModel");
const {Service} = require('../../../core');
const ProjectDomain = require("../domains/ProjectDomain");

class GsInitProjectService extends Service {

    constructor(ctx) {
        super(ctx);

    }

    /**
     * 工程项目
     */
    async init(projectModel, constructId) {
        this.projectModel = projectModel;
        this.constructId = constructId;
        //初始化项目基本信息
        await this.initProjectBasicInfo();

        await this.initProjectFreeRates();

        // 初始化工程量明细
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QUANTITIES, new Map());
        // 初始化标准换算
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.UNIT_CONVERSION, {});
    }

    async initProjectBasicInfo(){
       let list = await this.service.PreliminaryEstimate.gsOverviewService.initDataProject(this.projectModel);
       ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY)
        .set(this.service.PreliminaryEstimate.gsOverviewService.getDataMapKey(null,FunctionTypeConstants.JBXX_KEY_TYPE_11),list);
    }
    async initProjectFreeRates() {
        let freeRateProjectModel = new FreeRateProjectModel()
        freeRateProjectModel.childFreeRate = new Map();
        freeRateProjectModel.constructId = this.constructId;
        freeRateProjectModel.projectType = FreeRateModel.DEFAULT_PROJECT_TYPE;
        freeRateProjectModel.payTaxesAreas = FreeRateModel.DEFAULT_PAY_TAXES_AREAS;
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, new Map());
    }

    /**
     * 移除工程项目
     * @param pid
     * @param constructId
     */
    async remove(pid, constructId){


    }

}
GsInitProjectService.toString = () => '[class GsInitProjectService]';
module.exports = GsInitProjectService;
