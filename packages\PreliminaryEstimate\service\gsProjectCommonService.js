const {Service} = require('../../../core');
const {ResponseData} = require('../utils/ResponseData');
const {ObjectUtils} = require('../utils/ObjectUtils');
const ProjectDomain = require("../domains/ProjectDomain");
const WildcardMap = require("../core/container/WildcardMap");
const DeTypeConstants = require("../constants/DeTypeConstants");
const {GsConstructProjectRcj} = require("../models/GsConstructProjectRcj");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {GsItemBillProject} = require("../models/GsItemBillProject");
const RcjCommonConstants = require("../constants/RcjCommonConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {FreeViewModel} = require("../models/FreeViewModel");
const {FreeViewItemModel} = require("../models/FreeViewItemModel");
const {GsConversionModel} = require("../models/GsConversionModel");
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
class GsProjectCommonService extends Service {

    constructor(ctx) {
        super(ctx);
    }


    async getFBTree(args) {
        let {constructId,unitId} = args;
        return  ProjectDomain.getDomain(constructId).getDeDomain().getFbTreeDepth(constructId,unitId,
            null,[DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
    }
    /**
     * 获取定额行
     * @param constructId
     * @param unitId
     * @param deId
     */
    async findDeTreeByDeId(constructId, unitId, deId){
        return ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.sequenceNbr === deId);
    }
    /**
     * 获取定额行
     * @param constructId
     * @param unitId
     * @param deId
     */
    async findDeByDeId(constructId, unitId, deId){
        return ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.unitId === unitId && item.sequenceNbr === deId);
    }
    /**
     * 根据标准定额id获取定额行
     * @param constructId
     * @param unitId
     * @param standardDeId
     */
    async findDeByStandardDeId(constructId, unitId, standardDeId){
        return ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.standardDeId === standardDeId);
    }
    /**
     * 根据标准定额id获取定额行
     * @param constructId
     * @param unitId
     * @param deId
     * @param standardDeId
     */
    async findDesByDeIdAndStandardDeId(constructId, unitId, deId, standardDeId){
        return ProjectDomain.getDomain(constructId).getDeDomain().getDes(item => item.unitId === unitId && item.parentId === deId && item.standardDeId === standardDeId);
    }

    /**
     * 根据标准定额id获取定额行
     * @param constructId
     * @param unitId
     * @param standardDeId
     */
    async findDeTreeByStandardDeId(constructId, unitId, standardDeId){
        return await ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.standardDeId === standardDeId);
    }

    /**
     * 通过定额行ID 获取 下面所有对应的定额id
     * @param constructId
     * @param unitId
     * @param deId
     */
    findSubDeByDeId(constructId, unitId, deId, uniteRules){
        let childs = [];
        let deRowModel = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.unitId === unitId && item.sequenceNbr === deId);
        ProjectDomain.getDomain(constructId).getDeDomain().findDeRows(deRowModel, childs);

        //装饰定额不参与标准换算,但是参与换算信息
        let childsNew = [];
        if (ObjectUtils.isNotEmpty(childs)) {
            if (ObjectUtils.isEmpty(uniteRules)) {
                childs.forEach(p => {
                    let deRow = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(p);
                    if (!ZSFeeConstants.ZS_DE_LIST.includes(deRow.deCode)) {
                        childsNew.push(p);
                    }
                });
            } else {
                childsNew = childs;
            }
        }

        return childsNew;
    }


    /**
     * 获取用户定额
     * @param constructId
     * @param unitId
     */
    async findUserDe(constructId, unitId){
        return ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId
            && item.type === DeTypeConstants.DE_TYPE_USER_DE);
    }

    /**
     * 获取补充定额
     * @param libraryCode
     */
    async findDeRelation(libraryCode){
        let result = await this.service.PreliminaryEstimate.gsBaseDeRelationService.getByLibraryCode(libraryCode);
        return result;
    }

    /**
     * 获取单位工程
     * @param constructId
     * @param unitId
     */
    async getUnit(constructId, unitId){
        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        unit.constructId = constructId;
        return unit;
    }

    /**
     * 定额人材机 查询 人材机明细中的 相同编码的
     * @param rcj
     */
    getSameMarketPrice(rcj){
        let rcjDeKey = WildcardMap.generateKey(rcj.unitId) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(rcj.constructId).getResourceDomain().getResource(rcjDeKey);
        if(ObjectUtils.isEmpty(rcjList)){
            return null;
        }
        let rcjDetailList = new  Array();
        let constructRcjArray = new Array();
        rcjList = rcjList.filter(item => item.isDeResource !== 1);
        rcjList.forEach(item=>{
            if(item.levelMark  != RcjCommonConstants.LEVELMARK_ZERO  &&  ObjectUtils.isNotEmpty(item.pbs)){
                item.pbs.forEach(item2 => rcjDetailList.push(item2));
            }});
        Array.prototype.push.apply(constructRcjArray, rcjList);
        Array.prototype.push.apply(constructRcjArray, rcjDetailList);

        // 找出所有符合要求 rcj  明细
        for(let i=0  ; i<constructRcjArray.length  ;i++){
            let  item  =constructRcjArray[i]
            let  code =item.materialCode ;
            if (item.materialCode.includes("#")){
                code = item.materialCode.substring(0,item.materialCode.lastIndexOf("#"));
            }
            if (rcj.materialCode === code
                && rcj.materialName === item.materialName
                && rcj.unit === item.unit
            ) {
                return item;
            }
        }
        return null;
    }

    /**
     * 获取工程项目下所有单项工程
     * @param constructId
     */
    async getProjectSingleAll(constructId){
        return ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE);
    }

    /**
     * 获取工程项目下所有单位工程
     * @param constructId
     * @param unitId
     */
    async getProjectUnitAll(constructId){
        return ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
    }

    /**
     * 获取单项工程 下所有单位工程
     * @param constructId
     * @param unitId
     */
    async getSingleUnitAll(constructId, singleId){
        // 获取单项工程
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);
        // 获取该单项下的所有单位
        let unitProjectsByConstruct = new Array();
        PricingGSUtils.getUnitProjectsByCurrentNode(constructProject?.children, unitProjectsByConstruct);
        return unitProjectsByConstruct;
        //return ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.parentId === singleId);
    }

    /**
     * 获取单位工程下所有人材机
     * @returns {Promise<ResponseData>}
     * @param constructId
     * @param unitId
     */
    async getAllRcjByUnit(constructId, unitId) {
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjKey);
        return ResponseData.success(rcjList);
    }

    /**
     * 获取单位工程下所有人材机
     * @returns {Promise<ResponseData>}
     * @param constructId
     * @param unitId
     */
    async getAllRcjByUnitId(constructId, unitId) {
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        return ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjKey);
    }

    /**
     * 获取定额下 所有人材机
     * @returns {Promise<ResponseData>}
     * @param constructId
     * @param unitId
     * @param deId
     */
    async getAllRcjByDeId(constructId, unitId, deId) {
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let unitRcj = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjKey);
        return unitRcj.filter(item => item.deRowId === deId);
    }

    /**
     * 判断传入的数据在分部分项下还是措施项目下返回该数据
     */
    getModuleData(constructId, unitId,sequenceNbr){
        let unitProject = this.getUnit(constructId, unitId);

        let itemBillProjects = unitProject.itemBillProjects;
        let measureProjectTables = unitProject.measureProjectTables;

        if (!ObjectUtils.isEmpty(itemBillProjects.find(k =>k.sequenceNbr === sequenceNbr))){
            return itemBillProjects;
        }
        if (!ObjectUtils.isEmpty(measureProjectTables.find(k =>k.sequenceNbr === sequenceNbr))){
            return measureProjectTables;
        }
    }

    /**
     * 根据ID获取项目数据并且返回项目数据对象
     * 从内存里面取
     * @param path
     */
    getProjectObjById(id) {
        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(global.constructProject) || ObjectUtils.isEmpty(global.constructProject[id])) {
            return null;
        }
        return global.constructProject[id].proJectData;
    }

    /**
     * 获取单位下 人材机数据
     * @param constructId
     * @param unitId
     * @return {ConstructProjectRcj[]}
     */
    getRcjList(constructId, unitId) {
        let unitProject = this.getUnit(constructId, unitId);
        return unitProject.constructProjectRcjs;
    }

    /**
     * 获取单位下 换算信息
     * @param constructId
     * @param unitId
     * @return {ConversionInfo[]}
     */
    getConversionInfo(constructId, unitId) {
        let unitProject = this.getUnit(constructId, unitId);
        return unitProject.conversionInfoList;
    }

    /**
     * 获取单位工程下所有人材机
     * @returns {Promise<ResponseData>}
     * @param constructId
     * @param unitId
     */
    async getConstructProjectRcjs(constructId, unitId) {
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let rcjs = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjKey);
        return await this.resourceModel2ConstructProjectRcjs(rcjs);
    }

    /**
     * 人材机转换
     * @param rcjs
     */
    async resourceModel2ConstructProjectRcjs(rcjs) {
        let constructProjectRcjs = [];
        for (let rcj of rcjs) {
            constructProjectRcjs.push(ObjectUtils.copyPropAll(rcj, new GsConstructProjectRcj()))
        }
        return constructProjectRcjs;
    }

    /**
     * 获取单位工程下所有定额
     * @param constructId
     * @param unitId
     * @returns {Promise<*>}
     */
    async findDeByUnit(constructId, unitId){
        let resultList = []
        let deList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId);
        for (let item of deList) {
            resultList.push(ObjectUtils.copyPropAll(item, new GsItemBillProject()));
        }
        return resultList;
    }

    /**
     * 获取单位工程下定额行
     * @returns {Promise<ResponseData>}
     * @param constructId
     * @param unitId
     * @param deId
     */
    async findLineOnlyById(constructId, unitId, deId) {
        let line = await this.findDeByDeId(constructId, unitId, deId);
        return await this.de2GsItemGsBillProject(line);
    }

    /**
     * 定额转换
     * @param line
     */
    async de2GsItemGsBillProject(line) {
        let gsItemBillProject = ObjectUtils.copyPropAll(line, new GsItemBillProject());
        if (ObjectUtils.isEmpty(gsItemBillProject.appendType)) {
            gsItemBillProject.appendType = []
        }
        return gsItemBillProject;
    }

    /**
     * 定额转换
     * @param line
     */
    async gsItemGsBillProject2De(line, constructId, unitId, deId) {
        let deLine = await this.findDeByDeId(constructId, unitId, deId);
        let de = ObjectUtils.copyPropAll(line, deLine);
        return de;
    }

    /**
     * 导入项目后表格列设置恢复
     * @returns {Promise<void>}
     */
    async gsTransBglsz(map) {
        map = ObjectUtils.convertObjectToMap(map);
        for (let [key, unitMap] of map) {
            unitMap = ObjectUtils.convertObjectToMap(unitMap);
            map.set(key, unitMap);
        }
        return map;
    }

}

GsProjectCommonService.toString = () => '[class GsProjectCommonService]';
module.exports = GsProjectCommonService;
