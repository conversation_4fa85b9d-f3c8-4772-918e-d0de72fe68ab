const {ResponseData} = require("../utils/ResponseData");
const {Controller} = require("../../../core");

class GsLoadPriceSetController extends Controller {

  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取载价设置初始化数据
   * @param arg
   * @return {Promise<ResponseData>}
   */
  async queryLoadPriceAreaDate(arg){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.queryLoadPriceAreaDate(arg);
    return ResponseData.success(result);
  }

  /**
   * 点击批量载价
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async loadingPrice(args) {
    let result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.loadingPrice(args);
    if (result.length > 0) {
      return ResponseData.success(result);
    }else {  //没有数据  返回fail
      return ResponseData.fail(result);
    }
  }


  /**
   * 单条载价查询
   * @param args
   * @return {Promise<ResponseData>}
   */
  async loadingPriceOne(args) {
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.smartLoadPrice(args);
    return ResponseData.success(result);
  }


  /**
   * 单条载价应用
   * @param args
   * @return {Promise<ResponseData>}
   */
  async loadingPriceOneUse(args) {
    let {constructId, singleId, unitId, sequenceNbr, loadPrice, marketPrice, sourcePrice} = args;
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.loadingPriceOneUse(args);
    return ResponseData.success(result);
  }


  /**
   * 工程项目/单项载价应用（单条载价）
   * @param args
   * @return {Promise<ResponseData>}
   */
  async projectLoadingPriceOneUse(args) {
    let {constructId, singleId, unitId, sequenceNbr, loadPrice, marketPrice, sourcePrice} = args;
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.projectLoadingPriceOneUse(args);
    return ResponseData.success(result);
  }



  /**
   * 返回载价编辑弹窗数据
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async loadPriceEditPage(args) {
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.loadPriceEditPage(args);
    return ResponseData.success(result);
  }

  /**
   * 双击价格选择弹窗的数据行 更新待载价格
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async updateLoadPrice(args) {
    await this.service.PreliminaryEstimate.gsLoadPriceSetService.updateLoadPrice(args);
    return ResponseData.success(true);
  }

  /**
   * 批量应用接口更新市场价、价格来源
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async applyLoadingPriceInRcjDetails(args) {
    await this.service.PreliminaryEstimate.gsLoadPriceSetService.applyLoadingPriceInRcjDetails(args);
    return ResponseData.success(true);
  }

  /**
   * 查询载价报告明细列表
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async loadPriceList(args){
    let list =  await this.service.PreliminaryEstimate.gsLoadPriceSetService.loadPriceList(args);
    return ResponseData.success(list);
  }

  /**
   * 查询载价报告 -扇形图
   * @returns {Promise<ResponseData>}
   */
  async queryLoadPriceReportRcj(args){
    let result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.queryLoadPriceReportRcj(args);
    return ResponseData.success(result);
  }

  /**
   * 查询载价报告 -柱状图
   * @returns {Promise<ResponseData>}
   */
  async queryLoadPriceReportTarget(args){
    let result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.queryLoadPriceReportTarget(args);
    return ResponseData.success(result);
  }


  /**
   * 查询载价状态
   * @param args
   * @return {Promise<ResponseData>}
   */
  async loadPriceStatus(args){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.loadPriceStatus(args);
    return ResponseData.success(result);

  }

  /**
   * 鼠标右键清除载价
   * @param args
   * @return {Promise<ResponseData>}
   */
  async clearLoadPriceUse(args){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.clearLoadPriceUse(args);
    return ResponseData.success(result);
  }





















  /**
   * 取消勾选及类型 更新载入前后的人材机总价 及执行载价的条数
   * @param args
   * @returns {Promise<void>}
   */
  async cancelCheckOrType(args) {
    await this.service.PreliminaryEstimate.gsLoadPriceSetService.cancelCheckOrType(args);
    return ResponseData.success(true);
  }





  /**
   * 智能询价
   * @param args
   * @return {Promise<ResponseData>}
   */
  async smartLoadPrice(args){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.smartLoadPrice(args);
    return ResponseData.success(result);
  }


  /**
   * 智能询价后应用
   * @param args
   * @return {Promise<ResponseData>}
   */
  async smartLoadPriceUse(args){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.smartLoadPriceUse(args);
    return ResponseData.success(result);
  }



  /**
   * 鼠标右键查询人材机关联定额
   * @param args
   * @return {Promise<ResponseData>}
   */
  async getRcjDe(args){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.getRcjDe(args);
    return ResponseData.success(result);
  }


  /**
   * 鼠标右键查询人材机关联定额树结构
   * @param args
   * @return {Promise<ResponseData>}
   */
  async getConstructIdTree(args){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.getConstructIdTree(args);
    return ResponseData.success(result);
  }


  /**
   * 鼠标右键 查询定额是否存在 以及 是分部分项还是措施项目
   * @param args
   * @return {Promise<ResponseData>}
   */
  async existDe(args){
    const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.existDe(args);
    return ResponseData.success(result);
  }

  /**
   * 人材机询价
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async getRcjXj(args) {
    const result = await this.service.PreliminaryEstimate.gsRcjService.getRcjXj(args);
    return ResponseData.success(result);
  }






  /**
     * 获取逐条载价 人材机类型树
     * @param
     * @return {Promise<ResponseData>}
     */
    async getGsRcjTypeTree(){

        const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.getGsRcjTypeTree();

        return ResponseData.success(result);

    }


    /**
     * 逐条载价 查询人材机
     * @returns {Promise<void>}
     */
    async getGsZtzjRcj(args){
        const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.getGsZtzjRcj(args);

        return ResponseData.success(result);
    }


    /**
     * 获取信息价 地区列表 城市下面带着地区
     * @returns {Promise<void>}
     */
    async getGsDimRegion(){
        const result = await this.service.PreliminaryEstimate.gsLoadPriceSetService.getGsDimRegion();

        return ResponseData.success(result);
    }

}


GsLoadPriceSetController.toString = () => '[class GsLoadPriceSetController]';
module.exports = GsLoadPriceSetController;
