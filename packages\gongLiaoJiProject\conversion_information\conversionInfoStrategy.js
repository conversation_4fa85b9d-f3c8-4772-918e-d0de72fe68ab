
const Kind3TypeBRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeBRuleHandler");
const Kind2RuleHandler = require("../standard_conversion/rule_handler/Kind2RuleHandler");
const Kind3RuleHandler = require("../standard_conversion/rule_handler/Kind3RuleHandler");
const Kind3TypeCRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeCRuleHandler");
const Kind3TypeYRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeYRuleHandler");
const Kind3TypeTRuleHandler = require("../standard_conversion/rule_handler/Kind3TypeTRuleHandler");
const Kind4RuleHandler = require("../standard_conversion/rule_handler/Kind4RuleHandler");
const Kind0RuleHandler = require("../standard_conversion/rule_handler/Kind0RuleHandler");
const Kind1RuleHandler = require("../standard_conversion/rule_handler/Kind1RuleHandler");
const ConversionService = require("../standard_conversion/util/ConversionService");
const EE = require("../../../core/ee");
const Kind5RuleHandler = require("./rule_handler/Kind5RuleHandler");
const Kind6RuleHandler = require("./rule_handler/Kind6RuleHandler");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ConversionInfoUtil} = require("../standard_conversion/util/ConversionInfoUtil");
const ProjectDomain = require("../domains/ProjectDomain");
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const WildcardMap = require("../core/container/WildcardMap");
const CommonConstants = require("../constants/CommonConstants");
const {ObjectUtils} = require("../../PreliminaryEstimate/utils/ObjectUtils");

class ConversionInfoStrategy{
    static UNITE_RULE_KIND = "4";

    constructor() {
        this.service = EE.app.service;
        this.app = EE.app;
        this.commonService = this.service.gongLiaoJiProject.gljProjectCommonService;
    }

    async init(constructId, singleId, unitId, deId, cancelLockNumber) {
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.deId = deId;
        this.cancelLockNumber = !!cancelLockNumber;

        this.unitProject = await this.commonService.getUnit(constructId, unitId);

        this.deLine = await this.commonService.findDeByDeId2(constructId, unitId, deId);
        this.isCsxmDe = !!this.deLine.isCsxmDe;
        this.de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        this.deAdjustmentCoefficient = this.deLine.parent?.adjustmentCoefficient || 1;
        this.deOrCsxmDomain = this.isCsxmDe ? ProjectDomain.getDomain(this.constructId).csxmDomain : ProjectDomain.getDomain(this.constructId).deDomain;
        this.deUpDateObj = {
            // 换算信息
            redArray: [],
            blackArray: [],
            nameSuffixArray: [],
            addedDes: [],
            deTypes: []
        }
        this.isSzsswxyhzxzx22 = await this.service.gongLiaoJiProject.gljConversionInfoService.isSzsswxyhzxzx22(this.unitProject, this.de);
        this.rcjCacheMaps = this._createRcjCacheMap();

        this.conversionService = new ConversionService();

        // 精度
        this.precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        this.jd = {
            resQty: this.precision.DETAIL.RCJ.resQty, // 人材机消耗量精度
            totalNumber: this.precision.DETAIL.RCJ.totalNumber, //人材机数量精度
            quantity: this.precision.EDIT.DE.quantity, //定额工程量精度
            deRcjPrice: this.precision.EDIT.DERCJ.price, //定额人材机单价
        }
    }

    async prepare(){
        let params = {
            constructId: this.constructId,
            singleId: this.singleId,
            unitId: this.unitId,
            unitProject: this.unitProject,
            de: this.deLine,
            adjustmentCoefficient: this.deAdjustmentCoefficient,
            ...this.rcjCacheMaps
        };
        this.deInitialRcjs = await this.conversionService.deInitialRcjs(params);
        this._dealTempRemoveAndLockNumber(this.deInitialRcjs);
        await this._delDeAddByRule();
    }

    /**
     * 执行标准换算
     */
    async execute(){
        let conversionInfos = this.de.conversionInfo || [];

        // if(ObjectUtil.isEmpty(conversionInfos)){ //此处取消为空的判断，是因为最后一条换算信息取消后，费用记取可能需要重新执行
        //     return;
        // }
        await this.prepare();

        for(let rule of conversionInfos){
            //标准换算、统一换算规则处理
            if(rule.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE){
                for(let r of rule.children){
                    let handler = this.getRuleHandler(this, r);
                    await handler.execute();
                }
            }else{
                // 人材机修改生成换算信息处理
                let handler = this.getRuleHandler(this, rule);
                await handler.execute();
            }

        }
        await this.after();

        return this.deUpDateObj.addedDes;
    }


    /**
     * 标准换算执行后计算，其他材料/机械、单价构成、......
     */
    async after(){

        this._upDateDeInfo();

        // 调整系数处理
        this._dealDeAdjustmentCoefficient();

        // TODO 将处理后的人材机替换单位工程中对应定额人材机
        // let rcjs =this.constructProjectRcjs.filter(rcj => rcj.deId != this.de.sequenceNbr);
        // rcjs.push(...this.deInitialRcjs);
        // this.constructProjectRcjs = rcjs;
        //
        // for (let item of this.de.conversionInfo) {
        //     item.mathHandlers = []
        // }

        // let rcjs = ProjectDomain.getDomain(this.constructId).resourceDomain.getResource(WildcardMap.generateKey(this.unitId, this.de.sequenceNbr) + WildcardMap.WILDCARD);

        let isConversion = true;// 标准换算的定额类型，需要特殊处理
        if(this.isCsxmDe){
            // 重新计算人材机
            await ProjectDomain.getDomain(this.constructId).csxmDomain.notify(this.deLine);
            DeTypeCheckUtil.checkAllDeType(this.deLine, ProjectDomain.getDomain(this.constructId).csxmDomain.ctx,isConversion);
        }else{
            await ProjectDomain.getDomain(this.constructId).deDomain.notify(this.deLine);
            DeTypeCheckUtil.checkAllDeType(this.deLine, ProjectDomain.getDomain(this.constructId).deDomain.ctx,isConversion);
        }

        // 修改单价调差
        // await this._updateDePrice();

        // 重新计算费用汇总
        try {
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: this.constructId,
                singleId: this.singleId,
                unitId: this.unitId,
                constructMajorType: this.deLine.libraryCode
            });
            // await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
            //     constructId: this.constructId,
            //     singleId: this.singleId,
            //     unitId: this.unitId,
            //     qfMajorType: null
            // });
        } catch (error) {
            console.error("捕获到异常:", error);
        }

        // 处理人材机冻结状态：冻结的人材机不参与标准换算
        // this._dealFreeze();
    }

    _createRcjCacheMap(){
        let tempDeleteFlagMap = new Map();
        let rcjPriceMap = new Map();
        let rcjLockNumMap = new Map();
        let deRcjs = ProjectDomain.getDomain(this.constructId).resourceDomain.getResource(WildcardMap.generateKey(this.unitId, this.de.sequenceNbr) + WildcardMap.WILDCARD);

        // 人材机处理
        for(let r of deRcjs){
            // if(deModel.isTempRemove = CommonConstants.COMMON_YES ){
            //     resource.isTempRemove = CommonConstants.COMMON_YES;
            //     resource.changeResQty = resource.resQty;
            //     resource.resQty = 0;
            // }
            // 临时删除材料处理
            if(r.isTempRemove == CommonConstants.COMMON_YES){
                tempDeleteFlagMap.set(r.materialCode, {
                    ...r
                });
            }

            if(r.isNumLock && !rcjLockNumMap.has(r.materialCode)){
                rcjLockNumMap.set(r.materialCode, {
                    ...r
                });
            }

            rcjPriceMap.set(r.materialCode, {
                ...r
            });

        }

        return {
            tempDeleteFlagMap,
            rcjPriceMap,
            rcjLockNumMap
        }
    }

    _dealTempRemoveAndLockNumber(deInitialRcjs){
        for(let rcj of deInitialRcjs){
            // 锁定数量的处理要放在临时删除标记处理之前
            this.dealLockNumberOneRcj(rcj);
            this.dealTempRemoveOneRcj(rcj);
        }
    }

    dealTempRemoveOneRcj(rcj){
        let {tempDeleteFlagMap} = this.rcjCacheMaps;

        // 处理临时删除标记
        if(this.deLine.isTempRemove == 1 || tempDeleteFlagMap.has(rcj.materialCode)){
            let tempRemoveRcjTmp = tempDeleteFlagMap.get(rcj.materialCode);
            rcj.isTempRemove = tempRemoveRcjTmp.isTempRemove;
            rcj.isFirstTempRemove = tempRemoveRcjTmp.isFirstTempRemove;
            // 消耗量如何处理
            rcj.changeResQty = rcj.isNumLock ? tempRemoveRcjTmp.changeResQty : rcj.resQty;
            rcj.resQty = 0;
        }
    }

    dealLockNumberOneRcj(rcj){
        let {rcjLockNumMap} = this.rcjCacheMaps;
        if(rcjLockNumMap.has(rcj.materialCode)){
            let lockRcjTmp = rcjLockNumMap.get(rcj.materialCode);
            rcj.resQtyConversionLock = lockRcjTmp.resQtyForNumLockAndDeQuantityZero || rcj.resQty;
            rcj.isNumLock = lockRcjTmp.isNumLock;
            rcj.resQty = lockRcjTmp.resQtyForNumLockAndDeQuantityZero || lockRcjTmp.resQty;
            rcj.numLockNum = lockRcjTmp.numLockNum;
            rcj.totalNumber = lockRcjTmp.totalNumber;

            rcjLockNumMap.delete(rcj.materialCode);
        }
    }

    _dealDeAdjustmentCoefficient(){
        if(this.deAdjustmentCoefficient == 1){
            return;
        }

        let rcjs = ProjectDomain.getDomain(this.constructId).resourceDomain.getResource(WildcardMap.generateKey(this.unitId, this.de.sequenceNbr) + WildcardMap.WILDCARD);

        for(let rcj of rcjs){
            if(rcj.isNumLock){
                if(rcj.hasOwnProperty("resQtyConversionLock")){
                    rcj.resQtyConversionLock = rcj.resQtyConversionLock * this.deAdjustmentCoefficient;
                }
            }else{
                let destResQty = (rcj.isTempRemove == CommonConstants.COMMON_YES) ? rcj.changeResQty * this.deAdjustmentCoefficient : rcj.resQty * this.deAdjustmentCoefficient;
                this.conversionService.updateTempRemoveRCJResQty(rcj, destResQty);
            }
        }
    }

    _upDateDeInfo(){
        //换算信息
        this._resetDeName();

        this.de.redArray = this.deUpDateObj.redArray;
        this.de.codeSuffixHistory = this.deUpDateObj.redArray;
        this.de.blackArray = this.deUpDateObj.blackArray;
        this.de.nameSuffixHistory = this.deUpDateObj.nameSuffixArray;

        let nameSuffix = this.deUpDateObj.nameSuffixArray.join(" ");

        this.deLine.deName = `${this.deLine.deName} ${nameSuffix}`;
        this.de.deName = this.deLine.deName;
    }

    _resetDeName() {
        let de = this.de;
        let deLine = this.deLine;
        if (
            Array.isArray(de.nameSuffixHistory) &&
            de.nameSuffixHistory.length > 0
        ) {
            // 恢复名称
            for (let history of de.nameSuffixHistory) {
                if(ObjectUtil.isEmpty(history)){
                    continue;
                }
                history = history.trim();
                deLine.deName = deLine.deName.replace(history, "").trim();
            }
        }
    }

    async _delDeAddByRule(){
        let de = this.de;
        if (ObjectUtil.isEmpty(de.addByRuleDeIds)){
            return;
        }

        // 当恢复换算默认值时，删除上次新增定额
        for ( let deRuleIdObj of de.addByRuleDeIds) {
            let addDeId = deRuleIdObj.deId;
            let deAddByRule = await this.commonService.findDeByDeId2(this.constructId, this.unitId, addDeId);
            if(ObjectUtils.isNotEmpty(deAddByRule)) {
                if (!this.isCsxmDe) {
                    await ProjectDomain.getDomain(this.constructId).deDomain.removeDeRow(addDeId, true);
                }else {
                    await ProjectDomain.getDomain(this.constructId).csxmDomain.removeDeRow(addDeId, true);
                }
            }
        }

        de.addByRuleDeIds = [];
    }

    getRuleHandler(ctx, rule){
        if(rule.kind == "1"){
            return new Kind1RuleHandler(ctx, rule);
        }

        if(rule.kind == "2"){
            return new Kind2RuleHandler(ctx, rule);
        }

        if(rule.kind == "3"){
            if(rule.type == "b") {
                return new Kind3TypeBRuleHandler(ctx, rule);
            }
            if(rule.type == "c") {
                return new Kind3TypeCRuleHandler(ctx, rule);
            }

            if(rule.type == "y"){
                return new Kind3TypeYRuleHandler(ctx, rule);
            }

            if(rule.type == "t"){
                return new Kind3TypeTRuleHandler(ctx, rule);
            }

            return new Kind3RuleHandler(ctx, rule);
        }

        if(rule.kind == "4"){
            return new Kind4RuleHandler(ctx, rule);
        }

        if(rule.kind == "0"){
            return new Kind0RuleHandler(ctx, rule);
        }

        if(rule.kind == "5"){
            return new Kind5RuleHandler(ctx, rule);
        }

        if(rule.kind == "6"){
            return new Kind6RuleHandler(ctx, rule);
        }
    }

    _donorMaterialNumberRefresh(idParams) {
        let rcjs = this.deInitialRcjs.find(rcj => rcj.ifDonorMaterial == 1);
        if(ObjectUtil.isNotEmpty(rcjs)){
            this.service.rcjProcess.donorMaterialNumberRefresh(idParams.constructId, idParams.singleId, idParams.unitId);
        }
    }
}

module.exports = ConversionInfoStrategy;
