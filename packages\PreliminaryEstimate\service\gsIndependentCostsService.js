const {Service} = require("../../../core");

const ProjectDomain = require('../domains/ProjectDomain');
const { ObjectUtils } = require('../utils/ObjectUtils');
const {GsIndependentCosts} = require('../models/GsIndependentCosts');
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../utils/NumberUtil");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const { de } = require("../../../electron/enum/BranchProjectLevelConstant");
const {ResponseData} = require("../utils/ResponseData");


class GsIndependentCostsService extends Service{

    constructor(ctx) {
        super(ctx);
    }
    _reorder(array){
        if(ObjectUtils.isEmpty(array)){
            return;
        }
        array.forEach(item => {
            this.generateIndexes(array,item)
        });        
    }

    // 生成序号的函数  
    generateIndexes(list ,item) {  
        // 过滤出当前层级的元素  
        const currentLevelItems = list.filter(itemList => itemList.parentId === item.sequenceNbr);  

        // 遍历当前层级的元素并生成序号  
        for (let i = 0; i < currentLevelItems.length; i++) {  
            const itemChild = currentLevelItems[i];  
            // 否则，使用默认序号  
            itemChild.dispNo = `${item.dispNo || ''}.${i + 1}`;  
        
            // 递归处理子元素  
            this.generateIndexes(list, itemChild);  
        }  
    } 
    /**
     * 保存列表
     * @param args  
     */
    async save(args) {
        const {constructId,unitId,iCosts,operateType} = args;
        let newEqCosts = new GsIndependentCosts();
        ConvertUtil.setDstBySrc(iCosts, newEqCosts);
        let majorTypeSet = new Set();
        //初始化id
        let functionMap  = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY);
        //小数点精度
        let precision1 = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let precision = precision1.UNIT_DLF;
        let list = functionMap.get(this.getDataMapKey(unitId));
         
        if(operateType === 'insert'){
            let dispNo = newEqCosts.dispNo;
            let indexSequenceNbr = newEqCosts.sequenceNbr;
            newEqCosts.sequenceNbr = Snowflake.nextId();
            newEqCosts.name = ObjectUtils.isEmpty(newEqCosts.name)? null:newEqCosts.name;
            newEqCosts.unit = ObjectUtils.isEmpty(newEqCosts.unit)? '元':newEqCosts.unit;
            newEqCosts.customIndex = null;
            newEqCosts.quantity = null;
            newEqCosts.price = ObjectUtils.isEmpty(newEqCosts.price)? null:newEqCosts.price;
            newEqCosts.totalPrice = 0;
            newEqCosts.remark = null;
            newEqCosts.costMajorCode = null;
            newEqCosts.costMajorName = null;
            if(ObjectUtils.isEmpty(dispNo)){
                list.push(newEqCosts);
            }else{
                let findIndex = list.findIndex(item=>item.sequenceNbr===indexSequenceNbr);
                list.splice(findIndex+1,0,newEqCosts);
            }
            let parentItem = list.find(item=>item.sequenceNbr === newEqCosts.parentId);
            if(parentItem.levelType ===2){
                newEqCosts.costMajorName = parentItem.costMajorName;
                newEqCosts.costMajorCode = parentItem.costMajorCode;
            }
            await this.setCostMajorName(constructId,unitId,newEqCosts);
            majorTypeSet.add(newEqCosts.costMajorCode);
        }
        if(operateType === 'insertChild'){
            newEqCosts.parentId = newEqCosts.sequenceNbr;
            //先保存parentid在 生成新的 id
            newEqCosts.sequenceNbr = Snowflake.nextId();
            newEqCosts.levelType += 1
            let dispNo = newEqCosts.dispNo;
            //获取当前节点下标
            if(ObjectUtils.isEmpty(dispNo)){
                list.push(newEqCosts);
            }else{
                let parentIndex = 0
                let findIndex = 0;
                list.forEach((item,index)=>{
                    if(item.sequenceNbr===newEqCosts.parentId){
                        parentIndex = index;
                    }
                    if(item.parentId === newEqCosts.parentId){
                        findIndex = index;
                    }
                });
                let insertIndex = findIndex>0?findIndex+1:parentIndex+1;
                list.splice(insertIndex,0,newEqCosts);
            }
            newEqCosts.name = null;
            newEqCosts.unit = '元';
            newEqCosts.customIndex = null;
            newEqCosts.quantity = null;
            newEqCosts.price = null;
            newEqCosts.totalPrice = 0;
            newEqCosts.remark = null;
            //父级此此段为空
            let parentItem = list.find(item=>item.sequenceNbr === newEqCosts.parentId);
            if(parentItem.levelType ===2){
                parentItem.price=null;
                parentItem.unit=null;
                parentItem.quantity=null;
                newEqCosts.costMajorName = parentItem.costMajorName;
                newEqCosts.costMajorCode = parentItem.costMajorCode;
            }
            await this.setCostMajorName(constructId,unitId,newEqCosts);
            majorTypeSet.add(newEqCosts.costMajorCode);
        }
        if(operateType === 'edit'){
            let item = list.find(item=>item.sequenceNbr === newEqCosts.sequenceNbr);
            if(ObjectUtils.isEmpty(item)){
                return;
            }
            if(item.dispNo !== newEqCosts.dispNo){
                item.customIndex = newEqCosts.dispNo;
            }
            let oldCostMajorCode = item.costMajorCode;
            item.name = newEqCosts.name;
            item.unit = newEqCosts.unit;
            item.quantity = newEqCosts.quantity;
            item.price = newEqCosts.price;
            item.remark = newEqCosts.remark;
            item.costMajorName = newEqCosts.costMajorName;
            item.costMajorCode = newEqCosts.costMajorCode;
            if(oldCostMajorCode !== newEqCosts.costMajorCode){
                list.forEach(lItem=>{
                    if(lItem.parentId === item.sequenceNbr){
                        lItem.costMajorName = item.costMajorName;
                        lItem.costMajorCode = item.costMajorCode;
                    }
                });
            }
            majorTypeSet.add(oldCostMajorCode);
            majorTypeSet.add(newEqCosts.costMajorCode);
        }

        if(ObjectUtils.isEmpty(newEqCosts.parentId)){
            newEqCosts.parentId = list[0].sequenceNbr
        }
        this._reorder(list);
         
        //计算总的独立费
        this.caculatorTreeTotal(list,null, precision);
        //保存数据
        functionMap.set(this.getDataMapKey(unitId),list);
        //通知费用汇总变动消息
        let untiProject = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === unitId);
        for(let item of majorTypeSet){
            await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: untiProject[0].parentId,
                unitId: unitId,
                constructMajorType:item
            });
        }
    }
    async setCostMajorName(constructId,unitId,newEqCosts){
        if(newEqCosts.levelType === 1 || ObjectUtils.isNotEmpty(newEqCosts.costMajorName)){
            return;
        }
        let unitProject = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === unitId);

        let deLibrary = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(unitProject[0].constructMajorType);
        newEqCosts.costMajorName = deLibrary?deLibrary.projectType:null; 
        newEqCosts.costMajorCode = deLibrary?deLibrary.libraryCode:null; 
    }

    convertNumber(priceStr){
        let price = priceStr;
        if(ObjectUtils.isEmpty(price) || !ObjectUtils.isNumberStr(price)){
            price = 0;
        }
        if(ObjectUtils.isNumberStr(price)){
            price = Number(price);
        }
        return price;
    }

    //计算当前节点的汇总数据
    caculatorTreeTotal(list,item,precision){
        if(item === null){
            item = list[0];
        }
        
        let allCost = 0;
        let newList = list.filter(itemList=>itemList.parentId === item.sequenceNbr)
        if(ObjectUtils.isNotEmpty(newList)){
            newList.forEach(itemList=>{
                    this.caculatorTreeTotal(list,itemList,precision);
                    // allCost = NumberUtil.add(allCost,itemList.totalPrice);
                    allCost = NumberUtil.add(allCost, NumberUtil.numberScale(itemList.totalPrice, precision.totalPrice));
                }
            );
        }else{
            // let price = this.convertNumber(item.price);
            // let quantity = this.convertNumber(item.quantity);
            // allCost =  NumberUtil.multiply(quantity,price);
            item.price = ObjectUtils.isNotEmpty(item.price) ? NumberUtil.numberScale(this.convertNumber(item.price), precision.price) : item.price;
            item.quantity = ObjectUtils.isNotEmpty(item.quantity) ? NumberUtil.numberScale(this.convertNumber(item.quantity), precision.quantity) : item.quantity;
            allCost = NumberUtil.numberScale(NumberUtil.multiply(item.price, item.quantity), precision.totalPrice);
        }
        item.totalPrice = allCost;
        
    }
    /**
     * 删除列表
     * @param args
     */
    async delete(args) {
        
        const {constructId,unitId,sequenceNbr} = args;
        let functionMap  = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY);
        //小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId).UNIT_DLF;
        let list = functionMap.get(this.getDataMapKey(unitId));
        //不能删除第一个
        if(list[0].sequenceNbr === sequenceNbr){
            return;
        }
        //没有删除的对象返回
        let deleteItem = list.find(item=>item.sequenceNbr === sequenceNbr);
        if(ObjectUtils.isEmpty(deleteItem)){
            return;
        }
        let parentItem = list.find(item=>item.sequenceNbr === deleteItem.parentId);
        if(ObjectUtils.isEmpty(parentItem)){
            return;
        }
        let majorTypeSet = new Set();
        //只对二级三级能删除，其他无法使用
        let newList = [];
        for(let i=0;i<list.length;i++){
            let item = list[i];
            if(item.sequenceNbr !== sequenceNbr && item.parentId !== sequenceNbr){
                newList.push(item);
            }else{
                majorTypeSet.add(list[i].costMajorCode);
            }
        }
        //重新排序
        this._reorder(newList);
        //设置数据
        let parentHasChild = newList.find(item=>item.parentId === parentItem.sequenceNbr);
        if(ObjectUtils.isEmpty(parentHasChild) || parentHasChild.length === 0){
            parentItem.unit='元';
        }      
        //计算总的独立费
        this.caculatorTreeTotal(newList,null, precision);
        functionMap.set(this.getDataMapKey(unitId), newList);

        //通知费用汇总变动消息
        let untiProject = ProjectDomain.getDomain(constructId).getProject(item=>item.sequenceNbr === unitId);
        for(let item of majorTypeSet){
            await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: untiProject[0].parentId,
                unitId: unitId,
                constructMajorType:item
            });
        }
    }


    /**
     * 获取信息列表
     * @param args
     */
    async getList(args) {
        const {constructId,unitId} = args;
        //获取数据
        let functionMap  = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY);
        let list = functionMap.get(this.getDataMapKey(unitId));
        if(ObjectUtils.isEmpty(list)){
            list = await this.initData(args);
            functionMap.set(this.getDataMapKey(unitId), list);
        }else{
            // 兼容map对象处理
            if(list[0] instanceof Map){
                for (let index = 0; index < list.length; index++) {
                    list[index]= Object.fromEntries(list[index].entries())
                }
            }
        }
        this.editConfig(list);
        //获取取费专业数据,默认河北石家庄,
        let costMajorList = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getDeLibrariesByDirection('130000','130100');
        return {list:list,costMajorList:costMajorList};
    }
    editConfig(list){
        list.forEach(item=>{
            let childreds = list.filter(pItem=>pItem.parentId === item.sequenceNbr);
            item.editConfig =childreds&&childreds.length>0?false:true;
        });
    }

    async initData(args){

        let list = [];
        let root = new GsIndependentCosts(Snowflake.nextId(),"独立费","元",null,null,0,null,null,null,null,'1',1);
        list.push(root);
        return list;
    }

    /**
     * 11 基本信息 12 编制说明 13 特征
     * @param {*} unitId
     * @returns
     */
    getDataMapKey(unitId){
            
        if(ObjectUtils.isEmpty(unitId)){
            unitId = "0";//保持key风格一致性
        }
        return "INDCOST-" + unitId;
    }

    /**
     * 独立费-查询人材机数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async insertRcj(args){
        const {constructId, unitId, levelType, iCosts, operateType, rcj} = args;
        if (ObjectUtils.isEmpty(iCosts)) {
            return;
        }

        iCosts.name = rcj.materialName
        iCosts.unit = rcj.unit
        iCosts.price = rcj.price
        iCosts.isRcj = true

        let param = {
            constructId,
            unitId,
            levelType,
            iCosts,
            operateType,
        }
        await this.save(param);
    }



    async recalculatePrecisionDlf(constructId, unit, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);

        let precision1 = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(projectDomain.getRoot().sequenceNbr);



        let unitDlfKey = await this.getDataMapKey(unit.sequenceNbr);
        let list = projectDomain.functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY).get(unitDlfKey);
        if (ObjectUtils.isNotEmpty(list)) {
            await this.caculatorTreeTotal(list, null, precision1.UNIT_DLF);
            // for (let item of list) {
            //     //通知费用汇总变动消息
            //     await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
            //         constructId: constructId,
            //         singleId: unit.parentId,
            //         unitId: unit.sequenceNbr,
            //         qfMajorType: item.costMajorCode
            //     });
            // }
        }
    }
}
GsIndependentCostsService.toString = () => '[class GsIndependentCostsService]';
module.exports = GsIndependentCostsService;