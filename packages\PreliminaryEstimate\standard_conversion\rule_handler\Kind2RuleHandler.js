const Kind2MathHandler = require("../math_item_handler/Kind2MathHandler");
const RuleHandler = require("./ruleHandler");

class Kind2RuleHandler extends RuleHandler{
    constructor(strategyCtx, rule) {
        super(strategyCtx, rule);
        // 切换的目标材料是否是默认材料
        this.ifTargetIsDefalut = (this.rule.defaultRcjCode == this.rule.clpb.detailsCode && this.rule.defaultRcjLibraryCode == this.rule.clpb.libraryCode);
    }

    addDeUpdateInfo() {
        if(this.ifTargetIsDefalut){
            return;
        }
        super.addDeUpdateInfo();
    }

    deCodeUpdateInfo() {
        return {
            redStr: `[H${this.rule.defaultRcjCode} ${this.rule.clpb.detailsCode}]`,
            blackStr: null
        };
    }

    addConversionInfo() {
        if(this.ifTargetIsDefalut) {
            return;
        }
        super.addConversionInfo();
    }

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = this.HSCL_TEXT+`${this.rule.ruleInfo}`;
        conversionInfoItem.conversionString = `H${this.rule.defaultRcjCode} ${this.rule.clpb.detailsCode || this.rule.currentRcjCode}`;
    }


    deNameUpdateInfo(rule){
       return this.HSCL_TEXT + `${rule.ruleInfo}`;
    }


    /**
     * 解析规则：规则分类、输入/选择值、公式
     */
    analysisRule(){
        return [new Kind2MathHandler(this, this.rule.math)];
    }
}

module.exports = Kind2RuleHandler;
