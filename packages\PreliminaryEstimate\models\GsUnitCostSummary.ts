import {Column, Entity} from "typeorm";
import {GsBaseModel} from "./GsBaseModel";

/**
 * 费用汇总
 */
export class GsUnitCostSummary extends GsBaseModel {

    public dispNo : number;  //序号
    public unitId : string;  //单位id
    public code : string;  //费用代号
    public name : string;  //名称
    public calculateFormula : string;  //计算基数
    public instructions : string;  //基数说明
    public category : string; //费用类别
    public rate : string;  //费率
    public price : number;  //金额
    public remark : string;  //备注
    public orderNum : number;  //内部排序用的序号
    public whetherPrint : number;  // 是否打印 1:打印  0:不打印
    public whetherTax  : number;  // 是否税金字段 1:税金   !==1 不是税金字段
    public permission: [];  // 权限
    public adopted : boolean;  // 是否被引用
    public isUpdateRate : boolean;   // 费率是否被修改


    constructor(sequenceNbr: string, recUserCode: string, recStatus: string, recDate: string, extend1: string, extend2: string, extend3: string, description: string,
                unitId: string, code: string, name: string, calculateFormula: string, instructions: string, category: string, rate: string, price: number,
                remark: string, orderNum: number, dispNo: number, whetherPrint: number, whetherTax: number, permission: [], adopted: boolean, isUpdateRate: boolean) {
        super(sequenceNbr);
        this.unitId = unitId;
        this.code = code;
        this.name = name;
        this.calculateFormula = calculateFormula;
        this.instructions = instructions;
        this.category = category;
        this.rate = rate;
        this.price = price;
        this.remark = remark;
        this.orderNum = orderNum;
        this.dispNo = dispNo;
        this.whetherPrint = whetherPrint;
        this.whetherTax = whetherTax;
        this.permission = permission;
        this.adopted = adopted;
        this.isUpdateRate = isUpdateRate;
    }
}