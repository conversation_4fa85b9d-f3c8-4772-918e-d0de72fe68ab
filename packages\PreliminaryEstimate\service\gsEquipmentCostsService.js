const {Service} = require("../../../core");

const ProjectDomain = require('../domains/ProjectDomain');
const {ObjectUtils} = require('../utils/ObjectUtils');
const {ConvertUtil} = require("../utils/ConvertUtils");
const {GsEquipmentCostsCal} = require('../models/GsEquipmentCostsCal');
const {GsEquipmentCosts} = require('../models/GsEquipmentCosts');
// const {NumberUtil} = require("../../../common/NumberUtil");
const {NumberUtil} = require("../utils/NumberUtil");
const sbgzfCost = require("../jsonData/gs_sbgzf_cost.json");
const sbgzfJsjs = require("../jsonData/gs_sbgzf_jsjs.json");
const {Snowflake} = require("../utils/Snowflake");
const {ResponseData} = require("../utils/ResponseData");
const SbgzFeeEnum = require("../enums/SbgzFeeEnum");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {default: Decimal} = require("decimal.js");
const UtilsPs = require("../../../core/ps");
const FileOperatorType = require("../constants/FileOperatorType");
const {dialog} = require("electron");
const fs = require("fs");
const xeUtils = require("xe-utils");
const XLSX = require('xlsx');
const Excel = require('exceljs');


class GsEquipmentCostsService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    getChildrenMenuList(args) {
        let array = new Array();
        let obj1 = {};
        obj1[FunctionTypeConstants.SBGZF_KEY_TYPE_GN] = "国内采购设备";
        let obj2 = {};
        obj2[FunctionTypeConstants.SBGZF_KEY_TYPE_GW] = "国外采购设备";
        let obj3 = {};
        obj3[FunctionTypeConstants.SBGZF_KEY_TYPE_HZ] = "设备购置费汇总";
        // let obj1 ={
        //     FunctionTypeConstants.SBGZF_KEY_TYPE_GN:"国内采购设备"
        // };
        // let obj2 ={
        //     "v":"国外采购设备"
        // };
        // let obj3 ={
        //     FunctionTypeConstants.SBGZF_KEY_TYPE_HZ:"设备购置费汇总"
        // };
        array.push(obj1);
        array.push(obj2);
        array.push(obj3);
        let treeList = {};
        treeList.itemList = array;
        return treeList;
    }

    _reorder(array, type) {
        if (ObjectUtils.isEmpty(array)) {
            return;
        }
        if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
            array.forEach((item, index) => {
                item.dispNo = index === 0 ? null : index;
            });
        } else {
            array.forEach((item, index) => item.dispNo = index + 1);
        }
    }

    /**
     * 保存列表
     * @param args
     */
    async save(args) {

        const {constructId, unitId, type, eqCosts, operateType} = args;
        //初始化id
        let newEqCosts = new GsEquipmentCosts();
        if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
            newEqCosts = new GsEquipmentCostsCal();
        }
        ConvertUtil.setDstBySrc(eqCosts, newEqCosts);

        //小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(args.constructId);

        let list = this.getList(args);
        // if(operateType === "delete"){
        //     list = list.filter(item=>{
        //         return item.sequenceNbr !== newEqCosts.sequenceNbr;
        //     });
        //     return;
        // }
        if (operateType === "edit") {
            let oldCostCal = list.find(item => item.sequenceNbr === newEqCosts.sequenceNbr);
            //重新计算市场价
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GN) {

                let factoryOk = true;
                if (newEqCosts.price != oldCostCal.price) {
                    factoryOk = false;
                }
                ConvertUtil.setDstBySrc(newEqCosts, oldCostCal);
                let factoryPrice = oldCostCal.factoryPrice;
                let quantity = oldCostCal.quantity;
                //国内计算市场价
                this.gnCaculatorPrice(oldCostCal, factoryOk, precision);
                if (ObjectUtils.isEmpty(quantity)) {
                    oldCostCal.quantity = null;
                }
                if (ObjectUtils.isEmpty(factoryPrice)) {
                    oldCostCal.factoryPrice = null;
                }

            }
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GW) {
                //国外计算市场价
                this.gwCaculatorPrice(oldCostCal, newEqCosts);
                newEqCosts.equipmentCostsCal = oldCostCal.equipmentCostsCal;
                newEqCosts.cif_cny = oldCostCal.cif_cny;
                newEqCosts.cif_usd = oldCostCal.cif_usd;
                newEqCosts.price = oldCostCal.price;
                newEqCosts.fob_usd = oldCostCal.fob_usd ? (oldCostCal.fob_usd + "") : ''; //防止number化
                newEqCosts.totalPrice = oldCostCal.totalPrice;

                ConvertUtil.setDstBySrc(newEqCosts, oldCostCal);

            }
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
                //检查基数  &  费率 
                let newhzCode = newEqCosts.costBase.trim();
                if (newhzCode.length > 0) {

                    let costBases = newhzCode.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                    let isNotNumber = false;
                    for (let i = 0; i < costBases.length; i++) {
                        let tempCostBase = costBases[i];
                        if (tempCostBase !== FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF
                            && (!ObjectUtils.isNumberStr(tempCostBase) || this.isInvalidOctalLiteral(tempCostBase))) {
                            isNotNumber = true;
                            break;
                        }
                    }

                    if (isNotNumber) {
                        return ResponseData.fail('费率基数格式错误');
                    }

                }
                if (ObjectUtils.isNotEmpty(newEqCosts.rates)) {
                    if (ObjectUtils.isNumberStr(newEqCosts.rates)) {
                        newEqCosts.rates = Number(newEqCosts.rates);
                    }
                    if (!ObjectUtils.isNumber(newEqCosts.rates)) {
                        newEqCosts.rates = null;
                    }
                }
                newEqCosts.costBase = newhzCode;
                ConvertUtil.setDstBySrc(newEqCosts, oldCostCal);
                if (!oldCostCal.jsonStr) {
                    oldCostCal.jsonStr = newEqCosts.jsonStr ? newEqCosts.jsonStr : FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF;
                }
            }
        }
        if (operateType === "insert") {
            let newEqCosts2 = new GsEquipmentCosts();
            newEqCosts2.sequenceNbr = Snowflake.nextId();
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GN) {
                //国内计算市场价
                this.gnCaculatorPrice(newEqCosts2, true, precision);
                newEqCosts2.quantity = null;
                newEqCosts2.factoryPrice = null;
                newEqCosts2.remark = "国内设备运杂费率5%";
            }
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GW) {
                //国外计算市场价
                newEqCosts2.equipmentCostsCal = this.initCaculator();
                newEqCosts2.remark = "国外设备运杂费率3%";
            }

            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
                newEqCosts2 = new GsEquipmentCostsCal();
                newEqCosts2.sequenceNbr = Snowflake.nextId();
                newEqCosts2.costBase = FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF;
                newEqCosts2.price = 0;
                newEqCosts2.jsonStr = FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF;

                if (ObjectUtils.isEmpty(newEqCosts.dispNo)) {
                    newEqCosts.dispNo = 1;
                } else {
                    newEqCosts.dispNo = newEqCosts.dispNo + 1;
                }
            }
            list.splice(newEqCosts.dispNo, 0, newEqCosts2);
        }
        if (operateType === "copy") {
            newEqCosts.sequenceNbr = Snowflake.nextId();
            let dispNo = newEqCosts.dispNo;
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
                if (ObjectUtils.isEmpty(newEqCosts.costBase)) {
                    newEqCosts.costBase = FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF;
                }
                if (ObjectUtils.isEmpty(dispNo)) {
                    dispNo = 1;
                } else {
                    dispNo = dispNo + 1;
                }
            }
            list.splice(dispNo, 0, newEqCosts)
        }
        this._reorder(list, type);
        //操作 类型  1:插入 2:粘贴 3删除 4 修改
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, type), list);
        if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GN || type === FunctionTypeConstants.SBGZF_KEY_TYPE_GW) {
            this.caculatorTotal(constructId, unitId);
        } else if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
            let allCost = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(unitId, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF));
            if (ObjectUtils.isEmpty(allCost) || allCost === 0) {
                this.caculatorTotal(constructId, unitId);
            } else {
                this.caculatorSumTotal(constructId, unitId, allCost);
            }
        }
        // 获取建设其他费费用代码
        await this.service.PreliminaryEstimate.gsOtherProjectCostService.countOtherProjectCostCode({
            projectId: constructId
        });
        // //  计算概算费用代码
        // this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
        //     constructId: constructId
        // });

        return ResponseData.success();
    }

    /**
     * 国外
     * @param {*} eqCosts
     * @returns
     */
    gwCaculatorPrice(oldEqCosts, eqCosts) {
        let changePrice = true;
        let changedCif = false;
        if (oldEqCosts.price !== eqCosts.price) {
            changePrice = false;
            oldEqCosts.price = eqCosts.price;
            oldEqCosts.fob_usd = null;//把离岸价值为null
            oldEqCosts.cif_usd = null;//把到岸价值为null
            oldEqCosts.cif_cny = null;
        }
        //数量变化需要判断是否需要计算器
        if (oldEqCosts.quantity !== eqCosts.quantity) {
            let price = this.convertNumber(oldEqCosts.price);
            let fobUsd = this.convertNumber(oldEqCosts.fob_usd);
            changePrice = fobUsd <= 0 && price > 0 ? false : true;
        }
        if (changePrice && (oldEqCosts.fob_usd !== eqCosts.fob_usd
            || oldEqCosts.quantity !== eqCosts.quantity
            || oldEqCosts.exchangeRate !== eqCosts.exchangeRate)
            && ObjectUtils.isNotEmpty(oldEqCosts.equipmentCostsCal)) {
            oldEqCosts.fob_usd = eqCosts.fob_usd + "";//防止number化
            oldEqCosts.exchangeRate = eqCosts.exchangeRate;
            oldEqCosts.quantity = eqCosts.quantity;
            // oldEqCosts.equipmentCostsCal.forEach(item => {
            //     item.price = this.recalculatePrice(oldEqCosts.equipmentCostsCal,item,oldEqCosts.exchangeRate);
            // })
            this.caculateCaculator(oldEqCosts.equipmentCostsCal, oldEqCosts, true);
            let fobUsd = this.convertNumber(oldEqCosts.fob_usd);
            let newFobUsd = this.convertNumber(eqCosts.fob_usd);
            oldEqCosts.fob_usd = "" + (fobUsd > 0 ? oldEqCosts.fob_usd : newFobUsd);
        }
        let quantity = this.convertNumber(eqCosts.quantity);
        let price = this.convertNumber(oldEqCosts.price);
        oldEqCosts.totalPrice = NumberUtil.roundHalfUp(NumberUtil.multiply(quantity, price));
    }

    initCaculator() {
        let list = [];
        for (let i in sbgzfJsjs) {
            let eqCostsCal = new GsEquipmentCostsCal();
            ConvertUtil.setDstBySrc(sbgzfJsjs[i], eqCostsCal);
            eqCostsCal.sequenceNbr = Snowflake.nextId();
            eqCostsCal.jsonStr = Object.keys(SbgzFeeEnum).join(",");
            list.push(eqCostsCal);

        }
        return list;
    }

    /**
     * 国外计算器计算
     * @param {*} args
     * @returns
     */
    async reCaculator(args) {
        let {constructId, list, exchangeRate, sequenceNbr, eqCostsCal, operateType} = args;
        let reCaculateFlag = false; //是否需要重新计算金额
        if (operateType === "insert") {
            let newEqCostsCal = new GsEquipmentCostsCal();
            newEqCostsCal.sequenceNbr = Snowflake.nextId();
            newEqCostsCal.costType = "其他（人民币）";
            newEqCostsCal.price = 0;
            newEqCostsCal.costBase = '';
            newEqCostsCal.jsonStr = Object.keys(SbgzFeeEnum).join(",");
            list.splice(eqCostsCal.dispNo, 0, newEqCostsCal);
        }
        if (operateType === "delete") {

            let deleteIndex = -1;
            list.forEach((item, index) => {
                if (item.sequenceNbr === eqCostsCal.sequenceNbr) {
                    deleteIndex = index;
                } else {
                    item.costBase = this.replaceCostBase(item.costBase, eqCostsCal.code, '※');
                }
            });
            if (deleteIndex > -1) {
                list.splice(deleteIndex, 1);
            }
            reCaculateFlag = true;//需要重新计算je
        }
        if (operateType === "change") {
            reCaculateFlag = true;//需要重新计算je
        }
        if (operateType === "edit") {

            let oldCostCal = list.find(item => item.sequenceNbr === eqCostsCal.sequenceNbr);
            let oldCostBase = "" + oldCostCal.costBase; //防止number比较
            if (!this.compareIgnoringCase(oldCostBase, eqCostsCal.costBase)) {
                //检查是否hefa
                let costBases = eqCostsCal.costBase.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);

                let checkBaseErr = false;
                for (let i = 0; i < costBases.length; i++) {
                    let item = costBases[i];
                    //八进制表示法错误
                    if (this.isInvalidOctalLiteral(item)) {
                        checkBaseErr = true;
                        break;
                    }
                    if (item !== '※' && item.indexOf('※') > -1) {
                        checkBaseErr = true;
                        break;
                    }
                    if (ObjectUtils.isNotEmpty(item) && !ObjectUtils.isNumberStr(item)) {
                        let baseItem = list.find(itemList => this.compareIgnoringCase(itemList.code, item));
                        if (!baseItem) {
                            checkBaseErr = true;
                            break;
                        }
                    }
                }
                if (checkBaseErr) {
                    return ResponseData.fail('基数修改不合法');
                }
                oldCostCal.costBase = eqCostsCal.costBase;
            }
            if (oldCostCal.costType !== eqCostsCal.costType) {
                oldCostCal.costType = eqCostsCal.costType;
                if (SbgzFeeEnum[eqCostsCal.costType].code === 'FOB') {
                    let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_GW));
                    let gwItm = list.find(item => item.sequenceNbr === sequenceNbr);
                    oldCostCal.costBase = gwItm.fob_usd;
                }
            }
            if (!this.compareIgnoringCase(oldCostCal.code, eqCostsCal.code)) {
                let newCode = eqCostsCal.code.trim();
                let newCodeArr = newCode.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                if (newCodeArr.length > 1 || eqCostsCal.code.indexOf('※') > -1) {
                    return ResponseData.fail('基数修改不合法');
                }
                if (ObjectUtils.isNumber(eqCostsCal.code) || ObjectUtils.isNumberStr(eqCostsCal.code)) {
                    return ResponseData.fail('费用代号不能为数字');
                }
                if (newCode !== '') {
                    //检查重复
                    let findItem = list.find(item => this.compareIgnoringCase(item.code, eqCostsCal.code) && item.sequenceNbr !== eqCostsCal.sequenceNbr);
                    if (findItem) {
                        return ResponseData.fail('费用代号重复');
                    }
                }
                //先替换 后检查
                list.forEach(item => {
                    item.costBase = this.replaceCostBase(item.costBase, oldCostCal.code, newCode === '' ? '※' : newCode);
                })
                oldCostCal.code = newCode;
            }
            if (oldCostCal.rates !== eqCostsCal.rates) {
                oldCostCal.rates = eqCostsCal.rates
                if (!ObjectUtils.isNumber(eqCostsCal.rates) && !ObjectUtils.isNumberStr(eqCostsCal.rates)) {
                    return ResponseData.fail('费率必须数字');
                }
            }

            if (this.doInspection(list)) {
                return ResponseData.fail('费用代号存在循环依赖');
            }
            oldCostCal.costBase = eqCostsCal.costBase
            oldCostCal.code = eqCostsCal.code
            oldCostCal.name = eqCostsCal.name
            oldCostCal.remark = eqCostsCal.remark

            reCaculateFlag = true;//需要重新计算je
        }
        if (reCaculateFlag) {
            list.filter(item => {
                //重新计算单个计算器数据的值
                item.price = this.recalculatePrice(list, item, exchangeRate);
                console.log('costbase:' + item.costBase + "&rates：" + item.rates + "&price:" + item.price);
            });
        } else {
            //重新排序
            list.forEach((item, index) => item.dispNo = index + 1);
        }

        return ResponseData.success(list);
    }

    replaceCostBase(costBase, code, newCode) {
        let baseMap = new Map();
        let costBases = costBase.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
        costBases.forEach(item => {
            if (this.compareIgnoringCase(item, code)) {
                baseMap.set(item, 0);
            }
        });
        baseMap.forEach((value, key) => {
            let reg = new RegExp("\\b" + key + "\\b", "gi")
            costBase = costBase.replaceAll(reg, newCode);
        });
        return costBase;
    }

    isInvalidOctalLiteral(str) {
        // 匹配以至少两个零开头的字符串  
        const regex = /^00+/;
        return regex.test(str);
    }

    compareIgnoringCase(str1, str2) {
        if (ObjectUtils.isEmpty(str1)) {
            str1 = '';
        }
        if (ObjectUtils.isEmpty(str2)) {
            str2 = '';
        }

        // 使用正则表达式替换英文字符为小写
        const lowerCaseStr1 = str1.replace(/[a-zA-Z]/g, c => c.toLowerCase());
        const lowerCaseStr2 = str2.replace(/[a-zA-Z]/g, c => c.toLowerCase());

        // 比较两个处理后的字符串
        return lowerCaseStr1 === lowerCaseStr2;
    }

    /**
     * 重新计算器的当个项的价格
     * @param {*} list
     * @param {*} eqCostsCal
     * @param {*} exchangeRate
     * @returns
     */
    recalculatePrice(list, eqCostsCal, exchangeRate) {
        exchangeRate = this.convertNumber(exchangeRate);
        if (ObjectUtils.isEmpty(eqCostsCal.costBase)) {
            return 0;
        }
        //如果单纯的数字直接计算
        if (ObjectUtils.isNumber(eqCostsCal.costBase) || ObjectUtils.isNumberStr(eqCostsCal.costBase)) {
            let rates2 = 0;
            if (SbgzFeeEnum[eqCostsCal.costType].code === 'JNF') {
                let rateNumber = eval(100 - eqCostsCal.rates);
                rates2 = rateNumber === 0 ? 0 : NumberUtil.divide(eqCostsCal.rates, rateNumber);
            } else {
                rates2 = NumberUtil.divide(eqCostsCal.rates, 100);
            }
            let resultEval = eval(eqCostsCal.costBase)
            if (resultEval === Infinity || resultEval === -Infinity) {
                resultEval = 0;
            }
            return parseFloat((resultEval * rates2).toFixed(2));
        }
        let baseArr = eqCostsCal.costBase.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
        let baseMap = new Map();
        list.forEach(item => {
            baseArr.forEach(baseCodee => {
                if (baseCodee === item.code) {
                    let price = item.price;
                    if (SbgzFeeEnum[item.costType].type !== SbgzFeeEnum[eqCostsCal.costType].type) {
                        price = SbgzFeeEnum[eqCostsCal.costType].type === "00" ? item.price * exchangeRate : item.price / exchangeRate;
                    }
                    baseMap.set(baseCodee, price);
                }
            })
        })
        let afterCalculateFormula = eqCostsCal.costBase;
        afterCalculateFormula = afterCalculateFormula.replaceAll('※', 0);
        baseMap.forEach((value, key) => {
            let reg = new RegExp("\\b" + key + "\\b", "gi")
            afterCalculateFormula = afterCalculateFormula.replaceAll(reg, value);
        });
        let rates2 = 0;
        if (SbgzFeeEnum[eqCostsCal.costType].code === 'JNF') {
            let rateNumber = eval(100 - eqCostsCal.rates);
            rates2 = rateNumber === 0 ? 0 : NumberUtil.divide(eqCostsCal.rates, rateNumber);
        } else {
            rates2 = NumberUtil.divide(eqCostsCal.rates, 100);
        }
        let resultEval = 0;
        try {
            resultEval = eval(afterCalculateFormula)
        } catch (error) {
            if (error instanceof ReferenceError) {
                console.log('计算错误：' + afterCalculateFormula + ':' + error.message);
                resultEval = 0;
            }
        }
        if (resultEval === Infinity || resultEval === -Infinity) {
            resultEval = 0;
        }
        //判重
        return parseFloat((resultEval * rates2).toFixed(2));
    }

    //循环引用检测
    doInspection(nodes) {
        // 遍历数组中的每个节点
        for (const node of nodes) {
            // 创建一个Set用于记录已经访问过的节点
            const visited = new Set();
            // 如果在任何一个节点中发现循环引用，返回true
            if (this.dfs(node, nodes, visited)) {
                return true;
            }
        }

        // 如果没有发现循环引用，返回false
        return false;
    }


    // 递归函数，用于深度优先搜索
    dfs(node, allNodes, visited) {
        // 如果节点已经访问过，则存在循环引用
        if (visited.has(node)) {
            return true;
        }

        // 标记当前节点为已访问
        visited.add(node);

        // 遍历当前节点的子节点
        if (node.costBase) {
            let childrens = allNodes.filter(item => {
                if(!isNaN(node.costBase) && typeof node.costBase === 'number'){
                    return false;
                }
                let costBases = node.costBase.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/)
                let existsItem = costBases.find(listItem => {
                    return this.compareIgnoringCase(item.code, listItem)
                });
                if (ObjectUtils.isEmpty(existsItem)) {
                    return false;
                }
                return true;
            });
            for (const childNode of childrens) {
                let newVisited = new Set(visited);
                // 如果在子节点中发现循环引用，返回true
                if (this.dfs(childNode, allNodes, newVisited)) {
                    return true;
                }
            }
        }

        // 如果没有发现循环引用，返回false
        return false;
    }

    /**
     * 计算器点击确定
     * @param {*} args
     * @returns
     */
    async saveCaculator(args) {
        //处理计算器数据
        let {constructId, exchangeRate, unitId, sequenceNbr, list} = args;
        if (ObjectUtils.isEmpty(list)) {
            return ResponseData.success();
        }
        let gwList = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(unitId, FunctionTypeConstants.SBGZF_KEY_TYPE_GW));
        let gwItem = gwList.find(item => item.sequenceNbr === sequenceNbr);
        if (ObjectUtils.isNotEmpty(gwItem)) {
            gwItem.exchangeRate = this.convertNumber(exchangeRate);
            this.caculateCaculator(list, gwItem, false);

            //计算总价
            let quantity = this.convertNumber(gwItem.quantity);
            let price = this.convertNumber(gwItem.price);
            gwItem.totalPrice = NumberUtil.roundHalfUp(NumberUtil.multiply(quantity, price));
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, FunctionTypeConstants.SBGZF_KEY_TYPE_GW), gwList);
            //单价变化，影响SBF的变化，SBF变化影响汇总数据的变化
            this.caculatorTotal(constructId, unitId);
            // 获取建设其他费费用代码
            await this.service.PreliminaryEstimate.gsOtherProjectCostService.countOtherProjectCostCode({
                projectId: constructId
            });
            // //  计算概算费用代码
            // this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
            //     constructId: constructId
            // });
        }
        return ResponseData.success();
    }

    convertNumber(priceStr) {
        let price = priceStr;
        if (ObjectUtils.isEmpty(price) || !ObjectUtils.isNumberStr(price)) {
            price = 0;
        }
        if (ObjectUtils.isNumberStr(price)) {
            price = Number(price);
        }
        return price;
    }

    caculateCaculator(list, gwItem, isReplace) {
        let sbdjLists = [];
        let fobLists = [];
        let cifLists = [];
        let cif00Lists = [];
        list.filter(item => {
            if (isReplace && SbgzFeeEnum[item.costType].code === 'FOB') {
                item.costBase = gwItem.fob_usd;
            }
        });
        list.filter(item => {
            //重新计算单个计算器数据的值
            item.price = this.recalculatePrice(list, item, gwItem.exchangeRate);

            if (SbgzFeeEnum[item.costType].code === 'SBDJ') {
                sbdjLists.push(item);
            }
            if (SbgzFeeEnum[item.costType].code === 'FOB') {
                fobLists.push(item);
            }
            if (SbgzFeeEnum[item.costType].code === 'CIF') {
                cifLists.push(item);
            }
            if (SbgzFeeEnum[item.costType].code === 'CIF_00') {
                cif00Lists.push(item);
            }
        });
        let allPrice = 0;
        let cifAllUsd = 0;
        let cifAllCny = 0;
        let fobAllPrice = 0;
        //计算单价 
        sbdjLists.forEach(item => allPrice += this.convertNumber(item.price));
        fobLists.forEach(item => fobAllPrice += this.convertNumber(item.price));
        cifLists.forEach(item => cifAllUsd += this.convertNumber(item.price));
        cif00Lists.forEach(item => cifAllCny += this.convertNumber(item.price));
        //计算合价
        let quantity = this.convertNumber(gwItem.quantity);
        gwItem.quantity = quantity;

        gwItem.price = allPrice;
        gwItem.fob_usd = fobAllPrice + "";
        gwItem.cif_usd = NumberUtil.roundHalfUp(NumberUtil.multiply(cifAllUsd, quantity));
        //计算到岸价人民币
        if (cifAllCny > 0) {
            gwItem.cif_cny = NumberUtil.roundHalfUp(NumberUtil.multiply(cifAllCny, quantity));
        } else {
            let exchangeRate = this.convertNumber(gwItem.exchangeRate);
            let cif_usd = this.convertNumber(gwItem.cif_usd);
            gwItem.cif_cny = NumberUtil.roundHalfUp(NumberUtil.multiply(cif_usd, exchangeRate));
        }
        gwItem.equipmentCostsCal = list;
    }

    /**
     * 国内
     */
    gnCaculatorPrice(eqCosts, factoryOk, precision) {
        let price = this.convertNumber(eqCosts.price);

        let quantity = this.convertNumber(eqCosts.quantity);
        eqCosts.quantity = quantity;


        let factoryPrice = eqCosts.factoryPrice;
        if (factoryPrice !== '' && !ObjectUtils.isNumberStr(factoryPrice)) {
            factoryOk = false;
            factoryPrice = 0;
            eqCosts.factoryPrice = null;
        }
        if (ObjectUtils.isNumberStr(factoryPrice)) {
            factoryPrice = Number(factoryPrice);
            eqCosts.factoryPrice = factoryPrice;
        }
        if (factoryPrice === '') {
            factoryPrice = 0;
        }

        //市场价为出厂价*运杂费率 默认5%
        let rates = eqCosts.transportAndOtherRates;
        if (ObjectUtils.isEmpty(rates) || !ObjectUtils.isNumberStr(rates)) {
            eqCosts.transportAndOtherRates = 5;
            rates = 5;
        }
        rates = this.convertNumber(rates);
        rates = NumberUtil.divide(rates + 100, 100);
        let calPrice = 0;
        if (factoryOk) {
            //市场价 = 工厂价*(1+5%)
            let midPrice = NumberUtil.multiply(factoryPrice, NumberUtil.numberScale(rates,precision.PROJECT_PRECISION.SBGZF.transportAndOtherRates))
            calPrice = NumberUtil.roundHalfUp(midPrice);
            if (calPrice != price) {
                //如果市场价有值不精细此处理
                price = calPrice;
                eqCosts.price = price;
            }
        }
        price = this.convertNumber(price);
        eqCosts.totalPrice = NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.numberScale(quantity,precision.PROJECT_PRECISION.SBGZF.quantity), NumberUtil.numberScale(price,precision.PROJECT_PRECISION.SBGZF.price)));
    }

    /**
     * 删除列表
     * @param args
     */
    async delete(args) {

        const {constructId, unitId, type, sequenceNbr, operateType} = args;
        let noticeFlag = false;
        if (ObjectUtils.isNotEmpty(operateType) && operateType === "deleteAll") {
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, type), []);
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GN || type === FunctionTypeConstants.SBGZF_KEY_TYPE_GW) {
                this.caculatorTotal(constructId, unitId);
                noticeFlag = true;
            }
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
                let allCost = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(unitId, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF));
                //计算汇总数据的变化
                this.caculatorSumTotal(constructId, unitId, allCost);
                noticeFlag = true;
            }
        }

        let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(unitId, type));

        let deleteItemIndex = list.findIndex(item => (item.sequenceNbr === sequenceNbr))
        if (deleteItemIndex > -1) {
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ && deleteItemIndex === 0) { //汇总数据第一行删除不处理
                return;
            }
            list.splice(deleteItemIndex, 1);
            this._reorder(list, type);
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, type), list);
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GN || type === FunctionTypeConstants.SBGZF_KEY_TYPE_GW) {
                this.caculatorTotal(constructId, unitId);
                noticeFlag = true;
            }
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
                let allCost = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(unitId, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF));
                //计算汇总数据的变化
                this.caculatorSumTotal(constructId, unitId, allCost);
                noticeFlag = true;
            }
        }
        if (noticeFlag) {
            // 获取建设其他费费用代码
            await this.service.PreliminaryEstimate.gsOtherProjectCostService.countOtherProjectCostCode({
                projectId: constructId
            });
            // //  计算概算费用代码
            // this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
            //     constructId: constructId
            // });
        }
    }

    /**
     * 计算所有设备合价
     * @param {*} constructId
     * @param {*} unitId
     */
    caculatorTotal(constructId, unitId) {

        let allCost = 0;
        let gnlist = this.getList({
            constructId: constructId,
            unitId: unitId,
            type: FunctionTypeConstants.SBGZF_KEY_TYPE_GN
        });
        let gwlist = this.getList({
            constructId: constructId,
            unitId: unitId,
            type: FunctionTypeConstants.SBGZF_KEY_TYPE_GW
        });

        gnlist.forEach(item => {
            let totalPrice = this.convertNumber(item.totalPrice);
            allCost = NumberUtil.add(allCost, totalPrice)
        });
        gwlist.forEach(item => {
            let totalPrice = this.convertNumber(item.totalPrice);
            allCost = NumberUtil.add(allCost, totalPrice)
        });
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF), allCost);
        //计算汇总数据的变化
        this.caculatorSumTotal(constructId, unitId, allCost);
    }

    caculatorSumTotal(constructId, unitId, sbfCost) {
        let allCost = 0;
        let list = this.getList({
            constructId: constructId,
            unitId: unitId,
            type: FunctionTypeConstants.SBGZF_KEY_TYPE_HZ
        });
        //跳过首个汇总计算
        for (let i = 1; i < list.length; i++) {
            let item = list[i];
            let costNumber = 0;
            if (item.costBase === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF) {
                costNumber = sbfCost;
            } else {
                let costBases = item.costBase;
                if (costBases.indexOf(FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF) > -1) {
                    costBases = costBases.replaceAll(FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF, sbfCost);
                }
                let isNotNumber = true;
                let baseArr = costBases.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);

                for (let i = 0; i < baseArr.length; i++) {
                    let itemArr = baseArr[i];
                    if (ObjectUtils.isEmpty(itemArr)) {
                        isNotNumber = false;
                    }
                    //八进制表示法错误
                    if (this.isInvalidOctalLiteral(itemArr)) {
                        isNotNumber = false;
                    }
                    if (!ObjectUtils.isNumber(itemArr) && !ObjectUtils.isNumberStr(itemArr)) {
                        isNotNumber = false;
                    }
                }
                if (isNotNumber) {
                    costNumber = costBases;
                }

            }
            let rateNumber = NumberUtil.divide(item.rates, 100)
            let resultEval = eval(costNumber)
            if (resultEval === Infinity || resultEval === -Infinity) {
                resultEval = 0;
            }
            item.price = NumberUtil.roundHalfUp(NumberUtil.multiply(resultEval, rateNumber));
            if (ObjectUtils.isNumberStr(item.price)) {
                item.price = Number(item.price);
            }
            allCost = NumberUtil.add(allCost, item.price);
        }
        list[0].price = allCost; //总的金额
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ), list);
    }

    /**
     * 获取信息列表
     * @param args
     */
    getList(args) {
        const {constructId, unitId, type} = args;
        let list = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(unitId, type));
        if (ObjectUtils.isEmpty(list)) {
            list = [];
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
                for (let i in sbgzfCost) {
                    let eqCostsCal = new GsEquipmentCostsCal();
                    ConvertUtil.setDstBySrc(sbgzfCost[i], eqCostsCal);
                    eqCostsCal.sequenceNbr = Snowflake.nextId();
                    eqCostsCal.jsonStr = FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF;
                    list.push(eqCostsCal);
                }
            }
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, type), list);
        } else {
            // 兼容map对象处理
            if (list[0] instanceof Map) {
                for (let index = 0; index < list.length; index++) {
                    list[index] = ObjectUtils.stringifyComplexObject(list[index]);
                }
            }
        }
        return list;
    }

    initData(type) {
        let list = [];
        if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_HZ) {
            for (let i in sbgzfCost) {
                let eqCostsCal = new GsEquipmentCostsCal();
                ConvertUtil.setDstBySrc(sbgzfCost[i], eqCostsCal);
                eqCostsCal.jsonStr = sbgzfCost[i].jsonStr;
                eqCostsCal.sequenceNbr = Snowflake.nextId();
                list.push(eqCostsCal);
            }
        }
        if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GN) {
            let eqCosts = new GsEquipmentCosts();
            eqCosts.sequenceNbr = Snowflake.nextId();
            eqCosts.transportAndOtherRates = 5;
            eqCosts.dispNo = 1;
            eqCosts.totalPrice = '0.00';
            eqCosts.remark = '国内设备运杂费率5%';
            list.push(eqCosts)
        }
        if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GW) {
            let eqCosts = new GsEquipmentCosts();
            eqCosts.sequenceNbr = Snowflake.nextId();
            eqCosts.equipmentCostsCal = this.initCaculator();
            eqCosts.dispNo = 1;
            eqCosts.fob_usd = null;
            eqCosts.quantity = null;
            eqCosts.remark = '国外设备运杂费率3%';
            list.push(eqCosts)
        }
        return list;
    }

    /**
     * sbgzf00 国内采购设备 sbgzf01 国外采购设备 sbgzf02 设备购置费汇总 SBF   国内外设备费总额
     * @param {*} unitId
     * @param {*} type
     * @returns
     */
    getDataMapKey(unitId, type) {

        if (ObjectUtils.isEmpty(unitId)) {
            unitId = "0";//保持key风格一致性
        }
        return "EQMCOST-" + unitId + "-" + type;
    }


    /**
     * 导入设备购置费汇总
     * @param args
     */
    async importEquipmentCosts(args) {
        let {constructId, projectId} = args;
        constructId = projectId;

        const jsqtfTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\设备购置费';
        let options = {
            properties: ['openFile'],
            defaultPath: jsqtfTemplatePath, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_GZF]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            return ResponseData.fail('未选中任何文件');
        }

        // 建设其他费文件的路径
        const qtfFilePath = result[0];
        try {
            const data = fs.readFileSync(qtfFilePath, 'utf8');
            // 使用JSON.parse()方法将JSON字符串转换为JavaScript数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段
            const otherProjectCostSummaryArray = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.sequenceNbr = Snowflake.nextId();
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.costBase = line.costBase;
                obj.rates = line.rates;
                obj.price = line.price;  // 备份默认值
                obj.costType = line.costType;
                obj.remark = line.remark;
                obj.jsonStr = line.jsonStr;
                otherProjectCostSummaryArray.push(obj);
            });

            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ), otherProjectCostSummaryArray);

            let allCost = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF));
            if (ObjectUtils.isEmpty(allCost) || allCost === 0) {
                this.caculatorTotal(constructId, null);
            } else {
                this.caculatorSumTotal(constructId, null, allCost);
            }

            // 获取建设其他费费用代码
            await this.service.PreliminaryEstimate.gsOtherProjectCostService.countOtherProjectCostCode({
                projectId: constructId
            });

            return ResponseData.success(otherProjectCostSummaryArray);
        } catch (err) {
            return ResponseData.fail('导入失败');
        }
    }


    /**
     * 导出设备购置费
     * @param args
     */
    async exportEquipmentCosts(args) {
        let constructId = args.constructId;
        constructId = args.projectId;
        // 获取设备购置费汇总
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ));
        // 复制一份数据
        let otherProjectCostCopy = ConvertUtil.deepCopy(otherProjectCosts);
        // otherProjectCostCopy.forEach(item => {
        //     if (!(ObjectUtils.isNotEmpty(item.calculationMethod) && item.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2)) {
        //         item.price = null;
        //         item.quantity = null;
        //         item.priceDescription = null;
        //     }
        // });
        // 指定导出的列名
        const columns = ["dispNo", "code", "name", "costBase", "rates", "price", "costType", "remark", "jsonStr"];
        // 根据指定的列名来重组数据，确保导出的JSON只包含这些列
        const formattedData = otherProjectCostCopy.map(item => {
            return columns.reduce((acc, col) => {
                acc[col] = item[col];
                return acc;
            }, {});
        });
        // 将数组转换为JSON字符串   const jsonData = JSON.stringify(formattedData, null, 2);
        const jsonData = JSON.stringify(formattedData); // 第三个参数是缩进量，使输出更易读

        // 存放费用汇总文件的路径
        const jsqtfTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\设备购置费';
        const count = await this.countDirectories(jsqtfTemplatePath);
        let options = {
            title: '保存文件',
            defaultPath: jsqtfTemplatePath + '\\设备购置费汇总模板' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_GZF]} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(FileOperatorType.File_TYPE_GZF)) {
                filePath += FileOperatorType.File_TYPE_GZF;
            }
            // 写入文件
            fs.writeFile(filePath, jsonData, (err) => {
                if (err) {
                    ResponseData.fail('写入文件时发生错误');
                } else {
                    console.log('数据已成功导出到 ${filePath}');
                    ResponseData.success('数据已成功导出');
                }
            });
            return ResponseData.success(filePath);
        }
    }


    /**
     * 上移下移设备购置费
     * @param args
     * @returns {*}
     */
    async moveUpAndDownEquipmentCosts(args) {
        let {constructId, moveType, sequenceNbrArray, type, projectId} = args;
        constructId = projectId;
        //获取内存设备购置费数据
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(null, type));

        sequenceNbrArray.forEach(sequenceNbr => {
            // 获取当前选定行的父节点，获取他的子集
            let projectCostParentNode = {}
            projectCostParentNode.children = otherProjectCosts;
            if (ObjectUtils.isNotEmpty(projectCostParentNode) && ObjectUtils.isNotEmpty(projectCostParentNode.children)) {
                // 遍历建设其他费，找到点击行
                let projectCostNode = projectCostParentNode.children.find(item => item.sequenceNbr === sequenceNbr);
                // 将指定行，在集合中向上/向下移一个位置
                if (moveType === "up") {
                    this.moveItemUp(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                } else if (moveType === "down") {
                    this.moveItemDown(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                }
            }
        });


        let sortNo = 1;
        otherProjectCosts.forEach(o => {
            if (ObjectUtils.isNotEmpty(o.dispNo)) {
                o.dispNo = sortNo++;
            }
        });

        // 更新建设其他费
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(null, type), otherProjectCosts);
        return ResponseData.success(otherProjectCosts);
    }


    async countDirectories(dirPath) {
        let count = 1;
        let numbers = new Array();
        fs.readdirSync(dirPath).forEach((item) => {
            if (item.match(/\d+/g) !== null) {
                numbers.push(item.match(/\d+/g)[0]);
            }
        });
        if (ObjectUtils.isNotEmpty(numbers)) {
            count = Math.max(...numbers) + 1;
        }
        return count;
    }


    /**
     * 向上移位
     * @param array
     * @param index
     */
    moveItemUp(array, index) {
        // 检查索引是否大于0，因为不能移动第一个元素到更前面去
        if (index > 0) {
            // 保存要移动的元素
            let item = array.splice(index, 1)[0];
            // 在当前位置之前插入元素
            array.splice(index - 1, 0, item);
        }
    }


    /**
     * 向下移位
     * @param array
     * @param index
     */
    moveItemDown(array, index) {
        // 检查index是否在数组的有效范围内并且不是最后一个元素
        if (index >= 0 && index < array.length - 1) {
            // 使用splice取出要移动的元素
            const element = array.splice(index, 1)[0];
            // 将取出的元素插入到其下方的位置
            array.splice(index + 1, 0, element);
        }
        // return array;
    }


    /**
     * 导入excel，解析数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async importEquipmentCostsExcel(args) {
        let defaultStoragePath = await this.service.PreliminaryEstimate.gsAppService.getSetStoragePath(null);

        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: 'excel', extensions: ["xls", "xlsx"]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return ResponseData.fail('未选中任何文件');
        }
        //获取选中的路径
        let filePath = result[0];
        const workbookHeight = new Excel.Workbook();
        await workbookHeight.xlsx.readFile(filePath);

        const workbook = XLSX.readFile(filePath);
        const sheetNames = workbook.SheetNames;
        let sheetDates = [];
        for (let i = 0; i < sheetNames.length; i++) {
            let item = sheetNames[i];
            const sheet = workbook.Sheets[item];
            const data = XLSX.utils.sheet_to_json(sheet, {header: 1, raw: true, defval: ''});
            const mergedRanges = sheet['!merges'];  // 获取合并单元格的范围信息
            let rowHeightList = [];       //行高
            let columnWidthList = [];    //列宽
            if (workbookHeight.worksheets[i] !== undefined) {
                workbookHeight.worksheets[i].eachRow({includeEmpty: true}, (row, rowNumber) => {
                    let hanggao = {};
                    hanggao.rowNumber = rowNumber;
                    hanggao.height = row.height;
                    rowHeightList.push(hanggao);
                });
                if (ObjectUtils.isNotEmpty(workbookHeight.worksheets[i].columns)) {
                    workbookHeight.worksheets[i].columns.forEach((column) => {
                        let liekuan = {};
                        liekuan.colNumber = column._number;
                        liekuan.width = column.width;
                        columnWidthList.push(liekuan);
                    });
                }
            }
            let sheetData = {};
            sheetData.sheetName = item;
            sheetData.data = data;
            sheetData.mergedRanges = mergedRanges;
            sheetData.rowHeightList = rowHeightList;
            sheetData.columnWidthList = columnWidthList;
            sheetDates.push(sheetData);
        }
        return {
            filePath: filePath,
            date: sheetDates
        };
    }


    /**
     * 导入国内采购设备费、国外采购设备费保存
     * @param args   type: 国内采购设备 sbgzf00 国外采购设备 sbgzf01
     * @returns {Promise<ResponseData>}
     */
    async importEquipmentCostsData(args) {
        const {constructId, unitId, type, equipmentCosts} = args;

        if (ObjectUtils.isEmpty(equipmentCosts)) {
            return ResponseData.fail('无有效设备行！');
        }
        // 执行导入时，需至少包含一列有效列，若无任何一列有效列，则给出提示：当前无有效列，请识别后导入！
        let validColumnFlag = false;
        // 当导入时同时存在“出厂价”和“市场价”列时，则给出提示：出厂价与市场价互斥，请移除一列数据！。
        let exclusiveColumnFlag = false;

        let equipmentCostList = [];
        for (let i = 0; i < equipmentCosts.length; i++) {
            let equipmentCostOld = equipmentCosts[i];  // 规格型号\名称\单位\数量\出厂价\市场价
            let equipmentCost = new GsEquipmentCosts();
            ConvertUtil.setDstBySrc(equipmentCostOld, equipmentCost);
            let sequenceNbr = Snowflake.nextId();
            equipmentCost.dispNo = i + 1;
            equipmentCost.sequenceNbr = sequenceNbr;
            let quantity = ObjectUtils.isEmpty(equipmentCost.quantity) ? 0 : equipmentCost.quantity;
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GW) {
                equipmentCost.equipmentCostsCal = this.initCaculator();
                let fobUsd = ObjectUtils.isEmpty(equipmentCost.fob_usd) ? 0 : equipmentCost.fob_usd;
                equipmentCost.cif_usd = NumberUtil.roundHalfUp(NumberUtil.multiply(quantity, fobUsd));

                if (ObjectUtils.isEmpty(equipmentCost.specification) && ObjectUtils.isEmpty(equipmentCost.name) && ObjectUtils.isEmpty(equipmentCost.unit)
                    && ObjectUtils.isEmpty(equipmentCost.quantity) && ObjectUtils.isEmpty(equipmentCost.fob_usd)) {
                    validColumnFlag = true;
                }
            }
            if (type === FunctionTypeConstants.SBGZF_KEY_TYPE_GN) {
                if (ObjectUtils.isNotEmpty(equipmentCost.factoryPrice)) {
                    let rates = equipmentCost.transportAndOtherRates;
                    if (ObjectUtils.isEmpty(rates) || !ObjectUtils.isNumberStr(rates)) {
                        equipmentCost.transportAndOtherRates = 5;
                        rates = 5;
                    }
                    rates = this.convertNumber(rates);
                    rates = NumberUtil.divide(rates + 100, 100);
                    //市场价 = 工厂价*(1+5%)
                    let price = NumberUtil.roundHalfUp(NumberUtil.multiply(equipmentCost.factoryPrice, rates));
                    equipmentCost.price = this.convertNumber(price);
                    // 市场合价
                    equipmentCost.totalPrice = NumberUtil.roundHalfUp(NumberUtil.multiply(quantity, equipmentCost.price));
                } else {
                    // 市场合价
                    equipmentCost.factoryPrice = null;
                    let price = ObjectUtils.isEmpty(equipmentCost.price) ? 0 : equipmentCost.price;
                    equipmentCost.price = this.convertNumber(price);
                    equipmentCost.totalPrice = NumberUtil.roundHalfUp(NumberUtil.multiply(quantity, equipmentCost.price));
                }

                // 出厂价&市场价
                if (type === 'sbgzf01' && ObjectUtils.isNotEmpty(equipmentCost.factoryPrice) && ObjectUtils.isNotEmpty(equipmentCost.price)) {
                    exclusiveColumnFlag = true;
                }
                if (ObjectUtils.isEmpty(equipmentCost.specification) && ObjectUtils.isEmpty(equipmentCost.name) && ObjectUtils.isEmpty(equipmentCost.unit)
                    && ObjectUtils.isEmpty(equipmentCost.quantity) && ObjectUtils.isEmpty(equipmentCost.factoryPrice) && ObjectUtils.isEmpty(equipmentCost.price)) {
                    validColumnFlag = true;
                }
            }
            equipmentCostList.push(equipmentCost);
        }
        if (exclusiveColumnFlag) {
            return ResponseData.fail('出厂价与市场价互斥，请移除一列数据！');
        }
        if (validColumnFlag) {
            return ResponseData.fail('当前无有效列，请识别后导入！');
        }

        // 更新国内采购设备费
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).set(this.getDataMapKey(unitId, type), equipmentCostList);

        let allCost = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY).get(this.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF));
        if (ObjectUtils.isEmpty(allCost) || allCost === 0) {
            this.caculatorTotal(constructId, null);
        } else {
            this.caculatorSumTotal(constructId, null, allCost);
        }

        return ResponseData.success(equipmentCostList);
    }


    /**
     * 载入进口设备计算器模板
     * @param args
     */
    async importCaculator(args) {
        let {constructId} = args;

        const jsqtfTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\进口设备计算器';
        let options = {
            properties: ['openFile'],
            defaultPath: jsqtfTemplatePath, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_GZFJSQ]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            return ResponseData.fail('未选中任何文件');
        }

        // 建设其他费文件的路径
        const qtfFilePath = result[0];
        try {
            const data = fs.readFileSync(qtfFilePath, 'utf8');
            // 使用JSON.parse()方法将JSON字符串转换为JavaScript数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段  所有行“序号”、“费用代号”、“费用名称”、“取费基数”、“费率”、“费用类型”、“备注”
            const caculatorArray = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.sequenceNbr = Snowflake.nextId();
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.costBase = line.costBase;
                obj.rates = line.rates;
                obj.price = line.price;  // 备份默认值
                obj.costType = line.costType;
                obj.remark = line.remark;
                obj.jsonStr = line.jsonStr;
                caculatorArray.push(obj);
            });
            // 保存设备进口计算器
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_JSQ, caculatorArray);
            return ResponseData.success(caculatorArray);
        } catch (err) {
            return ResponseData.fail('导入失败');
        }
    }


    /**
     * 保存进口设备计算器模板
     * @param args
     */
    async exportCaculator(args) {
        let {constructId, caculatorArray} = args;

        // 获取工程项目的建设其他费
        if (ObjectUtils.isEmpty(caculatorArray)) {
            caculatorArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_JSQ);
        }

        // 指定导出的列名
        const columns = ["dispNo", "code", "name", "costBase", "rates", "price", "costType", "remark", "jsonStr"];
        // 根据指定的列名来重组数据，确保导出的JSON只包含这些列
        const formattedData = caculatorArray.map(item => {
            return columns.reduce((acc, col) => {
                acc[col] = item[col];
                return acc;
            }, {});
        });
        // 将数组转换为JSON字符串
        const jsonData = JSON.stringify(formattedData); // 第三个参数是缩进量，使输出更易读

        // 存放费用汇总文件的路径
        const jsqtfTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\进口设备计算器';
        const count = await this.countDirectories(jsqtfTemplatePath);
        let options = {
            title: '保存文件',
            defaultPath: jsqtfTemplatePath + '\\进口设备计算器模板' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_GZFJSQ]} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(FileOperatorType.File_TYPE_GZFJSQ)) {
                filePath += FileOperatorType.File_TYPE_GZFJSQ;
            }
            // 写入文件
            fs.writeFile(filePath, jsonData, (err) => {
                if (err) {
                    ResponseData.fail('写入文件时发生错误');
                } else {
                    console.log('数据已成功导出到 ${filePath}');
                    ResponseData.success('数据已成功导出');
                }
            });
            return ResponseData.success(filePath);
        }
    }

    async recalculatePrecisionSbgzf(constructId, unit, precision) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let precision1 = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(projectDomain.getRoot().sequenceNbr);


    }

}

GsEquipmentCostsService.toString = () => '[class GsEquipmentCostsService]';
module.exports = GsEquipmentCostsService;
