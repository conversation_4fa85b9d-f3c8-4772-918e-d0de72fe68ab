const {Service} = require("../../../core");
const {GSAnZhuangFeeRate} = require("../models/GSAnZhuangFee");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const AZFeeConstants = require("../constants/AZFeeConstants");
const UnitUtils = require("../core/tools/UnitUtils");
const {GsBaseDe} = require("../models/GsBaseDe");
const ProjectDomain = require("../domains/ProjectDomain");
const DeTypeConstants = require("../constants/DeTypeConstants");
const PropertyUtil = require("../domains/utils/PropertyUtil");
const ResourceKindConstants = require("../constants/ResourceKindConstants");
const {Snowflake} = require("../utils/Snowflake");
const StandardDeModel = require("../domains/deProcessor/models/StandardDeModel");
const ResourceModel = require("../domains/deProcessor/models/ResourceModel");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {FBCalculator} = require("../domains/calculators/de/FBCalculator");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const CommonConstants = require("../constants/CommonConstants");
const AnZhuangJiQqConstants = require("../constants/AnZhuangJiQqConstants");
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const BranchProjectDisplayConstant = require("../constants/BranchProjectDisplayConstant");
const WildcardMap = require("../core/container/WildcardMap");
const RcjLevelMarkConstant = require("../../../electron/enum/RcjLevelMarkConstant");

/**
 *
 */
class gsAZService extends Service
{

    static LIBRARY_CODE = "2018-AZGC-GS";
    static rangeSplitor = "-";
    static positionArray = [{"positionName":"对应分部","positionCode":0},{"positionName":"指定分部","positionCde":1}];
    static AZ_UNIT = "元";
    /**
     * 费用类型过滤方法集合
     * @type {{超高费: function(*, *): void, 垂直运输费: function(*, *): void, 操作高度增加费: function(*, *): void, 脚手架搭拆费: function(*, *): void, 系统调整费: function(*, *): void}}
     */
    static feeNameActions = {
        "超高费": (layer,height,rate,rateResultList) => {
            let rateHeightRange = rate.heightRange;
            let rateLayerRange = rate.layerRange;
            if(layer == 0 && height == 0)
            {
                rateResultList.push(rate);
            }
            else
            {
                //层
                if (!ObjectUtils.isEmpty(rateHeightRange) && (height !== 0 || ObjectUtils.isEmpty(height)) ){
                    let item = rateHeightRange.split(gsAZService.rangeSplitor);
                    if (NumberUtil.isBetween(height, parseInt(item[0]), parseInt(item[1]))) {
                        rateResultList.push(rate);
                    }
                }
                //高度
                if (!ObjectUtils.isEmpty(rateLayerRange)  && (layer !== 0 || ObjectUtils.isEmpty(layer) )){
                    let item1 = rateLayerRange.split(gsAZService.rangeSplitor);
                    if (NumberUtil.isBetween(layer, parseInt(item1[0]), parseInt(item1[1]))) {
                        rateResultList.push(rate);

                    }
                }
            }
        },
        "操作高度增加费": (layer,height,rate,rateResultList) => {
            gsAZService.layerRangeCompare(rate,rateResultList);
        },
        "垂直运输费": (layer,height,rate,rateResultList) => {
            gsAZService.layerRangeCompare(rate,rateResultList);
        },
        "脚手架搭拆费": (layer,height,rate,rateResultList) => {
            rateResultList.push(rate);
        },
        "系统调整费": (layer,height,rate,rateResultList) => {
            rateResultList.push(rate);
        }
    }
    constructor(ctx) {
        super(ctx);
        this.gsAnZhuangFeeRateDao = this.app.db.PreliminaryEstimate.manager.getRepository(GSAnZhuangFeeRate);
        this.gsBaseDeDao = this.app.db.PreliminaryEstimate.manager.getRepository(GsBaseDe);
    }

    /**
     *
     * @param libCode
     * @returns {Promise<*[]>}
     */
    async getAZRateDics(libCode,args)
    {

        let systemDefault = ObjectUtils.isEmpty(args.systemDefault) ? false : args.systemDefault;       //是否恢复系统默认

        // 使用GROUP BY进行分组求和
        let feeNameGroupSQL = " SELECT fy_Name as  feeName FROM gs_anzhuang_fy_calculate where fee_code ='' GROUP BY feeName order by CAST(sequence_nbr AS SIGNED INTEGER) asc ";
        const groupByQuery = this.app.gsSqlite3DataSource.prepare(feeNameGroupSQL);
        const groupResults = groupByQuery.all();
        let dicts = [];
        let sortNo = 0;
        let azCacheAllData = await this.service.PreliminaryEstimate.gsAZservice.getCachesType(args.constructId, args.unitId,AnZhuangJiQqConstants.AZ_JIQU_TYPE1);

        for(let result of groupResults)
        {
            let feeItem = {};
            feeItem.feeName = result.feeName;
            feeItem.sortNo = sortNo;
            feeItem.type = 0;
            feeItem.feeItems = [];

            if (ObjectUtils.isNotEmpty(azCacheAllData)) {
                let cacheChecked = azCacheAllData.data.find(p => p.feeName === feeItem.feeName && p.checked === true);
                if (ObjectUtils.isNotEmpty(cacheChecked)) {
                    feeItem.checked = true;
                    feeItem.type = cacheChecked.type;
                    feeItem.relationListId = cacheChecked.relationListId;
                }
            }

            let classlevelGroupSQL = " select classlevel,chapter,MIN(CAST(sequence_nbr AS SIGNED INTEGER)) AS sequence_nbr FROM gs_anzhuang_fy_calculate where fee_code = '' and  fy_Name  = '"+ result.feeName + "' group by classlevel ORDER BY sequence_nbr ASC";
            const classlevelResults = this.app.gsSqlite3DataSource.prepare(classlevelGroupSQL);
            const levelResults = classlevelResults.all();
            let i = 0;
            for(let classlevelResult of levelResults)
            {
                let  classlevelObject = {};
                feeItem.feeItems.push(classlevelObject);
                classlevelObject.classlevel1Name = "安装工程";
                classlevelObject.classlevelName = classlevelResult.chapter;

                let deListSql = await this.gsAnZhuangFeeRateDao.find({
                    where: {feeName:  result.feeName,
                        classLevel:classlevelResult.classlevel}
                });
                classlevelObject.deList = deListSql.sort((a, b) => Number(a.sequenceNbr) - Number(b.sequenceNbr));

                if (systemDefault) {
                    //恢复系统默认则选中第一条
                    classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                    classlevelObject.defaultRow = classlevelObject.deList[0];
                } else {
                    if ((args.layer === 0 && args.height === 0)) {
                        //用户未输入值，和之前逻辑一致默认选中第一条数据
                        classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                        classlevelObject.defaultRow = classlevelObject.deList[0];
                        if (ObjectUtils.isNotEmpty(azCacheAllData)) {
                            //如果缓存都数据则取缓存
                            let find = azCacheAllData.data.find(o => o.feeName === feeItem.feeName);
                            if (ObjectUtils.isNotEmpty(find)) {
                                let feeItem1 = find.feeItems[i];
                                if (ObjectUtils.isNotEmpty(feeItem1) && ObjectUtils.isNotEmpty(feeItem1.isDefault) && ObjectUtils.isNotEmpty(feeItem1.defaultRow)) {
                                    classlevelObject.isDefault = feeItem1.isDefault;
                                    classlevelObject.defaultRow = feeItem1.defaultRow;
                                }
                                i++;
                            }
                        }
                    } else {
                        if (feeItem.feeName === "超高费") {
                            for (let i = 0; i < classlevelObject.deList.length; i++) {
                                let item = classlevelObject.deList[i];
                                if (args.layer > 0) {
                                    if (await this.filterLayerHeight(item, args.layer, item.layerRange, "layer")) {
                                        classlevelObject.isDefault = item.sequenceNbr;
                                        classlevelObject.defaultRow = item;
                                    }
                                } else if (args.height > 0) {
                                    if (await this.filterLayerHeight(item, args.height, item.heightRange, "height")) {
                                        classlevelObject.isDefault = item.sequenceNbr;
                                        classlevelObject.defaultRow = item;
                                    }
                                }
                            }
                        } else {
                            classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                            classlevelObject.defaultRow = classlevelObject.deList[0];
                        }
                    }
                }

            }
            dicts.push(feeItem);
            sortNo++;
        }
        return dicts;
    }


    /**
     *
     * @param libCode
     * @returns {Promise<*[]>}
     */
    async getAZRateDicsV2(libCode, args) {

        let systemDefault = ObjectUtils.isEmpty(args.systemDefault) ? false : args.systemDefault;       //是否恢复系统默认

        // 使用GROUP BY进行分组求和
        let feeNameGroupSQL = " SELECT fy_Name as  feeName FROM gs_anzhuang_fy_calculate where fee_code !='' GROUP BY feeName order by CAST(sequence_nbr AS SIGNED INTEGER) asc ";
        const groupByQuery = this.app.gsSqlite3DataSource.prepare(feeNameGroupSQL);
        const groupResults = groupByQuery.all();
        let dicts = [];
        let sortNo = 0;
        let azCacheAllData = await this.service.PreliminaryEstimate.gsAZservice.getCachesType(args.constructId, args.unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE2);

        for (let result of groupResults) {
            let feeItem = {};
            feeItem.feeName = result.feeName;
            feeItem.sortNo = sortNo;
            feeItem.type = 0;
            feeItem.feeItems = [];

            if (ObjectUtils.isNotEmpty(azCacheAllData)) {
                let cacheChecked = azCacheAllData.data.find(p => p.feeName === feeItem.feeName && p.checked === true);
                if (ObjectUtils.isNotEmpty(cacheChecked)) {
                    feeItem.checked = true;
                    feeItem.type = cacheChecked.type;
                    feeItem.relationListId = cacheChecked.relationListId;
                }
            }

            let classlevelGroupSQL = " select classlevel,chapter,MIN(CAST(sequence_nbr AS SIGNED INTEGER)) AS sequence_nbr FROM gs_anzhuang_fy_calculate where fee_code != '' and  fy_Name  = '" + result.feeName + "' group by classlevel ORDER BY sequence_nbr ASC";
            const classlevelResults = this.app.gsSqlite3DataSource.prepare(classlevelGroupSQL);
            const levelResults = classlevelResults.all();
            let i = 0;
            for (let classlevelResult of levelResults) {
                let classlevelObject = {};
                feeItem.feeItems.push(classlevelObject);
                classlevelObject.classlevel1Name = "安装工程";
                classlevelObject.classlevelName = classlevelResult.chapter;

                let deListSql = await this.gsAnZhuangFeeRateDao.find({
                    where: {
                        feeName: result.feeName,
                        classLevel: classlevelResult.classlevel
                    }
                });
                classlevelObject.deList = deListSql.sort((a, b) => Number(a.sequenceNbr) - Number(b.sequenceNbr));

                if (systemDefault) {
                    //恢复系统默认则选中第一条
                    classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                    classlevelObject.defaultRow = classlevelObject.deList[0];
                } else {
                    if ((args.layer === 0 && args.height === 0)) {
                        //用户未输入值，和之前逻辑一致默认选中最后一条数据
                        classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                        classlevelObject.defaultRow = classlevelObject.deList[0];
                        if (ObjectUtils.isNotEmpty(azCacheAllData)) {
                            //如果缓存都数据则取缓存
                            let find = azCacheAllData.data.find(o => o.feeName === feeItem.feeName);
                            if (ObjectUtils.isNotEmpty(find)) {
                                let feeItem1 = find.feeItems[i];
                                if (ObjectUtils.isNotEmpty(feeItem1) && ObjectUtils.isNotEmpty(feeItem1.isDefault) && ObjectUtils.isNotEmpty(feeItem1.defaultRow)) {
                                    classlevelObject.isDefault = feeItem1.isDefault;
                                    classlevelObject.defaultRow = feeItem1.defaultRow;
                                }
                                i++;
                            }
                        }
                    } else {
                        if (feeItem.feeName === "超高费") {
                            for (let i = 0; i < classlevelObject.deList.length; i++) {
                                let item = classlevelObject.deList[i];
                                if (args.layer > 0) {
                                    if (await this.filterLayerHeight(item, args.layer, item.layerRange, "layer")) {
                                        classlevelObject.isDefault = item.sequenceNbr;
                                        classlevelObject.defaultRow = item;
                                    }
                                } else if (args.height > 0) {
                                    if (await this.filterLayerHeight(item, args.height, item.heightRange, "height")) {
                                        classlevelObject.isDefault = item.sequenceNbr;
                                        classlevelObject.defaultRow = item;
                                    }
                                }
                            }
                        } else {
                            classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                            classlevelObject.defaultRow = classlevelObject.deList[0];
                        }
                    }
                }

            }
            dicts.push(feeItem);
            sortNo++;
        }
        return dicts;
    }

    /**
     *
     * @param classlevelParam
     * @returns {Promise<*[]>}
     */
    async getAZRateDicsAlone(classlevelParam, args) {

        let systemDefault = ObjectUtils.isEmpty(args.systemDefault) ? false : args.systemDefault;       //是否恢复系统默认

        let result = {};
        result.layer = 0;
        result.height = 0;
        result.deRowId = args.deRowId;

        // 使用GROUP BY进行分组求和
        let feeNameGroupSQL = "SELECT fy_Name as  feeName FROM gs_anzhuang_fy_calculate where fee_code ='' GROUP BY feeName order by CAST(sequence_nbr AS SIGNED INTEGER) asc ";
        const groupByQuery = this.app.gsSqlite3DataSource.prepare(feeNameGroupSQL);
        const groupResults = groupByQuery.all();
        let dicts = [];
        let sortNo = 0;

        let azCacheAllData = await this.service.PreliminaryEstimate.gsAZservice.getCachesAlone(args.constructId, args.unitId, args.deRowId);
        let checkedRow = null;
        if (ObjectUtils.isNotEmpty(azCacheAllData)) {
            let checkRowList = azCacheAllData.data.filter(o => o.checked);
            if (ObjectUtils.isNotEmpty(checkRowList)) {
                checkedRow = checkRowList[0];
            }

            result.layer = azCacheAllData.layer;
            result.height = azCacheAllData.height;
            result.deRowId = azCacheAllData.deRowId;
        }

        for (let result of groupResults) {
            let feeItem = {};
            feeItem.feeName = result.feeName;
            feeItem.sortNo = sortNo;
            feeItem.type = 0;
            feeItem.calculateMethod = AnZhuangJiQqConstants.AnZhuangJiQq_ALONE;
            feeItem.checked = false;    //默认选中那个
            feeItem.feeItems = [];
            if (feeItem.feeName === "超高费") {
                feeItem.checked = true;     //默认选中超高
            }

            if (ObjectUtils.isNotEmpty(checkedRow)) {
                if (checkedRow.feeName === feeItem.feeName) {
                    feeItem.calculateMethod = checkedRow.calculateMethod;    //0:单独  1:全局
                    feeItem.checked = checkedRow.checked;
                } else {
                    feeItem.checked = false;
                }
            }

            let classlevelGroupSQL = " select classlevel,chapter,MIN(CAST(sequence_nbr AS SIGNED INTEGER)) AS sequence_nbr FROM gs_anzhuang_fy_calculate where fee_code = '' and  fy_Name  = '" + result.feeName + "' group by classlevel ORDER BY sequence_nbr ASC";
            const classlevelResults = this.app.gsSqlite3DataSource.prepare(classlevelGroupSQL);
            const levelResults = classlevelResults.all();
            let i = 0;
            for (let classlevelResult of levelResults) {
                let classlevelObject = {};

                if (classlevelParam?.indexOf(classlevelResult.classlevel) < 0) {
                    continue;
                }

                feeItem.feeItems.push(classlevelObject);
                classlevelObject.classlevel1Name = "安装工程";
                classlevelObject.classlevelName = classlevelResult.chapter;
                classlevelObject.deList = await this.gsAnZhuangFeeRateDao.find({
                    where: {
                        feeName: result.feeName,
                        classLevel: classlevelResult.classlevel
                    }
                });

                if (systemDefault) {
                    //恢复系统默认则选中第一条
                    classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                    classlevelObject.defaultRow = classlevelObject.deList[0];
                } else {
                    if ((args.layer === 0 && args.height === 0)) {
                        //用户未输入值，和之前逻辑一致默认选中最后一条数据
                        classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                        classlevelObject.defaultRow = classlevelObject.deList[0];
                        if (ObjectUtils.isNotEmpty(azCacheAllData)) {
                            //如果缓存都数据则取缓存
                            let find = azCacheAllData.data.find(o => o.feeName === feeItem.feeName);
                            if (ObjectUtils.isNotEmpty(find)) {
                                let feeItem1 = find.feeItems[i];
                                if (ObjectUtils.isNotEmpty(feeItem1) && ObjectUtils.isNotEmpty(feeItem1.isDefault) && ObjectUtils.isNotEmpty(feeItem1.defaultRow)) {
                                    classlevelObject.isDefault = feeItem1.isDefault;
                                    classlevelObject.defaultRow = feeItem1.defaultRow;
                                }
                                i++;
                            }
                        }
                    } else {
                        if (feeItem.feeName === "超高费") {
                            for (let i = 0; i < classlevelObject.deList.length; i++) {
                                let item = classlevelObject.deList[i];
                                if (args.layer > 0) {
                                    if (await this.filterLayerHeight(item, args.layer, item.layerRange, "layer")) {
                                        classlevelObject.isDefault = item.sequenceNbr;
                                        classlevelObject.defaultRow = item;
                                    }
                                } else if (args.height > 0) {
                                    if (await this.filterLayerHeight(item, args.height, item.heightRange, "height")) {
                                        classlevelObject.isDefault = item.sequenceNbr;
                                        classlevelObject.defaultRow = item;
                                    }
                                }
                            }
                        } else {
                            classlevelObject.isDefault = classlevelObject.deList[0].sequenceNbr;
                            classlevelObject.defaultRow = classlevelObject.deList[0];
                        }
                    }
                }

            }
            dicts.push(feeItem);
            sortNo++;
        }
        result.dicts = dicts;
        return result;
    }


    async filterLayerHeight(item, param, range, type) {
        if (range.includes("-")) {
            let rangeList = range.split("-");
            let range1 = parseInt(rangeList[0]);
            let range2 = parseInt(rangeList[1]);
            if (type === "layer") {
                return param >= range1 && param <= range2;
            } else if (type === "height") {
                return param > range1 && param <= range2;
            }
        } else {
            let rangeList = range.replace("大于", "");
            return param > parseInt(rangeList);
        }
    }


    /**
     *
     * @param feeName
     * @returns {Promise<*>}
     */
    async getDetailsByName(feeName)
    {
        return await this.gsAnZhuangFeeRateDao.find({
            where: {feeName: feeName}
        });
    }

    /**
     *
     * @param feeName
     * @param layer
     * @param height
     * @returns {Promise<*[]>}
     */
    async getDetailsByNameAndFeeType(feeName,layer,height)
    {
        let detailList = await this.getDetailsByName(feeName);
        let rateResultList = [];
        for (let rate of detailList) {

            if (gsAZService.feeNameActions[feeName]) {
                gsAZService.feeNameActions[feeName](layer,height,rate,rateResultList);
            }
        }

        if (!ObjectUtils.isEmpty(rateResultList)){
            rateResultList.forEach(k =>{
                k. isDefault = 0;
            });
            const maxObject = rateResultList.reduce((max, obj) => {
                return obj.sequenceNbr > max.sequenceNbr ? obj : max;
            });
            maxObject.isDefault = 1;
        }
        return rateResultList;
    }

    /**
     *
     * @param layer
     * @param rate
     * @param rateDetailList
     */
    static layerRangeCompare(layer,rate,rateDetailList)
    {
        let layerRange = rate.layerRange;
        if(ObjectUtils.isEmpty(layer) || layer == 0 )
        {
            rateDetailList.push(rate);
        }
        else if(layerRange.indexOf(AZFeeConstants.AZFEE_KEY_WORDS_G))
        {
            let layerNbr =  UnitUtils.removeCharter(layerRange);
            if(layer > layerNbr)
            {
                rateDetailList.push(rate);
            }
        }
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param feeName
     * @param azType
     * @param type
     * @returns {Promise<{chapterList: [...string[],...*], deTree: (*|unknown[])}>}
     */
    async chapterDropDownBox(constructId,unitId,feeName,azType,type)
    {

        if (type === AnZhuangJiQqConstants.AZ_JIQU_TYPE2) {
            //查询预算定额
            return await this.chapterDropDownBoxYS(constructId, unitId, feeName, azType);
        }

        let professionList = ['全部专业','安装专业'];
        let anZhuangRateList = await this.gsAnZhuangFeeRateDao.find({
            where: {feeName: feeName }
        });
        anZhuangRateList = anZhuangRateList.filter(o=>ObjectUtils.isEmpty(o.feeCode));
        let typeDeList = [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_USER_DE];
        let typeFbList = [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DEFAULT];
        let deRowList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && (typeFbList.includes(item.type) || (typeDeList.includes(item.type) && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE)));
        //把费率项转为map 以方便过滤定额
        let rateMap = PropertyUtil.groupBy(anZhuangRateList,"classLevel");
        let rateKeys = [...rateMap.keys()];
        //章节下拉框
        let chapterList = ["全部章节"];
        let resultDeRowList = this.getDeRowsByRate(constructId,unitId,rateKeys,deRowList);
        let archList = [];

        let baseDeList = [];
        let anCache = await this.service.PreliminaryEstimate.gsAZservice.getCachesType(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE1);
        if (ObjectUtils.isNotEmpty(anCache)) {
            if (ObjectUtils.isNotEmpty(anCache) && ObjectUtils.isNotEmpty(anCache.data)) {
                let anFeeData = anCache.data.find(o => o.feeName === feeName);
                if (ObjectUtils.isNotEmpty(anFeeData)) {
                    baseDeList = anFeeData.baseDeList;
                }
            }
        }

        //获取到分部分项下所有的定额主键
        for(let deRow03 of resultDeRowList)
        {
            archList.push(deRow03);
            gsAZService._joinParent(deRow03,archList,deRowList);
        }
        archList = PropertyUtil.uniqueByProperty(archList, "sequenceNbr");
        chapterList = [...chapterList, ...PropertyUtil.groupBy(archList, "classlevel02").keys()];

        archList.forEach(p=>{
            p.checked = ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(p.sequenceNbr);
        })

        //查询单独计取过的定额
        let filter = archList.filter(p => ObjectUtils.isNotEmpty(p.calculateMethod) && p.calculateMethod === 0);
        let notList = [];
        if (ObjectUtils.isNotEmpty(filter)) {
            filter.forEach(o => {
                let feeNameDeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.parentId === o.sequenceNbr
                    && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && item.deName.includes(feeName));
                if (ObjectUtils.isNotEmpty(feeNameDeList)) {
                    notList.push(o.sequenceNbr);
                }
            })
        }
        return {"chapterList":chapterList,
                "deTree":archList,
                "notDeList":notList};

    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param feeName
     * @param azType
     * @returns {Promise<{chapterList: [...string[],...*], deTree: (*|unknown[])}>}
     */
    async chapterDropDownBoxYS(constructId, unitId, feeName, azType) {
        let professionList = ['全部专业', '安装专业'];
        let anZhuangRateList = await this.gsAnZhuangFeeRateDao.find({
            where: {feeName: feeName}
        });
        anZhuangRateList = anZhuangRateList.filter(o=>ObjectUtils.isNotEmpty(o.feeCode));
        let typeDeList = [DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_USER_DE];
        let typeFbList = [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DEFAULT];
        let deRowList1 = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && (typeFbList.includes(item.type) || (typeDeList.includes(item.type) && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE)));
        let deRowList = [];
        if (ObjectUtils.isNotEmpty(deRowList1)) {
            deRowList1.forEach(o => {
                let parentDe = ProjectDomain.getDomain(constructId).deDomain.getDeById(o.parentId);
                if (ObjectUtils.isEmpty(parentDe) || parentDe.type !== DeTypeConstants.DE_TYPE_DELIST) {
                    deRowList.push(o);  //预算安装作为子定额时不参与计算
                }
            });
        }

        //把费率项转为map 以方便过滤定额
        let rateMap = PropertyUtil.groupBy(anZhuangRateList, "classLevel");
        let rateKeys = [...rateMap.keys()];
        //章节下拉框
        let chapterList = ["全部章节"];

        let resultDeRowList = this.getDeRowsByRateYS(constructId, unitId, rateKeys, deRowList, anZhuangRateList);
        let archList = [];

        let baseDeList = [];
        let anCache = await this.service.PreliminaryEstimate.gsAZservice.getCachesYS(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE2);
        if (ObjectUtils.isNotEmpty(anCache)) {
            if (ObjectUtils.isNotEmpty(anCache) && ObjectUtils.isNotEmpty(anCache.data)) {
                let anFeeData = anCache.data.find(o => o.feeName === feeName);
                if (ObjectUtils.isNotEmpty(anFeeData)) {
                    baseDeList = anFeeData.baseDeList;
                }
            }
        }

        //获取到分部分项下所有的定额主键
        for (let deRow03 of resultDeRowList) {
            archList.push(deRow03);
            gsAZService._joinParent(deRow03, archList, deRowList);
        }
        archList = PropertyUtil.uniqueByProperty(archList, "sequenceNbr");
        chapterList = [...chapterList, ...PropertyUtil.groupBy(archList, "classlevel02").keys()];

        archList.forEach(p => {
            p.checked = ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(p.sequenceNbr);
        })

        //查询单独计取过的定额
        let filter = archList.filter(p => ObjectUtils.isNotEmpty(p.calculateMethod) && p.calculateMethod === 0);
        let notList = [];
        if (ObjectUtils.isNotEmpty(filter)) {
            filter.forEach(o => {
                notList.push(o.sequenceNbr);
            })
        }
        return {
            "chapterList": chapterList,
            "deTree": archList,
            "notDeList": notList
        };

    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param rateKeys
     * @param deRowList
     * @returns {*[]}
     */
    getDeRowsByRate(constructId,unitId,rateKeys,deRowList)
    {


        let resultDeRowList = [];
        for(let deRow of deRowList)
        {
            if(deRow.type === DeTypeConstants.DE_TYPE_DELIST)
            {
                for(let rateKey of rateKeys)
                {
                    //如果存在于当前feeName对应的章节内
                    if(deRow.classlevel02?.indexOf(rateKey) >= 0)
                    {
                        resultDeRowList.push(deRow);
                    }
                }
            }

            let deAllData = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRow.deRowId);
            if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE)
            {
                for(let rateKey of rateKeys)
                {
                    //如果存在于当前feeName对应的章节内
                    if(deAllData.classifyLevel2?.indexOf(rateKey) >= 0)
                    {
                        resultDeRowList.push(deRow);
                    }
                }
            }

        }
        return resultDeRowList;
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param rateKeys
     * @param deRowList
     * @returns {*[]}
     */
    getDeRowsByRateYS(constructId,unitId,rateKeys,deRowList,anZhuangRateList)
    {


        let resultDeRowList = [];
        for(let deRow of deRowList)
        {
            // if(deRow.type === DeTypeConstants.DE_TYPE_DELIST)
            // {
            //     for(let rateKey of rateKeys)
            //     {
            //         //如果存在于当前feeName对应的章节内
            //         if(deRow.classlevel02?.indexOf(rateKey) >= 0)
            //         {
            //             resultDeRowList.push(deRow);
            //         }
            //     }
            // }
            let deAllData = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRow.deRowId);

            if(deRow.type === DeTypeConstants.DE_TYPE_DE && deRow.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE)
            {
                for(let rateKey of rateKeys)
                {
                    let chapter = anZhuangRateList.find(o=>o.classLevel === rateKey).chapter;
                    let zjList = NumberUtil.numToChByBusiness(chapter);
                    const keys = zjList.map(obj => Object.keys(obj)[0]);

                    //如果存在于当前feeName对应的章节内
                    if (deRow.classlevel01?.indexOf(rateKey) >= 0) {
                        keys.forEach(o => {
                            if (deRow.classlevel02.includes(o)) {               //判断章
                                const valueArray = zjList.find(obj => obj.hasOwnProperty(o))[o];
                                if (ObjectUtils.isNotEmpty(valueArray)) {       //有节则判断节，无节则整个章
                                    valueArray.forEach(p => {
                                        if (deAllData.classlevel03.includes(p)) {
                                            resultDeRowList.push(deRow);
                                        }
                                    });
                                } else {
                                    resultDeRowList.push(deRow);
                                }
                            }
                        });
                    }
                }
            }

            if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE)
            {
                for(let rateKey of rateKeys)
                {
                    //如果存在于当前feeName对应的章节内
                    if(deAllData.classifyLevel2?.indexOf(rateKey) >= 0)
                    {
                        resultDeRowList.push(deRow);
                    }
                }
            }

        }
        return resultDeRowList;
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param rateKeys
     * @param deRowList
     * @param baseDeList
     * @returns {*[]}
     */
    getDeRowsByRate1(constructId,unitId,rateKeys,deRowList,baseDeList)
    {


        let resultDeRowList = [];
        for(let deRow of deRowList)
        {
            if(deRow.type === DeTypeConstants.DE_TYPE_DELIST )
            {
                for(let rateKey of rateKeys)
                {
                    //如果存在于当前feeName对应的章节内
                    if(deRow.classlevel02?.indexOf(rateKey) >= 0)
                    {
                        if (ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(deRow.sequenceNbr)) {
                            resultDeRowList.push(deRow);
                        }
                    }
                }
            }

            let deAllData = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRow.deRowId);
            if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE )
            {
                for(let rateKey of rateKeys)
                {
                    //如果存在于当前feeName对应的章节内
                    if(deAllData.classifyLevel2?.indexOf(rateKey) >= 0)
                    {
                        if (ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(deRow.sequenceNbr)) {
                            resultDeRowList.push(deRow);
                        }
                    }
                }
            }
        }
        return resultDeRowList;
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param rateKeys
     * @param deRowList
     * @param baseDeList
     * @returns {*[]}
     */
    getDeRowsByRate1YS(constructId,unitId,rateKeys,deRowList,baseDeList, classlevelName)
    {

        let zjList = NumberUtil.numToChByBusiness(classlevelName);
        const keys = zjList.map(obj => Object.keys(obj)[0]);

        let resultDeRowList = [];
        for(let deRow of deRowList)
        {
            // if(deRow.type === DeTypeConstants.DE_TYPE_DELIST )
            // {
            //     for(let rateKey of rateKeys)
            //     {
            //         //如果存在于当前feeName对应的章节内
            //         if(deRow.classlevel02?.indexOf(rateKey) >= 0)
            //         {
            //             if (ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(deRow.sequenceNbr)) {
            //                 resultDeRowList.push(deRow);
            //             }
            //         }
            //     }
            // }
            let deAllData = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRow.deRowId);

            if (deRow.type === DeTypeConstants.DE_TYPE_DE) {
                for (let rateKey of rateKeys) {
                    //如果存在于当前feeName对应的章节内
                    if (deRow.classlevel01?.indexOf(rateKey) >= 0)       //判断册子
                    {
                        keys.forEach(o => {
                            if (deRow.classlevel02.includes(o)) {               //判断章
                                const valueArray = zjList.find(obj => obj.hasOwnProperty(o))[o];
                                if (ObjectUtils.isNotEmpty(valueArray)) {       //有节则判断节，无节则整个章
                                    valueArray.forEach(p => {
                                        if (deAllData.classlevel03.includes(p)) {
                                            if (ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(deRow.sequenceNbr)) {
                                                resultDeRowList.push(deRow);
                                            }
                                        }
                                    })

                                } else {
                                    if (ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(deRow.sequenceNbr)) {
                                        resultDeRowList.push(deRow);
                                    }
                                }
                            }
                        });
                    }
                }
            }



            if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE )
            {
                for(let rateKey of rateKeys)
                {
                    //如果存在于当前feeName对应的章节内
                    if(deAllData.classifyLevel2?.indexOf(rateKey) >= 0)
                    {
                        if (ObjectUtils.isNotEmpty(baseDeList) && baseDeList.includes(deRow.sequenceNbr)) {
                            resultDeRowList.push(deRow);
                        }
                    }
                }
            }
        }
        return resultDeRowList;
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param rateKeys
     * @param deRowList
     * @returns {*[]}
     */
    getDeRowsByRateAlone(constructId,unitId,rateKeys,deRowList)
    {
    let deRowListCopy = [];
    deRowListCopy.push(deRowList[0]);

        let resultDeRowList = [];
        for(let deRow of deRowListCopy)
        {

            let classifyLevelDe = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deRow.deRowId);

            if(classifyLevelDe.type === DeTypeConstants.DE_TYPE_DELIST || classifyLevelDe.type === DeTypeConstants.DE_TYPE_DE)
            {
                for(let rateKey of rateKeys)
                {
                    //如果存在于当前feeName对应的章节内
                    if(deRow.classlevel02?.indexOf(rateKey) >= 0)
                    {
                        resultDeRowList.push(deRow);
                    }
                }
            }

            if(classifyLevelDe.type === DeTypeConstants.DE_TYPE_USER_DE){
                for(let rateKey of rateKeys){
                    if(classifyLevelDe.classifyLevel2?.indexOf(rateKey) >= 0){
                        resultDeRowList.push(deRow);
                    }
                }
            }

        }
        return resultDeRowList;
    }

    static _joinParent(row,archList,deRowList)
    {
        if(row.parentId != 0)
        {
            let parentNode = deRowList.find(item => item.sequenceNbr === row.parentId )
            archList.push(parentNode);
            gsAZService._joinParent(parentNode,archList,deRowList);

        } else {
            let unit = ProjectDomain.getDomain(row.constructId).getProjectById(row.unitId);
            row.deName = unit.name;
        }
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @returns {{}}
     */
    initCache(constructId,unitId,data,layer,height)
    {
        let cache = {};
        cache.constructId = constructId;
        cache.unitId = unitId;
        cache.data = data;
        cache.layer = layer;
        cache.height = height;
        //cache放入缓存
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE +  constructId + unitId,cache);
        return cache;
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @returns {{}}
     */
    initCacheYS(constructId,unitId,data,layer,height)
    {
        let cache = {};
        cache.constructId = constructId;
        cache.unitId = unitId;
        cache.data = data;
        cache.layer = layer;
        cache.height = height;
        //cache放入缓存
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE_YS +  constructId + unitId,cache);
        return cache;
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @param deRowId
     * @returns {{}}
     */
    initCacheAlone(constructId,unitId,deRowId,data,layer,height)
    {
        let cache = {};
        cache.constructId = constructId;
        cache.unitId = unitId;
        cache.deRowId = deRowId;
        cache.data = data;
        cache.layer = layer;
        cache.height = height;
        //cache放入缓存
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE +  constructId + unitId + deRowId,cache);
        return cache;
    }



    async calcuateClassLevelSort(){
        let classlevelGroupSQL = " select classlevel,MIN(CAST(sequence_nbr AS SIGNED INTEGER)) AS sequence_nbr FROM gs_anzhuang_fy_calculate WHERE fee_code = ''  group by classlevel ORDER BY sequence_nbr ASC";
        let classlevelResults = this.app.gsSqlite3DataSource.prepare(classlevelGroupSQL);
        let levelResults = classlevelResults.all();

        let map = new Map();
        levelResults.forEach(o => {
            map.set(o.classlevel, o.sequence_nbr*100);
        })

        return map;
    }

    async calcuateClassLevelSortYS(){
        let classlevelGroupSQL = " select classlevel,MIN(CAST(sequence_nbr AS SIGNED INTEGER)) AS sequence_nbr FROM gs_anzhuang_fy_calculate WHERE fee_code != ''  group by classlevel ORDER BY sequence_nbr ASC";
        let classlevelResults = this.app.gsSqlite3DataSource.prepare(classlevelGroupSQL);
        let levelResults = classlevelResults.all();

        let map = new Map();
        levelResults.forEach(o => {
            map.set(o.classlevel, o.sequence_nbr*100);
        })

        return map;
    }


    async dealRelationListId(data) {
        if (ObjectUtils.isNotEmpty(data)) {
            for (let item of data) {
                if (item.type == AnZhuangJiQqConstants.AnZhuangJiQq_ZDFB) {
                    //指定分部
                    if (ObjectUtils.isEmpty(item.relationListId)) {
                        item.type = AnZhuangJiQqConstants.AnZhuangJiQq_DYFB;
                    } else {
                        if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked) {
                            for (let feeItem of item.feeItems) {
                                feeItem.relationListId = item.relationListId;
                                feeItem.defaultRow.relationListId = item.relationListId;
                            }
                        }
                    }
                } else {
                    if (ObjectUtils.isNotEmpty(item.relationListId)) {
                        item.relationListId = null;
                        if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked) {
                            for (let feeItem of item.feeItems) {
                                feeItem.relationListId = null;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @returns {null}
     */
    async calculateAZFee(constructId,unitId,data,layer,height) {
        await this.dealRelationListId(data);
        this.initCache(constructId,unitId,data,layer,height);
        await this.clearFeeDe(constructId,unitId);

        //计算顺序
        let ClassLevelSortMap = await this.calcuateClassLevelSort();
        let linshiDataArray = [];

        let typeList = [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_ANZHUANG_FEE,DeTypeConstants.DE_TYPE_USER_DE];
        let deRowList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && typeList.includes(item.type) && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE);
        if(ObjectUtils.isNotEmpty(data))
        {
            for(let item of data)
            {
                if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked)
                {
                    for(let feeItem of item.feeItems)
                    {
                        if(ObjectUtils.isNotEmpty(feeItem?.defaultRow))
                        {

                            let calBaseDeList = [];
                            //处理baseDeList，为空时默认计取全部；有数据时筛出存在的数据
                            if (ObjectUtils.isNotEmpty(item.baseDeList)) {
                                item.baseDeList.forEach(o => {
                                    let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(o);
                                    if (ObjectUtils.isNotEmpty(de)) {
                                        calBaseDeList.push(de.sequenceNbr);
                                    }
                                });
                                item.baseDeList = calBaseDeList;
                            }else if (ObjectUtils.isEmpty(item.baseDeList)) {
                                let deList = deRowList.filter(p => p.type === DeTypeConstants.DE_TYPE_DELIST || p.type === DeTypeConstants.DE_TYPE_USER_DE);
                                if (ObjectUtils.isNotEmpty(deList)) {
                                    deList.forEach(m=>{
                                        calBaseDeList.push(m.sequenceNbr);
                                    })
                                }
                            }

                            let relatedDeRows = this.getDeRowsByRate1(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList, calBaseDeList);
                            // let relatedDeRows = this.getDeRowsByRate(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList);

                            let relatedDeRowIds = [];
                            if (ObjectUtils.isNotEmpty(relatedDeRows)) {
                                relatedDeRows.forEach(o => {
                                    if (calBaseDeList.includes(o.sequenceNbr)) {
                                        //如果进行了勾选，则只计取勾选的
                                        relatedDeRowIds.push(o);
                                    }
                                });

                                //过滤掉单独计取的定额数据
                                // let cloneAzFeeListDe1 = relatedDeRowIds.filter(oo=>ObjectUtils.isEmpty(oo.calculateMethod) || oo.calculateMethod!==0);
                                let cloneAzFeeListDe1 = [];
                                for (let oo of relatedDeRowIds) {
                                    if (ObjectUtils.isEmpty(oo.calculateMethod) || oo.calculateMethod !== 0) {
                                        //全局计取可以计取
                                        cloneAzFeeListDe1.push(oo);
                                    } else {
                                        //单独计取，分item.feeName
                                        let feeNameDeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(o => o.unitId === unitId && o.parentId === oo.sequenceNbr
                                            && o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && o.deName.includes(item.feeName));
                                        if(ObjectUtils.isEmpty(feeNameDeList)){
                                            cloneAzFeeListDe1.push(oo);
                                        }
                                    }
                                }

                                if(ObjectUtils.isEmpty(cloneAzFeeListDe1)){
                                    continue;
                                }

                                //过滤临时删除定额数据
                                let cloneAzFeeListDe = cloneAzFeeListDe1.filter(oo=>ObjectUtils.isEmpty(oo.isTempRemove) || oo.isTempRemove=== CommonConstants.COMMON_NO);
                                if(ObjectUtils.isEmpty(cloneAzFeeListDe)){
                                    continue;
                                }


                                if (item.type === AnZhuangJiQqConstants.AnZhuangJiQq_DYFB) {
                                    let repeatListNo = [];
                                    for (let deItem of cloneAzFeeListDe) {
                                        if (!repeatListNo.includes(deItem.parentId)) {
                                            let filter = cloneAzFeeListDe.filter(o => o.parentId === deItem.parentId);

                                            let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === deItem.sequenceNbr);
                                            let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                            ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                            let linshiData = {};
                                            linshiData.constructId = constructId;
                                            linshiData.unitId = unitId;
                                            linshiData.type = item.type;
                                            linshiData.feeItem = feeItem;
                                            linshiData.relatedDeRow = relatedDeRow;
                                            let ids = [];
                                            filter.forEach(o => {
                                                ids.push(o.sequenceNbr);
                                            });
                                            linshiData.relatedDeRowIds = ids;
                                            linshiData.sort = newSort;
                                            linshiData.parentId = relatedDeRow.parentId;
                                            linshiDataArray.push(linshiData);

                                            repeatListNo.push(deItem.parentId);
                                        }
                                    }
                                } else {
                                    let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === cloneAzFeeListDe[0].sequenceNbr);
                                    let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                    ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                    let linshiData = {};
                                    linshiData.constructId = constructId;
                                    linshiData.unitId = unitId;
                                    linshiData.type = item.type;
                                    linshiData.feeItem = feeItem;
                                    linshiData.relatedDeRow = relatedDeRow;
                                    let ids = [];
                                    cloneAzFeeListDe.forEach(o => {
                                        ids.push(o.sequenceNbr);
                                    });
                                    linshiData.relatedDeRowIds = ids;
                                    linshiData.sort = newSort;
                                    linshiData.parentId = item.relationListId;
                                    linshiDataArray.push(linshiData);
                                }
                            }
                        }
                    }
                }
            }
        }

        if(ObjectUtils.isNotEmpty(linshiDataArray)){
            let sort = linshiDataArray.sort((a, b) => a.sort - b.sort);
            for (let o of sort) {
                let feeItem = o.feeItem;
                //已经存在且此次计取出来的，删除之前的
                let deRowList1 = deRowList.filter(o => ObjectUtils.isEmpty(o.calculateMethod) && o.deCode === feeItem.defaultRow.deCode);
                if (ObjectUtils.isNotEmpty(deRowList1)) {
                    for (const o of deRowList1) {
                        await ProjectDomain.getDomain(constructId).getDeDomain().removeDeRow(o.deRowId, true);
                    }
                }
                //创建费用定额
                let feeDeRow = this.createAZFeeDe(constructId, unitId, o.type, feeItem.defaultRow, o.relatedDeRow);
                feeDeRow.deCode = feeItem.defaultRow.deCode;
                feeDeRow.deName = feeItem.defaultRow.deName;
                feeDeRow.libraryCode = feeItem.defaultRow.libraryCode;
                feeDeRow.libraryName = feeItem.defaultRow.libraryName;
                feeDeRow.unit = gsAZService.COMMON_AZ_FEE_UNIT;
                feeDeRow.resQty = 1;
                feeDeRow.quantity = 1;
                feeDeRow.unit = gsAZService.AZ_UNIT;
                feeDeRow.costFileCode = AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE;
                feeDeRow.costMajorName = AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE_NAME;
                feeDeRow.remark = AnZhuangJiQqConstants.AZ_JIQU_NAME;

                //创建对应人材机
                this.ftProcess(constructId, unitId, feeItem.defaultRow.defaultFtStyle, feeItem.defaultRow.calculateBase, feeItem.defaultRow.rate, feeItem.defaultRow.rgzb
                    , feeItem.defaultRow.clzb, feeItem.defaultRow.jxzb, o.relatedDeRowIds, feeDeRow);
                await ProjectDomain.getDomain(constructId).deDomain.notify({constructId: constructId, unitId: unitId, deRowId:feeDeRow.sequenceNbr},true);

                try {
                    await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                        constructId: constructId,
                        unitId: unitId,
                        constructMajorType: feeDeRow.libraryCode
                    });
                } catch (error) {
                    console.error("捕获到异常:", error);
                }
            }
        }


        await FBCalculator.getInstance({constructId,unitId}, ProjectDomain.getDomain(constructId).ctx).analyze();
        return "calculateAZFee";
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param deRowId
     * @returns {null}
     */
    async calculateAZFeeAddRcj(constructId, unitId, deRowId) {
        let feeDeRow = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRowId);

        let gsAnZhuangFeeRateDao = this.app.db.PreliminaryEstimate.manager.getRepository(GSAnZhuangFeeRate);
        let feeNameCode = await gsAnZhuangFeeRateDao.findOne({
            where: {
                libraryCode: feeDeRow.libraryCode,
                deCode: feeDeRow.deCode
            }
        });

        let typeList = [DeTypeConstants.DE_TYPE_DELIST, DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB, DeTypeConstants.DE_TYPE_DEFAULT, DeTypeConstants.DE_TYPE_ANZHUANG_FEE, DeTypeConstants.DE_TYPE_USER_DE];
        let deRowList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && typeList.includes(item.type) && item.libraryCode === feeDeRow.libraryCode);
        let calBaseDeList = [];
        let deList = deRowList.filter(p => p.type === DeTypeConstants.DE_TYPE_DELIST || p.type === DeTypeConstants.DE_TYPE_USER_DE);
        if (ObjectUtils.isNotEmpty(deList)) {
            deList.forEach(m => {
                calBaseDeList.push(m.sequenceNbr);
            })
        }

        let relatedDeRows = null;
        if (feeDeRow.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE) {
            relatedDeRows = this.getDeRowsByRate1YS(constructId, unitId, [feeNameCode.classLevel], deRowList, calBaseDeList, feeNameCode.chapter);
        } else if (feeDeRow.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE) {
            relatedDeRows = this.getDeRowsByRate1(constructId, unitId, [feeNameCode.classLevel], deRowList, calBaseDeList);
        }

        let relatedDeRowIds = [];
        if (ObjectUtils.isNotEmpty(relatedDeRows)) {
            relatedDeRows.forEach(o => {
                if (calBaseDeList.includes(o.sequenceNbr)) {
                    //如果进行了勾选，则只计取勾选的
                    relatedDeRowIds.push(o);
                }
            });

            let cloneAzFeeListDe1 = [];
            for (let oo of relatedDeRowIds) {
                if (ObjectUtils.isEmpty(oo.calculateMethod) || oo.calculateMethod !== 0) {
                    //全局计取可以计取
                    cloneAzFeeListDe1.push(oo);
                } else {
                    //单独计取，分item.feeName
                    let feeNameDeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(o => o.unitId === unitId && o.parentId === oo.sequenceNbr
                        && o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && o.deName.includes(feeNameCode.feeName));
                    if (ObjectUtils.isEmpty(feeNameDeList)) {
                        cloneAzFeeListDe1.push(oo);
                    }
                }
            }

            let cloneAzFeeListDe = [];
            if (ObjectUtils.isNotEmpty(cloneAzFeeListDe1)) {
                //过滤临时删除定额数据
                cloneAzFeeListDe = cloneAzFeeListDe1.filter(oo => ObjectUtils.isEmpty(oo.isTempRemove) || oo.isTempRemove === CommonConstants.COMMON_NO);
            }

            let ids = [];
            if (ObjectUtils.isNotEmpty(cloneAzFeeListDe)) {
                cloneAzFeeListDe.forEach(o => {
                    ids.push(o.sequenceNbr);
                });
            }

            //创建对应人材机
            this.ftProcess(constructId, unitId, feeNameCode.defaultFtStyle, feeNameCode.calculateBase, feeNameCode.rate, feeNameCode.rgzb
                , feeNameCode.clzb, feeNameCode.jxzb, ids, feeDeRow);
            await ProjectDomain.getDomain(constructId).deDomain.notify({
                constructId: constructId,
                unitId: unitId,
                deRowId: feeDeRow.sequenceNbr
            }, true);

            try {
                await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    unitId: unitId,
                    constructMajorType: feeDeRow.libraryCode
                });
            } catch (error) {
                console.error("捕获到异常:", error);
            }

        }

        await FBCalculator.getInstance({constructId, unitId}, ProjectDomain.getDomain(constructId).ctx).analyze();
        return "calculateAZFee";
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @returns {null}
     */
    async calculateAZFeeYs(constructId,unitId,data,layer,height) {
        await this.dealRelationListId(data);
        this.initCacheYS(constructId,unitId,data,layer,height);
        await this.clearFeeDeYS(constructId,unitId);

        //计算顺序
        let ClassLevelSortMap = await this.calcuateClassLevelSortYS();
        let linshiDataArray = [];

        let typeList = [DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_USER_DE];
        let deRowList1 = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && typeList.includes(item.type) && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE);

        let deRowList = [];
        if (ObjectUtils.isNotEmpty(deRowList1)) {
            deRowList1.forEach(o => {
                let parentDe = ProjectDomain.getDomain(constructId).deDomain.getDeById(o.parentId);
                if (ObjectUtils.isEmpty(parentDe) || parentDe.type !== DeTypeConstants.DE_TYPE_DELIST) {
                    deRowList.push(o);  //预算安装作为子定额时不参与计算
                }
            });
        }

        if(ObjectUtils.isNotEmpty(data))
        {
            for(let item of data)
            {
                if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked)
                {
                    for(let feeItem of item.feeItems)
                    {
                        if(ObjectUtils.isNotEmpty(feeItem?.defaultRow))
                        {

                            let calBaseDeList = [];
                            //处理baseDeList，为空时默认计取全部；有数据时筛出存在的数据
                            if (ObjectUtils.isNotEmpty(item.baseDeList)) {
                                item.baseDeList.forEach(o => {
                                    let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(o);
                                    if (ObjectUtils.isNotEmpty(de)) {
                                        calBaseDeList.push(de.sequenceNbr);
                                    }
                                });
                                item.baseDeList = calBaseDeList;
                            }else if (ObjectUtils.isEmpty(item.baseDeList)) {
                                let deList = deRowList.filter(p => p.type === DeTypeConstants.DE_TYPE_DELIST || p.type === DeTypeConstants.DE_TYPE_DE || p.type === DeTypeConstants.DE_TYPE_USER_DE);
                                if (ObjectUtils.isNotEmpty(deList)) {
                                    deList.forEach(m=>{
                                        calBaseDeList.push(m.sequenceNbr);
                                    })
                                }
                            }

                            let classlevelName = feeItem.classlevelName;//章节1~6,8~15
                            let relatedDeRows = this.getDeRowsByRate1YS(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList, calBaseDeList, classlevelName);
                            // let relatedDeRows = this.getDeRowsByRate(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList);

                            let relatedDeRowIds = [];
                            if (ObjectUtils.isNotEmpty(relatedDeRows)) {
                                relatedDeRows.forEach(o => {
                                    if (calBaseDeList.includes(o.sequenceNbr)) {
                                        //如果进行了勾选，则只计取勾选的
                                        relatedDeRowIds.push(o);
                                    }
                                });

                                //过滤掉单独计取的定额数据
                                let cloneAzFeeListDe1 = relatedDeRowIds.filter(oo=>ObjectUtils.isEmpty(oo.calculateMethod) || oo.calculateMethod!==0);
                                if(ObjectUtils.isEmpty(cloneAzFeeListDe1)){
                                    continue;
                                }

                                //过滤临时删除定额数据
                                let cloneAzFeeListDe = cloneAzFeeListDe1.filter(oo=>ObjectUtils.isEmpty(oo.isTempRemove) || oo.isTempRemove=== CommonConstants.COMMON_NO);
                                if(ObjectUtils.isEmpty(cloneAzFeeListDe)){
                                    continue;
                                }


                                if (item.type === AnZhuangJiQqConstants.AnZhuangJiQq_DYFB) {
                                    let repeatListNo = [];
                                    for (let deItem of cloneAzFeeListDe) {
                                        if (!repeatListNo.includes(deItem.parentId)) {
                                            let filter = cloneAzFeeListDe.filter(o => o.parentId === deItem.parentId);

                                            let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === deItem.sequenceNbr);
                                            let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                            ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                            let linshiData = {};
                                            linshiData.constructId = constructId;
                                            linshiData.unitId = unitId;
                                            linshiData.type = item.type;
                                            linshiData.feeItem = feeItem;
                                            linshiData.relatedDeRow = relatedDeRow;
                                            let ids = [];
                                            filter.forEach(o => {
                                                ids.push(o.sequenceNbr);
                                            });
                                            linshiData.relatedDeRowIds = ids;
                                            linshiData.sort = newSort;
                                            linshiData.parentId = relatedDeRow.parentId;
                                            linshiDataArray.push(linshiData);

                                            repeatListNo.push(deItem.parentId);
                                        }
                                    }
                                } else {
                                    let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === cloneAzFeeListDe[0].sequenceNbr);
                                    let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                    ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                    let linshiData = {};
                                    linshiData.constructId = constructId;
                                    linshiData.unitId = unitId;
                                    linshiData.type = item.type;
                                    linshiData.feeItem = feeItem;
                                    linshiData.relatedDeRow = relatedDeRow;
                                    let ids = [];
                                    cloneAzFeeListDe.forEach(o => {
                                        ids.push(o.sequenceNbr);
                                    });
                                    linshiData.relatedDeRowIds = ids;
                                    linshiData.sort = newSort;
                                    linshiData.parentId = item.relationListId;
                                    linshiDataArray.push(linshiData);
                                }
                            }
                        }
                    }
                }
            }
        }

        if(ObjectUtils.isNotEmpty(linshiDataArray)){
            let sort = linshiDataArray.sort((a, b) => a.sort - b.sort);
            for (let o of sort) {
                let feeItem = o.feeItem;
                //已经存在且此次计取出来的，删除之前的
                let deRowList1 = deRowList.filter(o => ObjectUtils.isEmpty(o.calculateMethod) && o.deCode === feeItem.defaultRow.deCode);
                if (ObjectUtils.isNotEmpty(deRowList1)) {
                    for (const o of deRowList1) {
                        await ProjectDomain.getDomain(constructId).getDeDomain().removeDeRow(o.deRowId, true);
                    }
                }
                //创建费用定额
                let feeDeRow = this.createAZFeeDe(constructId, unitId, o.type, feeItem.defaultRow, o.relatedDeRow);
                feeDeRow.deCode = feeItem.defaultRow.deCode;
                feeDeRow.deName = feeItem.defaultRow.deName;
                feeDeRow.libraryCode = feeItem.defaultRow.libraryCode;
                feeDeRow.libraryName = feeItem.defaultRow.libraryName;
                feeDeRow.unit = gsAZService.COMMON_AZ_FEE_UNIT;
                feeDeRow.resQty = 1;
                feeDeRow.quantity = 1;
                feeDeRow.unit = gsAZService.AZ_UNIT;
                feeDeRow.costFileCode = AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE;
                feeDeRow.costMajorName = AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE_NAME;
                feeDeRow.remark = AnZhuangJiQqConstants.AZ_JIQU_NAME_YS;

                feeDeRow.classlevel01 = feeItem.defaultRow.classLevel;

                //创建对应人材机
                this.ftProcess(constructId, unitId, feeItem.defaultRow.defaultFtStyle, feeItem.defaultRow.calculateBase, feeItem.defaultRow.rate, feeItem.defaultRow.rgzb
                    , feeItem.defaultRow.clzb, feeItem.defaultRow.jxzb, o.relatedDeRowIds, feeDeRow);
                await ProjectDomain.getDomain(constructId).deDomain.notify({constructId: constructId, unitId: unitId, deRowId:feeDeRow.sequenceNbr},true);

                try {
                    await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                        constructId: constructId,
                        unitId: unitId,
                        constructMajorType: feeDeRow.libraryCode
                    });
                } catch (error) {
                    console.error("捕获到异常:", error);
                }
            }
        }


        await FBCalculator.getInstance({constructId,unitId}, ProjectDomain.getDomain(constructId).ctx).analyze();
        return "calculateAZFee";
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @returns {null}
     */
    async calculateAZFeeV2(constructId, unitId, data, layer, height) {
        for (let item of data) {
            if (item.name === AnZhuangJiQqConstants.AZ_JIQU_NAME) {
                await this.calculateAZFee(constructId, unitId, item.list, layer, height);
            } else if (item.name === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS) {
                await this.calculateAZFeeYs(constructId, unitId, item.list, layer, height);
            }
        }
        return "calculateAZFee";
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @returns {null}
     */
    async calculateAZFeeDelete(constructId,unitId,data,layer,height) {
        //计算顺序
        let ClassLevelSortMap = await this.calcuateClassLevelSort();
        let linshiDataArray = [];

        let typeList = [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_USER_DE];
        let deRowList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && typeList.includes(item.type) && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE && ObjectUtils.isEmpty(item.calculateMethod));
        if(ObjectUtils.isNotEmpty(data))
        {
            for(let item of data)
            {
                if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked)
                {
                    for(let feeItem of item.feeItems)
                    {
                        if(ObjectUtils.isNotEmpty(feeItem?.defaultRow))
                        {

                            let calBaseDeList = [];
                            //处理baseDeList，为空时默认计取全部；有数据时筛出存在的数据
                            if (ObjectUtils.isNotEmpty(item.baseDeList)) {
                                item.baseDeList.forEach(o => {
                                    let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(o);
                                    if (ObjectUtils.isNotEmpty(de)) {
                                        calBaseDeList.push(de.sequenceNbr);
                                    }
                                });
                                item.baseDeList = calBaseDeList;
                            }else if (ObjectUtils.isEmpty(item.baseDeList)) {
                                let deList = deRowList.filter(p => p.type === DeTypeConstants.DE_TYPE_DELIST || p.type === DeTypeConstants.DE_TYPE_USER_DE);
                                if (ObjectUtils.isNotEmpty(deList)) {
                                    deList.forEach(m=>{
                                        calBaseDeList.push(m.sequenceNbr);
                                    })
                                }
                            }

                            let relatedDeRows = this.getDeRowsByRate1(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList, calBaseDeList);
                            // let relatedDeRows = this.getDeRowsByRate(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList);

                            let relatedDeRowIds = [];
                            if (ObjectUtils.isNotEmpty(relatedDeRows)) {
                                relatedDeRows.forEach(o => {
                                    if (calBaseDeList.includes(o.sequenceNbr)) {
                                        //如果进行了勾选，则只计取勾选的
                                        relatedDeRowIds.push(o);
                                    }
                                });

                                //过滤掉单独计取的定额数据
                                // let cloneAzFeeListDe1 = relatedDeRowIds.filter(oo=>ObjectUtils.isEmpty(oo.calculateMethod) || oo.calculateMethod!==0);
                                let cloneAzFeeListDe1 = [];
                                for (let oo of relatedDeRowIds) {
                                    if (oo => ObjectUtils.isEmpty(oo.calculateMethod) || oo.calculateMethod !== 0) {
                                        //全局计取可以计取
                                        cloneAzFeeListDe1.push(oo);
                                    } else {
                                        //单独计取，分item.feeName
                                        let feeNameDeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(o => o.unitId === unitId && o.parentId === oo.sequenceNbr
                                            && o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && o.deName.includes(item.feeName));
                                        if(ObjectUtils.isEmpty(feeNameDeList)){
                                            cloneAzFeeListDe1.push(oo);
                                        }
                                    }
                                }

                                if(ObjectUtils.isEmpty(cloneAzFeeListDe1)){
                                    continue;
                                }

                                //过滤临时删除定额数据
                                let cloneAzFeeListDe = cloneAzFeeListDe1.filter(oo=>ObjectUtils.isEmpty(oo.isTempRemove) || oo.isTempRemove=== CommonConstants.COMMON_NO);
                                if(ObjectUtils.isEmpty(cloneAzFeeListDe)){
                                    continue;
                                }



                                if (item.type === AnZhuangJiQqConstants.AnZhuangJiQq_DYFB) {
                                    let repeatListNo = [];
                                    for (let deItem of cloneAzFeeListDe) {
                                        if (!repeatListNo.includes(deItem.parentId)) {
                                            let filter = cloneAzFeeListDe.filter(o => o.parentId === deItem.parentId);

                                            let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === deItem.sequenceNbr);
                                            let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                            ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                            let linshiData = {};
                                            linshiData.constructId = constructId;
                                            linshiData.unitId = unitId;
                                            linshiData.type = item.type;
                                            linshiData.feeItem = feeItem;
                                            linshiData.relatedDeRow = relatedDeRow;
                                            let ids = [];
                                            filter.forEach(o => {
                                                ids.push(o.sequenceNbr);
                                            });
                                            linshiData.relatedDeRowIds = ids;
                                            linshiData.sort = newSort;
                                            linshiData.parentId = relatedDeRow.parentId;
                                            linshiDataArray.push(linshiData);
                                            repeatListNo.push(deItem.parentId);
                                        }
                                    }
                                } else {
                                    let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === cloneAzFeeListDe[0].sequenceNbr);
                                    let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                    ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                    let linshiData = {};
                                    linshiData.constructId = constructId;
                                    linshiData.unitId = unitId;
                                    linshiData.type = item.type;
                                    linshiData.feeItem = feeItem;
                                    linshiData.relatedDeRow = relatedDeRow;
                                    let ids = [];
                                    cloneAzFeeListDe.forEach(o => {
                                        ids.push(o.sequenceNbr);
                                    });
                                    linshiData.relatedDeRowIds = ids;
                                    linshiData.sort = newSort;
                                    linshiData.parentId = item.relationListId;
                                    linshiDataArray.push(linshiData);
                                }
                            }
                        }
                    }
                }
            }
        }

        return linshiDataArray;
    }



    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @returns {null}
     */
    async calculateAZFeeDeleteYS(constructId,unitId,data,layer,height) {
        //计算顺序
        let ClassLevelSortMap = await this.calcuateClassLevelSortYS();
        let linshiDataArray = [];

        let typeList = [DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_ANZHUANG_FEE,DeTypeConstants.DE_TYPE_USER_DE];
        let deRowList1 = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && typeList.includes(item.type) && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE);
        let deRowList = [];
        if (ObjectUtils.isNotEmpty(deRowList1)) {
            deRowList1.forEach(o => {
                let parentDe = ProjectDomain.getDomain(constructId).deDomain.getDeById(o.parentId);
                if (ObjectUtils.isEmpty(parentDe) || parentDe.type !== DeTypeConstants.DE_TYPE_DELIST) {
                    deRowList.push(o);  //预算安装作为子定额时不参与计算
                }
            });
        }

        if(ObjectUtils.isNotEmpty(data))
        {
            for(let item of data)
            {
                if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked)
                {
                    for(let feeItem of item.feeItems)
                    {
                        if(ObjectUtils.isNotEmpty(feeItem?.defaultRow))
                        {

                            let calBaseDeList = [];
                            //处理baseDeList，为空时默认计取全部；有数据时筛出存在的数据
                            if (ObjectUtils.isNotEmpty(item.baseDeList)) {
                                item.baseDeList.forEach(o => {
                                    let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(o);
                                    if (ObjectUtils.isNotEmpty(de)) {
                                        calBaseDeList.push(de.sequenceNbr);
                                    }
                                });
                                item.baseDeList = calBaseDeList;
                            }else if (ObjectUtils.isEmpty(item.baseDeList)) {
                                let deList = deRowList.filter(p => p.type === DeTypeConstants.DE_TYPE_DELIST || p.type === DeTypeConstants.DE_TYPE_DE || p.type === DeTypeConstants.DE_TYPE_USER_DE);
                                if (ObjectUtils.isNotEmpty(deList)) {
                                    deList.forEach(m=>{
                                        calBaseDeList.push(m.sequenceNbr);
                                    })
                                }
                            }

                            let classlevelName = feeItem.classlevelName;//章节1~6,8~15
                            let relatedDeRows = this.getDeRowsByRate1YS(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList, calBaseDeList, classlevelName);
                            // let relatedDeRows = this.getDeRowsByRate(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList);

                            let relatedDeRowIds = [];
                            if (ObjectUtils.isNotEmpty(relatedDeRows)) {
                                relatedDeRows.forEach(o => {
                                    if (calBaseDeList.includes(o.sequenceNbr)) {
                                        //如果进行了勾选，则只计取勾选的
                                        relatedDeRowIds.push(o);
                                    }
                                });

                                //过滤掉单独计取的定额数据
                                let cloneAzFeeListDe1 = relatedDeRowIds.filter(oo=>ObjectUtils.isEmpty(oo.calculateMethod) || oo.calculateMethod!==0);
                                if(ObjectUtils.isEmpty(cloneAzFeeListDe1)){
                                    continue;
                                }

                                //过滤临时删除定额数据
                                let cloneAzFeeListDe = cloneAzFeeListDe1.filter(oo=>ObjectUtils.isEmpty(oo.isTempRemove) || oo.isTempRemove=== CommonConstants.COMMON_NO);
                                if(ObjectUtils.isEmpty(cloneAzFeeListDe)){
                                    continue;
                                }


                                if (item.type === AnZhuangJiQqConstants.AnZhuangJiQq_DYFB) {
                                    let repeatListNo = [];
                                    for (let deItem of cloneAzFeeListDe) {
                                        if (!repeatListNo.includes(deItem.parentId)) {
                                            let filter = cloneAzFeeListDe.filter(o => o.parentId === deItem.parentId);

                                            let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === deItem.sequenceNbr);
                                            let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                            ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                            let linshiData = {};
                                            linshiData.constructId = constructId;
                                            linshiData.unitId = unitId;
                                            linshiData.type = item.type;
                                            linshiData.feeItem = feeItem;
                                            linshiData.relatedDeRow = relatedDeRow;
                                            let ids = [];
                                            filter.forEach(o => {
                                                ids.push(o.sequenceNbr);
                                            });
                                            linshiData.relatedDeRowIds = ids;
                                            linshiData.sort = newSort;
                                            linshiData.parentId = relatedDeRow.parentId;
                                            linshiDataArray.push(linshiData);

                                            repeatListNo.push(deItem.parentId);
                                        }
                                    }
                                } else {
                                    let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === cloneAzFeeListDe[0].sequenceNbr);
                                    let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                    ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                    let linshiData = {};
                                    linshiData.constructId = constructId;
                                    linshiData.unitId = unitId;
                                    linshiData.type = item.type;
                                    linshiData.feeItem = feeItem;
                                    linshiData.relatedDeRow = relatedDeRow;
                                    let ids = [];
                                    cloneAzFeeListDe.forEach(o => {
                                        ids.push(o.sequenceNbr);
                                    });
                                    linshiData.relatedDeRowIds = ids;
                                    linshiData.sort = newSort;
                                    linshiData.parentId = item.relationListId;
                                    linshiDataArray.push(linshiData);
                                }
                            }
                        }
                    }
                }
            }
        }

        return linshiDataArray;
    }


    async calculateAZFeeLianAloneDong(constructId, unitId, de, type) {
        try {
            //如果该条上级是单独计取，则需要重新计取
            let parentDe = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(de.parentId);
            let parentId = de.sequenceNbr;
            //存在de为root时，此时root.parentId为0，parentDe==undefined
            if (ObjectUtils.isNotEmpty(parentDe)&&ObjectUtils.isNotEmpty(parentDe.calculateMethod)) {
                parentId = parentDe.sequenceNbr;
            }
            let parentTrueDe = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(parentId);

            if (ObjectUtils.isEmpty(parentTrueDe)) {
                return;
            }

            let nowAnFeeList = [];
            if (ObjectUtils.isNotEmpty(parentTrueDe.children)) {
                nowAnFeeList = parentTrueDe.children.filter(o => o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
                if (ObjectUtils.isEmpty(nowAnFeeList)) {
                    parentTrueDe.calculateMethod = undefined;
                    ProjectDomain.getDomain(constructId).getDeDomain().updateDe(parentTrueDe);
                    return true;
                }
            }

            let caches = await this.getCachesAlone(constructId, unitId, parentId);
            if (ObjectUtils.isNotEmpty(caches)) {
                let calculateMethod = parentTrueDe.calculateMethod;
                if (type === "delete") {
                    if (de.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE) {
                        //删除计取数据，将父级改为未计取
                        parentTrueDe.calculateMethod = undefined;
                        ProjectDomain.getDomain(constructId).getDeDomain().updateDe(parentTrueDe);
                    } else {
                        let azDeCalcu = await this.calculateAZFeeAloneDelete(constructId, unitId, caches.data, caches.layer, caches.height, parentId);
                        if(ObjectUtils.isNotEmpty(azDeCalcu)){
                            await this.calDeRCJAlone1(nowAnFeeList,azDeCalcu,calculateMethod);
                        } else {
                            //计取无数据，则将当前的改为0
                            for (let item of nowAnFeeList) {
                                await this.calDeRCJAlone(constructId, unitId, item, calculateMethod);
                            }
                        }
                    }
                } else {
                    //如果是增加/修改，重新计取
                    await this.calculateAZFeeAlone(constructId, unitId, caches.data, caches.layer, caches.height, parentId);
                }
            }
        } catch (error) {
            console.error("捕获到异常:", error);
        }
    }


    async calDeRCJUpdateAlone(constructId, unitId, o, jiqu, calculateMethod) {
        let nowRcjList = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, o.deRowId, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);

        let feeItem = jiqu.feeItem;
        let jqRcjList = this.ftProcess2(constructId, unitId, feeItem.defaultRow.defaultFtStyle, feeItem.defaultRow.calculateBase, feeItem.defaultRow.rate, feeItem.defaultRow.rgzb
            , feeItem.defaultRow.clzb, feeItem.defaultRow.jxzb, jiqu.relatedDeRowIds, jiqu);

        if (ObjectUtils.isNotEmpty(nowRcjList)) {
            for (let item of nowRcjList) {
                let filter = jqRcjList.filter(p => p.materialCode === item.materialCode);
                if (ObjectUtils.isNotEmpty(filter)) {
                    let args = {};
                    args.constructId = constructId;
                    args.unitId = unitId;
                    args.singleId = null;
                    args.deId = o.deRowId;
                    args.rcjDetailId = item.sequenceNbr;
                    let constructRcj = {};
                    constructRcj.resQty = filter[0].resQty;
                    constructRcj.totalNumber = filter[0].totalNumber;
                    args.constructRcj = constructRcj;
                    const result = await this.service.PreliminaryEstimate.gsRcjService.updateRcjDetail(args);
                }
            }

            // if (calculateMethod === 0) {
            //     for (let item of nowRcjList) {
            //         let filter = jqRcjList.filter(p => p.materialCode === item.materialCode);
            //         if (ObjectUtils.isNotEmpty(filter)) {
            //             item.resQty = filter[0].resQty;
            //             item.totalNumber = filter[0].totalNumber;
            //             ProjectDomain.getDomain(constructId).resourceDomain.updateResource(unitId, item.parentId, item);
            //         }
            //     }
            // } else {
            //
            // }
        }
    }

    async calDeRCJAlone1(nowAnDe, jiquAnDe,calculateMethod) {
        for (let item of nowAnDe) {
            let jiquDeFilterList = jiquAnDe.filter(o => o.feeItem.defaultRow.deCode === item.deCode);
            if (ObjectUtils.isNotEmpty(jiquDeFilterList)) {
                let jiquDeFilterListElement = jiquDeFilterList[0];
                await this.calDeRCJUpdateAlone(item.constructId, item.unitId, item, jiquDeFilterListElement,calculateMethod);
            } else {
                await this.calDeRCJAlone(item.constructId, item.unitId, item, calculateMethod);
            }
        }
    }

    async calDeRCJAlone(constructId, unitId, o, calculateMethod) {
        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let deRcjList = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, o.deRowId, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
        if (ObjectUtils.isNotEmpty(deRcjList)) {
            let totalNumber = o.totalNumber;
            //全局计取，这里是把人材机加起来，更新到定额上，不向上汇总
            let sum = 0;
            for (const p of deRcjList) {
                let args = {};
                args.constructId = constructId;
                args.unitId = unitId;
                args.singleId = null;
                args.deId = o.deRowId;
                args.rcjDetailId = p.sequenceNbr;
                let constructRcj = {};
                constructRcj.resQty = 0;
                constructRcj.totalNumber = 0;
                args.constructRcj = constructRcj;
                const result = await this.service.PreliminaryEstimate.gsRcjService.updateRcjDetail(args);

                // p.resQty = 0;
                // p.totalNumber = 0;
                // p.total = 0;
                // sum = NumberUtil.add(sum, p.resQty);
                // ProjectDomain.getDomain(constructId).resourceDomain.updateResource(unitId, p.parentId, p);
            }
            // o.price = sum;
            // o.totalNumber = sum;
            // ProjectDomain.getDomain(o.constructId).getDeDomain().updateDeOnlyOwnData(o);
            //
            // if (calculateMethod === 0) {
            //     //改为0在总金额减去
            //     let defaultRow = ProjectDomain.getDomain(constructId).deDomain.getDeById(unit.defaultDeId);
            //     defaultRow.totalNumber = NumberUtil.subtract(defaultRow.totalNumber, totalNumber);
            //     ProjectDomain.getDomain(defaultRow.constructId).getDeDomain().updateDeOnlyOwnData(defaultRow);
            // }
        }
    }


    async calculateAZFeeLianDongAndAloneBzhs(constructId, unitId, deId, type) {
        try {
            let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
            if (type === "delete") {
                // 删除安装费用重新计算
                if (deDomain.getDeById(deId).type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE) {
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDong(constructId, unitId, deDomain.getDeById(deId), type);
                    //todo 预算定额计算
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongYS(constructId, unitId, deDomain.getDeById(deId), type);
                }
                await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianAloneDong(constructId, unitId, deDomain.getDeById(deId), type);
            } else {
                if (deDomain.getDeById(deId).type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE) {
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDong(constructId, unitId, deDomain.getDeById(deId), type);
                    //todo 预算定额计算
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongYS(constructId, unitId, deDomain.getDeById(deId), type);
                    //判断但单定额计取是否需要更新
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianAloneDong(constructId, unitId, deDomain.getDeById(deId), type);
                }
            }

            //联动计算装饰超高人材机数量
            await this.service.PreliminaryEstimate.gsDeService.calculateZSFee(constructId, unitId, true);
        } catch (error) {
            console.error("联动计算安装计取费用捕获到异常:", error);
        }
    }


    async calculateAZFeeLianDongAndAlone(constructId, unitId, deId, type) {
        try {
            let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
            if (type === "delete") {
                await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianAloneDong(constructId, unitId, deDomain.getDeById(deId), type);
                // 删除安装费用重新计算
                if (deDomain.getDeById(deId).type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE || (deDomain.getDeById(deId).type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && deDomain.getDeById(deId).calculateMethod === 0)) {
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDong(constructId, unitId, deDomain.getDeById(deId), type);
                    //todo 预算定额安装计取
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongYS(constructId, unitId, deDomain.getDeById(deId), type);
                }
            } else {
                if (deDomain.getDeById(deId).type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE) {
                    //判断但单定额计取是否需要更新
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianAloneDong(constructId, unitId, deDomain.getDeById(deId), type);
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDong(constructId, unitId, deDomain.getDeById(deId), type);
                    //todo 预算定额安装计取
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDongYS(constructId, unitId, deDomain.getDeById(deId), type);
                }
            }

            //删除主材后，计算父级定额是否展开/折叠
            await this.calDeDisplaySign(constructId, unitId, deId);
        } catch (error) {
            console.error("联动计算安装计取费用捕获到异常:", error);
        }
    }


    async calDeDisplaySign(constructId, unitId, deParentId) {
        if (ObjectUtil.isNotEmpty(deParentId)) {
            let parentDeRow = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deParentId);
            let sign = true;
            if (ObjectUtil.isNotEmpty(parentDeRow) && ObjectUtil.isNotEmpty(parentDeRow.children)) {
                sign = false;
            }
            if (parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST || parentDeRow.type === DeTypeConstants.DE_TYPE_DE || parentDeRow.type === DeTypeConstants.DE_TYPE_USER_DE) {
                let rcjLists = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(parentDeRow.constructId, parentDeRow.unitId, deParentId, parentDeRow.type);
                if (ObjectUtil.isNotEmpty(rcjLists)) {
                    let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
                    if (ObjectUtil.isNotEmpty(filter)) {
                        sign = false;
                    }
                }
            }

            if (sign && parentDeRow.displaySign === BranchProjectDisplayConstant.open) {
                parentDeRow.displaySign = BranchProjectDisplayConstant.noSign;
            }
        }
    }


    async calculateAZFeeRepeat(constructId, unitId) {
        try {
            let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
            //重新计取安装定额
            let cache = await this.getCachesType(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE1);
            if (ObjectUtils.isNotEmpty(cache)) {
                cache.data.forEach(f => {
                    f.type = AnZhuangJiQqConstants.AnZhuangJiQq_DYFB;
                });
                await this.service.PreliminaryEstimate.gsAZservice.calculateAZFee(constructId, unitId, cache.data, cache.layer, cache.height)
            }

            //重新计取预算安装定额
            let cacheYS = await this.getCachesType(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE2);
            if (ObjectUtils.isNotEmpty(cacheYS)) {
                cacheYS.data.forEach(f => {
                    f.type = AnZhuangJiQqConstants.AnZhuangJiQq_DYFB;
                });
                await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeYs(constructId, unitId, cacheYS.data, cacheYS.layer, cacheYS.height)
            }
        } catch (error) {
            console.error("整理子目联动计算安装计取费用捕获到异常:", error);
        }
    }


    async calculateAZFeeLianDongAndAloneDelete(constructId, unitId, de, type) {
        try {
            let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
            if (type === "delete") {
                await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianAloneDong(constructId, unitId, de, type);
                // 删除安装费用重新计算
                if (de.type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE || (de.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && de.calculateMethod === 0)) {
                    await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDong(constructId, unitId, de, type);
                }
            }
        } catch (error) {
            console.error("联动计算安装计取费用捕获到异常:", error);
        }
    }


    async deleteAzRcjRepeatAddRcj(constructId, unitId, deId) {
        let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let rcj of rcjList) {
                await this.service.PreliminaryEstimate.gsRcjService.deleteRcjByCodeData(deId, constructId, unitId, rcj.sequenceNbr, true, {});
            }
            await this.calculateAZFeeAddRcj(constructId, unitId, deId);
        }
    }


    async calculateAZFeeLianDong(constructId, unitId,de, type) {
        try {
            //如果当前没有安装费，则不再更新数据
            // let deTree = ProjectDomain.getDomain(constructId).getDeDomain().getDeTreeDepth(constructId, unitId, null, null);
            let anDeData = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE && ObjectUtils.isEmpty(item.calculateMethod));
            // let anDeData1 = deTree.filter(o => o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
            // let anDeData = [];
            // if (ObjectUtils.isNotEmpty(anDeData1)) {
            //     for (let deRow of anDeData1) {
            //         let parentDe = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRow.parentId);
            //         if (parentDe.type !== DeTypeConstants.DE_TYPE_DELIST) {       //全部计取的得从新计算
            //             anDeData.push(deRow);
            //         }
            //     }
            // }

            if(ObjectUtils.isEmpty(anDeData)){
                return true;
            }

            let caches = await this.getCachesType(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE1);
            if (ObjectUtils.isNotEmpty(caches)) {
                let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                if (type === "delete") {
                    //如果是删除，计取无数据将剩下的安装费置为0，有数据则更新当前数据
                    let azDeCalcu = await this.calculateAZFeeDelete(constructId, unitId, caches.data, caches.layer, caches.height);
                    //循环当前安装定额，如果是页面计取出来的，则根据缓存再次计取刷新人材机数据；如果是手动录入编码选择，则删除人材机重新计算人材机插入
                    for (let azFeeDe of anDeData) {
                        let azFeeDeM = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(azFeeDe.sequenceNbr);
                        if (ObjectUtils.isEmpty(azFeeDeM.azCalculateSelectDe) || !azFeeDeM.azCalculateSelectDe) {
                            if (ObjectUtils.isNotEmpty(azDeCalcu)) {
                                let oneAzFeeDeList = [];
                                oneAzFeeDeList.push(azFeeDe);
                                await this.calDeRCJ1(oneAzFeeDeList, azDeCalcu);
                            } else {
                                await this.calDeRCJ(constructId, unitId, azFeeDe);
                            }
                        } else {
                            await this.deleteAzRcjRepeatAddRcj(constructId, unitId, azFeeDe.sequenceNbr);
                        }
                    }
                } else {
                    if (de.type === DeTypeConstants.DE_TYPE_DELIST || de.type === DeTypeConstants.DE_TYPE_USER_DE) {
                        //如果新增定额章节是新的则不计取
                        let anFeeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTreeDepth(constructId, unitId, null, ["07"]);
                        if (ObjectUtils.isEmpty(anFeeList)) {
                            return;
                        }
                        let anFeeAloneList = anFeeList.filter(o => ObjectUtils.isEmpty(o.calculateMethod));
                        if (ObjectUtils.isEmpty(anFeeAloneList)) {
                            return;
                        }

                        const regex = /\((.*?)\)/;
                        let classlevelList = [];
                        for (let item of anFeeAloneList) {
                            const result = item.deName.match(regex);
                            if(ObjectUtils.isNotEmpty(result) && result.length>1){
                                classlevelList.push(result[1]);
                            }
                        }

                        let calcue = false;
                        for (let rateKey of classlevelList) {
                            //如果存在于当前feeName对应的章节内
                            if(de.type === DeTypeConstants.DE_TYPE_DELIST && de.classlevel02?.indexOf(rateKey) >= 0){
                                calcue = true;
                            }
                            if(de.type === DeTypeConstants.DE_TYPE_USER_DE && de.classifyLevel2?.indexOf(rateKey) >= 0){
                                calcue = true;
                            }
                        }
                        if (calcue) {
                            await this.calculateAZFee(constructId, unitId, caches.data, caches.layer, caches.height);
                        }


                        //如果老定额计取过，替换成了不同章节的定额，需要重新计取
                        if (ObjectUtils.isNotEmpty(de.classlevel02) && ObjectUtils.isNotEmpty(de.oldclasslevel02) && de.classlevel02 !== de.oldclasslevel02) {
                            let azDeCalcu = await this.calculateAZFeeDelete(constructId, unitId, caches.data, caches.layer, caches.height);
                            //循环当前安装定额，如果是页面计取出来的，则根据缓存再次计取刷新人材机数据；如果是手动录入编码选择，则删除人材机重新计算人材机插入
                            for (let azFeeDe of anDeData) {
                                let azFeeDeM = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(azFeeDe.sequenceNbr);
                                if (ObjectUtils.isEmpty(azFeeDeM.azCalculateSelectDe) || !azFeeDeM.azCalculateSelectDe) {
                                    if (ObjectUtils.isNotEmpty(azDeCalcu)) {
                                        let oneAzFeeDeList = [];
                                        oneAzFeeDeList.push(azFeeDe);
                                        await this.calDeRCJ1(oneAzFeeDeList, azDeCalcu);
                                    } else {
                                        await this.calDeRCJ(constructId, unitId, azFeeDe);
                                    }
                                } else {
                                    await this.deleteAzRcjRepeatAddRcj(constructId, unitId, azFeeDe.sequenceNbr);
                                }
                            }
                        }
                    } else {
                        //如果是增加/修改，重新计取
                        await this.calculateAZFee(constructId, unitId, caches.data, caches.layer, caches.height);
                    }
                }
            }
        } catch (error) {
            console.error("安装费用计取捕获到异常:", error);
        }
    }

    async calculateAZFeeLianDongYS(constructId, unitId, de, type) {
        try {
            //如果当前没有安装费，则不再更新数据
            // let deTree = ProjectDomain.getDomain(constructId).getDeDomain().getDeTreeDepth(constructId, unitId, null, null);
            // let anDeData1 = deTree.filter(o => o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && o.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE);
            // let anDeData = [];
            // if (ObjectUtils.isNotEmpty(anDeData1)) {
            //     for (let deRow of anDeData1) {
            //         let parentDe = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRow.parentId);
            //         if (parentDe.type !== DeTypeConstants.DE_TYPE_DELIST) {       //全部计取的得从新计算
            //             anDeData.push(deRow);
            //         }
            //     }
            // }
            let anDeData = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE && ObjectUtils.isEmpty(item.calculateMethod));
            if (ObjectUtils.isEmpty(anDeData)) {
                return true;
            }

            let caches = await this.getCachesType(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE2);
            if (ObjectUtils.isNotEmpty(caches)) {
                let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
                if (type === "delete") {
                    let azDeCalcu = await this.calculateAZFeeDeleteYS(constructId, unitId, caches.data, caches.layer, caches.height);
                    //循环当前安装定额，如果是页面计取出来的，则根据缓存再次计取刷新人材机数据；如果是手动录入编码选择，则删除人材机重新计算人材机插入
                    for (let azFeeDe of anDeData) {
                        let azFeeDeM = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(azFeeDe.sequenceNbr);
                        if (ObjectUtils.isEmpty(azFeeDeM.azCalculateSelectDe) || !azFeeDeM.azCalculateSelectDe) {
                            if (ObjectUtils.isNotEmpty(azDeCalcu)) {
                                let oneAzFeeDeList = [];
                                oneAzFeeDeList.push(azFeeDe);
                                await this.calDeRCJ1(oneAzFeeDeList, azDeCalcu);
                            } else {
                                await this.calDeRCJ(constructId, unitId, azFeeDe);
                            }
                        } else {
                            await this.deleteAzRcjRepeatAddRcj(constructId, unitId, azFeeDe.sequenceNbr);
                        }
                    }
                } else {
                    if (de.type === DeTypeConstants.DE_TYPE_DE || de.type === DeTypeConstants.DE_TYPE_USER_DE) {
                        //如果新增定额章节是新的则不计取
                        // let anFeeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTreeDepth(constructId, unitId, null, ["07"]);
                        let anFeeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && item.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE);
                        if (ObjectUtils.isEmpty(anFeeList)) {
                            return;
                        }
                        let anFeeAloneList = anFeeList.filter(o => ObjectUtils.isEmpty(o.calculateMethod));
                        if (ObjectUtils.isEmpty(anFeeAloneList)) {
                            return;
                        }

                        const regex = /\((.*?)\)/;
                        const regex1 = "/(?:\\()\\w+(?:\\))/g";
                        let classlevelList = [];
                        for (let item of anFeeAloneList) {
                            // const result = item.deName.match(regex);
                            // if (ObjectUtils.isNotEmpty(result) && result.length > 1) {
                            //     classlevelList.push(result[1]);
                            // }
                            classlevelList.push(item.classlevel01);
                        }

                        let calcue = false;
                        for (let rateKey of classlevelList) {
                            //如果存在于当前feeName对应的章节内
                            if (de.type === DeTypeConstants.DE_TYPE_DE && de.classlevel01?.indexOf(rateKey) >= 0) {
                                calcue = true;
                            }
                            if (de.type === DeTypeConstants.DE_TYPE_USER_DE && de.classifyLevel1?.indexOf(rateKey) >= 0) {
                                calcue = true;
                            }
                        }
                        if (calcue) {
                            await this.calculateAZFeeYs(constructId, unitId, caches.data, caches.layer, caches.height);
                        }


                        //如果老定额计取过，替换成了不同章节的定额，需要重新计取
                        if (ObjectUtils.isNotEmpty(de.classlevel02) && ObjectUtils.isNotEmpty(de.oldclasslevel02) && de.classlevel02 !== de.oldclasslevel02) {
                            let azDeCalcu = await this.calculateAZFeeDeleteYS(constructId, unitId, caches.data, caches.layer, caches.height);
                            //循环当前安装定额，如果是页面计取出来的，则根据缓存再次计取刷新人材机数据；如果是手动录入编码选择，则删除人材机重新计算人材机插入
                            for (let azFeeDe of anDeData) {
                                let azFeeDeM = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(azFeeDe.sequenceNbr);
                                if (ObjectUtils.isEmpty(azFeeDeM.azCalculateSelectDe) || !azFeeDeM.azCalculateSelectDe) {
                                    if (ObjectUtils.isNotEmpty(azDeCalcu)) {
                                        let oneAzFeeDeList = [];
                                        oneAzFeeDeList.push(azFeeDe);
                                        await this.calDeRCJ1(oneAzFeeDeList, azDeCalcu);
                                    } else {
                                        await this.calDeRCJ(constructId, unitId, azFeeDe);
                                    }
                                } else {
                                    await this.deleteAzRcjRepeatAddRcj(constructId, unitId, azFeeDe.sequenceNbr);
                                }
                            }
                        }
                    } else {
                        //如果是增加/修改，重新计取
                        await this.calculateAZFeeYs(constructId, unitId, caches.data, caches.layer, caches.height);
                    }
                }
            }
        } catch (error) {
            console.error("安装费用计取捕获到异常:", error);
        }
    }

    async calDeRCJ1(nowAnDe, jiquAnDe) {
        for (let item of nowAnDe) {
            let jiquDeFilterList = jiquAnDe.filter(o => o.feeItem.defaultRow.deCode === item.deCode && o.parentId === item.parentId);
            if (ObjectUtils.isNotEmpty(jiquDeFilterList)) {
                let jiquDeFilterListElement = jiquDeFilterList[0];
                await this.calDeRCJUpdate(item.constructId, item.unitId, item, jiquDeFilterListElement);
            } else {
                await this.calDeRCJ(item.constructId, item.unitId, item);
            }
        }
    }

    async calDeRCJUpdate(constructId, unitId, o, jiqu) {
        let nowRcjList = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, o.deRowId, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);

        let feeItem = jiqu.feeItem;
        let jqRcjList = this.ftProcess2(constructId, unitId, feeItem.defaultRow.defaultFtStyle, feeItem.defaultRow.calculateBase, feeItem.defaultRow.rate, feeItem.defaultRow.rgzb
            , feeItem.defaultRow.clzb, feeItem.defaultRow.jxzb, jiqu.relatedDeRowIds, jiqu);

        if (ObjectUtils.isNotEmpty(nowRcjList)) {
            for (let item of nowRcjList) {
                let materialCode1 = item.materialCode;
                if (item.materialCode.includes("#")) {
                    let parts = item.materialCode.split("#");
                    materialCode1 = parts[0];
                }

                let filter = jqRcjList.filter(p => p.materialCode === materialCode1);
                if (ObjectUtils.isNotEmpty(filter)) {
                    let args = {};
                    args.constructId = constructId;
                    args.unitId = unitId;
                    args.singleId = null;
                    args.deId = o.deRowId;
                    args.rcjDetailId = item.sequenceNbr;
                    let constructRcj = {};

                    if (ObjectUtils.isNotEmpty(item.isTempRemove) && item.isTempRemove === CommonConstants.COMMON_YES) {
                        // constructRcj.changeResQty = filter[0].resQty;
                        let rcjDeKey = WildcardMap.generateKey(unitId, o.deRowId) + WildcardMap.WILDCARD;
                        let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
                        let rcjRes = rcjList.find(o => o.sequenceNbr === item.sequenceNbr);
                        if (ObjectUtils.isNotEmpty(rcjRes)) {
                            rcjRes.changeResQty = filter[0].resQty;
                            rcjRes.totalNumber = filter[0].totalNumber;
                        }
                    } else {
                        constructRcj.resQty = filter[0].resQty;
                        constructRcj.totalNumber = filter[0].totalNumber;
                        args.constructRcj = constructRcj;
                        const result = await this.service.PreliminaryEstimate.gsRcjService.updateRcjDetail(args);
                    }
                }
            }
        }
    }

    async calDeRCJ(constructId, unitId, o) {
        let result = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, o.deRowId, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
        if (ObjectUtils.isNotEmpty(result)) {
            for (let item of result) {
                let args = {};
                args.constructId = constructId;
                args.unitId = unitId;
                args.singleId = null;
                args.deId = o.deRowId;
                args.rcjDetailId = item.sequenceNbr;
                let constructRcj = {};

                if (ObjectUtils.isNotEmpty(item.isTempRemove) && item.isTempRemove === CommonConstants.COMMON_YES) {
                    // constructRcj.changeResQty = filter[0].resQty;
                    let rcjDeKey = WildcardMap.generateKey(unitId, o.deRowId) + WildcardMap.WILDCARD;
                    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
                    let rcjRes = rcjList.find(o => o.sequenceNbr === item.sequenceNbr);
                    if (ObjectUtils.isNotEmpty(rcjRes)) {
                        rcjRes.changeResQty = 0;
                        rcjRes.totalNumber = 0;
                    }
                } else {
                    constructRcj.resQty = 0;
                    constructRcj.totalNumber = 0;
                    args.constructRcj = constructRcj;
                    const result = await this.service.PreliminaryEstimate.gsRcjService.updateRcjDetail(args);
                }
            }
        }
    }



    /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @param deRowId
     * @returns {null}
     */
    async calculateAZFeeAlone(constructId, unitId, data, layer, height, deRowId) {
        let de1 = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRowId);
        if (ObjectUtils.isNotEmpty(de1) && de1.libraryCode !== AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE) {
            //除了安装定额可以单独计取，其余不能计取
            return "calculateAZFee";
        }


        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        this.initCacheAlone(constructId, unitId, deRowId, data, layer, height);
        await this.clearFeeDeAlone(constructId, unitId, deRowId);
        let deRowList = ProjectDomain.getDomain(constructId).deDomain
            .getDeTreeDepth(constructId, unitId, deRowId, [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB, DeTypeConstants.DE_TYPE_DEFAULT, DeTypeConstants.DE_TYPE_USER_DE]);
        let calculateMethod = data[0].calculateMethod;

        if (ObjectUtils.isNotEmpty(data)) {
            for (let item of data) {
                item.type = 1;

                if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked) {
                    for (let feeItem of item.feeItems) {
                        feeItem.defaultRow.relationListId = deRowId;
                        if (ObjectUtils.isNotEmpty(feeItem?.defaultRow)) {
                            let relatedDeRows = this.getDeRowsByRateAlone(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList);

                            let relatedDeRowIds = [];
                            if (ObjectUtils.isNotEmpty(relatedDeRows)) {
                                relatedDeRowIds = relatedDeRows.map(obj => obj.sequenceNbr);

                                let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === relatedDeRowIds[0]);
                                //创建费用定额
                                let feeDeRow = this.createAZFeeDe(constructId, unitId, item.type, feeItem.defaultRow, relatedDeRow);
                                feeDeRow.deCode = feeItem.defaultRow.deCode;
                                feeDeRow.deName = feeItem.defaultRow.deName;
                                feeDeRow.libraryCode = feeItem.defaultRow.libraryCode;
                                feeDeRow.libraryName = feeItem.defaultRow.libraryName;
                                feeDeRow.unit = gsAZService.COMMON_AZ_FEE_UNIT;
                                feeDeRow.resQty = 1;
                                feeDeRow.quantity = 1;
                                feeDeRow.unit = gsAZService.AZ_UNIT;
                                feeDeRow.calculateMethod = calculateMethod;
                                //创建对应人材机
                                this.ftProcess(constructId, unitId, feeItem.defaultRow.defaultFtStyle, feeItem.defaultRow.calculateBase, feeItem.defaultRow.rate, feeItem.defaultRow.rgzb
                                    , feeItem.defaultRow.clzb, feeItem.defaultRow.jxzb, relatedDeRowIds, feeDeRow);

                                let deDomain = ProjectDomain.getDomain(constructId).deDomain;
                                //联动计算
                                await deDomain.notify({
                                    constructId: constructId,
                                    unitId: unitId,
                                    deRowId: feeDeRow.sequenceNbr
                                }, true);
                                //借换标识处理
                                DeTypeCheckUtil.updateParentDeType(feeDeRow, deDomain.ctx);

                                try {
                                    await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                                        constructId: constructId,
                                        unitId: unitId,
                                        constructMajorType: feeDeRow.libraryCode
                                    });
                                } catch (error) {
                                    console.error("捕获到异常:", error);
                                }


                                //全局计取，这里是把人材机加起来，更新到定额上，不向上汇总
                                // let deRcjList = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, feeDeRow.sequenceNbr, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
                                // let sum = 0;
                                // deRcjList.forEach(o => {
                                //     o.totalNumber = o.resQty;
                                //     o.total = o.resQty;
                                //     ProjectDomain.getDomain(constructId).resourceDomain.updateResource(unitId, o.parentId, o);
                                //     sum = NumberUtil.add(sum, o.resQty);
                                // });
                                // feeDeRow.price = sum;
                                // feeDeRow.totalNumber = sum;
                                // ProjectDomain.getDomain(feeDeRow.constructId).getDeDomain().updateDeOnlyOwnData(feeDeRow);
                                //
                                // if (calculateMethod === 0) {
                                //     //单独计取需要在总金额加上计取的
                                //     let defaultRow = ProjectDomain.getDomain(constructId).deDomain.getDeById(unit.defaultDeId);
                                //     defaultRow.totalNumber = NumberUtil.add(defaultRow.totalNumber,sum);
                                //     ProjectDomain.getDomain(defaultRow.constructId).getDeDomain().updateDeOnlyOwnData(defaultRow);
                                // }
                            }
                        }
                    }
                }
            }
        }

        //需要向上汇总
        await FBCalculator.getInstance({constructId, unitId}, ProjectDomain.getDomain(constructId).ctx).analyze();

        //标记定额是单独计取的
        let de = ProjectDomain.getDomain(constructId).ctx.deMap.getNodeById(deRowId);
        de.calculateMethod = calculateMethod;
        ProjectDomain.getDomain(constructId).getDeDomain().updateDe(de);

        if (calculateMethod === AnZhuangJiQqConstants.AnZhuangJiQq_ALONE) {
            //单独计取，更新全部计取的值
            await this.service.PreliminaryEstimate.gsAZservice.calculateAZFeeLianDong(constructId, unitId, de, "delete");
        }

        return "calculateAZFee";
    }

     /**
     *
     * @param constructId
     * @param unitId
     * @param data
     * @param layer
     * @param height
     * @param deRowId
     * @returns {null}
     */
    async calculateAZFeeAloneDelete(constructId, unitId, data, layer, height, deRowId) {
        // this.initCacheAlone(constructId, unitId, data, layer, height, deRowId);
        // await this.clearFeeDeAlone(constructId, unitId);
        let deRowList = ProjectDomain.getDomain(constructId).deDomain
            .getDeTreeDepth(constructId, unitId, deRowId, [DeTypeConstants.DE_TYPE_DELIST, DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_DEFAULT]);
        let calculateMethod = data[0].calculateMethod;

        //计算顺序
        let ClassLevelSortMap = await this.calcuateClassLevelSort();
        let linshiDataArray = [];

        if (ObjectUtils.isNotEmpty(data)) {
            for (let item of data) {
                item.type = 1;

                if (ObjectUtils.isNotEmpty(item?.feeItems) && item.checked) {
                    for (let feeItem of item.feeItems) {
                        feeItem.defaultRow.relationListId = deRowId;
                        if (ObjectUtils.isNotEmpty(feeItem?.defaultRow)) {
                            let relatedDeRows = this.getDeRowsByRate(constructId, unitId, [feeItem.defaultRow.classLevel], deRowList);

                            let relatedDeRowIds = [];
                            if (ObjectUtils.isNotEmpty(relatedDeRows)) {
                                relatedDeRows.forEach(o => {
                                    if (ObjectUtils.isNotEmpty(item.baseDeList)) {
                                        if (item.baseDeList.includes(o.sequenceNbr)) {
                                            relatedDeRowIds.push(o.sequenceNbr);
                                        }
                                    } else {
                                        relatedDeRowIds.push(o.sequenceNbr);
                                    }
                                })
                                if (ObjectUtils.isEmpty(relatedDeRowIds)) {
                                    relatedDeRowIds = relatedDeRows.map(obj => obj.sequenceNbr);
                                }

                                let relatedDeRow = relatedDeRows.find(o => o.sequenceNbr === relatedDeRowIds[0]);

                                let newSort = ClassLevelSortMap.get(feeItem.defaultRow.classLevel) + 1;
                                ClassLevelSortMap.set(feeItem.defaultRow.classLevel, newSort);
                                let linshiData  = {};
                                linshiData.constructId = constructId;
                                linshiData.unitId = unitId;
                                linshiData.type = item.type;
                                linshiData.feeItem = feeItem;
                                linshiData.relatedDeRow = relatedDeRow;
                                linshiData.relatedDeRowIds = relatedDeRowIds;
                                linshiData.sort = newSort;
                                linshiDataArray.push(linshiData);


                                //创建费用定额
                                // let feeDeRow = this.createAZFeeDe(constructId, unitId, item.type, feeItem.defaultRow, relatedDeRow);
                                // feeDeRow.deCode = feeItem.defaultRow.deCode;
                                // feeDeRow.deName = feeItem.defaultRow.deName;
                                // feeDeRow.libraryCode = feeItem.defaultRow.libraryCode;
                                // feeDeRow.libraryName = feeItem.defaultRow.libraryName;
                                // feeDeRow.unit = gsAZService.COMMON_AZ_FEE_UNIT;
                                // feeDeRow.resQty = 1;
                                // feeDeRow.quantity = 1;
                                // feeDeRow.unit = gsAZService.AZ_UNIT;
                                // //创建对应人材机
                                // this.ftProcess(constructId, unitId, feeItem.defaultRow.defaultFtStyle, feeItem.defaultRow.calculateBase, feeItem.defaultRow.rate, feeItem.defaultRow.rgzb
                                //     , feeItem.defaultRow.clzb, feeItem.defaultRow.jxzb, relatedDeRowIds, feeDeRow);
                                //
                                // if (calculateMethod === 0) {
                                //     await ProjectDomain.getDomain(constructId).deDomain.notify({
                                //         constructId: constructId,
                                //         unitId: unitId,
                                //         deRowId: feeDeRow.sequenceNbr
                                //     }, true);
                                // }
                            }
                        }
                    }
                }
            }
        }

        return linshiDataArray;
    }


    createAZFeeDe(constructId,unitId,type,feeRate,relatedDeRow)
    {
        let deDomain = ProjectDomain.getDomain(constructId).deDomain;
        let  parentRow = deDomain.getDeById(relatedDeRow.parentId);
        let parentList = [];
        if (parentRow.parentId === 0) {
            parentList.push(parentRow);
        } else {
            deDomain.findParents(parentRow, parentList, [DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB, DeTypeConstants.DE_TYPE_DEFAULT]);
        }
        if(ObjectUtils.isNotEmpty(parentList))
        {
            // let parentId = parentList[0].sequenceNbr;
            let parentId = relatedDeRow.parentId;       //默认计取对应分部
            if (type === 1) {    //计取到指定分部
                if (ObjectUtils.isEmpty(feeRate.relationListId)) {
                    throw Error("未选择计取位置");
                }
                parentId = feeRate.relationListId;
            }

            let deRowModel = new StandardDeModel(constructId,unitId,Snowflake.nextId(),parentId,DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
            ProjectDomain.getDomain(constructId).deDomain.createDeRow(deRowModel);
            return deRowModel;
        }
        else
        {
            throw Error("找不到对应的父级分部.");
        }


    }
    /**
     * 分摊处理器
     * @param constructId
     * @param unitId
     * @param type
     * @param ftMethod
     * @param baseRate
     * @param rRatio
     * @param cRatio
     * @param jRatio
     * @param rowIds
     * @param feeDe
     */
    ftProcess(constructId,unitId,type,ftMethod,baseRate,rRatio,cRatio,jRatio,rowIds,feeDe)
    {
        let resourceList = [];
        let baseNumMap = this.fillBaseNum(constructId,unitId,rowIds);
        let feeResources = [];
        switch (type)
        {
            case AZFeeConstants.FT_TYPE_NONE:
            {
                let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
                let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
                let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);


                resourceR.kind = ResourceKindConstants.INT_TYPE_R;
                resourceC.kind = ResourceKindConstants.INT_TYPE_C;
                resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

                gsAZService.fillResource(feeDe,resourceR);
                gsAZService.fillResource(feeDe,resourceC);
                gsAZService.fillResource(feeDe,resourceJ);

                let baseNumR  = baseNumMap.get(ResourceKindConstants.INT_TYPE_R);
                let baseNumC  = baseNumMap.get(ResourceKindConstants.INT_TYPE_C);
                let baseNumJ  = baseNumMap.get(ResourceKindConstants.INT_TYPE_J);
                resourceR.resQty = NumberUtil.multiplyParams(baseNumR, 100 / 100, rRatio / 100).toFixed(2);
                resourceC.resQty = NumberUtil.multiplyParams(baseNumC, 100 / 100, cRatio / 100).toFixed(2);
                resourceJ.resQty = NumberUtil.multiplyParams(baseNumJ, 100 / 100, jRatio / 100).toFixed(2);

                // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * baseRate/100,2);
                // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * baseRate/100,2);
                // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * baseRate/100,2);

                ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
                ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
                ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);

                break;
            }
            case AZFeeConstants.FT_TYPE_FT:
            {
                let rcjRatioMap = new Map();
                rcjRatioMap.set(ResourceKindConstants.INT_TYPE_R,rRatio);
                rcjRatioMap.set(ResourceKindConstants.INT_TYPE_C,cRatio);
                rcjRatioMap.set(ResourceKindConstants.INT_TYPE_J,jRatio);
                //人工 材料 机械 人工+机械+材料  人工+机械 人工+材料 机械+材料

                gsAZService.ftStrategy[ftMethod](constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe);
                break;
            }
        }
    }


    ftProcess2(constructId,unitId,type,ftMethod,baseRate,rRatio,cRatio,jRatio,rowIds,feeDe)
    {

        let rcjList = [];

        let resourceList = [];
        let baseNumMap = this.fillBaseNum(constructId,unitId,rowIds);
        let feeResources = [];
        switch (type)
        {
            case AZFeeConstants.FT_TYPE_NONE:
            {
                let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
                let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
                let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);


                resourceR.kind = ResourceKindConstants.INT_TYPE_R;
                resourceC.kind = ResourceKindConstants.INT_TYPE_C;
                resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

                gsAZService.fillResource(feeDe,resourceR);
                gsAZService.fillResource(feeDe,resourceC);
                gsAZService.fillResource(feeDe,resourceJ);

                let baseNumR  = baseNumMap.get(ResourceKindConstants.INT_TYPE_R);
                let baseNumC  = baseNumMap.get(ResourceKindConstants.INT_TYPE_C);
                let baseNumJ  = baseNumMap.get(ResourceKindConstants.INT_TYPE_J);
                resourceR.resQty = NumberUtil.multiplyParams(baseNumR, 100 / 100, rRatio / 100).toFixed(2);
                resourceC.resQty = NumberUtil.multiplyParams(baseNumC, 100 / 100, cRatio / 100).toFixed(2);
                resourceJ.resQty = NumberUtil.multiplyParams(baseNumJ, 100 / 100, jRatio / 100).toFixed(2);

                // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * baseRate/100,2);
                // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * baseRate/100,2);
                // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * baseRate/100,2);

                // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
                // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
                // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);

                rcjList.push(resourceR);
                rcjList.push(resourceC);
                rcjList.push(resourceJ);

                break;
            }
            case AZFeeConstants.FT_TYPE_FT:
            {
                let rcjRatioMap = new Map();
                rcjRatioMap.set(ResourceKindConstants.INT_TYPE_R,rRatio);
                rcjRatioMap.set(ResourceKindConstants.INT_TYPE_C,cRatio);
                rcjRatioMap.set(ResourceKindConstants.INT_TYPE_J,jRatio);
                //人工 材料 机械 人工+机械+材料  人工+机械 人工+材料 机械+材料

                gsAZService.ftStrategy2[ftMethod](constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList);
                break;
            }
        }
        return rcjList;
    }

    /**
     * 基于定额填充人材机价格基数
     * @param constructId
     * @param unitId
     * @param deRowIds
     * @returns {Map<any, any>}
     */
    fillBaseNum(constructId,unitId,deRowIds)
    {
        let baseNumMap = new Map();
        let baseNumR = 0;
        let baseNumC = 0;
        let baseNumJ = 0;
        if (ObjectUtils.isNotEmpty(deRowIds)) {
            deRowIds.forEach((deId) => {
                let deRow = ProjectDomain.getDomain(constructId).deDomain.getDeById(deId);
                baseNumR += ObjectUtils.isEmpty(deRow.rdTotalSum) ? 0 : deRow.rdTotalSum;
                baseNumC += ObjectUtils.isEmpty(deRow.cdTotalSum) ? 0 : deRow.cdTotalSum;
                baseNumJ += ObjectUtils.isEmpty(deRow.jdTotalSum) ? 0 : deRow.jdTotalSum;
                baseNumMap.set(ResourceKindConstants.INT_TYPE_R, baseNumR);
                baseNumMap.set(ResourceKindConstants.INT_TYPE_C, baseNumC);
                baseNumMap.set(ResourceKindConstants.INT_TYPE_J, baseNumJ);
            });
        }
        return baseNumMap;
    }

    static fillResource(feeDe,resource)
    {
        resource.deRowId = feeDe.sequenceNbr;
        resource.displaySign = 0;
        resource.isDeResource = 0;
        resource.dePrice = 1;
        resource.marketPrice = 1;
        resource.unit = gsAZService.AZ_UNIT;
        resource.levelMark = RcjLevelMarkConstant.NO_SINK;    //不是二次解析

        switch (resource.kind)
        {
            case ResourceKindConstants.INT_TYPE_R:
            {
                resource.type = "人工费";
                resource.materialCode = "RGFTZ";
                resource.materialName = "人工费调整";
                break;
            }
            case ResourceKindConstants.INT_TYPE_C:
            {
                resource.type = "材料费";
                resource.materialCode = "CLFTZ";
                resource.materialName = "材料费调整";
                break;
            }
            case ResourceKindConstants.INT_TYPE_J:
            {

                resource.type = "机械费";
                resource.materialCode = "JXFTZ";
                resource.materialName = "机械费调整";
                break;
            }
        }

    }
    /**
     *
     * @type {{"人工+材料+机械": function(*, *, *, *): void, 人工: function(*, *, *, *): void, "机械+材料": function(*, *, *, *): void, 材料: function(*, *, *, *): void, 机械: function(*, *, *, *): void, "人工+材料": function(*, *, *, *): void}}
     */
    static ftStrategy= {
        "人工费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            resourceR.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty =  NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
        },
        "材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            resourceR.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
        },
        "机械费":(constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            resourceR.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_J), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_J), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_J), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100),2);
            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
        },
        "人工费+材料费+机械费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.addParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100));
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100));
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100));

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);

        },
        "人工费+机械费+材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.addParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100));
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100));
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100));

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);

        },
        "人工费+材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.add(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_C));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100),2);
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);

        },
        "机械费+材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe)=> {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.add(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100),2);
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
        },
        "人工费+机械费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe)=> {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.add(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100),2);
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
        }
    }


    static ftStrategy2= {
        "人工费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            resourceR.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty =  NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);

            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);
        },
        "材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            resourceR.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100),2);
            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);
        },
        "机械费":(constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            resourceR.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_J), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_J), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_J), baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100),2);
            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);
        },
        "人工费+材料费+机械费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.addParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100));
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100));
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100));

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);

        },
        "人工费+机械费+材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.addParams(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100));
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100));
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100));

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);

        },
        "人工费+材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList) => {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.add(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_C));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100),2);
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);

        },
        "机械费+材料费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList)=> {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.add(baseNumMap.get(ResourceKindConstants.INT_TYPE_C), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceC.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C)/100) * (baseRate/100),2);
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);
        },
        "人工费+机械费": (constructId,unitId,baseRate,rcjRatioMap,baseNumMap,feeDe,rcjList)=> {
            let resourceR = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_R);
            let resourceC = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_C);
            let resourceJ = new ResourceModel(feeDe.constructId,feeDe.unitId,Snowflake.nextId(),feeDe.sequenceNbr,ResourceKindConstants.INT_TYPE_J);

            resourceR.kind = ResourceKindConstants.INT_TYPE_R;
            resourceC.kind = ResourceKindConstants.INT_TYPE_C;
            resourceJ.kind = ResourceKindConstants.INT_TYPE_J;

            let baseNum = NumberUtil.add(baseNumMap.get(ResourceKindConstants.INT_TYPE_R), baseNumMap.get(ResourceKindConstants.INT_TYPE_J));
            resourceR.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R) / 100).toFixed(2);
            resourceC.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_C) / 100).toFixed(2);
            resourceJ.resQty = NumberUtil.multiplyParams(baseNum, baseRate / 100, rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J) / 100).toFixed(2);
            // resourceR.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_R)/100) * (baseRate/100),2);
            // resourceJ.resQty = NumberUtil.numberScale((baseNumMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (rcjRatioMap.get(ResourceKindConstants.INT_TYPE_J)/100) * (baseRate/100),2);

            gsAZService.fillResource(feeDe,resourceR);
            gsAZService.fillResource(feeDe,resourceC);
            gsAZService.fillResource(feeDe,resourceJ);

            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceR);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceC);
            // ProjectDomain.getDomain(constructId).resourceDomain.createResource(unitId,feeDe.sequenceNbr,resourceJ);
            rcjList.push(resourceR);
            rcjList.push(resourceC);
            rcjList.push(resourceJ);
        }
    }

    /**
     *
     * @param constructId
     * @param unitId
     */
    async clearFeeDe(constructId,unitId)
    {
        // let AZDeList = ProjectDomain.getDomain(constructId).deDomain
        //     .getDeTreeDepth(constructId, unitId, null, [DeTypeConstants.DE_TYPE_ANZHUANG_FEE]);

        let AZDeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
        if(ObjectUtils.isNotEmpty(AZDeList)) {
            for (let deRow of AZDeList) {
                if (ObjectUtils.isEmpty(deRow.calculateMethod) && deRow.price !== 0 && deRow.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE) {       //单独计取的不删除,单价为0的不删除
                    await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(deRow.deRowId);
                }
            }
        }
        return true;
    }

    /**
     *
     * @param constructId
     * @param unitId
     */
    async clearFeeDeYS(constructId,unitId)
    {
        // let AZDeList = ProjectDomain.getDomain(constructId).deDomain
        //     .getDeTreeDepth(constructId, unitId, null, [DeTypeConstants.DE_TYPE_ANZHUANG_FEE]);

        let AZDeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
        if(ObjectUtils.isNotEmpty(AZDeList)) {
            for (let deRow of AZDeList) {
                if (ObjectUtils.isEmpty(deRow.calculateMethod)  && deRow.libraryCode === AnZhuangJiQqConstants.AZ_JIQU_NAME_YS_CODE) {       //单独计取的不删除,单价为0的不删除
                    await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(deRow.deRowId);
                }
            }
        }
        return true;
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @param deRowId
     */
    async clearFeeDeAlone(constructId,unitId,deRowId)
    {
        let AZDeList = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && item.unitId === unitId && item.parentId === deRowId);
        if(ObjectUtils.isNotEmpty(AZDeList)) {
            for (let deRow of AZDeList) {
                await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(deRow.deRowId);
            }
        }
        return true;
    }

    /**
     *
     * @param constructId
     * @param unitId
     * @returns {null}
     */
    async getCaches(constructId, unitId) {
        let mapOrObject = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + unitId);
        if (mapOrObject instanceof Map) {
            mapOrObject = Object.fromEntries(mapOrObject);
        }
        return mapOrObject;
    }


    async checkJiquMethod(constructId, unitId) {
        let anzhaungDeList = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtils.isEmpty(item.calculateMethod));
        if (ObjectUtils.isEmpty(anzhaungDeList)) {
            //从未计取过，不做任何处理
            return 0;
        }
        let mapOrObject = await this.getCachesType(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE1);
        let mapOrObject1 = await this.getCachesType(constructId, unitId, AnZhuangJiQqConstants.AZ_JIQU_TYPE2);
        if (ObjectUtils.isEmpty(mapOrObject) && ObjectUtils.isEmpty(mapOrObject1)) {
            //没有缓存数据，不做处理
            return 0;
        }

        let change = false;
        if (ObjectUtils.isNotEmpty(mapOrObject)) {
            mapOrObject.data.forEach(o => {
                if (o.type === AnZhuangJiQqConstants.AnZhuangJiQq_ZDFB) {
                    //修改过规则
                    change = true;
                }
            });
        }
        if (ObjectUtils.isNotEmpty(mapOrObject1)) {
            mapOrObject1.data.forEach(o => {
                if (o.type === AnZhuangJiQqConstants.AnZhuangJiQq_ZDFB) {
                    //修改过规则
                    change = true;
                }
            });
        }


        if (change) {
            //修改过对应分部->指定分部
            return 2;
        } else {
            //未修改过
            return 1;
        }
    }



    /**
     *
     * @param constructId
     * @param unitId
     * @returns {null}
     */
    async getCachesYS(constructId, unitId) {
        let resultObj = {};
        resultObj.constructId = constructId;
        resultObj.unitId = unitId;
        resultObj.layer = 0;
        resultObj.height = 0;


        let result = [];
        let anzhuang1 = {};
        let mapOrObject = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + unitId);
        if (ObjectUtils.isNotEmpty(mapOrObject)) {
            if (mapOrObject instanceof Map) {
                mapOrObject = Object.fromEntries(mapOrObject);
            }
            anzhuang1.name = AnZhuangJiQqConstants.AZ_JIQU_NAME;
            anzhuang1.list = mapOrObject.data;

            resultObj.constructId = mapOrObject.constructId;
            resultObj.unitId = mapOrObject.unitId;
            resultObj.layer = mapOrObject.layer;
            resultObj.height = mapOrObject.height;

            result.push(anzhuang1);
        }

        let anzhuang2 = {};
        let mapOrObject2 = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + unitId);
        if (ObjectUtils.isNotEmpty(mapOrObject2)) {
            if (mapOrObject2 instanceof Map) {
                mapOrObject2 = Object.fromEntries(mapOrObject2);
            }
            anzhuang2.name = AnZhuangJiQqConstants.AZ_JIQU_NAME_YS;
            anzhuang2.list = mapOrObject2.data;

            resultObj.constructId = mapOrObject2.constructId;
            resultObj.unitId = mapOrObject2.unitId;
            resultObj.layer = mapOrObject2.layer;
            resultObj.height = mapOrObject2.height;

            result.push(anzhuang2);
        }

        if(ObjectUtils.isEmpty(result)){
            return null;
        }


        resultObj.data = result;
        return resultObj;
    }



    /**
     *
     * @param constructId
     * @param unitId
     * @returns {null}
     */
    async updateCaches(constructId, unitId) {
        let mapOrObject = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + unitId);
        if (ObjectUtils.isNotEmpty(mapOrObject)) {
            if (mapOrObject instanceof Map) {
                mapOrObject = Object.fromEntries(mapOrObject);
            }

            for(let item of mapOrObject.data){
                if(item.type === AnZhuangJiQqConstants.AnZhuangJiQq_ZDFB){
                    item.type = AnZhuangJiQqConstants.AnZhuangJiQq_DYFB;
                }
            }

            //cache放入缓存
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + unitId, mapOrObject);
        }

        let mapOrObject2 = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + unitId);
        if (ObjectUtils.isNotEmpty(mapOrObject2)) {
            if (mapOrObject2 instanceof Map) {
                mapOrObject2 = Object.fromEntries(mapOrObject2);
            }

            for(let item of mapOrObject.data){
                if(item.type === AnZhuangJiQqConstants.AnZhuangJiQq_ZDFB){
                    item.type = AnZhuangJiQqConstants.AnZhuangJiQq_DYFB;
                }
            }

            //cache放入缓存
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + unitId, mapOrObject2);
        }
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @returns {null}
     */
    async getCachesType(constructId, unitId, type) {

        if (type === AnZhuangJiQqConstants.AZ_JIQU_TYPE1) {
            let mapOrObject = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + unitId);
            if (mapOrObject instanceof Map) {
                mapOrObject = Object.fromEntries(mapOrObject);
            }
            return mapOrObject;
        } else if (type === AnZhuangJiQqConstants.AZ_JIQU_TYPE2) {
            let mapOrObject2 = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE_YS + constructId + unitId);
            if (mapOrObject2 instanceof Map) {
                mapOrObject2 = Object.fromEntries(mapOrObject2);
            }
            return mapOrObject2;
        }
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param deRowId
     * @returns {null}
     */
    async getCachesAlone(constructId, unitId, deRowId) {
        let mapOrObject = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_AZ_CACHE + constructId + unitId + deRowId);
        if (mapOrObject instanceof Map) {
            mapOrObject = Object.fromEntries(mapOrObject);
        }
        return mapOrObject;
    }

}
gsAZService.toString = () => '[class gsAZService]';
module.exports = gsAZService;
