const {Service} = require('../../../core');
const { Snowflake } = require('../../../electron/utils/Snowflake');
const {ParamUtils} = require("../../../core/core/lib/utils/ParamUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require("../domains/ProjectDomain");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const UnitUtils = require("../core/tools/UnitUtils");
const PropertyUtil = require("../domains/utils/PropertyUtil");
const BaseDomain = require("../domains/core/BaseDomain");
const {FreeRateModel} = require("../models/FreeRateModel");
const {GsQuantitiesModel} = require("../models/GsQuantitiesModel");
const DeTypeConstants = require("../constants/DeTypeConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");

class GsQuantitiesService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    // 计算说明 mathIllustrate
    // 计算式 mathFormula
    // 结果 mathResult
    // 变量引用 variables
    // 累加标识 accumulateFlag
    Default_Lines = () => {
        return [
            {"mathIllustrate": "","mathFormula": "0", "mathResult": 0, "variables":"GCGM", "accumulateFlag":0, "quotaListId": null},
            {"mathIllustrate": "","mathFormula": "", "mathResult": null, "variables":null, "accumulateFlag":1, "quotaListId": null},
            {"mathIllustrate": "","mathFormula": "", "mathResult": null, "variables":null, "accumulateFlag":1, "quotaListId": null},
            {"mathIllustrate": "","mathFormula": "", "mathResult": null, "variables":null, "accumulateFlag":1, "quotaListId": null},
            {"mathIllustrate": "","mathFormula": "", "mathResult": null, "variables":null, "accumulateFlag":1, "quotaListId": null},
            {"mathIllustrate": "","mathFormula": "", "mathResult": null, "variables":null, "accumulateFlag":1, "quotaListId": null}
        ]
    };

    Empty_Line = () => {
        return {"mathIllustrate": "","mathFormula": "", "mathResult": null, "variables":null, "accumulateFlag":0}
    }

    /**
     * 初始化
     * @param pointLine
     */
    initDatas(pointLine) {
        let initData = this.Default_Lines();
        for (let i = 0 ; i < initData.length ; ++i) {
            initData[i].sequenceNbr = Snowflake.nextId();
            initData[i].quotaListId = pointLine.quotaListId;
        }
        pointLine.quantities = initData;
        pointLine.quantityVariableName = "GCLMXHJ";
        pointLine.quantityVariableValue = 0;
    }

    /**
     * 数据上移
     * @param pointLine
     * @param quantitiesLineId
     */
    moveUp(pointLine, quantitiesLineId) {
        let buffer;
        for (let i= 0; i< pointLine.quantities.length; ++i) {
            if (quantitiesLineId === pointLine.quantities[i].sequenceNbr) {
                if (0 === i) { // 第一行不上移
                    return;
                }
                buffer = pointLine.quantities[i-1];
                pointLine.quantities[i-1] = pointLine.quantities[i];
                pointLine.quantities[i] = buffer;
                return;
            }
        }
    }

    /**
     * 数据下移
     * @param pointLine
     * @param quantitiesLineId
     */
    moveDown(pointLine, quantitiesLineId) {
        let buffer;
        for (let i= 0; i< pointLine.quantities.length; ++i) {
            if (quantitiesLineId === pointLine.quantities[i].sequenceNbr) {
                if (pointLine.quantities.length === (i+1)) { // 最后一行不下移
                    return;
                }
                buffer = pointLine.quantities[i+1];
                pointLine.quantities[i+1] = pointLine.quantities[i];
                pointLine.quantities[i] = buffer;
                return;
            }
        }
    }

    /**
     * 获取工程量明细
     * @param pointLine
     * @return {*}
     */
    getQuantitiesList(pointLine) {
        if (ObjectUtils.isEmpty(pointLine)) {
            return []
        }
        //判断定额是否是最父级定额
        if (!pointLine.quantities || pointLine.quantities.length === 0) {
            if (pointLine.parent.parentId === 0) {
                this.initDatas(pointLine);
            } else {
                pointLine.quantities = [];
            }
        }
        return pointLine.quantities;
    }

    /**
     * 新增工程量明细
     * @param pointLine
     * @param quantitiesInfo
     * @return {{variables: number, mathResult: number, mathFormula: string, accumulateFlag: number, mathIllustrate: string}}
     */
    add(pointLine, selectId) {
        let newLine = this.Empty_Line();
        newLine.sequenceNbr = Snowflake.nextId();
        let selectIndex = null;
        //查找选中行 位置
        for(let i = 0; i < pointLine.quantities.length; i++){
            if(pointLine.quantities[i].sequenceNbr === selectId){
                selectIndex = i;
                break;
            }
        }
        if(selectIndex !== null){
            pointLine.quantities.splice(selectIndex, 0, newLine)
        }else{
            pointLine.quantities.push(newLine);
        }



        return newLine;
    }

    /**
     * 新增工程量明细 往后插一行
     * @param pointLine
     * @param quantitiesInfo
     * @return {{variables: number, mathResult: number, mathFormula: string, accumulateFlag: number, mathIllustrate: string}}
     */
    addAfter(pointLine, selectId) {
        let newLine = this.Empty_Line();
        newLine.sequenceNbr = Snowflake.nextId();
        let selectIndex = null;
        //查找选中行 位置
        for(let i = 0; i < pointLine.quantities.length; i++){
            if(pointLine.quantities[i].sequenceNbr === selectId){
                selectIndex = i;
                break;
            }
        }
        if(selectIndex !== null){
            pointLine.quantities.splice(selectIndex + 1, 0, newLine)
        }else{
            pointLine.quantities.push(newLine);
        }
        return newLine;
    }

    /**
     * 删除工程量明细
     * @param pointLine
     * @param quantitiesLineId
     */
    async delete(pointLine, quantitiesLineId) {
        let deleteQt = pointLine.quantities.filter(line=>line.sequenceNbr===quantitiesLineId)[0];
        pointLine.quantities = pointLine.quantities.filter(line=>line.sequenceNbr!==quantitiesLineId);
        if (deleteQt.variables === null && deleteQt.mathResult === null) {
            return
        }

        // 回填清单定额
        this._fillBack(pointLine, pointLine.quantities);
        // 精度处理
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(pointLine.constructId);
        pointLine.quantity = NumberUtil.numberScale(pointLine.quantity, precision.EDIT.DE.quantity);
        let params = {
            constructId: pointLine.constructId,
            unitId: pointLine.unitId,
            deId: pointLine.quotaListId,
            quantity: pointLine.quantity,
            resetDeQuantities: false,
            quantityExpression: pointLine.quantityExpression
        }
        await this._updateDeQuantity(params)
    }

    async upDate(pointLine, sequenceNbr, mathFormula, mathIllustrate, mathResult, variables, accumulateFlag, type, constructId, singleId, unitId) {
        let quantities = pointLine.quantities;
        let quan = quantities.filter(f=>f.sequenceNbr === sequenceNbr)[0];

        this._replaceAllRelaseLine(quantities, quan.variables, variables);

        quan.mathIllustrate = mathIllustrate;
        if (ObjectUtils.isEmpty(mathFormula)) {
            quan.mathFormula = mathFormula;
        }else {
            // 中文符号处理
            quan.mathFormula = ObjectUtils.chineseChar2englishChar(mathFormula.toUpperCase());
        }
        quan.mathResult = mathResult;
        if (ObjectUtils.isEmpty(variables)) {
            quan.variables = variables;
        }else {
            quan.variables = variables.toUpperCase();
        }
        quan.accumulateFlag = accumulateFlag;
        // 计算四则云算结果
        this._doCaculate(quan, quantities);
        // 重新计算全部表达式
        this._recaculateAll(quan, quantities);

        // 精度处理
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        for (let item of quantities) {
            if (ObjectUtils.isNotEmpty(item.mathResult)){
                item.mathResult = NumberUtil.numberScale(item.mathResult, precision.DETAIL.QUANTITIES.mathResult);
            }
        }

        // 回填清单定额
        this._fillBack(pointLine, quantities);
        pointLine.quantity = NumberUtil.numberScale(pointLine.quantity, precision.EDIT.DE.quantity);
        let params = {
            constructId,
            unitId,
            deId: pointLine.quotaListId,
            quantity: pointLine.quantity,
            resetDeQuantities: false,
            quantityExpression: pointLine.quantityExpression
        }
        await this._updateDeQuantity(params)
    }

    async _updateDeQuantity(params) {
        let {constructId, unitId, deId, quantity, quantityExpression} = params
        let de = ProjectDomain.getDomain(constructId).deDomain.getDeById(deId);
        if(ObjectUtils.isNotEmpty(de)){
            let deQuantityExpression = de.quantityExpression
            if (String(deQuantityExpression).includes("GCLMXHJ")) {
                de.quantity = quantity
                params.quantity = deQuantityExpression
            }
            await this.service.PreliminaryEstimate.gsDeService.updateQuantity(params);
            if (String(deQuantityExpression).includes("GCLMXHJ")) {
                de.quantityExpression = de.originalQuantity = deQuantityExpression;
            } else {
                de.quantityExpression = de.originalQuantity = quantityExpression;
            }
        }
    }

    /**
     * 获取工程量明细计算的结果
     * 重新计算工程量
     * @param constructId
     * @param unitId
     * @param isGcgm
     * @returns {Promise<number>}
     */
    async recaculateQuantityByUnit(constructId, unitId, isGcgm) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        //联动工程量明细
        let deList = projectDomain.deDomain.getDeTree(p => p.unitId === unitId);
        if (ObjectUtil.isNotEmpty(deList)) {
            for (let deRow of deList) {
                let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
                let pointLine = quantitiesMap?.get(unitId)?.get(deRow.sequenceNbr)
                let quantities = pointLine?.quantities
                if (ObjectUtils.isNotEmpty(quantities)) {
                    if (isGcgm === true) {
                        let line = quantities.filter(item => item.accumulateFlag!==0 && item.variables !== "GCGM").find(item => item.mathFormula.includes("GCGM"));
                        if (ObjectUtils.isNotEmpty(line)) {
                            await this.recaculateQuantityToDe(constructId, unitId, deRow.sequenceNbr);
                        }
                    }
                }
            }
        }

    }

    /**
     * 获取工程量明细计算的结果
     * 重新计算工程量
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<number>}
     */
    async recaculateQuantityToDe(constructId, unitId, deId) {
        let result = 0;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap?.get(unitId)
        let pointLine = unitQuantiesMap?.get(deId);
        let quantities = pointLine?.quantities
        if (ObjectUtils.isEmpty(quantities)) {
            return result
        }
        // 更新工程规模
        await this.updateGcgm(constructId, unitId, pointLine);
        this._recaculateQuantities(quantities)
        // 精度处理
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        for (let item of quantities) {
            if (ObjectUtils.isNotEmpty(item.mathResult)){
                item.mathResult = NumberUtil.numberScale(item.mathResult, precision.DETAIL.QUANTITIES.mathResult);
            }
        }
        // 回填清单定额
        await this._fillBack(pointLine, quantities);
        // 精度处理
        pointLine.quantity = NumberUtil.numberScale(pointLine.quantity, precision.EDIT.DE.quantity);
        let params = {
            constructId: pointLine.constructId,
            unitId: pointLine.unitId,
            deId: pointLine.quotaListId,
            quantity: pointLine.quantity,
            resetDeQuantities: false,
            quantityExpression: pointLine.quantityExpression
        }
        await this._updateDeQuantity(params)
        return pointLine.quantityVariableValue;
    }


    /**
     *
     * @param constructId
     * @param unitId
     * @param deRowId
     * @param quantity
     * @param quantityExpression
     * @returns {Promise<{}>}
     */
    async updateQuantity(constructId, unitId, deRowId, quantity, quantityExpression, changeResQty = true,filterTempRemoveRow = true) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let deRow = deDomain.ctx.deMap.getNodeById(deRowId);
        if (!ObjectUtil.isEmpty(deRow)) {
            let  unitNbr = UnitUtils.removeCharter(deRow.unit);
            if(ObjectUtil.isNotEmpty(unitNbr)) {
                deRow.originalQuantity = quantity;
                deRow.quantity = NumberUtil.numberScale(NumberUtil.divide(quantity,unitNbr),5);
            }else {
                deRow.originalQuantity = quantity;
                deRow.quantity = quantity;
            }
            if(changeResQty){
                let parent = deDomain.ctx.deMap.getNodeById(deRow.parentId);
                //父级工程量为0 子级也为0
                if( parent.quantity === 0){
                    deRow.quantity = 0;
                }

                if (parent.quantity > 0 && parent.type !== DeTypeConstants.DE_TYPE_DEFAULT
                    && parent.type !== DeTypeConstants.DE_TYPE_FB
                    && parent.type !== DeTypeConstants.DE_TYPE_ZFB){
                    deRow.resQty = NumberUtil.numberScale(NumberUtil.divide(deRow.quantity,parent.quantity),3);
                    //消耗量保留3为后0,重置工程量为0
                    if(deRow.resQty === 0){
                        deRow.quantity = 0;
                    }
                }
            }

            deRow.quantityExpression = quantityExpression ;
            try {
                await deDomain.notify({ constructId, unitId, deRowId },true, filterTempRemoveRow);
                await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    unitId: unitId,
                    constructMajorType: deRow.libraryCode
                });
            } catch (error) {
                console.error("捕获到异常:", error);
            }
            return PropertyUtil.filterObjectProperties(deRow, BaseDomain.avoidProperty);
        }
    }

    /**
     * 计算四则云算
     */
    _doCaculate(caculateLine, all) {
        let varMap = new Map();
        let variables = [];
        for (let i = 0 ; i < all.length ; ++i) {
            let key = all[i].variables?all[i].variables:"";
            let value = all[i].mathResult?all[i].mathResult:0;
            varMap.set(key, value);
            if (key && key !== "") {
                variables.push(key);
            }
        }
        let evalStr = caculateLine.mathFormula;
        for (let j = 0 ; j<variables.length; ++j) {
            // let reg = new RegExp("\\b"+variables[j]+"\\b", "g");
            // evalStr = evalStr.replaceAll(reg, varMap.get(variables[j]));
            evalStr = this._replaceWholeWordAll(evalStr, variables[j], varMap.get(variables[j]));
        }
        let res = NumberUtil.numberScale6(eval(evalStr));
        caculateLine.mathResult = res;
    }

    _replaceWholeWordAll(str = "", srcWord, replacement){
        let reg = new RegExp("\\b"+srcWord+"\\b", "g");
        return str.replaceAll(reg, replacement);
    }

    _replaceAllRelaseLine(quantities = [], oriVar, newVar){
        quantities.forEach((quan) => {
            if(quan.mathFormula){
                quan.mathFormula = this._replaceWholeWordAll(quan.mathFormula, oriVar, newVar);
            }
        });
    }

    _recaculateAll(begin, quantities) {
        if(!begin.variables) {
            return;
        }
        // 将更改行和常量行设置为已计算过的
        begin.caculated = true;
        for (let i = 0 ; i<quantities.length; ++i) {
            if (!Number.isNaN(Number.parseFloat(quantities[i].mathFormula))) {
                quantities[i].caculated = true;
            }
        }
        this._doReCaculate(quantities);
        // 数据重置 为以后使用
        for (let i = 0 ; i<quantities.length; ++i) {
            quantities[i].caculated = undefined;
        }
    }

    _doReCaculate(quantities) {
        // 不断循环，每次找到 仅直接引用了 caculated = true的行
        // 直到所有行都 caculated = true，或者找不到引用行
        let nextLines = this._findRelaseLine(quantities);
        while(nextLines && nextLines.length>0) {
            for (let i = 0; i<nextLines.length; ++i) {
                this._doCaculate(nextLines[i], quantities);
                nextLines[i].caculated = true;
            }
            nextLines = this._findRelaseLine(quantities);
        }
    }

    _findRelaseLine(quantities) {
        // 计算过的被引用数据的集合
        let caculatedValues = {};
        for (let i = 0 ; i < quantities.length ; ++i) {
            let q = quantities[i];
            if (q.caculated) {
                caculatedValues[q.variables] = 1;
            }
        }
        // 从没被算过的数据中寻找，直接引用了被引用数据的数据
        let reg = new RegExp("\\b\\w{1,}\\b","g");
        let nextCaculates = [];
        for (let i = 0 ; i < quantities.length ; ++i) {
            let cq = quantities[i];
            if (cq.caculated) {
                continue;
            }
            // 每个未被计算的行拿到当前行引用的东西
            let vs = cq.mathFormula.match(reg);
            if (!vs) {
                vs = [];
            }
            let refs = [];
            for (let j = 0 ; j < vs.length ; ++j) {
                if (Number.isNaN(Number.parseFloat(vs[j]))) { //排除数字
                    refs.push(vs[j]);
                }
            }

            let allRefIsCaculated = true;
            for (let k = 0 ; k < refs.length ; ++k) {
                if(!caculatedValues[refs[k]]) {
                    allRefIsCaculated = false;
                    break;
                }
            }
            if (allRefIsCaculated) {
                if (cq.mathFormula && cq.mathFormula !== "") {
                    nextCaculates.push(cq);
                }
            }
        }

        return nextCaculates;
    }

    /**
     * 回填清单定额
     * @param pointLine
     * @param quantities
     * @private
     */
    async _fillBack(pointLine, quantities) {
        let res = 0;
        for (let i = 0; i < quantities.length; ++i) {
            if (quantities[i].mathResult && quantities[i].accumulateFlag === 1) {
                res += quantities[i].mathResult;
            }
        }

        let unit = Number.parseInt(pointLine.unit);
        if (Number.isNaN(unit)) {
            unit = 1;
        }
        if(ObjectUtils.isNotEmpty(pointLine.quantityExpression) && pointLine.quantityExpression.indexOf('GCLMXHJ')!=-1){
            pointLine.quantityVariableValue = NumberUtil.numberScale2(eval(pointLine.quantityExpression.replace('GCLMXHJ',res))) ;
        }else {
            pointLine.quantityVariableValue = res;
            pointLine.quantityExpression = 'GCLMXHJ';
            if(pointLine.quantityExpression .indexOf('QDL')!=-1){
                pointLine.quantityVariableValue =NumberUtil.numberScale2(eval(pointLine.quantityExpression.replace('QDL',res)))
            }
        }
        pointLine.quantityVariableName = "GCLMXHJ";
        //pointLine.quantity = pointLine.quantityVariableValue/unit;
        pointLine.quantity =NumberUtil.numberScale6(NumberUtil.divide(pointLine.quantityVariableValue,1))
        //更新定额费用代码
        let zmPriceCodes = [
            {code: 'GCLMXHJ',price: pointLine.quantity}
        ]
        await this.service.PreliminaryEstimate.gsDeService.setDeCostCode(pointLine.constructId, pointLine.unitId, pointLine.quotaListId, zmPriceCodes);
    }

    /**
     * 工程量明细
     * @param projectQuantitiesMap
     */
    transGclmx(projectQuantitiesMap) {
        projectQuantitiesMap = ObjectUtils.convertObjectToMap(projectQuantitiesMap);
        for (let [key, unitQuantitiesMap] of projectQuantitiesMap) {
            unitQuantitiesMap = ObjectUtils.convertObjectToMap(unitQuantitiesMap);
            for (let [key, gsQuantitiesModel] of unitQuantitiesMap) {
                gsQuantitiesModel = ObjectUtils.copyProp(gsQuantitiesModel, new GsQuantitiesModel());
                gsQuantitiesModel.quantities = ObjectUtils.convertMapToArrayAndObject(gsQuantitiesModel.quantities);
                if (ObjectUtils.isNotEmpty(gsQuantitiesModel.zmVariableRuleList)) {
                    gsQuantitiesModel.zmVariableRuleList = ObjectUtils.convertMapToArrayAndObject(gsQuantitiesModel.zmVariableRuleList);
                }
                unitQuantitiesMap.set(key, gsQuantitiesModel);
            }
            projectQuantitiesMap.set(key, unitQuantitiesMap);
        }
        return projectQuantitiesMap;
    }

    /**
     * 复制粘贴
     * @param pointLine
     * @param copyIds
     */
    copy(pointLine, copyIds) {

    }


    /**
     * 获取工程量明细计算的结果
     * 重新计算工程量
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<number>}
     */
    async recaculateQuantity(constructId, unitId, deId) {
        let result = 0;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap?.get(unitId)
        let pointLine = unitQuantiesMap?.get(deId);
        let quantities = pointLine?.quantities
        if (ObjectUtils.isEmpty(quantities)) {
            return result
        }
        // 更新工程规模
        await this.updateGcgm(constructId, unitId, pointLine);
        this._recaculateQuantities(quantities)
        // 精度处理
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        for (let item of quantities) {
            if (ObjectUtils.isNotEmpty(item.mathResult)){
                item.mathResult = NumberUtil.numberScale(item.mathResult, precision.DETAIL.QUANTITIES.mathResult);
            }
        }
        // 回填清单定额
        await this._fillBack(pointLine, quantities);
        return pointLine.quantityVariableValue;
    }


    /**
     * 更新工程规模
     * @param constructId
     * @param unitId
     * @param pointLine
     * @returns {Promise<void>}
     */
    async updateGcgm(constructId, unitId, pointLine) {
        // 更新工程规模
        let gcgmParams = {
            constructId,
            unitId,
            type: "13",
            levelType: 3
        }
        let gctz = await this.service.PreliminaryEstimate.gsOverviewService.getList(gcgmParams);
        let gcgm = gctz.find(item => item.name === '工程规模')?.context
        let gcgmQuantityLine = pointLine.quantities.find(item => item.variables  === 'GCGM')
        if (ObjectUtils.isNotEmpty(gcgmQuantityLine) && gcgm !== gcgmQuantityLine?.mathResult) {
            gcgmQuantityLine.mathResult = gcgm;
        }
    }

    /**
     * 所有工程量重新计算
     * @param quantities
     * @private
     */
    _recaculateQuantities(quantities) {
        // 将常量行设置为已计算过的
        for (let i = 0 ; i<quantities.length; ++i) {
            if (!Number.isNaN(Number.parseFloat(quantities[i].mathFormula))) {
                quantities[i].caculated = true;
            }
        }
        this._doReCaculate(quantities);
        // 数据重置 为以后使用
        for (let i = 0 ; i<quantities.length; ++i) {
            quantities[i].caculated = undefined;
        }
    }
}

GsQuantitiesService.toString = () => "[class GsQuantitiesService]"
module.exports = GsQuantitiesService
