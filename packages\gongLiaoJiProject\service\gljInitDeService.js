const {Service} = require('../../../core');
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {GsQuantitiesModel} = require("../models/GsQuantitiesModel");
const {GsConversionModel} = require("../models/GsConversionModel");
const { ObjectUtil } = require('../../../common/ObjectUtil');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {StandardConvertMod} = require("../enums/ConversionSourceEnum");
const {GljDeContentModel} = require("../models/GljDeContentModel");
const DeTypeConstants = require('../constants/DeTypeConstants');
const WildcardMap = require("../core/container/WildcardMap");
const GsProjectSettingEnum = require("../enums/GljProjectSettingEnum");

class GljInitDeService extends Service {

    constructor(ctx) {
        super(ctx);

    }

    /**
     * 定额初始化
     */
    async init(deModel) {

        // 工程量初始化
        await this.initDeQuantities(deModel);
        // 费率信息初始化
        await this.initDeFree(deModel);
        // 标准换算初始化
        await this.initDeConversion(deModel);
        // 说明信息初始化
        await this.initDeContent(deModel);
        // 定额费用代码 DE_COST_CODE
        await this.initDeCostCode(deModel);
    }

    /**
     * 删除定额
     * @param deModel
     * @returns {Promise<void>}
     */
    async remove(deModel) {
        // 删除工程量明细
        await this.removeQuantities(deModel);
        // 删除标准换算
        await this.removeConversion(deModel);
        //删除 说明信息
        await this.removeDeContent(deModel);
        //删除 定额费用代码
        await this.removeDeCostCode(deModel);
    }

    /**
     * 标准换算初始化
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeConversion(deModel) {
        let businessMap = ProjectDomain.getDomain(deModel.constructId)?.functionDataMap;
        let objMap = businessMap?.get(FunctionTypeConstants.PROJECT_SETTING);
        let mainMatConvertMod = objMap?.get(GsProjectSettingEnum.FACTOR_EFFECT);

        let gsConversionModel = new GsConversionModel();
        gsConversionModel.constructId = deModel.constructId;
        gsConversionModel.unitId = deModel.unitId;
        gsConversionModel.sequenceNbr = deModel.sequenceNbr;
        gsConversionModel.deId = deModel.sequenceNbr;
        gsConversionModel.type = deModel.type;
        gsConversionModel.standardId = deModel.standardId;
        gsConversionModel.libraryCode = deModel.libraryCode;
        gsConversionModel.mainMatConvertMod  = ObjectUtil.isNotEmpty(mainMatConvertMod)? mainMatConvertMod:true;
        gsConversionModel.standardConvertMod = StandardConvertMod.Current;
        gsConversionModel.isConversion = false;
        gsConversionModel.conversionInfo = [];
        let params = {
            constructId: deModel.constructId,
            unitId: deModel.unitId,
            standardDeId: deModel.standardDeId,
            fbFxDeId: deModel.sequenceNbr,
        }
        let conversionList = await this.service.gongLiaoJiProject.gljRuleDetailFullService.initStandardConvertList(params);
        gsConversionModel.conversionList = conversionList;
        gsConversionModel.originConversionList = ConvertUtil.deepCopy(conversionList);

        let defaultConcersions = await this.service.gongLiaoJiProject.gljRuleDetailFullService.initDef();
        gsConversionModel.defaultConcersions = defaultConcersions;
        gsConversionModel.originDefaultConcersions = ConvertUtil.deepCopy(defaultConcersions);

        let conversionMap = await ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(deModel.unitId);
        unitConversionMap?.set(deModel.sequenceNbr, gsConversionModel);
    }

    /**
     * 工程量初始化
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeQuantities(deModel) {
        let gsQuantitiesModel = new GsQuantitiesModel();
        gsQuantitiesModel.quotaListId = deModel.sequenceNbr;
        gsQuantitiesModel.unitId = deModel.unitId;
        gsQuantitiesModel.constructId = deModel.constructId;
        if (ObjectUtil.isNotEmpty(deModel.deCode)) {
            await this.service.gongLiaoJiProject.gljQuantitiesService.initDatas(gsQuantitiesModel);
        } else {
            gsQuantitiesModel.quantities = []
        }
        let quantitiesMap = ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        if(ObjectUtil.isNotEmpty(quantitiesMap)){
            let unitQuantiesMap = quantitiesMap.get(deModel.unitId)
            if (deModel.isAppendBaseDe === true && ObjectUtils.isNotEmpty(unitQuantiesMap?.get(deModel.sequenceNbr)?.quantities)) {
            } else {
                gsQuantitiesModel.zmVariableRuleList = unitQuantiesMap?.get(deModel.sequenceNbr)?.zmVariableRuleList
                unitQuantiesMap?.set(deModel.sequenceNbr, gsQuantitiesModel);
            }
        }
        deModel.isAppendBaseDe = false;
        //更新定额费用代码
        let zmPriceCodes = [
            {code: 'GCLMXHJ',price: 0}
        ]
        await this.service.gongLiaoJiProject.gljDeService.setDeCostCode(deModel.constructId, deModel.unitId, deModel.sequenceNbr, zmPriceCodes);

    }

    /**
     * 费率信息初始化
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeFree(deModel) {
        if(deModel.type == "-1"){
            return;
        }
        if(ObjectUtil.isEmpty(deModel.costFileCode)){
            // 人材机的随主工程
            if(deModel.type === DeTypeConstants.DE_TYPE_RESOURCE || deModel.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
                // 获取项目单位的qf类型
                let unitProject = ProjectDomain.getDomain(deModel.constructId).getProjectById(deModel.unitId);
                deModel.qfCode = unitProject.qfMajorType;
            }
            if(ObjectUtil.isEmpty(deModel.qfCode)){
                let feeItem  =  ObjectUtils.getMapWithKeysStartingWith2(ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB),deModel.unitId);
                if(ObjectUtils.isNotEmpty(feeItem)){
                    deModel.costMajorName = feeItem[0].freeProfession;
                    // 取费文件id
                    deModel.costFileCode = feeItem[0].qfCode;
                }
            }else{

                let feeItem = await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFile(deModel.qfCode);
                if(ObjectUtils.isNotEmpty(feeItem)){
                    deModel.costMajorName = feeItem.qfName;
                    deModel.costFileCode = feeItem.qfCode;
                }
            }
        }
        // let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(deModel.constructId, deModel.unitId);
        // let baseDeLibraryModel = await this.service.gongLiaoJiProject.gljBaseDeLibraryService.getByLibraryCode(unitProject.constructMajorType);
        // if(ObjectUtil.isEmpty(deModel.costFileCode)){
        //     // 取费专业名称
        //     deModel.costMajorName = baseDeLibraryModel.projectType
        //     // 取费文件id
        //     deModel.costFileCode = unitProject.constructMajorType
        // }
    }

    /**
     * 说明信息初始化
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeContent(deModel) {
        let gljDeContentModel = new GljDeContentModel();
        gljDeContentModel.constructId = deModel.constructId;
        gljDeContentModel.unitId = deModel.unitId;
        gljDeContentModel.deId = deModel.sequenceNbr;
        gljDeContentModel.standardId = deModel.standardId;
        if (ObjectUtil.isNotEmpty(deModel.deCode)) {
            let deJobContentModel = await this.service.gongLiaoJiProject.gljBaseDeJobContentService.getDeJobContentByDeId(deModel.standardId);
            gljDeContentModel.deJobContent = deJobContentModel ? deJobContentModel.deJobContent : ''
        } else {
            gljDeContentModel.deJobContent = ''
        }
        let deContentMap = ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DE_CONTENT);
        if(ObjectUtil.isNotEmpty(deContentMap)){
            let unitdeContentMap = deContentMap.get(deModel.unitId)
            unitdeContentMap?.set(deModel.sequenceNbr, gljDeContentModel);
        }
    }

    /**
     * 初始化定额费用代码
     * @param deModel
     * @returns {Promise<void>}
     */
    async initDeCostCode(deModel) {
        let {constructId, unitId, sequenceNbr} = deModel
        let deCostCode = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.DE_COST_CODE);
        if (ObjectUtil.isEmpty(deCostCode)) {
            deCostCode = {};
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.DE_COST_CODE, deCostCode);
        }
        if (ObjectUtil.isEmpty(deCostCode[unitId])) {
            deCostCode[unitId] = {};
        }
        if (ObjectUtil.isEmpty(deCostCode[unitId][sequenceNbr])) {
            deCostCode[unitId][sequenceNbr] = {
                constructId: constructId,
                unitId: unitId,
                deId: sequenceNbr,
                priceCodes: [
                    {
                        "code": "GCLMXHJ",
                        "price": 0
                    }
                ]
            };
        }
    }

    /**
     * 删除定额标准换算
     * @param deModel
     * @returns {Promise<void>}
     */
    async removeConversion(deModel) {
        // 删除标砖换算
        let conversionMap = ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(deModel.unitId);
        unitConversionMap?.delete(deModel.sequenceNbr);
        // 删除子定额工程量明细
        for (let children of deModel.children) {
            unitConversionMap?.delete(children.sequenceNbr);
        }
    }

    /**
     * 删除定额工程量明细
     * @param deModel
     * @returns {Promise<void>}
     */
    async removeQuantities(deModel) {
        // 删除工程量明细
        let quantitiesMap = ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap?.get(deModel.unitId)
        unitQuantiesMap?.delete(deModel.sequenceNbr);
        // 删除子定额工程量明细
        for (let children of deModel.children) {
            unitQuantiesMap?.delete(children.sequenceNbr);
        }
    }

    /**
     * 删除定额说明信息
     * @param deModel
     * @returns {Promise<void>}
     */
    async removeDeContent(deModel) {
        // 删除定额说明信息
        let deContentMap = ProjectDomain.getDomain(deModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DE_CONTENT);
        let unitDeContentMap = deContentMap?.get(deModel.unitId)
        unitDeContentMap?.delete(deModel.sequenceNbr);
        // 删除子定额定额说明信息
        for (let children of deModel.children) {
            unitDeContentMap?.delete(children.sequenceNbr);
        }
    }

    /**
     * 删除定额费用代码
     * @param deModel
     * @returns {Promise<void>}
     */
    async removeDeCostCode(deModel) {
        let {constructId, unitId, sequenceNbr} = deModel
        // 删除定额说明信息
        let deCostCode = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.DE_COST_CODE);
        if (ObjectUtil.isNotEmpty(deCostCode) && ObjectUtil.isNotEmpty(deCostCode[unitId]) && ObjectUtil.isNotEmpty(deCostCode[unitId][sequenceNbr])) {
            // 删除 sequenceNbr 属性
            delete deCostCode[unitId][sequenceNbr];
        }
    }

}
GljInitDeService.toString = () => '[class GljInitDeService]';
module.exports = GljInitDeService;
