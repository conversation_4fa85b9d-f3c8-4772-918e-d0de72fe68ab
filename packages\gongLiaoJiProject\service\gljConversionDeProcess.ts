import { Service } from "../../../core";
const Log = require("../../../core/log");
const { ObjectUtils } = require("../utils/ObjectUtils");
const {
	ConversionRuleOperationRecord,
} = require("../models/ConversionRuleOperationRecord");
const { Snowflake } = require("../utils/Snowflake");
const { SqlUtils } = require("../utils/SqlUtils");
const { NumberUtil } = require("../utils/NumberUtil");
const { ParamUtils } = require("../../../core/core/lib/utils/ParamUtils");
const ConstantUtil = require("../enums/ConstantUtil.js");
import { GsBaseRuleDetailFull } from "../models/GsBaseRuleDetailFull";
import {GljUnitProject} from "../models/GljUnitProject";
// import { BaseRuleDetailFull2022 } from "../model/BaseRuleDetailsFull2022";
// import { ItemBillProject } from "../../electron/model/ItemBillProject";
// import { MeasureProjectTable } from "../../electron/model/MeasureProjectTable";

import { GsConversionListItem } from "../models/GsConversionListItem";
import {GsItemBillProject} from "../models/GsItemBillProject";
import {GsMeasureProjectTable} from "../models/GsMeasureProjectTable";
const { GsBaseRuleFileDetails } = require("../models/GsBaseRuleFileDetails");
const { getRepository, In } = require("typeorm");

/**
 * 定额换算 process
 */
class GljConversionDeProcess extends Service {
	/**
	 * 构造函数
	 * @param ctx
	 */
	constructor(ctx: any) {
		super(ctx);
	}

	baseDeRuleRelationService = this.service.baseDeRuleRelationService;
	baseRuleDetailsService = this.service.baseRuleDetailsService;
	baseRuleFileDetailsService = this.service.baseRuleFileDetailsService;
	conversionRuleOperationRecordService =
		this.service.conversionRuleOperationRecordService;
	conversionInfoService = this.service.gsConversionInfoService;

	private readonly baseRuleDetailFull2022Repo = this.app.gsAppDataSource.getRepository(GsBaseRuleDetailFull);
	baseRuleDetailFullRepo = this.app.gsAppDataSource.getRepository(GsBaseRuleDetailFull);
	baseRuleFileDetailsRepo = this.app.gsAppDataSource.getRepository(GsBaseRuleFileDetails);

	getDefPiaoZhunHuanSuan = () => {
		return [
			{ sort: 1, type: "人工费", val: 1 },
			{ sort: 2, type: "机械费", val: 1 },
			{ sort: 3, type: "材料费", val: 1 },
			{ sort: 4, type: "主材费", val: 1 },
			{ sort: 5, type: "单价", val: 1 },
		];
	};

	async initDef(constructId: any, singleId: any, unitId: any, deId: string | number) {
		if (!constructId && !singleId && !unitId) {
			constructId = ParamUtils.getPatram("commonParam").constructId;
			singleId = ParamUtils.getPatram("commonParam").singleId;
			unitId = ParamUtils.getPatram("commonParam").unitId;
		}

		let defHuanSuan = this.getDefPiaoZhunHuanSuan();
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		unitProject.defaultConcersions
			? (unitProject.defaultConcersions[deId] = defHuanSuan)
			: (unitProject.defaultConcersions = {
					[deId]: defHuanSuan,
			  });
	}

	async upDateDefault(
		constructId: any,
		singleId: any,
		unitId: any,
		deId: any,
		oldAlgorithm: { val: any },
		newAlgorithm: { type: string; name: string; val: string | number }
	) {
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		//  1. 获取定额下需要变动的人材机
		let rcjs = unitProject.constructProjectRcjs.filter(
			(f: { deId: any }) => f.deId == deId
		);
		if (rcjs && rcjs.length > 0) {
			if (newAlgorithm.type === "人工费") {
				rcjs = rcjs.filter((f: { kind: number }) => f.kind === 1);
			} else if (newAlgorithm.type === "机械费") {
				rcjs = rcjs.filter((f: { kind: number }) => f.kind === 3);
			} else if (newAlgorithm.type === "材料费") {
				rcjs = rcjs.filter((f: { kind: number }) => f.kind === 2 || f.kind > 5);
			} else if (newAlgorithm.type === "主材费") {
				rcjs = rcjs.filter((f: { kind: number }) => f.kind === 5);
			} else if (newAlgorithm.type === "单价") {
				rcjs = rcjs;
			}
		}
		if (!rcjs || rcjs.length == 0) {
			return rcjs;
		}
		//  3. 做记录
		// 换算信息 后端存的一个，迭代1就有，不太清楚干啥的
		if (!unitProject.conversionRuleOperationRecordList) {
			unitProject.conversionRuleOperationRecordList = [];
		}
		unitProject.conversionRuleOperationRecordList =
			unitProject.conversionRuleOperationRecordList.filter(
				(f: { fbFxDeId: any; ruleDetailId: any }) =>
					f.fbFxDeId != deId && f.ruleDetailId != newAlgorithm.name
			);
		unitProject.conversionRuleOperationRecordList.push({
			sequenceNbr: Snowflake.nextId(),
			fbFxDeId: deId,
			ruleDetailId: newAlgorithm.name,
			source: "标准换算",
		});
		// 前端展示用的 删除旧的记录 添加新的记录
		if (!unitProject.conversionInfoList) {
			unitProject.conversionInfoList = [];
		}
		unitProject.conversionInfoList = unitProject.conversionInfoList.filter(
			(f: { deId: any; ruleId: any }) =>
				f.deId != deId && f.ruleId != newAlgorithm.name
		);
		unitProject.conversionInfoList.push({
			sequenceNbr: Snowflake.nextId(),
			deId: deId,
			ruleId: newAlgorithm.name,
			source: "标准换算",
			conversionString: newAlgorithm.name + "*" + newAlgorithm.val, // 用来记 kind2的换算规则
			conversionExplain: newAlgorithm.name + "*" + newAlgorithm.val, // 用来记kind2的查看
		});
		//  4. 根据oldAlgorithm反算人材机消耗量
		for (let i = 0; i < rcjs.length; ++i) {
			if (rcjs[i].ruleDeActive && !rcjs[i].ruleDeActive[newAlgorithm.name]) {
				// 改过的数据 不动
				continue;
			}
			rcjs[i].resQty = NumberUtil.divide(
				rcjs[i].resQty,
				Number(oldAlgorithm.val)
			);
		}

		//  5. 根据newAlgorithm计算人材机消耗量
		for (let i = 0; i < rcjs.length; ++i) {
			if (rcjs[i].ruleDeActive && !rcjs[i].ruleDeActive[newAlgorithm.name]) {
				// 改过的数据 不动
				continue;
			}
			if (!rcjs[i].lastResQty) {
				rcjs[i].lastResQty = rcjs[i].resQty;
			}
			rcjs[i].resQty = rcjs[i].lastResQty;
			rcjs[i].resQty = NumberUtil.multiply(
				rcjs[i].resQty,
				Number(oldAlgorithm.val)
			);
		}
		// 6. 删除人材机标识
		if (newAlgorithm.val == 1) {
			for (let i = 0; i < rcjs.length; ++i) {
				if (rcjs[i].ruleDeActive) {
					delete rcjs[i].ruleDeActive;
				}
			}
		}
	}

	/**
	 * kind下拉框一级接口
	 * @return {*}
	 */
	getGroupNames(libraryCode: string) {
		if (ObjectUtils.isEmpty(libraryCode)) {
			return [];
		}

		let tableName = "base_bzhs_clpb_2022";

		let sql =
			"select group_name as groupName, library_code as libraryCode from " +
			tableName +
			" where library_code = ? group by group_name";
		let sqlRes = this.app.gljSqlite3DataSource.prepare(sql).all(libraryCode);

		return sqlRes;
	}

	getGroupDetail(groupName: any, libraryCode: string) {
		if (ObjectUtils.isEmpty(libraryCode)) {
			throw new Error("libraryCode为空");
		}

		let tableName = "base_bzhs_clpb_2022";

		let sql =
			"select * from " +
			tableName +
			" where group_name = ? and library_code = ? order by group_name, details";
		let sqlRes = this.app.gljSqlite3DataSource
			.prepare(sql)
			.all(groupName, libraryCode);
		let convertRes = SqlUtils.convertToModel(sqlRes);

		return convertRes;
	}

	// R,C,J * k对应定额下的人工，材料（含主材），机械
	// 0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比
	rcjKindConvert: any = {
		"1": "R",
		"3": "J",
		"2": "C",
		"5": "C",
		"6": "C",
		"7": "C",
		"8": "C",
		"9": "C",
		"10": "C",
	};

	async cleanRules(
		standardDeId: any,
		fbFxDeId: string,
		constructId: any,
		singleId: any,
		unitId: any
	) {
		// const { line: rawLine, belong: type } =
		// 	this.service.baseBranchProjectOptionService.findLineOnlyById(
		// 		fbFxDeId
		// 	) as {
		// 		line: GsItemBillProject | GsMeasureProjectTable;
		// 		belong: "fbfx" | "csxm";
		// 	};
		let rawLine: GsItemBillProject = await this.service.gongLiaoJiProject.gljProjectCommonService.findLineOnlyById(constructId, unitId, fbFxDeId);
		const ruleIds = rawLine.conversionList.map((v) => v.sequenceNbr);
		let unitProject: GljUnitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let { defaultConcersions = {} } = unitProject;
		delete rawLine.conversionList;
		delete rawLine.conversionInfo;
		delete rawLine.redArray;
		delete rawLine.blackArray;
		delete defaultConcersions[fbFxDeId];
		if ((rawLine as GsItemBillProject).appendType) {
			(rawLine as GsItemBillProject).appendType = (
				rawLine as GsItemBillProject
			).appendType.filter((v) => v != "换");
		}
		this.service.gongLiaoJiProject.gljConversionDeService.resetLineSuffix(rawLine);

		// 删除换算新增的定额
		let itemBillProjects: Array<GsItemBillProject> = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByUnit(unitProject.constructId, unitProject.sequenceNbr);
		const delItemBillIds = itemBillProjects
			.filter(
				(v) =>
					v.fromConversionRuleId && ruleIds.includes(v.fromConversionRuleId)
			)
			.map((v) => v.sequenceNbr);
		for (const id of delItemBillIds) {
			(unitProject.itemBillProjects as any).removeNode(id);
		}

		// //kind =3 b 处理
		// if (unitProject.conversionRuleSeqNbrSortIndex)
		// 	unitProject.conversionRuleSeqNbrSortIndex[fbFxDeId] = [];
		// await this.cleanKind3bDe(constructId, singleId, unitId, fbFxDeId);

		// //记录消耗量
		// let promise = await this.recordRcj(constructId, singleId, unitId, fbFxDeId);

		// // 删除记录
		// if (
		// 	unitProject.conversionRuleOperationRecordList &&
		// 	unitProject.conversionRuleOperationRecordList.length > 0
		// ) {
		// 	unitProject.conversionRuleOperationRecordList =
		// 		unitProject.conversionRuleOperationRecordList.filter(
		// 			(f: { fbFxDeId: any }) => f.fbFxDeId !== fbFxDeId
		// 		);
		// }
		// if (
		// 	unitProject.conversionInfoList &&
		// 	unitProject.conversionInfoList.length > 0
		// ) {
		// 	unitProject.conversionInfoList = unitProject.conversionInfoList.filter(
		// 		(f: { deId: any }) => f.deId !== fbFxDeId
		// 	);
		// 	let list = unitProject.itemBillProjects.filter(
		// 		(f: { sequenceNbr: any }) => f.sequenceNbr == fbFxDeId
		// 	);
		// 	if (!list[0].appendType) {
		// 		list[0].appendType = [];
		// 	} else {
		// 		list[0].appendType = list[0].appendType.filter(
		// 			(f: string) => f != "换"
		// 		);
		// 	}
		// }
		// 人材机回退
		// this.service.rcjProcess.delRcjAndRcjDetailBatch(
		// 	[fbFxDeId],
		// 	constructId,
		// 	singleId,
		// 	unitId
		// );
		// let findRes = this.service.baseBranchProjectOptionService.findFromAllById(
		// 	constructId,
		// 	singleId,
		// 	unitId,
		// 	fbFxDeId
		// );
		// await this.service.rcjProcess.batchSaveRcjData(
		// 	findRes.item,
		// 	constructId,
		// 	singleId,
		// 	unitId
		// );

		// //使用记录消耗量
		// await this.useRecordRcj(constructId, singleId, unitId, fbFxDeId, promise);
		// unitProject.constructProjectRcjs
		// 	.filter((f: { deId: any }) => f.deId == fbFxDeId)
		// 	.forEach((rcj: any) => {
		// 		this.service.unitPriceService.caculateDeByRcj(
		// 			constructId,
		// 			singleId,
		// 			unitId,
		// 			rcj
		// 		);
		// 	});
		// //对定额下挂的kind3c执行规则进行清空
		// let itemBillProjects = unitProject.itemBillProjects;
		// let measureProjectTables = unitProject.measureProjectTables;
		// let moduleData;
		// moduleData = itemBillProjects.find(
		// 	(k: { sequenceNbr: any }) => k.sequenceNbr === fbFxDeId
		// );
		// if (ObjectUtils.isEmpty(moduleData)) {
		// 	moduleData = measureProjectTables.find(
		// 		(k: { sequenceNbr: any }) => k.sequenceNbr === fbFxDeId
		// 	);
		// }
		// moduleData.deKind3cRules = [];
		// //处理定额临时状态数据
		// let deList = unitProject.itemBillProjects.filter(
		// 	(f: { sequenceNbr: any }) => f.sequenceNbr == fbFxDeId
		// );
		// this.service.itemBillProjectOptionService.addDeTempDel(
		// 	constructId,
		// 	singleId,
		// 	unitId,
		// 	deList[0]
		// );
	}

	/**
	 * 记录定额下的人材机
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param deId
	 * @returns {Promise<{constructProjectRcjs: Array<ConstructProjectRcj>, rcjDetailList: Array<RcjDetails>}>}
	 */
	async recordRcj(constructId: any, singleId: any, unitId: any, deId: any) {
		let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let constructProjectRcjs = unit.constructProjectRcjs;
		let rcjDetailList = unit.rcjDetailList;

		if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
			constructProjectRcjs = constructProjectRcjs.filter(
				(i: { deId: any }) => i.deId == deId
			);
		}
		if (!ObjectUtils.isEmpty(rcjDetailList)) {
			rcjDetailList = rcjDetailList.filter(
				(i: { deId: any }) => i.deId == deId
			);
		}

		return {
			constructProjectRcjs: constructProjectRcjs,
			rcjDetailList: rcjDetailList,
		};
	}

	/**
	 * 使用记录
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param deId
	 * @param promise
	 */
	async useRecordRcj(
		constructId: any,
		singleId: any,
		unitId: any,
		deId: any,
		promise: { constructProjectRcjs: any; rcjDetailList?: any }
	) {
		let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let constructProjectRcjs = unit.constructProjectRcjs;
		let rcjDetailList = unit.rcjDetailList;

		let moduleData = await this.service.gongLiaoJiProject.gljProjectCommonService.getModuleData(constructId, singleId, unitId, deId);

		let t1 = moduleData.find(
			(i: { sequenceNbr: any }) => i.sequenceNbr == deId
		);

		if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
			constructProjectRcjs = constructProjectRcjs.filter(
				(i: { deId: any }) => i.deId == deId
			);
		}
		if (!ObjectUtils.isEmpty(rcjDetailList)) {
			rcjDetailList = rcjDetailList.filter(
				(i: { deId: any }) => i.deId == deId
			);
		}

		let constructProjectRcjs1 = promise.constructProjectRcjs;
		if (ObjectUtils.isEmpty(constructProjectRcjs1)) {
			return;
		}
		for (let constructProjectRcjs1Element of constructProjectRcjs1) {
			if (!ObjectUtils.isEmpty(constructProjectRcjs1Element.consumerResQty)) {
				let t = constructProjectRcjs.find(
					(i: { materialCode: any }) =>
						i.materialCode == constructProjectRcjs1Element.materialCode
				);
				if (!ObjectUtils.isEmpty(t)) {
					t.resQty = constructProjectRcjs1Element.resQty;

					t.totalNumber = NumberUtil.numberScale4(
						NumberUtil.multiply(Number(t.resQty), t1.quantity)
					);
					t.total = NumberUtil.numberScale2(
						NumberUtil.multiply(t.totalNumber, t.marketPrice)
					);
				}
			}
		}
	}

	/**
	 * kind =3 b 删除定额
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 * @param deid
	 */
	async cleanKind3bDe(constructId: any, singleId: any, unitId: any, deid: any) {
		let unit = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);

		let itemBillProjects = unit.itemBillProjects;
		let measureProjectTables = unit.measureProjectTables;
		let moduleData;
		let type = true;
		moduleData = itemBillProjects.find(
			(k: { sequenceNbr: any }) => k.sequenceNbr === deid
		);
		if (ObjectUtils.isEmpty(moduleData)) {
			moduleData = measureProjectTables.find(
				(k: { sequenceNbr: any }) => k.sequenceNbr === deid
			);
			type = false;
		}

		if (ObjectUtils.isEmpty(moduleData)) {
			return;
		}

		if (ObjectUtils.isEmpty(moduleData.createDeId)) {
			return;
		}

		if (type) {
			await this.service.itemBillProjectOptionService.removeLine(
				constructId,
				singleId,
				unitId,
				{
					sequenceNbr: moduleData.createDeId,
					parentId: moduleData.parentId,
					kind: "04",
				},
				false
			);
		} else {
			await this.service.stepItemCostService.removeLine(
				constructId,
				singleId,
				unitId,
				{
					sequenceNbr: moduleData.createDeId,
					parentId: moduleData.parentId,
					kind: "04",
				},
				false
			);
		}
		moduleData.createDeId = null;
	}

	/**
	 * 标准换算列表
	 * @param constructId
	 * @param unitId
	 */
	async conversionRuleListAll(args: {
		constructId: any;
		unitId: any;
	}) {
		const {
			constructId,
			unitId,
		} = args;

		return await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvertListAll(constructId, unitId);
	}

	/**
	 * 标准换算列表
	 * @param standardDeId 国标定额id
	 * @param fbFxDeId 分部分项定额id
	 * @param libraryCode 定额册编码
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 */
	async conversionRuleList(args: {
		standardDeId: any;
		fbFxDeId: any;
		libraryCode: any;
		constructId: any;
		singleId: any;
		unitId: any;
	}) {
		const {
			standardDeId,
			fbFxDeId,
			libraryCode,
			constructId,
			singleId,
			unitId,
		} = args;

		return await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvertList(constructId, unitId, fbFxDeId);
		return await this.standardConvertList(args);
		// 入参 7188546426811449344

		//查 定额和换算规则 的关系
		/*let deRuleRelation = await this.baseDeRuleRelationService.selectRelationByDeId(standardDeId);
        if (ObjectUtils.isEmpty(deRuleRelation) || ObjectUtils.isEmpty(deRuleRelation.relationGroupCode)) {
            Log.error("定额对应的规则关联关系为空或规则组编码relationGroupCode为空,定额id:", standardDeId);
            return [];
        }*/
		if (!standardDeId || !libraryCode) return [];
		// 根据定额和换算规则的关系查规则明细
		// let baseRuleDetailList = await this.baseRuleDetailsService.ByRelationGroupCode(deRuleRelation.relationGroupCode);
		const sqlRes = await (`${libraryCode}`.startsWith("2022")
			? this.baseRuleDetailFull2022Repo
			: this.baseRuleDetailFullRepo
		).find({
			where: {
				deId: standardDeId,
			},
			order: {
				sequenceNbr: "ASC",
			},
		});
		// 规则详情列表
		const baseRuleDetailList = SqlUtils.convertToModel(sqlRes).map(
			(item: { [x: string]: null }) => {
				//数仓可能有字符串为Null的问题  在这里再做一次过滤
				for (const key in item) {
					if (item[key] == "NULL") item[key] = null;
				}
				return item;
			}
		);
		// 组装查询参数
		// 批量查规则文件 by规则文件ids
		// 规则文件列表
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		const ruleFileIdList = baseRuleDetailList
			.filter((v: any) => !ObjectUtils.isEmpty(v))
			.map((v: { fileDetailsId: any }) => v.fileDetailsId);
		const baseRuleFileList =
			ruleFileIdList.length > 0
				? await this.baseRuleFileDetailsRepo.findBy({
						sequenceNbr: In(ruleFileIdList),
				  })
				: [];
		// 查换算规则操作记录（在内存或ysf文件中查规则的操作记录）
		let conversionRuleOperationRecordList =
			this.conversionRuleOperationRecordService.getDeConversionRuleOperationRecord(
				constructId,
				singleId,
				unitId,
				fbFxDeId
			);
		if (
			!conversionRuleOperationRecordList ||
			conversionRuleOperationRecordList.length == 0
		) {
			if (Array.isArray(unitProject.conversionInfoList)) {
				const target = unitProject.conversionInfoList.filter(
					(f: { deId: any }) => f.deId == fbFxDeId
				);
				if (target) conversionRuleOperationRecordList = target;
			}
		}
		//  给规则明细填充数据：填充规则文件及是否选中
		baseRuleDetailList.forEach(
			(i: {
				fileDetailsId: any;
				ruleFile: any;
				sequenceNbr: any;
				kind: number;
				defaultValue: any;
				relation: any;
				rcjId: any;
				selected: any;
				ruleInfo: any;
				beGray: boolean;
				selectedRuleGroup: any;
				topGroupType: any;
			}) => {
				// 填充规则文件
				if (!ObjectUtils.isEmpty(baseRuleFileList)) {
					if (!ObjectUtils.isEmpty(i.fileDetailsId)) {
						i.ruleFile = baseRuleFileList.find(
							(j: { sequenceNbr: any }) => i.fileDetailsId === j.sequenceNbr
						);
					}
				}
				if (!ObjectUtils.isEmpty(conversionRuleOperationRecordList)) {
					let history = conversionRuleOperationRecordList.find(
						(c: { ruleId: any }) => c.ruleId == i.sequenceNbr
					);
					if (history) {
						const data = {};
						switch (
							+i.kind
							// case 1:
							//     data.selected = history.selected
							//     break;
							// case 2:
							//     data.ruleInfo = history.description;
							//     /*i.selected = kind2Selected.description;*/
							//     data.selectedRuleGroup = history.selectedRuleGroup;
							// default:
							//     data.selectedRule = history.selectedRule;
							//     break;
						) {
						}
						Object.assign(i, data);
						// if (i.kind == 1) {
						//     let selected = conversionRuleOperationRecordList.find(c => c.ruleId == i.sequenceNbr);
						//     if (selected) {
						//         i.selected = true;
						//     }
						// } else if (i.kind == 2) {
						//     let kind2Selected = conversionRuleOperationRecordList.find(c => c.ruleId == i.sequenceNbr);
						//     if (kind2Selected) {
						//         i.ruleInfo = kind2Selected.description;
						//         /*i.selected = kind2Selected.description;*/
						//         i.selectedRuleGroup = kind2Selected.selectedRuleGroup;
						//     }
						// } else {
						//     let kind3Selected = conversionRuleOperationRecordList.find(c => c.ruleId == i.sequenceNbr);
						//     i.selectedRule = kind3Selected.selectedRule;
						// }
					}
				}
				if (i.kind == 2) {
					i.defaultValue = i.relation;
					let standId = i.rcjId;
					let rcjs = this.service.rcjProcess.getRcjListByDeId(
						fbFxDeId,
						constructId,
						singleId,
						unitId
					);
					let res = rcjs.filter(
						(f: { standardId: any }) => f.standardId === standId
					);
					if (res && res.length > 0) {
						i.selected = res[0].materialName;
						i.ruleInfo = res[0].materialName;
						i.beGray = false;
						if (!i.selectedRuleGroup) {
							i.selectedRuleGroup = i.topGroupType;
						}
					} else {
						i.beGray = true;
					}
				}
			}
		);

		if (baseRuleDetailList && baseRuleDetailList.length > 0) {
			baseRuleDetailList.forEach(
				(f: { kind: number; defaultValue: string; selectedRule: number }) => {
					if (f.kind == 1) {
						f.defaultValue = "-";
					}
					if (!f.selectedRule && f.selectedRule != 0) {
						// f.selectedRule = f.defaultValue;
					}
				}
			);
		}

		// 给规则列表的行数据中增加fbFxDeId, 用于处理ysf文件时和BS交互
		baseRuleDetailList.forEach(
			(i: { fbFxDeId: any; index: any }, index: any) => {
				i.fbFxDeId = fbFxDeId;
				i.index = index;
			}
		);
		if (
			unitProject.conversionRuleSeqNbrSortIndex &&
			Array.isArray(unitProject.conversionRuleSeqNbrSortIndex[fbFxDeId])
		) {
			const sortedList = baseRuleDetailList
				.sort((a: { sequenceNbr: any }, b: { sequenceNbr: any }) => {
					const aIndex = unitProject.conversionRuleSeqNbrSortIndex[
						fbFxDeId
					].findIndex((i: any) => i === a.sequenceNbr);
					const bIndex = unitProject.conversionRuleSeqNbrSortIndex[
						fbFxDeId
					].findIndex((i: any) => i === b.sequenceNbr);
					return aIndex - bIndex;
				})
				.map((v: { index: any }, index: any) => {
					v.index = index;
					return v;
				});
			return sortedList;
		}
		return baseRuleDetailList;
	}

	// 标准换算列表
	async standardConvertList(args: {
		standardDeId: any;
		fbFxDeId: any;
		libraryCode: any;
		constructId: any;
		singleId: any;
		unitId: any;
	}) {
		const {
			standardDeId,
			fbFxDeId,
			libraryCode,
			constructId,
			singleId,
			unitId,
		} = args;
		let rawLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, fbFxDeId);
		if (!rawLine || rawLine.type != "04" || !standardDeId) return [];
		if (!rawLine.conversionList || rawLine.conversionList.length <= 0) {
			const list: GsBaseRuleDetailFull[] = await (`${libraryCode}`.startsWith("2022")
				? this.baseRuleDetailFull2022Repo
				: this.baseRuleDetailFullRepo
			).find({
				where: {
					deId: standardDeId,
				},
				order: {
					sortNoGlobal: "ASC",
				},
			});
			rawLine.conversionList = list.map((v, index) => {
				const conversionListItem = new GsConversionListItem();
				return Object.assign(conversionListItem, v, {
					isSelect: false,
					value: v.defaultValue,
					selectValue: v.defaultValue,
					index,
					// kind3 前端渲染KEY
					selectedRule: v.kind == "3" ? v.defaultValue : null,
					// kind2 前端渲染KEY
					ruleInfo: v.kind == "2" ? v.relation : null,
					// kind1 前端渲染KEY
					selected: v.kind == "1" ? false : null,
					defaultValue:
						v.kind == "1" ? "-" : v.kind == "2" ? v.relation : v.defaultValue,
					selectedRuleGroup: v.topGroupType,
				});
			});
		}
		return rawLine.conversionList;
	}

	/**
	 * 定额标准换算勾选及下拉框点击
	 * @param fbFxDeId 分部分项定额id
	 * @param baseRuleDetails 规则明细数据行
	 * @param constructId
	 * @param singleId
	 * @param unitId
	 */
	async operationalConversionRule(
		fbFxDeId: any,
		baseRuleDetails: {
			sequenceNbr: any;
			kind: number;
			selected: any;
			math: any;
			rcjId: string;
			type: string;
			defaultValue: any;
		},
		constructId: any,
		singleId: any,
		unitId: any,
		clpb: {
			libraryCode: string;
			detailsCode: string;
			details: string;
			groupName: any;
		},
		consumerInput: any
	) {
		let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
		let excuteRule: any = {};
		excuteRule.ruleId = baseRuleDetails.sequenceNbr;
		// 规则id
		let ruleId = baseRuleDetails.sequenceNbr;
		let saveNote;
		if (baseRuleDetails.kind == 1) {
			excuteRule.kind = 1;
			// 是否选中 true是 false否
			let selected = baseRuleDetails.selected;
			// 判断是选中还是未选中
			if (selected) {
				excuteRule.type = "caculate";
				// 选中
				// 添加换算操作记录
				this.conversionRuleOperationRecordService.addOperationRecord(
					constructId,
					singleId,
					unitId,
					fbFxDeId,
					ruleId
				);
				// 添加换算信息  前端用
				this.conversionInfoService.addConversionInfo(
					constructId,
					singleId,
					unitId,
					fbFxDeId,
					baseRuleDetails
				);
			} else {
				excuteRule.type = "back";
				// 未选中（未选中即取消选中）
				// 删除换算操作记录
				this.conversionRuleOperationRecordService.deleteOperationRecord(
					constructId,
					singleId,
					unitId,
					fbFxDeId,
					ruleId
				);
				// 删除对应的换算信息  前端用
				this.conversionInfoService.deleteConversionInfo(
					constructId,
					singleId,
					unitId,
					fbFxDeId,
					ruleId
				);
			}
			excuteRule.math = baseRuleDetails.math;
		} else if (baseRuleDetails.kind == 2) {
			excuteRule.kind = 2;
			excuteRule.type = "caculate";
			excuteRule.math =
				baseRuleDetails.rcjId + "^" + clpb.libraryCode + ":" + clpb.detailsCode;

			let note = this.conversionRuleOperationRecordService.addOperationRecord(
				constructId,
				singleId,
				unitId,
				fbFxDeId,
				ruleId
			); // 换算信息
			note.description = clpb.details;
			note.selectedRuleGroup = clpb.groupName;
			if (!unitProject.conversionInfoList) {
				unitProject.conversionInfoList = [];
			} else {
				unitProject.conversionInfoList = unitProject.conversionInfoList.filter(
					(f: { ruleId: any }) => f.ruleId !== baseRuleDetails.sequenceNbr
				);
			}
			let sql =
				"select material_code as mcode from base_rcj where sequence_nbr = ?";
			let sqlRes: any = this.app.betterSqlite3DataSource
				.prepare(sql)
				.get(baseRuleDetails.rcjId);
			let oriCode = sqlRes.mcode;
			unitProject.conversionInfoList.push({
				sequenceNbr: Snowflake.nextId(),
				ruleId: baseRuleDetails.sequenceNbr,
				deId: fbFxDeId,
				source: "标准换算",
				conversionString: "H" + oriCode + " " + clpb.detailsCode, // 用来记 kind2的换算规则
				conversionExplain: "换算材料:" + clpb.details, // 用来记kind2的查看
			});

			let item = this.service.baseBranchProjectOptionService.findFromAllById(
				constructId,
				singleId,
				unitId,
				fbFxDeId
			).item;
			/* if (!item.appendType) {
                 item.appendType = [];
             } else {
                 item.appendType = item.appendType.filter(f=>f!="换");
             }
             item.appendType.push("换");*/
			if (item.bdName) {
				if (!item.bdName.endsWith(clpb.details)) {
					item.bdName += "\n";
					item.bdName += clpb.details;
				}
			} else {
				if (!item.name.endsWith(clpb.details)) {
					item.name += "\n";
					item.name += clpb.details;
				}
			}
		} else if (baseRuleDetails.kind == 3) {
			excuteRule.kind = 3;
			excuteRule.type = "caculate";
			excuteRule.oriRule = baseRuleDetails;
			excuteRule.consumerInput = consumerInput;
			// 记录
			let note = this.conversionRuleOperationRecordService.addOperationRecord(
				constructId,
				singleId,
				unitId,
				fbFxDeId,
				ruleId
			); // 换算信息
			// note.description = clpb.details;
			// 展示用的那个记录
			if (!unitProject.conversionInfoList) {
				unitProject.conversionInfoList = [];
			} else {
				saveNote = unitProject.conversionInfoList.filter(
					(f: { deId: any; ruleId: any }) =>
						f.deId == fbFxDeId && f.ruleId === baseRuleDetails.sequenceNbr
				);
				if (saveNote && saveNote.length > 0) {
					saveNote = saveNote[0];
				}
				// unitProject.conversionInfoList = unitProject.conversionInfoList.filter(f=>f.deId == fbFxDeId && f.ruleId !== baseRuleDetails.sequenceNbr);
			}
			if (!saveNote) {
				saveNote = {
					sequenceNbr: Snowflake.nextId(),
					ruleId: baseRuleDetails.sequenceNbr,
					deId: fbFxDeId,
					source: "标准换算",
					consumerInput: consumerInput,
					conversionString: baseRuleDetails.math, // 用来记 kind3的换算规则
					conversionExplain: baseRuleDetails.math, // 用来记kind3的查看
				};
			}
			unitProject.conversionInfoList.push(saveNote);
			// 分类
			if (baseRuleDetails.type === "a") {
				excuteRule.kindType = "a";
				excuteRule.defVal = baseRuleDetails.defaultValue;
			}
			if (baseRuleDetails.type === "b") {
				excuteRule.kindType = "b";
			}
			if (baseRuleDetails.type === "c") {
				excuteRule.kindType = "c";
			}
			if (baseRuleDetails.type === "d") {
				excuteRule.kindType = "d";
			}
			if (baseRuleDetails.type === "e") {
				excuteRule.kindType = "e";
			}
			if (baseRuleDetails.type === "f") {
				excuteRule.kindType = "f";
			}
			excuteRule.math = baseRuleDetails.math;
		}

		await this.caculateRcj(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			excuteRule,
			saveNote
		);

		let allData = this.service.rcjProcess.reCaculateDeRcjs(
			constructId,
			singleId,
			unitId,
			fbFxDeId
		); //改了定额工程量重算人材机合计数量 合价
		this.service.unitPriceService.caculataDEUnitPrice(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			true,
			allData
		);

		return true;
	}

	reSetRcjActiveStatu(
		excuteRule: {
			type: string;
			ruleId: string | number;
			defVal: any;
			consumerInput: any;
		},
		infRcjs: string | any[]
	) {
		if (excuteRule.type === "back") {
			for (let i = 0; i < infRcjs.length; ++i) {
				if (
					infRcjs[i].ruleDeActive &&
					infRcjs[i].ruleDeActive[excuteRule.ruleId]
				) {
					delete infRcjs[i].ruleDeActive[excuteRule.ruleId];
				}
			}
		}
		if (excuteRule.defVal == excuteRule.consumerInput) {
			for (let i = 0; i < infRcjs.length; ++i) {
				if (
					infRcjs[i].ruleDeActive &&
					infRcjs[i].ruleDeActive[excuteRule.ruleId]
				) {
					delete infRcjs[i].ruleDeActive[excuteRule.ruleId];
				}
			}
		}
	}

	async caculateRcj(
		constructId: any,
		singleId: any,
		unitId: any,
		fbFxDeId: any,
		excuteRule: any,
		saveNote: any
	) {
		let convertRules = this.convertRule(excuteRule, saveNote);
		let infRcjs: any[] = [];
		for (let i = 0; i < convertRules.length; ++i) {
			// 遍历规则
			let res = await this.excuteRule(
				constructId,
				singleId,
				unitId,
				convertRules[i],
				await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId),
				fbFxDeId,
				saveNote
			); // 对所有人材机执行规则
			infRcjs = infRcjs.concat(res);
		}
		// 算完后做记录
		let backs = [];
		for (let i = 0; i < convertRules.length; ++i) {
			let cr: any = convertRules[i];
			let singleBack: any = { "+": "-", "*": "/", "-": "+", "/": "*" };
			let backSingle = singleBack[cr.math.substring(0, 1)];
			let backVal = eval(cr.math.substring(1));
			let bacmMath = backSingle + backVal;
			backs.push(bacmMath);
		}
		saveNote.kind3Backs = backs;

		let infRcjIds: any = {};
		for (let i = 0; i < infRcjs.length; ++i) {
			infRcjIds[infRcjs[i].sequenceNbr] = true;
		}
		saveNote.infRcjIds = infRcjIds;
		this.reSetRcjActiveStatu(excuteRule, infRcjs);

		// 换
		let deLine = this.service.rcjProcess.findDeByDeId(
			constructId,
			singleId,
			unitId,
			fbFxDeId
		);
		let nowRcjs = this.service.rcjProcess.queryRcjDataByDeId(
			fbFxDeId,
			constructId,
			singleId,
			unitId
		);
		let baseRcjs = this.service.rcjProcess.getBaseRcjInfoByDeId(
			deLine.standardId
		);
		deLine.appendType = deLine.appendType.filter((a: string) => a !== "换");
		if (!this.isSameRcj(baseRcjs, nowRcjs)) {
			if (!deLine.appendType) {
				deLine.appendType = [];
			} else {
				deLine.appendType = deLine.appendType.filter((f: string) => f !== "换");
			}
			deLine.appendType.push("换");
		}
	}

	isSameRcj(rcjArrayA: string | any[], rcjArrayB: string | any[]) {
		if (!Array.isArray(rcjArrayA) || !Array.isArray(rcjArrayB)) {
			return false;
		}
		if (rcjArrayA.length !== rcjArrayB.length) {
			return false;
		}

		let aMap: any = {};
		for (let i = 0; i < rcjArrayA.length; ++i) {
			let str =
				"" +
				Number.parseFloat(rcjArrayA[i].resQty).toFixed(6) +
				rcjArrayA[i].materialCode +
				rcjArrayA[i].materialName +
				rcjArrayA[i].specification +
				rcjArrayA[i].kind +
				rcjArrayA[i].unit;
			aMap[str] = 1;
		}
		let bMap: any = {};
		for (let i = 0; i < rcjArrayB.length; ++i) {
			let str =
				"" +
				Number.parseFloat(rcjArrayB[i].resQty).toFixed(6) +
				rcjArrayB[i].materialCode +
				rcjArrayB[i].materialName +
				rcjArrayB[i].specification +
				rcjArrayB[i].kind +
				rcjArrayB[i].unit;
			let isBExistInA = aMap[str];
			if (!isBExistInA) {
				return false;
			}
			bMap[str] = 1;
		}

		for (let i = 0; i < rcjArrayA.length; ++i) {
			let str =
				"" +
				Number.parseFloat(rcjArrayA[i].resQty).toFixed(6) +
				rcjArrayA[i].materialCode +
				rcjArrayA[i].materialName +
				rcjArrayA[i].specification +
				rcjArrayA[i].kind +
				rcjArrayA[i].unit;
			let isAExistInB = bMap[str];

			if (!isAExistInB) {
				return false;
			}
		}

		return true;
	}

	async excuteRule(
		constructId: any,
		singleId: any,
		unitId: any,
		convertRule: any,
		unit: any,
		fbFxDeId: any,
		saveNote: any
	) {
		if (convertRule.type == 1) {
			//类型是1的规则
			return this._excuteKind1Rule(unit, fbFxDeId, convertRule);
		} else if (convertRule.type == 2) {
			//类型是2的规则
			return await this._excuteKind2Rule(
				constructId,
				singleId,
				unitId,
				unit,
				fbFxDeId,
				convertRule
			);
		} else if (convertRule.type == 3) {
			if (convertRule.kindType == "a") {
				return this._excuteKind3ARule(unit, fbFxDeId, convertRule, saveNote);
			}
			if (convertRule.kindType == "b") {
				return this._excuteKind3BRule(unit, fbFxDeId, convertRule);
			}
			if (convertRule.kindType == "c") {
				return this._excuteKind3CRule(unit, fbFxDeId, convertRule);
			}
			if (convertRule.kindType == "d") {
				return this._excuteKind3DRule(unit, fbFxDeId, convertRule);
			}
		}
	}

	_excuteKind3DRule(
		unit: { constructProjectRcjs: string | any[] },
		fbFxDeId: any,
		convertRule: {
			ruleId: string | number;
			find: { attr: string | number; val: any };
			math: any;
		}
	) {
		let infRcjs = [];
		for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
			let rcj = unit.constructProjectRcjs[i];
			if (rcj.deId == fbFxDeId) {
				// 如果对于本规则，人材机是未激活状态 则不执行
				if (rcj.ruleDeActive && rcj.ruleDeActive[convertRule.ruleId]) {
					continue;
				}
				// 正常执行
				if (!convertRule.find.attr) {
					// *num形式的规则
					rcj.resQty = NumberUtil.numberScale(
						eval(rcj.resQty + convertRule.math),
						6
					);
					infRcjs.push(rcj);
					continue;
				}
				if (
					rcj[convertRule.find.attr] == convertRule.find.val ||
					this.rcjKindConvert[rcj[convertRule.find.attr]] ===
						convertRule.find.val
				) {
					// RCJ，code
					rcj.resQty = NumberUtil.numberScale(
						eval(rcj.resQty + convertRule.math),
						6
					);
					infRcjs.push(rcj);
				}
			}
		}

		return infRcjs;
	}

	async _excuteKind2Rule(
		constructId: any,
		singleId: any,
		unitId: any,
		unit: GljUnitProject,
		fbFxDeId: any,
		convertRule: { findStandId: any; toLib: any; toCode: any }
	) {
		let infRcjs = [];
		for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
			let orgRcj = unit.constructProjectRcjs[i];
			if (orgRcj.deId == fbFxDeId) {
				if (orgRcj.standardId === convertRule.findStandId) {
					// 删除本人材机的配合比
					unit.rcjDetailList = unit.rcjDetailList.filter(
						(f: { rcjId: any }) => f.rcjId !== orgRcj.sequenceNbr
					);
					// 替换人材机
					let sql =
						"select sequence_nbr as sid from base_rcj where library_code = ? and material_code = ?";
					let baseIndex = (
						this.app.betterSqlite3DataSource
							.prepare(sql)
							.get(convertRule.toLib, convertRule.toCode) as any
					).sid;
					let { rcj, pb } =
						await this.service.rcjProcess.addRcjLineOnOptionMenu(
							constructId,
							singleId,
							unitId,
							baseIndex,
							fbFxDeId
						);
					let oriStand = unit.constructProjectRcjs[i].standardId;
					unit.constructProjectRcjs[i] = rcj;
					unit.constructProjectRcjs[i].standardId = oriStand;
					// 存入配合比
					unit.rcjDetailList.concat(pb);
					infRcjs.push(unit.constructProjectRcjs[i]);
				}
			}
		}
	}

	_excuteKind3CRule(
		unit: { constructProjectRcjs: string | any[] },
		fbFxDeId: any,
		convertRule: { newDe: any; math: any }
	): any {
		// c,s,u
		let constructId = ParamUtils.getPatram("commonParam").constructId;
		let singleId = ParamUtils.getPatram("commonParam").singleId;
		let unitId = ParamUtils.getPatram("commonParam").unitId;

		// 1 查fbFxDeId 得到定额行 并得出是fbfx 还是 csxm
		let deInfo =
			this.service.baseBranchProjectOptionService.findLineOnlyById(fbFxDeId);
		let deLine = deInfo.line;
		let type = deInfo.belong;
		// 2.根据根据定额id查询人材机
		let sql =
			"select *\n" +
			"from base_rcj\n" +
			"where sequence_nbr in (select rcj_id from base_de_rcj_relation where quota_id = ?)";
		let sqlRes = this.app.betterSqlite3DataSource
			.prepare(sql)
			.all(convertRule.newDe);
		let baseRcjs = SqlUtils.convertToModel(sqlRes);
		// 3.对新人才记得 resQty执行math
		for (let i = 0; i < baseRcjs.length; ++i) {
			baseRcjs[i].resQty = NumberUtil.numberScale(
				eval(baseRcjs[i].resQty + convertRule.math),
				6
			);
			baseRcjs[i].isBorrow = true;
			baseRcjs[i].deId = fbFxDeId;
			baseRcjs[i].sequenceNbr = Snowflake.nextId();
			// todo 处理合计数量，合价
		}
		// 4.将新的人材机添加到当前定额下
		unit.constructProjectRcjs = unit.constructProjectRcjs.concat(baseRcjs);

		return [];
	}

	_excuteKind3BRule(
		unit: any,
		fbFxDeId: any,
		convertRule: { newDe: any; math: any }
	): any {
		// c,s,u
		let constructId = ParamUtils.getPatram("commonParam").constructId;
		let singleId = ParamUtils.getPatram("commonParam").singleId;
		let unitId = ParamUtils.getPatram("commonParam").unitId;

		// 1 查fbFxDeId 得到定额行 并得出是fbfx 还是 csxm
		let deInfo =
			this.service.baseBranchProjectOptionService.findLineOnlyById(fbFxDeId);
		let deLine = deInfo.line;
		let type = deInfo.belong;
		// 2.根据 fbfx 还是 csxm 新增数据
		let sql = "select * from base_de where sequence_nbr = ?";
		let sqlRes = this.app.betterSqlite3DataSource
			.prepare(sql)
			.all(convertRule.newDe);
		let convertRes = SqlUtils.convertToModel(sqlRes);

		let newDataInfo: any;
		if (type == "fbfx") {
			newDataInfo = (
				this.service.itemBillProjectOptionService.fillDataFromIndexPage as any
			)(
				constructId,
				singleId,
				unitId,
				deLine,
				"04",
				convertRes[0].sequenceNbr,
				convertRes[0].unit
			);
		} else {
			newDataInfo = (
				this.service.stepItemCostService.fillDataFromIndexPage as any
			)(
				constructId,
				singleId,
				unitId,
				deLine,
				"04",
				convertRes[0].sequenceNbr,
				convertRes[0].unit
			);
		}
		// 3.对新数据的的人材机进行计算
		let newRcjs = this.service.gongLiaoJiProject.gljProjectCommonService.getRcjList(
			constructId,
			unitId
		).filter((f: { deId: any }) => f.deId === newDataInfo.data.sequenceNbr);
		if (newRcjs && newRcjs.length > 0) {
			newRcjs.forEach((rcj: { resQty: any }) => {
				rcj.resQty = NumberUtil.numberScale(
					eval(rcj.resQty + convertRule.math),
					6
				);
			});
		}
		// 4.新数据和当前数据进行关联
		deLine.createDeId = newDataInfo.data.sequenceNbr;
		newDataInfo.data.relationDeId = deLine.sequenceNbr;
		// 5.处理新定额的工程量表达式
		newDataInfo.quantityExpression = "GLZDE";
		newDataInfo.quantityExpressionNbr = deLine.quantityExpressionNbr;
		let unitNum = Number.parseInt(newDataInfo.unit);
		if (Number.isNaN(unitNum) || 0 == unitNum) {
			unitNum = 1;
		}
		newDataInfo.quantity = newDataInfo.quantityExpressionNbr / unitNum;

		return [];
	}

	_excuteKind3ARule(
		unit: { constructProjectRcjs: string | any[] },
		fbFxDeId: any,
		convertRule: { ruleId: string | number; math: any },
		saveNote: { infRcjIds: { [x: string]: any } }
	) {
		let infRcjs = [];
		for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
			let rcj = unit.constructProjectRcjs[i];
			if (rcj.deId == fbFxDeId) {
				if (!rcj.lastResQty) {
					rcj.lastResQty = rcj.resQty;
				}
				// 如果对于本规则，人材机是未激活状态 则不执行
				if (rcj.ruleDeActive && rcj.ruleDeActive[convertRule.ruleId]) {
					continue;
				}
				// 如果当前规则被执行过，则先反算
				if (saveNote.infRcjIds[rcj.sequenceNbr]) {
				}
				// 正常执行
				rcj.resQty = rcj.lastResQty;
				rcj.resQty = NumberUtil.numberScale(
					eval(rcj.resQty + convertRule.math),
					6
				);
				infRcjs.push(rcj);
			}
		}

		return infRcjs;
	}

	_excuteKind1Rule(
		unit: { constructProjectRcjs: string | any[] },
		fbFxDeId: any,
		convertRule: {
			ruleId: string | number;
			find: { attr: string | number; val: any };
			math: any;
		}
	) {
		let infRcjs = [];
		for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
			let rcj = unit.constructProjectRcjs[i];
			if (rcj.deId == fbFxDeId) {
				// 如果对于本规则，人材机是未激活状态 则不执行
				if (rcj.ruleDeActive && rcj.ruleDeActive[convertRule.ruleId]) {
					continue;
				}
				// 正常执行
				if (!convertRule.find.attr) {
					// *num形式的规则
					rcj.resQty = NumberUtil.numberScale(
						eval(rcj.resQty + convertRule.math),
						6
					);
					infRcjs.push(rcj);
					continue;
				}
				if (
					rcj[convertRule.find.attr] == convertRule.find.val ||
					this.rcjKindConvert[rcj[convertRule.find.attr]] ===
						convertRule.find.val
				) {
					// RCJ，code
					rcj.resQty = NumberUtil.numberScale(
						eval(rcj.resQty + convertRule.math),
						6
					);
					infRcjs.push(rcj);
				}
			}
		}

		return infRcjs;
	}

	convertRule(excuteRule: any, saveNote: any) {
		let convertRules = [];
		let excuteKind = excuteRule.kind;
		let excuteType = excuteRule.type;
		let kindMath = excuteRule.math;
		if (excuteKind == 1) {
			// 类型1 有两种表达式  R*1.2  ； HAE1-0011 AE1-0011 0
			let rules = kindMath.split(",");
			for (let i = 0; i < rules.length; ++i) {
				let oneRule = rules[i];
				convertRules.push(
					this.doConvertKind1Rule(excuteRule.sequenceNbr, oneRule, excuteType)
				);
			}
		} else if (excuteKind == 2) {
			convertRules.push(this.doConvertKind2Rule(excuteRule));
		} else if (excuteKind == 3) {
			let rules = kindMath.split(",");
			for (let i = 0; i < rules.length; ++i) {
				let oneRule = rules[i];
				convertRules.push(this.doConvertKind3Rule(excuteRule, oneRule));
			}
		}
		return convertRules;
	}

	doConvertKind3Rule(excuteRule: any, oneRule: any) {
		let bdRule = excuteRule.oriRule;

		if (bdRule.type == "a") {
			return this._convertKind3A(bdRule, excuteRule);
		}
		if (bdRule.type == "b") {
			return this._convertKind3b(bdRule, excuteRule);
		}
		if (bdRule.type == "c") {
			return this._convertKind3c(bdRule, excuteRule);
		}
		if (bdRule.type == "d") {
			return this._convertKind3d(bdRule, excuteRule, oneRule);
		}
	}

	_convertKind3d(
		bdRule: { sequenceNbr: any },
		excuteRule: { consumerInput: any },
		oneRule: string
	) {
		let findType, findAttr, math;
		// 两种格式 ED1-0041 +169*(V-4)，ZA1-0002 +0.042*(V-4)  R*(1+(RU(V-30))*3%)，J*(1+(RU(V-30))*3%);
		let splitR = oneRule.split(" ");
		if (splitR.length > 0) {
			// 是  编码 算式
			findType = "materialCode";
			findAttr = oneRule.split(" ")[0];
			math = oneRule.split(" ")[1];
		} else {
			let single = oneRule.match("[*+-/]")[0];
			let singleIndex = oneRule.match("[*+-/]").index;
			if (singleIndex == 0) {
				// 是 *Num格式
				findType = undefined;
				findAttr = undefined;
				math = oneRule.substring(singleIndex);
			} else {
				// 是 （R/C/J）*Num格式
				findType = "kind";
				findAttr = oneRule.substring(0, singleIndex);
				math = oneRule.substring(singleIndex);
			}
		}

		// 替换math中的数据
		// 替换 ru rd v
		let rega = new RegExp("\\b\\V\\b", "g");
		let regb = new RegExp("\\b(RU)\\b", "g");
		let regc = new RegExp("\\b(RD)\\b", "g");
		let regd = new RegExp("\\b(RD)\\b", "g");
		let convertMath = math.replace(rega, excuteRule.consumerInput);
		convertMath = convertMath.replace(regb, "Math.ceil");
		convertMath = convertMath.replace(regc, "Math.floor");
		convertMath = convertMath.replace(regd, "*0.01");
		// 处理 ^
		if (convertMath.indexOf("^") > 0) {
			let params = convertMath.split("^");
			params[0] = params[0].substring(1);
			convertMath = "*" + "Math.pow(" + params[0] + " , " + params[1] + ")";
		}

		return {
			type: 3,
			ruleId: bdRule.sequenceNbr,
			find: {
				// 用于标识怎么找人材机
				attr: findType,
				val: findAttr,
			},
			math: convertMath, // 用于对人材机的消耗量进行计算
		};
	}

	_convertKind3c(bdRule: { math: any }, excuteRule: { consumerInput: any }) {
		let oriMath = bdRule.math;
		let rega = new RegExp("\\b\\V\\b", "g");
		let regb = new RegExp("\\b(RU)\\b", "g");
		let regc = new RegExp("\\b(RD)\\b", "g");
		let convertMath = oriMath.replace(rega, excuteRule.consumerInput);
		convertMath = convertMath.replace(regb, "Math.ceil");
		convertMath = convertMath.replace(regc, "Math.floor");

		return {
			type: 3,
			kindType: "b",
			newDe: oriMath.relationDeId,
			math: convertMath, // 用于对人材机的消耗量进行计算
		};
	}

	_convertKind3b(bdRule: { math: any }, excuteRule: { consumerInput: any }) {
		let oriMath = bdRule.math;
		let rega = new RegExp("\\b\\V\\b", "g");
		let regb = new RegExp("\\b(RU)\\b", "g");
		let regc = new RegExp("\\b(RD)\\b", "g");
		let convertMath = oriMath.replace(rega, excuteRule.consumerInput);
		convertMath = convertMath.replace(regb, "Math.ceil");
		convertMath = convertMath.replace(regc, "Math.floor");

		return {
			type: 3,
			kindType: "b",
			newDe: oriMath.relationDeId,
			math: convertMath, // 用于对人材机的消耗量进行计算
		};
	}

	_convertKind3A(bdRule: { math: any }, excuteRule: { consumerInput: any }) {
		let oriMath = bdRule.math;
		// 处理 ，n = XXX
		if (oriMath.indexOf("n=") > 0) {
			let splitMath = oriMath.split("，");
			let mathStr = splitMath[0];
			let nstr = splitMath[1].substring(2);
			let regn = new RegExp("\\b(n)\\b", "g");
			oriMath = mathStr.replace(regn, nstr);
		}
		// 替换 ru rd v
		let rega = new RegExp("\\b\\V\\b", "g");
		let regb = new RegExp("\\b(RU)\\b", "g");
		let regc = new RegExp("\\b(RD)\\b", "g");
		let convertMath = oriMath.replace(rega, excuteRule.consumerInput);
		convertMath = convertMath.replace(regb, "Math.ceil");
		convertMath = convertMath.replace(regc, "Math.floor");
		// 处理 ^
		if (convertMath.indexOf("^") > 0) {
			let params = convertMath.split("^");
			params[0] = params[0].substring(1);
			convertMath = "*" + "Math.pow(" + params[0] + " , " + params[1] + ")";
		}

		return {
			type: 3,
			kindType: "a",
			math: convertMath, // 用于对人材机的消耗量进行计算
		};
	}

	doConvertKind2Rule(excuteRule: { math: string }) {
		// excuteRule.math = 被转换的rcj标砖id+"-"+要转换的人材机库+":"+要转换的人材机编码;
		let findCode = excuteRule.math.split("^")[0];
		let toInfo = excuteRule.math.split("^")[1];
		let toLib = toInfo.split(":")[0];
		let toCode = toInfo.split(":")[1];

		return {
			type: 2,
			findStandId: findCode,
			toLib: toLib,
			toCode: toCode,
		};
	}

	doConvertKind1Rule(ruleId: any, oneRule: string, excuteKind: string) {
		let singleBack: any = { "+": "-", "*": "/", "-": "+", "/": "*" };
		// R*1.2  或者 HAE1-0011 AE1-0011 0
		let findType, findAttr;
		let math;
		if (oneRule.indexOf(" ") == -1) {
			// 是 （R/C/J）+-*/ (num)
			let single = oneRule.match("[*+-/]")[0];
			let singleIndex = oneRule.match("[*+-/]").index;
			if (singleIndex == 0) {
				// 是 *Num格式
				findType = undefined;
				findAttr = undefined;
				let mathSingle = excuteKind == "caculate" ? single : singleBack[single];
				math = mathSingle + oneRule.substring(singleIndex + 1);
			} else {
				findType = "kind";
				findAttr = oneRule.substring(0, singleIndex);
				let mathSingle = excuteKind == "caculate" ? single : singleBack[single];
				math = mathSingle + oneRule.substring(singleIndex + 1);
			}
		} else {
			// 是 Hcode code ?Num
			findType = "materialCode";
			findAttr = oneRule.split(" ")[1];
			let orgMath = oneRule.split(" ")[2];
			let mathRes = orgMath.match("[*+-/]");
			let single, num;
			if (!mathRes) {
				single = "+";
				num = orgMath;
			} else {
				single = mathRes[0];
				num = orgMath.substring(mathRes.index + 1);
			}
			single = excuteKind == "caculate" ? single : singleBack[single];
			math = single + num;
		}

		return {
			type: 1,
			excuteKind: excuteKind,
			ruleId: ruleId,
			find: {
				// 用于标识怎么找人材机
				attr: findType,
				val: findAttr,
			},
			math: math, // 用于对人材机的消耗量进行计算
		};
	}
}

GljConversionDeProcess.toString = () => "[class GljConversionDeProcess]";
module.exports = GljConversionDeProcess;
