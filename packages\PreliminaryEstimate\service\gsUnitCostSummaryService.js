const {Service} = require("../../../core");
const gsJzFyhz = require("../jsonData/gs_jz_fyhz.json");
const gsAzFyhz = require("../jsonData/gs_az_fyhz.json");
const gsFyhzTotal = require("../jsonData/gs_fyhz_total.json");
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../utils/NumberUtil");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const FreeRateModel = require("../domains/projectProcessor/models/FreeRateModel");
const ConstructMajorTypeEnum = require("../enums/ConstructMajorTypeEnum");
const OtherProjectCostOptionMenuConstants = require("../constants/OtherProjectCostOptionMenuConstants");
const xeUtils = require("xe-utils");
const FileOperatorType = require("../constants/FileOperatorType");
const UtilsPs = require("../../../core/ps");
const fs = require("fs");
const path = require('path');
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {BrowserWindow, dialog} = require('electron');
const {ResponseData} = require("../../../common/ResponseData");
const {GsUnitCostSummary} = require("../models/GsUnitCostSummary");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {getConnection, getRepository, getManager} = require('typeorm');
const WildcardMap = require("../core/container/WildcardMap");
const {FileUtils} = require("../utils/FileUtils.js");
const XLSX = require('xlsx');
const RcjCommonConstants = require("../constants/RcjCommonConstants");
const {GsUnitCostSummaryTotal} = require("../models/GsUnitCostSummaryTotal");
const {readdirSync} = require("fs");
const {statSync} = require("fs");


/**
 * 判断字段是否包含在枚举中，并返回匹配的 key
 * @param {string} value - 要检查的字段值
 * @param {Object} enumObj - 枚举对象
 * @returns {string|undefined} - 返回匹配的 key，如果没有找到则返回 undefined
 */
function getEnumKeyByCode(value, enumObj) {
    for (const [key, item] of Object.entries(enumObj)) {
        if (value.includes(item.desc)) {
            return item.code;
        }
    }
    return undefined;
}


function getFiles(currentPath, filter = item => true) {
    let itemsInfo = [];
    const items = readdirSync(currentPath);
    for (const item of items) {
        const itemPath = path.join(currentPath, item);
        const stat = statSync(itemPath);

        const itemInfo = {
            name: path.parse(itemPath).name,  // item
            type: getEnumKeyByCode(path.parse(itemPath).name, ConstructMajorTypeEnum),
            path: itemPath,
            size: stat.size,
            createdAt: stat.ctime,
            modifiedAt: stat.mtime,
            isDirectory: stat.isDirectory(),
            children: []
        };

        if (filter(itemInfo)) {
            itemsInfo.push(itemInfo);
        }
    }
    return itemsInfo;
}

function getAllFiles(dirPath, filter = item => true) {
    function traverseDirectory(currentPath) {
        const items = readdirSync(currentPath);
        const itemsInfo = [];

        for (const item of items) {
            const itemPath = path.join(currentPath, item);
            const stat = statSync(itemPath);

            const itemInfo = {
                // name: item,
                name: path.parse(itemPath).name,
                type: getEnumKeyByCode(path.parse(itemPath).name, ConstructMajorTypeEnum),
                path: itemPath,
                size: stat.size,
                createdAt: stat.ctime,
                modifiedAt: stat.mtime,
                isDirectory: stat.isDirectory(),
                children: []
            };

            if (itemInfo.isDirectory) {
                itemInfo.children = traverseDirectory(itemPath);
            }
            if (filter(itemInfo)) {
                itemsInfo.push(itemInfo);
            }

        }
        return itemsInfo;
    }
    return traverseDirectory(dirPath);
}


/**
 * 单位费用汇总
 */
class GsUnitCostSummaryService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取默认费用汇总
     * @returns {any[]}
     */
    defaultUnitCostSummary(args) {
        let {constructId, singleId, unitId, constructMajorType} = args;
        // 获取单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 判断工程专业
        if (ObjectUtils.isEmpty(constructMajorType)) {
            constructMajorType = unitProject.constructMajorType;
        }
        // 获取费用汇总模板
        let gsFyhz;
        if (constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
            gsFyhz = gsJzFyhz;
        }
        if (constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
            gsFyhz = gsAzFyhz;
        }

        // 根据名称和路径获取模板数据
        let countUnitCostSummaryArray = [];
        let sort = 1;
        for (let i in gsFyhz) {
            sort++;
            let obj = new GsUnitCostSummary();
            ConvertUtil.setDstBySrc(gsFyhz[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            obj.orderNum = sort;
            obj.whetherPrint = 1;
            obj.unitId = unitId;
            obj.price = 0;
            obj.adopted = false;  // 是否被引用
            obj.isUpdateRate = false;  // 费率是否被修改
            obj["permission"] = OtherProjectCostOptionMenuConstants.Item;
            countUnitCostSummaryArray.push(obj);
        }
        return countUnitCostSummaryArray;
    }


    /**
     * 计算费用汇总
     * @param constructId
     * @param unitId
     * @param unitCostCodePriceArray
     * @param unitCostSummaryArray
     * @param constructMajorType
     * @returns {*}
     */
    async countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, constructMajorType) {
        // 计算费用汇总条目
        await this.countUnitCostSummaryItem(constructId, unitCostCodePriceArray, unitCostSummaryArray);
        // 更新费用汇总
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostSummaryArray);

        // 更新单位的工程造价、工程规模
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;

        if (ObjectUtils.isNotEmpty(unitCostCodePriceArray)) {
            unitProject.average = unitCostCodePriceArray.find(item => item.code === "GCGM").price;   // 工程规模
        }
        if (ObjectUtils.isNotEmpty(unitCostSummaryArray) && unitProject.qfMajorTypeMoneyMap) {
            let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));
            let qfMajorTypeMoney = NumberUtil.numberScale(unitCostSummaryArray.find(item => item.category === "工程造价").price, je);
            // 单专业
            if (unitProject.isSingleMajorFlag) {
                qfMajorTypeMoneyMap.set(unitProject.qfMajorType, qfMajorTypeMoney);
            } else {
                // 多专业
                qfMajorTypeMoneyMap.set(constructMajorType, qfMajorTypeMoney);
            }
            unitProject.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
            const sum = Array.from(qfMajorTypeMoneyMap.values()).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
            unitProject.projectCost = sum;
            unitProject.unitCost = NumberUtil.numberScale(NumberUtil.divide(sum, unitProject.average), je);  // 单方造价
            ProjectDomain.getDomain(constructId).updateProject(unitProject);
        }

        // 因为建安工程费，更新建设其他费
        await this.service.PreliminaryEstimate.gsOtherProjectCostService.countOtherProjectCostCode({
            projectId: constructId
        });

        // // 计算概算费用代码
        // this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
        //     constructId: constructId
        // });
        return unitCostSummaryArray;
    }

    /**
     * 计算费用汇总条目
     * @param constructId
     * @param unitCostCodePriceArray  费用代码
     * @param unitCostSummaryArray  费用汇总
     */
    async countUnitCostSummaryItem(constructId, unitCostCodePriceArray, unitCostSummaryArray) {
        //费用代码<费用代码,price>
        let priceMap = new Map();
        //计算基数 <费用汇总费用代号,calculateFormula>
        let codeFormulaMap = new Map();
        //费用汇总费率
        let codeRateMap = new Map();

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        //费用代码
        for (let i = 0; i < unitCostCodePriceArray?.length; i++) {
            let unitCostCodePrice = unitCostCodePriceArray[i];
            let code = ObjectUtils.isEmpty(unitCostCodePrice.code) ? unitCostCodePrice.code : unitCostCodePrice.code.toLowerCase();
            priceMap.set(code, unitCostCodePrice.price)
        }
        if (ObjectUtils.isNotEmpty(unitCostSummaryArray)) {
            //费用汇总
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                let code = ObjectUtils.isEmpty(unitCostSummary.code) ? unitCostSummary.code : unitCostSummary.code.toLowerCase();
                if (ObjectUtils.isNotEmpty(unitCostSummary.calculateFormula)) {
                    codeFormulaMap.set(code, unitCostSummary.calculateFormula.toLowerCase());
                }
                if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                    let rate = parseFloat(unitCostSummary.rate);
                    if (unitCostSummary.isUpdateRate === true) {
                        codeRateMap.set(code, NumberUtil.numberScale(unitCostSummary.rate, freeRate));
                    } else {
                        codeRateMap.set(code, rate);
                    }
                    // codeRateMap.set(code, rate);
                } else {
                    // unitCostSummary.rate = 100;
                    codeRateMap.set(code, 100);
                }
            }
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                if (ObjectUtils.isEmpty(unitCostSummary.calculateFormula)) {
                    continue;
                }
                //计算基数
                let calculateFormula = unitCostSummary.calculateFormula.toLowerCase();
                // 分解字符串成表达式和变量名
                const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
                //存放替换后的计算公式
                let afterCalculateFormula = calculateFormula;

                //递归计算费用汇总
                afterCalculateFormula = await this.recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je);

                let result;
                if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                    // result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * NumberUtil.numberScale(unitCostSummary.rate / 100, freeRate);
                    if (unitCostSummary.isUpdateRate === true) {
                        result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * NumberUtil.numberScale(unitCostSummary.rate / 100, freeRate);
                    } else {
                        result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * unitCostSummary.rate / 100;
                    }
                } else {
                    result = ObjectUtils.isEmpty(afterCalculateFormula) ? 0 : NumberUtil.numberScale(eval(afterCalculateFormula), je);
                }
                unitCostSummary.price = NumberUtil.numberScale(result, je);
                priceMap.set(ObjectUtils.isEmpty(unitCostSummary.code) ? unitCostSummary.code : unitCostSummary.code.toLowerCase(), unitCostSummary.price);
            }
        }
    }

    /**
     * 递归计算费用汇总
     * @param calculateFormula 计算基数
     * @param afterCalculateFormula  存放替换后的计算公式
     * @param codeFormulaMap 计算基数 <费用汇总费用代号,calculateFormula>
     * @param priceMap 费用代码<费用代码,price>
     * @param variablesToReplace 拆分计算基数后的数组
     * @param codeRateMap
     * @param je  金额精度
     * @returns {*}
     */
    async recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je) {
        for (let variable of variablesToReplace) {
            if (priceMap.has(variable) && ObjectUtils.isNotEmpty(afterCalculateFormula)) {  // todo
                if (priceMap.get(variable) < 0) {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + NumberUtil.numberScale(priceMap.get(variable), je) + ')');
                } else {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, NumberUtil.numberScale(priceMap.get(variable), je));
                }
            } else {
                if (isNaN(Number(variable))) {
                    if (codeFormulaMap.has(variable) && ObjectUtils.isNotEmpty(codeFormulaMap.get(variable)) && ObjectUtils.isNotEmpty(afterCalculateFormula)) {
                        let variablesToReplace1 = codeFormulaMap.get(variable).replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                        //说明当前行引用了费用代号 此处需要加上费率
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + variable + ')/100*' + codeRateMap.get(variable));
                        afterCalculateFormula = afterCalculateFormula.replace(variable, codeFormulaMap.get(variable));
                        afterCalculateFormula = await this.recursionSummary(codeFormulaMap.get(variable), afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace1, codeRateMap, je);
                    } else {
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '0'); // 比如E
                    }
                }
            }
        }
        return afterCalculateFormula;
    }

    /**
     * 导出局部费用汇总
     * @param args
     */
    async exportUnitPartCostSummary(args) {
        let {constructId, unitId, constructMajorType, rowList, isFyhz} = args;

        // 存放局部汇总文件的路径
        const exportDir = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\局部汇总';
        FileUtils.ensurePathExists(exportDir)
        let count = FileUtils.countDirectories(exportDir);
        let fileName = '局部费用汇总'
        if (constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
            fileName = '局部建筑工程费用汇总';
        } else if (constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
            fileName = '局部安装工程费用汇总';
        }

        let options = {
            title: '保存文件',
            defaultPath: exportDir + '\\' + fileName + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: ['xlsx']} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.endsWith('xlsx')) {
                filePath += 'xlsx';
            }

            let i = 1;
            // 提取需要的字段
            let filteredData = rowList.map(item => ({
                '序号': i++,
                '费用名称': item.name,
                '取费说明': item.instructions,
                '费率': ObjectUtils.isEmpty(item.rate) ? 100 : item.rate,
                '费用金额': item.price
            }));
            if (isFyhz === true) {
                i = 1;
                filteredData = rowList.map(item => {
                    if (item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ
                        || item.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                        item.instructions = "直接费+企业管理费+规费+利润+价款调整+独立费+安全生产、文明施工费+税金"
                    }
                    if (item.name === "工程造价") {
                        item.instructions = "专业造价总合计"
                    }
                    let headers = {
                        '序号': i++,
                        '费用名称': item.name,
                        '取费说明': item.instructions,
                        '费率': ObjectUtils.isEmpty(item.rate) ? 100 : item.rate,
                        '费用金额': item.price
                    }
                    return headers;
                });
            }

            // 转换数据为工作表
            const worksheet = XLSX.utils.json_to_sheet(filteredData);

            // 创建工作簿并添加工作表
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
            // 写入Excel文件
            XLSX.writeFile(workbook, filePath);
            return ResponseData.success(filePath);
        }
    }

    /**
     * 获取局部费用汇总
     * @param args
     */
    async getUnitPartCostSummaryList2(args) {
        let {constructId, unitId, constructMajorType, deLists, isCostSummary} = args;

        let unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(constructId, unitId);
        if (ObjectUtils.isEmpty(constructMajorType)) {
            constructMajorType = unitProject.constructMajorType;
        }
        let partDeRowIds = isCostSummary === true ? deLists.filter(item => item.type !== '0' && item.isSelected === 1).map(item => item.deRowId)
            : deLists.filter(item => item.type !== '0' && item.isSelected === 1 && item.costFileCode === constructMajorType).map(item => item.deRowId)

        let independentCostAllList = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            .get(this.service.PreliminaryEstimate.gsIndependentCostsService.getDataMapKey(unitId))
        independentCostAllList = independentCostAllList?.filter(item => item.costMajorCode === constructMajorType);
        // 获取费用代码
        let costCodePrices = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
        //费用代码
        let unitCostCodePrices = costCodePrices.get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let rcjAllList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        if (partDeRowIds !== undefined && (ObjectUtils.isNotEmpty(partDeRowIds) || partDeRowIds.length >= 0)) {
            rcjAllList = rcjAllList.filter(item => partDeRowIds.includes(item.deRowId));
        }
        let rcjList = [];
        rcjAllList.forEach(item => {
            if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs) && item.markSum === RcjCommonConstants.MARKSUM_JX) {
                item.pbs.forEach(item2 => {
                    item2.costFileCode = item.costFileCode;
                    rcjList.push(item2);
                })
            } else {
                rcjList.push(item)
            }
        });

        // 计算费用代码条目
        await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePriceItems(rcjList, independentCostAllList, unitCostCodePrices, constructId, unitId);

        let unitCostSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        unitCostSummaryArray = ConvertUtil.deepCopy(unitCostSummaryArray);
        // 计算费用汇总
        await this.countUnitCostSummaryItem(unitCostCodePrices, unitCostSummaryArray);

        return unitCostSummaryArray;
    }

    async getUnitPartCostSummaryList(args) {
        let {constructId, unitId, constructMajorType, deLists, isCostSummary} = args;

        let unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(constructId, unitId);
        if (ObjectUtils.isEmpty(constructMajorType)) {
            constructMajorType = unitProject.constructMajorType;
        }
        let partDeRowIds = isCostSummary === true ? deLists.filter(item => item.type !== '0' && item.isSelected === 1).map(item => item.deRowId)
            : deLists.filter(item => item.type !== '0' && item.isSelected === 1 && item.costFileCode === constructMajorType).map(item => item.deRowId)

        // let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        // let budgetBookTypes, csxmTypes, independentCostTypes, yssAllRcjs = [];
        // 1. 获取预算书定额的所有取费专业类型    //  && item.type === DeTypeConstants.DE_TYPE_DE
        let yssDes0 = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId && ObjectUtils.isNotEmpty(item.costFileCode));
        let yssDes = yssDes0.filter(item => partDeRowIds.includes(item.deRowId));
        // if (ObjectUtils.isNotEmpty(yssDes)) {
        //     budgetBookTypes = yssDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        // }

        // 2. 独立费的所有取费专业类型
        let independentCostAllList = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            .get(this.service.PreliminaryEstimate.gsIndependentCostsService.getDataMapKey(unitId))
        independentCostAllList = independentCostAllList?.filter(item => item.costMajorCode === constructMajorType);
        // if (ObjectUtils.isNotEmpty(independentCosts)) {
        //     independentCostTypes = independentCosts.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costMajorCode)).map(item => item.costMajorCode);
        // }

        // 获取费用代码
        let costCodePrices = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
        //费用代码
        let unitCostCodePrices = costCodePrices.get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        // 3. 获取当前单位所有的人材机
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let rcjAllList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        if (partDeRowIds !== undefined && (ObjectUtils.isNotEmpty(partDeRowIds) || partDeRowIds.length >= 0)) {
            rcjAllList = rcjAllList.filter(item => partDeRowIds.includes(item.deRowId));
        }
        // // 封装定额Map
        // const budgetBookDeMap = new Map(yssDes.map(item => [item.deRowId, item]));
        // // 给定额下挂的人材机添加工程专业
        // for (let i = 0; i < rcjAllList1.length; i++) {
        //     let rcj = rcjAllList1[i];
        //     // // 7.1 获取预算书人材机
        //     // if (yssDeMap.has(rcj.deRowId)) {
        //     //     let yssDe = yssDeMap.get(rcj.deRowId);
        //     //     if (ObjectUtils.isNotEmpty(yssDe)) {
        //     //         rcj.costFileCode = yssDe.costFileCode;
        //     //         yssAllRcjs.push(rcj);
        //     //     }
        //     // }
        //     let de = budgetBookDeMap.get(rcj.deRowId);
        //     if (ObjectUtils.isNotEmpty(de)) {
        //         rcj.costFileCode = de.costFileCode;
        //     }
        //     // 安装费用全局、单独计取
        //     if (de.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtils.isNotEmpty(de.calculateMethod) && de.calculateMethod === AnZhuangJiQqConstants.AnZhuangJiQq_ALl) {
        //
        //     } else {
        //         rcjAllList.push(rcj);
        //     }
        // }

        let rcjList = [];
        rcjAllList.forEach(item => {
            if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs) && item.markSum === RcjCommonConstants.MARKSUM_JX) {
                item.pbs.forEach(item2 => {
                    item2.costFileCode = item.costFileCode;
                    rcjList.push(item2);
                })
            } else {
                rcjList.push(item)
            }
        });

        // 计算费用代码条目
        await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePriceItems(yssDes, rcjList, independentCostAllList, unitCostCodePrices, constructId, unitId);

        let unitCostSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        unitCostSummaryArray = ConvertUtil.deepCopy(unitCostSummaryArray);
        // 计算费用汇总
        await this.countUnitCostSummaryItem(constructId, unitCostCodePrices, unitCostSummaryArray);

        return unitCostSummaryArray;
    }


    /**
     * 局部多专业汇总-多专业汇总后的总金额合计
     * @param args
     * @returns {null}
     */
    // async getPartCostSummaryMajorsTotal2(args) {
    //     let {constructId, unitId, deLists} = args;
    //     let unitCostSummaryMap = new Map();
    //     let jz_zy = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ;
    //     let jz_param = {constructId, unitId, deLists, constructMajorType: jz_zy}
    //     let jzCostSummary = await this.getUnitPartCostSummaryList(jz_param);
    //     let jzGczj = jzCostSummary.find(item => item.name === '工程造价')?.price;
    //     unitCostSummaryMap.set(unitId + '-' + jz_zy, jzCostSummary);
    //     let az_zy = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ;
    //     let az_param = {constructId, unitId, deLists, constructMajorType: az_zy}
    //     let azCostSummary = await this.getUnitPartCostSummaryList(az_param);
    //     let azGczj = azCostSummary.find(item => item.name === '工程造价')?.price;
    //     unitCostSummaryMap.set(unitId + '-' + az_zy, azCostSummary);
    //
    //     // 判断unitCostSummaryMap中key包含unitId的个数
    //     let count = 0;  // 获取unitCostSummaryMap
    //     Array.from(unitCostSummaryMap.keys()).forEach(key => {
    //         if (key.includes(unitId)) {
    //             count++;
    //         }
    //     });
    //
    //     // 未补充专业，不显示
    //     if (count < 2) {
    //         return null;
    //     }
    //
    //     // 获取费用汇总合计
    //     let unitCostCodeSummaryTotals = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL).get(unitId);
    //     // 获取当前单位
    //     let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
    //
    //     let costSummaryTotal = [];
    //     for (let i in unitCostCodeSummaryTotals) {
    //         let costSummary = unitCostCodeSummaryTotals[i];
    //
    //         if (costSummary.name === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ_NAME) {
    //             costSummary.price = jzGczj;    // 工程造价
    //             costSummaryTotal.push(costSummary);
    //         }
    //         if (costSummary.name === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ_NAME) {
    //             costSummary.price = azGczj;    // 工程造价
    //             costSummaryTotal.push(costSummary);
    //         }
    //         if (costSummary.name === '工程造价') {
    //             costSummary.price = NumberUtil.accAdd(jzGczj, azGczj);
    //             costSummaryTotal.push(costSummary);
    //         }
    //     }
    //     return costSummaryTotal;
    // }


    async getPartCostSummaryMajorsTotal(args) {
        let {constructId, unitId, deLists} = args;

        // 获取该单位下的存放map<取费专业，取费费用汇总工程造价>
        let qfMajorTypeMoneyMap = new Map();
        let jz_zy = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ;
        let jz_param = {constructId, unitId, deLists, constructMajorType: jz_zy}
        let jzCostSummary = await this.getUnitPartCostSummaryList(jz_param);
        qfMajorTypeMoneyMap.set(jz_zy, jzCostSummary.find(item => item.name === '工程造价')?.price);
        let az_zy = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ;
        let az_param = {constructId, unitId, deLists, constructMajorType: az_zy}
        let azCostSummary = await this.getUnitPartCostSummaryList(az_param);
        qfMajorTypeMoneyMap.set(az_zy, azCostSummary.find(item => item.name === '工程造价')?.price);

        // 获取费用汇总合计
        let unitCostCodeSummaryTotals = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL).get(unitId);

        let costSummaryTotals = [];
        let dispNo = 1;
        // 未补充专业，不显示
        for (let i in unitCostCodeSummaryTotals) {
            let qfModel = unitCostCodeSummaryTotals[i];
            // 获取定额中所有的专业
            if (Array.from(qfMajorTypeMoneyMap.keys()).includes(qfModel.code)) {
                let costSummary = new GsUnitCostSummaryTotal();
                costSummary.dispNo = dispNo++;
                costSummary.code = qfModel.code;
                costSummary.name = qfModel.name;
                costSummary.price = qfMajorTypeMoneyMap.get(qfModel.code);
                costSummaryTotals.push(costSummary);
            }
        }
        let costSummaryTotal = new GsUnitCostSummaryTotal();
        costSummaryTotal.dispNo = costSummaryTotals.length + 1;
        costSummaryTotal.name = "工程造价";
        costSummaryTotal.price = costSummaryTotals.reduce((sum, obj) => {
            return sum + (obj.price || 0); // 如果 obj.age 为 null 或 undefined，则视为 0
        }, 0);
        costSummaryTotals.push(costSummaryTotal);
        return costSummaryTotals;
    }

    /**
     * 获取费用汇总
     * @param args
     * @returns {*}
     */
    async getUnitCostSummaryList(args) {
        let {constructId, singleId, unitId, constructMajorType} = args;
        let unitCostSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        if (ObjectUtils.isNotEmpty(unitCostSummaryArray)) {
            // 复制 添加是否被引用标识
            let unitCostSummarys = ConvertUtil.deepCopy(unitCostSummaryArray);
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                //判断需要删除的费用汇总中的费用代号是否被引用
                let deleteItem = unitCostSummaryArray[i];
                if (ObjectUtils.isNotEmpty(deleteItem)) {
                    for (let i = 0; i < unitCostSummarys.length; i++) {
                        if (!ObjectUtils.isEmpty(unitCostSummarys[i].calculateFormula)) {
                            let codeList = unitCostSummarys[i].calculateFormula.split(/[+\-*/]/);
                            if (!ObjectUtils.isEmpty(codeList) && !ObjectUtils.isEmpty(deleteItem.code)) {
                                if (this.stringInArray(codeList, deleteItem.code)) {
                                    deleteItem.adopted = true;  // 该行已被引用，不可删除
                                }
                            }
                        }
                    }
                }
            }
            // 添加序号
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                unitCostSummaryArray[i].sortIndex = i + 1;
            }
        }
        return unitCostSummaryArray;
    }

    /**
     * 新增粘贴费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async addCostSummary(args) {
        let {constructId, singleId, unitId, constructMajorType, unitCostSummary} = args;
        // 添加元素位置
        let lineNumber = args.lineNumber;

        // 获取费用汇总
        let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        // 新增行
        let addUnitCostSummary = [];
        unitCostSummary.sequenceNbr = Snowflake.nextId();
        unitCostSummary.whetherPrint = 1;
        unitCostSummary["permission"] = OtherProjectCostOptionMenuConstants.Item;
        unitCostSummary.adopted = false; // 是否被引用
        unitCostSummary.isUpdateRate = false;
        // 添加新增行
        addUnitCostSummary.push(unitCostSummary);
        unitCostSummarys.splice(lineNumber, 0, ...addUnitCostSummary);

        // 更新费用汇总
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostSummarys);
        return unitCostSummarys;
    }

    /**
     * 删除费用汇总(需要判断)
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteCostSummary(args) {
        let {constructId, singleId, unitId, constructMajorType, sequenceNbr} = args;

        // 获取费用汇总
        let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        //判断需要删除的费用汇总中的费用代号是否被引用
        let deleteItem = unitCostSummarys.find(item => item.sequenceNbr === sequenceNbr);
        if (ObjectUtils.isNotEmpty(deleteItem)) {
            for (let i = 0; i < unitCostSummarys.length; i++) {
                if (!ObjectUtils.isEmpty(unitCostSummarys[i].calculateFormula)) {
                    let codeList = unitCostSummarys[i].calculateFormula.split(/[+\-*/]/);
                    if (!ObjectUtils.isEmpty(codeList) && !ObjectUtils.isEmpty(deleteItem.code)) {
                        if (this.stringInArray(codeList, deleteItem.code)) {
                            // return ResponseData.fail('该行已被引用，不可删除');
                            const regex = new RegExp(`(?<![a-zA-Z0-9_])${deleteItem.code}(?![a-zA-Z0-9_])`, 'g');
                            unitCostSummarys[i].calculateFormula = unitCostSummarys[i].calculateFormula.replace(regex, "※");
                            // unitCostSummarys[i].calculateFormula = unitCostSummarys[i].calculateFormula.replaceAll(deleteItem.code, "※");
                            unitCostSummarys[i].instructions = unitCostSummarys[i].instructions.replaceAll(deleteItem.name, "0");
                            // unitCostSummarys[i].price = NumberUtil.subtract(unitCostSummarys[i].price, deleteItem.price);
                        }
                    }
                }
            }
            // 删除该行数据
            let unitCostSummaryArray = unitCostSummarys.filter(item => item.sequenceNbr !== sequenceNbr);

            // 获取费用代码
            let unitCostCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);
            // 调用计算费用汇总
            let unitCostSummaryList = await this.countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, constructMajorType);
            // 更新费用汇总
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
                .set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostSummaryList);
            return ResponseData.success(unitCostSummaryList);
        } else {
            return ResponseData.fail('该行不存在');
        }
    }

    /**
     * 保存修改费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async saveCostSummary(args) {
        let {constructId, singleId, unitId, constructMajorType} = args;
        let param = args.unitCostSummary;

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;
        let je = precision.COST_SUMMARY.je;

        if (ObjectUtils.isEmpty(param)) {
            return ResponseData.fail('参数错误');
        }
        let bussinessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        // 获取费用代码
        let unitCostCodePriceArray = bussinessMap.get(FunctionTypeConstants.UNIT_COST_CODE).get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);
        for (let i = 0; i < unitCostCodePriceArray.length; i++) {
            if (param.code === unitCostCodePriceArray[i].code) {
                return ResponseData.fail('当前费用代号与费用代码重复，请修改');
            }
        }
        // 获取费用汇总
        let unitCostSummaryArray = bussinessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);
        for (let i = 0; i < unitCostSummaryArray.length; i++) {
            if (param.sequenceNbr !== unitCostSummaryArray[i].sequenceNbr && ObjectUtils.isNotEmpty(param.code)
                && ObjectUtils.isNotEmpty(unitCostSummaryArray[i].code) && param.code.toLowerCase() === unitCostSummaryArray[i].code.toLowerCase()) {
                return ResponseData.fail('当前费用代码已被使用');
            }
            if (unitCostSummaryArray[i].category === param.category && unitCostSummaryArray[i].sequenceNbr !== param.sequenceNbr
                && ObjectUtils.isNotEmpty(unitCostSummaryArray[i].category)) {
                return ResponseData.fail('该费用已存在');
            }
        }

        let copyCode = param.code;
        let copyName = param.name;
        if (ObjectUtils.isEmpty(param.code)) {
            param.code = "※";
            param.name = "0";
        }
        let codePriceMap = new Map();  //1费用代号、代码存放<费用代号、代码,price>
        let codeFormulaMap = new Map();  //计算基数
        let codeInstructionsMap = new Map();  //基数说明、费用名称<费用代号、代码,instructions>
        let codeRateMap = new Map();
        let codeNameMap = new Map();
        let currentId = param.sequenceNbr;
        let priceChangeArray = [];
        // let code = param.code;  // 数据备份
        // let calculateFormula = param.calculateFormula; // 数据备份
        if (ObjectUtils.isNotEmpty(param.sequenceNbr)) {
            // 修改
            let unitCostSummary = unitCostSummaryArray.find(item => item.sequenceNbr === param.sequenceNbr);
            if (unitCostSummary == null) {
                return ResponseData.fail('参数错误');
            }

            await this.getAllCodeFormulaPriceMap(constructId, unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, unitCostSummaryArray);

            let lowerParamCalculateFormula = ObjectUtils.isNotEmpty(param.calculateFormula) ? param.calculateFormula.toLowerCase() : param.calculateFormula;  // 转小写
            if (ObjectUtils.isNotEmpty(lowerParamCalculateFormula)) {
                let codeList = lowerParamCalculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                if (ObjectUtils.isNotEmpty(codeList)) {
                    for (let i = 0; i < codeList.length; i++) {
                        // 判断公式中引用的费用代码是不是引用的列表不存在的
                        let keys = [...codeNameMap.keys()].map(function (item) {
                            if (ObjectUtils.isNotEmpty(item)) {
                                return item.toLowerCase();
                            }
                        });
                        if (!keys.includes(codeList[i]) && isNaN(Number(codeList[i]))) {
                            return ResponseData.fail("费用代码输入有误！");
                        }
                    }
                }
            }

            //判断code
            if (ObjectUtils.isNotEmpty(unitCostSummary.code) && param.code !== unitCostSummary.code) {
                // 如果修改了code  那么就要去看这个老的code是不有地方引用了他  如果有引用  那么不能修改这个code  后面产品又说改了code其他引用了code的地方也要同步变更...
                await this.inspectionCode(param.code, unitCostSummary.code, codeFormulaMap);
            }
            //判断name
            if (ObjectUtils.isNotEmpty(unitCostSummary.name) && param.name !== unitCostSummary.name && ObjectUtils.isNotEmpty(copyCode)) {
                // 如果修改了name  那么就要去看这个老的name是不有地方引用了他  如果有引用  那么不能修改这个name  后面产品又说改了name其他引用了name的地方也要同步变更...
                await this.inspectionName(param.name, unitCostSummary.name, codeInstructionsMap);
            }

            //判断 计算基数和费率修改
            if (param.calculateFormula !== unitCostSummary.calculateFormula || (param.rate !== unitCostSummary.rate && ObjectUtils.isNotEmpty(param.calculateFormula))) {
                // 如果参数传来的计算公式或者费率不一样  那么说明修改了计算公式或者费率   就需要对计算公式进行验证并计算结果
                let responseData = await this.handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap);
                if (!ObjectUtils.isEmpty(responseData)) {
                    return ResponseData.fail(responseData.message);
                }

                // param.code = code;  // 还原数据备份
                // param.calculateFormula = calculateFormula;  // 还原数据备份
                // 把本次计算的结果存入map  留待后续计算使用
                codePriceMap.set(param.code, NumberUtil.numberScale(param.price, je));
                codeFormulaMap.set(param.code, param.calculateFormula);
                codeInstructionsMap.set(param.code, param.instructions);
                // 如果公式进行了修改  那么需要对引用了这个条公式对应的code的所有公式重新计算，并且对扩散影响的所有公式都需要重新计算
                await this.handleCodePriceChange(param.code, codePriceMap, codeFormulaMap, codeRateMap);
            }
            // 根据上面这步的计算 得出有哪些数据需要更新
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let item = unitCostSummaryArray[i];
                let price = codePriceMap.get(item.code);
                let formula = codeFormulaMap.get(item.code);
                let instructions = codeInstructionsMap.get(item.code);

                let updateUnitCostSummary = new GsUnitCostSummary();
                updateUnitCostSummary.sequenceNbr = item.sequenceNbr;
                let flag = false;
                if (ObjectUtils.isNotEmpty(price) && price !== item.price) {
                    updateUnitCostSummary.price = price;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(formula) && formula !== item.calculateFormula) {
                    updateUnitCostSummary.calculateFormula = formula;
                    flag = true;
                } else {
                    updateUnitCostSummary.price = 0;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(instructions) && instructions !== item.instructions) {
                    updateUnitCostSummary.instructions = instructions;
                    flag = true;
                }
                if (flag === true) {
                    priceChangeArray.push(updateUnitCostSummary);
                }
            }

        } else {
            // 新增
            let find = unitCostSummaryArray.find(item => item.code === param.code);
            if (ObjectUtils.isNotEmpty(find)) {
                return ResponseData.fail('当前code已存在');
            }
            // 获取统一的map数据
            await this.getAllCodeFormulaPriceMap(constructId, unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, unitCostSummaryArray);
            // 处理公式校验和计算
            await this.handleUpdateCalculateFormula(param, codePriceMap, codeFormulaMap, codeNameMap);
        }

        // 针对不同工程专业，取费表数据更新
        let array = ['企业管理费', '利润', '税金', '规费', '安全文明施工费'];
        if (ObjectUtils.isNotEmpty(param.category) && array.includes(param.category)) {
            let freeRateModel = new FreeRateModel();
            freeRateModel.constructId = constructId;
            freeRateModel.unitId = unitId;
            freeRateModel.libraryCode = constructMajorType;
            let rate = ObjectUtils.isNotEmpty(param.rate) ? param.rate : 0;
            switch (param.category) {
                case "企业管理费":
                    freeRateModel.manageFeeRate = parseFloat(rate);
                    break;
                case "利润":
                    freeRateModel.profitRate = parseFloat(rate);
                    break;
                case "税金":
                    freeRateModel.taxRate = parseFloat(rate);
                    break;
                case "规费":
                    freeRateModel.gfRate = parseFloat(rate);
                    break;
                case "安全文明施工费":
                    freeRateModel.anwenRate = parseFloat(param.rate);
                    break;
                default:
                    break;
            }
            await this.service.PreliminaryEstimate.gsFreeRateService.updateUnitFree(freeRateModel);
        }

        // args.unitCostSummary.code = code;  // 还原数据备份
        // args.unitCostSummary.calculateFormula = calculateFormula; // 还原数据备份
        if (ObjectUtils.isNotEmpty(param.code) && param.code === '※') {
            args.unitCostSummary.code = copyCode;
            args.unitCostSummary.name = copyName;
        }
        // 进行数据更新
        await this.setUnitCostSummaryData(args, unitCostSummaryArray, priceChangeArray);
        //调用计算费用汇总
        await this.countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, constructMajorType);

        // 更新费用汇总
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostSummaryArray)
        return ResponseData.success(unitCostSummaryArray);
    }

    /**
     * 判断 target中是否包含 arr
     * @param arr
     * @param target
     * @returns {boolean}
     */
    stringInArray(arr, target) {
        for (let item of arr) {
            if (item === target) {  // item.includs(target)
                return true;
            }
        }
        return false;
    }

    /**
     * 修改code - 修改计算基数
     * @param newCode
     * @param oldCode
     * @param codeFormulaMap
     */
    async inspectionCode(newCode, oldCode, codeFormulaMap) {
        let lowerOldCode = oldCode.toLowerCase();
        for (let [key, value] of codeFormulaMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let codeList = value.toLowerCase().split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(codeList) && this.stringInArray(codeList, lowerOldCode)) {
                    let reg = new RegExp("\\b" + lowerOldCode + "\\b", "gi")
                    let replace = value.replaceAll(reg, newCode);
                    codeFormulaMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 修改name - 修改基数说明
     * @param newName
     * @param oldName
     * @param codeInstructionsMap
     */
    async inspectionName(newName, oldName, codeInstructionsMap) {
        for (let [key, value] of codeInstructionsMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let nameList = value.toLowerCase().split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(nameList) && this.stringInArray(nameList, oldName)) {
                    let replace = value.replaceAll(oldName, newName);
                    codeInstructionsMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 获取单位工程的所有已有的费用代码基数和对应的价格
     * @param constructId
     * @param unitCostCodePriceArray
     * @param currentId
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeInstructionsMap
     * @param codeRateMap
     * @param codeNameMap
     * @param unitCostSummaryArray
     */
    async getAllCodeFormulaPriceMap(constructId, unitCostCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, unitCostSummaryArray) {

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        if (ObjectUtils.isNotEmpty(unitCostCodePriceArray)) {
            for (let i = 0; i < unitCostCodePriceArray.length; i++) {
                let unitCostCodePrice = unitCostCodePriceArray[i];
                let code = unitCostCodePrice.code;
                codePriceMap.set(code, NumberUtil.numberScale(unitCostCodePrice.price, je));
                codeFormulaMap.set(code, unitCostCodePrice.code);
                codeInstructionsMap.set(code, unitCostCodePrice.name);
                codeNameMap.set(code, unitCostCodePrice.name);
            }
        }
        if (ObjectUtils.isNotEmpty(unitCostSummaryArray)) {
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                let code = unitCostSummary.code;
                if (!ObjectUtils.isEmpty(currentId) && currentId === unitCostSummary.sequenceNbr) {
                    continue;
                }
                codePriceMap.set(code, NumberUtil.numberScale(unitCostSummary.price, je));
                if (ObjectUtils.isNotEmpty(code)) {
                    codeFormulaMap.set(code, unitCostSummary.calculateFormula);
                    codeInstructionsMap.set(code, unitCostSummary.instructions);
                }
                codeNameMap.set(code, unitCostSummary.name);
                if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                    if (unitCostSummary.isUpdateRate === true) {
                        codeRateMap.set(code, NumberUtil.numberScale(unitCostSummary.rate, freeRate));
                    } else {
                        codeRateMap.set(code, unitCostSummary.rate);
                    }
                }
            }
        }
    }

    /**
     * 费用代码修改 - 计算基数修改
     * @param calculateFormula
     * @param codePriceMap
     * @returns {any}
     */
    async doCalculator(calculateFormula, codePriceMap) {
        if (ObjectUtils.isEmpty(calculateFormula)) {
            return calculateFormula;
        }
        // 分解字符串成表达式和变量名
        const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
        //存放替换后的计算公式
        let afterCalculateFormula = calculateFormula;

        // 创建一个新的 Map 来存储小写的键和对应的值
        const keysLowercase = new Map();
        // 使用 forEach 遍历原始的 Map
        codePriceMap.forEach((value, key) => {
            // 将键转换为小写
            const lowerCaseKey = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            // 将新的键值对添加到新的 Map 中
            keysLowercase.set(lowerCaseKey, value);
        });

        //替换费用代码和费用代号
        for (let variable of variablesToReplace) {
            let variableLowerCase = variable.toLowerCase(); // 转小写
            if (keysLowercase.has(variableLowerCase)) {
                afterCalculateFormula = afterCalculateFormula.replace(variable, keysLowercase.get(variableLowerCase));
            }
        }

        let flag = await this.isValidExpression(afterCalculateFormula);
        if (!flag) {
            throw new Error("表达式有误，请重新编辑！");
        }
        // 匹配表达式中的※为0，进行检验
        afterCalculateFormula = afterCalculateFormula.replaceAll("※", "0");
        return eval(afterCalculateFormula);
    }

    /**
     * 效验取费基数：A+B+V+
     * @param expression
     * @returns {boolean}
     */
    async isValidExpression(expression) {
        // 匹配表达式中的※为0，进行检验
        expression = expression.replaceAll("※", "0");
        // 匹配四则运算表达式的正则表达式
        const regex = /^[\d\+\-\*\/\(\)\.]+$/;

        // 检查表达式是否匹配正则表达式
        if (!regex.test(expression)) {
            return false;
        }

        try {
            // 使用 eval() 函数计算表达式的值
            eval(expression);
            return true;
        } catch (e) {
            // 如果表达式有语法错误，eval() 函数会抛出异常
            return false;
        }
    }

    /**
     * 计算基数修改 - 基数说明修改
     * @param formula
     * @param codeNameMap
     * @returns {string|*}
     */
    async getFormulaInstructions(formula, codeNameMap) {
        if (ObjectUtils.isEmpty(formula)) {
            return formula;
        }
        // codeNameMap的key转小写
        let codeNameMapLowerCase = new Map();
        for (let [key, value] of codeNameMap) {
            let keyNew = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            codeNameMapLowerCase.set(keyNew, value);
        }
        // 把公式进行分割
        formula = formula.toLowerCase();
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (!codeList.length) {
            return "";
        }
        for (let i = 0; i < codeList.length; i++) {
            let code = codeList[i];
            let instruction = codeNameMapLowerCase.get(code);
            if (ObjectUtils.isNotEmpty(instruction) && instruction.trim()) {
                formula = formula.replace(code, instruction);
            } else {
                if (isNaN(Number(code))) { // code不是一个数字
                    formula = formula.replace(code, "");   // formula.replace(code, "") todo 解决引用有费用代号无名称，导致的基数说明错误问题   formula.replace("+" + code, "");
                } else {
                    formula = formula.replace(code, code);
                }
            }
        }
        return formula;
    }

    /**
     * 处理公式修改逻辑
     * @param constructId
     * @param param
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeNameMap
     */
    async handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap) {

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        // 对本次新增或者修改进行正确性检测
        let set = new Set();
        // set.add(param.code);
        try {
            await this.doInspection(set, param, codeFormulaMap, param.code);
            // 如果没有抛出异常  说明检查通过了
            let res = await this.doCalculator(param.calculateFormula, codePriceMap);
            if (param.rate) {
                // res = parseFloat((res * param.rate / 100).toFixed(3));
                if (param.isUpdateRate === true) {
                    res = NumberUtil.numberScale(parseFloat(res * param.rate / 100), freeRate);
                } else {
                    res = parseFloat((res * param.rate / 100));
                }
            }
            param.price = Math.round(res * 100) / 100;
            param.instructions = await this.getFormulaInstructions(param.calculateFormula, codeNameMap)
        } catch (e) {
            return ResponseData.fail(e.message);
        }
    }

    /**
     * 获取所有引用code
     * @param param
     * @param calculateFormula
     * @param codeFormulaMap
     * @param set
     */
    async getCodes(param, calculateFormula, codeFormulaMap, set) {
        if (codeFormulaMap.has(calculateFormula) && ObjectUtils.isNotEmpty(codeFormulaMap.get(calculateFormula))) {
            // 获取公式calculateFormula中的code
            let codeList = codeFormulaMap.get(calculateFormula).replace(/[+\-*/]/g, ',').split(',');
            // let codeList = codeFormulaMap.get(calculateFormula).match(/[A-Za-z][A-Za-z0-9]*/g);
            if (ObjectUtils.isNotEmpty(codeList)) {
                if (codeList.length === 1 && codeList[0] === calculateFormula) {
                    return;
                }
                for (let i = 0; i < codeList.length; i++) {
                    let code = codeList[i];
                    if (code !== "※") {
                        set.add(code);
                    }
                    await this.getCodes(param, code, codeFormulaMap, set);
                }
            }
        } else {
            let codeList = calculateFormula.replace(/[+\-*/]/g, ',').split(',');
            for (let i = 0; i < codeList.length; i++) {
                let code = codeList[i];
                if (codeFormulaMap.has(code) && ObjectUtils.isNotEmpty(codeFormulaMap.get(code))) {
                    let calculateFormula2 = codeFormulaMap.get(code);
                    if (calculateFormula2.includes(param.code)) {
                        set.add(calculateFormula2);
                    }
                }
            }
        }
    }

    /**
     * 对公式的正确性进行检查 检查是否有错误引用或者循环引用
     * ps：
     * 这个有个简单的做法  就是从修改的公式code开始  把每一层的每一个元素都分解到一个二叉树里面
     * 如果这个二叉树的任何一条从顶层到底层的分支中出现重复的元素  那就说明这个公式存在循环引用   但是这样做的话错误提示不明确
     * @param codes
     * @param param
     * @param codeFormulaMap
     * @param code
     */
    async doInspection(codes, param, codeFormulaMap, code) {
        let formula = param.calculateFormula;

        let formulaLowerCase = ObjectUtils.isNotEmpty(formula) ? formula.toLowerCase() : formula;
        let codeLowerCase = ObjectUtils.isNotEmpty(code) ? code.toLowerCase() : code;

        if (ObjectUtils.isEmpty(formula)) {
            return;
        }
        if (formulaLowerCase === codeLowerCase) {
            throw new Error("公式存在循环引用，请检查并修改");
        }

        // 创建一个新的 Map 来存储小写的键和对应的值
        const codeFormulaMapLowercase = new Map();
        // 使用 forEach 遍历原始的 Map
        codeFormulaMap.forEach((value, key) => {
            // 将键转换为小写
            const lowerCaseKey = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            const lowerCaseValue = ObjectUtils.isNotEmpty(value) ? value.toLowerCase() : value;
            // 将新的键值对添加到新的 Map 中
            codeFormulaMapLowercase.set(lowerCaseKey, lowerCaseValue);
        });

        // 获取应用取费基数下所有的子code
        await this.getCodes(param, formulaLowerCase, codeFormulaMapLowercase, codes);
        if (codes.has(codeLowerCase)) {
            throw new Error("公式存在循环引用，请检查并修改");
        }
        // 根据 加减乘除 分割计算公式
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (codeList.length === 0) {
            throw new Error("运算公式格式错误，请检查并修改");
        }
        // if (codeList.length === 1) {
        //     if (codeList[0] === code) {
        //         throw new Error("公式存在循环引用，请检查并修改");
        //     }
        //     let codeFormula = codeFormulaMap.get(codeList[0].replace(/\s/g, ''));
        //     if (!codeFormula) {
        //         // 在map里面没找到  那就是引用了一个不存在的基数code
        //         //判断是否为数字
        //         if (isNaN(Number(codeList[0]))) {
        //             throw new Error("公式存在未知引用，请检查并修改");
        //         }
        //     }
        //     return;
        // }
        // for (let i = 0; i < codeList.length; i++) {
        //     const c = codeList[i];
        //     if (codes.has(c)) {
        //         // 说明是自己的公式引用了自己
        //         throw new Error('公式存在循环引用，请检查并修改');
        //     }
        //     let codeFormula = codeFormulaMap.get(c.replace(/\s/g, ''));
        //     if (!codeFormula) {
        //         if (ObjectUtils.isEmpty(codeFormula)) {  // E默认没有计算基数，会导致引用E的时候报错
        //             codeFormula = 0;
        //         } else {
        //             // 在map里面没找到  那就是引用了一个不存在的基数code
        //             if (isNaN(Number(c))) {
        //                 throw new Error("公式存在未知引用，请检查并修改");
        //             } else {
        //                 codeFormula = c;
        //             }
        //             let newCodes = new Set(codes);
        //             if (c !== codeFormula) {
        //                 newCodes.add(c);
        //             }
        //             this.doInspection(newCodes, codeFormula, codeFormulaMap, code);
        //         }
        //     }
        // }
    }

    /**
     * 计算费率
     * @param code
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeRateMap
     */
    async handleCodePriceChange(code, codePriceMap, codeFormulaMap, codeRateMap) {
        for (let [key, value] of codeFormulaMap.entries()) {
            if (ObjectUtils.isNotEmpty(value) && ObjectUtils.isNotEmpty(value) && isNaN(Number(value))) {
                // 对公式进行分解
                let codeList = value.split(/[\+\-\*\/\(\)]+/);
                if (codeList.length > 1) {
                    if (codeList.includes(code)) {
                        let res = await this.doCalculator(value, codePriceMap);
                        let rate = codeRateMap.get(key);
                        if (rate) {
                            res = parseFloat((res * rate / 100));
                            codePriceMap.set(key, Math.round(res * 100) / 100)
                        } else {
                            codePriceMap.set(key, res);
                        }
                        await this.handleCodePriceChange(key, codePriceMap, codeFormulaMap, codeRateMap);
                    }
                }
            }
        }
    }

    /**
     * 设置数据
     * @param args
     * @param unitCostSummaryArray
     * @param priceChangeArray
     */
    async setUnitCostSummaryData(args, unitCostSummaryArray, priceChangeArray) {
        //新增的数据
        let newUnitCostSummary = args.unitCostSummary;

        if (ObjectUtils.isEmpty(newUnitCostSummary.sequenceNbr)) {
            // // 新增
            // newUnitCostSummary.sequenceNbr = Snowflake.nextId();
            // for (let i = unitCostSummaryArray.length - 1; i >= 0; i--) {
            //     const item = unitCostSummaryArray[i];
            //     if (item.sortNum >= newUnitCostSummary.sortNum) {
            //         item.sortNum += 1;
            //     }
            // }
            unitCostSummaryArray.push(newUnitCostSummary);
        } else {
            //修改
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let element = unitCostSummaryArray[i];
                if (element.sequenceNbr === newUnitCostSummary.sequenceNbr) {
                    unitCostSummaryArray[i] = newUnitCostSummary;
                }
            }
        }
        //更新修改后的金额
        if (!ObjectUtils.isEmpty(priceChangeArray)) {
            for (let i = 0; i < priceChangeArray.length; i++) {
                let item = priceChangeArray[i];
                for (let j = 0; j < unitCostSummaryArray.length; j++) {
                    let element = unitCostSummaryArray[j];
                    if (element.sequenceNbr === item.sequenceNbr) {
                        if (!ObjectUtils.isEmpty(item.price)) {
                            unitCostSummaryArray[j].price = item.price;
                        }
                        if (!ObjectUtils.isEmpty(item.calculateFormula)) {
                            unitCostSummaryArray[j].calculateFormula = item.calculateFormula;
                        }
                        if (!ObjectUtils.isEmpty(item.instructions)) {
                            unitCostSummaryArray[j].instructions = item.instructions;
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取工程专业下拉框
     * @param args
     * @returns {(((searchElement: unknown, fromIndex?: number) => number) | {(...items: ConcatArray<unknown>): unknown[], (...items: unknown[]): unknown[]} | {(callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown): unknown, (callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown, initialValue: unknown): unknown, <U>(callbackfn: (previousValue: U, currentValue: unknown, currentIndex: number, array: unknown[]) => U, initialValue: U): U} | {<S extends unknown>(predicate: (value: unknown, index: number, array: unknown[]) => value is S, thisArg?: any): S[], (predicate: (value: unknown, index: number, array: unknown[]) => unknown, thisArg?: any): unknown[]} | (() => unknown) | ((predicate: (value: unknown, index: number, array: unknown[]) => unknown, thisArg?: any) => boolean) | number | ((...items: unknown[]) => number) | (() => unknown[]) | (() => string) | ((start?: number, end?: number) => unknown[]) | (<U>(callbackfn: (value: unknown, index: number, array: unknown[]) => U, thisArg?: any) => U[]) | {(callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown): unknown, (callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown, initialValue: unknown): unknown, <U>(callbackfn: (previousValue: U, currentValue: unknown, currentIndex: number, array: unknown[]) => U, initialValue: U): U} | {<S extends unknown>(predicate: (value: unknown, index: number, array: unknown[]) => value is S, thisArg?: any): this is S[], (predicate: (value: unknown, index: number, array: unknown[]) => unknown, thisArg?: any): boolean} | ((compareFn?: (a: unknown, b: unknown) => number) => ObjectConstructor) | ((separator?: string) => string) | ((callbackfn: (value: unknown, index: number, array: unknown[]) => void, thisArg?: any) => void) | {(start: number, deleteCount?: number): unknown[], (start: number, deleteCount: number, ...items: unknown[]): unknown[]})[] | any[]}
     */
    async getConstructMajorTypeEnum(args) {
        return Object.values(ConstructMajorTypeEnum);
    }

    /**
     * 获取费用汇总左侧树
     *
     * @param args
     * @returns {{}}
     */
    async getCostSummaryMajorMenuList(args) {
        const {constructId, singleId, unitId} = args;
        let constructMajorTypeList = [];
        // 获取费用汇总
        let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);

        // 当前单位的工程专业
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 获取取费专业
        let qfList = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryAll();
        let qfLibraryCodeList = qfList.map(item => item.libraryCode);

        let qfCodeNameMap = qfList.reduce((acc, item) => {
            acc.set(item.libraryCode, item.projectType);
            return acc;
        }, new Map());

        // 单专业汇总
        if (unitProject.isSingleMajorFlag) {  // ObjectUtils.isNotEmpty(unitProject.isSingleMajorFlag) &&
            // 遍历费用汇总的模板
            // unitCostSummaryMap.forEach((value, key) => {
            //     let majorObj = {};
            //     // 获取该单位的费用汇总key
            //     if (key.includes(unitId)) {
            //         if (key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ)) {
            //             majorObj[UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ] = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ_NAME;
            //             constructMajorTypeList.push(majorObj);
            //         }
            //     }
            //     // 获取该单位的费用汇总key
            //     if (key.includes(unitId)) {
            //         if (key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ)) {
            //             majorObj[UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ] = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ_NAME;
            //             constructMajorTypeList.push(majorObj);
            //         }
            //     }
            // });

            // 遍历费用汇总的模板
            unitCostSummaryMap.forEach((value, key) => {
                let majorObj = {};
                // 获取定额中所有的专业
                qfLibraryCodeList.forEach(qfCode => {
                    if (key.includes(unitId)) {
                        if (key.includes(qfCode)) {
                            majorObj[qfCode] = qfCodeNameMap.get(qfCode);
                            constructMajorTypeList.push(majorObj);
                        }
                    }
                })
            });

            // 多专业汇总
        } else {
            // 预算书下定额
            let budgetBooks = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId);
            let typeArray = ["03", "04", "06", "07", "08", "09"];
            let budgetBookDes = budgetBooks.filter(item => typeArray.includes(item.type));

            // 独立费
            let independentCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
                .get(this.service.PreliminaryEstimate.gsIndependentCostsService.getDataMapKey(unitId));

            // 遍历费用汇总的模板
            unitCostSummaryMap.forEach((value, key) => {
                let majorObj = {};
                // 获取该单位的费用汇总key
                if (key.includes(unitId)) {
                    // if (key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ)) {
                    //     let budgetBookTypes;
                    //     if (ObjectUtils.isNotEmpty(budgetBookDes)) {
                    //         // budgetBooks.children.shift();
                    //         // 获取预算书下的专业类型  todo:projectType  有的定额是空的  如：04
                    //         budgetBookTypes = budgetBookDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)
                    //             && budgetBook.costFileCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ).map(item => item.costFileCode);
                    //     }
                    //     let independentCostTypes;
                    //     if (ObjectUtils.isNotEmpty(independentCosts)) {
                    //         // independentCosts.shift();
                    //         // 获取独立费下的专业类型
                    //         independentCostTypes = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode)
                    //             && item.costMajorCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ).map(item => item.costMajorCode);
                    //     }
                    //     if (ObjectUtils.isNotEmpty(budgetBookTypes) || ObjectUtils.isNotEmpty(independentCostTypes)) {
                    //         majorObj[UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ] = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ_NAME;
                    //         constructMajorTypeList.push(majorObj);
                    //     }
                    // }
                    //
                    // if (key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ)) {
                    //     let budgetBookTypes;
                    //     if (ObjectUtils.isNotEmpty(budgetBookDes)) {
                    //         // budgetBooks.children.shift();
                    //         // 获取预算书下的专业类型  todo:projectType  有的定额是空的  如：04
                    //         budgetBookTypes = budgetBookDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)
                    //             && budgetBook.costFileCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ).map(item => item.costFileCode);
                    //     }
                    //     let independentCostTypes;
                    //     if (ObjectUtils.isNotEmpty(independentCosts)) {
                    //         // independentCosts.shift();
                    //         // 获取独立费下的专业类型
                    //         independentCostTypes = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode)
                    //             && item.costMajorCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ).map(item => item.costMajorCode);
                    //     }
                    //     if (ObjectUtils.isNotEmpty(budgetBookTypes) || ObjectUtils.isNotEmpty(independentCostTypes)) {
                    //         majorObj[UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ] = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ_NAME;
                    //         constructMajorTypeList.push(majorObj);
                    //     }
                    // }
                    // 获取定额中所有的专业
                    qfLibraryCodeList.forEach(qfCode => {
                        if (key.includes(qfCode)) {
                            let budgetBookTypes;
                            if (ObjectUtils.isNotEmpty(budgetBookDes)) {
                                // 获取预算书下的专业类型
                                budgetBookTypes = [...new Set(budgetBookDes.filter(item => ObjectUtils.isNotEmpty(item.costFileCode)).map(item => item.costFileCode))];
                            }
                            let independentCostTypes;
                            if (ObjectUtils.isNotEmpty(independentCosts)) {
                                // 获取独立费下的专业类型
                                independentCostTypes = [...new Set(independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode)).map(item => item.costMajorCode))];
                            }
                            if (ObjectUtils.isNotEmpty(budgetBookTypes) || ObjectUtils.isNotEmpty(independentCostTypes)) {
                                majorObj[qfCode] = qfCodeNameMap.get(qfCode);
                                constructMajorTypeList.push(majorObj);
                            }
                        }
                    });
                }
            });
        }
        if (ObjectUtils.isNotEmpty(constructMajorTypeList)) {
            let treeList = {};
            treeList.itemList = constructMajorTypeList;
            return treeList;
        }
    }

    /**
     * 多专业汇总-多专业汇总补充工程专业
     * @param args
     */
    async supplyCostSummaryMajors(args) {
        let {constructId, singleId, unitId, constructMajorType, isSingleMajorFlag} = args;

        // 当前单位的工程专业
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let qfMajorTypeMoneyMap = new Map();
        unitProject.isSingleMajorFlag = isSingleMajorFlag;
        unitProject.jzProjectCost = 0;    // 工程造价
        unitProject.azProjectCost = 0;    // 工程造价

        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        // isSingleMajorFlag = true 单专业
        if (isSingleMajorFlag) {
            // if (constructMajorType !== unitProject.constructMajorType) {
            // 修改单位，记录多专业或单专业汇总的标识
            unitProject.qfMajorType = constructMajorType;
            qfMajorTypeMoneyMap.set(constructMajorType, 0);
            unitProject.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
            ProjectDomain.getDomain(constructId).updateProject(unitProject);

            // 删除原来的费用代码
            let codeMap = businessMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            let newCodeMap = new Map();
            for (let [key, value] of codeMap.entries()) {
                if (!key.includes(unitId)) {
                    newCodeMap.set(key, value);
                }
            }
            businessMap.set(FunctionTypeConstants.UNIT_COST_CODE, newCodeMap);

            // 对专业类型进行费用代码，并对费用汇总填写数据
            let unitCostCodePrices = await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.defaultUnitCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                constructMajorType: constructMajorType
            });
            // 更新或添加费用代码
            businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostCodePrices);

            // 删除原来的费用汇总
            let codeSummaryMap = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
            let newCodeSummaryMap = new Map();
            for (let [key, value] of codeSummaryMap.entries()) {
                if (!key.includes(unitId) || (key.includes(constructMajorType) && key.includes(unitId))) {
                    newCodeSummaryMap.set(key, value);
                }
            }
            businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);

            // 获取费用汇总
            let unitCostSummarys = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);
            if(ObjectUtils.isEmpty(unitCostSummarys)){
                // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
                unitCostSummarys = await this.defaultUnitCostSummary({
                    constructId: constructId,
                    singleId: singleId,
                    unitId: unitId,
                    constructMajorType: constructMajorType
                });
            }
            // 更新或添加费用汇总
            businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostSummarys);

            // 对专业类型进行费用代码，并对费用汇总填写数据
            let unitCostCodePriceArray = await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                constructMajorType: constructMajorType
            });
            // 更新或添加费用代码
            businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostCodePriceArray);

            // 添加补充的专业的取费表记录
            await this.service.PreliminaryEstimate.gsFreeRateService.addUnitFreeRate(constructId, unitId, constructMajorType);

            // isSingleMajorFlag = false 多专业
        } else {

            // 对专业类型进行费用代码，并对费用汇总填写数据
            await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                constructMajorType: constructMajorType
            });

            // 同步更新取费文件
            await this.service.PreliminaryEstimate.gsFreeRateService.addUnitFreeRate(constructId, unitId, constructMajorType);

            // // 预算书下定额
            // let budgetBooks = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === unitId);
            // let typeArray = ["03", "04", "06", "07", "08", "09"];
            // let budgetBookDes = budgetBooks.filter(item => typeArray.includes(item.type));
            // let budgetBookTypes;
            // if (ObjectUtils.isNotEmpty(budgetBookDes)) {
            //     // 获取预算书下的专业类型
            //     budgetBookTypes = budgetBookDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode) && budgetBook.costFileCode === constructMajorType).map(item => item.costFileCode);
            // }
            //
            // // 独立费
            // let independentCosts = businessMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            //     .get(this.service.PreliminaryEstimate.gsIndependentCostsService.getDataMapKey(unitId));
            // let independentCostTypes;
            // if (ObjectUtils.isNotEmpty(independentCosts)) {
            //     // 获取独立费下的专业类型
            //     independentCostTypes = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.costMajorCode === constructMajorType).map(item => item.costMajorCode);
            // }
            // if (!ObjectUtils.isEmpty(budgetBookTypes) || !ObjectUtils.isEmpty(independentCostTypes)) {
            //     // 专业名称数组合并并去重: 如果预算书、独立费有一个有不同专业类型，就可点击弹窗，就有几个子节点
            //     let constructMajorTypeSet = new Set();
            //     constructMajorTypeSet.add(unitProject.constructMajorType);
            //
            //     if (ObjectUtils.isNotEmpty(budgetBookTypes)) {
            //         constructMajorTypeSet = new Set([...constructMajorTypeSet, ...budgetBookTypes]);
            //     }
            //     if (ObjectUtils.isNotEmpty(independentCostTypes)) {
            //         constructMajorTypeSet = new Set([...constructMajorTypeSet, ...independentCostTypes]);
            //     }
            //
            //     if (ObjectUtils.isNotEmpty(constructMajorTypeSet)) {
            //         if (constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
            //             constructMajorTypeSet.add(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ);
            //         }
            //         if (constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
            //             constructMajorTypeSet.add(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ);
            //         }
            //         if (ObjectUtils.isNotEmpty(constructMajorTypeSet)) {
            //             // 删除原来的费用代码
            //             let codeMap = businessMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            //             let newCodeMap = new Map();
            //             for (let [key, value] of codeMap.entries()) {
            //                 if (!key.includes(unitId)) {
            //                     newCodeMap.set(key, value);
            //                 }
            //             }
            //             // businessMap.set(FunctionTypeConstants.UNIT_COST_CODE, newCodeMap);
            //
            //             // 删除原来的费用汇总
            //             let codeSummaryMap = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
            //             let newCodeSummaryMap = new Map();
            //             for (let [key, value] of codeSummaryMap.entries()) {
            //                 if (!key.includes(unitId)) {
            //                     newCodeSummaryMap.set(key, value);
            //                 }
            //             }
            //             // businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);
            //
            //             // 方便费用代码计算时归类人材机数据
            //             constructMajorTypeSet.forEach(majorName => {
            //                 newCodeMap.set(unitId + FunctionTypeConstants.SEPARATOR + majorName, null);
            //                 newCodeSummaryMap.set(unitId + FunctionTypeConstants.SEPARATOR + majorName, null);
            //             });
            //             businessMap.set(FunctionTypeConstants.UNIT_COST_CODE, newCodeMap);
            //             businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);
            //
            //             // 修改单位，记录多专业或单专业汇总的标识
            //
            //             ProjectDomain.getDomain(constructId).updateProject(unitProject);
            //
            //             for (const majorName of constructMajorTypeSet) {
            //                 // if (majorName !== unitProject.constructMajorType) {
            //                 // 对专业类型进行费用代码，并对费用汇总填写数据
            //                 let unitCostCodePrices = await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.defaultUnitCostCodePrice({
            //                     constructId: constructId,
            //                     singleId: singleId,
            //                     unitId: unitId,
            //                     constructMajorType: majorName
            //                 });
            //                 // 更新或添加费用代码
            //                 businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePrices);
            //
            //                 // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
            //                 let unitCostSummarys = await this.defaultUnitCostSummary({
            //                     constructId: constructId,
            //                     singleId: singleId,
            //                     unitId: unitId,
            //                     constructMajorType: majorName
            //                 });
            //                 // 更新或添加费用汇总
            //                 businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostSummarys);
            //                 // }
            //
            //                 // 对专业类型进行费用代码，并对费用汇总填写数据
            //                 let unitCostCodePriceArray = await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
            //                     constructId: constructId,
            //                     singleId: singleId,
            //                     unitId: unitId,
            //                     constructMajorType: majorName
            //                 });
            //                 // 更新或添加费用代码
            //                 businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePriceArray);
            //             }
            //         }
            //     }
            // }
        }
        return ResponseData.success();
    }


    /**
     * 获取单位工程中各专业类型模板的各费用和
     * @param args
     */
    async getUnitCostSummary(args) {
        let {constructId, singleId, unitId} = args;
        // 获取默认map
        let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
        // // 获取当前单位
        // let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.COST_SUMMARY.je;

        // 创建一个新的Map对象来存储安装与建筑费用相加的值
        let unitCostSummaryPriceMap = new Map();
        // if (ObjectUtils.isNotEmpty(unitProject) && unitProject.isSingleMajorFlag === true) {
        //     unitCostSummaryPriceMap = unitCostSummaryMap.get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL).reduce((acc, item) => {
        //         acc.set(item.category, NumberUtil.numberScale(item.price, je));
        //         return acc;
        //     }, new Map());
        // } else {
        if (ObjectUtils.isNotEmpty(unitCostSummaryMap)) {
            unitCostSummaryMap.forEach((value, key) => {
                if (key.includes(unitId)) {
                    value.forEach(item => {
                        if (unitCostSummaryPriceMap.has(item.category)) {
                            unitCostSummaryPriceMap.set(item.category, NumberUtil.numberScale(NumberUtil.add(unitCostSummaryPriceMap.get(item.category), item.price, je)));
                        } else {
                            unitCostSummaryPriceMap.set(item.category, NumberUtil.numberScale(item.price, je));
                        }
                    });
                }
            })
        }
        // }
        return unitCostSummaryPriceMap;
    }


    /**
     * 取费表修改费率后，修改费用汇总费率，并计算费用汇总
     * @param constructId
     * @param singleId
     * @param unitId
     * @param unitFreeRate
     */
    async updateUnitCostSummaryRate(constructId, singleId, unitId, unitFreeRate) {

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let freeRate = precision.COST_SUMMARY.SUMMARY.freeRate;

        if (ObjectUtils.isNotEmpty(unitFreeRate)) {
            // 获取当前工程专业费用汇总
            let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
                .get(unitId + FunctionTypeConstants.SEPARATOR + unitFreeRate.libraryCode);
            // 遍历费用汇总更新费率
            for (let i = 0; i < unitCostSummarys.length; i++) {
                let unitCostSummary = unitCostSummarys[i];
                if (ObjectUtils.isNotEmpty(unitCostSummary.category)) {
                    switch (unitCostSummary.category) {
                        case "企业管理费":
                            unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.manageFeeRate) ? NumberUtil.numberScale(unitFreeRate.manageFeeRate, freeRate) : 0;
                            break;
                        case "利润":
                            unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.profitRate) ? NumberUtil.numberScale(unitFreeRate.profitRate, freeRate) : 0;
                            break;
                        case "税金":
                            unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.taxRate) ? NumberUtil.numberScale(unitFreeRate.taxRate, freeRate) : 0;
                            break;
                        case "规费":
                            unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.gfRate) ? NumberUtil.numberScale(unitFreeRate.gfRate, freeRate) : 0;
                            break;
                        case "安全文明施工费":
                            unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.anwenRate) ? NumberUtil.numberScale(unitFreeRate.anwenRate, freeRate) : 0;
                            break;
                        default:
                            break;
                    }
                    // switch (unitCostSummary.category) {
                    //     case "企业管理费":
                    //         if (unitFreeRate.manageFeeRateUpdate === true) {
                    //             unitCostSummary.isUpdateRate = true;
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.manageFeeRate) ? NumberUtil.numberScale(unitFreeRate.manageFeeRate, freeRate) : 0;
                    //         } else {
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.manageFeeRate) ? unitFreeRate.manageFeeRate : 0;
                    //         }
                    //         break;
                    //     case "利润":
                    //         if (unitFreeRate.profitRateUpdate === true) {
                    //             unitCostSummary.isUpdateRate = true;
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.profitRate) ? NumberUtil.numberScale(unitFreeRate.profitRate, freeRate) : 0;
                    //         } else {
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.profitRate) ? unitFreeRate.profitRate : 0;
                    //         }
                    //         break;
                    //     case "税金":
                    //         if (unitFreeRate.taxRateUpdate === true) {
                    //             unitCostSummary.isUpdateRate = true;
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.taxRate) ? NumberUtil.numberScale(unitFreeRate.taxRate, freeRate) : 0;
                    //         } else {
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.taxRate) ? unitFreeRate.taxRate : 0;
                    //         }
                    //         break;
                    //     case "规费":
                    //         unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.gfRate) ? unitFreeRate.gfRate : 0;
                    //         break;
                    //     case "安全文明施工费":
                    //         if (unitFreeRate.anwenRateUpdate === true) {
                    //             unitCostSummary.isUpdateRate = true;
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.anwenRate) ? NumberUtil.numberScale(unitFreeRate.anwenRate, freeRate) : 0;
                    //         } else {
                    //             unitCostSummary.isUpdateRate = false;
                    //             unitCostSummary.rate = ObjectUtils.isNotEmpty(unitFreeRate.anwenRate) ? unitFreeRate.anwenRate : 0;
                    //         }
                    //         break;
                    //     default:
                    //         break;
                    // }
                }
            }
            // 获取当前工程专业费用代码
            let unitCostCodePrices = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .get(unitId + FunctionTypeConstants.SEPARATOR + unitFreeRate.libraryCode);
            // 重新计算费用汇总
            let unitCostSummaryArray = await this.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummarys, unitFreeRate.libraryCode);
            // 更新费用汇总
            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
                .set(unitId + FunctionTypeConstants.SEPARATOR + unitFreeRate.libraryCode, unitCostSummaryArray);
        }
    }


    /**
     * 导入费用汇总
     * @param args
     */
    async importUnitCostSummary(args) {
        let {constructId, unitId, constructMajorType} = args;

        // 获取费用汇总模版存放路径  D:\IdeaProjects\gaiSuan\pricing-cs\build\extraResources\excelTemplate\gs\费用汇总
        const fyhzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\费用汇总';
        let options = {
            properties: ['openFile'],
            defaultPath: fyhzTemplatePath, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_FYHZ]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            return ResponseData.fail('未选中任何文件');
        }

        // 费用汇总的路径
        const fyhzFilePath = result[0];
        try {
            const data = fs.readFileSync(fyhzFilePath, 'utf8');
            // 使用JSON.parse()方法将JSON字符串转换为JavaScript数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段
            const unitCostSummaryArray = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.calculateFormula = line.calculateFormula;
                obj.instructions = line.instructions;
                obj.category = line.category;
                obj.rate = line.rate;
                obj.permission = line.permission;
                obj.remark = line.remark;
                obj.sequenceNbr = line.sequenceNbr;
                obj.whetherPrint = line.whetherPrint;
                // obj.parentId = line.parentId;
                unitCostSummaryArray.push(obj);
            });

            // 获取费用代码
            let unitCostCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

            //调用计算费用汇总
            let unitCostSummarys = await this.countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, constructMajorType);
            // 更新费用汇总
            // ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            //     .set(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType, unitCostSummarys);
            return ResponseData.success(unitCostSummarys);
        } catch (err) {
            return ResponseData.fail('导入失败');
        }
    }


    /**
     * 导出费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitCostSummary(args) {
        let {constructId, unitId, constructMajorType} = args;
        // 获取工程项目的费用汇总
        let unitCostSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        // 指定导出的列名
        const columns = ["dispNo", "code", "name", "calculateFormula", "instructions", "category", "rate", "permission", "remark", "whetherPrint", "sequenceNbr"];
        // 根据指定的列名来重组数据，确保导出的JSON只包含这些列
        const formattedData = unitCostSummarys.map(item => {
            return columns.reduce((acc, col) => {
                acc[col] = item[col];
                return acc;
            }, {});
        });

        // 将数组转换为JSON字符串   const jsonData = JSON.stringify(formattedData, null, 2);
        const jsonData = JSON.stringify(formattedData); // 第三个参数是缩进量，使输出更易读

        // 存放费用汇总文件的路径
        const fyhzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\费用汇总';
        let count = this.countDirectories(fyhzTemplatePath);
        let options = {
            title: '保存文件',
            defaultPath: fyhzTemplatePath + '\\费用汇总模板' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_FYHZ]} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(FileOperatorType.File_TYPE_FYHZ)) {
                filePath += FileOperatorType.File_TYPE_FYHZ;
            }
            // 写入文件
            fs.writeFile(filePath, jsonData, (err) => {
                if (err) {
                    ResponseData.fail('写入文件时发生错误');
                } else {
                    ResponseData.success('数据已成功导出到');
                }
            });
            return ResponseData.success(filePath);
        }
    }

    /**
     * 获取当前文件夹路径下文件个数
     * @param dirPath
     * @returns {number}
     */
    countDirectories(dirPath) {
        let count = 1;
        let numbers = [];
        fs.readdirSync(dirPath).forEach((item) => {
            if (item.match(/\d+/g) !== null) {
                numbers.push(item.match(/\d+/g)[0]);
            }
        });
        if (ObjectUtils.isNotEmpty(numbers)) {
            count = Math.max(...numbers) + 1;
        }
        return count;
    }


    /**
     * 获取费用汇总模板列表
     * @param args
     * @returns {{folder: string[], files: *[]}}
     */
    getCostSummaryTemplate(args) {
        // 获取费用汇总模版存放路径  D:\IdeaProjects\gaiSuan\pricing-cs\build\extraResources\excelTemplate\gs\费用汇总
        const defaultPath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\费用汇总';
        let {path} = args;
        let defaultStoragePath = path ? path : defaultPath;
        // let filesNames = [];
        // try {
        //     // 读取目录中的文件列表
        //     const files = fs.readdirSync(fyhzTemplatePath);
        //     // 去除每个文件名的后缀
        //     filesNames = files.map(file => {
        //         return path.parse(file).name;
        //     });
        // } catch (error) {
        //     console.error(`获取模板失败: ${error.message}`);
        // }

        let folder = [defaultStoragePath];
        if (!defaultStoragePath.includes("build")) {
            let files = getFiles(defaultStoragePath, itemInfo => {
                if (itemInfo.path.endsWith(FileOperatorType.File_TYPE_FYHZ)) return true;
                return false;
            });
            return {folder, files};
        }
        let files = getAllFiles(defaultStoragePath, itemInfo => {
            if (itemInfo.isDirectory) {
                folder.push(itemInfo.path);
                return true;
            }
            if (itemInfo.path.endsWith(FileOperatorType.File_TYPE_FYHZ)) return true;
            return false;
        });
        return {folder, files};
        // return {'folder': [fyhzTemplatePath], 'files': filesNames};
    }

    /**
     * 恢复默认模板路径
     * @param args
     * @returns {ResponseData}
     */
    async restoreDefaultTemplatePath(args) {
        return UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\费用汇总';
    }

    /**
     * 选择费用汇总模板
     * @returns {Promise<ResponseData>}
     */
    async selectCostSummaryTemplate(args) {
        let {constructId, unitId, constructMajorType, path} = args;

        // 获取费用汇总模版存放路径  D:\IdeaProjects\gaiSuan\pricing-cs\build\extraResources\excelTemplate\gs\费用汇总
        try {
            const data = fs.readFileSync(path, 'utf8');
            // 使用JSON.parse()方法将JSON字符串转换为JavaScript数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段
            const unitCostSummaryArray = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.calculateFormula = line.calculateFormula;
                obj.instructions = line.instructions;
                obj.category = line.category;
                obj.rate = line.rate;
                obj.permission = line.permission;
                obj.remark = line.remark;
                obj.sequenceNbr = line.sequenceNbr;
                obj.whetherPrint = line.whetherPrint;
                // obj.parentId = line.parentId;
                unitCostSummaryArray.push(obj);
            });

            // 获取费用代码
            let unitCostCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

            // 调用计算费用汇总
            let unitCostSummarys = await this.countUnitCostSummary(constructId, unitId, unitCostCodePriceArray, unitCostSummaryArray, constructMajorType);

            return ResponseData.success(unitCostSummarys);
        } catch (error) {
            console.error(`设置模板失败: ${error.message}`);
            return ResponseData.fail('设置模板失败');
        }
    }


    /**
     * 根据模板名称获取数据
     */
    getTemplateData(args) {
        let {constructId, unitId, path} = args;

        // 获取费用汇总模版存放路径  D:\IdeaProjects\gaiSuan\pricing-cs\build\extraResources\excelTemplate\gs\费用汇总
        try {
            // 同步读取文件内容
            const data = fs.readFileSync(path, 'utf8');
            // 将 JSON 字符串解析为 JavaScript 对象或数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段
            const unitCostSummaryArray = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.calculateFormula = line.calculateFormula;
                obj.instructions = line.instructions;
                obj.category = line.category;
                obj.rate = line.rate;
                obj.permission = line.permission;
                obj.remark = line.remark;
                obj.sequenceNbr = line.sequenceNbr;
                obj.whetherPrint = line.whetherPrint;
                // obj.parentId = line.parentId;
                unitCostSummaryArray.push(obj);
            });
            // 返回解析后的数组
            return unitCostSummaryArray;
        } catch (error) {
            console.error(`读取文件失败: ${error.message}`);
            return [];
        }
    }


    /**
     * 获取费用汇总应用范围
     * @param args
     * @returns {*}
     */
    async scopeOfApplicationsCostSummary(args) {
        let {constructId} = args;
        // 获取该工程项目
        let constructProjectTree = {};
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        if (ObjectUtils.isNotEmpty(constructProject)) {
            constructProjectTree.sequenceNbr = constructProject.sequenceNbr;
            constructProjectTree.name = constructProject.name;
            constructProjectTree.type = constructProject.type;
            // 添加是否被勾选标识
            constructProjectTree["ifCheck"] = false;
            constructProjectTree.parentId = constructProject.parentId;

            // 获取取费专业
            let qfList = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryAll();
            let qfLibraryCodeList = qfList.map(item => item.libraryCode);

            let qfCodeNameMap = qfList.reduce((acc, item) => {
                acc.set(item.libraryCode, item.projectType);
                return acc;
            }, new Map());

            // 添加节点属性
            await this.addPropertiesToTree(constructProject, constructProject.sequenceNbr, constructProjectTree, constructId, qfLibraryCodeList, qfCodeNameMap);
        }
        let constructProjectList = xeUtils.toTreeArray(new Array(constructProjectTree));
        constructProjectList.forEach(item => {
            item.children = [];
        });
        return constructProjectList;
    }


    /**
     * 添加节点属性
     * @param constructProject
     * @param parentId
     * @param constructProjectTree
     * @param constructId
     * @param qfLibraryCodeList
     * @param qfCodeNameMap
     */
    async addPropertiesToTree(constructProject, parentId, constructProjectTree, constructId, qfLibraryCodeList, qfCodeNameMap) {
        let singleProjectArray = [];
        if (ObjectUtils.isNotEmpty(constructProject.children)) {
            for (const node of constructProject.children) {
                let child = {};
                child.sequenceNbr = node.sequenceNbr;
                child.name = node.name;
                child.parentId = parentId;
                child.type = node.type;
                if (node.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    child.constructMajorType = node.constructMajorType;
                    let costSummaryMajorMenu = await this.getCostSummaryMajorMenuList({
                        constructId: constructId,
                        singleId: null,
                        unitId: node.sequenceNbr,
                    });
                    if (ObjectUtils.isNotEmpty(costSummaryMajorMenu)) {
                        let itemList = costSummaryMajorMenu.itemList;
                        if (ObjectUtils.isNotEmpty(itemList)) {
                            itemList.forEach(item => {
                                item.sequenceNbr = Snowflake.nextId();
                                item.ifCheck = false;
                                item.parentId = node.sequenceNbr;
                                item.type = ProjectTypeConstants.PROJECT_TYPE_UNIT_MAJOR;

                                qfLibraryCodeList.forEach(qfCode => {
                                    if (ObjectUtils.isNotEmpty(item[qfCode]) && item[qfCode] === qfCodeNameMap.get(qfCode)) {
                                        item.constructMajorType = qfCode;
                                        item.name = qfCodeNameMap.get(qfCode);
                                    }
                                });
                            });
                            child.children = itemList;
                        }
                    }
                }
                // 添加是否被勾选标识
                child["ifCheck"] = false;
                singleProjectArray.push(child);
                constructProjectTree.children = singleProjectArray;
                if (node) {
                    await this.addPropertiesToTree(node, node.sequenceNbr, child, constructId, qfLibraryCodeList, qfCodeNameMap);
                }
            }
        }
    }


    /**
     * 批量应用费用汇总
     * @param args
     */
    async batchApplicationsCostSummary(args) {
        let {constructProjectList, constructId, unitId, constructMajorType, name} = args;
        if (ObjectUtils.isEmpty(constructProjectList)) {
            return ResponseData.fail('请选择应用范围');
        }
        // 遍历工程单位的树，获取被勾选的单位下的工程专业List
        let constructMajorList = constructProjectList.filter(item => {
            return (item.ifCheck === true && item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT_MAJOR)
                || (item.ifCheck === true && item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && ObjectUtils.isEmpty(item.children))
        });

        // 获取费用汇总
        let costSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);

        // 通过当前指定的单位、工程专业，获取工程专业模板
        let unitCostSummary = costSummaryArray.get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);

        // 获取费用代码
        let costCodeCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
        // 遍历所有单位的费用汇总模板，补充专业后的所有模板
        if (ObjectUtils.isNotEmpty(constructMajorList)) {
            for (let i = 0; i < constructMajorList.length; i++) {
                let unitMajorTypeProject = constructMajorList[i];
                let unitId;
                if (unitMajorTypeProject.type === ProjectTypeConstants.PROJECT_TYPE_UNIT_MAJOR) {
                    console.log("constructMajorType----。" + unitMajorTypeProject.constructMajorType + "unitId--->" + unitMajorTypeProject.parentId);
                    unitId = unitMajorTypeProject.parentId;
                }
                if (unitMajorTypeProject.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    unitId = unitMajorTypeProject.sequenceNbr;
                }

                // 获取费用代码
                let unitCostCodePrices = costCodeCodePriceArray.get(unitId + FunctionTypeConstants.SEPARATOR + unitMajorTypeProject.constructMajorType);

                // 复制，防止修改原值
                let unitCostSummaryCopy = ConvertUtil.deepCopy(unitCostSummary);
                unitCostSummaryCopy.forEach(item => {
                    item.price = 0.00;
                });
                // 重新计算费用汇总
                await this.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummaryCopy, unitMajorTypeProject.constructMajorType);
            }
        }
        return ResponseData.success();
    }


    /**
     * 获取默认费用汇总合计
     * @param args
     * @returns {*}
     */
    defaultCostSummaryMajorsTotal(args) {
        let {constructId, unitId} = args;
        // 复制，防止修改原值
        let gsFyhzTotalCopy = ConvertUtil.deepCopy(gsFyhzTotal);
        for (let i in gsFyhzTotalCopy) {
            let costSummary = gsFyhzTotalCopy[i];
            costSummary.sequenceNbr = Snowflake.nextId();
        }
        return gsFyhzTotalCopy;
    }


    /**
     * 多专业汇总-多专业汇总后的总金额合计
     * @param args
     * @returns {null}
     */
    // async getCostSummaryMajorsTotal2(args) {
    //     let {constructId, unitId} = args;
    //     // 获取费用汇总
    //     let unitCostSummaryMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
    //
    //     let constructMajorType;
    //     // 判断unitCostSummaryMap中key包含unitId的个数
    //     let count = 0;  // 获取unitCostSummaryMap
    //     Array.from(unitCostSummaryMap.keys()).forEach(key => {
    //         if (key.includes(unitId)) {
    //             count++;
    //         }
    //         if (key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ)) {
    //             constructMajorType = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ;
    //         }
    //         if (key.includes(UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ)) {
    //             constructMajorType = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ;
    //         }
    //     });
    //
    //     // 获取费用汇总合计
    //     let unitCostCodeSummaryTotals = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL).get(unitId);
    //     // 获取当前单位
    //     let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
    //
    //     let costSummaryTotal = [];
    //     let dispNo = 1;
    //     // 未补充专业，不显示
    //     if (count < 2) {
    //         for (let i in unitCostCodeSummaryTotals) {
    //             let costSummary = unitCostCodeSummaryTotals[i];
    //             if (constructMajorType === costSummary.code && costSummary.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
    //                 costSummary.dispNo = dispNo++;
    //                 costSummary.price = ObjectUtils.isNotEmpty(unitProject.jzProjectCost) ? unitProject.jzProjectCost : 0;    // 工程造价
    //                 costSummaryTotal.push(costSummary);
    //             }
    //             if (constructMajorType === costSummary.code && costSummary.code === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
    //                 costSummary.dispNo = dispNo++;
    //                 costSummary.price = ObjectUtils.isNotEmpty(unitProject.azProjectCost) ? unitProject.azProjectCost : 0;    // 工程造价
    //                 costSummaryTotal.push(costSummary);
    //             }
    //             if (costSummary.name === '工程造价') {
    //                 costSummary.dispNo = dispNo++;
    //                 costSummary.price = ObjectUtils.isNotEmpty(unitProject.projectCost) ? unitProject.projectCost : 0;
    //                 costSummaryTotal.push(costSummary);
    //             }
    //         }
    //     } else {
    //         for (let i in unitCostCodeSummaryTotals) {
    //             let costSummary = unitCostCodeSummaryTotals[i];
    //             if (costSummary.name === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ_NAME) {
    //                 costSummary.dispNo = dispNo++;
    //                 costSummary.price = ObjectUtils.isNotEmpty(unitProject.jzProjectCost) ? unitProject.jzProjectCost : 0;    // 工程造价
    //                 costSummaryTotal.push(costSummary);
    //             }
    //             if (costSummary.name === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ_NAME) {
    //                 costSummary.dispNo = dispNo++;
    //                 costSummary.price = ObjectUtils.isNotEmpty(unitProject.azProjectCost) ? unitProject.azProjectCost : 0;    // 工程造价
    //                 costSummaryTotal.push(costSummary);
    //             }
    //             if (costSummary.name === '工程造价') {
    //                 costSummary.dispNo = dispNo++;
    //                 costSummary.price = ObjectUtils.isNotEmpty(unitProject.projectCost) ? unitProject.projectCost : 0;
    //                 costSummaryTotal.push(costSummary);
    //             }
    //         }
    //     }
    //     return costSummaryTotal;
    // }


    async getCostSummaryMajorsTotal(args) {
        let {constructId, unitId} = args;

        // 获取费用汇总合计
        let unitCostCodeSummaryTotals = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL).get(unitId);
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 获取该单位下的存放map<取费专业，取费费用汇总工程造价>
        let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));

        let costSummaryTotals = [];
        let dispNo = 1;
        // 未补充专业，不显示
        for (let i in unitCostCodeSummaryTotals) {
            let qfModel = unitCostCodeSummaryTotals[i];
            // 获取定额中所有的专业
            if (Array.from(qfMajorTypeMoneyMap.keys()).includes(qfModel.code)) {
                let costSummary = new GsUnitCostSummaryTotal();
                costSummary.dispNo = dispNo++;
                costSummary.code = qfModel.code;
                costSummary.name = qfModel.name;
                costSummary.price = qfMajorTypeMoneyMap.get(qfModel.code);
                costSummaryTotals.push(costSummary);
            }
        }
        let costSummaryTotal = new GsUnitCostSummaryTotal();
        costSummaryTotal.dispNo = costSummaryTotals.length + 1;
        costSummaryTotal.name = "工程造价";
        costSummaryTotal.price = unitProject.projectCost;
        // costSummaryTotal.price = costSummaryTotals.reduce((sum, obj) => {
        //     return sum + (obj.price || 0); // 如果 obj.age 为 null 或 undefined，则视为 0
        // }, 0);
        costSummaryTotals.push(costSummaryTotal);
        return costSummaryTotals;
    }

    /**
     * 多专业汇总-多专业汇总后的总金额合计-修改
     * @param args
     * @returns {ResponseData}
     */
    async updateCostSummaryMajorsTotal(args) {
        let {constructId, unitId, costSummaryTotal} = args;

        // 获取费用汇总合计
        let unitCostCodeSummaryTotals = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL).get(unitId);

        if (ObjectUtils.isNotEmpty(unitCostCodeSummaryTotals)) {
            unitCostCodeSummaryTotals.forEach(item => {
                if (item.sequenceNbr === costSummaryTotal.sequenceNbr) {
                    item.remark = costSummaryTotal.remark;
                }
            })
        }
        // 更新费用汇总合计
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL).set(unitId, unitCostCodeSummaryTotals);
        return ResponseData.success(unitCostCodeSummaryTotals);
    }

    /**
     * 获取是否进行多专业汇总
     * @param args
     * @returns {Promise<*>}
     */
    async getIsSingleMajorFlag(args) {
        let {constructId, singleId, unitId, isPartFlag} = args;
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        return unitProject.isSingleMajorFlag;
    }


    /**
     * 设置费用汇总或局部汇总，是否是多专业标识
     * @param args
     * @returns {Promise<*>}
     */
    async setIsSingleMajorFlag(args) {
        let {constructId, singleId, unitId, isPartFlag, isSingleMajorFlag, qfMajorType} = args;
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 判断是否是局部汇总
        if (isPartFlag) {
            unitProject.isPartSingleMajorFlag = isSingleMajorFlag;
            if(isSingleMajorFlag){
                unitProject.qfPartMajorType = qfMajorType;
            }
        } else {
            unitProject.isSingleMajorFlag = isSingleMajorFlag;
            unitProject.qfMajorType = qfMajorType;
        }
        // 修改单位的取费专业
        return ProjectDomain.getDomain(constructId).updateProject(unitProject);
    }

    /**
     * 获取当前单位的取费专业
     * @param args
     * @returns {Promise<*>}
     */
    async getQfMajorTypeByUnit(args) {
        let {constructId, singleId, unitId, isPartFlag} = args;
        // 获取当前单位
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        return unitProject.qfMajorType;
    }


}

GsUnitCostSummaryService.toString = () => '[class GsUnitCostSummaryService]';
module.exports = GsUnitCostSummaryService;