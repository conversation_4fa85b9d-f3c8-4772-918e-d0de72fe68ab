const {ObjectUtils} = require("../../utils/ObjectUtils");

const JSONMathHandler = require("./JSONMathHandler");

/**
 * 处理定额相关规则：根据定额工程量计算消耗量
 */
class JSONDEMathHandler extends JSONMathHandler{
    setParseMath(){
        // let deQuantity = this.ctx.deLine.quantity;
        let deUnit = this.ctx.deLine.unit;
        let deUnitNum = this._getUnitNumber(deUnit);
        // let realQuantity = deQuantity * deUnitNum;
        let realQuantity =  deUnitNum;

        let id = this.mathJson.base.id;

        let qtyMath = this.mathJson.base.math;
        let operator = this.mathOperator(qtyMath);
        if(ObjectUtils.isNotEmpty(operator)){
            qtyMath = this.conversionService.mathAfterCalculation(`${realQuantity}${qtyMath}`, this.conversionInfoDigits);
        }
        // let reg = new RegExp(`\\b${id}\\b`);
        // this.parseMath = this.mathJson.math.replace(reg, qtyMath);

        this.parseMath = this.mathReplaceAllDeal(this.mathJson.math, id, qtyMath);
    }

    _getUnitNumber(deUnit){
        const match = deUnit.match(/^[.0-9]+/);
        return match ? parseFloat(match[0]) : 1;
    }
}

module.exports = JSONDEMathHandler;