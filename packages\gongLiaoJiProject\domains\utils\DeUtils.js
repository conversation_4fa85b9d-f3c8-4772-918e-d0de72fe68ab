const _ = require("lodash");
const DeTypeConstants = require('../../constants/DeTypeConstants');
const CostDeMatchConstants = require('../../constants/CostDeMatchConstants');
const {Snowflake} = require("../../utils/Snowflake");
const {ObjectUtils} = require("../../utils/ObjectUtils")
const PropertyUtil = require("./PropertyUtil");
const ResourceKindConstants = require("../../constants/ResourceKindConstants");
const UnitUtils = require('../../core/tools/UnitUtils');

class DeUtils {

    static findAllQdParendIds(deRowId, ctx ,deRange){
      let qdIds = [];
      let qd = deRange=='all'?ctx.allDeMap.getNodeById(deRowId):ctx.deMap.getNodeById(deRowId);
      if(qd.type === DeTypeConstants.DE_TYPE_DELIST){
        qdIds.push(deRowId);
        qdIds = qdIds.concat(this.findAllQdParendIds(qd.parentId,ctx,deRange));
      }
      return qdIds;
    }

    /**
     * 构建新的定额树
     * @param {*} sourceDes  不要该里面的值
     * @param {*} targetDeMap 
     * @param {*} targetConstructId 
     * @param {*} targetUnitId 
     * @param {*} maxAttempts 
     * @returns 
     */
    static buildTreeWithParents( sourceDes, targetDeMap, targetConstructId,targetUnitId,maxAttempts = sourceDes.length * 2) {
        const createdNodes = new Map(); // 存储已创建的节点
        const checkedNodes = new Map(); // 存储已检查的节点
        const queue = []; // 临时队列，用于处理待创建的节点
        // 将所有对象加入队列
        for (let de of sourceDes) {
            
            let newDe = _.cloneDeep(de);
            newDe.constructId = targetConstructId;
            newDe.unitId = targetUnitId;
            newDe.children = [];// 设置为空，防止double
            newDe.importSequenceNbr = newDe.sequenceNbr;
            newDe.sequenceNbr = Snowflake.nextId();
            newDe.deRowId = newDe.sequenceNbr;
            queue.push(newDe);
        }

        let attemptCount = 0;

        while (queue.length > 0 && attemptCount < maxAttempts) {
            let currentNode = queue.shift();//不要更改其属性

            attemptCount++;

            if (checkedNodes.has(currentNode.sequenceNbr)) {
                console.error(`Detected infinite loop or data inconsistency: Node with id ${currentNode.id} is already checked.`);
                break;
            }

            if (createdNodes.has(currentNode.importSequenceNbr)) {
                continue; // 跳过已创建的节点
            }

            if (currentNode.parentId === null || currentNode.parentId === undefined || currentNode.parentId === 0)
            {
                let root = targetDeMap.find(item=>item.unitId === targetUnitId && item.type === DeTypeConstants.DE_TYPE_DEFAULT);
                PropertyUtil.copyProperties(currentNode,root,["sequenceNbr","deRowId","updateDate"]); //父级处理
                currentNode = root;
            } else {
                let parentNode = createdNodes.get(currentNode.parentId);
                if(!parentNode){
                    parentNode = targetDeMap.getAllNodes().filter(item=>item.importSequenceNbr === currentNode.parentId)
                }
                if (parentNode) {
                    currentNode.parentId = parentNode.sequenceNbr;//重置父级的id
                    targetDeMap.addNode(currentNode, parentNode);
                } else if (queue.includes(currentNode.parentId)) {
                    console.error(`Data inconsistency: Parent node with id ${currentNode.parentId} not found in the queue.`);
                    break;
                } else {
                    queue.push(currentNode); // 父节点未创建，放回队列
                    continue;
                }
            }

            createdNodes.set(currentNode.importSequenceNbr, currentNode);
            checkedNodes.set(currentNode.importSequenceNbr,currentNode.sequenceNbr);
        }

        if (attemptCount >= maxAttempts) {
            console.error('Maximum number of attempts reached, possible data inconsistency or infinite loop.');
        }

        return checkedNodes;
    }

    static needSetCostDeQuantityEq1(baseDe){
      if([CostDeMatchConstants.ZJCS_DE,CostDeMatchConstants.AZ_DE,CostDeMatchConstants.FXTJ_CG
        ,CostDeMatchConstants.FXTJ_CZYS,CostDeMatchConstants.FXTJ_GCSDF,CostDeMatchConstants.FXTJ_ZXXJX,CostDeMatchConstants.GJMQ_DE_CG
        ,CostDeMatchConstants.GJMQ_DE_CZYS,CostDeMatchConstants.GJMQ_DE_ZXXJX,CostDeMatchConstants.FGJZ_DE_ZXXJX].includes(baseDe.isCostDe)){
      
        return true;
      }
      return false;
    }

    static setCostDeQuantityEq1(baseDe){
      if(ObjectUtils.isNotEmpty(baseDe.quantity) && (baseDe.quantity > 0 || baseDe.quantity < 0)){
        return;
      }
      if(this.needSetCostDeQuantityEq1(baseDe)){
        baseDe.quantity = 1;
        baseDe.originalQuantity = 1;
        baseDe.quantityExpression = 1;
      }
    }
    //用于判断定额是否满足该编码
    static reduceCostDe(deRow,priceCodes){

      if(ObjectUtils.isEmpty(priceCodes)){
        return [];
      }
      
      priceCodes = priceCodes.filter(element => {
        
        if(element.code == CostDeMatchConstants.DSZSGR){
          return deRow.isCostDe == CostDeMatchConstants.DS_CY_DE;
        }
        if(element.code == CostDeMatchConstants.DXZSGR){
          return deRow.isCostDe == CostDeMatchConstants.DX_CY_DE;
        }
        if(element.code == CostDeMatchConstants.CGRGHJ){
          return deRow.isCostDe == CostDeMatchConstants.CG_DE;
        }
        return true;
      });
      return priceCodes;
    }

    static isCostDeByBaseDe(baseDe) {
        let { deCode, value, zjcsClassCode } = baseDe;
    
        if (ObjectUtils.isEmpty(value) && ObjectUtils.isEmpty(zjcsClassCode)) {
          return CostDeMatchConstants.NON_COST_DE;
        }
        // 这个value是基础定额库定额的value 【110是垂运定额、120是超高定额、210是安装的超高费、220是安装的系统调试费、230是安装的垂直运输费、240是安装的脚手架搭拆费、250是安装的操作高度增加费】
        // 所以这个value只能判断是不是垂运、超高、安装  并不能区分出是不是【总价措施】或者【安文费】
        // 同时我们的垂运分为了【地上】和【地下】 value=110只能说明是垂运  当value==110的时候  deCode是【B8-1、B8-3、B8-2、B8-4】中的时，为地下，否则为地上
        const azCostCode = [210, 220, 230, 240, 250];
        if (ObjectUtils.isNotEmpty(value)) {
          if (azCostCode.includes(value)) {
            // 安装
            return CostDeMatchConstants.AZ_DE;
          }
          if (value == 120) {
            // 超高
            return CostDeMatchConstants.CG_DE;
          }
          if (value == CostDeMatchConstants.CG) {
            // 房修土建-超高
            return CostDeMatchConstants.FXTJ_CG;
          }
          if (value == CostDeMatchConstants.CZYS) {
            // 房修土建-垂直运输费
            return CostDeMatchConstants.FXTJ_CZYS;
          }
          if (value == CostDeMatchConstants.ZXXJX) {
            // 房修土建-中小型机械使用费
            return CostDeMatchConstants.FXTJ_ZXXJX;
          }
          if (value == CostDeMatchConstants.GCSDF) {
            // 房修土建-工程水电费
            return CostDeMatchConstants.FXTJ_GCSDF;
          }
          //
          if (value == CostDeMatchConstants.GJMQ_CG) {
            // 古建明清-超高
            return CostDeMatchConstants.GJMQ_DE_CG;
          }
          if (value == CostDeMatchConstants.GJMQ_CZYS) {
            // 房修土建-垂直运输费
            return CostDeMatchConstants.GJMQ_DE_CZYS;
          }
          if (value == CostDeMatchConstants.GJMQ_ZXXJX) {
            // 房修土建-中小型机械使用费
            return CostDeMatchConstants.GJMQ_DE_ZXXJX;
          }
          if (value == CostDeMatchConstants.FGJZ_ZXXJX) {
            // 房修土建-工程水电费
            return CostDeMatchConstants.FGJZ_DE_ZXXJX;
          }
          if (value == 110) {
            // 垂运
            const DS_CY = ['8-1', '8-3', '8-2', '8-4'];
            if (DS_CY.includes(deCode)) {
              return CostDeMatchConstants.DX_CY_DE;
            } else {
              return CostDeMatchConstants.DS_CY_DE;
            }
          }
        }
        if (ObjectUtils.isNotEmpty(zjcsClassCode)) {
          if (zjcsClassCode == '0') {
            // 安文费
            return CostDeMatchConstants.AWF_DE;
          } else {
            // 其他总价措施
            return CostDeMatchConstants.ZJCS_DE;
          }
        }
        return CostDeMatchConstants.NON_COST_DE;
    }

    static getPrecisionByRcj(precisionSetting){
      
      return Object.assign(precisionSetting.DETAIL.RCJ,precisionSetting.DETAIL.PTRCJZS);
    }

    /**
     * 获取定额的工程量保留小数位数值
     * @param {*} precision 
     * @param {*} deRow 
     * @returns 
     */
    static getQuantiyPrecision(precision,deRow){

        let unitPrecision = precision.EDIT.DE.quantity;
        if(precision.EDIT.DE.isUnitQuantity == true && ObjectUtils.isNotEmpty(deRow.unit)){
            let  unitNbr = UnitUtils.removeCharter(deRow.unit);
            let realUnit = deRow.unit.replace(unitNbr,'');
            let tepmUnitPrecision = precision.EDIT.DE.unitQuantity[realUnit];
            if(ObjectUtils.isNotEmpty(tepmUnitPrecision)){
                unitPrecision = tepmUnitPrecision;
            }
        }
        if(ObjectUtils.isEmpty(unitPrecision)){
            unitPrecision = 5;
        }
        return unitPrecision;
    }

    /**
     * 获取各类型精度设置对象
     * @param {*} precisionSetting 
     * @param {*} de 
     * @returns 
     */
    static getPrecisionByDe(precisionSetting,de){
      if([DeTypeConstants.DE_TYPE_RESOURCE,DeTypeConstants.DE_TYPE_USER_RESOURCE].includes(de.type)){
        if(de.deResourceKind == ResourceKindConstants.INT_TYPE_SB || de.deResourceKind == ResourceKindConstants.INT_TYPE_ZC){
          return precisionSetting.EDIT.DEZS;
        }
        return precisionSetting.EDIT.DERCJ;
      }
      if(de.type === DeTypeConstants.SUB_DE_TYPE_DE){
        return precisionSetting.EDIT.DEXZS;
      }
      return  precisionSetting.EDIT.DE;
    }

    static convertDigitalKey(key){
      if(key === 'baseJournalPrice'){
        key='price';
      }
      if(key === 'baseJournalTotalNumber'){
        key='totalNumber';
      }
      if(key === 'RDSum'){
        key='RSum';
      }
      if(key === 'rdTotalSum'){
        key='rTotalSum';
      }
      if(key === 'CDSum'){
        key='CSum';
      }
      if(key === 'cdTotalSum'){
        key='cTotalSum';
      }
      if(key === 'JDSum'){
        key='JSum';
      }
      if(key === 'jdTotalSum'){
        key='jTotalSum';
      }
      if(key === 'SDSum'){
        key='SSum';
      }
      if(key === 'sdTotalSum'){
        key='sTotalSum';
      }
      if(key === 'ZDSum'){
        key='ZSum';
      }
      if(key === 'zdTotalSum'){
        key='zTotalSum';
      }
      return key;
    }
}
DeUtils.toString = () => 'DeUtils';
module.exports = DeUtils;