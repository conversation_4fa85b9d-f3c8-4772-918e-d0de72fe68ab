const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const {  rcjBaseFn, RJCRules, PBRules} = require('./ResourceCodes');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const WildcardMap = require('../../../core/container/WildcardMap');
const LogUtil = require("../../../core/tools/logUtil");
const {NumberUtil} = require("../../../utils/NumberUtil");
const CommonConstants = require('../../../constants/CommonConstants');
const ZSFeeConstants = require("../../../constants/ZSFeeConstants");
const EE = require('../../../../../core/ee');
const DeUtils = require('../../utils/DeUtils');



class ResourceCalculator extends CalculateEngine{
  static SPLITOR = "_";
  constructId;
  deRowId;
  currentDe;
  unitId;
  resourceId;
  ctx;
  precision;
  rcjContext = [];
  digitPropertyMap = new  Map();
  static rcjMap = [
    "total",
    "totalNumber",//数量
    "dePrice",
    "marketPrice",
    "scCount"
  ];
   static  rcjSubMap = [
    "total",
    "totalNumber",
    "scCount"
  ];

  /**
   * 中间过程不做任何处理
   * @param {*} value 
   * @param {*} param 
   * @returns 
   */
  convertValue(value,param) {
    // let paramArray = param.split(ResourceCalculator.SPLITOR);
    // let digits = this.digitPropertyMap.get(paramArray[0]);
    // if(ObjectUtils.isEmpty(digits)) {
    //   digits = 2;
    // }
    // return NumberUtil.numberScale(value, digits);
    return value;
  }
  initDigitPropertyMap()
  {
    this.digitPropertyMap.set("resQty",5);
    this.digitPropertyMap.set("totalNumber",4);
    this.digitPropertyMap.set("quantity",5);
    this.digitPropertyMap.set("scCount",4);
  }

  static getInstance({constructId, unitId,deRowId},ctx){
    return new ResourceCalculator(constructId,unitId,deRowId,null,ctx);
  }

  /**
   *
   * @param constructId 当前工程
   * @param unitId 当前修改的人材机所属的单位工程
   * @param deRowId 当前修改的人材机所属的定额
   * @param resourceId 为当前修改的人材机ID
   * @param ctx
   */
  constructor(constructId,unitId,deRowId,resourceId,ctx) {
    super(ctx);
    this.ctx = ctx;
    this.constructId = constructId;
    this.unitId = unitId;
    this.deRowId = deRowId;
    this.resourceId = resourceId;
    this.initDigitPropertyMap();

  }
  async prepare()
  {
    const {service} = EE.app;
    this.precision = service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(this.constructId);
  }

  async analyze() {
    await this.prepare();
    this.preload(rcjBaseFn);
    this.buildRules();
    await this.render()
  }

  /**
   * 填充人材机数据
   */
  async render() {
    this.rcjContext.forEach(item =>{
      for (const key of ResourceCalculator.rcjMap) {
        LogUtil.renderLogger("ResourceCalculator :" + item.sequenceNbr + "---key :" + key );
        let digital = DeUtils.getRCJPrecision(key, this.precision);
        if(ObjectUtils.isEmpty(digital)){
          digital = 2;
        }
        if (key === "scCount") {
          item[key] = NumberUtil.numberScale(this.parser(key + "_" + item.sequenceNbr), 4);
        }else{
          if(key === "totalNumber"){//特殊处理，结果值不限制位数，计算值算位数
            let columnKey = key + "_" + item.sequenceNbr;
            item[key] = NumberUtil.numberFormat(this.parser(columnKey),digital);
            this.instanceMap[columnKey] = NumberUtil.numberFormat(item[key],digital);
          }else{
            item[key] = NumberUtil.numberFormat(this.parser(key+"_"+ item.sequenceNbr), digital);
          }
        }
        LogUtil.renderLogger("ResourceCalculator :" + item.sequenceNbr + "---key :" + key + "---value :" + item[key]);
      }
    })

  }

  initCurrentDe(){
    this.currentDe = this.ctx.deMap.getNodeById(this.deRowId);
  }

  /**
   * sequenceNbr  配比材料的父级， 如果定额是人材机，则为定额的id
   * @param {*} rules 
   * @param {*} pbs 
   * @param {*} sequenceNbr 
   * @returns 
   */
  buildPbRules(rules, pbs, sequenceNbr){
    if(ObjectUtils.isEmpty(pbs)){
      return;
    }
    //定额价
    let dePriceRules = "0";
    //市场价
    let marketPriceRules = "0";
    pbs.forEach(i => {
      this.rcjContext.push(i);
      rules[this.getMarketPriceKey(i.sequenceNbr)] = PBRules['marketPrice'].mathFormula;
      rules[this.getRCJPriceKey(i.sequenceNbr)] = PBRules['dePrice'].mathFormula;
      rules[this.getResQtyKey(i.sequenceNbr)] = PBRules['resQty'].mathFormula;
      //子数量= 子消耗量*父工程量
      rules[this.getTotalNumberKey(i.sequenceNbr)] = this.getResQtyKey(i.sequenceNbr) + "*" + this.getTotalNumberKey(sequenceNbr);
      //合价=工程量*市场价
      rules[this.getTotalKey(i.sequenceNbr)] = this.getTotalNumberKey(i.sequenceNbr) + "*" + this.getMarketPriceKey(i.sequenceNbr);
      //Σ 消耗量*定额价
      dePriceRules += "+" + this.getResQtyKey(i.sequenceNbr) + "*" + this.getRCJPriceKey(i.sequenceNbr);
      //Σ 子消耗量*子市场
      marketPriceRules += "+" + this.getMarketPriceKey(i.sequenceNbr) + "*" + this.getResQtyKey(i.sequenceNbr);

      rules[this.getScCount(i.sequenceNbr)] = PBRules['scCount'].mathFormula;
      rules[this.getTransferFactor(i.sequenceNbr)] = PBRules['transferFactor'].mathFormula;
      //三材量
      rules[this.getScCount(i.sequenceNbr)] = this.getTotalNumberKey(i.sequenceNbr) + "*" + this.getTransferFactor(i.sequenceNbr);
    });
    rules[this.getRCJPriceKey(sequenceNbr)] = dePriceRules;
    rules[this.getMarketPriceKey(sequenceNbr)] = marketPriceRules;
  }

  buildRules() {
    this.initCurrentDe();
    let rules = {};
    let rcjs = this.ctx.resourceMap.getValues(WildcardMap.generateKey(this.unitId, this.deRowId) + WildcardMap.WILDCARD);

    rcjs.forEach(item => {

      if (!ZSFeeConstants.ZS_RCJ_LIST.includes(item.materialCode)) {
        this.rcjContext.push(item);
        let sequenceNbr = item.sequenceNbr;

        rules[this.getMarketPriceKey(sequenceNbr)] = RJCRules['marketPrice'].mathFormula;
        rules[this.getResQtyKey(sequenceNbr)] = RJCRules['resQty'].mathFormula;
        //合计数量
        if (item.isDeResource === CommonConstants.COMMON_YES) {
          rules[this.getTotalNumberKey(this.deRowId)] = RJCRules['quantity'].mathFormula;
        }//是不是定额人材机都需要处理此项
        rules[this.getTotalNumberKey(sequenceNbr)] = this.getResQtyKey(sequenceNbr) + "*quantity";//定额：合计数量 = 工程量*消耗量

        //合价
        rules[this.getTotalKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getMarketPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        //有配比
        if (item.markSum == CommonConstants.COMMON_YES && ObjectUtils.isNotEmpty(item.pbs)) {
          this.buildPbRules(rules, item.pbs, sequenceNbr);
        } else {
          rules[this.getRCJPriceKey(sequenceNbr)] = "dePrice";
          rules[this.getMarketPriceKey(sequenceNbr)] = "marketPrice";
        }

        rules[this.getScCount(sequenceNbr)] = RJCRules['scCount'].mathFormula;
        rules[this.getTransferFactor(sequenceNbr)] = RJCRules['transferFactor'].mathFormula;
        //三材量
        rules[this.getScCount(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getTransferFactor(sequenceNbr);
      } else {
        //降效系数人材机数量=await this.service.PreliminaryEstimate.gsDeService.calculateZSFee

        this.rcjContext.push(item);
        let sequenceNbr = item.sequenceNbr;

        rules[this.getMarketPriceKey(sequenceNbr)] = RJCRules['marketPrice'].mathFormula;
        rules[this.getResQtyKey(sequenceNbr)] = RJCRules['resQty'].mathFormula;
        //合计数量
        if (item.isDeResource === CommonConstants.COMMON_YES) {
          rules[this.getTotalNumberKey(sequenceNbr)] = RJCRules['quantity'].mathFormula;
        }
        //是不是定额人材机都需要处理此项
        rules[this.getTotalNumberKey(sequenceNbr)] = "totalNumber";

        //合价
        rules[this.getTotalKey(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getMarketPriceKey(sequenceNbr);// parentRules["total"]; 合价（市场价合价）= 合计数量 * 市场价
        //有配比
        if (item.markSum == CommonConstants.COMMON_YES && ObjectUtils.isNotEmpty(item.pbs)) {
          this.buildPbRules(rules, item.pbs, sequenceNbr);
        } else {
          rules[this.getRCJPriceKey(sequenceNbr)] = "dePrice";
          rules[this.getMarketPriceKey(sequenceNbr)] = "marketPrice";
        }

        rules[this.getScCount(sequenceNbr)] = RJCRules['scCount'].mathFormula;
        rules[this.getTransferFactor(sequenceNbr)] = RJCRules['transferFactor'].mathFormula;
        //三材量
        rules[this.getScCount(sequenceNbr)] = this.getTotalNumberKey(sequenceNbr) + "*" + this.getTransferFactor(sequenceNbr);
      }

      // QTCLF1（其他材料费）的数量=归属定额中与其平级的材料∑（类型为材料的市场价*消耗量）*该条材料的消耗量/100 * 归属定额工程量
      // if (item.materialCode.includes('QTCLF1') && item.unit === '%') {
      //   let totalNumberFormula = 0;
      //   let sequenceNbr = item.sequenceNbr;
      //   let noneQtclfRcjs = rcjs.filter(item => !item.materialCode.includes('QTCLF1') && item.kind === 2);
      //   noneQtclfRcjs.forEach(noneQtclfRcj => {
      //     let sequenceNbr2 = noneQtclfRcj.sequenceNbr;
      //     rules[this.getMarketPriceKey(sequenceNbr2)] = "marketPrice";
      //     // QTCLF1（其他材料费）的数量=归属定额中与其平级的材料∑（类型为材料的市场价*消耗量）*该条材料的消耗量/100 * 归属定额工程量
      //     totalNumberFormula = totalNumberFormula + '+(' + this.getResQtyKey(sequenceNbr2) + "*" + this.getMarketPriceKey(sequenceNbr2) + ')';
      //   })
      //   rules[this.getTotalNumberKey(sequenceNbr)] = "(" + totalNumberFormula + ")" + "*" + this.getResQtyKey(sequenceNbr) + "*" + this.currentDe.quantity + "/100";
      // }


    });

    this.loadRules(rules);
  }


  getValue({type,kind,column})
  {
    let currentDe = this.ctx.deMap.getNodeById(this.deRowId);
    let value;
    switch (type) {
      case `DE`:{
        if (typeof column == 'function') {
          value = column({ de: currentDe});
        } else {
          value = currentDe[column];
        }
        break;
      }

      default:{
        value = {type,kind,column};
        break;
      }
    }
    return value;
  }
  getRuntimeValue({type,kind,column},param)
  {
    let value= 0;
    let key = param.split(ResourceCalculator.SPLITOR)[1];
    let item  = this.rcjContext.find(item => item.sequenceNbr === key);
    switch (type) {
      case `DE`: {
        if (typeof column == "function") {
          value = column(item);
        } else {
          value = item[column]
        }
      }
      case `item`: {
        if (typeof column == "function") {
          value = column(item);
        } else {
          value = item[column]
        }
        
      }
    }

    if (ObjectUtils.isEmpty(value)) {
      value = 0;
    }
    let columnKey = param.split(ResourceCalculator.SPLITOR)[0];
    //如果是人材机的数量，则需要根据精度进行处理
    let digital = DeUtils.getRCJPrecision(columnKey, this.precision);
    if (ObjectUtils.isNotEmpty(digital)) {
      value = NumberUtil.numberFormat(value, digital);
    }

    return value;
  }

  getResQtyKey(sequenceNbr)
  {
    return "resQty_" + sequenceNbr;
  }

  getTotalNumberKey(sequenceNbr){
    return "totalNumber_" + sequenceNbr;
  }
  getTotalKey = (sequenceNbr) => {
    return "total_" + sequenceNbr;
  }
  getRCJPriceKey = (sequenceNbr) => {
    return "dePrice_" + sequenceNbr;
  }
  getMarketPriceKey = (sequenceNbr) => {
    return "marketPrice_" + sequenceNbr;
  }
  getPriceKey = (sequenceNbr) => {
    return "price_" + sequenceNbr;
  }

  getQuantity = (sequenceNbr) => {
    return "quantity_" + sequenceNbr;
  }
  getPrice = (sequenceNbr) => {
    return "price_" + sequenceNbr;
  }

  getScCount = (sequenceNbr) => {
    return "scCount_" + sequenceNbr;
  }

  getTransferFactor = (sequenceNbr) => {
    return "transferFactor_" + sequenceNbr;
  }
}
module.exports = {ResourceCalculator};