const {Service} = require('../../../core');
const {GsBaseDeRelation} = require("../models/GsBaseDeRelation");
const {ObjectUtils} = require('../utils/ObjectUtils');
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../utils/NumberUtil");
const {GsBaseDeRelation2012} = require("../models/GsBaseDeRelation2012");

/**
 * 二级定额 service
 * @class
 */
class GsBaseDeRelationService extends Service{

    constructor(ctx) {
        super(ctx);
        this.gsBaseDeRelationDao = this.app.db.PreliminaryEstimate.manager.getRepository(GsBaseDeRelation);
        this.gsBaseDeRelationDao2012 = this.app.db.PreliminaryEstimate.manager.getRepository(GsBaseDeRelation2012);
    }

    /**
     * 查询二级定额
     * @param parentDeId
     * @returns {Promise<GsBaseDeRelation[]|Error>}
     */
    async listSubDeList(parentDeId) {
        if (ObjectUtils.isEmpty(parentDeId)) {
            throw new Error("必传参数定额标准为空");
        }

        return await this.gsBaseDeRelationDao.find({
            where: {deId: parentDeId},
            order: {sortNo: "ASC"}
        });
    }

    /**
     * 查询二级定额
     * @returns {Promise<GsBaseDeRelation[]|Error>}
     * @param sequenceNbr
     */
    async getBySequenceNbr(sequenceNbr) {
        if (ObjectUtils.isEmpty(sequenceNbr)) {
            throw new Error("必传参数定额sequenceNbr为空");
        }

        let deRelation = await this.gsBaseDeRelationDao.findOne({
            where: {sequenceNbr: sequenceNbr}
        });
        return deRelation;
    }

    /**
     * 查询二级定额
     * @returns {Promise<GsBaseDeRelation[]|Error>}
     * @param libraryCode
     */
    async getByLibraryCode(libraryCode) {
        if (ObjectUtils.isEmpty(libraryCode)) {
            throw new Error("必传参数定额libraryCode为空");
        }

        let deRelationList = await this.gsBaseDeRelationDao.find({
            where: {libraryCode: libraryCode}
        });
        return deRelationList;
    }

    /**
     * 查询二级定额
     * @returns {Promise<GsBaseDeRelation[]|Error>}
     * @param deCode
     */
    async getByDeCode(deCode) {
        if (ObjectUtils.isEmpty(deCode)) {
            throw new Error("必传参数定额deCode为空");
        }

        let deRelationList = await this.gsBaseDeRelationDao.find({
            where: {deCode: deCode}
        });
        return deRelationList;
    }

    /**
     * 查询二级定额
     * @returns {Promise<GsBaseDeRelation|Error>}
     * @param constructId
     * @param unitId
     * @param deCode
     */
    async getOneByDeCode(deCode, libraryCode) {
        if (ObjectUtils.isEmpty(deCode)) {
            throw new Error("必传参数定额deCode为空");
        }
        let deRelationList = await this.gsBaseDeRelationDao.findOne({
            where: {
                deCode: deCode,
                libraryCode: libraryCode
            }
        });
        return deRelationList;
    }



    /**
     * 获取子目定额
     * @param deIdF
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async getZmRelationByDeIdF(deIdF) {
        if (null == deIdF) {
            throw new Error("必传参数deIdF为空");
        }
        let result = await this.gsBaseDeRelationDao2012.find({
            where: {
                deIdF: deIdF
            }
        });
        result.forEach(item => item.isRelationed = false);
        return result;
    }

    /**
     * 获取子目定额
     * @param params
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async getZmRelation(params) {
        if (null == params.libraryCode) {
            throw new Error("必传参数定额标准为空");
        }

        return await this.gsBaseDeRelationDao2012.find({
            where: {
                libraryCode: params.libraryCode,
                deCodeF: params.deCode,
                deNameF: params.deName,
            }
        });
    }

    /**
     * 获取子目定额
     * @param standardId
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async zmDeList(standardId) {
        if (null == standardId) {
            throw new Error("必传参数standardId为空");
        }
        let result = {};
        let deRelationList = await this.getZmRelationByDeIdF(standardId);
        if (ObjectUtil.isEmpty(deRelationList)) {
            return result;
        }
        deRelationList.sort((a, b) =>
            a.deCodeZ.localeCompare(b.deCodeZ, undefined, {
                numeric: true,        // 按数字而非字符串排序
                sensitivity: 'base'   // 忽略大小写和重音
            })
        );
        result = await this.zmGroup(deRelationList);
        // 提取 quantity 值并去重
        let quantityFormulas = [...new Set(deRelationList.map(item => item.quantity))].map(q => ({ formula: q }));
        let quantitys = await this.cariableCoefficient(result.zmVariableRuleList, quantityFormulas);
        for (let zmDe of deRelationList) {
            zmDe.quantity = quantitys.find(item => zmDe.quantityExpression === item.formula)?.resultValue;
            zmDe.quantity = ObjectUtil.isEmpty(zmDe.quantity)?0:zmDe.quantity;
        }
        return result;
    }

    /**
     * 子目定额工程量计算
     * @param standardId
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async zmDeListCalculate(constructId, standardId, zmVariableRuleList) {
        if (null == standardId) {
            throw new Error("必传参数standardId为空");
        }
        let result = {};
        let deRelationList = await this.getZmRelationByDeIdF(standardId);
        if (ObjectUtil.isNotEmpty(deRelationList)) {
            deRelationList.sort((a, b) =>
                a.deCodeZ.localeCompare(b.deCodeZ, undefined, {
                    numeric: true,        // 按数字而非字符串排序
                    sensitivity: 'base'   // 忽略大小写和重音
                })
            );
        }
        // 提取 quantity 值并去重
        let quantityFormulas = [...new Set(deRelationList.map(item => item.quantity))].map(q => ({ formula: q }));
        zmVariableRuleList = await this.service.PreliminaryEstimate.gsBaseDeRelationVariableCoefficientService.updateVariableCoefficient(zmVariableRuleList);
        zmVariableRuleList.forEach(item => {
            if (ObjectUtils.isNotEmpty(item.resultValue) || item.ifEditable === 1) {
                item.resultValue = NumberUtil.numberScale(item.resultValue, 6);
            }
        })
        let quantitys = await this.cariableCoefficient(zmVariableRuleList, quantityFormulas);
        for (let zmDe of deRelationList) {
            let quantity = quantitys.find(item => zmDe.quantityExpression === item.formula)?.resultValue
            zmDe.quantity = quantity;
        }
        result = await this.zmGroup(deRelationList);
        result.zmVariableRuleList = zmVariableRuleList
        return result;
    }

    /**
     * 子目定额工程量计算
     * @param standardId
     * @returns {Promise<BaseDeRelation2022[]|Error>}
     */
    async zmCalculateQuantity(constructId, zmVariableRuleList, zmDe) {
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        // 提取 quantity 值并去重
        let quantityFormulas = [{ formula: zmDe.quantityExpression}];
        let quantitys = await this.cariableCoefficient(zmVariableRuleList, quantityFormulas);
        let quantity = quantitys.find(item => zmDe.quantityExpression === item.formula)?.resultValue
        zmDe.quantity = NumberUtil.numberScale(quantity, precision.EDIT.DE.quantity);
        return zmDe;
    }

    /**
     *
     * @returns {Promise<void>}
     */
    async zmGroup(deRelationList) {
        let result = {};
        if (ObjectUtil.isEmpty(deRelationList)) {
            return result
        }
        // 遍历 deRelationList
        let grouped = deRelationList.reduce((acc, item) => {
            let { groupId, relationContent } = item;
            // 创建一个唯一的键来组合 groupId 和 relationContent
            let key = `${groupId}-${relationContent}`;
            // 如果这个键还不存在于结果中，初始化它
            if (!acc[key]) {
                acc[key] = {
                    sequenceNbr: Snowflake.nextId(),
                    groupId,
                    relationContent,
                    children: []
                };
            }
            // 将当前项添加到对应的 children 数组中
            acc[key].children.push(item);
            return acc;
        }, {});
        // 将结果转换为数组（可选）
        let groupedResult = Object.values(grouped);
        let zmPointList = groupedResult.map(item => item.relationContent);
        let zmDeList = []
        let sh = {
            sequenceNbr: Snowflake.nextId(),
            relationContent: deRelationList[0].libraryNameRelation,
            children: groupedResult
        }
        zmDeList.push(sh)
        result.zmDeList = zmDeList
        result.zmPointList = zmPointList

        //获取子目变量规则
        let zmVariableRuleList = await this.service.PreliminaryEstimate.gsBaseDeRelationVariableCoefficientService.getCoefficientByGroupId(groupedResult[0].groupId);
        result.libraryNameRelation = deRelationList[0].libraryNameRelation
        result.zmVariableRuleList = zmVariableRuleList
        return result;
    }


    async cariableCoefficient(data, quantitys = []) {
        // 创建一个对象来存储变量名和对应的值
        const variables = {};
        // 首先，将所有输入的值存储到 variables 对象中
        data.forEach(item => {
            if (item.variableCode && item.value !== undefined) {
                variables[item.variableCode] = item.resultValue;
            }
        });
        quantitys.forEach(item => {
            let formulaFunc = new Function(...Object.keys(variables), `return ${item.formula};`);
            item.resultValue = formulaFunc(...Object.values(variables));
        });
        return quantitys;
    }


}

GsBaseDeRelationService.toString = () => '[class GsBaseDeRelationService]';
module.exports = GsBaseDeRelationService;
