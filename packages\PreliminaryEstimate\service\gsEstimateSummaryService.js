const {Service} = require("../../../core");
const gsHz = require("../jsonData/gs_gshz.json");
const {Snowflake} = require("../utils/Snowflake");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const ProjectDomain = require("../domains/ProjectDomain");
const OtherProjectCostOptionMenuConstants = require("../constants/OtherProjectCostOptionMenuConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const xeUtils = require("xe-utils");
const EstimateSummaryCategoryConstants = require("../constants/EstimateSummaryCategoryConstants");
const FileOperatorType = require("../constants/FileOperatorType");
const UtilsPs = require("../../../core/ps");
const fs = require("fs");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {BrowserWindow, dialog} = require('electron');
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../../../common/ResponseData");
const {GsEstimateSummary} = require("../models/GsEstimateSummary");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {getConnection, getRepository, getManager} = require('typeorm');

/**
 * 概算汇总
 */
class GsEstimateSummaryService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 初始化建设其他费列表
     */
    defaultEstimateSummary() {
        // 复制，防止修改原值
        let gsHzCopy = ConvertUtil.deepCopy(gsHz);
        // 添加节点属性
        this.addPropertiesToTree(gsHzCopy, null);
        // 树结构转数组
        let gsEstimateSummarys = xeUtils.toTreeArray(gsHzCopy);
        gsEstimateSummarys.forEach(item => {
            item.children = [];
        });
        return gsEstimateSummarys;
    }

    /**
     * 添加节点属性
     * @param gsHz  概算汇总模板
     * @param parentId
     */
    addPropertiesToTree(gsHz, parentId) {
        gsHz.forEach(node => {
            // 添加sequenceNbr
            node["sequenceNbr"] = Snowflake.nextId();
            // 添加父节点
            node["parentId"] = parentId;
            // 添加层级权限
            // if (node.category === EstimateSummaryCategoryConstants.PROJECT_COST) {
            //     node["permission"] = OtherProjectCostOptionMenuConstants.TopItem;
            // }
            if (node.category === EstimateSummaryCategoryConstants.JA_PROJECT_COST) {
                node["permission"] = OtherProjectCostOptionMenuConstants.SubItem;
            } else if (node.category === EstimateSummaryCategoryConstants.JS_LOAN_INTEREST || node.category === EstimateSummaryCategoryConstants.COMMON_COST
                || node.category === EstimateSummaryCategoryConstants.TOTAL_COST) {
                node["permission"] = OtherProjectCostOptionMenuConstants.CommonItem;
            } else {
                node["permission"] = OtherProjectCostOptionMenuConstants.SubItem;
            }
            node.jzFee = null;  // 建筑工程费
            node.azFee = null;  // 安装工程费
            node.sbgzFee = null;  // 设置购置费
            node.qtFee = null;  // 其他费用
            node.price = 0;  //金额
            node.unit = null;  // 单位
            node.average = null;  // 工程规模
            node.unitCost = null;  // 单方造价
            node.proportion = null;  // 占总投资比例
            node.whetherPrint = 1;  // 是否打印 1:打印  0:不打印
            node.adopted = false;  // 是否被引用
            if (node.children && node.children.length) {
                this.addPropertiesToTree(node.children, node.sequenceNbr);
            }
        });
    }

    /**
     * 获取概算汇总
     * @param args
     * @returns {*}
     */
    async getEstimateSummaryList(args) {
        let {constructId} = args;

        let bussiness = ProjectDomain.getDomain(constructId).functionDataMap;
        // 获取概算汇总
        let gsEstimateSummaryList = bussiness.get(FunctionTypeConstants.PROJECT_GS_SUMMARY);

        // 获取项目结构
        let gsEstimateSummarys = this.setProjectStructure(constructId, gsEstimateSummaryList);

        // 获取建设其他费
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        // 更新建设期贷款利息，使用filter筛选满足条件的元素
        const filteredData = otherProjectCosts.filter(item => item.category === '贷款利息-等额本金' || item.category === '贷款利息-等额本息');
        if (ObjectUtils.isNotEmpty(filteredData)) {
            // 使用reduce对筛选后的元素的'amount'字段求和
            const sum = filteredData.reduce((accumulator, currentItem) => accumulator + currentItem.amount, 0);
            gsEstimateSummarys.forEach(item => {
                if (item.category === EstimateSummaryCategoryConstants.JS_LOAN_INTEREST) {
                    item.price = sum;
                    item.children = [];
                }
            });
        }

        // 获取概算费用代码
        let gsEstimateCodes = bussiness.get(FunctionTypeConstants.PROJECT_GS_CODE);

        // 费用汇总添加数据
        let estimateSummaryArray = await this.countEstimateSummary(constructId, gsEstimateSummarys, gsEstimateCodes);

        estimateSummaryArray.forEach(item => {
            item.children = [];
            if (item.category === EstimateSummaryCategoryConstants.SB_PROJECT_COST) {
                item.sbgzFee = item.price;
            }
            if (item.category === EstimateSummaryCategoryConstants.OTHER_PROJECT_COST) {
                item.qtFee = item.price;
            }
            item.proportion = NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.divide(item.price, estimateSummaryArray[estimateSummaryArray.length - 1].price), 100), 2); // 占总投资比例
        });

        if (ObjectUtils.isNotEmpty(estimateSummaryArray)) {
            // 复制  添加是否被引用标识
            let newLists = ConvertUtil.deepCopy(estimateSummaryArray);
            for (let i = 0; i < estimateSummaryArray.length; i++) {
                //判断需要删除的费用汇总中的费用代号是否被引用
                let deleteItem = estimateSummaryArray[i];
                if (ObjectUtils.isNotEmpty(deleteItem)) {
                    for (let i = 0; i < newLists.length; i++) {
                        if (!ObjectUtils.isEmpty(newLists[i].calculateFormula)) {
                            let codeList = newLists[i].calculateFormula.split(/[+\-*/]/);
                            if (!ObjectUtils.isEmpty(codeList) && !ObjectUtils.isEmpty(deleteItem.code)) {
                                if (this.stringInArray(codeList, deleteItem.code)) {
                                    deleteItem.adopted = true;  // 该行已被引用，不可删除
                                }
                            }
                        }
                    }
                }
            }
        }

        // 转树
        let arrayTree = xeUtils.toArrayTree(estimateSummaryArray, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        // 添加序号
        this.addChildLevelNumbers(arrayTree);
        let newList = xeUtils.toTreeArray(arrayTree);

        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_GS_SUMMARY, newList);
        return newList;
    }

    /**
     * 设置工程项目结构
     * @param constructId  工程项目ID
     * @param gsEstimateSummaryList  概算汇总集合
     */
    setProjectStructure(constructId, gsEstimateSummaryList) {

        let whetherPrintMap = new Map();  // 是否打印输出map
        let remarkMap = new Map();   // 备注map
        let dispNoMap = new Map();  // 编码map
        let codeMap = new Map();   // codeMap
        let projectCostBelongsMap = new Map();   // 工程费用归属map

        let gsEstimateSummarys = [];
        let projectCostLow, sbProjectCostLow;
        gsEstimateSummaryList.forEach(item => {
            if (item.category === EstimateSummaryCategoryConstants.PROJECT_COST) {
                projectCostLow = item;
            }
            if (item.category === EstimateSummaryCategoryConstants.SB_PROJECT_COST) {
                sbProjectCostLow = item;
                dispNoMap.set(item.sequenceNbr, item.dispNo);
            }
            if (item.category === EstimateSummaryCategoryConstants.JA_PROJECT_COST) {
                whetherPrintMap.set(item.sequenceNbr, item.whetherPrint);
                remarkMap.set(item.sequenceNbr, item.remark);
                dispNoMap.set(item.sequenceNbr, item.dispNo);
                codeMap.set(item.sequenceNbr, item.code);
                projectCostBelongsMap.set(item.sequenceNbr, item.projectCostBelongs);
            } else {
                // 移除单项、单位，防止累加
                gsEstimateSummarys.push(item);
            }
        });
        // 获取工程项目
        let projectObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // // 获取该单项下的所有单位
        // let unitProjectsByConstruct = new Array();
        // PricingGSUtils.getUnitProjectsByCurrentNode(projectObj.children, unitProjectsByConstruct);

        // 获取该工程项目的层级结构数据
        let gsEstimateSummaryArray = [];
        if (ObjectUtils.isNotEmpty(projectObj) && ObjectUtils.isNotEmpty(projectObj.children)) {
            for (let i = 0; i < projectObj.children.length; i++) {
                // let dispNo = i + 1;
                let singleObj = projectObj.children[i];
                let gsEstimateSummary = {};  // GsEstimateSummary
                gsEstimateSummary.dispNo = null;  // dispNo.toString()
                gsEstimateSummary.sequenceNbr = singleObj.sequenceNbr;
                gsEstimateSummary.name = singleObj.name;
                gsEstimateSummary.calculateFormula = null; // 计算基数
                gsEstimateSummary.instructions = null;  // 基数说明
                gsEstimateSummary.category = EstimateSummaryCategoryConstants.JA_PROJECT_COST; // 费用类别
                gsEstimateSummary.rate = null;   // 费率
                gsEstimateSummary.average = ObjectUtils.isNotEmpty(singleObj.average) ? singleObj.average : 0;  // 工程规模
                gsEstimateSummary.unitCost = NumberUtil.numberScale(NumberUtil.divide(singleObj.projectCost, singleObj.average));  // 单方造价
                gsEstimateSummary.proportion = 0;  // 占总投资比例
                if (whetherPrintMap.has(singleObj.sequenceNbr)) {
                    gsEstimateSummary.whetherPrint = whetherPrintMap.get(singleObj.sequenceNbr);  // 是否打印 1:打印  0:不打印
                } else {
                    gsEstimateSummary.whetherPrint = 1;  // 是否打印 1:打印  0:不打印
                }
                if (remarkMap.has(singleObj.sequenceNbr)) {
                    gsEstimateSummary.remark = remarkMap.get(singleObj.sequenceNbr);
                } else {
                    gsEstimateSummary.remark = null;
                }
                if (codeMap.has(singleObj.sequenceNbr)) {
                    gsEstimateSummary.code = codeMap.get(singleObj.sequenceNbr);
                } else {
                    gsEstimateSummary.code = null;
                }
                gsEstimateSummary.parentId = projectCostLow.sequenceNbr;
                gsEstimateSummary.type = singleObj.type;

                // 存放项目与单位之间的子单项、单位
                let subSingleObjectArray = [];
                subSingleObjectArray.push(gsEstimateSummary);
                let unitObjectArray = [];
                // 添加单项、单位层级       dispNo:dispNo.toString()
                this.setProjectDate(singleObj, gsEstimateSummary, null, projectObj.sequenceNbr, subSingleObjectArray, unitObjectArray,
                    whetherPrintMap, remarkMap, dispNoMap, codeMap, projectCostBelongsMap);
                // 添加项目与单位之间子单项的数据
                this.setSubSingleDate(subSingleObjectArray, unitObjectArray, whetherPrintMap, remarkMap, dispNoMap, codeMap, constructId);
                gsEstimateSummaryArray.push(gsEstimateSummary);
            }

            // 按层级汇总相关值
            let newTree = this.sumChildNodePrices(new Array(gsEstimateSummaryArray));
            gsEstimateSummaryArray = newTree[0];
            // 单项的工程造价
            let prices = Number(gsEstimateSummaryArray.reduce((accumulator, estimateSummary) => {
                return NumberUtil.add(accumulator, estimateSummary.price);
            }, 0).toFixed(2));
            // 单项的建筑工程费
            let jzFees = Number(gsEstimateSummaryArray.reduce((accumulator, estimateSummary) => {
                return NumberUtil.add(accumulator, estimateSummary.jzFee);
            }, 0).toFixed(2));
            // 单项的安装工程费
            let azFees = Number(gsEstimateSummaryArray.reduce((accumulator, estimateSummary) => {
                return NumberUtil.add(accumulator, estimateSummary.azFee);
            }, 0).toFixed(2));

            if (ObjectUtils.isNotEmpty(gsEstimateSummaryArray)) {
                // 给每个单项添加parentId
                gsEstimateSummaryArray.forEach(item => {
                    item.parentId = projectCostLow.sequenceNbr;
                });
                // 将工程项目树结构转数组
                let gsEstimateSummaryProjects = xeUtils.toTreeArray(gsEstimateSummaryArray);
                gsEstimateSummaryProjects.forEach(item => {
                    item.children = [];
                    item["permission"] = OtherProjectCostOptionMenuConstants.SubItem;
                });
                // sbProjectCostLow.dispNo = dispNoMap.get(sbProjectCostLow.sequenceNbr);   // (gsEstimateSummaryArray.length + 1).toString();
                sbProjectCostLow.parentId = projectCostLow.sequenceNbr;
                projectCostLow.type = projectObj.type;
                projectCostLow.price = NumberUtil.add(ObjectUtils.isNotEmpty(sbProjectCostLow.price) ? sbProjectCostLow.price : 0, prices);
                projectCostLow.jzFee = NumberUtil.add(ObjectUtils.isNotEmpty(sbProjectCostLow.jzFee) ? sbProjectCostLow.jzFee : 0, jzFees);
                projectCostLow.azFee = NumberUtil.add(ObjectUtils.isNotEmpty(sbProjectCostLow.azFee) ? sbProjectCostLow.azFee : 0, azFees);
                projectCostLow.sbgzFee = gsEstimateSummarys[1].price;
                // gsEstimateSummarys[0].average = ObjectUtils.isNotEmpty(projectObj.average) ? projectObj.average : 0;  // 工程规模
                // gsEstimateSummarys[0].unitCost = NumberUtil.numberScale(NumberUtil.divide(gsEstimateSummarys[0].price, projectObj.average));  // 单方造价

                // 添加项目层级至概算模板
                gsEstimateSummaryProjects.unshift(1, 0);
                Array.prototype.splice.apply(gsEstimateSummarys, gsEstimateSummaryProjects);
            }
        } else {
            sbProjectCostLow.dispNo = "1";
            projectCostLow.price = sbProjectCostLow.price;
        }
        return gsEstimateSummarys;
    }

    /**
     * 设置工程项目数据
     * @param singleObj  单项
     * @param gsEstimateSummary 单项-概算
     * @param dispNo  编码
     * @param constructId  工程项目id
     * @param subSingleObjectArray  子单项集合
     * @param unitObjectArray  单位集合
     * @param whetherPrintMap  单项、单位是否打印Map
     * @param remarkMap  单项、单位备注Map
     * @param dispNoMap  显示序号Map
     * @param codeMap  单项、单位费用代号Map
     * @param projectCostBelongsMap  单项、单位工程费用归属Map
     */
    setProjectDate(singleObj, gsEstimateSummary, dispNo, constructId, subSingleObjectArray, unitObjectArray, whetherPrintMap, remarkMap, dispNoMap, codeMap, projectCostBelongsMap) {
        let subGsEstimateSummarys = [];
        // 金额、建筑工程费、安装工程费
        let total = 0, jzFeeTotal = 0, azFeeTotal = 0;  // 安装工程费
        if (ObjectUtils.isNotEmpty(singleObj.children)) {
            for (let i = 0; i < singleObj.children.length; i++) {
                let child = singleObj.children[i];
                let subGsEstimateSummary = {};
                // if (ObjectUtils.isNotEmpty(dispNo)) {
                //     subGsEstimateSummary.dispNo = dispNo + "." + (i + 1);
                // } else {
                //     subGsEstimateSummary.dispNo = (i + 1);
                // }
                subGsEstimateSummary.dispNo = null;
                subGsEstimateSummary.sequenceNbr = child.sequenceNbr;
                subGsEstimateSummary.name = child.name;
                subGsEstimateSummary.calculateFormula = null; // 计算基数
                subGsEstimateSummary.instructions = null;  // 基数说明
                subGsEstimateSummary.category = EstimateSummaryCategoryConstants.JA_PROJECT_COST; // 费用类别
                subGsEstimateSummary.rate = null;  // 费率
                subGsEstimateSummary.jzFee = 0;  // 建筑工程费
                subGsEstimateSummary.azFee = 0;  // 安装工程费
                subGsEstimateSummary.price = child.projectCost;  // 金额
                total = NumberUtil.add(total, child.projectCost);
                subGsEstimateSummary.average = child.average;  // 工程规模
                subGsEstimateSummary.unitCost = NumberUtil.numberScale(NumberUtil.divide(child.projectCost, child.average));  // 单方造价
                if (whetherPrintMap.has(child.sequenceNbr)) {
                    subGsEstimateSummary.whetherPrint = whetherPrintMap.get(child.sequenceNbr);   // 是否打印 1:打印  0:不打印
                } else {
                    subGsEstimateSummary.whetherPrint = 1;   // 是否打印 1:打印  0:不打印
                }
                if (remarkMap.has(child.sequenceNbr)) {
                    subGsEstimateSummary.remark = remarkMap.get(child.sequenceNbr);
                } else {
                    subGsEstimateSummary.remark = null;
                }
                if (dispNoMap.has(child.sequenceNbr)) {
                    subGsEstimateSummary.dispNo = dispNoMap.get(child.sequenceNbr);
                } else {
                    subGsEstimateSummary.dispNo = null;
                }
                if (codeMap.has(child.sequenceNbr)) {
                    subGsEstimateSummary.code = codeMap.get(child.sequenceNbr);
                } else {
                    subGsEstimateSummary.code = null;
                }
                if (projectCostBelongsMap.has(child.sequenceNbr)) {
                    subGsEstimateSummary.projectCostBelongs = projectCostBelongsMap.get(child.sequenceNbr);
                } else {
                    subGsEstimateSummary.projectCostBelongs = null;
                }
                subGsEstimateSummary.parentId = singleObj.sequenceNbr;
                subGsEstimateSummary["ifProject"] = true;
                if (child.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    subGsEstimateSummary.unit = child.averageUnit;  // 工程规模单位
                    if (child.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                        subGsEstimateSummary.jzFee = child.projectCost;  // 工程造价
                    }
                    if (child.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                        subGsEstimateSummary.azFee = child.projectCost;  // 工程造价
                    }
                    subGsEstimateSummary.libraryCode = child.constructMajorType;
                    subGsEstimateSummary.unitCost = NumberUtil.numberScale(NumberUtil.divide(child.projectCost, child.average));  // 单方造价
                    unitObjectArray.push(subGsEstimateSummary);
                }
                subGsEstimateSummary.type = child.type;
                jzFeeTotal = NumberUtil.add(jzFeeTotal, subGsEstimateSummary.jzFee);
                azFeeTotal = NumberUtil.add(azFeeTotal, subGsEstimateSummary.azFee);
                subGsEstimateSummarys.push(new Object(subGsEstimateSummary));

                // 搜集所有的中间子单项
                if (child.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE && ObjectUtils.isNotEmpty(child.children)) {
                    // 当该单项下是单位，且不包含单项  注：单项下只能包括子单项或者全是单位
                    let units = child.children.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.parentId === child.sequenceNbr);
                    if (ObjectUtils.isEmpty(units)) {
                        subSingleObjectArray.push(subGsEstimateSummary);
                    }
                }
                gsEstimateSummary.children = subGsEstimateSummarys;
                gsEstimateSummary.price = total;
                gsEstimateSummary.jzFee = jzFeeTotal;
                gsEstimateSummary.azFee = azFeeTotal;
                gsEstimateSummary.unitCost = NumberUtil.numberScale(NumberUtil.divide(total, gsEstimateSummary.average));  // 单方造价
                if (ObjectUtils.isNotEmpty(child)) {  // dispNo: subGsEstimateSummary.dispNo
                    this.setProjectDate(child, subGsEstimateSummary, null, constructId, subSingleObjectArray, unitObjectArray, whetherPrintMap, remarkMap, dispNoMap, codeMap, projectCostBelongsMap);
                }
            }
        }
    }

    /**
     * 添加项目与单位之间子单项的数据
     * @param subSingleObjectArray  子单项集合
     * @param unitObjectArray  单位集合
     * @param whetherPrintMap  单项、单位是否打印Map
     * @param remarkMap  单项、单位备注Map
     * @param dispNoMap  显示序号Map
     * @param codeMap 单项、单位费用代号Map
     * @param constructId
     */
    setSubSingleDate(subSingleObjectArray, unitObjectArray, whetherPrintMap, remarkMap, dispNoMap, codeMap, constructId) {
        let jzFeeTotal = 0;  // 建筑工程费
        let azFeeTotal = 0;  // 安装工程费
        let sbgzFeeTotal = 0;  // 设置购置费
        let qtFeeTotal = 0;  // 其他费用
        let priceTotal = 0;  //金额
        unitObjectArray.forEach(item => {
            // 工程费用归属
            if (ObjectUtils.isEmpty(item.projectCostBelongs)) {
                if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                    item.projectCostBelongs = '建筑工程';
                }
                if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                    item.projectCostBelongs = '安装工程';
                }
            } else {
                let bussiness = ProjectDomain.getDomain(constructId).functionDataMap;
                let gsAdjustMap = bussiness.get(FunctionTypeConstants.PROJECT_GS_ADJUST);
                if (item.projectCostBelongs === '建筑工程' && item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                    item.jzFee = item.price;
                    item.azFee = 0;
                    // if(ObjectUtils.isNotEmpty(gsAdjustMap)){
                    //     let  adjustData =gsAdjustMap.find(item1=>item1.sequenceNbr === item.sequenceNbr);
                    //     if(ObjectUtils.isNotEmpty(adjustData)){
                    //         this.service.PreliminaryEstimate.gsAdjustService.updateGsAdjustFindOne(constructId,adjustData.sequenceNbr,0,item.jzFee)
                    //         adjustData.updateFlag=0;
                    //         adjustData.libraryCode= UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ;
                    //     }
                    // }
                }
                if (item.projectCostBelongs === '安装工程' && item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                    item.azFee = item.price;
                    item.jzFee = 0;
                    // if(ObjectUtils.isNotEmpty(gsAdjustMap)){
                    //     let  adjustData =gsAdjustMap.find(item1=>item1.sequenceNbr === item.sequenceNbr);
                    //     if(ObjectUtils.isNotEmpty(adjustData)){
                    //         this.service.PreliminaryEstimate.gsAdjustService.updateGsAdjustFindOne(constructId,adjustData.sequenceNbr,item.azFee,0)
                    //         adjustData.updateFlag=0;
                    //         adjustData.libraryCode=UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ;
                    //     }
                    // }
                }
            }
            jzFeeTotal = NumberUtil.add(jzFeeTotal, item.jzFee);
            azFeeTotal = NumberUtil.add(azFeeTotal, item.azFee);
            // sbgzFeeTotal = NumberUtil.add(sbgzFeeTotal, item.sbgzFee);
            // qtFeeTotal = NumberUtil.add(qtFeeTotal, item.qtFee);
            priceTotal = NumberUtil.add(priceTotal, item.price);
        });
        subSingleObjectArray.forEach(item => {
            item.jzFee = jzFeeTotal;
            item.azFee = azFeeTotal;
            // item.sbgzFee = sbgzFeeTotal;
            // item.qtFee = qtFeeTotal;
            item.price = priceTotal;
            if (whetherPrintMap.has(item.sequenceNbr)) {
                item.whetherPrint = whetherPrintMap.get(item.sequenceNbr);   // 是否打印 1:打印  0:不打印
            } else {
                item.whetherPrint = 1;
            }
            if (remarkMap.has(item.sequenceNbr)) {
                item.remark = remarkMap.get(item.sequenceNbr);
            } else {
                item.remark = null;
            }
            if (dispNoMap.has(item.sequenceNbr)) {
                item.dispNo = dispNoMap.get(item.sequenceNbr);
            } else {
                item.dispNo = null;
            }
            if (codeMap.has(item.sequenceNbr)) {
                item.code = codeMap.get(item.sequenceNbr);
            } else {
                item.code = null;
            }
            item.unitCost = NumberUtil.numberScale(NumberUtil.divide(priceTotal, item.average));
        });
    }

    /**
     * 树结构,每一级的值向上一级汇总
     * @param node 树节点
     * @returns {*}
     */
    childNodePrices(node) {
        if (!node.children || node.children.length === 0) {
            if (node.category === EstimateSummaryCategoryConstants.JA_PROJECT_COST) {
                node.jzFee = NumberUtil.numberScale(node.jzFee || 0);
                node.azFee = NumberUtil.numberScale(node.azFee || 0);
                node.price = NumberUtil.numberScale(NumberUtil.multiply(node.price || 0, (node.rate === null ? 100 : parseFloat(node.rate)) / 100));
            }
            return node;
        }
        if (node.category === EstimateSummaryCategoryConstants.JA_PROJECT_COST) {
            // 累加子节点的金额
            node.jzFee = node.children.reduce((sum, child) => {
                this.childNodePrices(child);
                return NumberUtil.numberScale(NumberUtil.add(sum, (child.jzFee || 0))); // 加上当前子节点金额
            }, 0);
            // 累加子节点的金额
            node.azFee = node.children.reduce((sum, child) => {
                this.childNodePrices(child);
                return NumberUtil.numberScale(NumberUtil.add(sum, (child.azFee || 0))); // 加上当前子节点金额
            }, 0);
            // 累加子节点的金额
            node.price = node.children.reduce((sum, child) => {
                this.childNodePrices(child);
                return NumberUtil.numberScale(NumberUtil.add(sum, (child.price || 0))); // 加上当前子节点金额
            }, 0);
            node.price = NumberUtil.numberScale(NumberUtil.multiply(node.price, (node.rate === null ? 100 : parseFloat(node.rate)) / 100));
        }
        return node; // 返回当前节点累计的金额
    }

    /**
     * 数组树
     * @param trees
     * @returns {*}
     */
    sumChildNodePrices(trees) {
        return trees.map(tree => {
            this.childNodePrices(tree);
            return tree;
        });
    }

    /**
     * 计算概算汇总数据
     * @param constructId
     * @param gsEstimateSummarys
     * @param gsEstimateCodes
     */
    async countEstimateSummary(constructId, gsEstimateSummarys, gsEstimateCodes) {
        // 费用代码<概算费用代码,price>
        let priceMap = new Map();
        // 计算基数 <概算汇总费用代号,calculateFormula>
        let codeFormulaMap = new Map();
        // 概算汇总费率
        let codeRateMap = new Map();

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.ESTIMATE_COST.je;
        let freeRate = precision.ESTIMATE_COST.SUMMARY.freeRate;

        // 费用代码
        for (let i = 0; i < gsEstimateCodes.length; i++) {
            let estimateCode = gsEstimateCodes[i];
            let code = ObjectUtils.isEmpty(estimateCode.code) ? estimateCode.code : estimateCode.code.toLowerCase();
            priceMap.set(code, estimateCode.price);
        }
        for (let i = 0; i < gsEstimateSummarys.length; i++) {
            let estimateSummary = gsEstimateSummarys[i];

            let code = ObjectUtils.isEmpty(estimateSummary.code) ? estimateSummary.code : estimateSummary.code.toLowerCase();
            if (estimateSummary.code === "A") {
                priceMap.set(code, estimateSummary.price);
            }
            if (ObjectUtils.isNotEmpty(estimateSummary.calculateFormula)) {
                codeFormulaMap.set(code, estimateSummary.calculateFormula.toLowerCase());
            } else {
                codeFormulaMap.set(code, estimateSummary.calculateFormula);
            }
            if (ObjectUtils.isNotEmpty(estimateSummary.rate)) {
                if (estimateSummary.rate.includes("~")) {
                    // 正则表达式用于匹配形如 "X%" 的字符串中的数字 X
                    const regex = /^(\d+)%$/;
                    const match = estimateSummary.rate.split("~")[0].match(regex);
                    let rate = parseFloat(match);
                    codeRateMap.set(code, NumberUtil.numberScale(rate, freeRate));
                } else {
                    let rate = parseFloat(estimateSummary.rate);
                    codeRateMap.set(code, NumberUtil.numberScale(rate, freeRate));
                }
            } else {
                // estimateSummary.rate = 100;
                codeRateMap.set(code, 100);
            }
            // priceMap.set(code, estimateSummary.price);
        }
        for (let i = 0; i < gsEstimateSummarys.length; i++) {
            let estimateSummary = gsEstimateSummarys[i];
            if (estimateSummary.category !== EstimateSummaryCategoryConstants.JA_PROJECT_COST) {
                //计算基数
                if (ObjectUtils.isEmpty(estimateSummary.calculateFormula)) {
                    continue;
                }

                let calculateFormula = estimateSummary.calculateFormula.toLowerCase();
                // 分解字符串成表达式和变量名
                const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
                // 存放替换后的计算公式
                let afterCalculateFormula = calculateFormula; // 102654.12+0+0    1836.46+100725.84+91.82

                // 递归计算概算汇总
                afterCalculateFormula = await this.recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je);
                let result;
                if (ObjectUtils.isNotEmpty(estimateSummary.rate)) {
                    if (estimateSummary.rate.includes("~")) {
                        // 正则表达式用于匹配形如 "X%" 的字符串中的数字 X
                        const regex = /^(\d+)%$/;
                        const match = estimateSummary.rate.split("~")[0].match(regex);
                        let rate = parseFloat(match);
                        result = NumberUtil.multiply(NumberUtil.numberScale(eval(afterCalculateFormula), je), NumberUtil.numberScale(rate / 100, freeRate));
                    } else {
                        result = NumberUtil.multiply(NumberUtil.numberScale(eval(afterCalculateFormula), je), NumberUtil.numberScale(estimateSummary.rate / 100, freeRate));
                    }
                } else {
                    result = ObjectUtils.isEmpty(afterCalculateFormula) ? 0 : NumberUtil.numberScale(eval(afterCalculateFormula), je);
                }
                estimateSummary.price = result;
                // estimateSummary.price = NumberUtil.numberScale(eval(afterCalculateFormula));
            }
        }

        gsEstimateSummarys.forEach(item => {
            item.children = [];
        });
        // 转树
        let arrayTree = xeUtils.toArrayTree(gsEstimateSummarys, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });

        // 树结构,每一级的值向上一级汇总
        let gsEstimateSummaryList = this.sumChildNodePrices(arrayTree);
        let newList = xeUtils.toTreeArray(gsEstimateSummaryList);

        // 更新概算汇总
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_GS_SUMMARY, newList);
        return newList;
    }

    /**
     * 递归计算概算汇总
     * @param calculateFormula 计算基数
     * @param afterCalculateFormula  存放替换后的计算公式
     * @param codeFormulaMap 计算基数 <费用汇总费用代号,calculateFormula>
     * @param priceMap 费用代码<费用代码,price>
     * @param variablesToReplace 拆分计算基数后的数组
     * @param codeRateMap
     * @param je  金额精度
     * @returns {*}
     */
    async recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je) {
        for (let variable of variablesToReplace) {
            if (priceMap.has(variable)) {
                if (priceMap.get(variable) < 0) {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + NumberUtil.numberScale(priceMap.get(variable), je) + ')');
                } else {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, NumberUtil.numberScale(priceMap.get(variable), je));
                }
            } else {
                if (isNaN(Number(variable))) {
                    //此处认为是数字不进来
                    if (codeFormulaMap.has(variable) && ObjectUtils.isNotEmpty(codeFormulaMap.get(variable)) && ObjectUtils.isNotEmpty(afterCalculateFormula)) {
                        let variablesToReplace1 = codeFormulaMap.get(variable).replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                        //说明当前行引用了费用代号 此处需要加上费率
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + variable + ')/100*' + codeRateMap.get(variable));
                        afterCalculateFormula = afterCalculateFormula.replace(variable, codeFormulaMap.get(variable));
                        afterCalculateFormula = await this.recursionSummary(codeFormulaMap.get(variable), afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace1, codeRateMap, je);
                    } else {
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '0'); // 比如E
                    }
                }
            }
        }
        return afterCalculateFormula;
    }

    /**
     * 添加概算汇总
     * @param args
     */
    async addEstimateSummary(args) {
        let {constructId, estimateSummary} = args;
        let lineNumber = args.lineNumber;   // 添加元素位置
        let parentId = estimateSummary.parentId;   // 添加元素的父id
        // 获取概算汇总
        let estimateSummarys = await this.getEstimateSummaryList(args);

        // 新增行
        let addEstimateSummary = [];
        if (ObjectUtils.isEmpty(estimateSummarys.parentId)) {
            estimateSummary.sequenceNbr = Snowflake.nextId();
            estimateSummary.whetherPrint = 1;
            estimateSummary.permission = OtherProjectCostOptionMenuConstants.CommonItem;
        } else {
            //查询父级
            let parentNode = estimateSummarys.find(item => item.sequenceNbr === parentId);
            estimateSummary.sequenceNbr = Snowflake.nextId();
            estimateSummary.whetherPrint = 1;
            estimateSummary.category = parentNode.category;
            estimateSummary.permission = OtherProjectCostOptionMenuConstants.CommonItem;
        }
        estimateSummary.adopted = false;  // 是否被引用
        estimateSummary.ifUpdateRate = true; // 费率是否修改字段

        // 添加新增行
        addEstimateSummary.push(estimateSummary);
        estimateSummarys.splice(lineNumber, 0, ...addEstimateSummary);

        estimateSummarys.forEach(item => {
            item.children = [];
        });
        // 转树
        let arrayTree = xeUtils.toArrayTree(estimateSummarys, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        // 添加序号
        this.addChildLevelNumbers(arrayTree);

        // 树结构,每一级的值向上一级汇总
        let gsEstimateSummaryList = this.sumChildNodePrices(arrayTree);
        let newList = xeUtils.toTreeArray(gsEstimateSummaryList);

        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_GS_SUMMARY, newList);
        return newList;
    }

    /**
     * 删除指定节点
     * @param args
     */
    async deleteEstimateSummary(args) {
        let {constructId, sequenceNbr} = args;
        // 获取概算汇总
        let estimateSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_SUMMARY);
        //判断需要删除的费用汇总中的费用代号是否被引用
        let deleteItem = estimateSummarys.find(item => item.sequenceNbr === sequenceNbr);
        if (ObjectUtils.isNotEmpty(deleteItem)) {
            for (let i = 0; i < estimateSummarys.length; i++) {
                if (ObjectUtils.isNotEmpty(estimateSummarys[i].calculateFormula)) {
                    let codeList = estimateSummarys[i].calculateFormula.split(/[+\-*/]/);
                    if (ObjectUtils.isNotEmpty(codeList) && ObjectUtils.isNotEmpty(deleteItem.code)) {
                        if (this.stringInArray(codeList, deleteItem.code)) {
                            // return ResponseData.fail('该行已被引用，不可删除');
                            const regex = new RegExp(`(?<![a-zA-Z0-9_])${deleteItem.code}(?![a-zA-Z0-9_])`, 'g');
                            estimateSummarys[i].calculateFormula = estimateSummarys[i].calculateFormula.replace(regex, "※");
                            // estimateSummarys[i].calculateFormula = estimateSummarys[i].calculateFormula.replaceAll(deleteItem.code, "※");
                            estimateSummarys[i].instructions = estimateSummarys[i].instructions.replaceAll(deleteItem.name, "0");
                            estimateSummarys[i].price = NumberUtil.subtract(estimateSummarys[i].price, deleteItem.price);
                        }
                    }
                }
            }
            // 删除该行数据
            // let estimateSummaryList = estimateSummarys.filter(item => item.sequenceNbr !== args.sequenceNbr && item.parentId !== args.sequenceNbr);
            estimateSummarys.forEach(item => {
                item.children = [];
            });
            // 转树
            let arrayTree = xeUtils.toArrayTree(estimateSummarys, {
                key: 'sequenceNbr',
                parentKey: 'parentId',
            });

            // 删除指定行及其所有子级数据
            this.deleteNodeById(arrayTree, sequenceNbr);

            // // 添加序号
            // this.addChildLevelNumbers(arrayTree);
            //
            // // 树结构,每一级的值向上一级汇总
            // let gsEstimateSummaryList = this.sumChildNodePrices(arrayTree);
            // let newList = xeUtils.toTreeArray(gsEstimateSummaryList);
            //
            // // 更新概算汇总
            // ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_GS_SUMMARY, newList);


            let estimateSummaryArray = xeUtils.toTreeArray(arrayTree);
            // 获取概算汇总代码
            let estimateCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_CODE);

            //调用计算费用汇总
            let newList = await this.countEstimateSummary(constructId, estimateSummaryArray, estimateCodePriceArray);
            return ResponseData.success(newList);
        } else {
            return ResponseData.fail('该行不存在');
        }
    }

    /**
     * 删除指定行及其所有子级数据
     * @param node
     * @param sequenceNbr
     */
    deleteNodeById(node, sequenceNbr) {
        if (node) {
            for (let i = 0; i < node.length; i++) {
                if (node[i].sequenceNbr === sequenceNbr) {
                    node.splice(i, 1);
                    break;
                } else {
                    this.deleteNodeById(node[i].children, sequenceNbr);
                }
            }
        }
    }

    /**
     * 保存或者修改概算汇总
     * @param args
     * @returns {ResponseData}
     */
    async saveEstimateSummary(args) {
        let {constructId} = args;
        let param = args.estimateSummary;

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.ESTIMATE_COST.je;
        let freeRate = precision.ESTIMATE_COST.SUMMARY.freeRate;

        if (ObjectUtils.isEmpty(param)) {
            return ResponseData.fail('参数错误');
        }
        // 获取概算汇总费用代码
        let estimateCodePriceArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_CODE);
        for (let i = 0; i < estimateCodePriceArray.length; i++) {
            if (param.code === estimateCodePriceArray[i].code) {
                return ResponseData.fail('当前费用代号与费用代码重复，请修改');
            }
        }
        // 获取概算汇总
        let estimateSummaryArray = await this.getEstimateSummaryList(args);
        // 判断概算汇总的费用代号是否被使用
        for (let i = 0; i < estimateSummaryArray.length; i++) {
            if (param.sequenceNbr !== estimateSummaryArray[i].sequenceNbr && ObjectUtils.isNotEmpty(param.code)
                && ObjectUtils.isNotEmpty(estimateSummaryArray[i].code) && param.code.toLowerCase() === estimateSummaryArray[i].code.toLowerCase()) {
                return ResponseData.fail('当前费用代码已被使用');
            }
        }

        if (ObjectUtils.isNotEmpty(param.category)) {
            if (param.category === EstimateSummaryCategoryConstants.JS_LOAN_INTEREST) {
                // 获取该工程项目的建设其他费
                let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
                // 使用filter筛选满足条件的元素
                const filteredData = otherProjectCosts.filter(item => item.category === '贷款利息-等额本金' || item.category === '贷款利息-等额本息');
                // 使用reduce对筛选后的元素的'amount'字段求和
                param.price = filteredData.reduce((accumulator, currentItem) => accumulator + currentItem.amount, 0);
                param.calculateFormula = null;
                param.instructions = null;
                param.rate = null;
            }
            // 当费用类别为这些的时候，费率不可修改
            if (EstimateSummaryCategoryConstants.UPDATE_RATE_CATEGORY_PERMISSIONS.includes(param.category)) {
                param.ifUpdateRate = false;
            }
        }

        let copyCode = param.code;
        let copyName = param.name;
        if (ObjectUtils.isEmpty(param.code)) {
            param.code = "※";
            param.name = "0";
        }
        let codePriceMap = new Map();  //1费用代号、代码存放<费用代号、代码,price>
        let codeFormulaMap = new Map();  //计算基数
        let codeInstructionsMap = new Map();  //基数说明、费用名称<费用代号、代码,instructions>
        let codeRateMap = new Map();
        let codeNameMap = new Map();
        let currentId = param.sequenceNbr;
        let priceChangeArray = [];
        // let code = param.code;  // 数据备份
        // let calculateFormula = param.calculateFormula;  // 数据备份
        if (ObjectUtils.isNotEmpty(param.sequenceNbr)) {
            // 修改
            let estimateSummary = estimateSummaryArray.find(item => item.sequenceNbr === param.sequenceNbr);
            if (estimateSummary == null) {
                return ResponseData.fail('参数错误');
            }

            await this.getAllCodeFormulaPriceMap(constructId, estimateCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, estimateSummaryArray);
            if (ObjectUtils.isEmpty(param.rate)) {
                param.rate = "100";
            }
            if (param.category !== estimateSummary.category) {
                param.price = 0;
            }
            let lowerParamCalculateFormula = ObjectUtils.isNotEmpty(param.calculateFormula) ? param.calculateFormula.toLowerCase() : param.calculateFormula;  // 转小写
            if (ObjectUtils.isNotEmpty(lowerParamCalculateFormula)) {
                // param.code = param.code.toLowerCase();  // 转小写
                // param.calculateFormula = param.calculateFormula.toLowerCase();   // 转小写
                let codeList = lowerParamCalculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                if (ObjectUtils.isNotEmpty(codeList)) {
                    for (let i = 0; i < codeList.length; i++) {
                        // 判断公式中引用的费用代码是不是引用的列表不存在的
                        let keys = [...codeNameMap.keys()].map(function (item) {
                            if (ObjectUtils.isNotEmpty(item)) {
                                return item.toLowerCase();
                            }
                        });
                        if (!keys.includes(codeList[i]) && isNaN(Number(codeList[i]))) {
                            return ResponseData.fail("费用代码输入有误！");
                        }
                    }
                }
            }
            //判断code
            if (ObjectUtils.isNotEmpty(estimateSummary.code) && param.code !== estimateSummary.code) {
                // 如果修改了code  那么就要去看这个老的code是不有地方引用了他  如果有引用  那么不能修改这个code  后面产品又说改了code其他引用了code的地方也要同步变更...
                await this.inspectionCode(param.code, estimateSummary.code, codeFormulaMap);
            }
            //判断name
            if (ObjectUtils.isNotEmpty(estimateSummary.name) && param.name !== estimateSummary.name) {
                // 如果修改了name  那么就要去看这个老的name是不有地方引用了他  如果有引用  那么不能修改这个name  后面产品又说改了name其他引用了name的地方也要同步变更...
                await this.inspectionName(param.name, estimateSummary.name, codeInstructionsMap);
            }

            //判断 计算基数和费率修改
            if (param.calculateFormula !== estimateSummary.calculateFormula || (param.rate !== estimateSummary.rate && ObjectUtils.isNotEmpty(param.calculateFormula))) {
                // 如果参数传来的计算公式或者费率不一样  那么说明修改了计算公式或者费率   就需要对计算公式进行验证并计算结果
                let responseData = await this.handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap);
                if (!ObjectUtils.isEmpty(responseData)) {
                    return ResponseData.fail(responseData.message);
                }

                // param.code = code;  // 恢复备份
                // param.calculateFormula = calculateFormula; // 恢复备份
                // 把本次计算的结果存入map  留待后续计算使用
                codePriceMap.set(param.code, NumberUtil.numberScale(param.price, je));
                codeFormulaMap.set(param.code, param.calculateFormula);
                codeInstructionsMap.set(param.code, param.instructions);
                // 如果公式进行了修改  那么需要对引用了这个条公式对应的code的所有公式重新计算，并且对扩散影响的所有公式都需要重新计算
                await this.handleCodePriceChange(param.code, codePriceMap, codeFormulaMap, codeRateMap);
            } else {
                param.price = null;
                param.instructions = null;
            }
            // 根据上面这步的计算 得出有哪些数据需要更新
            for (let i = 0; i < estimateSummaryArray.length; i++) {
                let item = estimateSummaryArray[i];
                let price = codePriceMap.get(item.code);
                let formula = codeFormulaMap.get(item.code);
                let instructions = codeInstructionsMap.get(item.code);

                let updateEstimateSummary = new GsEstimateSummary();
                updateEstimateSummary.sequenceNbr = item.sequenceNbr;
                let flag = false;
                if (ObjectUtils.isNotEmpty(price) && price !== item.price) {
                    updateEstimateSummary.price = price;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(formula) && formula !== item.calculateFormula) {
                    updateEstimateSummary.calculateFormula = formula;
                    flag = true;
                } else {
                    updateEstimateSummary.price = 0;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(instructions) && instructions !== item.instructions) {
                    updateEstimateSummary.instructions = instructions;
                    flag = true;
                }
                if (flag === true) {
                    priceChangeArray.push(updateEstimateSummary);
                }
            }

        } else {
            // 新增
            let find = estimateSummaryArray.find(item => item.code === param.code);
            if (ObjectUtils.isNotEmpty(find)) {
                return ResponseData.fail('当前code已存在');
            }
            // 获取统一的map数据
            await this.getAllCodeFormulaPriceMap(constructId, estimateCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, estimateSummaryArray);
            // 处理公式校验和计算
            await this.handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeInstructionsMap);
        }

        // args.estimateSummary.code = code;  // 恢复备份
        // args.estimateSummary.calculateFormula = calculateFormula;  // 恢复备份
        if (ObjectUtils.isNotEmpty(param.code) && param.code === '※') {
            args.estimateSummary.code = copyCode;
            args.estimateSummary.name = copyName;
        }
        // 进行数据更新
        await this.setEstimateSummaryData(args, estimateSummaryArray, priceChangeArray);

        //调用计算费用汇总  todo  计算后是否保存
        let newList = await this.countEstimateSummary(constructId, estimateSummaryArray, estimateCodePriceArray);

        return ResponseData.success(newList);
    }

    /**
     * 判断 target中是否包含 arr
     * @param arr
     * @param target
     * @returns {boolean}
     */
    stringInArray(arr, target) {
        for (let item of arr) {
            if (item.includes(target)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 修改code - 修改计算基数
     * @param newCode
     * @param oldCode
     * @param codeFormulaMap
     */
    async inspectionCode(newCode, oldCode, codeFormulaMap) {
        let lowerOldCode = oldCode.toLowerCase();
        for (let [key, value] of codeFormulaMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let codeList = value.toLowerCase().split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(codeList) && this.stringInArray(codeList, lowerOldCode)) {
                    let reg = new RegExp("\\b" + lowerOldCode + "\\b", "gi")
                    let replace = value.replaceAll(reg, newCode);
                    codeFormulaMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 修改name - 修改基数说明
     * @param newName
     * @param oldName
     * @param codeInstructionsMap
     */
    async inspectionName(newName, oldName, codeInstructionsMap) {
        for (let [key, value] of codeInstructionsMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let nameList = value.toLowerCase().split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(nameList) && this.stringInArray(nameList, oldName)) {
                    let replace = value.replaceAll(oldName, newName);
                    codeInstructionsMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 获取单位工程的所有已有的费用代码基数和对应的价格
     * @param constructId
     * @param estimateCodePriceArray
     * @param currentId
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeInstructionsMap
     * @param codeRateMap
     * @param codeNameMap
     * @param estimateSummaryArray
     */
    async getAllCodeFormulaPriceMap(constructId, estimateCodePriceArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, estimateSummaryArray) {

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.ESTIMATE_COST.je;
        let freeRate = precision.ESTIMATE_COST.SUMMARY.freeRate;

        if (ObjectUtils.isNotEmpty(estimateCodePriceArray)) {
            for (let i = 0; i < estimateCodePriceArray.length; i++) {
                let unitCostCodePrice = estimateCodePriceArray[i];
                let code = unitCostCodePrice.code;
                codePriceMap.set(code, NumberUtil.numberScale(unitCostCodePrice.price, je));
                codeFormulaMap.set(code, unitCostCodePrice.code);
                codeInstructionsMap.set(code, unitCostCodePrice.name);
                codeNameMap.set(code, unitCostCodePrice.name);
            }
        }
        if (ObjectUtils.isNotEmpty(estimateSummaryArray)) {
            for (let i = 0; i < estimateSummaryArray.length; i++) {
                let estimateSummary = estimateSummaryArray[i];
                let code = estimateSummary.code;
                if (!ObjectUtils.isEmpty(currentId) && currentId === estimateSummary.sequenceNbr) {
                    continue;
                }
                codePriceMap.set(code, NumberUtil.numberScale(estimateSummary.price, je));
                if (estimateSummary.category !== EstimateSummaryCategoryConstants.JA_PROJECT_COST) {
                    if (ObjectUtils.isNotEmpty(code)) {
                        codeFormulaMap.set(code, estimateSummary.calculateFormula);
                        codeInstructionsMap.set(code, estimateSummary.instructions);
                    }
                }
                codeNameMap.set(code, estimateSummary.name);
                if (ObjectUtils.isNotEmpty(estimateSummary.rate)) {
                    codeRateMap.set(code, estimateSummary.rate);
                }
            }
        }
    }

    /**
     * 费用代码修改、计算基数修改
     * @param calculateFormula
     * @param codePriceMap
     * @returns {any}
     */
    async doCalculator(calculateFormula, codePriceMap) {
        if (ObjectUtils.isEmpty(calculateFormula)) {
            return calculateFormula;
        }
        // 分解字符串成表达式和变量名
        const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
        //存放替换后的计算公式
        let afterCalculateFormula = calculateFormula;

        // 创建一个新的 Map 来存储小写的键和对应的值
        const keysLowercase = new Map();
        // 使用 forEach 遍历原始的 Map
        codePriceMap.forEach((value, key) => {
            // 将键转换为小写
            const lowerCaseKey = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            // 将新的键值对添加到新的 Map 中
            keysLowercase.set(lowerCaseKey, value);
        });

        //替换费用代码和费用代号
        for (let variable of variablesToReplace) {
            let variableLowerCase = variable.toLowerCase(); // 转小写
            if (keysLowercase.has(variableLowerCase)) {
                afterCalculateFormula = afterCalculateFormula.replace(variable, keysLowercase.get(variableLowerCase));
            }
        }

        let flag = await this.isValidExpression(afterCalculateFormula);
        if (!flag) {
            throw new Error("表达式有误，请重新编辑！");
        }
        // 匹配表达式中的※为0，进行检验
        afterCalculateFormula = afterCalculateFormula.replaceAll("※", "0");
        return eval(afterCalculateFormula);
    }


    /**
     * 效验取费基数：A+B+V+
     * @param expression
     * @returns {boolean}
     */
    async isValidExpression(expression) {
        // 匹配表达式中的※为0，进行检验
        expression = expression.replaceAll("※", "0");
        // 匹配四则运算表达式的正则表达式
        const regex = /^[\d\+\-\*\/\(\)\.]+$/;

        // 检查表达式是否匹配正则表达式
        if (!regex.test(expression)) {
            return false;
        }

        try {
            // 使用 eval() 函数计算表达式的值
            eval(expression);
            return true;
        } catch (e) {
            // 如果表达式有语法错误，eval() 函数会抛出异常
            return false;
        }
    }

    /**
     * 获取取费说明
     * @param formula
     * @param codeNameMap
     * @returns {string|*}
     */
    async getFormulaInstructions(formula, codeNameMap) {
        if (ObjectUtils.isEmpty(formula)) {
            return formula;
        }
        // codeNameMap的key转小写
        let codeNameMapLowerCase = new Map();
        for (let [key, value] of codeNameMap) {
            let keyNew = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            codeNameMapLowerCase.set(keyNew, value);
        }
        // 把公式进行分割
        formula = formula.toLowerCase();
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (!codeList.length) {
            return "";
        }
        for (let i = 0; i < codeList.length; i++) {
            let code = codeList[i];
            let instruction = codeNameMapLowerCase.get(code);
            if (ObjectUtils.isNotEmpty(instruction) && instruction.trim()) {
                formula = formula.replace(code, instruction);
            } else {
                if (isNaN(Number(code))) { // code不是一个数字
                    formula = formula.replace(code, "");    // formula.replace(code, "") todo 解决引用有费用代号无名称，导致的基数说明错误问题   formula.replace("+" + code, "");
                } else {
                    formula = formula.replace(code, code);
                }
            }
        }
        return formula;
    }

    /**
     * 处理公式修改逻辑
     * @param constructId
     * @param param
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeNameMap
     */
    async handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap) {

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.OTHER_PROJECT_COST.je;
        let freeRate = precision.OTHER_PROJECT_COST.SUMMARY.freeRate;

        // 对本次新增或者修改进行正确性检测
        let set = new Set();
        // set.add(param.code);
        try {
            await this.doInspection(set, param.calculateFormula, codeFormulaMap, param.code);
            // 如果没有抛出异常  说明检查通过了
            let res = await this.doCalculator(param.calculateFormula, codePriceMap);
            // if (param.rate) {
            //     res = parseFloat((res * param.rate / 100).toFixed(3));
            // } else {
            //     res = parseFloat((res * 100 / 100).toFixed(3));
            // }
            if (param.rate) {
                res = NumberUtil.numberScale(parseFloat(res * param.rate / 100), freeRate);
            } else {
                res = parseFloat(res);
            }
            param.price = Math.round(res * 100) / 100;
            param.instructions = await this.getFormulaInstructions(param.calculateFormula, codeNameMap);
        } catch (e) {
            return ResponseData.fail(e.message);
        }
    }

    /**
     * 获取所有引用code
     * @param calculateFormula
     * @param codeFormulaMap
     * @param set
     */
    async getCodes(calculateFormula, codeFormulaMap, set) {
        if (codeFormulaMap.has(calculateFormula) && ObjectUtils.isNotEmpty(codeFormulaMap.get(calculateFormula))) {
            // 获取公式calculateFormula中的code
            let codeList = codeFormulaMap.get(calculateFormula).replace(/[+\-*/]/g, ',').split(',');
            // let codeList = codeFormulaMap.get(calculateFormula).match(/[A-Za-z][A-Za-z0-9]*/g);
            if (ObjectUtils.isNotEmpty(codeList)) {
                if (codeList.length === 1 && codeList[0] === calculateFormula) {
                    return;
                }
                for (let i = 0; i < codeList.length; i++) {
                    let code = codeList[i];
                    if (code !== "※") {
                        set.add(code);
                    }
                    await this.getCodes(code, codeFormulaMap, set);
                }
            }
        }
    }

    /**
     * 对公式的正确性进行检查 检查是否有错误引用或者循环引用
     * ps：
     * 这个有个简单的做法  就是从修改的公式code开始  把每一层的每一个元素都分解到一个二叉树里面
     * 如果这个二叉树的任何一条从顶层到底层的分支中出现重复的元素  那就说明这个公式存在循环引用   但是这样做的话错误提示不明确
     * @param codes
     * @param formula
     * @param codeFormulaMap
     * @param code
     */
    async doInspection(codes, formula, codeFormulaMap, code) {

        let formulaLowerCase = ObjectUtils.isNotEmpty(formula) ? formula.toLowerCase() : formula;
        let codeLowerCase = ObjectUtils.isNotEmpty(code) ? code.toLowerCase() : code;

        if (ObjectUtils.isEmpty(formula)) {
            return;
        }
        if (formulaLowerCase === codeLowerCase) {
            throw new Error("公式存在循环引用，请检查并修改");
        }

        // 创建一个新的 Map 来存储小写的键和对应的值
        const codeFormulaMapLowercase = new Map();
        // 使用 forEach 遍历原始的 Map
        codeFormulaMap.forEach((value, key) => {
            // 将键转换为小写
            const lowerCaseKey = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            const lowerCaseValue = ObjectUtils.isNotEmpty(value) ? value.toLowerCase() : value;
            // 将新的键值对添加到新的 Map 中
            codeFormulaMapLowercase.set(lowerCaseKey, lowerCaseValue);
        });

        // 获取应用取费基数下所有的子code
        await this.getCodes(formulaLowerCase, codeFormulaMapLowercase, codes);
        if (codes.has(codeLowerCase)) {
            throw new Error("公式存在循环引用，请检查并修改");
        }
        // 根据 加减乘除 分割计算公式
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (codeList.length === 0) {
            throw new Error("运算公式格式错误，请检查并修改");
        }
        // if (codeList.length === 1) {
        //     if (codeList[0] === code) {
        //         throw new Error("公式存在循环引用，请检查并修改");
        //     }
        //     let codeFormula = codeFormulaMap.get(codeList[0].replace(/\s/g, ''));
        //     if (!codeFormula) {  //ObjectUtils.isNotEmpty(codeFormula)
        //         // 在map里面没找到  那就是引用了一个不存在的基数code
        //         //判断是否为数字
        //         if (isNaN(Number(codeList[0]))) {
        //             throw new Error("公式存在未知引用，请检查并修改");
        //         }
        //     }
        //     return;
        // }
        // for (let i = 0; i < codeList.length; i++) {
        //     const c = codeList[i];
        //     if (codes.has(c)) {
        //         // 说明是自己的公式引用了自己
        //         throw new Error('公式存在循环引用，请检查并修改');
        //     }
        //     let codeFormula = codeFormulaMap.get(c.replace(/\s/g, ''));
        //     if (!codeFormula) {
        //         if (ObjectUtils.isEmpty(codeFormula)) {  // E默认没有计算基数，会导致引用E的时候报错
        //             codeFormula = 0;
        //         } else {
        //             // 在map里面没找到  那就是引用了一个不存在的基数code
        //             if (isNaN(Number(c))) {
        //                 throw new Error("公式存在未知引用，请检查并修改");
        //             } else {
        //                 codeFormula = c;
        //             }
        //             let newCodes = new Set(codes);
        //             if (c !== codeFormula) {
        //                 newCodes.add(c);
        //             }
        //             this.doInspection(newCodes, codeFormula, codeFormulaMap, code);
        //         }
        //     }
        // }
    }


    /**
     * 处理价格变化
     * @param code
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeRateMap
     */
    async handleCodePriceChange(code, codePriceMap, codeFormulaMap, codeRateMap) {
        for (let [key, value] of codeFormulaMap.entries()) {
            if (ObjectUtils.isNotEmpty(value) && ObjectUtils.isNotEmpty(value) && isNaN(Number(value))) {
                // 对公式进行分解
                let codeList = value.split(/[\+\-\*\/\(\)]+/);
                if (codeList.length > 1) {
                    if (codeList.includes(code)) {
                        let res = await this.doCalculator(value, codePriceMap);
                        let rate = codeRateMap.get(key);
                        if (rate) {
                            res = parseFloat((res * rate / 100));
                            codePriceMap.set(key, Math.round(res * 100) / 100)
                        } else {
                            codePriceMap.set(key, res);
                        }
                        await this.handleCodePriceChange(key, codePriceMap, codeFormulaMap, codeRateMap);
                    }
                }
            }
        }
    }

    /**
     * 进行数据更新
     * @param args
     * @param estimateSummaryArray
     * @param priceChangeArray
     */
    async setEstimateSummaryData(args, estimateSummaryArray, priceChangeArray) {
        //新增的数据
        let newEstimateSummary = args.estimateSummary;
        if (ObjectUtils.isEmpty(newEstimateSummary.sequenceNbr)) {
            // //新增
            // newEstimateSummary.sequenceNbr = Snowflake.nextId();
            // for (let i = estimateSummaryArray.length - 1; i >= 0; i--) {
            //     const item = estimateSummaryArray[i];
            //     if (item.sortNum >= newEstimateSummary.sortNum) {
            //         item.sortNum += 1;
            //     }
            // }
            estimateSummaryArray.push(newEstimateSummary);
        } else {
            //修改
            for (let i = 0; i < estimateSummaryArray.length; i++) {
                let element = estimateSummaryArray[i];
                if (element.sequenceNbr === newEstimateSummary.sequenceNbr) {
                    estimateSummaryArray[i] = newEstimateSummary;
                }
            }
        }
        //更新修改后的金额
        if (ObjectUtils.isNotEmpty(priceChangeArray)) {
            for (let i = 0; i < priceChangeArray.length; i++) {
                let item = priceChangeArray[i];
                for (let j = 0; j < estimateSummaryArray.length; j++) {
                    let element = estimateSummaryArray[j];
                    if (element.sequenceNbr === item.sequenceNbr) {
                        if (!ObjectUtils.isEmpty(item.price) && item.category !== EstimateSummaryCategoryConstants.JS_LOAN_INTEREST) {
                            estimateSummaryArray[j].price = item.price;
                        }
                        if (!ObjectUtils.isEmpty(item.calculateFormula)) {
                            estimateSummaryArray[j].calculateFormula = item.calculateFormula;
                        }
                        if (!ObjectUtils.isEmpty(item.instructions)) {
                            estimateSummaryArray[j].instructions = item.instructions;
                        }
                    }
                }
            }
        }
    }

    /**
     * 导入概算汇总
     * @param args
     * @returns {ResponseData}
     */
    async importEstimateSummary(args) {
        let constructId = args.projectId;

        // 获取概算汇总模版存放路径  D:\IdeaProjects\gaiSuan\pricing-cs\build\extraResources\excelTemplate\gs\概算汇总
        const gshzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\概算汇总';
        let options = {
            properties: ['openFile'],
            defaultPath: gshzTemplatePath, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_GSHZ]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            return ResponseData.fail('未选中任何文件');
        }

        // 建设其他费文件的路径
        const gsFilePath = result[0];
        try {
            const data = fs.readFileSync(gsFilePath, 'utf8');
            // 使用JSON.parse()方法将JSON字符串转换为JavaScript数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段
            const estimateSummaryList = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.calculateFormula = line.calculateFormula;
                obj.instructions = line.instructions;
                obj.category = line.category;
                obj.rate = line.rate;
                // obj.jzFee = line.jzFee;
                // obj.azFee = line.azFee;
                // obj.sbgzFee = line.sbgzFee;
                // obj.qtFee = line.qtFee;
                // obj.price = line.price;
                // obj.unit = line.unit;
                // obj.average = line.average;
                // obj.unitCost = line.unitCost;
                // obj.proportion = line.proportion;
                obj.permission = line.permission;
                obj.ifProject = line.ifProject;
                obj.remark = line.remark;
                obj.whetherPrint = line.whetherPrint;
                obj.sequenceNbr = line.sequenceNbr;
                obj.parentId = line.parentId;
                estimateSummaryList.push(obj);
            });

            // 重新获取项目结构
            let estimateSummaryArray = this.setProjectStructure(constructId, estimateSummaryList);

            //  因为建安工程费、建设其他费，更新计算概算费用代码
            let estimateCodeArray = await this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
                constructId: constructId
            });
            // 调用计算概算汇总
            let estimateSummarys = await this.countEstimateSummary(constructId, estimateSummaryArray, estimateCodeArray);

            // // 转树
            // let arrayTree = xeUtils.toArrayTree(estimateSummarys, {
            //     key: 'sequenceNbr',
            //     parentKey: 'parentId',
            // });
            //
            // // this.addLevelNumbers(arrayTree, '1');
            // let newList = xeUtils.toTreeArray(arrayTree);
            // newList.forEach(item => {
            //     item.children = [];
            // });
            // // 更新概算汇总
            // ProjectDomain.getDomain(projectId).functionDataMap.set(FunctionTypeConstants.PROJECT_GS_SUMMARY, newList);
            return ResponseData.success(estimateSummarys);
        } catch (err) {
            return ResponseData.fail('导入失败');
        }
    }

    /**
     * 导出概算汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportEstimateSummary(args) {
        let constructId = args.projectId;
        // 获取工程项目的概算汇总
        let estimateSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_SUMMARY);

        // 指定导出的列名  "jzFee", "azFee", "sbgzFee", "qtFee", "price", "unit", "average", "unitCost", "proportion",
        const columns = ["dispNo", "code", "name", "calculateFormula", "instructions", "category", "rate",
            "permission", "ifProject", "remark", "whetherPrint", "sequenceNbr", "parentId"];

        // 根据指定的列名来重组数据，确保导出的JSON只包含这些列
        const formattedData = estimateSummarys.map(item => {
            return columns.reduce((acc, col) => {
                acc[col] = item[col];
                return acc;
            }, {});
        });
        // 将数组转换为JSON字符串   const jsonData = JSON.stringify(formattedData, null, 2);
        const jsonData = JSON.stringify(formattedData); // 第三个参数是缩进量，使输出更易读

        // 存放概算汇总文件的路径
        const gshzTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\概算汇总';
        const count = this.countDirectories(gshzTemplatePath);
        let options = {
            title: '保存文件',
            defaultPath: gshzTemplatePath + '\\概算汇总模板' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_GSHZ]} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(FileOperatorType.File_TYPE_GSHZ)) {
                filePath += FileOperatorType.File_TYPE_GSHZ;
            }
            // 写入文件
            fs.writeFile(filePath, jsonData, (err) => {
                if (err) {
                    ResponseData.fail('写入文件时发生错误');
                } else {
                    ResponseData.success('数据已成功导出');
                }
            });
            return ResponseData.success(filePath);
        }
    }

    /**
     * 获取当前文件夹路径下文件个数
     * @param dirPath
     * @returns {number}
     */
    countDirectories(dirPath) {
        let count = 1;
        let numbers = [];
        fs.readdirSync(dirPath).forEach((item) => {
            if (item.match(/\d+/g) !== null) {
                numbers.push(item.match(/\d+/g)[0]);
            }
        });
        if (ObjectUtils.isNotEmpty(numbers)) {
            count = Math.max(...numbers) + 1;
        }
        return count;
    }

    /**
     * 获取指定的节点
     * @param nodeList
     * @param sequenceNbr
     * @returns {null|*}
     */
    getNodeById(nodeList, sequenceNbr) {
        if (ObjectUtils.isNotEmpty(nodeList)) {
            for (let i = 0; i < nodeList.length; i++) {
                if (nodeList[i].sequenceNbr === sequenceNbr) {
                    return nodeList[i];
                } else {
                    this.getNodeById(nodeList[i].children, sequenceNbr);
                }
            }
        }
        return null;
    }

    /**
     * 上移下移概算汇总
     * @param args
     * @returns {*}
     */
    async moveUpAndDownEstimateSummary(args) {
        let {constructId, sequenceNbrArray, moveType} = args;

        // 获取概算汇总，并转化成树结构
        let estimateSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_SUMMARY);

        let arrayTree = xeUtils.toArrayTree(estimateSummarys, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });

        sequenceNbrArray.forEach(sequenceNbr => {
            // 获取选中行
            let estimateSummary = estimateSummarys.find(item => item.sequenceNbr === sequenceNbr);

            if (ObjectUtils.isNotEmpty(estimateSummary.parentId)) {
                // 获取当前选定行的父节点，获取他的子集
                let estimateSummaryParentNode = this.getNodeById(arrayTree, estimateSummary.parentId);
                if (ObjectUtils.isNotEmpty(estimateSummaryParentNode) && ObjectUtils.isNotEmpty(estimateSummaryParentNode.children)) {
                    // 将指定行，在集合中向上/向下移一个位置
                    // 遍历建设其他费，找到点击行
                    let estimateSummaryNode = estimateSummaryParentNode.children.find(item => item.sequenceNbr === sequenceNbr);
                    if (moveType === "up") {
                        this.moveItemUp(estimateSummaryParentNode.children, estimateSummaryParentNode.children.indexOf(estimateSummaryNode));
                    } else if (moveType === "down") {
                        this.moveItemDown(estimateSummaryParentNode.children, estimateSummaryParentNode.children.indexOf(estimateSummaryNode));
                    }
                }

            } else {
                // 遍历建设其他费，找到点击行
                let estimateSummaryNode = arrayTree.find(item => item.sequenceNbr === sequenceNbr);
                if (moveType === "up") {
                    this.moveItemUp(arrayTree, arrayTree.indexOf(estimateSummaryNode));
                } else if (moveType === "down") {
                    this.moveItemDown(arrayTree, arrayTree.indexOf(estimateSummaryNode));
                }
            }
        });
        // 平铺概算汇总列表数据，并返回
        let newList = xeUtils.toTreeArray(arrayTree);

        // 更新概算汇总
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_GS_SUMMARY, newList);
        return ResponseData.success(newList);
    }


    /**
     * 向上移位
     * @param array
     * @param index
     */
    moveItemUp(array, index) {
        // 检查索引是否大于0，因为不能移动第一个元素到更前面去
        if (index > 0) {
            // 保存要移动的元素
            let item = array.splice(index, 1)[0];
            // 在当前位置之前插入元素
            array.splice(index - 1, 0, item);
        }
    }


    /**
     * 向下移位
     * @param array
     * @param index
     */
    moveItemDown(array, index) {
        // 检查index是否在数组的有效范围内并且不是最后一个元素
        if (index >= 0 && index < array.length - 1) {
            // 使用splice取出要移动的元素
            const element = array.splice(index, 1)[0];
            // 将取出的元素插入到其下方的位置
            array.splice(index + 1, 0, element);
        }
        // return array;
    }

    /**
     * 添加序号列
     * @param trees
     * @returns {*}
     */
    addChildLevelNumbers(trees) {
        let sort = 1;
        return trees.map(tree => {
            this.addLevelNumbers(tree, (sort).toString());
            sort++;
            return tree;
        });
    }

    /**
     * 添加序号列
     * @param node
     * @param prefix
     */
    addLevelNumbers(node, prefix = '') {
        // 当前节点的编号为当前prefix
        node.sortIndex = prefix;

        if (node.children) {
            // 遍历当前节点的所有子节点
            for (let i = 0; i < node.children.length; i++) {
                // 为子节点生成新的编号，基于当前节点的编号
                const childPrefix = `${prefix}${prefix ? '.' : ''}${i + 1}`;
                this.addLevelNumbers(node.children[i], childPrefix);
            }
        }
    }

}

GsEstimateSummaryService.toString = () => '[class GsEstimateSummaryService]';
module.exports = GsEstimateSummaryService;