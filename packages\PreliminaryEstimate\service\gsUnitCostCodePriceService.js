const WildcardMap = require('../core/container/WildcardMap');
const {Service} = require("../../../core");
const gsFydm = require("../jsonData/gs_fydm.json");
const {Snowflake} = require("../utils/Snowflake");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const CostCodeTypeEnum = require("../enums/CostCodeTypeEnum");
const ResourceKindConstants = require("../constants/ResourceKindConstants");
const {NumberUtil} = require("../utils/NumberUtil");
const {GsUnitCostCodePrice} = require("../models/GsUnitCostCodePrice");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {getConnection, getRepository, getManager} = require('typeorm');
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const DeTypeConstants = require("../constants/DeTypeConstants");
const AnZhuangJiQqConstants = require("../constants/AnZhuangJiQqConstants");
const {GsConstructProjectRcj} = require('../models/GsConstructProjectRcj');


class GsUnitCostCodePriceService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取默认费用代码
     * @returns {any[]}
     */
    defaultUnitCostCodePrice(args) {
        const {constructId, singleId, unitId, constructMajorType} = args;

        let unitCostCodeArray = [];
        for (let i in gsFydm) {
            let obj = new GsUnitCostCodePrice();
            ConvertUtil.setDstBySrc(gsFydm[i], obj)
            obj.price = 0;
            obj.sequenceNbr = Snowflake.nextId();
            unitCostCodeArray.push(obj);
        }
        return unitCostCodeArray;
    }

    /**
     * 获取单位费用代码
     * @param args
     * @returns {*}
     */
    async getUnitCostCodePrice(args) {
        const {constructId, singleId, unitId, constructMajorType} = args;
        return ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
            .get(unitId + FunctionTypeConstants.SEPARATOR + constructMajorType);
    }

    /**
     * 费用代码明细左侧目录树  注：因为类型用汉字表示了，该代码去重让前端写了
     * @returns {unknown[] | any[]}
     */
    costCodeTypeList() {
        return Object.values(CostCodeTypeEnum);
    }

    /**
     * 根据费用代码类型获取费用代码
     * @param args
     * @returns {ResponseData}
     */
    async costCodePrice(args) {
        const {constructId, singleId, unitId, constructMajorType, type} = args;
        let unitCostCodePrice = await this.getUnitCostCodePrice(args);
        if (args.type) {
            return unitCostCodePrice?.filter(item => item.type === args.type);
        } else {
            return unitCostCodePrice;
        }
    }


    /**
     * 计算当前工程专业的费用代码和更新费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async countCostCodePrice(args) {
        const {constructId, singleId, unitId, constructMajorType} = args;

        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let budgetBookTypes, independentCostTypes, yssAllRcjs = [], rcjAllList = [], allRcjList = [];

        // 1. 预算书  获取预算书定额的所有取费专业类型
        let yssDes = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId && ObjectUtils.isNotEmpty(item.costFileCode));
        if (ObjectUtils.isNotEmpty(yssDes)) {
            budgetBookTypes = yssDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        }

        // 2. 独立费的所有取费专业类型
        let independentCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            .get(this.service.PreliminaryEstimate.gsIndependentCostsService.getDataMapKey(unitId))
            ?.filter(item => item.isRcj !== true);
        if (ObjectUtils.isNotEmpty(independentCosts)) {
            independentCostTypes = independentCosts.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costMajorCode)).map(item => item.costMajorCode);
        }

        const yssDeMap = new Map(yssDes.map(item => [item.sequenceNbr, item]));
        // 3. 获取当前单位所有的人材机
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let rcjAllList1 = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        // 封装定额Map
        const budgetBookDeMap = new Map(yssDes.map(item => [item.deRowId, item]));
        // 给定额下挂的人材机添加工程专业
        for (let i = 0; i < rcjAllList1.length; i++) {
            let rcj = rcjAllList1[i];
            // // 7.1 获取预算书人材机
            // if (yssDeMap.has(rcj.deRowId)) {
            //     let yssDe = yssDeMap.get(rcj.deRowId);
            //     if (ObjectUtils.isNotEmpty(yssDe)) {
            //         rcj.costFileCode = yssDe.costFileCode;
            //         yssAllRcjs.push(rcj);
            //     }
            // }
            let de = budgetBookDeMap.get(rcj.deRowId);
            if (ObjectUtils.isNotEmpty(de)) {
                rcj.costFileCode = de.costFileCode;
            }
            // 安装费用全局、单独计取
            if (de.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtils.isNotEmpty(de.calculateMethod) && de.calculateMethod === AnZhuangJiQqConstants.AnZhuangJiQq_ALl) {

            } else {
                rcjAllList.push(rcj);
            }
        }

        rcjAllList.forEach(item => {
            if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs) && item.markSum === RcjCommonConstants.MARKSUM_JX) {
                // item.pbs.forEach(item2 => allRcjList.push(item2));
                item.pbs.forEach(item2 => {
                    item2.costFileCode = item.costFileCode;
                    allRcjList.push(item2);
                })
            } else {
                allRcjList.push(item)
            }
        });

        // 4. 获取该单位所有的专业：专业名称数组判空且合并去重，没有数据则返回
        let nonEmptyLists = [budgetBookTypes, independentCostTypes].filter(arr => ObjectUtils.isNotEmpty(arr));
        let constructMajorTypeSet = [...new Set(nonEmptyLists.flat())];

        // 获取单位工程
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        console.log("当前单位工程专业====>" + unitProject.constructMajorType);

        let yssDeList = [], yssRcjList = [], rcjList, independentCostList, unitCostCodePrices, majorName;
        let constructMajorTypeList = [...constructMajorTypeSet];

        // 是单专业汇总
        if (unitProject.isSingleMajorFlag) {
            console.log("单专业取费。。。。" + constructMajorTypeList[0]);
            majorName = unitProject.qfMajorType;

            yssDeList = yssDes;
            yssRcjList = yssAllRcjs;
            rcjList = allRcjList;
            if (ObjectUtils.isNotEmpty(independentCosts)) {
                independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode));
            }
            //费用代码
            unitCostCodePrices = businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).get(unitId + FunctionTypeConstants.SEPARATOR + majorName);

            // 计算费用代码条目
            await this.countCostCodePriceItems(yssDeList, rcjList, independentCostList, unitCostCodePrices, constructId, unitId);
            // 更新费用代码
            businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePrices);
            // 获取费用汇总
            let unitCostCodeSummarys = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).get(unitId + FunctionTypeConstants.SEPARATOR + majorName);
            //更新费用汇总
            await this.service.PreliminaryEstimate.gsUnitCostSummaryService.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostCodeSummarys, majorName);

        } else {
            console.log("当前单位取费主专业====>" + unitProject.constructMajorType);
            let qfMajorTypeMoneyMap = new Map();
            // 修改单位的取费专业汇总
            constructMajorTypeList.forEach(majorName => {
                qfMajorTypeMoneyMap.set(majorName, 0);
            });
            unitProject.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
            ProjectDomain.getDomain(constructId).updateProject(unitProject);

            // 删除原来的费用代码
            let codeMap = businessMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            let newCodeMap = new Map();
            for (let [key, value] of codeMap.entries()) {
                if (!key.includes(unitId)) {
                    newCodeMap.set(key, value);
                }
            }
            // 对专业类型进行费用代码，并对费用汇总填写数据
            businessMap.set(FunctionTypeConstants.UNIT_COST_CODE, newCodeMap);

            // 删除原来的费用汇总
            let codeSummaryMap = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
            let newCodeSummaryMap = new Map();
            // for (let [key, value] of codeSummaryMap.entries()) {
            //     if (!key.includes(unitId) || (key.includes(constructMajorType) && key.includes(unitId))) {
            //         newCodeSummaryMap.set(key, value);
            //     }
            // }
            // businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);
            if (constructMajorTypeList.length === 1) {
                console.log("多专业汇总，单专业时。。。。" + constructMajorTypeList[0]);

                yssDeList = yssDes;
                yssRcjList = yssAllRcjs;
                rcjList = allRcjList;
                if (ObjectUtils.isNotEmpty(independentCosts)) {
                    independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode));
                }

                // 为空是，是多专业汇总按钮进来的
                if (ObjectUtils.isEmpty(constructMajorType) || !constructMajorTypeList.includes(constructMajorType)) {
                    majorName = constructMajorTypeList[0];
                    // 进行多专业汇总之后进来的
                } else {
                    majorName = constructMajorType;
                }

                //费用代码
                unitCostCodePrices = await this.defaultUnitCostCodePrice({
                    constructId: constructId,
                    singleId: singleId,
                    unitId: unitId,
                    constructMajorType: majorName
                });

                for (let [key, value] of codeSummaryMap.entries()) {
                    if (!key.includes(unitId) || (key.includes(majorName) && key.includes(unitId))) {
                        newCodeSummaryMap.set(key, value);
                    }
                }
                businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);
                // 获取费用汇总
                let unitCostSummarys = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).get(unitId + FunctionTypeConstants.SEPARATOR + majorName);
                if(ObjectUtils.isEmpty(unitCostSummarys)){
                    // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
                    unitCostSummarys = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.defaultUnitCostSummary({
                        constructId: constructId,
                        singleId: singleId,
                        unitId: unitId,
                        constructMajorType: majorName
                    });
                }
                // 更新或添加费用汇总
                businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostSummarys);

                // 计算费用代码条目
                await this.countCostCodePriceItems(yssDeList, rcjList, independentCostList, unitCostCodePrices, constructId, unitId);
                // 更新费用代码
                businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePrices);
                //更新费用汇总
                await this.service.PreliminaryEstimate.gsUnitCostSummaryService.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummarys, majorName);

            } else {
                console.log("多专业汇总，多专业时。。。。" + constructMajorTypeList);

                for (const majorName of constructMajorTypeList) {
                    // 预算书定额分类
                    if (ObjectUtils.isNotEmpty(yssDes)) {
                        yssDeList = yssDes.filter(item => item.costFileCode === majorName);
                    }

                    // 预算书人材机分类
                    if (ObjectUtils.isNotEmpty(yssAllRcjs)) {
                        yssRcjList = yssAllRcjs.filter(item => item.costFileCode === majorName);
                    }

                    // 人材机分类
                    if (ObjectUtils.isNotEmpty(allRcjList)) {
                        rcjList = allRcjList.filter(item => item.costFileCode === majorName);
                    }

                    // 独立费分类
                    if (ObjectUtils.isNotEmpty(independentCosts)) {
                        independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.costMajorCode === majorName && item.levelType === 1);
                    }

                    // 对专业类型进行初始化费用代码
                    unitCostCodePrices = await this.defaultUnitCostCodePrice({
                        constructId: constructId,
                        singleId: singleId,
                        unitId: unitId,
                        constructMajorType: majorName
                    });

                    for (let [key, value] of codeSummaryMap.entries()) {
                        if (!key.includes(unitId) || (key.includes(majorName) && key.includes(unitId))) {
                            newCodeSummaryMap.set(key, value);
                        }
                    }
                    businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);
                    // 获取费用汇总
                    let unitCostSummarys = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).get(unitId + FunctionTypeConstants.SEPARATOR + majorName);
                    if(ObjectUtils.isEmpty(unitCostSummarys)){
                        // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
                        unitCostSummarys = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.defaultUnitCostSummary({
                            constructId: constructId,
                            singleId: singleId,
                            unitId: unitId,
                            constructMajorType: majorName
                        });
                    }
                    // 更新或添加费用汇总
                    businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostSummarys);

                    // 计算费用代码条目
                    await this.countCostCodePriceItems(yssDeList, rcjList, independentCostList, unitCostCodePrices, constructId, unitId);
                    // 更新费用代码
                    businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePrices);

                    //更新费用汇总
                    await this.service.PreliminaryEstimate.gsUnitCostSummaryService.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummarys, majorName);

                }
            }
        }
        return unitCostCodePrices;
    }

    processDonorMaterialNumber(rcjs) {
        let result = [];
        //分组
        const grouped = rcjs.reduce((accumulator, currentValue) => {
            let tempcol = currentValue.materialCode
                .concat(currentValue.materialName)
                .concat(currentValue.kind)
                .concat(!ObjectUtils.isEmpty(currentValue.specification) ? currentValue.specification : '').concat(currentValue.unit).concat(currentValue.dePrice);
            // 将分组作为对象的 key，相同分组的项放入同一个数组
            (accumulator[tempcol] = accumulator[tempcol] || []).push(currentValue);
            return accumulator;
        }, {});
        //循环分组之后的人材机
        for (let group in grouped) {
            if (grouped.hasOwnProperty(group)) {
                let number = 0;
                grouped[group].forEach(item => {
                    number = NumberUtil.add(number, item.totalNumber);
                });
                grouped[group].forEach(item => {
                    if ((ObjectUtils.isEmpty(item.updateFalg) ? 0 : item.updateFalg) === 0 && item.ifDonorMaterial != RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
                        item.donorMaterialNumber = NumberUtil.numberScale(number, 4);
                    }
                    let constructProjectRcj = new GsConstructProjectRcj();
                    ConvertUtil.setDstBySrc(item, constructProjectRcj);
                    result.push(constructProjectRcj);
                });

            }
        }
        return result;
    }

    /**
     * 计算费用代码条目
     * @param yssDeList  预算书定额
     * @param rcjList   人材机
     * @param independentCostList  独立费
     * @param unitCostCodePrices  费用代码
     * @param constructId
     * @param unitId
     */
    async countCostCodePriceItems(yssDeList, rcjList, independentCostList, unitCostCodePrices, constructId, unitId) {

        //人工人材机
        let rgRcj = rcjList.filter(item => item.kind == 1);
        //材料人材机
        let clRcj = rcjList.filter(item => (item.kind == 2 || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10));
        //机械人材机
        let jxRcj = rcjList.filter(item => item.kind == 3);
        //主材人材机
        let zcRcj = rcjList.filter(item => item.kind == 5);
        //设备人材机
        let sbRcj = rcjList.filter(item => item.kind == 4);
        // 工日人工人材机
        let grRcj = rcjList.filter(item => item.kind == 1 && item.unit == '工日');

        //甲供人工人材机
        let jgRgRcj
        if (ObjectUtils.isNotEmpty(rgRcj)) {
            let jgRcjTemp = rgRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgRgRcj = Array.from(map.values());
            // jgRgRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }
        //甲供材料人材机
        let jgClRcj
        if (ObjectUtils.isNotEmpty(clRcj)) {
            let jgRcjTemp = clRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgClRcj = Array.from(map.values());
            // jgClRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }
        //甲供机械人材机
        let jgJxRcj
        if (ObjectUtils.isNotEmpty(jxRcj)) {
            let jgRcjTemp = jxRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgJxRcj = Array.from(map.values());
            // jgJxRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }
        //甲供主材人材机
        let jgZcRcj
        if (ObjectUtils.isNotEmpty(zcRcj)) {
            let jgRcjTemp = zcRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgZcRcj = Array.from(map.values());
            // jgZcRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }
        //甲供设备人材机
        let jgSbRcj
        if (ObjectUtils.isNotEmpty(sbRcj)) {
            let jgRcjTemp = sbRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgSbRcj = Array.from(map.values());
            // jgSbRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }

        // 价格
        let ysZjf = 0, ysRgf = 0, ysClf = 0, ysJxf = 0, ysZcf = 0, ysSbf = 0;
        // 价差
        let rcjjc = 0, rgjc = 0, cljc = 0, jxjc = 0, zcjc = 0, sbjc = 0;
        //甲供
        let jgrgf = 0, jgclf = 0, jgjxf = 0, jgzcf = 0, jgsbf = 0;

        // 建筑面积(m、㎡)
        let gcgm = ProjectDomain.getDomain(constructId).getProjectById(unitId).average;

        // 小数点精度
        let precision = this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let jzgm = precision.COST_ANALYSIS.jzgm;
        let totalNumber = precision.RCJ_COLLECT.totalNumber;
        let marketPrice = precision.RCJ_COLLECT.marketPrice;  // 不含税市场价
        let dePrice = precision.RCJ_COLLECT.dePrice;  // 定额价
        let donorMaterialPrice = precision.COST_CODE.donorMaterialPrice;  // 甲供价格
        let donorMaterialNumber = precision.COST_CODE.donorMaterialNumber;  // 甲供价格
        let je = precision.RCJ_COLLECT.je;  // 价格


        if (ObjectUtils.isNotEmpty(unitCostCodePrices)) {
            for (let i = 0; i < unitCostCodePrices.length; i++) {
                let unitCostCodePrice = unitCostCodePrices[i];
                switch (unitCostCodePrice.code) {
                    // ∑预算书下（最父级定额层级的 人工费合价+材料费合价+机械费合价）
                    case 'ZJF': {
                        // ∑预算书下 最父级定额层级的 人工费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            ysRgf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                            }, 0));
                            // ∑预算书下 最父级定额层级的 材料费合价
                            ysClf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                            }, 0));
                            // ∑预算书下 最父级定额层级的 机械费合价
                            ysJxf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                            }, 0));
                            // ∑预算书下 最父级定额层级的 主材费合价
                            ysZcf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.zTotalSum);
                            }, 0));
                            // ∑预算书下 最父级定额层级的 设备费合价
                            ysSbf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.sTotalSum);
                            }, 0));
                        }
                        ysZjf = NumberUtil.addParams(ysRgf, ysClf, ysJxf, ysZcf, ysSbf);
                        if (!ObjectUtils.isEmpty(ysZjf)) {
                            unitCostCodePrice.price = ysZjf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RGF': {
                        // ∑预算书下 最父级定额层级的 人工费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            ysRgf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                            }, 0));
                            unitCostCodePrice.price = ysRgf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'CLF': {
                        // ∑预算书下 最父级定额层级的 材料费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            ysClf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                            }, 0));
                            unitCostCodePrice.price = ysClf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JXF': {
                        // ∑预算书下 最父级定额层级的 机械费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            ysJxf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                            }, 0));
                            unitCostCodePrice.price = ysJxf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZCF': {
                        // ∑预算书下 最父级定额层级的 主材费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            ysZcf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.zTotalSum);
                            }, 0));
                            unitCostCodePrice.price = ysZcf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'SBF': {
                        // ∑预算书下 最父级定额层级的 设备费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            ysSbf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.sTotalSum);
                            }, 0));
                            unitCostCodePrice.price = ysSbf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'GR': {
                        // 工日合计  ∑预算书下编辑区及人材机明细区类型=“人工”且单位=“工日”的数量
                        let deGr, rcjGr;
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            let yssDesGr = yssDeList.filter(item => item.kind === 1 && item.unit === '工日');
                            deGr = yssDesGr.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0);
                        }
                        if (!ObjectUtils.isEmpty(rcjList)) {
                            let yssRcjsGr = rcjList.filter(item => item.kind === 1 && item.unit === '工日');
                            rcjGr = yssRcjsGr.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0);
                        }
                        if (!ObjectUtils.isEmpty(rcjList)) {
                            unitCostCodePrice.price = NumberUtil.numberScale(NumberUtil.addParams(deGr, rcjGr), totalNumber);
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RGYSJ': {
                        // ∑预算书下 按定额价计算的 最父级定额层级的 人工费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'CLYSJ': {
                        // ∑预算书下 按定额价计算的 最父级定额层级的 材料费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JXYSJ': {
                        // ∑预算书下 按定额价计算的 最父级定额层级的 机械费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'SBYSJ': {
                        // ∑预算书下 按定额价计算的 最父级定额层级的 设备费合价  todo
                        if (!ObjectUtils.isEmpty(sbRcj)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.sdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZCYSJ': {
                        // ∑预算书下 按定额价计算的 最父级定额层级的 主材费合价  todo
                        if (!ObjectUtils.isEmpty(sbRcj)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.zdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'DLF': {
                        // 独立费	∑独立费下费用合计
                        if (ObjectUtils.isNotEmpty(independentCostList)) {
                            unitCostCodePrice.price = Number(independentCostList.reduce((accumulator, independentCost) => {
                                return NumberUtil.add(accumulator, independentCost.totalPrice);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RCJJC': {
                        // 人材机价差  ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“市场价”- ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“定额价”
                        if (!ObjectUtils.isEmpty(rgRcj)) {
                            rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                        }
                        // ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“定额价”
                        if (!ObjectUtils.isEmpty(clRcj)) {
                            cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                        }
                        // ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                        if (!ObjectUtils.isEmpty(jxRcj)) {
                            jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                            unitCostCodePrice.price = jxjc;
                        }
                        rcjjc = NumberUtil.addParams(rgjc, cljc, jxjc);
                        if (!ObjectUtils.isEmpty(rcjjc)) {
                            unitCostCodePrice.price = rcjjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RGJC': {
                        // 人工价差  ∑预算书下编辑区及人材机明细区类型=“人工”的“数量”*“市场价”（编辑区为“工程量”*“单价”）-∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“定额价”
                        if (!ObjectUtils.isEmpty(rgRcj)) {
                            rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                            unitCostCodePrice.price = rgjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'CLJC': {
                        // 材料价差  ∑预算书下编辑区及人材机明细区类型为="材料、商砼、砼、浆、商浆、配比"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(clRcj)) {
                            cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                            unitCostCodePrice.price = cljc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JXJC': {
                        // 机械价差	∑预算书下编辑区及人材机明细区类型为="机械"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(jxRcj)) {
                            jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                            unitCostCodePrice.price = jxjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'SBJC': {
                        // 设备价差	∑预算书下编辑区及人材机明细区类型为="设备"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(sbRcj)) {
                            sbjc = Number(sbRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                            unitCostCodePrice.price = sbjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZCJC': {
                        // 主材价差	∑预算书下编辑区及人材机明细区类型为="主材"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(zcRcj)) {
                            zcjc = Number(zcRcj.reduce((accumulator, constructProjectRcj) => {
                                let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.dePrice, dePrice));
                                return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                            }, 0));
                            unitCostCodePrice.price = zcjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGRGF': {
                        // 甲供人工费	  ∑标记为甲供的人工费“数量”*“市场价”
                        if (!ObjectUtils.isEmpty(jgRgRcj)) {
                            jgrgf = Number(jgRgRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, donorMaterialNumber)), je));
                            }, 0));
                            unitCostCodePrice.price = jgrgf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGCLF': {
                        // 甲供材料费  ∑标记为甲供的材料=“材料、商砼、砼、浆、商浆、配比”的“数量”*“市场价”
                        if (!ObjectUtils.isEmpty(jgClRcj)) {
                            jgclf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, donorMaterialNumber)), je));
                            }, 0));
                            unitCostCodePrice.price = jgclf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGJXF': {
                        // 甲供机械费  ∑标记为甲供的机械费“数量”*“市场价”
                        if (!ObjectUtils.isEmpty(jgJxRcj)) {
                            jgjxf = Number(jgJxRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, donorMaterialNumber)), je));
                            }, 0));
                            unitCostCodePrice.price = jgjxf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGSBF': {
                        // 甲供设备费	  ∑标记为甲供的设备费“数量”*“市场价”
                        if (!ObjectUtils.isEmpty(jgSbRcj)) {
                            jgsbf = Number(jgSbRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, donorMaterialNumber)), je));
                            }, 0));
                            unitCostCodePrice.price = jgsbf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGZCF': {
                        // 甲供主材费  ∑标记为甲供的主材费“数量”*“市场价”
                        if (!ObjectUtils.isEmpty(jgZcRcj)) {
                            jgzcf = Number(jgZcRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber,donorMaterialNumber)) ,je));
                            }, 0));
                            unitCostCodePrice.price = jgzcf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'GCGM': {
                        // GCGM	工程规模	本单位工程的工程规模
                        if (!ObjectUtils.isEmpty(gcgm)) {
                            unitCostCodePrice.price = NumberUtil.numberScale(Number(gcgm), jzgm);
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                }
            }
        }
    }

}

GsUnitCostCodePriceService.toString = () => '[class GsUnitCostCodePriceService]';
module.exports = GsUnitCostCodePriceService;