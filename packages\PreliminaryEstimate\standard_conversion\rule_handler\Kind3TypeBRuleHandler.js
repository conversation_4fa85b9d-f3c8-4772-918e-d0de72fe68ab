const Kind3RuleHandler = require("./Kind3RuleHandler");
const {NumberUtil} = require("../../utils/NumberUtil");
const { Snowflake } = require("../../utils/Snowflake");

class Kind3TypeBRuleHandler extends Kind3RuleHandler {
    constructor(strategyCtx, rule)  {
        super(strategyCtx, rule);
        this.formatMath = strategyCtx.conversionService.mathFormat(rule.math, rule);
    }

    async addDeByRule(){
        const {
            constructId,
            unitId,
            singleId,
            de,
            unitProject,
            deBeLong,
            deLine,
        } = this.ctx;
        let rule = this.rule;

        let ruleMath = this.formatMath;
        let newMath = ruleMath;
        let addDeNumber = 0;
        if("+-*/".includes(ruleMath.charAt(0))){
            addDeNumber = NumberUtil.numberScale(eval(ruleMath.substring(1)), 6);
            newMath = ruleMath.charAt(0) + addDeNumber;
        }else{
            addDeNumber = NumberUtil.numberScale(eval(ruleMath), 6);
            newMath = "" + addDeNumber;
        }

        // 如果新增定额数量小于1，则直接退出
        if(addDeNumber < 1){
            return;
        }

        // 获取相关定额 子集定额
        let relationDe = await this.ctx.service.PreliminaryEstimate.gsBaseDeService.getDeAndRcj(rule.relationDeId);
        // 新增定额
        let model = {
            constructId: constructId,
            unitId: unitId,
            type: '04',
            parentId: de.sequenceNbr,
        }
        let standardDeModelList = await this.ctx.service.PreliminaryEstimate.gsProjectCommonService.findDesByDeIdAndStandardDeId(constructId, unitId, de.sequenceNbr, relationDe.standardDeId);
        let addedDe = standardDeModelList.filter(item => !ObjectUtil.isEmpty(item.conversionAddByRule))[0];
        if (ObjectUtil.isEmpty(addedDe)) {
            addedDe = await this.ctx.service.PreliminaryEstimate.gsDeService.createDeRowAppendBaseDe(model, relationDe.standardDeId);
        }
        addedDe.conversionAddByRule = {
            sequenceNbr: Snowflake.nextId(),
            type: "",
            kind: "0",
            math: newMath,
            relation: newMath,
            defaultValue: 1,
            selectedRule: addDeNumber,
            index: -1,
            libraryCode: rule.libraryCode,
            ruleInfo: newMath,
            selected: true
        };

        // 修改工程量
        let params = {
            constructId,
            unitId,
            deId: addedDe.sequenceNbr,
            quantity: deLine.quantity,
            resetDeQuantities: false,
            quantityExpression: "HSGCL"
        }
        await this.ctx.service.PreliminaryEstimate.gsDeService.updateQuantity(params);

        let ruleDeIdObj = {
            deId: addedDe.sequenceNbr,
            ruleId: rule.sequenceNbr
        }
        if(de.addByRuleDeIds) {
            de.addByRuleDeIds.push(ruleDeIdObj);
        }else{
            de.addByRuleDeIds = [ruleDeIdObj];
        }

        this.ctx.deUpDateObj.addedDes.push(addedDe);
    }

    /**
     * 逐条执行换算规则
     */
    async execute(){
        await this.prepare();
        await this.addDeByRule();
        this.after();
    }

    analysisRule(){
        // 什么都不做
    }

    deCodeUpdateInfo() {
        return {redStr: `[Z ${this.rule.relationDeCode}]`, blackStr: null}
    }

    deTypeUpdateInfo(rule){
        // 什么都不做
    }
}
module.exports = Kind3TypeBRuleHandler;
