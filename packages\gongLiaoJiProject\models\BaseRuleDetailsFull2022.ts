import { Column, Entity, PrimaryColumn } from "typeorm";
import {BaseModel} from "./BaseModel";

@Entity({ name: "base_rule_details_full_2022" })
export class BaseRuleDetailsFull2022 extends BaseModel {

	@Column({ name: "library_code", nullable: true })
	public libraryCode: string; // 定额库编码

	@Column({ name: "relation_group_code", nullable: true })
	public relationGroupCode: string; // 关联规则组编码

	@Column({ name: "kind", nullable: true })
	public kind: string; // 规则分类 1.勾选类 2.下拉框 3.填写类

	@Column({ name: "relation_group_id", nullable: true })
	public relationGroupId: string; // 规则组id

	@Column({ name: "rule_group_id", nullable: true })
	public ruleGroupId: string; // 规则组id

	@Column({ name: "relation_group_name", nullable: true })
	public relationGroupName: string; // 规则组名称

	@Column({ name: "relation_group_cnt", type: "int", nullable: true })
	public relationGroupCnt?: number; // 规则数量

	@Column({ name: "relation_group_rule", nullable: true })
	public relationGroupRule?: string; // 规则选项

	@Column({ name: "file_details_id", type: "bigint", nullable: true })
	public fileDetailsId?: number; // 规则文件明细表id

	@Column({ name: "relation_code", nullable: true })
	public relationCode?: string; // 规则编码

	@Column({ name: "relation", nullable: true })
	public relation?: string; // 规则内容

	@Column({ name: "math", nullable: true })
	public math?: string; // 规则公式

	@Column({ name: "de_id", type: "bigint", nullable: true })
	public deId?: number; // 定额id

	@Column({ name: "de_code", nullable: true })
	public deCode?: string; // 定额编码

	@Column({ name: "de_name", nullable: true })
	public deName?: string; // 定额名称

	@Column({ name: "default_value", type: "decimal", precision: 16, scale: 6, nullable: true })
	public defaultValue?: number; // 默认值

	@Column({ name: "default_value_max", nullable: true })
	public defaultValueMax?: string; // 默认值上限

	@Column({ name: "default_value_min", nullable: true })
	public defaultValueMin?: string; // 默认值下限

	@Column({ name: "rule_range", nullable: true })
	public ruleRange?: string; // 规则范围

	@Column({ name: "relation_de_code", nullable: true })
	public relationDeCode?: string; // 关联定额编码

	@Column({ name: "relation_de_id", type: "bigint", nullable: true })
	public relationDeId?: number; // 关联定额id

	@Column({ name: "data_format", type: "int", nullable: true })
	public dataFormat?: number; // 数据格式要求(1 向上取整 2 向下取整 3 保留四位小数并四舍五入)

	@Column({ name: "sort_no", type: "bigint", nullable: true })
	public sortNo?: number; // 排序序号

	@Column({ name:"type" , nullable : true})
	public type?: string; // a：整体单价乘数 b：联动新增定额 c：联动增加定额明细 d：R/C/J/乘数 e1：指定材料更改消耗量-无约束 e2：指定材料更改消耗量

	@Column({ length : 255 ,nullable:true,name:"agency_code"})
	public agencyCode?: string;

	@Column({ length : 255 ,nullable:true,name:"product_code"})
	public productCode?: string;

	@Column({ length : 255 ,nullable:true,name:"top_group_type"})
	public topGroupType?: string;

	@Column({ type :"bigint" ,nullable:true,name:"rcj_id"})
	public rcjId?: number;

	@Column({ type :"int" ,nullable:true,name:"sort_no_global"})
	public sortNoGlobal?: number;

	@Column({ name: "exclude_material_codes", nullable: true })
	public excludeMaterialCodes?: string; // 规则范围
}