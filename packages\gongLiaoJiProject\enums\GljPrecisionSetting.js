class GljPrecisionSetting {
    // 编辑区
    static EDIT = {
        // 定额
        DE: {
            isUnitQuantity: false, // 是否按单位设置子目工程量精度
            unitQuantity: {}, // 单位 - 工程量

            quantity: 5, // 工程量
            quantityExpression: '', // 工程量表达式
            baseJournalPrice:2,
            price: 2, // 单价
            totalNumber: 2, // 合价
            baseJournalTotalNumber:2,  //
            RSum: 2, // 人工费单价
            rTotalSum: 2, // 人工费合价
            rdTotalSum:2,
            CSum: 2, // 材料费单价
            cTotalSum: 2, // 材料费合价
            cdTotalSum:2,
            JSum: 2, // 机械费单价
            jTotalSum: 2, // 机械费合价
            jdTotalSum:2,
            ZSum: 2, // 主材费单价
            zTotalSum: 2, // 主材费合价
            SSum: 2, // 设备费单价
            sTotalSum: 2, // 设备费合价

            adjustmentCoefficient: '', // 调整系数
            freeRate: 2, // 费率
        },
        // 定额人材机
        DERCJ: {
            quantity: 5, // 工程量
            quantityExpression: '', // 工程量表达式

            price: 2, // 单价
            totalNumber: 2, // 合价
            RSum: 2, // 人工费单价
            rTotalSum: 2, // 人工费合价
            CSum: 2, // 材料费单价
            cTotalSum: 2, // 材料费合价
            JSum: 2, // 机械费单价
            jTotalSum: 2, // 机械费合价
        },
        // 定额主材/设备
        DEZS: {
            quantity: 5, // 工程量
            quantityExpression: '', // 工程量表达式

            price: 2, // 单价
            totalNumber: 2, // 合价
            ZSum: 2, // 主材费单价
            zTotalSum: 2, // 主材费合价
            SSum: 2, // 设备费单价
            sTotalSum: 2, // 设备费合价
        },
        // 定额下主材/设备
        DEXZS: {
            resQty: 4, // 消耗量
            quantity: 4, // 工程量
            quantityExpression: '', // 工程量表达式

            price: 2, // 单价
            totalNumber: 2, // 合价
            ZSum: 2, // 主材费单价
            zTotalSum: 2, // 主材费合价
            SSum: 2, // 设备费单价
            sTotalSum: 2, // 设备费合价
        },
        // 措施项目
        CSXM: {
            calculationBase: 5,
            CSX: {
                price: 2, // 单价
                totalNumber: 2, // 合价
                RSum: 2, // 人工费单价
                rTotalSum: 2, // 人工费合价
                CSum: 2, // 材料费单价
                cTotalSum: 2, // 材料费合价
                JSum: 2, // 机械费单价
                jTotalSum: 2, // 机械费合价
                ZSum: 2, // 主材费单价
                zTotalSum: 2, // 主材费合价
                SSum: 2, // 设备费单价
                sTotalSum: 2, // 设备费合价
            }
        },
        // 单位工程行
        UNITLINE: {
            totalNumber: 2, // 合价
            rTotalSum: 2, // 人工费合价
            cTotalSum: 2, // 材料费合价
            jTotalSum: 2, // 机械费合价
            zTotalSum: 2, // 主材费合价
            sTotalSum: 2, // 设备费合价
        },
        // 分部行/子分部行
        FBLINE:{
            totalNumber: 2, // 合价
            rTotalSum: 2, // 人工费合价
            cTotalSum: 2, // 材料费合价
            jTotalSum: 2, // 机械费合价
            zTotalSum: 2, // 主材费合价
            sTotalSum: 2, // 设备费合价
        }
    };

    // 明细区
    static DETAIL = {
        // 人材机
        RCJ: {
            resQty: 4, // 消耗量
            totalNumber: 4, // 数量
            resQtyFactor: 15, // 数量
            taxRate: 4, // 税率
            ftRate: 2, // 分摊比例
        },
        // 补充人材机
        BCRCJ: {
            taxRate: 4, // 税率
        },
        // 工程量明细
        QUANTITIES: {
            mathFormula: 8, // 计算式
            mathResult: 3, // 结果
        },
        // 价表
        // 普通人材机、主材、设备
        PTRCJZS: {
            // 不含税基期价
            baseJournalPrice: 2,
            // 含税基期价
            baseJournalTaxPrice: 2,
            // 不含税市场价
            marketPrice: 2,
            // 含税市场价
            marketTaxPrice: 2,
            // 合价
            total: 2,
        },
        // 补充人材机、主材、设备
        BCRCJZS: {
            baseJournalPrice: 2, // 不含税基期价
            baseJournalTaxPrice: 2, // 含税基期价
            marketPrice: 2, // 不含税市场价
            marketTaxPrice: 2, // 含税市场价
        },
        // 费用/调整人材机
        FYTZRCJ: {
            baseJournalPrice: 2, // 不含税基期价
            baseJournalTaxPrice: 2, // 含税基期价
            marketPrice: 1, // 不含税市场价
            marketTaxPrice: 1, // 含税市场价
        },
        // 措施项目
        CSXM: {
            rgfFt: 2, //人工费分摊值
            clfFt: 2, //材料费分摊值
            jxfFt: 2, //机械费分摊值
        },
    };

    //人材机汇总
    static RCJ_COLLECT = {
        totalNumber: 4, // 数量
        donorMaterialNumber: 4, // 甲供数量

        baseJournalPrice: 2, // 不含税基期价
        baseJournalTaxPrice: 2, // 含税基期价
        marketPrice: 2, // 不含税市场价
        marketTaxPrice: 2, // 含税市场价
        total: 2, // 不含税市场价合计
        totalTax: 2, // 含税市场价合计
        jc: 2, // 价差
        jchj: 2, // 价差合计
        donorMaterialPrice: 2, // 甲供价

        marketPriceAdjustUnit: 4, // 市场价调整系数
        taxRate: 4, // 税率
        transferFactor: 4, // 三材系数
    };

    // 造价分析
    static COST_ANALYSIS = {
        je: 2, // 金额
        jzgm: 3, // 建筑规模
        scNumber: 4, // 三材数量
        costProportion: 2 // 造价占比
    };

    // 独立费
    static UNIT_DLF = {
        quantity: 3, // 数量
        price: 2, // 单价
        totalPrice: 2, // 合价
    };

    // 费用汇总
    static COST_CODE = {
        je: 2, // 金额
    };

    // 费用汇总
    static COST_SUMMARY = {
        awf: 2, // 安文费
        je: 2, // 金额
        jqsdf: 2, // 记取水电费
        calculationBase :2 ,

        // 安、文明细
        AWMX:{
            freeRate: 2, // 费率
        },
        // 计取水电
        JQSD:{
            kcRate: 2, //扣除系数
        },
        // 汇总表
        SUMMARY:{
            freeRate: 2, //费率  由于百分号的原因，必须多两位
        }
    };

    // 取费表
    static FREE_RATE=  {
        manageFeeRate: 2, //企业管理费
        profitRate: 2,  // 利润
        taxRate: 2,  // 税金
        anwenRate: 2, // 安全生产、文明施工费（%）
    }

    // 项目层级
    static PROJECT_PRECISION=  {
        // 设备购置费
        SBGZF: {
            quantity: 3, // 设备购置费数量
            price: 2, // 设备购置费金额

            transportAndOtherRates: 2 //运杂费率
        },
        // 建设其他费
        JSQTF: {
            jsqtf: 3, // 建设其他费

            price: 2, // 单价
            calculateBase: 2, // 计算基数
            je: 2, // 金额

            freeRate: 2, // 费率
        },
        // 费用计算器
        FYJSQ: {
            priceType: 7, // 价格类
        },
        // 概算汇总
        GSHZ: {
            calculateBase: 2, // 计算基数

            proportion: 2, // 投资占比
        },
        // 概算调整
        GSTZ: {
            priceType: 7, // 价格类
        },
        // 国外设备计算器
        GWSBJSQ: {
            freeRate: 2, // 费率
        }
    }

    // 前端配置项
    static PRECISION_SETTING_UI = {

        RCJ:{
            totalNumber: 4, // 数量精度统一设置
        },

        FREE_RATE:{
            freeRate: 4, // 取费表、费用汇总费率
        },

        DE: {
            quantitySelect: 'quantity',
            quantity: 5, // 工程量精度统一设置
            // 按照单位设置工程量精度
            unitQuantity: {
                组: 0,
                宗: 0,
                元: 2,
                套: 0,
                台: 0,
                千米: 3,
                辆: 0,
                块: 0,
                公斤: 2,
                付: 0,
                处: 0,
                部: 0,
                千克: 2,
                kg: 2,
                吨: 2,
                t : 2,
                立方米: 2,
                m3: 2,
                米: 2,
                m : 2,
                平方米: 2,
                m2: 2,
                工日: 0,
                个: 0,
                台班: 0,
                座: 0,
                km: 3,
                樘: 0,
                件: 0,
                根: 0,
                系统: 0,
                株: 0,
                丛: 0,
                缸: 0,
                只: 0,
                支: 0,
                对: 0,
                份: 0,
                攒: 0,
                榀: 0,
                其他: 2,
            }
        },

    }
}
module.exports = GljPrecisionSetting
