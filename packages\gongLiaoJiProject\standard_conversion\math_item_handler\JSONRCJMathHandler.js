const JSONMathHandler = require("./JSONMathHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");
/**
 * 处理RCJ相关规则：根据指定人材机消耗量计算消耗量
 */
class JSONRCJMathHandler extends JSONMathHandler{
    setParseMath() {
        let qtyStr = this.mathJson.math;
        for (let oneBase of this.mathJson.base) {
            oneBase.source = oneBase.source || "";
            let oneBaseRcjs = this.findActiveRCJByCode(oneBase.source.trim());
            let oneQty = 0;
            let oneQtyMath = oneBase.math;
            let id = oneBase.id;
            let operator = this.mathOperator(oneQtyMath);
            if (ObjectUtils.isNotEmpty(oneBaseRcjs)) {
                let onBaseRcjQty = oneBaseRcjs[0].resQty || 0;
                if (ObjectUtils.isNotEmpty(operator)) {
                    oneQty = this.conversionService.mathAfterCalculation(`${onBaseRcjQty}${oneQtyMath}`, this.conversionInfoDigits);
                }else{
                    oneQty = this.conversionService.mathAfterCalculation(`${oneQtyMath}`, this.conversionInfoDigits);
                }
            }
            // let reg = new RegExp(`\\b${id}\\b`);
            // qtyStr = qtyStr.replace(reg, oneQty)
            qtyStr = this.mathReplaceAllDeal(qtyStr, id, oneQty);
        }
        this.parseMath = this.conversionService.mathAfterCalculation(qtyStr, this.conversionInfoDigits);
    }
}

module.exports = JSONRCJMathHandler;