const {FreeRateModel} = require("../models/FreeRateModel");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ObjectUtils} = require('../utils/ObjectUtils');
const {Service} = require('../../../core');
const WildcardMap = require("../core/container/WildcardMap");
const BusinessConstants = require("../constants/BusinessConstants");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtil} = require("../../../common/ObjectUtil");

class GsInitUnitProjectService extends Service {

    constructor(ctx) {
        super(ctx);

    }

    /**
     * 单位
     */
    async init(projectModel, constructId) {
        this.projectModel = projectModel;
        this.constructId = constructId;

        let qfMajorTypeMoneyMap = new Map();
        projectModel.qfMajorType = projectModel.constructMajorType;
        projectModel.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
        // 初始化取费表
        await this.initUnitProjectFreeRates(this.projectModel);
        // 初始化费用代码
        this.initUnitCostCodePrice(this.projectModel);
        // 初始化费用汇总
        this.initUnitCostSummary(this.projectModel);
        // 初始化费用汇总合计
        this.initUnitCostSummaryTotal(this.projectModel);
        // 初始化工程量明细
        this.initQuantities(this.projectModel);
        // 初始化标准换算
        this.initDeConversion(this.projectModel);
        // 初始化单位基本信息
        this.initOverView(this.projectModel);
        // // 初始化表格列信息
        // this.initTableColumn(this.projectModel);
    }

    /**
     * 移除单位工程
     * @param pid
     * @param constructId
     */
    async remove(pid, constructId) {
        //删除 取费表 UNIT_QFB、PROJECT_QFB
        await this.removeQfb(pid, constructId);
        //删除 工程量 UNIT_QUANTITIES
        await this.removeQuantities(pid, constructId);
        //删除 标准换算 UNIT_CONVERSION
        await this.removeConversion(pid, constructId);
        //删除 费用查看 PROJECT_FYCK、SINGLE_FYCK、UNIT_FYCK
        await this.removeFyck(pid, constructId);
        //删除 费用汇总 UNIT_COST_SUMMARY
        await this.removeCostSummary(pid, constructId);
        //删除 费用代码 UNIT_COST_CODE
        await this.removeCostCode(pid, constructId);
        // 删除 费用汇总合计 UNIT_COST_SUMMARY_TOTAL
        await this.removeCostSummaryTotal(pid, constructId);
        //删除 工程基本信息 JBXX_KEY
        await this.removeJbxxKey(pid, constructId);
        //删除 单位独立费 UNIT_DLF_KEY
        await this.removeUnitDlfKey(pid, constructId);
        //删除 用户人材机 PROJECT_USER_RCJ
        await this.removeUserRcj(pid, constructId);
        //删除 人材机汇总 RCJ_COLLECT
        await this.removeRcjCollect(pid, constructId);


        //删除 定额
        await this.removeDe(pid, constructId);
        // 删除单位重新汇总计算数据：建设其他项目、概算汇总
        await this.removeUnitReCountSummary(pid, constructId);

        //删除 页签缓存
        await this.removeTableSetting(pid, constructId);
    }

    /**
     * 初始化标准换算
     * @param projectModel
     * @returns {Promise<void>}
     */
    async initDeConversion(projectModel) {
        if (ObjectUtils.isEmpty(projectModel)) {
            return;
        }
        let conversion = ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        if (ObjectUtil.isEmpty(conversion)) {
            conversion = {};
            ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.UNIT_CONVERSION, conversion);
        }
        if (ObjectUtil.isEmpty(conversion[projectModel.sequenceNbr])) {
            conversion[projectModel.sequenceNbr] = {};
        }
    }


    /**
     * 初始化工程量明细
     * @param projectModel
     * @returns {Promise<void>}
     */
    async initQuantities(projectModel) {
        if (ObjectUtils.isEmpty(projectModel)) {
            return;
        }
        let quantitiesMap = ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        quantitiesMap.set(projectModel.sequenceNbr, new Map());
    }

    /**
     * 
     */
    async initOverView(projectModel) {
        let projectDomain = ProjectDomain.getDomain(this.constructId);
        if (ObjectUtils.isEmpty(projectDomain)) {
            return;
        }
        await this.service.PreliminaryEstimate.gsOverviewService.initData({constructId:this.constructId,
            unitId:projectModel.sequenceNbr,type:FunctionTypeConstants.JBXX_KEY_TYPE_11});
        await this.service.PreliminaryEstimate.gsOverviewService.initData({constructId:this.constructId,
            unitId:projectModel.sequenceNbr,type:FunctionTypeConstants.JBXX_KEY_TYPE_13});
        await this.service.PreliminaryEstimate.gsOverviewService.initData({constructId:this.constructId,
            unitId:projectModel.sequenceNbr,type:FunctionTypeConstants.JBXX_KEY_TYPE_12});
    }
    /**
     * 初始化单位工程的费率
     * @param projectModel
     */
    async initUnitProjectFreeRates(projectModel) {
        if (ObjectUtils.isEmpty(projectModel)) {
            return;
        }
        let libraryCode = projectModel.constructMajorType
        if (ObjectUtils.isEmpty(libraryCode)) {
            return;
        }
        let deLibrary = await this.service.PreliminaryEstimate.gsBaseDeLibraryService.getByLibraryCode(libraryCode);
        let param = {
            libraryCode: deLibrary.libraryCode,
            constructId: this.constructId,
            singleId: projectModel.parentId,
            unitId: projectModel.sequenceNbr,
            projectType: FreeRateModel.DEFAULT_PROJECT_TYPE,
            payTaxesAreas: FreeRateModel.DEFAULT_PAY_TAXES_AREAS,
        };

        // 如果工程项目下存在该取费专业的费率，则直接同步数据
        let freeRateProjectModel = ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        if (ObjectUtils.isEmpty(freeRateProjectModel)) {
            return;
        }
        if (ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate)) {
            freeRateProjectModel.childFreeRate= new Map();
        }
        let freeRate = ConvertUtil.deepCopy(freeRateProjectModel.childFreeRate.get(libraryCode));
        if (ObjectUtils.isNotEmpty(freeRateProjectModel) && ObjectUtils.isEmpty(freeRate)) {
            if (ObjectUtils.isEmpty(freeRate)) {
                param.projectType = freeRateProjectModel.projectType;
                param.payTaxesAreas = freeRateProjectModel.payTaxesAreas;
                freeRate = await this.service.PreliminaryEstimate.gsFreeRateService.getBaseFreeRate(param);
            }
        }else {
            freeRate.unitId = param.unitId;
        }
        // 初始化单位工程取费率
        let unitQfbMap = ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let freeKey = WildcardMap.generateKey(param.unitId, freeRate.libraryCode);
        ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRate));

        // 同步至工程项目取费率
        if (ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate.get(libraryCode))) {
            let args = {
                libraryCode: freeRate.libraryCode,
                projectType: freeRateProjectModel.projectType,
                payTaxesAreas: freeRateProjectModel.payTaxesAreas
            };
            let freeRateModel = ObjectUtils.copyProp(await this.service.PreliminaryEstimate.gsBaseFreeRateService.searchFreeRateInfo(args), new FreeRateModel());
            freeRateModel.constructId = this.constructId;
            freeRateModel.projectType = freeRateProjectModel.projectType;
            freeRateModel.payTaxesAreas = freeRateProjectModel.payTaxesAreas;
            freeRateProjectModel.childFreeRate.set(freeRate.libraryCode, freeRateModel);
            ProjectDomain.getDomain(this.constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        }
    }

    /**
     * 移除 工程量明细
     * @param pid
     * @param constructId
     */
    async removeQuantities(pid, constructId) {
        // 获取工程量明细
        let quantitiesMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        if (ObjectUtils.isEmpty(quantitiesMap)) {
            return
        }
        for (const [key, value] of quantitiesMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                quantitiesMap.delete(key);
            }
        }
    }

    /**
     * 移除 标准换算
     * @param pid
     * @param constructId
     */
    async removeConversion(pid, constructId) {
        // 获取标准换算
        let conversion = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        if (ObjectUtils.isEmpty(conversion)) {
            return
        }
        Object.entries(conversion).forEach(([key, value]) => {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                delete conversion[key];
            }
        })
    }

    /**
     * 移除 费用查看
     * @param pid
     * @param constructId
     */
    async removeFyck(pid, constructId) {
        // 费用查看
        let unitFyck = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FYCK);
        if (ObjectUtils.isNotEmpty(unitFyck)) {
            for (const [key, value] of unitFyck) {
                if (ObjectUtils.isNotEmpty(key) && key.endsWith(pid)) {
                    unitFyck.delete(key);
                }
            }
        }
    }

    /**
     * 移除 费用汇总
     * @param pid
     * @param constructId
     */
    async removeCostSummary(pid, constructId) {
        // 获取费用汇总
        let unitCostCodeSummarys = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
        if (ObjectUtils.isEmpty(unitCostCodeSummarys)) {
            return
        }
        for (const [key, value] of unitCostCodeSummarys) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                unitCostCodeSummarys.delete(key);
            }
        }
    }

    /**
     * 移除 费用代码
     * @param pid
     * @param constructId
     */
    async removeCostCode(pid, constructId) {
        // 获取费用代码
        let unitCostCode = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
        if (ObjectUtils.isEmpty(unitCostCode)) {
            return
        }
        for (const [key, value] of unitCostCode) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                unitCostCode.delete(key);
            }
        }
    }


    /**
     * 移除 费用汇总合计
     * @param pid
     * @param constructId
     */
    async removeCostSummaryTotal(pid, constructId) {
        // 获取费用汇总合计
        let unitCostCodeSummaryTotals = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL)
        if (ObjectUtils.isEmpty(unitCostCodeSummaryTotals)) {
            return
        }
        for (const [key, value] of unitCostCodeSummaryTotals) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                unitCostCodeSummaryTotals.delete(key);
            }
        }
    }


    /**
     * 移除 工程基本信息 JBXX_KEY
     * @param pid
     * @param constructId
     */
    async removeJbxxKey(pid, constructId) {
        // 获取工程基本信息 JBXX_KEY
        let functionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY)
        if (ObjectUtils.isEmpty(functionMap)) {
            return
        }
        for (const [key, value] of functionMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith("JBXX-" + pid)) {
                functionMap.delete(key);
            }
        }
    }

    /**
     * 移除 单位独立费 UNIT_DLF
     *
     * @param pid
     * @param constructId
     */
    async removeUnitDlfKey(pid, constructId) {
        // 单位独立费 UNIT_DLF
        let functionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
        if (ObjectUtils.isEmpty(functionMap)) {
            return
        }
        for (const [key, value] of functionMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith("INDCOST-" + pid)) {
                functionMap.delete(key);
            }
        }
    }

    /**
     * 移除 用户人材机 PROJECT_USER_RCJ
     *
     * @param pid
     * @param constructId
     */
    async removeUserRcj(pid, constructId) {
        // 单位独立费 UNIT_DLF
        let functionDataMap = await ProjectDomain.getDomain(constructId).functionDataMap
        let rcjUserList = functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ)
        if (ObjectUtils.isEmpty(rcjUserList)) {
            return
        }
        rcjUserList.delete(pid);
        //functionDataMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, rcjUserList.filter(item => pid !== item.unitId))
    }

    /**
     * 移除 单人材机汇总 RCJ_COLLECT
     *
     * @param pid
     * @param constructId
     */
    async removeRcjCollect(pid, constructId) {
        // 颜色删除
        let functionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT)
        if (ObjectUtils.isEmpty(functionMap)) {
            return
        }
        for (let [key, value] of functionMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(FunctionTypeConstants.UNIT_COLOR)) {
                value?.delete(pid);
            }
        }

    }

    /**
     * 删除定额
     * @param pid
     * @param constructId
     * @returns {Promise<void>}
     */
    async removeDe(pid, constructId) {
        let deList = await ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === pid && item.parentId === 0)
        if (ObjectUtils.isEmpty(deList)) {
            return
        }
        for (let de of deList) {
            await ProjectDomain.getDomain(constructId).getDeDomain().removeDeRow(de.sequenceNbr);
        }
    }

    /**
     * 移除 取费表
     * @param pid
     * @param constructId
     */
    async removeQfb(pid, constructId) {
        //删除单位工程取费表
        let unitQfbMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isEmpty(unitQfbMap)) {
            return
        }

        for (const [key, value] of unitQfbMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                unitQfbMap.delete(key);
            }
        }
        //删除工程项目取费表
        let result = new Set();
        for (const str of unitQfbMap.keys()) {
            const [key, value] = str.split("--");
            if (value) {
                result.add(value.trim());
            }
        }
        let unitTypes = Array.from(result);
        let projectQfbMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        const projectTypes = [];
        for (const key of projectQfbMap.childFreeRate.keys()) {
            projectTypes.push(key);
        }
        let differentTypes = projectTypes.filter(item => !unitTypes.includes(item));
        for (let differentType of differentTypes) {
            projectQfbMap.childFreeRate.delete(differentType);
        }
    }


    /**
     * 初始化费用代码
     * @param unitProject
     */
    initUnitCostCodePrice(projectModel) {
        let param = {
            constructId: this.constructId,
            singleId: projectModel.parentId,
            unitId: projectModel.sequenceNbr,
            // constructMajorType: "2018-JZGC-GS"
            constructMajorType: projectModel.constructMajorType
        };
        let unitCostCodePrices = this.service.PreliminaryEstimate.gsUnitCostCodePriceService.defaultUnitCostCodePrice(param);
        ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
            .set(param.unitId + FunctionTypeConstants.SEPARATOR + param.constructMajorType, unitCostCodePrices);

    }

    /**
     * 初始化费用汇总
     * @param unitProject
     */
    initUnitCostSummary(projectModel) {
        let param = {
            constructId: this.constructId,
            singleId: projectModel.parentId,
            unitId: projectModel.sequenceNbr,
            // constructMajorType: "2018-JZGC-GS"
            constructMajorType: projectModel.constructMajorType
        };
        let unitCostSummarys = this.service.PreliminaryEstimate.gsUnitCostSummaryService.defaultUnitCostSummary(param);
        ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(param.unitId + FunctionTypeConstants.SEPARATOR + param.constructMajorType, unitCostSummarys);
    }

    /**
     * 初始化费用汇总合计
     * @param unitProject
     */
    initUnitCostSummaryTotal(projectModel) {
        let param = {
            constructId: this.constructId,
            unitId: projectModel.sequenceNbr
        };
        let unitCostSummaryTotals = this.service.PreliminaryEstimate.gsUnitCostSummaryService.defaultCostSummaryMajorsTotal(param);
        ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL).set(param.unitId, unitCostSummaryTotals);
    }

    /**
     * 初始化表格列
     * @param projectModel
     */
    initTableColumn(projectModel) {
        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(this.constructId).getProjectById(this.constructId);
        // 如果表格列是全局配置
        if (!constructProject.isDefault) {
            let businessMap = ProjectDomain.getDomain(this.constructId).functionDataMap;

            // 获取该单项下的所有单位
            let unitProjectsByConstruct = new Array();
            if (ObjectUtils.isNotEmpty(constructProject.children)) {
                PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
            }

            // 获取曾经全局设置的单位的id
            let unitProject = unitProjectsByConstruct[0];
            let unitId1 = unitProject.sequenceNbr;

            //设置预算书表头
            if (ObjectUtils.isNotEmpty(constructProject.businessIdArray)) {
                if (constructProject.businessIdArray.includes(BusinessConstants.UNIT_YSH_ID)) {
                    let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
                    if (ObjectUtils.isEmpty(objMap)) {
                        objMap = new Map();
                        businessMap.set(FunctionTypeConstants.YSH_TABLELIST, objMap);
                    }
                    let header = objMap.get(FunctionTypeConstants.YSH_TABLELIST + this.constructId + FunctionTypeConstants.SEPARATOR + unitId1);
                    if (ObjectUtils.isNotEmpty(header)) {
                        objMap.set(FunctionTypeConstants.YSH_TABLELIST + this.constructId + FunctionTypeConstants.SEPARATOR + projectModel.sequenceNbr, header);
                    }
                }
                // 单位工程-独立费
                if (constructProject.businessIdArray.includes(BusinessConstants.UNIT_DLF_ID)) {
                    let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
                    if (ObjectUtils.isEmpty(objMap)) {
                        objMap = new Map();
                        businessMap.set(FunctionTypeConstants.DLF_TABLELIST, objMap);
                    }
                    let header = objMap.get(unitId1);
                    if (ObjectUtils.isNotEmpty(header)) {
                        objMap.set(projectModel.sequenceNbr, header);
                    }
                }
            }
        }
    }

    /**
     * 删除单位重新汇总计算数据：建设其他项目、概算汇总
     * @param pid
     * @param constructId
     */
    async removeUnitReCountSummary(pid, constructId) {

        // 删除单位，重新计算建设其他项目：因为建安工程费，更新建设其他费
        await this.service.PreliminaryEstimate.gsOtherProjectCostService.countOtherProjectCostCode({
            projectId: constructId
        });

        // 删除单位，重新计算概算汇总：因为建安工程费、建设其他费，更新计算概算费用代码
        this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
            constructId: constructId
        });
    }

    /**
     * 移除 页签缓存
     * @param pid
     * @param constructId
     * @returns {Promise<void>}
     */
    async removeTableSetting(pid, constructId) {
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let tableSettingObj = businessMap.get(FunctionTypeConstants.TABLE_SETTING_CACHE);
        if (ObjectUtils.isNotEmpty(tableSettingObj)) {
            delete tableSettingObj[pid];
        }
    }

}

GsInitUnitProjectService.toString = () => '[class GsInitUnitProjectService]';
module.exports = GsInitUnitProjectService;
