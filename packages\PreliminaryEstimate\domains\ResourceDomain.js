const BaseDomain = require('./core/BaseDomain');
const WildcardMap = require('../core/container/WildcardMap');
const ResourceConstants = require('../constants/ResourceConstants');
const EE = require('../../../core/ee');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { ObjectUtils } = require('../../../electron/utils/ObjectUtils');
const { ResourceCalculator } = require('./calculators/resource/ResourceCalculator');
const {NumberUtil} = require("../utils/NumberUtil");
const DeUtils = require("./utils/DeUtils");
const {DeCalculator} = require("./calculators/de/DeCalculator");
const {QDCalculator} = require("./calculators/de/QDCalculator");
const {FBCalculator} = require("./calculators/de/FBCalculator");
const {Snowflake} = require("../utils/Snowflake");
const DeTypeConstants = require('../constants/DeTypeConstants');
const {DeTypeCheckUtil} = require("./utils/DeTypeCheckUtil");

class ResourceDomain extends BaseDomain{


  constructor(ctx) {
    super();
    super.ctx = ctx;
    this.initData();
  }
  initData() {

  }

  /**
   * 获得人材机
   * @param predicate
   * @returns {*}
   */
  getResource(predicate) {
    return this.ctx.resourceMap.getValues(predicate);
  }

  /**
   *  获取全量人材机 Array
    * @returns {any[]}
   */
  getResourceArray() {
    return this.ctx.resourceMap.valuesAsArray();
  }

  /**
   * 创建人材机
   * @param unitId
   * @param deId
   * @param resource
   * @param checkMarketPriceZero  是否检查市场价为0
   */
  createResource(unitId,deId,resource,checkMarketPriceZero = true) {
    if (checkMarketPriceZero &&(ObjectUtils.isEmpty(resource.marketPrice) || resource.marketPrice === 0))
    {
      //resource.marketPrice = resource.price;
    }
    this.ctx.resourceMap.set(WildcardMap.generateKey(unitId,deId,resource.sequenceNbr),resource);
  }

  async createResourcePBs(unitId, deId, parentId, rcj)
  {
    let pbs = [];
    let {service} = EE.app;
    switch (rcj.levelMark)
    {
      case ResourceConstants.LEVEL_MARK_PB_CL:
        pbs = await service.PreliminaryEstimate.gsBaseRcjService.getClpb(rcj)
        break;
      case ResourceConstants.LEVEL_MARK_PB_JX:
        pbs = await service.PreliminaryEstimate.gsBaseRcjService.getJxpb(rcj)
        break;
    }
    if(!ObjectUtils.isEmpty(pbs)) {
      pbs.forEach(item=>{
        item.rcjPbsId =item.sequenceNbr;
        item.originalQty =item.resQty;
        item.totalNumber = 0;
        item.sequenceNbr=Snowflake.nextId();
        item.parentId=parentId;
        item.deId = deId;
      });
      rcj.pbs = pbs;
    }
  }

  /**
   * 获取用户自定义人材机
   * @param code
   * @returns {T[]}
   */
  getUserResource(code)
  {
    if(ObjectUtil.isEmpty(code)) {
      return [];
    }
    return this.resourceMap.valuesAsArray().find(item => item.deCode = code);
  }


  /**
   * 修改人材机合价
   * @param unitId
   * @param deRowId
   * @param resourceId
   * @param totalNumber
   */
  updateResourceTotalNumber(unitId,deRowId,resourceId,totalNumber)
  {
    let deRow = this.ctx.deMap.getAllNodes().find(item => item.sequenceNbr === deRowId);
    let resource = this.ctx.resourceMap.valuesAsArray().find(item => item.sequenceNbr === resourceId );
    if(ObjectUtils.isNotEmpty(deRow) && ObjectUtils.isNotEmpty(resource)) {
        if(deRow.quantity !== 0 )
        {
          resource.totalNumber = totalNumber;
          resource.resQty = NumberUtil.numberScale(NumberUtil.divide(resource.totalNumber,deRow.quantity),3);
          for (let pb of resource?.pbs)
          {
            pb.totalNumber = NumberUtil.multiply(pb.resQty,resource.totalNumber);
          }
          this.notify(resource);
        }
        else
        {
          //如果父定额工程量为0 无论怎么修改下属人材机数量 都是会变回0
          resource.totalNumber = 0;
          return false;
        }
    }
    else
    {
      throw Error("找不到人材机或者父定额.");
    }
    return true;
  }

  /**
   * 修改人材机市场价
   * @param unitId
   * @param deRowId
   * @param resourceId
   * @param marketPrice
   */
  updateResourceMarketPrice(unitId,deRowId,resourceId,marketPrice)
  {
    let deRow = this.ctx.deMap.getAllNodes().find(item => item.sequenceNbr === deRowId);
    let resource = this.ctx.resourceMap.valuesAsArray().find(item => item.sequenceNbr === resourceId );
    if(ObjectUtils.isNotEmpty(deRow) && ObjectUtils.isNotEmpty(resource)) {
      resource.marketPrice = marketPrice;
      this.notify(resource);
    }
    else
    {
      throw Error("找不到人材机或者父定额.");
    }
    return true;
  }


  /**
   * 修改人材机合价
   * @param unitId
   * @param deRowId
   * @param resourceId
   * @param pbId
   * @param totalNumber
   */
  updatePBTotalNumber(unitId,deRowId,resourceId,pbId,totalNumber)
  {
    let deRow = this.ctx.deMap.getAllNodes().find(item => item.sequenceNbr === deRowId);
    let resource = this.ctx.resourceMap.getResource(item => item.sequenceNbr === resourceId );
    if(ObjectUtils.isNotEmpty(deRow) && ObjectUtils.isNotEmpty(resource) && ObjectUtils.isNotEmpty(resource.pbs) && ObjectUtils.isNotEmpty(resource.pbs.find(item => item.sequenceNbr === pbId))) {
      let pb = resource.pbs.find(item => item.sequenceNbr === pbId)
      pb.totalNumber = totalNumber;
      pb.resQty = NumberUtil.numberScale(NumberUtil.divide(pb.totalNumber,resource.quantity),3);
      this.notify(resource);
    }
    else
    {
      throw Error("找不到配比或者人材机.");
    }
    return true;
  }

  /**
   *
   * @param unitId
   * @param deRowId
   * @param resourceId
   * @param pbId
   * @param marketPrice
   * @returns {boolean}
   */
  updatePBMarketPrice(unitId,deRowId,resourceId,pbId,marketPrice)
  {

    let deRow = this.ctx.deMap.getAllNodes().find(item => item.sequenceNbr === deRowId);
    let resource = this.ctx.resourceMap.getResource(item => item.sequenceNbr === resourceId );
    if(ObjectUtils.isNotEmpty(deRow) && ObjectUtils.isNotEmpty(resource) && ObjectUtils.isNotEmpty(resource.pbs) && ObjectUtils.isNotEmpty(resource.pbs.find(item => item.sequenceNbr === pbId))) {
      let pb = resource.pbs.find(item => item.sequenceNbr === pbId)
      pb.marketPrice = marketPrice;
      this.notify(resource);
    }
    else
    {
      throw Error("找不到配比或者人材机.");
    }
    return true;
  }
  /**
   *
   * @param unitId
   * @param deId
   * @param parentId
   * @param resource
   */
  updateResource(unitId,deId,resource)
  {
    this.ctx.resourceMap.set(WildcardMap.generateKey(unitId,deId,resource.sequenceNbr),resource);

  }

  updateResourcePrice(unitId,deId,resource)
  {
    this.updateResource(unitId,deId,resource);
    this.notify(resource);
    return resource;
  }

  async notify(resource,reCalculateResource=true) {

    //1.重新计算人材机
    if(reCalculateResource){
      let rc = ResourceCalculator.getInstance(resource, this.ctx);
      await rc.analyze();
    }
    
    //2.重新计算定额
    let deRow = this.ctx.deMap.getAllNodes().find(item => item.sequenceNbr === resource.deRowId);
    //2.0人材机消耗量变更或新增删除人材机，需要处理定额标识
    DeTypeCheckUtil.checkAndUpdateDeType(deRow,this.ctx);
    // 2.1 如果操作时调整人材机时，则定额有可能时03定额
    let qdParentId = deRow.parentId;
    if(deRow.type === DeTypeConstants.DE_TYPE_DELIST){
      qdParentId = deRow.sequenceNbr;
    }else{
      let dc = DeCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId:deRow.sequenceNbr},this.ctx);
      await dc.analyze();
    }
    //3.重新计算清单
    let allQdIds = DeUtils.findAllQdParendIds(qdParentId, this.ctx);
    for(let qdItem of allQdIds){
      let qc = QDCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId,deRowId: qdItem}, this.ctx);
      await qc.analyze();
    }
    //4.重新计算分部
    let fc = FBCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId: deRow.parentId},this.ctx);
    await fc.analyze();
  }
  /**
   * 删除人材机
   * @param key
   */
  async removeResource(key)
  {
    let resource = this.ctx.resourceMap.getValues(key);
    if (resource.length >= 1) {
      let {constructId,unitId,deRowId} = resource[0];
      this.ctx.resourceMap.removeByPattern(key);
      await this.notify({constructId,unitId,deRowId});
    }
  }

  /**
   * 删除人材机配比
   * @param key
   * @param rcjDetailId
   */
  async removeResourcePb(key,rcjDetailId)
  {
    let  rcj =this.ctx.resourceMap.getValues(key);
    let  rcjPbs =null;
    if(ObjectUtils.isNotEmpty(rcj)){
        rcjPbs=rcj[0].pbs;

    if(ObjectUtils.isNotEmpty(rcjPbs)){
      for (let i = 0; i < rcjPbs.length; i++) {
        if (rcjPbs[i].sequenceNbr === rcjDetailId) {
          rcjPbs.splice(i, 1);
          break;
        }
      }
      if(rcjPbs.length===0){
        rcj.levelMark = RcjCommonConstants.LEVELMARK;
      }
    }

    await this.notify(rcj[0]);
    }
  }


}
ResourceDomain.toString = () => 'ResourceDomain';
module.exports = ResourceDomain;