import {GsConversionListItem} from "./GsConversionListItem";
import {GsConversionInfoItem} from "./GsConversionInfoItem";
import {StandardConvertMod} from "../enums/ConversionSourceEnum";

/**
 * 标准换算
 */
export class GsConversionModel{

    public constructId: string; // 工程项目id

    public unitId: string; //单位id

    public sequenceNbr: string; // 定额rowId

    public deId: string; //定额id

    public standardId: string; //标准定额id

    public type: string; //定额id

    public libraryCode: string; //定额id

    // 换算列表
    public conversionList: GsConversionListItem[];

    // 换算列表
    public originConversionList: GsConversionListItem[];

    // 换算列表
    public defaultConcersions: object;

    // 换算列表
    public originDefaultConcersions: object;

    // 换算信息
    public conversionInfo: GsConversionInfoItem[];

    // 名称后缀历史记录 用于标准换算时取消换算 恢复名称时判断
    // 标准换算换算串
    public redArray: string[];
    // 统一换算换算串
    public blackArray: string[];
    public nameSuffixHistory: string[];
    public codeSuffixHistory: string[];
    public appendType: string[];
    // 通过标准换算新增定额的规则ID
    public fromConversionRuleId?: string;

    public standardConvertMod: StandardConvertMod;

    public mainMatConvertMod: boolean;
    //是否执行标准换算
    public isConversion: boolean;

    constructor(constructId: string, unitId: string, sequenceNbr: string, deId: string, standardId: string, type: string, libraryCode: string, conversionList: GsConversionListItem[], originConversionList: GsConversionListItem[], defaultConcersions: object, originDefaultConcersions: object, conversionInfo: GsConversionInfoItem[], redArray: string[], blackArray: string[], nameSuffixHistory: string[], codeSuffixHistory: string[], appendType: string[], fromConversionRuleId: string, standardConvertMod: StandardConvertMod, mainMatConvertMod: boolean, isConversion: boolean) {
        this.constructId = constructId;
        this.unitId = unitId;
        this.sequenceNbr = sequenceNbr;
        this.deId = deId;
        this.standardId = standardId;
        this.type = type;
        this.libraryCode = libraryCode;
        this.conversionList = conversionList;
        this.originConversionList = originConversionList;
        this.defaultConcersions = defaultConcersions;
        this.originDefaultConcersions = originDefaultConcersions;
        this.conversionInfo = conversionInfo;
        this.redArray = redArray;
        this.blackArray = blackArray;
        this.nameSuffixHistory = nameSuffixHistory;
        this.codeSuffixHistory = codeSuffixHistory;
        this.appendType = appendType;
        this.fromConversionRuleId = fromConversionRuleId;
        this.standardConvertMod = standardConvertMod;
        this.mainMatConvertMod = mainMatConvertMod;
        this.isConversion = isConversion;
    }
}
