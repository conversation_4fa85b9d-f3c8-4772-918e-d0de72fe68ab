class RcjCommonConstants {

  static DEFAULT_IFDONORMATERIAL = 0;

  static LEVELMARK_ZERO = '0';

  static MARKSUM_JX = 1;

  static SCOPE_ALL = '0';

  static MARKSUM_BJX = 0;

  static TOTALNUMBER_DEFAULT = 0 ;

  static IFDONORMATERIAL = 1 ;

  static IFDONORMATERIAL_PARTYB = 2 ;

  static IFLOCKPRICE = 1 ;

  static IFLOCKPRICE_DEFAULT = 0 ;

  static MARKETPRICEDIFF = '1';

  static LEVELMARK = '0';

  static LEVELMARK_OTHER = '2';


  static SBFTAXREMOVAL = 11.36;

  static ERROR_MESSAGE = "unqualified";

  static ORIGINALQTY = 0;

  static SOURCEPRICE = "";

  static ISNUMLOCK = false;

  static ISNUMLOCK_TRUE = true;

  static RCJDETAILEDIT = false;

  static RCJDETAILEDIT_TRUE = true;

  static DEFAULT_RESQTY = 0;

  static DEFAULT_RESQTY_ONE = 1;

  static MEUN_TYPE_INIT = 0;

  static MEUN_TYPE_UPDATE = 1;

  static ORDER_ASC ="asc";

  static ORDER_DESC ="desc";

  static SUPPLEMENT_RCJ_FLAG =1;

  static SUPPLEMENT_DE_RCJ_FLAG =1;

  static MATERIALS = "补充材料";
  static MECHANICAL = "补充机械";

  static RCJ_MEMORY_REPLACE = "2";

  static RCJ_MERGE_REPLACE = "0";

  static IF_PROVISIONAL_ESTIMATE = 0 ; //是否是暂估(0:不是，1：是)

  static IS_FYRCJ = 1 ; //(0:不是，1：是)
  static MATERIAL_QT_NON = 0 ; //(0:不是，1：是其他材料，其他机械，2：是调整非，3：是费用人材)

  static GENERAL_FORWARD = 1 ; // 一般
  static SIMPLE_REVERSE = 0 ; // 简易

  static UNIT_PERCENT = '%' ;
  static UNIT_YUAN = '元' ;


}
RcjCommonConstants.toString = () => 'RcjCommonConstants';
module.exports = RcjCommonConstants;