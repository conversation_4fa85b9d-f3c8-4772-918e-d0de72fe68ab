const {ConSoleCommonHandler} = require("../../../electron/console_handle/ConSoleCommonHandler");
const EE = require("../../../core/ee");
const {UPCContext} = require("../../../electron/unit_price_composition/core/UPCContext");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");
const {getOperator} = require("../core/tools/fileOperator/FileOperator");
const {File_TYPE_YGS} = require("../constants/FileOperatorType");
const YGSOperator = require("../core/tools/fileOperator/YGLJOperator");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {Snowflake} = require("../utils/Snowflake");
const YGLJOperator = require("../core/tools/fileOperator/YGLJOperator");
const {ObjectUtils} = require("../utils/ObjectUtils");
const WildcardMap = require("../core/container/WildcardMap");
const ProjectDomain = require("../domains/ProjectDomain");
const DeTypeConstants = require("../constants/DeTypeConstants");
const GljPatch = require("./GljPatch");
const {BaseDe2022} = require("../models/BaseDe2022");
const {GljRcj} = require("../models/GljRcj");
const {BaseRcj2022} = require("../models/BaseRcj2022");
const crypto = require("crypto");


class GLJConSoleHandler extends ConSoleCommonHandler{

    constructor({path}) {
        super({path});
        this.path = path;
    }

    /**
     *文件数据处理
     */
    async fileDataHandle(obj){
        return obj;
    }

    async after(win,obj) {
        //用户的打开历史记录列表数据处理
        ProjectFileUtils.writeUserHistoryListFile(obj);
    }

    /**
     * 打开本地文件
     * @param obj
     */
    async openLocalObj(obj) {
        let projectDomain = null;
        let projectRoot = null;

        // let objStr = JSON.stringify(obj);
        // let projectRootObj = await YGLJOperator.getConstructRoot(objStr);
        let projectRootObj = obj[ProjectDomain.KEY_PROJECT_TREE].find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT && ObjectUtils.isEmpty(item.parentId));
        if (obj.path !== projectRootObj.path) {
            //项目路径改变，重新刷新项目constructId
            // let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomain));
            let data = JSON.stringify(obj);
            //  获取项目id
            // let oldConstructId = await YGLJOperator.getFirstConstructId(data);
            let oldConstructId = obj[ProjectDomain.KEY_PROJECT_TREE].find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT).sequenceNbr;
            // let newConstructId = Snowflake.nextId();
            // let newConstructId = oldConstructId + "1";
            let newConstructId = oldConstructId + await this.hashString(obj.path);
            const regex = new RegExp(oldConstructId, 'g');
            data = data.replace(regex, newConstructId);
            // 尝试解析 JSON 数据
            obj = JSON.parse(data);

            // 补丁
            obj = await GljPatch.processPatch(obj);
            projectDomain = await YGSOperator.destructuringFile(obj);
            projectRoot = projectDomain.getRoot();
        } else {
            // 补丁
            obj = await GljPatch.processPatch(obj);
            projectDomain = await YGSOperator.destructuringFile(obj);
            projectRoot = projectDomain.getRoot();
        }

        let constructId = projectDomain.getRoot().sequenceNbr;
        let unitProjects = projectDomain.getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
        let {service} = EE.app;
        try {
            //查询文件是否有小数点配置数据，无的话取默认重新计算
            if (ObjectUtil.isEmpty(projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING))) {
                await service.gongLiaoJiProject.gljProjectService.repeatCalPrecision(constructId, unitProjects);
            }
            //处理历史文件人材机没有taxRateInit属性，无法判断税率是否修改标红
            await service.gongLiaoJiProject.gljRcjService.dealRcjTaxRateInit(constructId, unitProjects);
            //处理历史文件取费表没有sortNo属性，无法排序
            await service.gongLiaoJiProject.gljBaseFreeRateService.dealQfbSortNoInit(constructId, unitProjects);
            //处理历史文件单位人材机汇总没有调价材料表数据
            await service.gongLiaoJiProject.gljRcjCollectService.calUnitRcjCollectMenuData(constructId, unitProjects);
            // 处理历史文件 标准换算 kind2 显示配合比材料分组不存在
            await service.gongLiaoJiProject.gljRuleDetailFullService.processPatch20250905(constructId, unitProjects);
        } catch (e) {
            console.log("打开文件处理历史项目数据报错");
            console.log(e);
        }

        // 获取单位层级取费表数据， 去除key包含--null取费专业的数据
        let unitQfbMap = projectDomain.functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let freeRateMapUnit = new Map();
        if (ObjectUtils.isNotEmpty(unitQfbMap)) {
            for (let [key, value] of unitQfbMap) {
                if (!key.includes("--null")) {
                    freeRateMapUnit.set(key, value);
                }
            }
        }
        // 更新取费表数据
        projectDomain.functionDataMap.set(FunctionTypeConstants.UNIT_QFB, freeRateMapUnit);

        for (let unitProject of unitProjects) {

            //处理主定额册
            if(ObjectUtils.isEmpty(unitProject.mainDeLibrary)){
                unitProject.mainDeLibrary = unitProject.mainDeLibrary;
            }
            try {
                if (unitProject.qfMajorTypeMoneyMap) {
                    let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));
                    for (let qfMajorType of Array.from(qfMajorTypeMoneyMap.keys())) {
                        await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                            constructId: constructId,
                            unitId: unitProject.sequenceNbr,
                            qfMajorType: qfMajorType
                        });
                    }
                }
            } catch (error) {
                console.error("捕获到异常:", error);
            }
        }

        projectRoot.path = obj.path;

        //获取选中的路径
        let filePath = projectRoot.path;
        //无需返回
        let win = await service.gongLiaoJiProject.gljAppService.newEstimateProject(projectRoot.sequenceNbr);

        //打开后就保存，保证filepath的存在
        // if (!projectRoot.path) {
        //     projectRoot.path = filePath;
        //     let operate = getOperator(File_TYPE_YGS);
        //     await operate.saveByFullPath(projectDomain, filePath);
        // }
        projectRoot.path = filePath;
        let operate = getOperator(File_TYPE_YGS);
        await operate.saveByFullPath(projectDomain, filePath);

        //窗口事件定义
        // this.winEventInit(win, projectRoot);
        projectRoot.constructName = projectRoot.name;
        await this.after(win, projectRoot);
        return win;
    }

    async hashString(str, encoding = 'hex') {
        return crypto.createHash('sha256').update(str).digest(encoding);
    }

}

module.exports = {
    GLJConSoleHandler: GLJConSoleHandler
}
