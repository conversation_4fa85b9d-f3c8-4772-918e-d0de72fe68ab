const sizeof = require('object-sizeof');
const TreeProjectModel = require('../../domains/projectProcessor/models/TreeProjectModel');

class AppContext {
  // 静态的Map用于存储上下文实例
  static #contextMap = new Map();
  // 静态属性用于设置和获取最大容量
  static #maxCapacity;

  static #windowMap = new Map();

  static #idInformation = new Map();
  static #copyBuffer = new Map();

  static getCopyBuffer(){
    return this.#copyBuffer;
  }

  static getIdInformation()
  {
    return this.#idInformation;
  }

  static getWindowMap(){
    return AppContext.#windowMap;
  }
  // 设置最大容量
  static setMaxCapacity(n) {
    if (typeof n !== 'number' || n <= 0) {
      throw new Error('Max capacity must be a positive number.');
    }
    this.#maxCapacity = n;
  }

  // 获取当前容量
  static getCurrentCapacity() {
    return this.#contextMap.size;
  }

  // 添加上下文实例到容器中
  static addContext(projectKey, constructProject) {
    if(!projectKey instanceof String ||  !constructProject instanceof TreeProjectModel)
    {
      throw new Error('Type error for "key" or "constructProjectContext".');
    }
    if (this.getCurrentCapacity() >= this.#maxCapacity) {
      throw new Error('Max capacity reached. Cannot add more contexts.');
    }
    if (this.#contextMap.has(projectKey)) {
      throw new Error(`Context with key '${projectKey}' already exists.`);
    }
    console.log(sizeof(constructProject))
    this.#contextMap.set(projectKey, constructProject);
  }

  // 从容器中获取上下文实例
  static getContext(key) {
    return this.#contextMap.get(key);
  }

  // 从容器中删除上下文实例
  static removeContext(key) {
    this.#contextMap.delete(key);
  }



  // 清空容器中的所有上下文实例
  static clearContexts() {
    this.#contextMap.clear();
  }

  static getAllContexts() {
    return this.#contextMap;
  }
}
module.exports = AppContext;