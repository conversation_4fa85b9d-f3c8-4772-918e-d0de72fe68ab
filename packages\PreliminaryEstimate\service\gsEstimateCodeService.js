const {Service} = require("../../../core");
const gsGsHzFydm = require("../jsonData/gs_gshz_fydm.json");
const {Snowflake} = require("../utils/Snowflake");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {NumberUtil} = require('../utils/NumberUtil');
const {GsEstimateCodePrice} = require("../models/GsEstimateCodePrice");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {getConnection, getRepository, getManager} = require('typeorm');

/**
 * 概算代码
 */
class GsEstimateCodeService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取概算费用代码列表
     * @param args
     */
    async getEstimateCodeList(args) {
        let {constructId} = args;
        return ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_CODE);
    }

    /**
     * 计算概算费用代码
     * @param args
     * @returns {any[]}
     */
    async countEstimateCode(args) {
        let {constructId} = args;
        // 获取当前工程项目下所有的单位
        let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.ESTIMATE_COST.je;

        let jagcf = 0, jzgcf = 0, azgcf = 0;
        // 获取该项目的所有单位工程
        for (let i in unitProjects) {
            let unitProject = unitProjects[i];
            // 建筑工程费 = ∑各建筑单位工程造价合价
            if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                jzgcf = NumberUtil.add(jzgcf, NumberUtil.numberScale(unitProject.projectCost, je));
            }
            // 安装工程费 = ∑各安装单位工程造价合价
            if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                azgcf = NumberUtil.add(azgcf, NumberUtil.numberScale(unitProject.projectCost, je));
            }
            // 建安工程费
            jagcf = NumberUtil.add(jagcf, NumberUtil.numberScale(unitProject.projectCost, je));
        }
        // 建安工程费 = ∑各建筑单位工程造价合价+各安装单位工程造价合价
        // jagcf = NumberUtil.addParams(jagcf, jzgcf, azgcf);
        // 设备费 = ∑国内采购设备费合价+国外采购设备费合价
        // let sbf = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY)
        //     .get(this.service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null, "SBF"));
        // 整个项目的总工程造价值
        let unitSbf = 0;
        for (let j = 0; j < unitProjects.length; j++) {
            let unitProject = unitProjects[j];
            // 获取该单位的费用汇总
            let param = {
                constructId: constructId,
                singleId: unitProject.parentId,
                unitId: unitProject.sequenceNbr
            }
            let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);
            unitSbf = NumberUtil.add(unitSbf, NumberUtil.numberScale(unitCostSummaryPriceMap.get("设备费"), je));
        }
        let sbf = unitSbf;

        // 设备购置费 = ∑设备购置费汇总合计
        let equipmentCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY)
            .get(this.service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ));
        let sbgzf = NumberUtil.numberScale(equipmentCosts[0].price, je);

        // 建设其他费	 = ∑建设其他费用汇总
        let jsqtf;
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        if (ObjectUtils.isNotEmpty(otherProjectCosts)) {
            jsqtf = NumberUtil.numberScale(otherProjectCosts[0].amount, je);
        } else {
            jsqtf = 0;
        }

        // 获取概算费用代码
        let estimateCodes = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_CODE);
        estimateCodes[0].price = ObjectUtils.isEmpty(jagcf) ? 0 : jagcf;
        estimateCodes[1].price = ObjectUtils.isEmpty(jzgcf) ? 0 : jzgcf;
        estimateCodes[2].price = ObjectUtils.isEmpty(azgcf) ? 0 : azgcf;
        estimateCodes[3].price = ObjectUtils.isEmpty(sbf) ? 0 : sbf;
        estimateCodes[4].price = ObjectUtils.isEmpty(sbgzf) ? 0 : sbgzf;
        estimateCodes[5].price = ObjectUtils.isEmpty(jsqtf) ? 0 : jsqtf;

        // 更新工程项目的概算代码
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_GS_CODE, estimateCodes);

        // // 获取概算汇总
        // let gsEstimateSummarys = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_GS_SUMMARY);
        //
        // //调用计算概算汇总
        // this.service.PreliminaryEstimate.gsEstimateSummaryService.countEstimateSummary(constructId, gsEstimateSummarys, estimateCodes);

        return estimateCodes;
    }

    /**
     * 初始化概算费用代码列表
     * @param args
     */
    defaultEstimateCode(args) {
        let gsEstimateCodePrices = [];
        // 概算汇总模板
        for (let i = 0; i < gsGsHzFydm.length; i++) {
            let gshzFydm = gsGsHzFydm[i];
            let gsEstimateCodePrice = new GsEstimateCodePrice();
            ConvertUtil.setDstBySrc(gshzFydm, gsEstimateCodePrice);
            gsEstimateCodePrice.sequenceNbr = Snowflake.nextId();
            gsEstimateCodePrices.push(gsEstimateCodePrice);
        }
        return gsEstimateCodePrices;
    }


}

GsEstimateCodeService.toString = () => '[class GsEstimateCodeService]';
module.exports = GsEstimateCodeService;