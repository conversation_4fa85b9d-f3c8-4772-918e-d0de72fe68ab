const {Controller} = require('../../../core');
const CommonConstants = require("../constants/CommonConstants");
const {<PERSON>rowserWindow, dialog} = require('electron');
const {ResponseData} = require("../utils/ResponseData");
const FileOperator = require("../core/tools/fileOperator/FileOperator");
const FileOperatorType = require("../constants/FileOperatorType");
const {ObjectUtils} = require("../utils/ObjectUtils");
const XLSX = require('xlsx');
const Excel = require('exceljs');
const {ConvertUtil} = require("../utils/ConvertUtils");
const AppContext = require("../core/container/APPContext");
const {promises: fsp} = require("fs");
const ProjectDomain = require("../domains/ProjectDomain");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const DeTypeConstants = require("../constants/DeTypeConstants");
const PropertyUtil = require("../domains/utils/PropertyUtil");
const BaseDomain = require("../domains/core/BaseDomain");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");


class GsDeImportController extends Controller {

    constructor(ctx) {
        super(ctx);
    }


    async queryFbChild(args) {
        let {importConstructId, importSingleId, importUnitId} = args;
        let deList = ProjectDomain.getDomain(importConstructId).getDeDomain().getDeTree(item => item.unitId === importUnitId);
        let filter = deList.filter(p => p.type === DeTypeConstants.DE_TYPE_FB || p.type === DeTypeConstants.DE_TYPE_ZFB);

        let resultFbZfbList = [];
        if (ObjectUtils.isNotEmpty(filter)) {
            resultFbZfbList = filter.filter(oo => ObjectUtils.isEmpty(oo.isTempRemove) || oo.isTempRemove === CommonConstants.COMMON_NO);
        }

        return ResponseData.success(resultFbZfbList);
    }


    /**
     * 导入工程
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async importYgs(args) {
        let {constructId, unitId} = args;
        let defaultStoragePath = await this.service.PreliminaryEstimate.gsAppService.getSetStoragePath(null);
        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [CommonConstants.GAISUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return ResponseData.fail('未选中任何文件');
        }
        //获取选中的路径
        let filePath = result[0];
        let ygsOperator = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);

        // let data = await fsp.readFile(filePath, 'utf8');
        // // 尝试解析 JSON 数据
        // const parsedData = JSON.parse(data);
        let parsedData = await PricingFileFindUtils.getProjectObjByPath(filePath);
        let constructIdImport = parsedData.ProjectTree[0].sequenceNbr;     //导入文件的项目
        let contextMap = AppContext.getAllContexts();   //当前窗口打开的项目
        if (contextMap.has(constructIdImport)) {
            if (ObjectUtils.isNotEmpty(contextMap.get(constructIdImport).ctx.treeProject.root)) {
                return ResponseData.fail('不支持导入正在使用中的工程文件');
            }
        }

        let fileProjectDomain = await ygsOperator.openFile(filePath, false);
        if (fileProjectDomain) {
            let projectTreeResult = [];
            fileProjectDomain.getRoot().path = filePath;
            let constructId = fileProjectDomain.getRoot().sequenceNbr;
            let projectTree = fileProjectDomain.getProjectTree();
            if (ObjectUtils.isNotEmpty(projectTree)) {
                let projectTreeCopy = ConvertUtil.deepCopy(projectTree);
                for (let item of projectTreeCopy) {
                    item.select = false;
                    projectTreeResult.push(item);
                }
            }
            return ResponseData.success(projectTreeResult);
        } else {
            return ResponseData.fail('导入失败');
        }
    }


    /**
     *
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async importYgsDeCancel(args) {
        //中途取消导入，清空内存，不然下次没法导入
        AppContext.removeContext(args.importConstructId);
        return ResponseData.success(true);
    }


    async importYgsDe(args) {
        let result = ResponseData.success(true);//默认成功
        try {
            result = await this.service.PreliminaryEstimate.gsDeImportService.importYgsDe(args);
            if (args.importConstructId !== args.constructId) {
                //防止自己导入自己后内存数据清空
                AppContext.removeContext(args.importConstructId);
            }
        } catch (e) {
            if (args.importConstructId !== args.constructId) {
                //防止自己导入自己后内存数据清空
                AppContext.removeContext(args.importConstructId);
            }
            console.log("导入失败：" + e.message);
        }
        return result;
    }


    /**
     * 导入excel
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async importExcel(args) {
        let defaultStoragePath = await this.service.PreliminaryEstimate.gsAppService.getSetStoragePath(null);

        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: 'excel', extensions: ["xls", "xlsx"]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return ResponseData.fail('未选中任何文件');
        }
        //获取选中的路径
        let filePath = result[0];
        const workbookHeight = new Excel.Workbook();
        await workbookHeight.xlsx.readFile(filePath);

        const workbook = XLSX.readFile(filePath);
        const sheetNames = workbook.SheetNames;
        let result1 = [];
        for (let i = 0; i < sheetNames.length; i++) {
            let item = sheetNames[i];
            const sheet = workbook.Sheets[item];
            const data = XLSX.utils.sheet_to_json(sheet, {header: 1, raw: true, defval: ''});
            const mergedRanges = sheet['!merges'];  // 获取合并单元格的范围信息
            let rowHeightList = [];       //行高
            let columnWidthList = [];       //列宽
            workbookHeight.worksheets[i].eachRow({includeEmpty: true}, (row, rowNumber) => {
                let hanggao = {};
                hanggao.rowNumber = rowNumber;
                hanggao.height = row.height;
                rowHeightList.push(hanggao);
            });
            workbookHeight.worksheets[i].columns.forEach((column) => {
                let liekuan = {};
                liekuan.colNumber = column._number;
                liekuan.width = column.width;
                columnWidthList.push(liekuan);
            });

            let sheetData = {};
            sheetData.sheetName = item;
            sheetData.data = data;
            sheetData.mergedRanges = mergedRanges;
            sheetData.rowHeightList = rowHeightList;
            sheetData.columnWidthList = columnWidthList;
            result1.push(sheetData);
        }

        return ResponseData.success(result1);
    }

    async importExcelDe(args) {
        let {constructId, singleId, unitId, deList} = args;

        await this.service.PreliminaryEstimate.gsDeImportService.importExcelDe(args);
        return ResponseData.success(true);

    }


}

GsDeImportController.toString = () => 'gsDeImportController';
module.exports = GsDeImportController;
