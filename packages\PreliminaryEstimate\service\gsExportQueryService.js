const {Service} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const ExportSheetNameEnum = require("../enums/GSExportSheetNameEnum");
const ProjectDomain = require("../domains/ProjectDomain");
const {GSExcelUtil} = require("../utils/GSExcelUtil.js");
const {ShenHeWriteExcelBySheetUtil} = require("../utils/GSWriteExcelBySheetUtil.js");
const path = require('path');
const {NumberUtil} = require("../utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const UtilsPs = require('../../../core/ps');
const AdmZip = require('adm-zip');
const fs = require('fs');
const {
    app: electronApp, dialog, shell, BrowserView, Notification, powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const projectLevelConstant = require("../constants/ProjectLevelConstant");
const {map} = require("rxjs");
const Decimal = require("decimal.js");

const ProjectLevelConstant = require("../constants/ProjectLevelConstant");
const DeTypeConstants = require("../constants/DeTypeConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const GSExportSheetNameEnum = require("../enums/GSExportSheetNameEnum");
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const {GsExportSettingUtil} = require('../utils/GsExportSettingUtil');
const UnitUtils = require('../core/tools/UnitUtils');
const ResourceKindConstants = require('../constants/ResourceKindConstants');
const {GsConstructProjectRcj} = require('../models/GsConstructProjectRcj');

class GsExportQueryService extends Service {
    constructor(ctx) {
        super(ctx);
    }

    //展示报表查看的表名列表
    async showExportHeadLine(itemLevel, args) {
        //存放大栏目下的表的表名
        let gongChengLiangList = ExportSheetNameEnum.概算报表.filter(function (element) {
            if (element.projectLevel == itemLevel) return element;
        });

        if (ObjectUtils.isNotEmpty(args.unitId)) {
            gongChengLiangList = await this.calUnitZyhz(args.constructId, args.singleId, args.unitId, gongChengLiangList);
        }

        return gongChengLiangList;
    }


    async calUnitZyhz(constructId, singleId, unitId, gongChengLiangList) {
        //todo 计算是否进行了多专业汇总 如果汇总则显示两个专业sheet，未汇总则只显示主专业
        let args = {};
        args.constructId = constructId;
        args.singleId = singleId;
        args.unitId = unitId;
        args.type = 1;
        args.levelType = projectLevelConstant.unit;
        args.code = "8";
        let majorMenuList = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getCostSummaryMajorMenuList(args);
        if (ObjectUtils.isNotEmpty(majorMenuList)) {
            if (majorMenuList.itemList.length === 1) {
                gongChengLiangList = gongChengLiangList.filter(o => o.headLine != "单位工程费用表(安装工程)" && o.headLine != "单位工程费用表(建筑工程)");
                //未进行专业汇总，只显示一个主专业
                // let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
                // if (ObjectUtils.isNotEmpty(unit) && ObjectUtils.isNotEmpty(unit.constructMajorType)) {
                //     let JZGC = unit.constructMajorType.includes("JZGC");    //主专业是建筑
                //     let AZGC = unit.constructMajorType.includes("AZGC");    //主专业是安装
                //     if (JZGC) {
                //         gongChengLiangList = gongChengLiangList.filter(o => o.headLine != "单位工程费用表(安装工程)");
                //     }
                //     if (AZGC) {
                //         gongChengLiangList = gongChengLiangList.filter(o => o.headLine != "单位工程费用表(建筑工程)");
                //     }
                // }
            } else if (majorMenuList.itemList.length > 1) {

            }
        } else {
            gongChengLiangList = gongChengLiangList.filter(o => o.headLine != "单位工程费用表(安装工程)" && o.headLine != "单位工程费用表(建筑工程)");
            //未进行专业汇总，只显示一个主专业
            // let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            // if (ObjectUtils.isNotEmpty(unit) && ObjectUtils.isNotEmpty(unit.constructMajorType)) {
            //     let JZGC = unit.constructMajorType.includes("JZGC");    //主专业是建筑
            //     let AZGC = unit.constructMajorType.includes("AZGC");    //主专业是安装
            //     if (JZGC) {
            //         gongChengLiangList = gongChengLiangList.filter(o => o.headLine != "单位工程费用表(安装工程)");
            //     }
            //     if (AZGC) {
            //         gongChengLiangList = gongChengLiangList.filter(o => o.headLine != "单位工程费用表(建筑工程)");
            //     }
            // }
        }
        return gongChengLiangList;
    }

    async calUnitSheetZyhz(constructId, unitId, singleId, worksheet) {
        let args = {};
        args.constructId = constructId;
        args.singleId = singleId;
        args.unitId = unitId;
        args.type = 1;
        args.levelType = projectLevelConstant.unit;
        args.code = "8";
        let majorMenuList = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getCostSummaryMajorMenuList(args);
        if (ObjectUtils.isNotEmpty(majorMenuList)) {
            if (majorMenuList.itemList.length === 1) {
                worksheet.removeWorksheet("单位工程费用表(安装工程)");
                worksheet.removeWorksheet("单位工程费用表(建筑工程)");
                //未进行专业汇总，只显示一个主专业
                // let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
                // if (ObjectUtils.isNotEmpty(unit) && ObjectUtils.isNotEmpty(unit.constructMajorType)) {
                //     let JZGC = unit.constructMajorType.includes("JZGC");    //主专业是建筑
                //     let AZGC = unit.constructMajorType.includes("AZGC");    //主专业是安装
                //     if (JZGC) {
                //         worksheet.removeWorksheet("单位工程费用表(安装工程)");
                //     }
                //     if (AZGC) {
                //         worksheet.removeWorksheet("单位工程费用表(建筑工程)");
                //     }
                // }
            } else if (majorMenuList.itemList.length > 1) {

            }
        } else {
            worksheet.removeWorksheet("单位工程费用表(安装工程)");
            worksheet.removeWorksheet("单位工程费用表(建筑工程)");
            //未进行专业汇总，只显示一个主专业
            // let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            // if (ObjectUtils.isNotEmpty(unit) && ObjectUtils.isNotEmpty(unit.constructMajorType)) {
            //     let JZGC = unit.constructMajorType.includes("JZGC");    //主专业是建筑
            //     let AZGC = unit.constructMajorType.includes("AZGC");    //主专业是安装
            //     if (JZGC) {
            //         worksheet.removeWorksheet("单位工程费用表(安装工程)");
            //     }
            //     if (AZGC) {
            //         worksheet.removeWorksheet("单位工程费用表(建筑工程)");
            //     }
            // }
        }
    }


    async showSheetStyle(itemLevel, sheetName, args) {
        let shenhe = this.getProjectRootPath() + "\\excelTemplate\\gs";
        if (itemLevel == ProjectLevelConstant.construct) {
            shenhe = shenhe + "\\工程项目层级.xlsx";
        } else if (itemLevel == ProjectLevelConstant.single) {
            shenhe = shenhe + "\\单项工程层级.xlsx";
        } else if (itemLevel == ProjectLevelConstant.unit) {
            shenhe = shenhe + "\\单位工程层级.xlsx";
        }
        let loadPath = shenhe;

        let workbook = await GSExcelUtil.readToWorkBook(loadPath);
        args["workbook"] = workbook;
        try {
            await this.switchWorkSheet(itemLevel, workbook.getWorksheet(sheetName), args);
        } catch (e) {
            console.log("报表填充数据异常");
        }
        let result;
        try {
            result = await GSExcelUtil.findCellStyleList(workbook.getWorksheet(sheetName));
        } catch (e) {
            console.log("报表填充数据异常");
        }
        return result;
    }

    async queryLanMuData(constructId) {
        let project = [];
        let single = [];
        let unit = [];


        let construct = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        const treeList = ProjectDomain.getDomain(constructId).getProjectTree();
        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum.概算报表, projectLevelConstant.construct, project);
        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum.概算报表, projectLevelConstant.single, single);
        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum.概算报表, projectLevelConstant.unit, unit);

        let result = {};
        let newProject = await this.deepCopy(project);
        result["headLine"] = construct.name;
        result["childrenList"] = newProject;
        result["id"] = construct.sequenceNbr;
        result["constructId"] = construct.sequenceNbr;
        result["levelType"] = construct.type;

        // let unitList = ProjectDomain.getUnitList(constructId);
        let unitList = treeList.filter(function (element) {
            if (element.type == 3) return element;
        });

        // let singleProjectList = ProjectDomain.getSingleProjectList(constructId);
        let singleProjectList = treeList.filter(function (element) {
            if (element.type == 2) return element;
        });

        for (let i = 0; i < singleProjectList.length; i++) {
            let singleObject = {};
            let singleChildrenList = await this.deepCopy(single);
            singleObject["headLine"] = singleProjectList[i].name;
            singleObject["childrenList"] = singleChildrenList;
            singleObject["id"] = singleProjectList[i].sequenceNbr;
            singleObject["singleId"] = singleProjectList[i].sequenceNbr;
            singleObject["levelType"] = singleProjectList[i].type;
            newProject.push(singleObject);

            let filter = unitList.filter(function (element) {
                if (element.parentId == singleProjectList[i].sequenceNbr) return element;
            });
            for (let j = 0; j < filter.length; j++) {
                let unitObject = {};
                unitObject["headLine"] = filter[j].name;
                unitObject["childrenList"] = await this.deepCopy(unit);//虽然返回新的数组  但里面的对象重复使用
                unitObject["id"] = filter[j].sequenceNbr;
                unitObject["unitId"] = filter[j].sequenceNbr;
                unitObject['levelType'] = filter[j].type;
                unitObject['singleId'] = filter[j].parentId;

                unitObject["childrenList"] = await this.calUnitZyhz(result["constructId"], singleObject["singleId"], unitObject["unitId"], unitObject["childrenList"]);

                singleChildrenList.push(unitObject);
            }
        }
        //对result进行递归遍历  增加唯一序号
        let object = {};
        object['id'] = 1;
        await this.addOrderNum(result, object);

        return result;
    }

    async addOrderNum(result, object) {
        if (result['id'] == null) {
            result['id'] = object.id++;
        }
        if (result.childrenList != null) {
            for (let i = 0; i < result.childrenList.length; i++) {
                await this.addOrderNum(result.childrenList[i], object);
            }
        }
    }

    async deepCopy(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        let clone = Array.isArray(obj) ? [] : {};

        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                clone[key] = await this.deepCopy(obj[key]);
            }
        }

        return clone;
    }

    async exportExcel(params) {
        // let {constructName} = params.headLine;
        // let defaultStoragePath = ProjectDomain.getDefaultStoragePath(constructName);
        const dialogOptions = {
            title: '保存文件', defaultPath: params.headLine, filters: [{name: 'zip', extensions: ['zip']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.exportExcelZip(params, filePath);
            return true;
            // this.service.shenHeYuSuanProject.systemService.openWindowForProject(constructName,sequenceNbr);
        } else {
            return false;
        }
    }

    async exportExcelZip(params, filePath) {

        let project = await this.initWorkBook(projectLevelConstant.construct);
        let single = await this.initWorkBook(projectLevelConstant.single);
        let unit = await this.initWorkBook(projectLevelConstant.unit);

        let fileDir = this.getProjectRootPath() + "\\excelTemplate\\gs\\" + params.headLine;
        let args = {};
        args['constructId'] = params.id;
        await this.parseParams(params, project, single, unit, fileDir, args);
        //对 对应的目录进行压缩 生成zip文件
        // 创建一个新的 Zip 实例
        const zip = new AdmZip();

        // 递归遍历目录及其子目录中的文件和子目录
        function traverseDirectory(dirPath, relativePath = '') {
            const files = fs.readdirSync(dirPath);

            files.forEach(file => {
                const filePath = path.join(dirPath, file);
                const stats = fs.statSync(filePath);

                if (stats.isDirectory()) {
                    const fileRelativeNext = path.join(relativePath, file);
                    zip.addFile(fileRelativeNext + '/', Buffer.alloc(0), '', 493); // 添加空文件夹
                    traverseDirectory(filePath, fileRelativeNext);
                } else {
                    zip.addLocalFile(filePath, relativePath);
                }
            });
        }

        traverseDirectory(fileDir, params.headLine);
        // 将 zip 文件保存到指定路径
        zip.writeZip(filePath);

        function deleteDirectory(dirPath) {
            if (fs.existsSync(dirPath)) {
                fs.readdirSync(dirPath).forEach(file => {
                    const filePath = path.join(dirPath, file);

                    if (fs.lstatSync(filePath).isDirectory()) {
                        deleteDirectory(filePath); // 递归删除子目录
                    } else {
                        fs.unlinkSync(filePath); // 删除文件
                    }
                });

                fs.rmdirSync(dirPath); // 删除空目录
                console.log('目录删除成功');
            } else {
                console.log('目录不存在');
            }
        }

        deleteDirectory(fileDir);
    }

    async parseParams(params, project, single, unit, fileDir, args) {
        if (args == null) {
            args = {};
        }

        for (let i = 0; i < params.childrenList.length; i++) {
            let param = params.childrenList[i];
            //如果为总工程层级
            if (param.projectLevel != null && param.projectLevel == projectLevelConstant.construct) {
                args["constructId"] = params.id;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(project, param.projectLevel, param.headLine, args);
                } else {
                    project.removeWorksheet(param.headLine);
                }
                if (project.worksheets.length == 1 && project.worksheets[0].name == "格式替换sheet") {
                    project.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == projectLevelConstant.single) {
                args["singleId"] = params.id;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(single, param.projectLevel, param.headLine, args);
                } else {
                    single.removeWorksheet(param.headLine);
                }
                if (single.worksheets.length == 1 && single.worksheets[0].name == "格式替换sheet") {
                    single.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == projectLevelConstant.unit) {
                args["unitId"] = params.id;
                args["singleId"] = params.singleId;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(unit, param.projectLevel, param.headLine, args);
                } else {
                    unit.removeWorksheet(param.headLine);
                }
                if (unit.worksheets.length == 1 && unit.worksheets[0].name == "格式替换sheet") {
                    unit.removeWorksheet("格式替换sheet");
                }
            }
        }

        //针对不同的workbook 生成该一层级的excel文件
        let filename = fileDir + "\\" + params.headLine + ".xlsx";

        // 创建目录
        function createDirectory(directoryPath) {
            if (!fs.existsSync(directoryPath)) {
                fs.mkdirSync(directoryPath, {recursive: true});
                console.log('目录已创建');
            } else {
                console.log('目录已存在');
            }
        }

        if (params.childrenList != null && params.childrenList[0].projectLevel == projectLevelConstant.construct) {
            project.removeWorksheet("格式替换sheet");
            let projectTableList = params.childrenList.filter(o => ObjectUtils.isNotEmpty(o.projectLevel) && o.projectLevel === 1 && o.selected === true);
            if (ObjectUtils.isNotEmpty(projectTableList)) {
                await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet3(projectTableList, project.getWorksheet("A.0.3 目录"));  //写入工程级别目录数据
            } else {
                project.removeWorksheet("A.0.3 目录");
            }
            if (project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                createDirectory(fileDir);
                await project.xlsx.writeFile(filename);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == projectLevelConstant.single) {
            single.removeWorksheet("格式替换sheet");
            if (single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                createDirectory(fileDir);
                await single.xlsx.writeFile(filename);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == projectLevelConstant.unit) {
            unit.removeWorksheet("格式替换sheet");
            await this.calUnitSheetZyhz(args.constructId, args.singleId, params.unitId, unit);  //判断是否专业汇总去除sheet
            if (unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                createDirectory(fileDir);
                await unit.xlsx.writeFile(filename);
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList != null);//含有子节点的节点
        if (filter != null) {
            let directory;
            for (let i = 0; i < filter.length; i++) {
                //同时对single  和 unit对象进行初始化
                single = await this.initWorkBook(projectLevelConstant.single);
                unit = await this.initWorkBook(projectLevelConstant.unit);
                directory = fileDir + "\\" + filter[i].headLine;
                await this.parseParams(filter[i], project, single, unit, directory, args);
            }
        }
    }

    async initWorkBook(projectLevel) {
        let loadDir = this.getProjectRootPath() + "\\excelTemplate\\gs";
        let loadPath = "";

        if (projectLevel == projectLevelConstant.construct) {
            loadPath = loadDir + "\\工程项目层级.xlsx";
        } else if (projectLevel == projectLevelConstant.single) {
            loadPath = loadDir + "\\单项工程层级.xlsx";
        } else if (projectLevel == projectLevelConstant.unit) {
            loadPath = loadDir + "\\单位工程层级.xlsx";
        }

        //加载workbook
        let workbook = await GSExcelUtil.readToWorkBook(loadPath);
        return workbook;
    }

    async getWorkSheetWithData(workbook, projectType, sheetName, args) {
        let worksheet = workbook.getWorksheet(sheetName);
        args["workbook"] = workbook;
        try {
            await this.switchWorkSheet(projectType, worksheet, args);
        } catch (e) {
            console.log("报表填充数据异常" + sheetName);
        }
        return worksheet;
    }

    async dealWithForSinglePageWhenExport(workSheet, workbook, headArgs) {
        let headStartNum = 0;
        let headEndNum = 0;
        if (headArgs != null) {
            headStartNum = headArgs['headStartNum'];
            headEndNum = headArgs['headEndNum'];
            if (headArgs['titlePage'] == null) {
                headArgs['titlePage'] = false;//默认为 数据页
            }
        } else {
            headArgs = {};
            headStartNum = 1;
            headEndNum = 4;
            headArgs['headStartNum'] = headStartNum;
            headArgs['headEndNum'] = headEndNum;
            headArgs['titlePage'] = false;//默认为 数据页
        }

        //1、复制表头
        //2、进行 行高自适应的处理 确定行高后  进行分页
        //10号字体
        // 在该行下方插入一个分页符
        //A4 行高 721.5   宽度
        // let marginLeft = ;//左边距
        //得到每一个cell的宽度比例 并计入map
        await GSExcelUtil.getRatioWidthSheet(workSheet);


        let mergeMap = new Map(Object.entries(workSheet._merges));
        let fontSize = 13;
        //行高自适应  如果所需行高大于1行  则置为两行
        for (let i = headEndNum + 1; i <= workSheet._rows.length; i++) {
            let minHeight = 0;
            let fitRight = true;//这里预设为false 就会保留初始模板的空白行高度 为true针对空白行统统高度为0

            for (let j = 0; j < workSheet.getRow(i)._cells.length; j++) {
                let cell = workSheet.getRow(i)._cells[j];
                let celltextValue = cell.model.value;
                if (!celltextValue) {
                    continue;
                }
                fitRight = true;
                if (typeof celltextValue === 'number') {
                    celltextValue = String(celltextValue);
                }
                let contents;
                try {
                    contents = celltextValue.split("\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
                } catch (e) {
                    console.log("");
                }
                let mergeName = GSExcelUtil.getMergeName(workSheet._merges, cell);
                let mergeLength = 0;//得到该cell的宽度大小
                if (mergeName != null) {
                    let value = mergeMap.get(mergeName).model;
                    for (let m = value.left; m <= value.right; m++) {
                        mergeLength += workSheet.getRow(i)._cells[m - 1]._column.width;
                    }
                } else {
                    mergeLength = cell._column.width;
                }
                // let rowWordNum = Math.trunc(mergeLengthRatio * ExcelEnum.A4Width / ((fontSize / 72) * 10)) //每一列能够存放的字数
                //这若为0  会造成递归死循环
                let rowWordNum = Math.trunc(mergeLength / ((fontSize / 72) * 10)) //每一列能够存放的字数
                let rowSpace = 2;//行间距
                let rowNumTotal = 0;
                for (let j = 0; j < contents.length; j++) {
                    let rowText = contents[j];
                    if (!rowText && rowText.length == 0) {
                        continue;
                    }
                    // "垂直运输费 ±0.00以下 四层以内"  类似这种问题 在excel中会展示三行 实际计算是两行  18/9 此时满除就多加一行
                    let rowNum = Math.ceil(rowText.length / rowWordNum);
                    //优化处理  如果单行字数超过五  考虑到单元格的两侧边界距离  实际每行能存放的字数进行减二
                    if (rowNum >= 2 && rowNum * rowWordNum == rowText.length) {
                        rowNum++;
                    }
                    rowNumTotal += rowNum;
                }
                if (rowNumTotal > 2) {
                    rowNumTotal = 2;  //最大置为两行
                }
                let newMinHeight = ((fontSize) + rowSpace) * rowNumTotal + 8;   //计算出该Cell列 的最小适应高度  加4是上下的总共边距

                if (minHeight < newMinHeight) {
                    minHeight = newMinHeight; //得到该行的最大行高
                }
            }
            if (fitRight) {
                workSheet.getRow(i).height = minHeight;
            }
        }
        //分页处理
        // await workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
        if (!headArgs['titlePage']) {  //如果不是扉页
            let totalPage = await GSExcelUtil.pageSplit(workSheet, 1, headArgs, 0);
            /*****************************************/
            //对页码显示进行处理
            let cellList = GSExcelUtil.findContainValueCell(workSheet, "第 1 页  共 1 页");
            if (cellList.length == 0) {
                cellList = GSExcelUtil.findContainValueCell(workSheet, "第 1 页 共 1 页");//横版是如此格式
            }
            const grouped = cellList.reduce((result, obj) => {
                const key = obj.cell._row._number;
                if (!result[key]) {
                    result[key] = [];
                }
                result[key].push(obj.cell);
                return result;
            }, {});
            let mergeMap = new Map(Object.entries(grouped));
            let count = 0;
            for (let [key, value] of mergeMap) {
                count++;
                let str = "第 " + (count) + " 页 共 " + totalPage + " 页";
                for (let i = 0; i < value.length; i++) {
                    let elementCell = value[i];
                    elementCell.value = str;
                }
            }
            /*****************对空白行的处理********************************/
            //要求空白行只能是在末尾页  而不是在页中  否则逻辑出错
            // let blankRowList = await this.getBlankRow(workSheet);
            await GSExcelUtil.dealWithBlankRow(workSheet, headArgs);
        }
    }

    async switchWorkSheet(projectType, worksheet, args) {
        if (projectType == ProjectLevelConstant.construct) {
            let constructProjectJBXX = await this.getconstructProjectJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //工程项目层级
                case "A.0.1 封面":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet1(constructProjectJBXX, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 7;
                    headArgsQd1['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    break;
                case "A.0.2 签署页":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet2(constructProjectJBXX, worksheet);
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 8;
                    headArgsQd2['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    break;
                case "A.0.3 目录":
                    // let projectTableList = ExportSheetNameEnum.概算报表.filter(function (element) {
                    //     if (element.projectLevel == 1) return element;
                    // });
                    // await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet3(projectTableList, worksheet);
                    // let headArgsQd3 = {};
                    // headArgsQd3['headStartNum'] = 1;
                    // headArgsQd3['headEndNum'] = 11;
                    // headArgsQd3['titlePage'] = true;
                    // await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    // break;
                case "A.0.4 编制说明":
                    let bzsm = constructProjectJBXX.filter(object => object.name == "编制说明")[0];
                    if (ObjectUtils.isNotEmpty(bzsm) && ObjectUtils.isNotEmpty(bzsm.remark)) {
                        let remark = await GSExcelUtil.removeTags(bzsm.remark);
                        await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet4(remark, worksheet);
                    }
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 2;
                    headArgsQd4['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    break;
                case "B.0.1 总概算表":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet5(constructProjectJBXX, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 7;
                    headArgsQd5['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    break;
                case "B.0.2 总概算表":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet6(constructProjectJBXX, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 7;
                    headArgsQd6['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    break;
                case "B.0.3 其他费用计算表":
                    let constructProjectSheet7List = await this.getconstructProjectSheet7List(args);
                    constructProjectJBXX["projectQtfyjsbHeji"] = args["projectQtfyjsbHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet7(constructProjectJBXX, constructProjectSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 3;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell7 = GSExcelUtil.findValueCell(worksheet, "合计");
                    let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(13, row7.number + 1, worksheet, workSheetGeshi, 4, 8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.5 概算汇总表":
                    let constructProjectSheet8List = await this.getconstructProjectSheet8List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet8(constructProjectJBXX, constructProjectSheet8List, worksheet);
                    let headArgsQd8 = {};
                    headArgsQd8['headStartNum'] = 1;
                    headArgsQd8['headEndNum'] = 4;
                    headArgsQd8['titlePage'] = false;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.6 概算汇总表（含工程建设其他费细项）":
                    let constructProjectSheet9List = await this.getconstructProjectSheet9List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet9(constructProjectJBXX, constructProjectSheet9List, worksheet);
                    let headArgsQd9 = {};
                    headArgsQd9['headStartNum'] = 1;
                    headArgsQd9['headEndNum'] = 4;
                    headArgsQd9['titlePage'] = false;
                    headArgsQd9['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.7 概算汇总表（金额为0不输出）":
                    let constructProjectSheet10List = await this.getconstructProjectSheet10List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet10(constructProjectJBXX, constructProjectSheet10List, worksheet);
                    let headArgsQd10 = {};
                    headArgsQd10['headStartNum'] = 1;
                    headArgsQd10['headEndNum'] = 4;
                    headArgsQd10['titlePage'] = false;
                    headArgsQd10['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.8 概算汇总表（万元）":
                    let constructProjectSheet11List = await this.getconstructProjectSheet11List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet11(constructProjectJBXX, constructProjectSheet11List, worksheet);
                    let headArgsQd11 = {};
                    headArgsQd11['headStartNum'] = 1;
                    headArgsQd11['headEndNum'] = 4;
                    headArgsQd11['titlePage'] = false;
                    headArgsQd11['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd11);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.9 工程项目材料数量及价格表":
                    let constructProjectSheet12List = await this.getconstructProjectSheet12List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet12(constructProjectJBXX, constructProjectSheet12List, worksheet);
                    let headArgsQd12 = {};
                    headArgsQd12['headStartNum'] = 1;
                    headArgsQd12['headEndNum'] = 3;
                    headArgsQd12['titlePage'] = false;
                    headArgsQd12['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd12);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(23, worksheet._rows.length + 1, worksheet, workSheetGeshi, 4, 8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.10 总概算对比表":
                    let constructProjectSheet13List = await this.getconstructProjectSheet13List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet13(constructProjectJBXX, constructProjectSheet13List, worksheet);
                    let headArgsQd13 = {};
                    headArgsQd13['headStartNum'] = 1;
                    headArgsQd13['headEndNum'] = 4;
                    headArgsQd13['titlePage'] = false;
                    headArgsQd13['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(25, worksheet._rows.length + 1, worksheet, workSheetGeshi, 7, 14);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.12 进口设备材料货价及从属费用计算表":
                    let constructProjectSheet14List = await this.getconstructProjectSheet14List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet14(constructProjectJBXX, constructProjectSheet14List, worksheet);
                    let headArgsQd14 = {};
                    headArgsQd14['headStartNum'] = 1;
                    headArgsQd14['headEndNum'] = 4;
                    headArgsQd14['titlePage'] = false;
                    headArgsQd14['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd14);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(27, worksheet._rows.length + 1, worksheet, workSheetGeshi, 7, 13);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    if (ObjectUtils.isNotEmpty(constructProjectSheet14List)) {
                        await this.sheetMerge(worksheet, headArgsQd14['headEndNum'], "");
                    }
                    break;
                case "B.0.13 国内采购设备表":
                    let constructProjectSheet15List = await this.getconstructProjectSheet15List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet15(constructProjectJBXX, constructProjectSheet15List, worksheet);
                    let headArgsQd15 = {};
                    headArgsQd15['headStartNum'] = 1;
                    headArgsQd15['headEndNum'] = 3;
                    headArgsQd15['titlePage'] = false;
                    headArgsQd15['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd15);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(29, worksheet._rows.length + 1, worksheet, workSheetGeshi, 5, 10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "B.0.14 项目设备费汇总表":
                    let constructProjectSheet16List = await this.getconstructProjectSheet16List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet16(constructProjectJBXX, constructProjectSheet16List, worksheet);
                    let headArgsQd16 = {};
                    headArgsQd16['headStartNum'] = 1;
                    headArgsQd16['headEndNum'] = 3;
                    headArgsQd16['titlePage'] = false;
                    headArgsQd16['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd16);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(31, worksheet._rows.length + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    break;
                case "附表B.0.1 总概算表":
                    let constructProjectSheet17List = await this.getconstructProjectSheet17List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet17(constructProjectJBXX, constructProjectSheet17List, worksheet);
                    let headArgsQd17 = {};
                    headArgsQd17['headStartNum'] = 1;
                    headArgsQd17['headEndNum'] = 4;
                    headArgsQd17['titlePage'] = false;
                    headArgsQd17['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd17);
                    await GSExcelUtil.copyRowsWithOtherSheetHeji(33, worksheet._rows.length + 1, worksheet, workSheetGeshi, 4, 8, 10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "附表B.0.2 总概算表":
                    let constructProjectSheet18List = await this.getconstructProjectSheet18List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet18(constructProjectJBXX, constructProjectSheet18List, worksheet);
                    let headArgsQd18 = {};
                    headArgsQd18['headStartNum'] = 1;
                    headArgsQd18['headEndNum'] = 4;
                    headArgsQd18['titlePage'] = false;
                    headArgsQd18['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd18);
                    await GSExcelUtil.copyRowsWithOtherSheetHeji(35, worksheet._rows.length + 1, worksheet, workSheetGeshi, 4, 8, 11);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructProjectJBXX, worksheet);
                    break;
                case "1 封面（适用于中介单位）":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet19(constructProjectJBXX, worksheet);
                    let headArgsQd19 = {};
                    headArgsQd19['headStartNum'] = 1;
                    headArgsQd19['headEndNum'] = 9;
                    headArgsQd19['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd19);
                    break;
                case "2 签署页（适用于中介单位）":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet20(constructProjectJBXX, worksheet);
                    let headArgsQd20 = {};
                    headArgsQd20['headStartNum'] = 1;
                    headArgsQd20['headEndNum'] = 11;
                    headArgsQd20['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd20);
                    break;
            }
        }
        if (projectType == ProjectLevelConstant.single) {
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            let constructSingleJBXX = await this.getconstructProjectSingleJBXX(args);
            switch (worksheet.name) {
                case "B.0.05 综合概算表":
                    let constructSingleSheet1List = await this.getconstructSingleSheet1List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSingleSheet1(constructSingleJBXX, constructSingleSheet1List, worksheet);
                    let headArgsQd21 = {};
                    headArgsQd21['headStartNum'] = 1;
                    headArgsQd21['headEndNum'] = 4;
                    headArgsQd21['titlePage'] = false;
                    headArgsQd21['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd21);
                    await GSExcelUtil.copyRowsWithOtherSheetHeji(1, worksheet._rows.length + 1, worksheet, workSheetGeshi, 2, 5, 9);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructSingleJBXX, worksheet);
                    break;
                case "B.0.11 综合概算对比表":
                    let constructSingleSheet2List = await this.getconstructSingleSheet2List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSingleSheet2(constructSingleJBXX, constructSingleSheet2List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    await GSExcelUtil.copyRowsWithOtherSheetHejiV2(3, worksheet._rows.length + 1, worksheet, workSheetGeshi, 5, 12);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructSingleJBXX, worksheet);
                    break;
            }
        }
        if (projectType == ProjectLevelConstant.unit) {
            let constructUnitJBXX = await this.getconstructProjectSingleUnitJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //单位工程层级
                case "封面":
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet1(constructUnitJBXX, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 11;
                    headArgsQd1['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    break;
                case "编制说明":
                    let unitBzsm = constructUnitJBXX.filter(object => object.name == "编制说明")[0];
                    if (ObjectUtils.isNotEmpty(unitBzsm) && ObjectUtils.isNotEmpty(unitBzsm.remark)) {
                        let remark = await GSExcelUtil.removeTags(unitBzsm.remark);
                        await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet4(remark, worksheet);
                    }
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 2;
                    headArgsQd2['titlePage'] = true;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    break;
                case "单位工程概预算表(A4竖)":
                    let constructUnitSheet3List = await this.getconstructUnitSheet3List(args);
                    constructUnitJBXX["unitDwgcgysba4sHeji"] = args["unitDwgcgysba4sHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet3(constructUnitJBXX, constructUnitSheet3List, worksheet);
                    let headArgsQd3 = {};
                    headArgsQd3['headStartNum'] = 1;
                    headArgsQd3['headEndNum'] = 4;
                    headArgsQd3['titlePage'] = false;
                    headArgsQd3['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程概预算表(A4横)":
                    let constructUnitSheet4List = await this.getconstructUnitSheet4List(args);
                    constructUnitJBXX["unitDwgcgysba4hHeji"] = args["unitDwgcgysba4hHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet4(constructUnitJBXX, constructUnitSheet4List, worksheet);
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 4;
                    headArgsQd4['titlePage'] = false;
                    headArgsQd4['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程概预算表(自然单位)":
                    let constructUnitSheet30List = await this.getconstructUnitSheet30List(args);
                    constructUnitJBXX["unitDwgcgysba4sHeji"] = args["unitDwgcgysba4sHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet3(constructUnitJBXX, constructUnitSheet30List, worksheet);
                    let headArgsQd30 = {};
                    headArgsQd30['headStartNum'] = 1;
                    headArgsQd30['headEndNum'] = 4;
                    headArgsQd30['titlePage'] = false;
                    headArgsQd30['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd30);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程费用表":
                    let constructUnitSheet5List = await this.getconstructUnitSheet5List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet5(constructUnitJBXX, constructUnitSheet5List, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 3;
                    headArgsQd5['titlePage'] = false;
                    headArgsQd5['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    await this.calDwgcfyHeji(args["unitDwgcfyHeji"], worksheet);
                    // let heJiCell5 = GSExcelUtil.findValueCell(worksheet, "合计");
                    // let row5 = worksheet.getRow(heJiCell5.cell._row._number);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(9, row5.number + 1, worksheet, workSheetGeshi, 2, 4, 6);
                    // await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程费用表(建筑工程)":
                    let constructUnitSheet6List = await this.getconstructUnitSheet6List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet6(constructUnitJBXX, constructUnitSheet6List, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 3;
                    headArgsQd6['titlePage'] = false;
                    headArgsQd6['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    await this.calDwgcfyHeji(args["unitDwgcfyjzgcHeji"], worksheet);
                    // let heJiCell6 = GSExcelUtil.findValueCell(worksheet, "合计");
                    // let row6 = worksheet.getRow(heJiCell6.cell._row._number);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(11, row6.number + 1, worksheet, workSheetGeshi, 4, 8, 12);
                    // await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程费用表(安装工程)":
                    let constructUnitSheet7List = await this.getconstructUnitSheet7List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet7(constructUnitJBXX, constructUnitSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 3;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    await this.calDwgcfyHeji(args["unitDwgcfyzgcHeji"], worksheet);
                    // let heJiCell7 = GSExcelUtil.findValueCell(worksheet, "合计");
                    // let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    // await GSExcelUtil.copyRowsWithOtherSheetHeji(13, row7.number + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    // await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程人材机汇总表":
                    let constructUnitSheet8List = await this.getconstructUnitSheet8List(args);
                    constructUnitJBXX["unitRcjHeji"] = args["unitRcjHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet8(constructUnitJBXX, constructUnitSheet8List, worksheet);
                    let headArgsQd8 = {};
                    headArgsQd8['headStartNum'] = 1;
                    headArgsQd8['headEndNum'] = 3;
                    headArgsQd8['titlePage'] = false;
                    headArgsQd8['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程人材机价差表（人材机汇总）":
                    let constructUnitSheet9List = await this.getconstructUnitSheet9List(args);
                    constructUnitJBXX['unitRcjjcHeji'] = args["unitRcjjcHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet9(constructUnitJBXX, constructUnitSheet9List, worksheet);
                    let headArgsQd9 = {};
                    headArgsQd9['headStartNum'] = 1;
                    headArgsQd9['headEndNum'] = 3;
                    headArgsQd9['titlePage'] = false;
                    headArgsQd9['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程三材汇总表":
                    let constructUnitSheet10List = await this.getconstructUnitSheet10List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet10(constructUnitJBXX, constructUnitSheet10List, worksheet);
                    let headArgsQd10 = {};
                    headArgsQd10['headStartNum'] = 1;
                    headArgsQd10['headEndNum'] = 3;
                    headArgsQd10['titlePage'] = false;
                    headArgsQd10['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd10);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位工程主材表":
                    let constructUnitSheet11List = await this.getconstructUnitSheet11List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet11(constructUnitJBXX, constructUnitSheet11List, worksheet);
                    let headArgsQd11 = {};
                    headArgsQd11['headStartNum'] = 1;
                    headArgsQd11['headEndNum'] = 3;
                    headArgsQd11['titlePage'] = false;
                    headArgsQd11['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd11);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "预拌砼汇总表":
                    let constructUnitSheet12List = await this.getconstructUnitSheet12List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet12(constructUnitJBXX, constructUnitSheet12List, worksheet);
                    let headArgsQd12 = {};
                    headArgsQd12['headStartNum'] = 1;
                    headArgsQd12['headEndNum'] = 3;
                    headArgsQd12['titlePage'] = false;
                    headArgsQd12['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd12);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "独立费表":
                    let constructUnitSheet13List = await this.getconstructUnitSheet13List(args);
                    constructUnitJBXX["unitDlfHeji"] = args["unitDlfHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet13(constructUnitJBXX, constructUnitSheet13List, worksheet);
                    let headArgsQd13 = {};
                    headArgsQd13['headStartNum'] = 1;
                    headArgsQd13['headEndNum'] = 3;
                    headArgsQd13['titlePage'] = false;
                    headArgsQd13['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "主要材料价格表":
                    let constructUnitSheet14List = await this.getconstructUnitSheet14List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet14(constructUnitJBXX, constructUnitSheet14List, worksheet);
                    let headArgsQd14 = {};
                    headArgsQd14['headStartNum'] = 1;
                    headArgsQd14['headEndNum'] = 3;
                    headArgsQd14['titlePage'] = false;
                    headArgsQd14['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd14);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                case "单位全费指标分析表":
                    let constructUnitSheet15List = await this.getconstructUnitSheet15List(args);
                    constructUnitJBXX["unitDwqfzbfxbHeji"] = args["unitDwqfzbfxbHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet15(constructUnitJBXX, constructUnitSheet15List, worksheet);
                    let headArgsQd15 = {};
                    headArgsQd15['headStartNum'] = 1;
                    headArgsQd15['headEndNum'] = 4;
                    headArgsQd15['titlePage'] = false;
                    headArgsQd15['workSheetGeshi'] = workSheetGeshi;
                    await GSExcelUtil.dealWithPage(worksheet, workbook, headArgsQd15);
                    await ShenHeWriteExcelBySheetUtil.writeDataToSheetXioji(constructUnitJBXX, worksheet);
                    break;
                default:
            }
        }
    }


    async calDwgcfyHeji(unitDwgcfyHeji, worksheet) {
        let {total} = unitDwgcfyHeji;
        let totalZW = "含税工程造价：零元整";
        if (ObjectUtils.isNotEmpty(total) && total > 0) {
            let numToCny = NumberUtil.numToCny(total);
            totalZW = "含税工程造价：" + numToCny;
        }
        let heJiCell7 = GSExcelUtil.findValueCell(worksheet, "含税工程造价：零元整");
        let row7 = worksheet.getRow(heJiCell7.cell._row._number);
        //合并单元格
        worksheet.unMergeCells(row7.number, 1, row7.number, 5);
        worksheet.mergeCells(row7.number, 1, row7.number, 5);
        row7.getCell(1).value = totalZW;
        row7.getCell(1).style.alignment.horizontal = "center";
        row7.getCell(1).style.alignment.vertical = "middle";
    }

    async sheetMerge(worksheet, headEndNum, totalSS) {
        let dataNum = headEndNum + 1;
        if (worksheet.name.includes("B.0.12 进口设备材料货价及从属费用计算表")) {
            await this.sheetMergeFirstpage(dataNum, worksheet);     //先合并第一页数据
            // let rowBreaks = worksheet.rowBreaks;
            // if (ObjectUtils.isNotEmpty(rowBreaks) && rowBreaks.length > 0) {
            //     let start = 0;
            //     for (let j = 0; j < rowBreaks.length; j++) {
            //         let rowBreak = rowBreaks[j];
            //         let id = rowBreak.id;
            //         start = id + dataNum;
            //
            //         let hebinghangS = start;
            //         let hebinghangE = start;
            //         let hebingstr = worksheet.getRow(start).getCell(1).value == null || worksheet.getRow(start).getCell(1).value == undefined ? "" : worksheet.getRow(start).getCell(1).value;
            //         for (let j = start + 1; j <= id; j++) {
            //             let cell1 = worksheet.getRow(j).getCell(1);
            //             if (cell1.value != null) {
            //                 if (cell1.value === hebingstr) {
            //                     hebinghangE = j;
            //                 } else {
            //                     if (hebinghangE - hebinghangS > 0) {
            //                         if(cell1.value!==""){
            //                             //先合并，再重置start
            //                         worksheet.unMergeCells(hebinghangS, 1, hebinghangE, 1);
            //                         worksheet.mergeCells(hebinghangS, 1, hebinghangE, 1);
            //                         worksheet.unMergeCells(hebinghangS, 2, hebinghangE, 2);
            //                         worksheet.mergeCells(hebinghangS, 2, hebinghangE, 2);
            //                         worksheet.unMergeCells(hebinghangS, 3, hebinghangE, 3);
            //                         worksheet.mergeCells(hebinghangS, 3, hebinghangE, 3);
            //                         worksheet.unMergeCells(hebinghangS, 4, hebinghangE, 4);
            //                         worksheet.mergeCells(hebinghangS, 4, hebinghangE, 4);
            //                         worksheet.unMergeCells(hebinghangS, 5, hebinghangE, 5);
            //                         worksheet.mergeCells(hebinghangS, 5, hebinghangE, 5);
            //                         worksheet.unMergeCells(hebinghangS, 8, hebinghangE, 8);
            //                         worksheet.mergeCells(hebinghangS, 8, hebinghangE, 8);
            //                         worksheet.unMergeCells(hebinghangS, 9, hebinghangE, 9);
            //                         worksheet.mergeCells(hebinghangS, 9, hebinghangE, 9);
            //                         worksheet.unMergeCells(hebinghangS, 12, hebinghangE, 12);
            //                         worksheet.mergeCells(hebinghangS, 12, hebinghangE, 12);
            //                         worksheet.unMergeCells(hebinghangS, 13, hebinghangE, 13);
            //                         worksheet.mergeCells(hebinghangS, 13, hebinghangE, 13);
            //                         hebinghangS = j;
            //                         hebinghangE = j;
            //                         }
            //
            //                     } else {
            //                         hebinghangS = j;
            //                         hebingstr = "";
            //                     }
            //                 }
            //                 hebingstr = cell1.value;
            //             } else {
            //                 hebinghangS = j;
            //                 hebingstr = "";
            //             }
            //         }
            //         // start = rowBreak.id;
            //     }
            // }
        }
    }


    async sheetMergeFirstpage(dataNum, worksheet) {
        //没有分页
        let start = dataNum;
        let hebinghangS = start;
        let hebinghangE = start;
        let hebingstr = worksheet.getRow(start).getCell(1).value == null || worksheet.getRow(start).getCell(1).value == undefined ? "" : worksheet.getRow(start).getCell(1).value;
        for (let j = start + 1; j <= worksheet._rows.length; j++) {
            let cell1 = worksheet.getRow(j).getCell(1);
            let cell1Last = worksheet.getRow(j - 1).getCell(1);
            if (cell1.value != null) {
                if (cell1.value === hebingstr) {
                    hebinghangE = j;
                } else {
                    if (hebinghangE - hebinghangS > 0) {
                        if (cell1.value !== "编制人：") {
                            //先合并，再重置start
                            worksheet.unMergeCells(hebinghangS, 1, hebinghangE, 1);
                            worksheet.mergeCells(hebinghangS, 1, hebinghangE, 1);
                            worksheet.unMergeCells(hebinghangS, 2, hebinghangE, 2);
                            worksheet.mergeCells(hebinghangS, 2, hebinghangE, 2);
                            worksheet.unMergeCells(hebinghangS, 3, hebinghangE, 3);
                            worksheet.mergeCells(hebinghangS, 3, hebinghangE, 3);
                            worksheet.unMergeCells(hebinghangS, 4, hebinghangE, 4);
                            worksheet.mergeCells(hebinghangS, 4, hebinghangE, 4);
                            worksheet.unMergeCells(hebinghangS, 5, hebinghangE, 5);
                            worksheet.mergeCells(hebinghangS, 5, hebinghangE, 5);
                            worksheet.unMergeCells(hebinghangS, 8, hebinghangE, 8);
                            worksheet.mergeCells(hebinghangS, 8, hebinghangE, 8);
                            worksheet.unMergeCells(hebinghangS, 9, hebinghangE, 9);
                            worksheet.mergeCells(hebinghangS, 9, hebinghangE, 9);
                            worksheet.unMergeCells(hebinghangS, 12, hebinghangE, 12);
                            worksheet.mergeCells(hebinghangS, 12, hebinghangE, 12);
                            worksheet.unMergeCells(hebinghangS, 13, hebinghangE, 13);
                            worksheet.mergeCells(hebinghangS, 13, hebinghangE, 13);
                            hebinghangS = j;
                            hebinghangE = j;
                        }
                    } else {
                        hebinghangS = j;
                        hebingstr = "";
                    }
                }
                hebingstr = cell1.value;
            } else {
                hebinghangS = j;
                hebingstr = "";
            }
        }
    }


    traverseHeadLineList(headLineList, strCondition) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];
            if (element.headLine == strCondition) {
                return element;
            }
            if (element.hasOwnProperty("children")) {
                let result = this.traverseHeadLineList(element.children, strCondition);
                if (result != null) return result;
            }
        }
        return null;
    }

    //拿到list中 没有children的元素
    traverseGetHeadLineAndLeaf(headLineList, levelType, list) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];

            if (element.projectLevel == levelType && element.children == null) {
                element['selected'] = false;
                list.push(element);
            }
            if (element.projectLevel == levelType && element.children != null) {
                this.traverseGetHeadLineAndLeaf(element.children, levelType, list);
            }
        }
        return list;
    }

    async getconstructProjectJBXX(param) {
        //获取工程项目对象
        let construct = ProjectDomain.getDomain(param.constructId).getProjectById(param.constructId);
        let array = new Array();
        let constructName = {};
        constructName.name = "项目名称";
        constructName.remark = construct.name;
        array.push(constructName);
        // let constructCode = {};
        // constructCode.name = "项目编码";
        // constructCode.remark = construct.code;
        // array.push(constructCode);

        await this.getconstructJBXX(param.constructId, array);

        //获取编制说明
        let args1 = {};
        args1.constructId = param.constructId;
        args1.type = "12";
        args1.levelType = projectLevelConstant.construct;
        args1.unitId = null;
        const BZSM = await this.service.PreliminaryEstimate.gsOverviewService.getList(args1);
        let constructBzsm = {};
        constructBzsm.name = "编制说明";
        if (ObjectUtils.isNotEmpty(BZSM)) {
            constructBzsm.remark = BZSM.context;
        } else {
            constructBzsm.remark = null;
        }
        array.push(constructBzsm);

        return array;
    }


    async getconstructJBXX(constructId, array) {
        //获取基本信息
        let args = {};
        args.constructId = constructId;
        args.type = "11";
        args.levelType = projectLevelConstant.construct;
        args.unitId = null;
        const JBXXArray = await this.service.PreliminaryEstimate.gsOverviewService.getList(args);

        let jbxx = JBXXArray.find(o => o.name === "基本信息");
        let jsdwxx = JBXXArray.find(o => o.name === "建设单位信息");
        let sjdwxx = JBXXArray.find(o => o.name === "设计单位信息");
        let zjzxdwxx = JBXXArray.find(o => o.name === "造价咨询单位信息");

        let chongmingMap = new Map();
        for (let i = 0; i < JBXXArray.length; i++) {
            let heJBXX = JBXXArray[i];

            if (chongmingMap.has(heJBXX.name)) {
                continue;
            }

            if (heJBXX.name === "造价咨询单位") {
                let project = {};
                if (heJBXX.parentId === zjzxdwxx.sequenceNbr) {
                    project.name = zjzxdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(zjzxdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            } else if (heJBXX.name === "编制人") {
                let project = {};
                if (heJBXX.parentId === sjdwxx.sequenceNbr) {
                    project.name = sjdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(sjdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                } else if (heJBXX.parentId === zjzxdwxx.sequenceNbr) {
                    project.name = zjzxdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(zjzxdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            } else if (heJBXX.name === "核对人（复核人）") {
                let project = {};
                if (heJBXX.parentId === sjdwxx.sequenceNbr) {
                    project.name = sjdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(sjdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                } else if (heJBXX.parentId === zjzxdwxx.sequenceNbr) {
                    project.name = zjzxdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(zjzxdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            } else if (heJBXX.name === "法定代表人或其授权人") {
                let project = {};
                if (heJBXX.parentId === sjdwxx.sequenceNbr) {
                    project.name = sjdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(sjdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                } else if (heJBXX.parentId === zjzxdwxx.sequenceNbr) {
                    project.name = zjzxdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(zjzxdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            } else if (heJBXX.name === "建设单位") {
                let project = {};
                if (heJBXX.parentId === jsdwxx.sequenceNbr) {
                    project.name = jsdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(jsdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            } else if (heJBXX.name === "技术负责人") {
                let project = {};
                if (heJBXX.parentId === zjzxdwxx.sequenceNbr) {
                    project.name = zjzxdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(zjzxdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            } else if (heJBXX.name === "项目编号") {
                let project = {};
                if (heJBXX.parentId === jbxx.sequenceNbr) {
                    project.name = "项目编码";
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(zjzxdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            } else if (heJBXX.name === "编制时间") {
                let project = {};
                if (heJBXX.parentId === zjzxdwxx.sequenceNbr) {
                    project.name = zjzxdwxx.name + "-" + heJBXX.name;
                    project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                    array.push(project);
                    chongmingMap.set(zjzxdwxx.name + "-" + heJBXX.name, heJBXX.remark);
                }
            }
        }
    }


    async getconstructProjectSingleJBXX(param) {
        //获取工程项目对象
        let construct = ProjectDomain.getDomain(param.constructId).getProjectById(param.constructId);
        let single = ProjectDomain.getDomain(param.constructId).getProjectById(param.singleId);


        let array = new Array();
        let constructName = {};
        constructName.name = "单项名称";
        constructName.remark = single.name;
        array.push(constructName);

        let constructCode = {};
        constructCode.name = "项目编码";
        constructCode.remark = construct.code;
        array.push(constructCode);

        await this.getconstructJBXX(param.constructId, array);

        return array;

    }


    async getconstructProjectSingleUnitJBXX(param) {
        let array = new Array();
        //获取工程项目、单项、单位
        let construct = ProjectDomain.getDomain(param.constructId).getProjectById(param.constructId);
        let single = ProjectDomain.getDomain(param.constructId).getProjectById(param.singleId);
        let unit = ProjectDomain.getDomain(param.constructId).getProjectById(param.unitId);
        param['levelType'] = projectLevelConstant.construct;

        let obj = {};
        obj.name = "单位名称";
        obj.remark = single.name + "-" + unit.name;
        array.push(obj);

        //获取基本信息
        let args = {};
        args.constructId = param.constructId;
        args.type = "11";
        args.levelType = projectLevelConstant.unit;
        args.unitId = param.unitId;
        const JBXXArray = await this.service.PreliminaryEstimate.gsOverviewService.getList(args);
        let chongmingMap = new Map();
        for (let i = 0; i < JBXXArray.length; i++) {
            let heJBXX = JBXXArray[i];
            if (chongmingMap.has(heJBXX.name)) {
                continue;
            }
            if (heJBXX.name === "建设单位名称") {
                let project = {};
                project.name = heJBXX.name;
                project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                array.push(project);
                chongmingMap.set(heJBXX.name, heJBXX.remark);
            } else if (heJBXX.name === "编制时间") {
                let project = {};
                project.name = heJBXX.name;
                project.remark = ObjectUtils.isNotEmpty(heJBXX.remark) ? heJBXX.remark : "";
                array.push(project);
                chongmingMap.set(heJBXX.name, heJBXX.remark);
            }

        }

        //获取编制说明
        let args1 = {};
        args1.constructId = param.constructId;
        args1.type = "12";
        args1.levelType = projectLevelConstant.unit;
        args1.unitId = param.unitId;
        const BZSM = await this.service.PreliminaryEstimate.gsOverviewService.getList(args1);
        let constructBzsm = {};
        constructBzsm.name = "编制说明";
        if (ObjectUtils.isNotEmpty(BZSM)) {
            constructBzsm.remark = BZSM.context;
        } else {
            constructBzsm.remark = null;
        }
        array.push(constructBzsm);


        //获取工程特征
        let args2 = {};
        args2.constructId = param.constructId;
        args2.type = "13";
        args2.levelType = projectLevelConstant.unit;
        args2.unitId = param.unitId;
        const GCTZArray = await this.service.PreliminaryEstimate.gsOverviewService.getList(args2);
        let chongmingMap1 = new Map();
        for (let i = 0; i < GCTZArray.length; i++) {
            let heJBXX = GCTZArray[i];
            if (chongmingMap1.has(heJBXX.name)) {
                continue;
            }
            if (heJBXX.name === "工程规模") {
                let project = {};
                project.name = heJBXX.name;
                project.remark = ObjectUtils.isNotEmpty(heJBXX.context) ? heJBXX.context : "";
                array.push(project);
                chongmingMap1.set(heJBXX.name, heJBXX.remark);
            }
        }


        try {
            //获取造价分析
            let args3 = {};
            args3.type = projectLevelConstant.unit;
            args3.singleId = param.singleId;
            args3.constructId = param.constructId;
            args3.unitId = param.unitId;
            let costAnalysiss = await this.service.PreliminaryEstimate.gsCostAnalysisService.getCostAnalysisData(args3);
            if (ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisUnitVOList)) {
                for (let item of costAnalysiss.costAnalysisUnitVOList) {
                    if (item.name === "工程总造价（小写）") {
                        let project = {};
                        project.name = item.name;
                        project.remark = ObjectUtils.isNotEmpty(item.context) ? await this.removeExtraZerosAndDot(new Decimal(item.context).toFixed(2)) : "";
                        array.push(project);
                    } else if (item.name === "工程总造价（大写）") {
                        let project = {};
                        project.name = item.name;
                        project.remark = ObjectUtils.isNotEmpty(item.context) ? item.context : "";
                        array.push(project);
                    } else if (item.name === "单方造价") {
                        let project = {};
                        project.name = item.name;
                        project.remark = ObjectUtils.isNotEmpty(item.context) ? await this.removeExtraZerosAndDot(new Decimal(item.context).toFixed(2)) : "";
                        array.push(project);
                    }
                }
            }
        } catch (e) {
            console.log("获取报表单位造价分析报错" + param.unitId);
        }


        await this.getconstructJBXX(param.constructId, array);

        return array;
    }


    async removeExtraZerosAndDot(numStr) {
        return numStr.toString().replace(/(\.0*|0+)$/, '');
    }

    async getconstructProjectSheet7List(param) {
        let Sheet4List = [];
        let heji = {};
        heji.total = "";

        let args = {};
        args.type = 1;
        args.levelType = projectLevelConstant.construct;
        args.code = 5;
        args.unitId = null;
        args.constructId = param.constructId;
        args.singleId = null;
        args.projectId = param.constructId;
        let otherProjectCostList = await this.service.PreliminaryEstimate.gsOtherProjectCostService.getOtherProjectCostList(args);
        if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
            heji.total = otherProjectCostList[0].amount;
            // let filter = otherProjectCostList.filter(o => ObjectUtils.isNotEmpty(o.parentId));
            // if (ObjectUtils.isNotEmpty(filter)) {
            //     heji.total = filter.reduce((sum, item) => sum + Number(ObjectUtils.isEmpty(item.amount) ? 0 : item.amount), 0);
            //     for (let item of filter) {
            //         if (ObjectUtils.isNotEmpty(item) && item.amount > 0) {
            //             Sheet4List.push(item);
            //             if (ObjectUtils.isNotEmpty(item.children)) {
            //                 for (let item1 of item.children) {
            //                     if (ObjectUtils.isNotEmpty(item1) && item1.amount > 0) {
            //                         Sheet4List.push(item1);
            //                     }
            //                 }
            //             }
            //         }
            //     }
            // }

            otherProjectCostList.forEach(p => {
                if (ObjectUtils.isNotEmpty(p.amount) && p.amount > 0) {
                    let deepCopy1 = ConvertUtil.deepCopy(p);
                    deepCopy1.amount = GsExportSettingUtil.formatWith0(deepCopy1.amount,GsExportSettingUtil.PRICE_ALL);
                    if(ObjectUtils.isNotEmpty(deepCopy1.price)){
                        deepCopy1.price = deepCopy1.amount;
                    }
                    Sheet4List.push(deepCopy1);
                }
            })
        }

        let zhongwen = 0;
        let count = 0;

        for (const p of Sheet4List) {
            if (ObjectUtils.isNotEmpty(p.dispNo)) {
                let isInt = Number.isFinite(+p.dispNo);
                if (isInt) {
                    count++;
                    p.dispNo = count.toString();
                } else {
                    zhongwen++;
                    count = 0;
                    p.dispNo = await this.toChineseCapital(zhongwen.toString());
                }
            }
        }
        param['projectQtfyjsbHeji'] = heji;

        return Sheet4List;
    }


    async toChineseCapital(numStr) {
        const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        const units = ['', '十', '百', '千', '万'];
        let str = numStr;
        let res = '';

        for (let i = 0; i < str.length; i++) {
            let num = parseInt(str[i]);
            if (str[0] === '0' && str.length > 1) {
                res += units[i];
            } else {
                res += chineseNumbers[num] + units[i];
            }
        }

        return res;
    }

    _fillProjectDispNo(singleProject,summaryList,dispNo){
        let childs = singleProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i<childs.length;i++){
                let findItem = summaryList.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = dispNo +"."+ (i+1);
                this._fillProjectDispNo(childs[i],summaryList,findItem.dispNo);
            }
        }
    }

    async getconstructProjectSheet8List(param) {
        let Sheet4List = [];

        let args = {};
        args.constructId = param.constructId;
        let summaryList = await this.service.PreliminaryEstimate.gsEstimateSummaryService.getEstimateSummaryList(args);  

        if (ObjectUtils.isNotEmpty(summaryList)) {
            summaryList.forEach(o => {
                let copyO = ConvertUtil.deepCopy(o);
                if(ObjectUtils.isEmpty(copyO.jzFee) || copyO.jzFee == 0 || copyO.jzFee == '0.00'){
                    copyO.jzFee = '';
                }else{
                    copyO.jzFee = GsExportSettingUtil.formatWith0(copyO.jzFee,GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(copyO.azFee) || copyO.azFee == 0 || copyO.azFee == '0.00'){
                    copyO.azFee = '';
                }else{
                    copyO.azFee = GsExportSettingUtil.formatWith0(copyO.azFee,GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(copyO.sbgzFee) || copyO.sbgzFee == 0 || copyO.sbgzFee == '0.00'){
                    copyO.sbgzFee = '';
                }else{
                    copyO.sbgzFee = GsExportSettingUtil.formatWith0(copyO.sbgzFee,GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(copyO.qtFee) || copyO.qtFee == 0 || copyO.qtFee == '0.00'){
                    copyO.qtFee = '';
                }else{
                    copyO.qtFee = GsExportSettingUtil.formatWith0(copyO.qtFee,GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(copyO.price) || copyO.price == 0 || copyO.price == '0.00'){
                    copyO.price = '';
                }else{
                    copyO.price = GsExportSettingUtil.formatWith0(copyO.price,GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(copyO.unitCost) || copyO.unitCost == 0 || copyO.unitCost == '0.00'){
                    copyO.unitCost = '';
                }else{
                    copyO.unitCost = GsExportSettingUtil.formatWith0(copyO.unitCost,GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(copyO.proportion) || copyO.proportion == 0 || copyO.proportion == '0.00'){
                    copyO.proportion = '';
                }else{
                    copyO.proportion = GsExportSettingUtil.formatWith0(copyO.proportion,GsExportSettingUtil.PRICE_ALL);
                }
                Sheet4List.push(copyO);
            })
        }

        //设备购置费
        let sbgzfItem = Sheet4List.find(item=>item.category==="设备购置费")
        //设置单项的编号
        let rootProject = ProjectDomain.getDomain(param.constructId).getRoot();
        let childs = rootProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i < childs.length;i++){
                let findItem = Sheet4List.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = (i+1);
                this._fillProjectDispNo(childs[i],Sheet4List,findItem.dispNo)
            }
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
                sbgzfItem.dispNo = childs.length+1;
            }
        }else{
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
              sbgzfItem.dispNo = 1;
            }
        }

        return Sheet4List;
    }


    async getconstructProjectSheet9List(param) {
        let Sheet4List = [];

        let args = {};
        args.constructId = param.constructId;
        let summaryList = await this.service.PreliminaryEstimate.gsEstimateSummaryService.getEstimateSummaryList(args);

        if (ObjectUtils.isNotEmpty(summaryList)) {
            for(let o of summaryList){
                let copyO = ConvertUtil.deepCopy(o);
                if(ObjectUtils.isEmpty(copyO.jzFee) || copyO.jzFee == 0 || copyO.jzFee == '0.00'){
                    copyO.jzFee = '';
                }
                if(ObjectUtils.isEmpty(copyO.azFee) || copyO.azFee == 0 || copyO.azFee == '0.00'){
                    copyO.azFee = '';
                }
                if(ObjectUtils.isEmpty(copyO.sbgzFee) || copyO.sbgzFee == 0 || copyO.sbgzFee == '0.00'){
                    copyO.sbgzFee = '';
                }
                if(ObjectUtils.isEmpty(copyO.qtFee) || copyO.qtFee == 0 || copyO.qtFee == '0.00'){
                    copyO.qtFee = '';
                }
                if(ObjectUtils.isEmpty(copyO.price) || copyO.price == 0 || copyO.price == '0.00'){
                    copyO.price = '';
                }
                if(ObjectUtils.isEmpty(copyO.unitCost) || copyO.unitCost == 0 || copyO.unitCost == '0.00'){
                    copyO.unitCost = '';
                }
                if(ObjectUtils.isEmpty(copyO.proportion) || copyO.proportion == 0 || copyO.proportion == '0.00'){
                    copyO.proportion = '';
                }
                Sheet4List.push(copyO);
                if (o.name === "工程建设其他费用") {
                    //增加建设其他费的数据
                    let args1 = {};
                    args1.type = 1;
                    args1.levelType = projectLevelConstant.construct;
                    args1.code = 5;
                    args1.unitId = null;
                    args1.constructId = param.constructId;
                    args1.singleId = null;
                    args1.projectId = param.constructId;
                    let otherProjectCostList = await this.service.PreliminaryEstimate.gsOtherProjectCostService.getOtherProjectCostList(args1);
                    if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
                        let otherProjectCostListCopy = ConvertUtil.deepCopy(otherProjectCostList);
                        for (let i = 1; i < otherProjectCostListCopy.length; i++) {
                            let p = otherProjectCostListCopy[i];
                            p.price = '';
                            if (ObjectUtils.isNotEmpty(p.amount) && p.amount != "0" && p.amount != "0.00") {
                                p.price = p.amount;
                            }
                            p.dispNo = p.code;
                            Sheet4List.push(p);
                        }
                    }
                }
            }
        }

        //设备购置费
        let sbgzfItem = Sheet4List.find(item=>item.category==="设备购置费")
        //设置单项的编号
        let rootProject = ProjectDomain.getDomain(param.constructId).getRoot();
        let childs = rootProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i < childs.length;i++){
                let findItem = Sheet4List.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = (i+1);
                this._fillProjectDispNo(childs[i],Sheet4List,findItem.dispNo)
            }
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
                sbgzfItem.dispNo = childs.length+1;
            }
        }else{
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
              sbgzfItem.dispNo = 1;
            }
        }

        return Sheet4List;
    }


    async getconstructProjectSheet10List(param) {
        let Sheet4List = [];

        let args = {};
        args.constructId = param.constructId;
        let summaryList = await this.service.PreliminaryEstimate.gsEstimateSummaryService.getEstimateSummaryList(args);

        if (ObjectUtils.isNotEmpty(summaryList)) {
            for (let item of summaryList) {
                if (ObjectUtils.isNotEmpty(item.price) && item.price > 0) {
                    let copyO = ConvertUtil.deepCopy(item);
                    if(ObjectUtils.isEmpty(copyO.jzFee) || copyO.jzFee == 0 || copyO.jzFee == '0.00'){
                        copyO.jzFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.azFee) || copyO.azFee == 0 || copyO.azFee == '0.00'){
                        copyO.azFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.sbgzFee) || copyO.sbgzFee == 0 || copyO.sbgzFee == '0.00'){
                        copyO.sbgzFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.qtFee) || copyO.qtFee == 0 || copyO.qtFee == '0.00'){
                        copyO.qtFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.price) || copyO.price == 0 || copyO.price == '0.00'){
                        copyO.price = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.unitCost) || copyO.unitCost == 0 || copyO.unitCost == '0.00'){
                        copyO.unitCost = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.proportion) || copyO.proportion == 0 || copyO.proportion == '0.00'){
                        copyO.proportion = '';
                    }
                    Sheet4List.push(copyO);
                    if (item.name === "工程建设其他费用") {
                        //增加建设其他费的数据
                        let args1 = {};
                        args1.type = 1;
                        args1.levelType = projectLevelConstant.construct;
                        args1.code = 5;
                        args1.unitId = null;
                        args1.constructId = param.constructId;
                        args1.singleId = null;
                        args1.projectId = param.constructId;
                        let otherProjectCostList =  await this.service.PreliminaryEstimate.gsOtherProjectCostService.getOtherProjectCostList(args1);
                        if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
                            let otherProjectCostListCopy = ConvertUtil.deepCopy(otherProjectCostList);
                            for (let i = 1; i < otherProjectCostListCopy.length; i++) {
                                let p = otherProjectCostListCopy[i];
                                if (ObjectUtils.isNotEmpty(p.amount) && p.amount != "0" && p.amount != "0.00") {
                                    p.price = p.amount;
                                    p.dispNo = p.code;
                                    Sheet4List.push(p);
                                }
                            }
                        }
                    }
                }
            }
        }
        //设备购置费
        let sbgzfItem = Sheet4List.find(item=>item.category==="设备购置费");
        //设置单项的编号
        let rootProject = ProjectDomain.getDomain(param.constructId).getRoot();
        let childs = rootProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i < childs.length;i++){
                let findItem = Sheet4List.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = (i+1);
                this._fillProjectDispNo(childs[i],Sheet4List,findItem.dispNo)
            }
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
                sbgzfItem.dispNo = childs.length+1;
            }
        }else{
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
              sbgzfItem.dispNo = 1;
            }
        }
        
        return Sheet4List;
    }

    async getconstructProjectSheet11List(param) {
        let Sheet4List = [];

        let args = {};
        args.constructId = param.constructId;
        let summaryList = await this.service.PreliminaryEstimate.gsEstimateSummaryService.getEstimateSummaryList(args);

        if (ObjectUtils.isNotEmpty(summaryList)) {
            for (let item of summaryList) {
                if (ObjectUtils.isNotEmpty(item.price) && item.price > 0) {
                    let copyO = ConvertUtil.deepCopy(item);
                    if(ObjectUtils.isEmpty(copyO.jzFee) || copyO.jzFee == 0 || copyO.jzFee == '0.00'){
                        copyO.jzFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.azFee) || copyO.azFee == 0 || copyO.azFee == '0.00'){
                        copyO.azFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.sbgzFee) || copyO.sbgzFee == 0 || copyO.sbgzFee == '0.00'){
                        copyO.sbgzFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.qtFee) || copyO.qtFee == 0 || copyO.qtFee == '0.00'){
                        copyO.qtFee = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.price) || copyO.price == 0 || copyO.price == '0.00'){
                        copyO.price = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.unitCost) || copyO.unitCost == 0 || copyO.unitCost == '0.00'){
                        copyO.unitCost = '';
                    }
                    if(ObjectUtils.isEmpty(copyO.proportion) || copyO.proportion == 0 || copyO.proportion == '0.00'){
                        copyO.proportion = '';
                    }
                    Sheet4List.push(copyO);
                    if (item.name === "工程建设其他费用") {
                        //增加建设其他费的数据
                        let args1 = {};
                        args1.type = 1;
                        args1.levelType = projectLevelConstant.construct;
                        args1.code = 5;
                        args1.unitId = null;
                        args1.constructId = param.constructId;
                        args1.singleId = null;
                        args1.projectId = param.constructId;
                        let otherProjectCostList = await this.service.PreliminaryEstimate.gsOtherProjectCostService.getOtherProjectCostList(args1);
                        if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
                            let otherProjectCostListCopy = ConvertUtil.deepCopy(otherProjectCostList);
                            for (let i = 1; i < otherProjectCostListCopy.length; i++) {
                                let p = otherProjectCostListCopy[i];
                                if (ObjectUtils.isNotEmpty(p.amount) && p.amount != "0" && p.amount != "0.00") {
                                    p.price = p.amount;
                                    p.dispNo = p.code;
                                    p.unit = "万元";
                                    Sheet4List.push(p);
                                }
                            }
                        }
                    }
                }
            }
        }

        //设备购置费
        let sbgzfItem = Sheet4List.find(item=>item.category==="设备购置费");
        //设置单项的编号
        let rootProject = ProjectDomain.getDomain(param.constructId).getRoot();
        let childs = rootProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i < childs.length;i++){
                let findItem = Sheet4List.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = (i+1);
                this._fillProjectDispNo(childs[i],Sheet4List,findItem.dispNo)
            }
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
                sbgzfItem.dispNo = childs.length+1;
            }
        }else{
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
              sbgzfItem.dispNo = 1;
            }
        }
        
        return Sheet4List;
    }


    async getconstructProjectSheet12List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 0;
        args.levelType = projectLevelConstant.construct;
        args.constructId = param.constructId;
        let list = await this.service.PreliminaryEstimate.gsRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(list)) {
            Sheet4List = ConvertUtil.deepCopy(list);
            for(let item of Sheet4List){
                item.totalNumber = NumberUtil.numberFormat(item.totalNumber, GsExportSettingUtil.TOTALNUMBER_ALL);
                item.total = NumberUtil.numberFormat(item.total, GsExportSettingUtil.PRICE_ALL);
            }
        }

        return Sheet4List;
    }

    async getconstructProjectSheet13List(param) {
        let Sheet4List = [];

        let args = {};
        args.levelType = projectLevelConstant.construct;
        args.constructId = param.constructId;
        let adjustList =await this.service.PreliminaryEstimate.gsAdjustService.getGsAdjustList(args);

        if (ObjectUtils.isNotEmpty(adjustList)) {
            Sheet4List = ConvertUtil.deepCopy(adjustList);
            for(let item of Sheet4List){
                if(ObjectUtils.isEmpty(item.jzFee) || item.jzFee == 0 || item.jzFee == '0.00'){
                    item.jzFee =''
                }else{   
                    item.jzFee = GsExportSettingUtil.formatWith0(item.jzFee, GsExportSettingUtil.PRICE_ALL);
                }
                 if(ObjectUtils.isEmpty(item.gzFee) || item.gzFee == 0 || item.gzFee == '0.00'){
                    item.gzFee =''
                }else{
                    item.gzFee = GsExportSettingUtil.formatWith0(item.gzFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.azFee) || item.azFee == 0 || item.azFee == '0.00'){
                    item.azFee =''
                }else{
                    item.azFee =  GsExportSettingUtil.formatWith0(item.azFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.qtFee) || item.qtFee == 0 || item.qtFee == '0.00'){
                    item.qtFee =''
                }else{
                    item.qtFee =  GsExportSettingUtil.formatWith0(item.qtFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.price) || item.price == 0 || item.price == '0.00'){
                    item.price =''
                }else{
                    item.price = GsExportSettingUtil.formatWith0(item.price, GsExportSettingUtil.PRICE_ALL);
                }
                
                if(ObjectUtils.isEmpty(item.tzjzFee) || item.tzjzFee == 0 || item.tzjzFee == '0.00'){
                    item.tzjzFee =''
                }else{
                    item.tzjzFee =  GsExportSettingUtil.formatWith0(item.tzjzFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.tzszgzFee) || item.tzszgzFee == 0 || item.tzszgzFee == '0.00'){
                    item.tzszgzFee =''
                }else{
                    item.tzszgzFee = GsExportSettingUtil.formatWith0(item.tzszgzFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.tzazFee) || item.tzazFee == 0 || item.tzazFee == '0.00'){
                    item.tzazFee =''
                }else{
                    item.tzazFee = GsExportSettingUtil.formatWith0(item.tzazFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.tzprice) || item.tzprice == 0 || item.tzprice == '0.00'){
                    item.tzprice =''
                }else{
                    item.tzprice = GsExportSettingUtil.formatWith0(item.tzprice, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.tzqtFee) || item.tzqtFee == 0 || item.tzqtFee == '0.00'){
                    item.tzqtFee =''
                }else{
                    item.tzqtFee = GsExportSettingUtil.formatWith0(item.tzqtFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.diffAmount) || item.diffAmount == 0 || item.diffAmount == '0.00'){
                    item.diffAmount =''
                }else{
                    item.diffAmount = GsExportSettingUtil.formatWith0(item.diffAmount, GsExportSettingUtil.PRICE_ALL);
                }                    
            }
        }

        //设备购置费
        let sbgzfItem = Sheet4List.find(item=>item.name==="设备购置费");
        //设置单项的编号
        let rootProject = ProjectDomain.getDomain(param.constructId).getRoot();
        let childs = rootProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i < childs.length;i++){
                let findItem = Sheet4List.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = (i+1);
                this._fillProjectDispNo(childs[i],Sheet4List,findItem.dispNo)
            }
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
                sbgzfItem.dispNo = childs.length+1;
            }
        }else{
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
              sbgzfItem.dispNo = 1;
            }
        }

        return Sheet4List;
    }

    async getconstructProjectSheet14List(param) {
        let Sheet4List = [];

        let args = {};
        args.levelType = projectLevelConstant.construct;
        args.unitId = null;
        args.constructId = param.constructId;
        args.type = FunctionTypeConstants.SBGZF_KEY_TYPE_GW;
        let resultList = this.service.PreliminaryEstimate.gsEquipmentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            for (let item of resultList) {
                let otherMYArray = [];
                let otherRMBArra = [];
                let equipmentCostsCal = item.equipmentCostsCal;
                let otherMY = equipmentCostsCal.filter(o => o.costType === "其他（美元）");
                let otherRMB = equipmentCostsCal.filter(o => o.costType === "其他（人民币）");
                for (let i = 0; i < otherMY.length; i++) {
                    let myElement = otherMY[i];
                    let data = {};
                    data.dispNo = item.dispNo;
                    data.name = item.name;
                    data.unit = item.unit;
                    data.quantity = item.quantity;
                    data.fob_usd = item.fob_usd;
                    data.costNameMY = myElement.name;
                    data.costMoneyMY = myElement.price;
                    data.cif_usd = item.cif_usd;
                    data.cif_cny = item.cif_cny;
                    data.price = item.price;
                    data.totalPrice = item.totalPrice;
                    otherMYArray.push(data);
                }
                for (let i = 0; i < otherRMB.length; i++) {
                    let data1 = {};
                    if (ObjectUtils.isNotEmpty(otherMYArray[i])) {
                        data1 = otherMYArray[i];
                    }
                    data1.dispNo = item.dispNo;
                    data1.name = item.name;
                    data1.unit = item.unit;
                    data1.quantity = item.quantity;
                    data1.fob_usd = item.fob_usd;
                    data1.cif_usd = item.cif_usd;
                    data1.cif_cny = item.cif_cny;
                    data1.costNameRMB = otherRMB[i].name;
                    data1.costMoneyRMB = otherRMB[i].price;
                    data1.price = item.price;
                    data1.totalPrice = item.totalPrice;
                    otherRMBArra.push(data1);
                }
                if (ObjectUtils.isNotEmpty(otherRMBArra)) {
                    otherRMBArra.forEach(o => {
                        Sheet4List.push(o);
                    })
                } else {
                    otherMYArray.forEach(o => {
                        Sheet4List.push(o);
                    })
                }
            }
        }
        for(let item of Sheet4List){
            // item.quantity;
            // if(ObjectUtils.isEmpty(item.quantity) || item.quantity === 0 || item.quantity === '0.00') {
            //     item.quantity = '';
            // }
            if(ObjectUtils.isEmpty(item.fob_usd) || item.fob_usd === 0 || item.fob_usd === '0.00') {
                item.fob_usd = '';
            }else{
                item.fob_usd = GsExportSettingUtil.formatWith0(item.fob_usd, GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.costMoneyMY) || item.costMoneyMY === 0 || item.costMoneyMY === '0.00') {
                item.costMoneyMY = '';
            }else{
                item.costMoneyMY = GsExportSettingUtil.formatWith0(item.costMoneyMY, GsExportSettingUtil.PRICE_ALL);
            }
            //离岸价（美元）
            if(ObjectUtils.isEmpty(item.cif_usd) || item.cif_usd === 0 || item.cif_usd === '0.00') {
                item.cif_usd = '';
            }else{
                item.cif_usd = GsExportSettingUtil.formatWith0(item.cif_usd, GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.cif_cny) || item.cif_cny === 0 || item.cif_cny === '0.00') {
                item.cif_cny = '';
            }else{                
                item.cif_cny = GsExportSettingUtil.formatWith0(item.cif_cny, GsExportSettingUtil.PRICE_ALL);
            }
            //人民币费用金额
            if(ObjectUtils.isEmpty(item.costMoneyRMB) || item.costMoneyRMB === 0 || item.costMoneyRMB === '0.00') {
                item.costMoneyRMB = '';
            }else{
                item.costMoneyRMB = GsExportSettingUtil.formatWith0(item.costMoneyRMB, GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.price) || item.price === 0 || item.price === '0.00') {
                item.price = '';    
            }else{                
                item.price = GsExportSettingUtil.formatWith0(item.price, GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.totalPrice) || item.totalPrice === 0 || item.totalPrice === '0.00') {
                item.totalPrice = '';
            }else{
                item.totalPrice = GsExportSettingUtil.formatWith0(item.totalPrice, GsExportSettingUtil.PRICE_ALL);
            }
        }
        return Sheet4List;
    }

    async getconstructProjectSheet15List(param) {
        let Sheet4List = [];

        let args = {};
        args.levelType = 1;
        args.type = FunctionTypeConstants.SBGZF_KEY_TYPE_GN;
        args.unitId = null;
        args.constructId = param.constructId;

        let list = this.service.PreliminaryEstimate.gsEquipmentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(list)) {
            let filter = list.filter(o => ObjectUtils.isNotEmpty(o.quantity) && ObjectUtils.isNotEmpty(o.factoryPrice));
            if (ObjectUtils.isNotEmpty(filter)) {
                Sheet4List = ConvertUtil.deepCopy(filter);
            }
        }
        return Sheet4List;
    }


    async getconstructProjectSheet16List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 4;
        args.levelType = projectLevelConstant.construct;
        args.constructId = param.constructId;
        let list = await this.service.PreliminaryEstimate.gsRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(list)) {
            Sheet4List = ConvertUtil.deepCopy(list);
        }

        return Sheet4List;
    }


    async getconstructProjectSheet17List(param) {
        let Sheet4List = [];

        let MY = 0;
        let RMB = 0;
        //国外采购设置数据
        let args = {};
        args.levelType = 1;
        args.constructId = param.constructId;
        args.unitId = null;
        args.type = "sbgzf01";
        let gwsbList = this.service.PreliminaryEstimate.gsEquipmentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(gwsbList)) {
            for (let item of gwsbList) {
                // 离岸价美元fob_usd  数量quantity  汇率
                if (ObjectUtils.isNotEmpty(item.fob_usd) && ObjectUtils.isNotEmpty(item.quantity)) {
                    MY = NumberUtil.multiply(item.fob_usd, item.quantity).toFixed(2);
                }
                if (ObjectUtils.isNotEmpty(item.exchangeRate)) {
                    RMB = NumberUtil.multiply(MY, item.exchangeRate).toFixed(2);
                }
            }
        }

        //概算汇总数据
        let args1 = {};
        args1.constructId = param.constructId;
        let summaryList = await this.service.PreliminaryEstimate.gsEstimateSummaryService.getEstimateSummaryList(args1);
        if (ObjectUtils.isNotEmpty(summaryList)) {
            let deepCopy1 = ConvertUtil.deepCopy(summaryList);
            for (let item1 of deepCopy1) {
                if (item1.name === "工程费用" || item1.name === "设备购置费") {
                    item1.MY = MY;
                    item1.RMB = RMB;
                }
                Sheet4List.push(item1);
            }
        }
        for(let item of Sheet4List){
            if(ObjectUtils.isEmpty(item.jzFee) || item.jzFee == 0 || item.jzFee == '0.00'){
                item.jzFee = '';
            }else{
                item.jzFee = GsExportSettingUtil.formatWith0(NumberUtil.divide(item.jzFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.sbgzFee) || item.sbgzFee == 0 || item.sbgzFee == '0.00'){
                item.sbgzFee = '';
            }else{
                item.sbgzFee = GsExportSettingUtil.formatWith0(NumberUtil.divide(item.sbgzFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.azFee) || item.azFee == 0 || item.azFee == '0.00'){
                item.azFee = '';
            }else{
                item.azFee = GsExportSettingUtil.formatWith0(NumberUtil.divide(item.azFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.qtFee) || item.qtFee == 0 || item.qtFee == '0.00'){
                item.qtFee = '';
            }else{
                item.qtFee = GsExportSettingUtil.formatWith0(NumberUtil.divide(item.qtFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.price) || item.price == 0 || item.price == '0.00'){
                item.price = '';
            }else{
                item.price = GsExportSettingUtil.formatWith0(NumberUtil.divide(item.price,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.MY) || item.MY == 0 || item.MY == '0.00'){
                item.MY = '';
            }else{
                item.MY = GsExportSettingUtil.formatWith0(NumberUtil.divide(item.MY,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.RMB) || item.RMB == 0 || item.RMB == '0.00'){
                item.RMB = '';
            }else{
                item.RMB = GsExportSettingUtil.formatWith0(NumberUtil.divide(item.RMB,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.proportion) || item.proportion == 0 || item.proportion == '0.00'){
                item.proportion = '';
            }
        }
        
        //设备购置费
        let sbgzfItem = Sheet4List.find(item=>item.category==="设备购置费")
        //设置单项的编号
        let rootProject = ProjectDomain.getDomain(param.constructId).getRoot();
        let childs = rootProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i < childs.length;i++){
                let findItem = Sheet4List.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = (i+1);
                this._fillProjectDispNo(childs[i],Sheet4List,findItem.dispNo)
            }
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
                sbgzfItem.dispNo = childs.length+1;
            }
        }else{
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
              sbgzfItem.dispNo = 1;
            }
        }

        return Sheet4List;
    }


    async getconstructProjectSheet18List(param) {
        let Sheet4List = [];

        let MY = 0;
        let RMB = 0;
        //国外采购设置数据
        let args = {};
        args.levelType = 1;
        args.constructId = param.constructId;
        args.unitId = null;
        args.type = "sbgzf01";
        let gwsbList = this.service.PreliminaryEstimate.gsEquipmentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(gwsbList)) {
            for (let item of gwsbList) {
                // 离岸价美元fob_usd  数量quantity  汇率
                if (ObjectUtils.isNotEmpty(item.fob_usd) && ObjectUtils.isNotEmpty(item.quantity)) {
                    MY = NumberUtil.multiply(item.fob_usd, item.quantity).toFixed(2);
                }
                if (ObjectUtils.isNotEmpty(item.exchangeRate)) {
                    RMB = NumberUtil.multiply(MY, item.exchangeRate).toFixed(2);
                }
            }
        }

        //概算汇总数据
        let args1 = {};
        args1.constructId = param.constructId;
        let summaryList = await this.service.PreliminaryEstimate.gsEstimateSummaryService.getEstimateSummaryList(args1);
        if (ObjectUtils.isNotEmpty(summaryList)) {
            let deepCopy1 = ConvertUtil.deepCopy(summaryList);
            for (let item1 of deepCopy1) {
                if (item1.name === "工程费用" || item1.name === "设备购置费") {
                    item1.MY = MY;
                    item1.RMB = RMB;
                }
                Sheet4List.push(item1);
            }
        }
        for(let item of Sheet4List){
            if(ObjectUtils.isEmpty(item.jzFee) || item.jzFee == 0 || item.jzFee == '0.00'){
                item.jzFee = '';
            }else{
                item.jzFee = NumberUtil.numberFormat(NumberUtil.divide(item.jzFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.sbgzFee) || item.sbgzFee == 0 || item.sbgzFee == '0.00'){
                item.sbgzFee = '';
            }else{
                item.sbgzFee =  NumberUtil.numberFormat(NumberUtil.divide(item.sbgzFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.azFee) || item.azFee == 0 || item.azFee == '0.00'){
                item.azFee = '';
            }else{
                item.azFee =  NumberUtil.numberFormat(NumberUtil.divide(item.azFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.qtFee) || item.qtFee == 0 || item.qtFee == '0.00'){
                item.qtFee = '';
            }else{
                item.qtFee =  NumberUtil.numberFormat(NumberUtil.divide(item.qtFee,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.price) || item.price == 0 || item.price == '0.00'){
                item.price = '';
            }else{
                item.price =  NumberUtil.numberFormat(NumberUtil.divide(item.price,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.MY) || item.MY == 0 || item.MY == '0.00'){
                item.MY = '';
            }else{
                item.MY =  NumberUtil.numberFormat(NumberUtil.divide(item.MY,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.RMB) || item.RMB == 0 || item.RMB == '0.00'){
                item.RMB = '';
            }else{
                item.RMB =  NumberUtil.numberFormat(NumberUtil.divide(item.RMB,10000), GsExportSettingUtil.PRICE_ALL);
            }
            if(ObjectUtils.isEmpty(item.proportion) || item.proportion == 0 || item.proportion == '0.00'){
                item.proportion = '';
            }
        }

        //设备购置费
        let sbgzfItem = Sheet4List.find(item=>item.category==="设备购置费")
        //设置单项的编号
        let rootProject = ProjectDomain.getDomain(param.constructId).getRoot();
        let childs = rootProject.children;        
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i < childs.length;i++){
                let findItem = Sheet4List.find(item=>item.sequenceNbr === childs[i].sequenceNbr);
                findItem.dispNo = (i+1);
                this._fillProjectDispNo(childs[i],Sheet4List,findItem.dispNo)
            }
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
                sbgzfItem.dispNo = childs.length+1;
            }
        }else{
            if(ObjectUtils.isNotEmpty(sbgzfItem)){
              sbgzfItem.dispNo = 1;
            }
        }
        return Sheet4List;
    }

    async getconstructSingleSheet1List(param) {
        let Sheet4List = [];
        let args = {};
        args.type = projectLevelConstant.single;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = null;
        let costAnalysiss = await this.service.PreliminaryEstimate.gsCostAnalysisService.getCostAnalysisData(args);

        if (ObjectUtils.isNotEmpty(costAnalysiss.costAnalysisSingleVOList)) {
            let deepCopy1 = ConvertUtil.deepCopy(costAnalysiss.costAnalysisSingleVOList);
            await this.cacluCostAnalysiss(deepCopy1, Sheet4List, "1", false);
        }
        return Sheet4List;
    }


    async cacluCostAnalysiss(cacluData, result, parentDispNo, suffix) {
        if (ObjectUtils.isNotEmpty(cacluData)) {
            for (let i = 0; i < cacluData.length; i++) {
                let item = cacluData[i];
                item.dispNo = parentDispNo;
                if (suffix) {
                    let number = i + 1;
                    item.dispNo = parentDispNo + "." + number;
                }
                result.push(item);
                if (ObjectUtils.isNotEmpty(item.childrenList)) {
                    await this.cacluCostAnalysiss(item.childrenList, result, item.dispNo, true);
                }
            }
        }
    }

    _getFlatAdjust(gsAdjust,gsAllAdjusts,dispNo){
        let childs = gsAdjust.children;
        if(ObjectUtils.isNotEmpty(childs)){
            for(let i=0;i<childs.length;i++){
                this._getFlatAdjust(childs[i],gsAllAdjusts,dispNo+"."+(i+1));
            }
        }else{
            gsAdjust.dispNo = dispNo;
            gsAllAdjusts.push(gsAdjust);
        }
    }

    async getconstructSingleSheet2List(param) {
        let Sheet4List = [];
        let args = {};
        // args.type = projectLevelConstant.single;
        args.constructId = param.constructId;
        // args.singleId = param.singleId;
        // args.unitId = param.unitId;
        let gsAdjust = await this.service.PreliminaryEstimate.gsAdjustService.getGsAdjustList(args);
        //TODO 筛选出该单项的数据，目前数据应该是整个工程
        let  gsSingleAdjust = gsAdjust.find(item=>item.sequenceNbr === param.singleId);
        let  gsAllAdjusts = [];
        this._getFlatAdjust(gsSingleAdjust,gsAllAdjusts,1);
        if (ObjectUtils.isNotEmpty(gsAllAdjusts)) {
            Sheet4List = ConvertUtil.deepCopy(gsAllAdjusts);
            for(let item of Sheet4List){
                if(ObjectUtils.isEmpty(item.jzFee) || item.jzFee == 0 || item.jzFee == '0.00'){
                    item.jzFee =''
                }else{   
                    item.jzFee = GsExportSettingUtil.formatWith0(item.jzFee, GsExportSettingUtil.PRICE_ALL);
                }
                 if(ObjectUtils.isEmpty(item.gzFee) || item.gzFee == 0 || item.gzFee == '0.00'){
                    item.gzFee =''
                }else{
                    item.gzFee = GsExportSettingUtil.formatWith0(item.gzFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.azFee) || item.azFee == 0 || item.azFee == '0.00'){
                    item.azFee =''
                }else{
                    item.azFee =  GsExportSettingUtil.formatWith0(item.azFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.price) || item.price == 0 || item.price == '0.00'){
                    item.price =''
                }else{
                    item.price = GsExportSettingUtil.formatWith0(item.price, GsExportSettingUtil.PRICE_ALL);
                }
                
                if(ObjectUtils.isEmpty(item.tzjzFee) || item.tzjzFee == 0 || item.tzjzFee == '0.00'){
                    item.tzjzFee =''
                }else{
                    item.tzjzFee =  GsExportSettingUtil.formatWith0(item.tzjzFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.tzszgzFee) || item.tzszgzFee == 0 || item.tzszgzFee == '0.00'){
                    item.tzszgzFee =''
                }else{
                    item.tzszgzFee = GsExportSettingUtil.formatWith0(item.tzszgzFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.tzazFee) || item.tzazFee == 0 || item.tzazFee == '0.00'){
                    item.tzazFee =''
                }else{
                    item.tzazFee = GsExportSettingUtil.formatWith0(item.tzazFee, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.tzprice) || item.tzprice == 0 || item.tzprice == '0.00'){
                    item.tzprice =''
                }else{
                    item.tzprice = GsExportSettingUtil.formatWith0(item.tzprice, GsExportSettingUtil.PRICE_ALL);
                }
                if(ObjectUtils.isEmpty(item.diffAmount) || item.diffAmount == 0 || item.diffAmount == '0.00'){
                    item.diffAmount =''
                }else{
                    item.diffAmount = GsExportSettingUtil.formatWith0(item.diffAmount, GsExportSettingUtil.PRICE_ALL);
                }
            }
        }

        return Sheet4List;
    }

    async getconstructUnitSheet3List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";

        let projectDomain = ProjectDomain.getDomain(param.constructId);
        let pricingMethod = projectDomain.getRoot().pricingMethod === 1;//1 按市场价，，否则位否
        let deTree = projectDomain.getDeDomain().getDeTreeDepth(param.constructId,param.unitId);
        // let deTree = ProjectDomain.getDomain(param.constructId).getDeDomain().getDeTree(item => item.unitId === param.unitId);
        if (ObjectUtils.isNotEmpty(deTree)) {
            // let zfbList = deTree.filter(o => o.type === DeTypeConstants.DE_TYPE_ZFB);    //子分部集合
            // if (ObjectUtils.isNotEmpty(zfbList)) {
            //     let disNo = 1;
            //     let totalPriceExcel = 0;
            //     for (let p of zfbList) {
            //         let filter = deTree.filter(o => o.type === DeTypeConstants.DE_TYPE_DELIST && o.parentId === p.sequenceNbr);
            //         if (ObjectUtils.isNotEmpty(filter)) {
            //             let deepCopy1 = ConvertUtil.deepCopy(filter);
            //             let toNumber = deepCopy1.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.totalNumber) ? 0 : item.totalNumber), 0).toNumber();
            //             totalPriceExcel = NumberUtil.add(totalPriceExcel, toNumber);
            //             for (let item of deepCopy1) {      //主定额
            //                 item.dispNo = disNo++;
            //                 // await this.calDERGCLJXGRv2(param.constructId, param.unitId, item, deTree);        //计算定额的人工材料机械合计
            //                 resultList.push(item);
            //
            //                 //筛选主材费和设备费,并计算定额的人工材料机械费
            //                 await this.calDERCJLIST(param.constructId, param.unitId, item, item, deTree, resultList);
            //             }
            //         }
            //     }
            //     heji.total = totalPriceExcel;
            // }


            let deList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST && ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || (o.type === DeTypeConstants.DE_TYPE_DE && !ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || o.type === DeTypeConstants.DE_TYPE_RESOURCE || [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(o.type));    //定额集合
            if (ObjectUtils.isNotEmpty(deList)) {
                let deepCopy1 = ConvertUtil.deepCopy(deList);
                let disNo = 1;
                for (let item of deepCopy1) {      //主定额
                    let find = deTree.find(p=>p.sequenceNbr === item.parentId);
                    if (find.type === DeTypeConstants.DE_TYPE_DELIST || find.type === DeTypeConstants.DE_TYPE_DE) {
                        continue;
                    }

                    if (ObjectUtils.isNotEmpty(item.initDeRcjNameList)) {
                        for (let item1 of item.initDeRcjNameList) {
                            if (ObjectUtils.isNotEmpty(item1.replaceMaterialName)) {
                                item.deName = item.deName + item1.replaceMaterialName;
                            }
                        }
                    }
                    
                    // await this.calDERGCLJXGRv2(param.constructId, param.unitId, item, deTree);        //计算定额的人工材料机械合计
                    resultList.push(item);
                    //筛选主材费和设备费,并计算定额的人工材料机械费
                    let zcsbRcj = [];
                    await this.calDERCJLIST(param.constructId, param.unitId, item, item, deTree, resultList, zcsbRcj,pricingMethod);
                    //格式化主材费和设备费的价格及量
                    for(let zcsb of zcsbRcj) {
                        //格式化价格
                        zcsb.price = GsExportSettingUtil.formatWith0(zcsb.price, GsExportSettingUtil.PRICE_ALL);
                        zcsb.totalNumber = GsExportSettingUtil.formatWith0(zcsb.totalNumber, GsExportSettingUtil.PRICE_ALL);
                        zcsb.quantity = GsExportSettingUtil.formatWith0(zcsb.quantity, GsExportSettingUtil.QUANTITY_ALL);
                    }
                    
                    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(item.type)) {
                        //格式化价格
                        item.totalNumber = GsExportSettingUtil.formatWith0(pricingMethod?item.totalNumber : item.baseJournalTotalNumber, GsExportSettingUtil.PRICE_ALL);
                        item.dispNo = ' ';//不显示但是需要显示人材机费
                    }else{
                        item.dispNo = disNo++;
                        //单价
                        item.price = GsExportSettingUtil.formatWith0(pricingMethod?item.price : item.baseJournalPrice, GsExportSettingUtil.PRICE_ALL);
                        //合价
                        item.totalNumber = GsExportSettingUtil.formatWith0(pricingMethod?item.totalNumber : item.baseJournalTotalNumber, GsExportSettingUtil.PRICE_ALL);    
                        //工程量
                        item.quantity = GsExportSettingUtil.formatWith0(item.quantity, GsExportSettingUtil.QUANTITY_ALL);
                    }   
                    //格式化定额人材机费
                    item.rgf = GsExportSettingUtil.formatWith0(pricingMethod?item.rTotalSum:item.rdTotalSum, GsExportSettingUtil.PRICE_ALL);
                    item.clf = GsExportSettingUtil.formatWith0(pricingMethod?item.cTotalSum:item.cdTotalSum, GsExportSettingUtil.PRICE_ALL);
                    item.jxf = GsExportSettingUtil.formatWith0(pricingMethod?item.jTotalSum:item.jdTotalSum, GsExportSettingUtil.PRICE_ALL);
                }
                heji.total = deepCopy1.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.totalNumber) ? 0 : item.totalNumber), 0).toNumber();
                heji.total = GsExportSettingUtil.formatWith0(heji.total, GsExportSettingUtil.PRICE_ALL);
            }
        }

        param["unitDwgcgysba4sHeji"] = heji;
        return resultList;
    }

    /** *
     *  获取预算书定额A4竖  的变体-自然单位
     * @param param
     * @returns {Promise<*>}
     */
    async getconstructUnitSheet30List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";

        let projectDomain = ProjectDomain.getDomain(param.constructId);
        let pricingMethod = projectDomain.getRoot().pricingMethod === 1;//1 按市场价，，否则位否
        let deTree = projectDomain.getDeDomain().getDeTreeDepth(param.constructId,param.unitId);
        if (ObjectUtils.isNotEmpty(deTree)) {

            let deList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST && ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || (o.type === DeTypeConstants.DE_TYPE_DE && !ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || o.type === DeTypeConstants.DE_TYPE_RESOURCE || [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(o.type));    //定额集合
            if (ObjectUtils.isNotEmpty(deList)) {
                let deepCopy1 = ConvertUtil.deepCopy(deList);
                let disNo = 1;
                for (let item of deepCopy1) {      //主定额
                    let find = deTree.find(p=>p.sequenceNbr === item.parentId);
                    if (find.type === DeTypeConstants.DE_TYPE_DELIST || find.type === DeTypeConstants.DE_TYPE_DE) {
                        continue;
                    }

                    if (ObjectUtils.isNotEmpty(item.initDeRcjNameList)) {
                        for (let item1 of item.initDeRcjNameList) {
                            if (ObjectUtils.isNotEmpty(item1.replaceMaterialName)) {
                                item.deName = item.deName + item1.replaceMaterialName;
                            }
                        }
                    }
                    
                    // await this.calDERGCLJXGRv2(param.constructId, param.unitId, item, deTree);        //计算定额的人工材料机械合计
                    resultList.push(item);
                    //筛选主材费和设备费,并计算定额的人工材料机械费
                    let zcsbRcj = [];
                    await this.calDERCJLIST(param.constructId, param.unitId, item, item, deTree, resultList, zcsbRcj,pricingMethod);
                    //格式化主材费和设备费的价格及量
                    for(let zcsb of zcsbRcj) {
                        zcsb.price = GsExportSettingUtil.formatWith0(zcsb.price, GsExportSettingUtil.PRICE_ALL);
                        zcsb.totalNumber = GsExportSettingUtil.formatWith0(zcsb.totalNumber, GsExportSettingUtil.PRICE_ALL);
                        zcsb.quantity = GsExportSettingUtil.formatWith0(zcsb.quantity, GsExportSettingUtil.QUANTITY_ALL);
                    }
                    
                    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(item.type)) {
                        item.totalNumber = GsExportSettingUtil.formatWith0(pricingMethod?item.totalNumber:item.baseJournalTotalNumber, GsExportSettingUtil.PRICE_ALL);
                        item.dispNo = ' ';//不显示但是需要显示人材机费
                    }else{
                        item.dispNo = disNo++;
                        //单价
                        item.price = GsExportSettingUtil.formatWith0(pricingMethod?item.price:item.baseJournalPrice, GsExportSettingUtil.PRICE_ALL);
                        //合价
                        item.totalNumber = GsExportSettingUtil.formatWith0(pricingMethod?item.totalNumber:item.baseJournalTotalNumber, GsExportSettingUtil.PRICE_ALL);

                        let oUnit = UnitUtils.removeCharter(item.unit);
                        if(ObjectUtils.isEmpty(oUnit))
                        {
                            oUnit = 1;
                        }
                        item.unit = item.unit.replace(oUnit, ""); //去掉单位中的数字
                        //工程量
                        item.quantity = NumberUtil.numberFormat(NumberUtil.multiply(item.quantity,oUnit), GsExportSettingUtil.QUANTITY_DEFAULT);
                    }   
                    //格式化定额人材机费
                    item.rgf = GsExportSettingUtil.formatWith0(pricingMethod?item.rTotalSum:item.rdTotalSum, GsExportSettingUtil.PRICE_ALL);
                    item.clf = GsExportSettingUtil.formatWith0(pricingMethod?item.cTotalSum:item.cdTotalSum, GsExportSettingUtil.PRICE_ALL);
                    item.jxf = GsExportSettingUtil.formatWith0(pricingMethod?item.jTotalSum:item.jdTotalSum, GsExportSettingUtil.PRICE_ALL);
                }
                heji.total = deepCopy1.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.totalNumber) ? 0 : item.totalNumber), 0).toNumber();
                heji.total = GsExportSettingUtil.formatWith0(heji.total, GsExportSettingUtil.PRICE_ALL);
            }
        }

        param["unitDwgcgysba4sHeji"] = heji;
        return resultList;
    }

    async getconstructUnitSheet4List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";
        let projectDomain = ProjectDomain.getDomain(param.constructId);
        let pricingMethod = projectDomain.getRoot().pricingMethod === 1;//1 按市场价，，否则位否
        let deTree = projectDomain.getDeDomain().getDeTreeDepth(param.constructId,param.unitId);
        // let deTree = ProjectDomain.getDomain(param.constructId).getDeDomain().getDeTree(item => item.unitId === param.unitId);
        if (ObjectUtils.isNotEmpty(deTree)) {
            
            let deList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST && ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || (o.type === DeTypeConstants.DE_TYPE_DE && !ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || o.type === DeTypeConstants.DE_TYPE_RESOURCE
                || [DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB].includes(o.type));    //定额集合
            if (ObjectUtils.isNotEmpty(deList)) {
                let deepCopy1 = ConvertUtil.deepCopy(deList);
                let disNo = 1;
                for (let item of deepCopy1) {      //主定额
                    let find = deTree.find(p=>p.sequenceNbr === item.parentId);
                    if (find.type === DeTypeConstants.DE_TYPE_DELIST || find.type === DeTypeConstants.DE_TYPE_DE) {
                        continue;
                    }

                    if (ObjectUtils.isNotEmpty(item.initDeRcjNameList)) {
                        for (let item1 of item.initDeRcjNameList) {
                            if (ObjectUtils.isNotEmpty(item1.replaceMaterialName)) {
                                item.deName = item.deName + item1.replaceMaterialName;
                            }
                        }
                    }
                    // await this.calDERGCLJXGRv2(param.constructId, param.unitId, item, deTree);        //计算定额的人工材料机械合计
                    resultList.push(item);
                    //筛选主材费和设备费,并计算定额的人工材料机械费
                    let deRcjList = [];
                    await this.calDERCJLIST(param.constructId, param.unitId, item, item, deTree, resultList, deRcjList, pricingMethod);
                    //格式化主材费和设备费的价格及量
                    for(let rcj of deRcjList) {
                        rcj.price = GsExportSettingUtil.formatWith0(rcj.price, GsExportSettingUtil.PRICE_ALL);
                        rcj.totalNumber = GsExportSettingUtil.formatWith0(rcj.totalNumber, GsExportSettingUtil.PRICE_ALL);
                        rcj.quantity = NumberUtil.numberFormat(rcj.quantity, GsExportSettingUtil.QUANTITY_DEFAULT);
                    }
                    if( [DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB].includes(item.type)){
                        item.dispNo = ' '; //不显示但是需要显示人材机费
                        item.totalNumber = GsExportSettingUtil.formatWith0(pricingMethod?item.totalNumber:item.baseJournalTotalNumber, GsExportSettingUtil.PRICE_ALL);
                    }else{
                        item.dispNo = disNo++;
                        //单价
                        item.price = GsExportSettingUtil.formatWith0(pricingMethod?item.price:item.baseJournalPrice, GsExportSettingUtil.PRICE_ALL);
                        //合价
                        item.totalNumber = GsExportSettingUtil.formatWith0(pricingMethod?item.totalNumber:item.baseJournalTotalNumber, GsExportSettingUtil.PRICE_ALL);
                        //工程量
                        item.quantity = GsExportSettingUtil.formatWith0(item.quantity, GsExportSettingUtil.QUANTITY_ALL);
                    }
                    //格式化定额人材机费
                    item.rgf = GsExportSettingUtil.formatWith0(pricingMethod?item.rTotalSum:item.rdTotalSum, GsExportSettingUtil.PRICE_ALL);
                    item.clf = GsExportSettingUtil.formatWith0(pricingMethod?item.cTotalSum:item.cdTotalSum, GsExportSettingUtil.PRICE_ALL);
                    item.jxf = GsExportSettingUtil.formatWith0(pricingMethod?item.jTotalSum:item.jdTotalSum, GsExportSettingUtil.PRICE_ALL);
                    item.grhj = GsExportSettingUtil.formatWith0(item.grhj, GsExportSettingUtil.GRHZ_PRICE_ALL);
                }
                heji.total = deepCopy1.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.totalNumber) ? 0 : item.totalNumber), 0).toNumber();
                heji.total = GsExportSettingUtil.formatWith0(heji.total, GsExportSettingUtil.PRICE_ALL);
            }
        }

        param["unitDwgcgysba4hHeji"] = heji;
        return resultList;
    }


    async calDERCJLIST(constructId, unitId, deData, childDeData, allDeNode, resultList, rcjList, pricingMethod) {
        let childDeList = allDeNode.filter(o => o.parentId === childDeData.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_DE);
        if (ObjectUtils.isNotEmpty(childDeList)) {
            let childDeList1 = ConvertUtil.deepCopy(childDeList);
            for (let childDe of childDeList1) {
                //筛选主材费和设备费
                let deRcjList = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, childDe.sequenceNbr, DeTypeConstants.DE_TYPE_DE);
                if (ObjectUtils.isNotEmpty(deRcjList)) {
                    let deRcjList1 = ConvertUtil.deepCopy(deRcjList);
                    let zcsbList = deRcjList1.filter(o => o.type === "主材费" || o.type === "设备费");
                    if (ObjectUtils.isNotEmpty(zcsbList)) {
                        zcsbList.forEach(o => {
                            let find = rcjList.find(p => p.deCode === o.materialCode);
                            if (ObjectUtils.isNotEmpty(find)) {
                                //编码一致的人材机只进行数量汇总
                                find.quantity = NumberUtil.add(find.quantity, o.totalNumber);
                            } else {
                                o.deCode = o.materialCode;
                                o.deName = o.materialName;
                                o.quantity = o.totalNumber;
                                o.price = pricingMethod?o.marketPrice:o.dePrice;
                                o.totalNumber = o.total;  //合价
                                resultList.push(o);
                                rcjList.push(o);
                            }
                        })
                    }

                    // let RGFLIST = deRcjList1.filter(o => o.type === "人工费");
                    // let CLFLIST = deRcjList1.filter(o => o.type === "材料费");
                    // let JXFLIST = deRcjList1.filter(o => o.type === "机械费");
                    let GRHJLIST = deRcjList1.filter(o => o.type === "人工费" && o.unit === "工日");
                    // if (ObjectUtils.isNotEmpty(RGFLIST)) {
                    //     let toNumber = RGFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                    //     deData.rgf = NumberUtil.add(deData.rgf, toNumber);
                    // }
                    // if (ObjectUtils.isNotEmpty(CLFLIST)) {
                    //     let toNumber1 = CLFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                    //     deData.clf = NumberUtil.add(deData.clf, toNumber1);
                    // }
                    // if (ObjectUtils.isNotEmpty(JXFLIST)) {
                    //     let toNumber2 = JXFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                    //     deData.jxf = NumberUtil.add(deData.jxf, toNumber2);
                    // }
                    if (ObjectUtils.isNotEmpty(GRHJLIST)) {
                        if(!pricingMethod) {
                            for(let rghj of GRHJLIST) {
                                rghj.total = NumberUtil.multiply(rghj.totalNumber , rghj.dePrice); //合价
                            }
                        }
                        let toNumber3 = GRHJLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.grhj = NumberUtil.add(deData.grhj, toNumber3);
                    }
                }

                let child2DeList = allDeNode.filter(o => o.parentId === childDe.sequenceNbr);
                if (ObjectUtils.isNotEmpty(child2DeList)) {
                    //说明地下还有子集，迭代查询
                    await this.calDERCJLIST(constructId, unitId, deData, childDe, allDeNode, resultList, rcjList, pricingMethod);
                }
            }
        }
    }


    async calDERGCLJXGRv2(constructId, unitId, deData, allDeNode) {
        let childDeList = allDeNode.filter(o => o.parentId === deData.sequenceNbr);
        if (ObjectUtils.isNotEmpty(childDeList)) {
            for (let childDe of childDeList) {
                let deRcjList = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, childDe.sequenceNbr, DeTypeConstants.DE_TYPE_DE);
                if (ObjectUtils.isNotEmpty(deRcjList)) {
                    let RGFLIST = deRcjList.filter(o => o.type == "人工费");
                    let CLFLIST = deRcjList.filter(o => o.type == "材料费");
                    let JXFLIST = deRcjList.filter(o => o.type == "机械费");
                    let GRHJLIST = deRcjList.filter(o => o.type == "人工费" && o.unit == "工日");

                    if (ObjectUtils.isNotEmpty(RGFLIST)) {
                        let toNumber = RGFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.rgf = NumberUtil.add(deData.rgf, toNumber);
                    }
                    if (ObjectUtils.isNotEmpty(CLFLIST)) {
                        let toNumber1 = CLFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.clf = NumberUtil.add(deData.clf, toNumber1);
                    }
                    if (ObjectUtils.isNotEmpty(JXFLIST)) {
                        let toNumber2 = JXFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                        deData.jxf = NumberUtil.add(deData.jxf, toNumber2);
                    }
                    if (ObjectUtils.isNotEmpty(GRHJLIST)) {
                        let toNumber3 = JXFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.totalNumber) ? 0 : item.totalNumber), 0).toNumber();
                        deData.grhj = NumberUtil.add(deData.grhj, toNumber3);
                    }
                }
            }
        }
    }


    async calDERGCLJXGR(constructId, unitId, deData) {
        let deRcjList = await this.service.PreliminaryEstimate.gsRcjService.getAllRcjDetail(constructId, unitId, deData.sequenceNbr, DeTypeConstants.DE_TYPE_DE);
        if (ObjectUtils.isNotEmpty(deRcjList)) {
            let RGFLIST = deRcjList.filter(o => o.type == "人工费");
            let CLFLIST = deRcjList.filter(o => o.type == "材料费");
            let JXFLIST = deRcjList.filter(o => o.type == "机械费");
            let GRHJLIST = deRcjList.filter(o => o.type == "人工费" && o.unit == "工日");

            if (ObjectUtils.isNotEmpty(RGFLIST)) {
                deData.rgf = RGFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
            }
            if (ObjectUtils.isNotEmpty(CLFLIST)) {
                deData.clf = CLFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
            }
            if (ObjectUtils.isNotEmpty(JXFLIST)) {
                deData.jxf = JXFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
            }
            if (ObjectUtils.isNotEmpty(GRHJLIST)) {
                //合价
                // item.grhj = JXFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
                //合计数量
                deData.grhj = JXFLIST.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.totalNumber) ? 0 : item.totalNumber), 0).toNumber();
            }
        }
    }


    async getconstructUnitSheet5List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";

        let unit = ProjectDomain.getDomain(param.constructId).getProjectById(param.unitId);

        let args = {};
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        args.type = 1;
        args.levelType = projectLevelConstant.unit;
        args.code = "8";

        let majorMenuList = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getCostSummaryMajorMenuList(args);
        if (ObjectUtils.isNotEmpty(majorMenuList.itemList)) {
            if (majorMenuList.itemList.length === 1) {
                //说明没汇总，只有一个专业数据
                args.constructMajorType = unit.constructMajorType;
                let costSummaryList = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummaryList(args);
                if (ObjectUtils.isNotEmpty(costSummaryList)) {
                    let costSummaryPrintList = costSummaryList.filter(p => p.whetherPrint == 1);
                    if (ObjectUtils.isNotEmpty(costSummaryPrintList)) {
                        for (let map1 of costSummaryPrintList) {
                            //100不显示
                            map1.rate = ObjectUtils.isNotEmpty(map1.rate) ? map1.rate=="100"?"":map1.rate : "";
                            //价格保留2位
                            map1.price = GsExportSettingUtil.formatWith0(map1.price,GsExportSettingUtil.PRICE_ALL);
                            
                            if (map1.name === "工程造价" && ObjectUtils.isNotEmpty(map1.price)) {
                                heji.total = map1.price;
                            }
                            resultList.push(map1);
                        }
                    }
                }

                // let data = {};
                // data.name = "工程造价";
                // for (let map1 of costSummaryList) {
                //     if (map1.name === "工程造价" && ObjectUtils.isNotEmpty(map1.price)) {
                //         data.instructions = "专业造价总合计";
                //         data.rate = map1.rate;
                //         data.price = map1.price;
                //         break;
                //     }
                // }
                // heji.total = data.price;
            } else if (majorMenuList.itemList.length > 1) {
                //说明汇总了，有俩专业数据
                for (let menu of majorMenuList.itemList) {
                    args.constructMajorType = Object.keys(menu)[0];
                    let firstValue = Object.values(menu)[0];
                    let costSummaryList1 = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummaryList(args);
                    let data = {};
                    data.name = firstValue;
                    for (let map1 of costSummaryList1) {
                        if (map1.name === "工程造价" && ObjectUtils.isNotEmpty(map1.price)) {
                            data.instructions = map1.instructions;
                            //100不显示
                            data.rate = ObjectUtils.isNotEmpty(map1.rate) ? map1.rate=='100'?"":map1.rate : "";
                            //保留2位，补充0
                            data.price = GsExportSettingUtil.formatWith0(map1.price,GsExportSettingUtil.PRICE_ALL);
                            
                            break;
                        }
                    }
                    resultList.push(data);
                }

                let dataAll = {};
                dataAll.name = "工程造价";
                dataAll.instructions = "专业造价总合计";
                dataAll.rate = "";
                let allPrice = resultList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.price) ? 0 : item.price), 0).toNumber();
               
                //保留2位，补充0
                dataAll.price = GsExportSettingUtil.formatWith0(allPrice,GsExportSettingUtil.PRICE_ALL);
                heji.total = dataAll.price;
                resultList.push(dataAll);
            }
        }

        param["unitDwgcfyHeji"] = heji;
        return resultList;
    }


    async getconstructUnitSheet6List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";

        let args = {};
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        args.constructMajorType = "2018-JZGC-GS"
        let costSummaryList11 = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummaryList(args);
        let costSummaryPrintList = costSummaryList11.filter(p => p.whetherPrint == 1);
        if (ObjectUtils.isNotEmpty(costSummaryPrintList)) {
            for (let map1 of costSummaryPrintList) {
                let data = {};
                data.name = map1.name;
                data.instructions = map1.instructions;

                data.rate = map1.rate == 100?"":map1.rate; //100不显示
                data.price = GsExportSettingUtil.formatWith0(map1.price, GsExportSettingUtil.PRICE_ALL);
                if (data.name === "工程造价") {
                    heji.total = data.price;
                }
                resultList.push(data);
            }
        }
        param["unitDwgcfyjzgcHeji"] = heji;

        return resultList;
    }

    async getconstructUnitSheet7List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";

        let args = {};
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        args.constructMajorType = "2018-AZGC-GS"
        let costSummaryList11 = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummaryList(args);
        let costSummaryPrintList = costSummaryList11.filter(p => p.whetherPrint == 1);
        if (ObjectUtils.isNotEmpty(costSummaryPrintList)) {
            for (let map1 of costSummaryPrintList) {
                let data = {};
                data.name = map1.name;
                data.instructions = map1.instructions;
                data.rate = map1.rate == 100?"":map1.rate; //100不显示
                //价格保留2位，补充0
                data.price = GsExportSettingUtil.formatWith0(map1.price, GsExportSettingUtil.PRICE_ALL);
                if (data.name === "工程造价") {
                    heji.total = data.price;
                }
                resultList.push(data);
            }
        }
        param["unitDwgcfyzgcHeji"] = heji;

        return resultList;
    }

    async getconstructUnitSheet8List(param) {
        let Sheet4List = [];
        let heji = {};
        heji.total = "";
        heji.ystotal = "";
        heji.sctotal = "";

        let args = {};
        args.kind = 0;
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        let resultList = await this.service.PreliminaryEstimate.gsRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            let deepCopyResultList = ConvertUtil.deepCopy(resultList);
            for(let rcj of deepCopyResultList) {
                //格式化价格
                rcj.totalNumber = GsExportSettingUtil.formatWith0(rcj.totalNumber, GsExportSettingUtil.TOTALNUMBER_ALL);
                rcj.total = NumberUtil.numberFormat(rcj.total, GsExportSettingUtil.PRICE_ALL);
                rcj.dePrice = NumberUtil.numberFormat(rcj.dePrice, GsExportSettingUtil.PRICE_ALL);
                rcj.marketPrice = NumberUtil.numberFormat(rcj.marketPrice, GsExportSettingUtil.PRICE_ALL);
            }
            //添加合计行
            heji.total = deepCopyResultList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.total) ? 0 : item.total), 0).toNumber();
            heji.total = NumberUtil.numberFormat(heji.total, GsExportSettingUtil.PRICE_ALL);
            heji.ystotal = deepCopyResultList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.dePrice) ? 0 : item.dePrice), 0).toNumber();
            heji.ystotal = NumberUtil.numberFormat(heji.ystotal, GsExportSettingUtil.PRICE_ALL);
            heji.sctotal = deepCopyResultList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.marketPrice) ? 0 : item.marketPrice), 0).toNumber();
            heji.sctotal = NumberUtil.numberFormat(heji.sctotal, GsExportSettingUtil.PRICE_ALL);
            Sheet4List = deepCopyResultList;
        }
        let groupAllRcjs = Sheet4List.reduce((acc,obj)=> {
            const key = obj.kind;
            if(!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(obj);
            return acc;
        }, {});
        let newSheet4List = [];
        let groupKeys = Object.keys(groupAllRcjs);
        let clfAllRcj = []
        let rgfAllRcj = [];
        let jxfAllRcj = [];
        let zcfAllRcj = [];
        let sbfAllRcj = [];
        let pbclfAllRcj = [];//配比材料
        //分组处理
        for(let key of groupKeys) {
            let groupRcj = groupAllRcjs[key];
            if(ObjectUtils.isEmpty(groupRcj)){
                continue;
            }
            //人工费
            if(key == ResourceKindConstants.INT_TYPE_R ) {
                rgfAllRcj = this._fillRcjData(rgfAllRcj, groupRcj,"total", "人工");
            }
            //机械费
            else if(key == ResourceKindConstants.INT_TYPE_J) {
                jxfAllRcj = this._fillRcjData(jxfAllRcj, groupRcj, "total", "机械");
            }else if (key == ResourceKindConstants.INT_TYPE_ZC){
                zcfAllRcj = this._fillRcjData(zcfAllRcj, groupRcj, "total", "主材");
            }else if (key == ResourceKindConstants.INT_TYPE_SB){
                sbfAllRcj = this._fillRcjData(sbfAllRcj, groupRcj, "total", "设备");
            }else if (key == 10){//配比材料
                pbclfAllRcj = this._fillRcjData(pbclfAllRcj, groupRcj, "total", "配比");
            }
            //材料费
            else {
                clfAllRcj = clfAllRcj.concat(groupRcj);
            }
        }
        if(ObjectUtils.isNotEmpty(clfAllRcj)) {
            let tempClf = [];
            tempClf = this._fillRcjData(tempClf,clfAllRcj, "total", "材料");
            clfAllRcj = tempClf;
        }
        newSheet4List = newSheet4List.concat(rgfAllRcj,clfAllRcj,pbclfAllRcj,jxfAllRcj,zcfAllRcj,sbfAllRcj);
        //添加合计行
        let headCount = 1;
        newSheet4List.forEach((item, index) => {
            if(item.materialCode == "headTitle" ){
                item.dispNo = NumberUtil.numberToChinese(headCount);
                headCount++;
            }
        });
        newSheet4List.pop(); //去掉最后一个空行
        param['unitRcjHeji'] = heji;
        return newSheet4List;

    }
    _fillRcjData(rgfAllRcj, groupRcj,sumField,headMaterialName) {

        let headRcj = this._getRcjData("headTitle",headMaterialName);
        let tailRcj = this._getRcjData("","小计");
        let spaceRcj = this._getRcjData(""," ");
        let sumTotal = groupRcj.reduce((sum, item) => Decimal.add(sum, item[sumField]), 0).toNumber();
        tailRcj[sumField] = NumberUtil.numberFormat(sumTotal, GsExportSettingUtil.PRICE_ALL);
        groupRcj.forEach((item,index)=>item.dispNo = index + 1);
        rgfAllRcj.push(headRcj);
        rgfAllRcj = rgfAllRcj.concat(groupRcj);
        rgfAllRcj.push(tailRcj);
        rgfAllRcj.push(spaceRcj);

        return rgfAllRcj;
    }
    _getRcjData(materialCode,materialName) {
        let tailRcj = new GsConstructProjectRcj();
        tailRcj.dispNo='';
        tailRcj.materialCode = materialCode;
        tailRcj.materialName = materialName;
        tailRcj.unit = "";
        tailRcj.totalNumber = "";
        tailRcj.dePrice = "";
        tailRcj.marketPrice = "";
        tailRcj.total = "";
        return tailRcj;
    }

    async getconstructUnitSheet9List(param) {
        let Sheet4List = [];
        let heji = {};
        heji.ystotal = "";
        heji.sctotal = "";
        heji.jc = "";
        heji.jctotal = "";

        let args = {};
        args.kind = 0;
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        let resultList = await this.service.PreliminaryEstimate.gsRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            let deepCopyResultList = ConvertUtil.deepCopy(resultList);
            let jcReslutList = deepCopyResultList.filter(o => ObjectUtils.isNotEmpty(o.priceDifferenc) && o.priceDifferenc != 0);
            if (ObjectUtils.isNotEmpty(jcReslutList)) {
                for(let jc of jcReslutList) {
                    jc.totalNumber = GsExportSettingUtil.formatWith0(jc.totalNumber, GsExportSettingUtil.TOTALNUMBER_ALL);
                    jc.dePrice = GsExportSettingUtil.formatWith0(jc.dePrice, GsExportSettingUtil.PRICE_ALL);
                    jc.marketPrice = GsExportSettingUtil.formatWith0(jc.marketPrice, GsExportSettingUtil.PRICE_ALL);
                    jc.total = GsExportSettingUtil.formatWith0(jc.total, GsExportSettingUtil.PRICE_ALL);
                    jc.priceDifferenc = GsExportSettingUtil.formatWith0(jc.priceDifferenc, GsExportSettingUtil.PRICE_ALL);
                }

                heji.ystotal = jcReslutList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.dePrice) ? 0 : item.dePrice), 0).toNumber();
                heji.ystotal = GsExportSettingUtil.formatWith0(heji.ystotal, GsExportSettingUtil.PRICE_ALL);
                heji.sctotal = jcReslutList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.marketPrice) ? 0 : item.marketPrice), 0).toNumber();
                heji.sctotal = GsExportSettingUtil.formatWith0(heji.sctotal, GsExportSettingUtil.PRICE_ALL);
                heji.jc = jcReslutList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.priceDifferenc) ? 0 : item.priceDifferenc), 0).toNumber();
                heji.jc = GsExportSettingUtil.formatWith0(heji.jc, GsExportSettingUtil.PRICE_ALL);
                heji.jctotal = jcReslutList.reduce((sum, item) => Decimal.add(sum, ObjectUtils.isEmpty(item.priceDifferencSum) ? 0 : item.priceDifferencSum), 0).toNumber();
                heji.jctotal = GsExportSettingUtil.formatWith0(heji.jctotal, GsExportSettingUtil.PRICE_ALL);
                Sheet4List = jcReslutList;
            }
        }
        let groupAllRcjs = Sheet4List.reduce((acc,obj)=> {
            const key = obj.kind;
            if(!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(obj);
            return acc;
        }, {});
        let newSheet4List = [];
        let groupKeys = Object.keys(groupAllRcjs);
        let clfAllRcj = []
        let rgfAllRcj = [];
        let jxfAllRcj = [];
        //分组处理
        for(let key of groupKeys) {
            let groupRcj = groupAllRcjs[key];
            if(ObjectUtils.isEmpty(groupRcj)){
                continue;
            }
            //人工费
            if(key == ResourceKindConstants.INT_TYPE_R ) {
                rgfAllRcj = this._fillRcjData(rgfAllRcj, groupRcj, "priceDifferencSum", "人工");
            }
            //机械费
            else if(key == ResourceKindConstants.INT_TYPE_J) {
                jxfAllRcj = this._fillRcjData(jxfAllRcj, groupRcj, "priceDifferencSum", "机械");
            }
            //材料费
            else {
                clfAllRcj = clfAllRcj.concat(groupRcj);
            }
        }
        if(ObjectUtils.isNotEmpty(clfAllRcj)) {
            let tempClf = [];
            tempClf = this._fillRcjData(tempClf,clfAllRcj,"priceDifferencSum", "材料");
            clfAllRcj = tempClf;
        }
        newSheet4List = newSheet4List.concat(rgfAllRcj,clfAllRcj,jxfAllRcj);
        //添加合计行
        let headCount = 1;
        newSheet4List.forEach((item, index) => {
            if(item.materialCode == "headTitle" ){
                item.dispNo = NumberUtil.numberToChinese(headCount);
                headCount++;
            }
        });
        newSheet4List.pop(); //去掉最后一个空行

        param['unitRcjjcHeji'] = heji;
        return newSheet4List;
    }


    async getconstructUnitSheet10List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 0;
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        let resultList = await this.service.PreliminaryEstimate.gsRcjCollectService.getScList(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            let deepCopyResultList = ConvertUtil.deepCopy(resultList);
            Sheet4List = deepCopyResultList;
            Sheet4List.forEach((item, index) => {
                //格式化价格
                item.totalNumber = GsExportSettingUtil.formatWith0(item.scCount, GsExportSettingUtil.TOTALNUMBER_ALL);
                if(item.name == '钢筋'){
                    item.materialName = "其中：钢筋"
                }else{
                    item.materialName = item.name;
                }
            });
        }

        return Sheet4List;
    }

    async getconstructUnitSheet11List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 5;
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        let resultList = await this.service.PreliminaryEstimate.gsRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            Sheet4List = ConvertUtil.deepCopy(resultList);
            for(let zcRcj of Sheet4List) {
                zcRcj.totalNumber = GsExportSettingUtil.formatWith0(zcRcj.totalNumber, GsExportSettingUtil.TOTALNUMBER_ALL);
                zcRcj.marketPrice = GsExportSettingUtil.formatWith0(zcRcj.marketPrice, GsExportSettingUtil.PRICE_ALL);
                zcRcj.total = GsExportSettingUtil.formatWith0(zcRcj.total, GsExportSettingUtil.PRICE_ALL);
            }
        }

        return Sheet4List;
    }


    async getconstructUnitSheet12List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 6;
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        let resultList = await this.service.PreliminaryEstimate.gsRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            Sheet4List = ConvertUtil.deepCopy(resultList);
            for(let sbRcj of Sheet4List) {
                sbRcj.totalNumber = GsExportSettingUtil.formatWith0(sbRcj.totalNumber, GsExportSettingUtil.TOTALNUMBER_3_ALL);
                sbRcj.marketPrice = GsExportSettingUtil.formatWith0(sbRcj.marketPrice, GsExportSettingUtil.PRICE_ALL);
                sbRcj.total = GsExportSettingUtil.formatWith0(sbRcj.total, GsExportSettingUtil.PRICE_ALL);
            }
        }

        return Sheet4List;
    }

    async getconstructUnitSheet13List(param) {
        let Sheet4List = [];


        let heji = {};
        heji.total = "";

        let args = {};
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        const dlf = await this.service.PreliminaryEstimate.gsIndependentCostsService.getList(args);
        if (ObjectUtils.isNotEmpty(dlf.list)) {
            Sheet4List = ConvertUtil.deepCopy(dlf.list);
            for(let item of Sheet4List) {
                if(item.editConfig){
                    item.price = GsExportSettingUtil.formatWith0(item.price, GsExportSettingUtil.PRICE_ALL);
                    item.quantity = ObjectUtils.isEmpty(item.quantity) ? 0 : item.quantity;
                }
                //格式化价格
                item.totalPrice = GsExportSettingUtil.formatWith0(item.totalPrice, GsExportSettingUtil.PRICE_ALL);
            }
            if(Sheet4List.length == 1){
                Sheet4List[0].quantity=0;
            }
            let filter = Sheet4List.filter(o => !o.dispNo.includes("."));
            if (ObjectUtils.isNotEmpty(filter)) {
                heji.total = filter.reduce((sum, item) => sum + Number(ObjectUtils.isEmpty(item.totalPrice) ? 0 : item.totalPrice), 0);
                heji.total = GsExportSettingUtil.formatWith0(heji.total, GsExportSettingUtil.PRICE_ALL);
            }
        }

        param["unitDlfHeji"] = heji;
        return Sheet4List;
    }


    async getconstructUnitSheet14List(param) {
        let Sheet4List = [];

        let args = {};
        args.kind = 7;
        args.levelType = projectLevelConstant.unit;
        args.constructId = param.constructId;
        args.singleId = param.singleId;
        args.unitId = param.unitId;
        let resultList = await this.service.PreliminaryEstimate.gsRcjCollectService.getRcjCellectData(args);
        if (ObjectUtils.isNotEmpty(resultList)) {
            Sheet4List = ConvertUtil.deepCopy(resultList);
            for(let zyRcj of Sheet4List){
                zyRcj.totalNumber = GsExportSettingUtil.formatWith0(zyRcj.totalNumber, GsExportSettingUtil.TOTALNUMBER_ALL);
                zyRcj.marketPrice = NumberUtil.numberFormat(zyRcj.marketPrice, GsExportSettingUtil.PRICE_ALL);
                zyRcj.total =  NumberUtil.numberFormat(zyRcj.total, GsExportSettingUtil.PRICE_ALL);
                zyRcj.dePrice = NumberUtil.numberFormat(zyRcj.dePrice, GsExportSettingUtil.PRICE_ALL);
            }
        }

        return Sheet4List;
    }
    /** *
     *  获取单位全费指标分析
     * @param param
     * @returns {Promise<*>}
     */
    async getconstructUnitSheet15List(param) {
        let resultList = [];
        let heji = {};
        heji.total = "";

        let projectDomain = ProjectDomain.getDomain(param.constructId);
        let deTree = projectDomain.getDeDomain().getDeTreeDepth(param.constructId,param.unitId);
        if (ObjectUtils.isNotEmpty(deTree)) {

            let deList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST && ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || (o.type === DeTypeConstants.DE_TYPE_DE && !ZSFeeConstants.ZS_DE_LIB.includes(o.libraryCode))
                || o.type === DeTypeConstants.DE_TYPE_RESOURCE 
                || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
                || o.type === DeTypeConstants.DE_TYPE_USER_DE
                || [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(o.type));    //定额集合
            if (ObjectUtils.isNotEmpty(deList)) {
                let deepCopy1 = ConvertUtil.deepCopy(deList);
                let disNo = 1;
                for (let item of deepCopy1) {      //主定额
                    let find = deTree.find(p=>p.sequenceNbr === item.parentId);
                    if (find.type === DeTypeConstants.DE_TYPE_DELIST || find.type === DeTypeConstants.DE_TYPE_DE) {
                        continue;
                    }

                    if (ObjectUtils.isNotEmpty(item.initDeRcjNameList)) {
                        for (let item1 of item.initDeRcjNameList) {
                            if (ObjectUtils.isNotEmpty(item1.replaceMaterialName)) {
                                item.deName = item.deName + item1.replaceMaterialName;
                            }
                        }
                    }
                    
                    // await this.calDERGCLJXGRv2(param.constructId, param.unitId, item, deTree);        //计算定额的人工材料机械合计
                    resultList.push(item);
                    
                    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(item.type)) {
                        item.dispNo = ' ';//不显示但是需要显示人材机费
                    }else{
                        item.dispNo = disNo++;
                       if (ObjectUtils.isEmpty(item.quantity) || item.quantity == 0) {
                        item.quantity = '';
                       }else{
                        //工程量
                        item.quantity = NumberUtil.numberFormat(item.quantity, GsExportSettingUtil.TOTALNUMBER_ALL);
                       }
                    }   
                    
                }
            }
        }
        let fbfx = {}
        fbfx.deName='分部分项合计';
        resultList.push(fbfx);
        param["unitDwqfzbfxbHeji"] = heji;
        return resultList;
    }


    getProjectRootPath() {
        // let relativePath = __filename;
        // let index = relativePath.indexOf("pricing-cs");
        // let prefix = relativePath.substring(0,index);
        return UtilsPs.getExtraResourcesDir();
        // return prefix+"pricing-cs";
    }

    async showSheetStyleSample() {

        let excelPath = this.getProjectRootPath() + "\\excelTemplate\\unit\\sample.xlsx";
        let sheetName = "表1-6 分部分项工程量清单与计价表";
        let worksheet = await GSExcelUtil.read(excelPath, sheetName);
        let result = await GSExcelUtil.findCellStyleList(worksheet);
        return ResponseData.success(result);
    }

}


GsExportQueryService
    .toString = () => '[class GsExportQueryService]';
module
    .exports = GsExportQueryService;
