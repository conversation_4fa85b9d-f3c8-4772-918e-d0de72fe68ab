const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const DeTypeConstants = require("../../../constants/DeTypeConstants");
const {ObjectUtil} = require("../../../../../common/ObjectUtil");
const {deFlattenerBaseFn, deFlattenerRules} = require("./DeFlattenerCodes");
const {NumberUtil} = require("../../../utils/NumberUtil");
const LogUtil = require("../../../core/tools/logUtil");
const CommonConstants = require('../../../constants/CommonConstants');
const DeQualityUtils = require('../../utils/DeQualityUtils');
const DeUtils = require('../../utils/DeUtils');
const EE = require('../../../../../core/ee');

/**
 * 工程量铺设器
 */
class DeFlattener extends CalculateEngine{
    static SPLITOR = "_";
    constructId;
    deRowId;
    currentDe;
    unitId;
    precision;
    resourceId;
    priceCodes = [];
    ctx;
    filterTempRemoveRow;//是否过滤临时删除项
    relatedDeRows ;
    digitPropertyMap = new Map();
    static  DebMap = [
        "quantity"
        // ,"originalQuantity"//被QuantityExpression替代,不计算了
    ];
    initDigitPropertyMap()
    {
        this.digitPropertyMap.set("resQty",-1);//不在过程中取精度
        this.digitPropertyMap.set("quantity",-1);//不在过程中取精度
        // this.digitPropertyMap.set("originalQuantity",8);//被QuantityExpression替代
    }
    convertValue(value,param) {
        let paramArray = param.split(DeFlattener.SPLITOR);
        let digits = this.digitPropertyMap.get(paramArray[0]);
        if(ObjectUtil.isEmpty(digits)) {
            digits = 2;
        }
        if(digits<0){
            return value;
        }
        return NumberUtil.numberScale(value, digits);
    }
    static getInstance({constructId, unitId,deRowId},ctx,filterTempRemoveRow,priceCodes){
        return new DeFlattener(constructId,unitId,deRowId,null,ctx,filterTempRemoveRow,priceCodes);
    }

    /**
     *
     * @param constructId 当前工程
     * @param unitId 当前修改的人材机所属的单位工程
     * @param deRowId 当前修改的人材机所属的定额
     * @param resourceId 为当前修改的人材机ID
     * @param ctx
     */
    constructor(constructId,unitId,deRowId,resourceId,ctx,filterTempRemoveRow,priceCodes) {
        super(ctx);
        this.ctx = ctx;
        this.constructId = constructId;
        this.unitId = unitId;
        this.deRowId = deRowId;
        this.resourceId = resourceId;
        this.relatedDeRows = [];
        this.initDigitPropertyMap();
        this.filterTempRemoveRow = filterTempRemoveRow;
        this.priceCodes = priceCodes;
    }

    async prepare()
    {
        let {service} = EE.app;
        this.precision = await service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(this.constructId);
    }

    async analyze() {
        //措施数据0 01 02 03 类型不更新工程量
        this.currentDe = this.ctx.allDeMap.find(item => item.sequenceNbr === this.deRowId );
        if(this.currentDe.isCsxm == 1 && [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DELIST].includes(this.currentDe.type)){
            
            return;
        }
        await this.prepare();
        this.preload(deFlattenerBaseFn);
        this.buildRules();
        await this.render()
    }
    _

    /**
     * 回填数据
     * @returns {Promise<void>}
     */
    async render() {

        for(let currentDeRow of this.relatedDeRows)
        {
            if([DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(currentDeRow.type)){
                continue;
            }
            let precisionObj = DeUtils.getPrecisionByDe(this.precision,currentDeRow);
            if(ObjectUtil.isNotEmpty(currentDeRow)) {
                for (let key of DeFlattener.DebMap) {
                    let digits = precisionObj[key];
                    if(key === 'quantity'){
                        digits = DeUtils.getQuantiyPrecision(this.precision,currentDeRow);
                    }
                    if(ObjectUtil.isEmpty(digits)){
                        digits = this.digitPropertyMap.get(key);
                        if(ObjectUtil.isEmpty(digits)) {
                            digits = 2;
                        }
                    }
                    let result = null;
                    if(digits < 0){
                        result = this.parser(key + "_" + currentDeRow.sequenceNbr);
                    }else{
                        //更新位数
                        result = NumberUtil.numberScale( this.parser(key + "_" + currentDeRow.sequenceNbr),digits);
                    }
                    if(key === "originalQuantity"){
                        let curRes = DeQualityUtils.evalQualityWithCodes(currentDeRow.originalQuantity,this.priceCodes);//入口检验过，此处无需判断异常?
                        if(curRes === result){
                            LogUtil.renderLogger("DeFCalculator : no set originalQuantity" + currentDeRow.sequenceNbr + "-------------key :" + key + "-------------value :" + currentDeRow[key]);
                            continue;
                        }                        
                    }
                    currentDeRow[key] = result;
                    LogUtil.renderLogger("DeFCalculator :" + currentDeRow.sequenceNbr + "-------------key :" + key + "-------------value :" + currentDeRow[key]);
                }
            }
        }
    }

    /**
     * 根据设置工程量规则
     * @param rules
     */
    fill4QuantityRules(rules)
    {
        for(let currentDeRow of this.relatedDeRows) {
            if(ObjectUtil.isNotEmpty(currentDeRow)) {
                let parent = this.ctx.allDeMap.getNodeById(currentDeRow.parentId)
                if (parent.type === DeTypeConstants.DE_TYPE_DEFAULT || parent.type === DeTypeConstants.DE_TYPE_FB || parent.type === DeTypeConstants.DE_TYPE_ZFB)
                {
                    rules[this.getChildQuantityKey(currentDeRow.sequenceNbr)] = deFlattenerRules["quantity"].mathFormula;//初始化工程量规则
                    rules[this.getChildOriginalQuantityKey(currentDeRow.sequenceNbr)] =  deFlattenerRules["original"].mathFormula;
                }
                else
                {
                    //当前最顶级定额，不随父变更工程量， 用消耗量（减少或增加）满足  当前工程量=消耗量*父工程量 规则
                    if(this.deRowId === currentDeRow.sequenceNbr){
                        rules[this.getChildQuantityKey(currentDeRow.sequenceNbr)] = deFlattenerRules["quantity"].mathFormula;
                    }else{
//                        rules[this.getParentQuantityKey(currentDeRow.parentId)] = deFlattenerRules["quantity"].mathFormula;
                        if(currentDeRow.isCsxmDe && DeUtils.needSetCostDeQuantityEq1(currentDeRow)){//措施项目的定额的费用定额需要消耗量计算
                            currentDeRow.resQty = 1;//满足规则，防止更改为0
                        }
                        rules[this.getChildQuantityKey(currentDeRow.sequenceNbr)] = this.getResQtyKey(currentDeRow.sequenceNbr) + "*" + this.getParentQuantityKey(currentDeRow.parentId);
                    }
                    rules[this.getChildOriginalQuantityKey(currentDeRow.sequenceNbr)] = this.getChildUnitKey(currentDeRow.sequenceNbr) + "*" + this.getChildQuantityKey(currentDeRow.sequenceNbr);
                }
                rules[this.getChildUnitKey(currentDeRow.sequenceNbr)] = deFlattenerRules["unit"].mathFormula;
                rules[this.getResQtyKey(currentDeRow.sequenceNbr)] = deFlattenerRules["resQty"].mathFormula;//初始化消耗量规则

            }
        }

    }

    /**
     * 递归查找子定额
     * 子定额临时删除的不重新计算
     * @param deRow
     * @param relatedDeRows
     */
    findChildren(deRow,relatedDeRows)
    {
        if(ObjectUtil.isNotEmpty(deRow.children))
        {
            for (let childRow of deRow.children)
            {
                if((this.filterTempRemoveRow && childRow.isTempRemove === CommonConstants.COMMON_YES)
                    || (childRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtil.isNotEmpty(childRow.calculateMethod))
                ){
                    continue;
                }else{
                    relatedDeRows.push(childRow);
                    this.findChildren(childRow,relatedDeRows);
                }
            }
        }

    }


    buildRules() {
        let currentDeRow = this.currentDe;
        if(currentDeRow.type != DeTypeConstants.DE_TYPE_ZFB && currentDeRow.type != DeTypeConstants.DE_TYPE_FB && currentDeRow.type != DeTypeConstants.DE_TYPE_DEFAULT ) {
            this.relatedDeRows.push(currentDeRow);
        }
        let rules = {};
        this.findChildren(currentDeRow,this.relatedDeRows);
        this.fill4QuantityRules(rules);
        this.loadRules(rules);
    }


    getValue({type,kind,column})
    {
        let currentDeRow = this.currentDe;
        let value;
        switch (type) {
            case `QD`:{
                if (typeof column == 'function') {
                    value = column(currentDeRow);
                } else {
                    value = currentDeRow[column];
                }
                break;
            }
            case `DE`:{
                if (typeof column == 'function') {
                    value = column(currentDeRow);
                } else {
                    value = currentDeRow[column];
                }
                break;
            }
            default:{
                value = {type,kind,column};
                break;
            }
        }
        return value;
    }
    getRuntimeValue({type,kind,column},param)
    {
        let value= 0;
        let key = param.split(DeFlattener.SPLITOR)[1];

        switch (type) {
            case `DE`: {
                let item  = this.relatedDeRows.find(item => item.sequenceNbr === key);
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
                break;
            }
            case `runtime`: {
                let item  = this.relatedDeRows.find(item => item.sequenceNbr === key);
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
                break;
            }
            case `Resource`: {
                let item  = this.ctx.resourceMap.find(item => item.sequenceNbr === key);
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
                break;
            }
        }

        if(column == "originalQuantity"){
            return DeQualityUtils.evalQualityWithCodes(value,this.priceCodes);
        }
        return eval(value); //兼容表达式
    }
    getResQtyKey(sequenceNbr){
        return "resQty_" + sequenceNbr;
    }

    getParentQuantityKey = (sequenceNbr) => {
        return "quantity_" + sequenceNbr;
    }
    getChildQuantityKey = (sequenceNbr) => {
        return "quantity_" + sequenceNbr;
    }
    getChildOriginalQuantityKey = (sequenceNbr) => {
        return "originalQuantity_" + sequenceNbr;
    }
    getChildUnitKey = (sequenceNbr) => {
        return "unit_" + sequenceNbr;
    }
}
module.exports = {DeFlattener: DeFlattener};