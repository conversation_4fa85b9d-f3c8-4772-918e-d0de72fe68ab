const TreeProjectModel = require('./TreeProjectModel');
const { BaseModel } = require('../../../../../electron/model/BaseModel');

class ProjectModel extends TreeProjectModel {


   LAT ; // Last Access Time,最后访问时间
   path; // '存放路径',
   fileCode; // '文件唯一编码',UIC;

   provinceCode ; // '所属省份code',
   province ; // '所属省份名称',
   cityCode ; // '所属地市code',
   city ; // '所属地市名称',
   projectStatus; // '项目状态',


   createDate; // '创建时间',

   coverUrl; // '封面图片url',
   businessType; // '招投标类型 0招标 1投标 2 单位工程项目',
   importLink; // '导入文件url',
   reportLink; // '导出文件url',
   gfId ; // '规费政策文件id',
   awfId ; // '安文费政策文件id',
   rgfId ; // '人工费政策文件id',
   qdStandardId ; // '清单标准id',
   deStandardId ; // '定额标准id',
   deStandardReleaseYear; // 定额标准发布年份
   projectOverview;//项目概况
   fddbr;//法定代表人
   constructionUnit;//建设单位
   unitProject;//单位工程
   unitProjectArray;//单位工程集合
   constructProjectRcjs;//人材机汇总表
   constructProjectJBXX ;// 基本信息
   organizationInstructions ;// 编制说明
   projectTaxCalculation;// 计税方式
   xmlFactory ;// xml厂家
   projectRcjsLoading;//聚合后的待载人材机汇总
   projectAttrRelateMergeScheme; //项目特征关联 组价方案设置  如果为false 只更改项目特征 默认为true
   rgfInMeasureAndRPriceInMechanicalAction;  //以系数计算的措施项目和机械台班中的人工单价参与调整

   jzProjectCost;  // 建筑工程造价
   azProjectCost;  // 安装工程造价
   projectCost;  // 工程造价:单位、单项、工程项目
   average; // 工程规模
   averageUnit;  // 工程规模单位
   proportion;   // 占总投资比例

   isDefault = true;  // 是否默认当前单位   true:仅限当前单位  false:全局，整个项目
   businessIdArray; // 业务id

}
ProjectModel.toString = () => 'ProjectModel';
module.exports = ProjectModel;