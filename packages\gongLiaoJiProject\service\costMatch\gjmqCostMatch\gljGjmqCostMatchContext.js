'use strict';

const GljGjmqCgFeeMatchHandle = require('./gljGjmqCgFeeMatchHandle');
const GljGjmqCzysFeeMatchHandle = require('./gljGjmqCzysFeeMatchHandle');
const GljGjmqZxxjxFeeMatchHandle = require('./gljGjmqZxxjxFeeMatchHandle');
const CostDeMatchConstants = require('../../../constants/CostDeMatchConstants');

/**
 * 房修土建费用记取上下文
 */
class GljGjmqjCostMatchContext {

  constructor(feeType) {
    this.costMatchStrategy = this.getHandler(feeType);
  }

  getHandler(feeType) {
    let handle;
    switch (feeType) {
      case CostDeMatchConstants.GJMQ_CG:
        handle = new GljGjmqCgFeeMatchHandle();
        break;
      case CostDeMatchConstants.GJMQ_CZYS:
        handle = new GljGjmqCzysFeeMatchHandle();
        break;
      case CostDeMatchConstants.GJMQ_ZXXJX:
        handle = new GljGjmqZxxjxFeeMatchHandle();
        break;
    }
    return handle;
  }

  async getViewData(args) {
    return await this.costMatchStrategy.getViewData(args);
  }

  async costMatch(args) {
    return await this.costMatchStrategy.costMatch(args);
  }

  async getCache(args) {
    return await this.costMatchStrategy.getCache(args);
  }

  async autoCostMatch(args) {
    return await this.costMatchStrategy.autoCostMatch(args);
  }

}

GljGjmqjCostMatchContext.toString = () => '[class GljGjmqjCostMatchContext]';
module.exports = GljGjmqjCostMatchContext;
