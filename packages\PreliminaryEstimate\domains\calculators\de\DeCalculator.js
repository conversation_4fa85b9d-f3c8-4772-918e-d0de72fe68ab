const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const WildcardMap = require('../../../core/container/WildcardMap');
const {DERules, deCalculatorBaseFn} = require("./DeCalculatorCodes");
const DeTypeConstants = require("../../../constants/DeTypeConstants");
const ResourceKindConstants = require("../../../constants/ResourceKindConstants");
const LogUtil = require("../../../core/tools/logUtil");
const {NumberUtil} = require("../../../utils/NumberUtil");
const CommonConstants = require("../../../constants/CommonConstants");
const ZSFeeConstants = require("../../../constants/ZSFeeConstants");
const EE = require('../../../../../core/ee');
const DeUtils = require("../../utils/DeUtils");

/**
 * 定额计算器
 */
class DeCalculator extends CalculateEngine{
  static SPLITOR = "_";
  constructId;
  deRowId;
  precision;
  unitId;
  ctx;
  digitPropertyMap = new  Map();

  static  deMap = [
    "RSum",
    "CSum",
    "JSum",
    "SSum",
    "ZSum",
    "rTotalSum",
    "cTotalSum",
    "jTotalSum",
    "sTotalSum",
    "zTotalSum",
    "RDSum",
    "CDSum",
    "JDSum",
    "ZDSum",
    "SDSum",
    "rdTotalSum",
    "cdTotalSum",
    "jdTotalSum",
    "zdTotalSum",
    "sdTotalSum",
    "price",
    "baseJournalPrice",
    "totalNumber",
    "baseJournalTotalNumber",
  ];


  static getInstance({constructId, unitId,deRowId},ctx){
    return new DeCalculator(constructId,unitId,deRowId,null,ctx);
  }

  /**
   *
   * @param constructId 当前工程
   * @param unitId 当前修改的人材机所属的单位工程
   * @param deRowId 当前修改的人材机所属的定额
   * @param resourceId 为当前修改的人材机ID
   * @param ctx
   */
  constructor(constructId,unitId,deRowId,resourceId,ctx) {
    super(ctx);
    this.ctx = ctx;
    this.constructId = constructId;
    this.unitId = unitId;
    this.deRowId = deRowId;
    this.initDigitPropertyMap();
  }
  convertValue(value,param) {
    // let paramArray = param.split(DeCalculator.SPLITOR);
    // let digits = this.digitPropertyMap.get(paramArray[0]);
    // if(ObjectUtil.isEmpty(digits)) {
    //   digits = 2;
    // }

    // return NumberUtil.numberScale(value, digits);
    return value;
  }
  initDigitPropertyMap()
  {
    this.digitPropertyMap.set("resQty",5);
    this.digitPropertyMap.set("quantity",5);
    this.digitPropertyMap.set("totalNumber",5);//精度控制影响结果,定额是合价，人材机位数量
    this.digitPropertyMap.set("rTotalSum",5);
    this.digitPropertyMap.set("cTotalSum",5);
    this.digitPropertyMap.set("jTotalSum",5);
    this.digitPropertyMap.set("sTotalSum",5);
    this.digitPropertyMap.set("zTotalSum",5);
    this.digitPropertyMap.set("rdTotalSum",5);
    this.digitPropertyMap.set("cdTotalSum",5);
    this.digitPropertyMap.set("jdTotalSum",5);
  }
  
  async prepare() {
      const {service} = EE.app;
      this.precision = service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(this.constructId);
  }

  async analyze() {
    await this.prepare();
    this.preload(deCalculatorBaseFn);
    this.buildRules();
    await this.render()
  }
  
  /**
   * 填充人材机数据
   */
  async render() {
    let deRow = this.ctx.deMap.getNodeById(this.deRowId);
    if(ObjectUtils.isNotEmpty(deRow) && (deRow.type != DeTypeConstants.DE_TYPE_ZFB && deRow.type != DeTypeConstants.DE_TYPE_FB
        && deRow.type != DeTypeConstants.DE_TYPE_DEFAULT &&  deRow.type != DeTypeConstants.DE_TYPE_DELIST  ))
    {

      for(let key of DeCalculator.deMap)
      {
        let digital = DeUtils.getDePrecision(key,this.precision);
        if(ObjectUtils.isEmpty(digital))
        {
          digital = 2;
        }
        let columnKey = key + "_" + deRow.sequenceNbr;
        deRow[key] = NumberUtil.numberFormat(this.parser(columnKey),digital);
        this.instanceMap[columnKey] = deRow[key];
        LogUtil.renderLogger("DeCalculator :" + deRow.sequenceNbr + "---------key :" + key + "---------value :" + deRow[key]);
      }
    }
    deRow.updateDate = Date.now();
    //LogUtil.renderLogger(deRow);
  }

  buildRules() {
    let rules = {};
    let rSum = "0";//人 基数 单价
    let rTotalSum = "0";//人 基数 合价
    let cSum = "0";//材 基数 单价
    let cTotalSum = "0";//材 基数 合价
    let jSum = "0";//机 基数 单价
    let jTotalSum = "0";//机 基数 合价
    let sSum = "0";//主设 基数 单价
    let sTotalSum = "0";//主设 基数 合价
    let zSum = "0";//主材 基数 单价
    let zTotalSum = "0";//主材 基数 合价

    let rdSum = "0";//人 基数 单价  消耗量*定额价
    let cdSum = "0";//人 基数 单价  消耗量*定额价
    let jdSum = "0";//人 基数 单价  消耗量*定额价
    let zdSum = "0";//人 基数 单价  消耗量*定额价
    let sdSum = "0";//人 基数 单价  消耗量*定额价

    let rdTotalSum = "0";//人 基数定额价 合价
    let cdTotalSum = "0";//材 基数定额价 合价
    let jdTotalSum = "0";//机 基数定额价 合价
    let zdTotalSum = "0";//主设 基数定额价 合价
    let sdTotalSum = "0";//主设 基数定额价 合价

    //单价(Σ (人材机的消耗量*人材机的市场价))
    let deRow = this.ctx.deMap.getNodeById(this.deRowId);
    let subResource = this.ctx.resourceMap.getValues(WildcardMap.generateKey(this.unitId, this.deRowId) + WildcardMap.WILDCARD);
    let deList = this.ctx.deMap.getAllNodes().filter(item => item.unitId === deRow.unitId && item.type !== DeTypeConstants.DE_TYPE_FB && item.type !== DeTypeConstants.DE_TYPE_ZFB);


    if (!ZSFeeConstants.ZS_DE_LIST.includes(deRow.deCode)) {
      //如果定额是主材或者设备定额，需要计算主材和设备
      if ((deRow.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || deRow.deResourceKind == ResourceKindConstants.INT_TYPE_SB)
          && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)) {
        subResource.forEach(item => {
          rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
          rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
          rules[this.getRcjDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
          rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
          if(item.kind == ResourceKindConstants.INT_TYPE_ZC){
            zSum += "+1*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zdSum += "+1*" + this.getRcjDePriceKey(item.sequenceNbr);
            zdTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
          }else{
            sSum += "+1*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sdSum += "+1*" + this.getRcjDePriceKey(item.sequenceNbr);
            sdTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
          }
        })
      } else {
        subResource.forEach(item => {

          rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
          rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
          rules[this.getRcjDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
          //主材和设备不计算在单价里面
          if (item.kind != ResourceKindConstants.INT_TYPE_ZC && item.kind != ResourceKindConstants.INT_TYPE_SB) {
          }
          //乱改，兼容统一处理
          if (item.kind == ResourceKindConstants.INT_TYPE_ZC){
            zSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            zdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          if(item.kind == ResourceKindConstants.INT_TYPE_SB) {
            sSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            sdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
          rules[this.getDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
          // 修改item.kind 赋值为字符串，此处与int比较不了 暂时用if==
          // switch(item.kind){
          if (item.kind == ResourceKindConstants.INT_TYPE_R) {
            rSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            rTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            rdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getDePriceKey(item.sequenceNbr);

            rdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            // break;
          }
          //从那里抄袭过来了，，这么坑的不行
          if (item.kind == ResourceKindConstants.INT_TYPE_C || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10) {
            cSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            cTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            cdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getDePriceKey(item.sequenceNbr);

            cdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            // break;
          }
          if (item.kind == ResourceKindConstants.INT_TYPE_J) {
            jSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            jTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            jdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getDePriceKey(item.sequenceNbr);

            jdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            // break;
          }
          // }

        });
      }
      //人材机基数
      rules[this.getRSum(this.deRowId)] = rSum;//当前定额下人的基数汇总
      rules[this.getCSum(this.deRowId)] = cSum;//当前定额下材的基数汇总
      rules[this.getJSum(this.deRowId)] = jSum;//当前定额下机的基数汇总
      rules[this.getSSum(this.deRowId)] = sSum;//当前定额下材的基数汇总
      rules[this.getZSum(this.deRowId)] = zSum;//当前定额下机的基数汇总
      rules[this.getRTotalSum(this.deRowId)] = rTotalSum;//当前定额下人的基数汇总
      rules[this.getCTotalSum(this.deRowId)] = cTotalSum;//当前定额下材的基数汇总
      rules[this.getJTotalSum(this.deRowId)] = jTotalSum;//当前定额下机的基数汇总
      rules[this.getSTotalSum(this.deRowId)] = sTotalSum;//当前定额下材的基数汇总
      rules[this.getZTotalSum(this.deRowId)] = zTotalSum;//当前定额下机的基数汇总

      rules[this.getRDSum(this.deRowId)] = rdSum;//当前定额下人的基数汇总
      rules[this.getCDSum(this.deRowId)] = cdSum;//当前定额下人的基数汇总
      rules[this.getJDSum(this.deRowId)] = jdSum;//当前定额下人的基数汇总
      rules[this.getZDSum(this.deRowId)] = zdSum;//当前定额下人的基数汇总
      rules[this.getSDSum(this.deRowId)] = sdSum;//当前定额下人的基数汇总

      rules[this.getRdTotalSum(this.deRowId)] = rdTotalSum;//当前定额下人的基数 定额价汇总
      rules[this.getCdTotalSum(this.deRowId)] = cdTotalSum;//当前定额下材的基数 定额价汇总
      rules[this.getJdTotalSum(this.deRowId)] = jdTotalSum;//当前定额下机的基数 定额价汇总
      rules[this.getZdTotalSum(this.deRowId)] = zdTotalSum;//当前定额下机的基数 定额价汇总
      rules[this.getSdTotalSum(this.deRowId)] = sdTotalSum;//当前定额下机的基数 定额价汇总
      //单价
      // if(deRow.type === DeTypeConstants.DE_TYPE_DE) {
      // if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE)
      // {
      //   rules[this.getPriceKey(this.deRowId)] = DERules["price"].mathFormula;
      // }
      // else
      // {
      rules[this.getPriceKey(this.deRowId)] = this.getRSum(this.deRowId) + "+" + this.getCSum(this.deRowId) + "+" + this.getJSum(this.deRowId);
      rules[this.getBaseJournalPriceKey(this.deRowId)] = this.getRDSum(this.deRowId) + "+" + this.getCDSum(this.deRowId) + "+" + this.getJDSum(this.deRowId);
      // }
      //合价(当前定额的工程量 * 当前定额的单价)
      rules[this.getTotalNumberKey(this.deRowId)] = this.getPriceKey(this.deRowId) + "*quantity";
      rules[this.getBaseJournalTotalNumberKey(this.deRowId)] = this.getBaseJournalPriceKey(this.deRowId) + "*quantity";
      // }
      // }
    } else {
        let linshijisuanR = 0;
        let linshijisuanC = 0;
        let linshijisuanJ = 0;
      //如果是装饰超高定额，先算合价=人材机合价，单价=合价/工程量
      //如果定额是主材或者设备定额，需要计算主材和设备
      if ((deRow.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || deRow.deResourceKind == ResourceKindConstants.INT_TYPE_SB)
          && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)) {
        subResource.forEach(item => {
          rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
          rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
          rules[this.getRcjDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
          rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
          dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
          baseJournalPriceRules = "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
          if(item.kind == ResourceKindConstants.INT_TYPE_ZC){
            zSum += "+1*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zdSum += "+1*" + this.getRcjDePriceKey(item.sequenceNbr);
            zdTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
          }else{
            sSum += "+1*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sdSum += "+1*" + this.getRcjDePriceKey(item.sequenceNbr);
            sdTotalSum +=  "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
          }
        })
      } else {
        subResource.forEach(item => {
          rules[this.getRcjResQtyKey(item.sequenceNbr)] = DERules["resQty"].mathFormula;
          rules[this.getRcjMarketPriceKey(item.sequenceNbr)] = DERules["marketPrice"].mathFormula;
          rules[this.getRcjDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
          rules[this.getRcjTotal(item.sequenceNbr)] = DERules["total"].mathFormula;
          //主材和设备不计算在单价里面
          if (item.kind != ResourceKindConstants.INT_TYPE_ZC && item.kind != ResourceKindConstants.INT_TYPE_SB) {
            // totalNumberRules += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            // dePriceRules += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
          }
          //乱改，兼容统一处理
          if (item.kind == ResourceKindConstants.INT_TYPE_ZC){
            zSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            zdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            zdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          if(item.kind == ResourceKindConstants.INT_TYPE_SB) {
            sSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            sdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            sdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            deRow.isExistedZcSb = CommonConstants.COMMON_YES;
          }
          rules[this.getTotalNumberKey(item.sequenceNbr)] = DERules["totalNumber"].mathFormula;
          rules[this.getDePriceKey(item.sequenceNbr)] = DERules["dePrice"].mathFormula;
          // 修改item.kind 赋值为字符串，此处与int比较不了 暂时用if==
          // switch(item.kind){
          if (item.kind == ResourceKindConstants.INT_TYPE_R) {
            if (!ZSFeeConstants.ZS_RCJ_LIST.includes(item.materialCode)) {
              //非降效系数的人材机单价 = 消耗量*市场价
              rSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
              dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
              baseJournalPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            } else {
              //效系数的人材机单价 = 父级定额同级的定额所有人材机 （消耗量*定额价） * 人材机消耗量 /100
              let deParentChildDeList = deList.filter(p => p.parentId === deRow.parentId && !ZSFeeConstants.ZS_DE_LIST.includes(p.deCode) && p.type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE && p.deRowId !== deRow.deRowId);
              //2统计平级定额的计算基数 各自人工/机械定额价*各自消耗量）*除最父级外，其余各父级定额的消耗量
              let baseCount = 0;
              baseCount = this.calRSum(deParentChildDeList, deList, 0, 1);
              let number = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.multiplyParams(baseCount, item.resQty), 100), 5);
              linshijisuanR = NumberUtil.add(linshijisuanR,number);
            }

            rTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            rdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getDePriceKey(item.sequenceNbr);

            rdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            // break;
          }
          if (item.kind == ResourceKindConstants.INT_TYPE_C || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10) {
            cSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            baseJournalPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            cTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            cdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getDePriceKey(item.sequenceNbr);

            cdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            // break;
          }
          if (item.kind == ResourceKindConstants.INT_TYPE_J) {
            if (!ZSFeeConstants.ZS_RCJ_LIST.includes(item.materialCode)) {
              //非降效系数的人材机单价 = 消耗量*市场价
              jSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
              dePriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
              baseJournalPriceRules += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            } else {
              //效系数的人材机单价 = 父级定额同级的定额所有人材机 （消耗量*定额价） * 人材机消耗量 /100
              let deParentChildDeList = deList.filter(p => p.parentId === deRow.parentId && !ZSFeeConstants.ZS_DE_LIST.includes(p.deCode) && p.type !== DeTypeConstants.DE_TYPE_ANZHUANG_FEE && p.deRowId !== deRow.deRowId);
              //2统计平级定额的计算基数 各自人工/机械定额价*各自消耗量）*除最父级外，其余各父级定额的消耗量
              let baseCount = 0;
              baseCount = this.calJSum(deParentChildDeList, deList, 0, 1);
              let number = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.multiplyParams(baseCount, item.resQty), 100), 5);
              linshijisuanJ = NumberUtil.add(linshijisuanJ,number);
            }

            jTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getRcjMarketPriceKey(item.sequenceNbr);
            jdTotalSum += "+" + this.getTotalNumberKey(item.sequenceNbr) + "*" + this.getDePriceKey(item.sequenceNbr);

            jdSum += "+" + this.getRcjResQtyKey(item.sequenceNbr) + "*" + this.getRcjDePriceKey(item.sequenceNbr);
            // break;
          }
          // }

        });
      }

      linshijisuanR = NumberUtil.numberScale(linshijisuanR,2);
      linshijisuanC = NumberUtil.numberScale(linshijisuanC,2);
      linshijisuanJ = NumberUtil.numberScale(linshijisuanJ,2);
      rSum = linshijisuanR + rSum.slice(1);
      cSum = linshijisuanC + cSum.slice(1);
      jSum = linshijisuanJ + jSum.slice(1);
      //人材机基数 有没有作用先加上再说
      rdSum = linshijisuanR + rdSum.slice(1);
      cdSum = linshijisuanC + cdSum.slice(1);
      jdSum = linshijisuanJ + jdSum.slice(1);

      rules[this.getRSum(this.deRowId)] = rSum;
      rules[this.getCSum(this.deRowId)] = cSum;
      rules[this.getJSum(this.deRowId)] = jSum;
      rules[this.getSSum(this.deRowId)] = sSum;
      rules[this.getZSum(this.deRowId)] = zSum;

      rules[this.getRDSum(this.deRowId)] = rdSum;//当前定额下人的基数汇总
      rules[this.getJDSum(this.deRowId)] = cdSum;//当前定额下材的基数汇总
      rules[this.getJDSum(this.deRowId)] = jdSum;//当前定额下材的基数汇总
      rules[this.getZDSum(this.deRowId)] = zdSum;//当前定额下机的基数汇总
      rules[this.getSDSum(this.deRowId)] = sdSum;//当前定额下机的基数汇总

      //人材机基数
      rules[this.getRTotalSum(this.deRowId)] = "(" + rSum + ")" +"*quantity";//当前定额下人的基数汇总
      rules[this.getCTotalSum(this.deRowId)] = cSum + "*quantity";//当前定额下材的基数汇总
      rules[this.getJTotalSum(this.deRowId)] = "(" + jSum + ")" + "*quantity";//当前定额下机的基数汇总
      rules[this.getSTotalSum(this.deRowId)] = sSum + "*quantity";//当前定额下材的基数汇总
      rules[this.getZTotalSum(this.deRowId)] = zSum + "*quantity";//当前定额下机的基数汇总

      rules[this.getRdTotalSum(this.deRowId)] = rdSum + "*quantity";//当前定额下人的基数 定额价汇总
      rules[this.getCdTotalSum(this.deRowId)] = cdSum + "*quantity";//当前定额下材的基数 定额价汇总
      rules[this.getJdTotalSum(this.deRowId)] = jdSum + "*quantity";//当前定额下机的基数 定额价汇总
      rules[this.getZdTotalSum(this.deRowId)] = zdSum + "*quantity";//当前定额下机的基数 定额价汇总
      rules[this.getSdTotalSum(this.deRowId)] = sdSum + "*quantity";//当前定额下机的基数 定额价汇总

      // let linshijisuan = NumberUtil.addParams(linshijisuanR,linshijisuanC,linshijisuanJ);
      // linshijisuan = NumberUtil.numberScale(linshijisuan,2);
      // dePriceRules = linshijisuan + dePriceRules.slice(1);
      // baseJournalPriceRules = linshijisuan + baseJournalPriceRules.slice(1);
      //单价
      // if(deRow.type === DeTypeConstants.DE_TYPE_DE) {
      // if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE)
      // {
      //   rules[this.getPriceKey(this.deRowId)] = DERules["price"].mathFormula;
      // }
      // else
      // {
      // rules[this.getTotalNumberKey(this.deRowId)] = totalNumberRules;
      // }
      //合价(当前定额的工程量 * 当前定额的单价)
      // rules[this.getPriceKey(this.deRowId)] = this.getTotalNumberKey(this.deRowId) + "/quantity";
      // }
      // }

      rules[this.getPriceKey(this.deRowId)] = this.getRSum(this.deRowId) + "+" + this.getCSum(this.deRowId) + "+" + this.getJSum(this.deRowId);
      rules[this.getBaseJournalPriceKey(this.deRowId)] = this.getRDSum(this.deRowId) + "+" + this.getCDSum(this.deRowId) + "+" + this.getJDSum(this.deRowId);
      rules[this.getTotalNumberKey(this.deRowId)] = this.getPriceKey(this.deRowId) + "*quantity";
      rules[this.getBaseJournalTotalNumberKey(this.deRowId)] = this.getBaseJournalPriceKey(this.deRowId) + "*quantity";
    }

    this.loadRules(rules);
  }


  calRSum(deParentChildDeList, deList, RSum, level) {
    for (let item of deParentChildDeList) {
      if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
          || item.type === DeTypeConstants.DE_TYPE_DELIST) {
        RSum = NumberUtil.add(RSum, item.RSum);
      }
    }
    return RSum;
  }

  calJSum(deParentChildDeList, deList, JSum, level) {
    for (let item of deParentChildDeList) {
      if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_RESOURCE || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
          || item.type === DeTypeConstants.DE_TYPE_DELIST) {
        JSum = NumberUtil.add(JSum, item.JSum);
      }
    }
    return JSum;
  }

  getValue({type,kind,column})
  {
    let currentDe = this.ctx.deMap.getNodeById(this.deRowId);
    let value;
    switch (type) {
      case `De`:{
        if (typeof column == 'function') {
          value = column({ de: currentDe});
        } else {
          value = currentDe[column];
          if(ObjectUtils.isEmpty(value)){
            value = 0;
          }
          let digital = DeUtils.getDePrecision(column,this.precision);
          if (ObjectUtils.isNotEmpty(digital)) {
            value = NumberUtil.numberFormat(value, digital);
          }
        }
        break;
      }

      default:{
        value = {type,kind,column};
        break;
      }
    }
    return value;
  }
  getRuntimeValue({type,kind,column},param)
  {
    let value= 0;
    let key = param.split(DeCalculator.SPLITOR)[1];
    let columnKey = param.split(DeCalculator.SPLITOR)[0];
    let item  = this.ctx.resourceMap.asArray().map(item => item[1]).find(item => item.sequenceNbr === key);
    switch (type) {
      case `item`: {
        if (typeof column == "function") {
          value = column(item);
        } else {
          value = item[column]
        }
        
        let digital = DeUtils.getRCJPrecision(columnKey,this.precision);
        if (ObjectUtils.isNotEmpty(digital) && ObjectUtils.isNotEmpty(value)) {
          value = NumberUtil.numberFormat(value, digital);
        }
        break;
      }
      case `de`: {
        let item = this.ctx.deMap.getNodeById(key);
        if (typeof column == "function") {
          value = column(item);
        } else {
          value = item[column]
        }
        let digital = DeUtils.getDePrecision(columnKey,this.precision);
        if (ObjectUtils.isNotEmpty(digital)) {
          value = NumberUtil.numberFormat(value, digital);
        }
        break;
      }
    }

    return value;
  }
  /**
   * 定额价之和
   * @param {*} sequenceNbr 
   * @returns 
   */
  getRdTotalSum = (sequenceNbr) => {
    return "rdTotalSum_" + sequenceNbr;
  }
  getCdTotalSum = (sequenceNbr) => {
    return "cdTotalSum_" + sequenceNbr;
  }
  getJdTotalSum = (sequenceNbr) => {
    return "jdTotalSum_" + sequenceNbr;
  }
  getZdTotalSum = (sequenceNbr) => {
    return "zdTotalSum_" + sequenceNbr;
  }
  getSdTotalSum = (sequenceNbr) => {
    return "sdTotalSum_" + sequenceNbr;
  }

  getDePriceKey = (sequenceNbr)=>{
    return "dePrice_" + sequenceNbr;
  }

  getRTotalSum = (sequenceNbr) => {
    return "rTotalSum_" + sequenceNbr;
  }
  getCTotalSum = (sequenceNbr) => {
    return "cTotalSum_" + sequenceNbr;
  }
  getJTotalSum = (sequenceNbr) => {
    return "jTotalSum_" + sequenceNbr;
  }
  getSTotalSum = (sequenceNbr) => {
    return "sTotalSum_" + sequenceNbr;
  }
  getZTotalSum = (sequenceNbr) => {
    return "zTotalSum_" + sequenceNbr;
  }
  getRSum = (sequenceNbr) => {
    return "RSum_" + sequenceNbr;
  }
  getJSum = (sequenceNbr) => {
    return "JSum_" + sequenceNbr;
  }
  getCSum = (sequenceNbr) => {
    return "CSum_" + sequenceNbr;
  }
  getSSum = (sequenceNbr) => {
    return "SSum_" + sequenceNbr;
  }
  getZSum = (sequenceNbr) => {
    return "ZSum_" + sequenceNbr;
  }
  getTotalNumberKey = (sequenceNbr) => {
    return "totalNumber_" + sequenceNbr;
  }
  getRcjResQtyKey = (sequenceNbr) => {
    return "resQty_" + sequenceNbr;
  }
  getRcjMarketPriceKey = (sequenceNbr) => {
    return "marketPrice_" + sequenceNbr;
  }
  getRcjDePriceKey = (sequenceNbr) => {
    return "dePrice_" + sequenceNbr;
  }
  getPriceKey = (sequenceNbr) => {
    return "price_" + sequenceNbr;
  }
  getQuantity = (sequenceNbr) => {
    return "quantity_" + sequenceNbr;
  }
  getRcjTotal = (sequenceNbr) => {
    return "total_" + sequenceNbr;
  }
  getRDSum = (sequenceNbr) => {
    return "RDSum_" + sequenceNbr;
  }
  getCDSum = (sequenceNbr) => {
    return "CDSum_" + sequenceNbr;
  }
  getJDSum = (sequenceNbr) => {
    return "JDSum_" + sequenceNbr;
  }
  getZDSum = (sequenceNbr) => {
    return "ZDSum_" + sequenceNbr;
  }
  getSDSum = (sequenceNbr) => {
    return "SDSum_" + sequenceNbr;
  }

  getBaseJournalPriceKey = (sequenceNbr) => {
    return "baseJournalPrice_" + sequenceNbr;
  }
  getBaseJournalTotalNumberKey = (sequenceNbr) => {
    return "baseJournalTotalNumber_" + sequenceNbr;
  }
}
module.exports = {DeCalculator};