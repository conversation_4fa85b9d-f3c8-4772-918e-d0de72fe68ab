const Kind2MathHandler = require("../math_item_handler/Kind2MathHandler");
const RuleHandler = require("./ruleHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");

class Kind2RuleHandler extends RuleHandler{
    constructor(strategyCtx, rule) {
        super(strategyCtx, rule);
        // 切换的目标材料是否是默认材料
        this.ifTargetIsDefalut = (this.rule.defaultRcjCode == this.rule.clpb.detailsCode && this.rule.defaultRcjLibraryCode == this.rule.clpb.libraryCode);
    }

    addDeUpdateInfo() {
        if(this.ifTargetIsDefalut){
            return;
        }
        super.addDeUpdateInfo();
    }

    deCodeUpdateInfo() {
        return {
            redStr: `[H${this.rule.defaultRcjCode} ${this.rule.clpb.detailsCode}]`,
            blackStr: null
        };
    }

    addConversionInfo() {
        if(this.ifTargetIsDefalut) {
            return;
        }
        super.addConversionInfo();
    }

    dealConversionInfo(conversionInfoItem) {
        conversionInfoItem.conversionExplain = this._conversionExplain();
        conversionInfoItem.conversionString = `H${this.rule.defaultRcjCode} ${this.rule.clpb.detailsCode}`;
    }


    deNameUpdateInfo(rule){
        return this._conversionExplain();
    }

    _conversionExplain(){
        if(this.conversionExplain_kind2){
            return this.conversionExplain_kind2;
        }

        let rule = this.rule;

        if(ObjectUtils.isEmpty(rule.defaultRcjName)){
            this.conversionExplain_kind2 = this.HSCL_TEXT+`${this.rule.ruleInfo}`;
            return this.conversionExplain_kind2;
        }

        let codeLikeRcjs = this.deRcjs.filter(rcj => (rcj.materialCode == rule.currentRcjCode || rcj.materialCode.startsWith(rule.currentRcjCode+"#")) && rcj.libraryCode == rule.currentRcjLibraryCode)

        if(ObjectUtils.isEmpty(codeLikeRcjs)){
            let newRcjProxy = rule.clpb;
            let specification = newRcjProxy.specification;
            if(ObjectUtils.isNotEmpty(newRcjProxy.specification) && newRcjProxy.details.trim().endsWith(newRcjProxy.specification)){
                specification = null;
            }
            let newNameSpecification = `${newRcjProxy.details}` + (specification ? `(${specification})` : "");
            let oldNameSpecification = `${rule.defaultRcjName}` + (rule.defaultRcjSpecification ? `(${rule.defaultRcjSpecification})` : "");
            this.conversionExplain_kind2 = `把人材机${rule.defaultRcjCode}(${oldNameSpecification})替换为${newRcjProxy.detailsCode}(${newNameSpecification})`;

        }else{
            let oldRcj = codeLikeRcjs.find(rcj => rcj.materialCode == rule.currentRcjCode);
            if(!oldRcj){
                oldRcj = codeLikeRcjs[0];
            }
            let newRcjProxy = rule.clpb;
            let specification = newRcjProxy.specification;
            if(ObjectUtils.isNotEmpty(newRcjProxy.specification) && newRcjProxy.details.trim().endsWith(newRcjProxy.specification)){
                specification = null;
            }
            let newNameSpecification = `${newRcjProxy.details}` + (specification ? `(${specification})` : "");
            //let newNameSpecification = `${newRcjProxy.details}` + (newRcjProxy.specification ? `(${newRcjProxy.specification})` : "");
            let oldNameSpecification = `${oldRcj.materialName}` + (oldRcj.specification ? `(${oldRcj.specification})` : "");
            this.conversionExplain_kind2 = `把人材机${oldRcj.materialCode}(${oldNameSpecification})替换为${newRcjProxy.detailsCode}(${newNameSpecification})`;

        }
        return this.conversionExplain_kind2;
    }


    /**
     * 解析规则：规则分类、输入/选择值、公式
     */
    analysisRule(){
        return [new Kind2MathHandler(this, this.rule.math)];
    }
}

module.exports = Kind2RuleHandler;
