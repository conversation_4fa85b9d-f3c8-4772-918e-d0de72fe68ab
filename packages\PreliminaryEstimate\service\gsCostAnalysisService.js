const ProjectDomain = require("../domains/ProjectDomain");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {GsCostAnalysisUnitVO} = require("../models/GsCostAnalysisUnitVO");
const gsJzZjfx = require('../jsonData/gs_jz_zjfx.json');
const gsAzZjfx = require('../jsonData/gs_az_zjfx.json');
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const {GsCostAnalysisSingleVO} = require("../models/GsCostAnalysisSingleVO");
const {GsCostAnalysisConstructVO} = require("../models/GsCostAnalysisConstructVO");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {GsCostAnalysisVO} = require("../models/GsCostAnalysisVO");
const {Service} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const UtilsPs = require("../../../core/ps");
const {FileUtils} = require("../utils/FileUtils");
const {dialog} = require("electron");
const XLSX = require("xlsx");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const ObjectUtil = require("../../../common/ObjectUtil");

/**
 * 造价分析
 */
class GsCostAnalysisService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取造价分析
     * @param args
     * @returns {Promise<GsCostAnalysisVO>}
     */
    async getCostAnalysisData(args) {
        let {constructId, singleId, unitId, type} = args;
        //返回对象
        let costAnalysisVO = new GsCostAnalysisVO();
        // 工程项目层级
        if (type === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            costAnalysisVO.costAnalysisConstructVOList = await this.generateConstructCostAnalysisData(constructId);
            // 单项层级
        } else if (type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            let singleProject = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === singleId
                && item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE);
            costAnalysisVO.costAnalysisSingleVOList = await this.generateSingleCostAnalysisData(constructId, singleProject[0], "1");
            // 单位层级
        } else if (type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            costAnalysisVO.costAnalysisUnitVOList = await this.generateUnitCostAnalysisData(constructId, singleId, unitId);
        }
        return costAnalysisVO;
    }

    /**
     * 获去当前单项下的所有子集造价分析
     * @param constructId
     * @param singleProject
     * @param dispNo
     * @returns {any[]}
     */
    async generateSingleCostAnalysisData(constructId, singleProject, dispNo) {
        let singleCostAnalysisArray = [];
        let singleCostAnalysis = new GsCostAnalysisSingleVO();
        // 子单项
        let singleProjects = singleProject.children;
        singleCostAnalysis.sequenceNbr = singleProject.sequenceNbr;
        singleCostAnalysis.projectName = singleProject.name;
        singleCostAnalysis.average = ObjectUtils.isNotEmpty(singleProject.average) ? singleProject.average : 0;
        singleCostAnalysis.childrenList = [];
        let allSubSingles = [];
        // 遍历添加造价分析数据
        await this.getSingleLevelCostAnalysis(singleProjects, singleCostAnalysis, dispNo, constructId, allSubSingles);
        singleCostAnalysisArray.push(singleCostAnalysis);

        // 对子项单项进行向上累加
        await this.getSubSingleCostAnalysisDataTotal(constructId, allSubSingles, singleProject);
        return singleCostAnalysisArray;
    }

    /**
     * 获取当前单项的所有层级
     * @param singleProjects
     * @param singleCostAnalysis
     * @param dispNo
     * @param constructId
     * @param allSubSingles
     */
    async getSingleLevelCostAnalysis(singleProjects, singleCostAnalysis, dispNo, constructId, allSubSingles) {
        let subSingleCostAnalysis = [];
        // 当该层是单位时，获取所有单位造价分析
        let units = singleProjects.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
        if (ObjectUtils.isNotEmpty(units)) {
            let commonCostAnalysis = {};
            singleCostAnalysis.childrenList = await this.generateUnitsCostAnalysisData(units, dispNo.dispNo, constructId, commonCostAnalysis);
            singleCostAnalysis.azCostSummary = commonCostAnalysis.azCostSummary
            singleCostAnalysis.jzCostSummary = commonCostAnalysis.jzCostSummary;
            // 添加单项层级
            allSubSingles.push(singleCostAnalysis);
        } else {
            // 当该层不是单位是，进行层级遍历，获取单项层级
            for (let i = 0; i < singleProjects.length; i++) {
                let subSingleProject = singleProjects[i];
                let singleCostAnalysisVO = new GsCostAnalysisSingleVO();
                singleCostAnalysisVO.sequenceNbr = subSingleProject.sequenceNbr;
                singleCostAnalysisVO.dispNo = dispNo + "." + (i + 1);
                singleCostAnalysisVO.projectName = subSingleProject.name;
                singleCostAnalysisVO.average = ObjectUtils.isNotEmpty(subSingleProject.average) ? subSingleProject.average : 0;

                if (ObjectUtils.isNotEmpty(subSingleProject.children)) {
                    // 当该单项下是单位，且不包含单项  注：单项下只能包括子单项或者全是单位
                    let units = subSingleProject.children.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.parentId === subSingleProject.sequenceNbr);
                    if (ObjectUtils.isNotEmpty(units)) {
                        subSingleCostAnalysis = await this.getSubSingleContainUnitsCostAnalysisData(constructId, subSingleProject, dispNo);
                        if (ObjectUtils.isEmpty(singleCostAnalysis.childrenList)) {
                            singleCostAnalysis.childrenList = subSingleCostAnalysis;
                        } else {
                            singleCostAnalysis.childrenList.push(...subSingleCostAnalysis);
                        }

                        // 添加单项层级
                        allSubSingles.push(singleCostAnalysis);
                        allSubSingles.push(singleCostAnalysisVO);
                        // 当该单项下不是单位，是单项，继续获取单项层级
                    } else {
                        // 封装造价层级数据
                        subSingleCostAnalysis.push(singleCostAnalysisVO);
                        singleCostAnalysis.childrenList = subSingleCostAnalysis;
                        // 添加单项层级
                        allSubSingles.push(singleCostAnalysis);
                        allSubSingles.push(singleCostAnalysisVO);
                        this.getSingleLevelCostAnalysis(subSingleProject.children, singleCostAnalysisVO, dispNo, constructId, allSubSingles);
                    }
                } else {
                    // 处理单项下没有子单项、单位的情况
                    subSingleCostAnalysis = await this.getSubSingleContainUnitsCostAnalysisData(constructId, subSingleProject, dispNo);
                    if (ObjectUtils.isEmpty(singleCostAnalysis.childrenList)) {
                        singleCostAnalysis.childrenList = subSingleCostAnalysis;
                    } else {
                        singleCostAnalysis.childrenList.push(...subSingleCostAnalysis);
                    }
                    allSubSingles.push(singleCostAnalysis);
                    allSubSingles.push(singleCostAnalysisVO);
                }
            }
        }
    }


    /**
     * 对同一层级的子单项造价分析数据进行汇总
     * @param constructId
     * @param allSubSingles  该单项所有的子单项
     * @param subSingleProject
     * @returns {Promise<GsCostAnalysisSingleVO>} 返回该单项及子单项的造价分析数据
     */
    async getSubSingleCostAnalysisDataTotal(constructId, allSubSingles, subSingleProject) {

        let jzCostSummary = 0;  // 工程费用汇总-建筑工程
        let azCostSummary = 0;  // 工程费用汇总-安装工程

        let budgetzjf = 0; // 预算书-直接费 合计
        let qzrgf = 0;   // 其中-人工费
        let qzclf = 0;   // 其中-材料费
        let qzjxf = 0;   // 其中-机械费
        let qzsbf = 0;   // 其中-设备费
        let qzzcf = 0;   // 其中-主材费

        let qtcsf = 0;  // 其他措施费
        let qtcsrgf = 0;  // 其中-其他措施费-人工费
        let qtcsclf = 0;  // 其中-其他措施费-材料费
        let qtcsjxf = 0;  // 其中-其他措施费-机械费

        let lxgcbgf = 0;  // 零星工程包干费
        let lxgcbgrgf = 0;  // 其中-零星工程包干费-人工费
        let lxgcbgclf = 0;  // 其中-零星工程包干费-材料费
        let lxgcbgsbf = 0;  // 其中-零星工程包干费-机械费
        let qyglf = 0;  // 企业管理费
        let gf = 0;  // 规费
        let lr = 0;  // 利润
        let dlf = 0; // 独立费
        let aqwmsgf = 0; // 安全生产、文明施工费
        let sj = 0;  // 税金

        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // 获取该单项下的所有单位
        let unitProjectsByConstruct = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
        // 整个项目的总工程造价值
        let constructCost = 0;
        for (let j = 0; j < unitProjectsByConstruct.length; j++) {
            let unitProject = unitProjectsByConstruct[j];
            // 获取该单位的费用汇总
            let param = {
                constructId: constructId,
                singleId: unitProject.parentId,
                unitId: unitProject.sequenceNbr
            }
            let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);
            constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
        }

        // 获取该单项下的所有单位
        let unitProjects = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(subSingleProject.children, unitProjects);
        // 存放所有单位对应的造价分析的和
        if (ObjectUtils.isNotEmpty(unitProjects)) {
            for (let j = 0; j < unitProjects.length; j++) {
                let unitProject = unitProjects[j];

                // 获取单位工程费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);

                //单项合计计算
                budgetzjf = NumberUtil.add(budgetzjf, unitCostSummaryPriceMap.get("直接费"));  // 预算书-直接费
                qzrgf = NumberUtil.add(qzrgf, unitCostSummaryPriceMap.get("人工费"));  // 其中-人工费
                qzclf = NumberUtil.add(qzclf, unitCostSummaryPriceMap.get("材料费"));  // 其中-材料费
                qzjxf = NumberUtil.add(qzjxf, unitCostSummaryPriceMap.get("机械费"));  // 其中-机械费
                qzsbf = NumberUtil.add(qzsbf, unitCostSummaryPriceMap.get("设备费"));  // 其中-设备费
                qzzcf = NumberUtil.add(qzzcf, unitCostSummaryPriceMap.get("主材费"));  // 其中-主材费

                qtcsf = NumberUtil.add(qtcsf, unitCostSummaryPriceMap.get("其他措施费"));   // 其他措施费
                qtcsrgf = NumberUtil.add(qtcsrgf, unitCostSummaryPriceMap.get("其他措施费人工费"));  // 其中-其他措施费-人工费
                qtcsclf = NumberUtil.add(qtcsclf, unitCostSummaryPriceMap.get("其他措施费材料费"));  // 其中-其他措施费-材料费
                qtcsjxf = NumberUtil.add(qtcsjxf, unitCostSummaryPriceMap.get("其他措施费机械费") ? unitCostSummaryPriceMap.get("其他措施费机械费") : 0);  // 其中-其他措施费-机械费

                lxgcbgf = NumberUtil.add(lxgcbgf, unitCostSummaryPriceMap.get("零星工程包干费"));  // 零星工程包干费
                lxgcbgrgf = NumberUtil.add(lxgcbgrgf, unitCostSummaryPriceMap.get("零星工程包干费人工费"));  // 其中-零星工程包干费-人工费
                lxgcbgclf = NumberUtil.add(lxgcbgclf, unitCostSummaryPriceMap.get("零星工程包干费材料费"));  // 其中-零星工程包干费-材料费
                lxgcbgsbf = NumberUtil.add(lxgcbgsbf, unitCostSummaryPriceMap.get("零星工程包干费机械费") ? unitCostSummaryPriceMap.get("零星工程包干费机械费") : 0);

                qyglf = NumberUtil.add(qyglf, unitCostSummaryPriceMap.get("企业管理费"));  // 企业管理费
                gf = NumberUtil.add(gf, unitCostSummaryPriceMap.get("规费"));  // 规费
                lr = NumberUtil.add(lr, unitCostSummaryPriceMap.get("利润"));  // 利润
                dlf = NumberUtil.add(dlf, unitCostSummaryPriceMap.get("独立费"));  // 独立费
                aqwmsgf = NumberUtil.add(aqwmsgf, unitCostSummaryPriceMap.get("安全文明施工费"));  // 安全生产、文明施工费
                sj = NumberUtil.add(sj, unitCostSummaryPriceMap.get("税金"));  // 税金

                // 建筑工程费 = ∑各建筑单位工程造价合价
                if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                    jzCostSummary = NumberUtil.add(jzCostSummary, unitProject.projectCost);
                }
                // 安装工程费 = ∑各安装单位工程造价合价
                if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                    azCostSummary = NumberUtil.add(azCostSummary, unitProject.projectCost);
                }
            }
        }

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let unitcost = precision.COST_ANALYSIS.unitcost;
        let costProportion = precision.COST_ANALYSIS.costProportion;
        let je = precision.COST_ANALYSIS.je;


        for (let i = 0; i < allSubSingles.length; i++) {
            let singleCostAnalysisVO = allSubSingles[i];
            singleCostAnalysisVO.jzCostSummary = jzCostSummary;
            singleCostAnalysisVO.azCostSummary = azCostSummary;

            singleCostAnalysisVO.budgetzjf = budgetzjf;
            singleCostAnalysisVO.qzrgf = qzrgf;
            singleCostAnalysisVO.qzclf = qzclf;
            singleCostAnalysisVO.qzjxf = qzjxf;
            singleCostAnalysisVO.qzsbf = qzsbf;
            singleCostAnalysisVO.qzzcf = qzzcf;

            singleCostAnalysisVO.qtcsf = qtcsf;
            singleCostAnalysisVO.qtcsrgf = qtcsrgf;
            singleCostAnalysisVO.qtcsclf = qtcsclf;
            singleCostAnalysisVO.qtcsjxf = qtcsjxf;

            singleCostAnalysisVO.lxgcbgf = lxgcbgf;
            singleCostAnalysisVO.lxgcbgrgf = lxgcbgrgf;
            singleCostAnalysisVO.lxgcbgclf = lxgcbgclf;
            singleCostAnalysisVO.lxgcbgsbf = lxgcbgsbf;
            singleCostAnalysisVO.qyglf = qyglf;
            singleCostAnalysisVO.gf = gf;
            singleCostAnalysisVO.lr = lr;
            singleCostAnalysisVO.dlf = dlf;
            singleCostAnalysisVO.aqwmsgf = aqwmsgf;
            singleCostAnalysisVO.sj = sj;

            // singleCostAnalysisVO.average = ObjectUtils.isNotEmpty(subSingleProject.average) ? subSingleProject.average : 0;
            singleCostAnalysisVO.unitcost = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(jzCostSummary, azCostSummary), singleCostAnalysisVO.average), unitcost);  // 单方造价(元/m、元/㎡);
            singleCostAnalysisVO.costProportion = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(jzCostSummary, azCostSummary), constructCost) * 100, costProportion); // 造价占比
            singleCostAnalysisVO.levelType = ProjectTypeConstants.PROJECT_TYPE_SINGLE;
        }
    }

    /**
     * 获取指定单项下的所有的单位
     * @param constructId
     * @param subSingleProject
     * @param dispNo
     * @returns []
     */
    async getSubSingleContainUnitsCostAnalysisData(constructId, subSingleProject, dispNo) {

        let jzCostSummary = 0;  // 工程费用汇总-建筑工程
        let azCostSummary = 0;  // 工程费用汇总-安装工程

        let budgetzjf = 0; // 预算书-直接费 合计
        let qzrgf = 0;   // 其中-人工费
        let qzclf = 0;   // 其中-材料费
        let qzjxf = 0;   // 其中-机械费
        let qzsbf = 0;   // 其中-设备费
        let qzzcf = 0;   // 其中-主材费

        let qtcsf = 0;  // 其他措施费
        let qtcsrgf = 0;  // 其中-其他措施费-人工费
        let qtcsclf = 0;  // 其中-其他措施费-材料费
        let qtcsjxf = 0;  // 其中-其他措施费-机械费

        let lxgcbgf = 0;  // 零星工程包干费
        let lxgcbgrgf = 0;  // 其中-零星工程包干费-人工费
        let lxgcbgclf = 0;  // 其中-零星工程包干费-材料费
        let lxgcbgsbf = 0;  // 其中-零星工程包干费-机械费
        let qyglf = 0;  // 企业管理费
        let gf = 0;  // 规费
        let lr = 0;  // 利润
        let dlf = 0; // 独立费
        let aqwmsgf = 0; // 安全生产、文明施工费
        let sj = 0;  // 税金

        // 指定单项下的所有单位
        let unitProjectsBySingle = subSingleProject.children;
        // 整个项目的总工程造价值
        let constructCost = 0;
        if (ObjectUtils.isNotEmpty(unitProjectsBySingle)) {
            // 获取该工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
            // 获取该单项下的所有单位
            let unitProjectsByConstruct = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);

            for (let j = 0; j < unitProjectsByConstruct.length; j++) {
                let unitProject = unitProjectsByConstruct[j];
                // 获取该单位的费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);
                constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
            }

            for (let j = 0; j < unitProjectsBySingle.length; j++) {
                let unitProject = unitProjectsBySingle[j];
                // 获取单位工程费用汇总，进行数据添值
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);

                //单项合计计算
                budgetzjf = NumberUtil.add(budgetzjf, unitCostSummaryPriceMap.get("直接费"));  // 预算书-直接费
                qzrgf = NumberUtil.add(qzrgf, unitCostSummaryPriceMap.get("人工费")); // 其中-人工费
                qzclf = NumberUtil.add(qzclf, unitCostSummaryPriceMap.get("材料费")); // 其中-材料费
                qzjxf = NumberUtil.add(qzjxf, unitCostSummaryPriceMap.get("机械费")); // 其中-机械费
                qzsbf = NumberUtil.add(qzsbf, unitCostSummaryPriceMap.get("设备费")); // 其中-设备费
                qzzcf = NumberUtil.add(qzzcf, unitCostSummaryPriceMap.get("主材费")); // 其中-主材费
                qtcsf = NumberUtil.add(qtcsf, unitCostSummaryPriceMap.get("其他措施费"));  // 其他措施费
                qtcsrgf = NumberUtil.add(qtcsrgf, unitCostSummaryPriceMap.get("其他措施费人工费"));  // 其中-其他措施费-人工费
                qtcsclf = NumberUtil.add(qtcsclf, unitCostSummaryPriceMap.get("其他措施费材料费"));  // 其中-其他措施费-材料费
                qtcsjxf = NumberUtil.add(qtcsjxf, unitCostSummaryPriceMap.get("其他措施费机械费") ? unitCostSummaryPriceMap.get("其他措施费机械费") : 0);   // 其中-其他措施费-机械费
                lxgcbgf = NumberUtil.add(lxgcbgf, unitCostSummaryPriceMap.get("零星工程包干费"));  // 零星工程包干费
                lxgcbgrgf = NumberUtil.add(lxgcbgrgf, unitCostSummaryPriceMap.get("零星工程包干费人工费"));  // 其中-零星工程包干费-人工费
                lxgcbgclf = NumberUtil.add(lxgcbgclf, unitCostSummaryPriceMap.get("零星工程包干费材料费"));  // 其中-零星工程包干费-材料费
                lxgcbgsbf = NumberUtil.add(lxgcbgsbf, unitCostSummaryPriceMap.get("零星工程包干费机械费") ? unitCostSummaryPriceMap.get("零星工程包干费机械费") : 0);   // 其中-零星工程包干费-机械费
                qyglf = NumberUtil.add(qyglf, unitCostSummaryPriceMap.get("企业管理费"));   // 企业管理费
                gf = NumberUtil.add(gf, unitCostSummaryPriceMap.get("规费"));   // 规费
                lr = NumberUtil.add(lr, unitCostSummaryPriceMap.get("利润"));   // 利润
                dlf = NumberUtil.add(dlf, unitCostSummaryPriceMap.get("独立费"));  // 独立费
                aqwmsgf = NumberUtil.add(aqwmsgf, unitCostSummaryPriceMap.get("安全文明施工费")); // 安全生产、文明施工费
                sj = NumberUtil.add(sj, unitCostSummaryPriceMap.get("税金"));  // 税金

                // 建筑工程费 = ∑各建筑单位工程造价合价
                if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                    jzCostSummary = NumberUtil.add(jzCostSummary, unitProject.projectCost);
                }
                // 安装工程费 = ∑各安装单位工程造价合价
                if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                    azCostSummary = NumberUtil.add(azCostSummary, unitProject.projectCost);
                }
            }
        }

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let unitcost = precision.COST_ANALYSIS.unitcost;
        let costProportion = precision.COST_ANALYSIS.costProportion;
        let je = precision.COST_ANALYSIS.je;

        //存放单项工程数据
        let singleCostAnalysisArray = [];
        let singleCostAnalysisVO = new GsCostAnalysisSingleVO();
        singleCostAnalysisVO.dispNo = dispNo;
        singleCostAnalysisVO.projectName = subSingleProject.name;
        singleCostAnalysisVO.sequenceNbr = subSingleProject.sequenceNbr;
        singleCostAnalysisVO.jzCostSummary = jzCostSummary;
        singleCostAnalysisVO.azCostSummary = azCostSummary;

        singleCostAnalysisVO.budgetzjf = budgetzjf;
        singleCostAnalysisVO.qzrgf = qzrgf;
        singleCostAnalysisVO.qzclf = qzclf;
        singleCostAnalysisVO.qzjxf = qzjxf;
        singleCostAnalysisVO.qzsbf = qzsbf;
        singleCostAnalysisVO.qzzcf = qzzcf;

        singleCostAnalysisVO.qtcsf = qtcsf;
        singleCostAnalysisVO.qtcsrgf = qtcsrgf;
        singleCostAnalysisVO.qtcsclf = qtcsclf;
        singleCostAnalysisVO.qtcsjxf = qtcsjxf;

        singleCostAnalysisVO.lxgcbgf = lxgcbgf;
        singleCostAnalysisVO.lxgcbgrgf = lxgcbgrgf;
        singleCostAnalysisVO.lxgcbgclf = lxgcbgclf;
        singleCostAnalysisVO.lxgcbgsbf = lxgcbgsbf;

        singleCostAnalysisVO.qyglf = qyglf;
        singleCostAnalysisVO.gf = gf;
        singleCostAnalysisVO.lr = lr;
        singleCostAnalysisVO.dlf = dlf;
        singleCostAnalysisVO.aqwmsgf = aqwmsgf;
        singleCostAnalysisVO.sj = sj;
        singleCostAnalysisVO.average = ObjectUtils.isNotEmpty(subSingleProject.average) ? subSingleProject.average : 0;  // 工程规模
        singleCostAnalysisVO.unitcost = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(jzCostSummary, azCostSummary), singleCostAnalysisVO.average), unitcost); // 单方造价(元/m、元/㎡)
        singleCostAnalysisVO.costProportion = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(jzCostSummary, azCostSummary), constructCost) * 100, costProportion); // 造价占比

        //存放所有单位对应的造价分析
        let commonCostAnalysis = {};
        singleCostAnalysisVO.childrenList = await this.generateUnitsCostAnalysisData(unitProjectsBySingle, singleCostAnalysisVO.dispNo, constructId, commonCostAnalysis);
        singleCostAnalysisVO.azCostSummary = commonCostAnalysis.azCostSummary
        singleCostAnalysisVO.jzCostSummary = commonCostAnalysis.jzCostSummary;
        singleCostAnalysisVO.levelType = ProjectTypeConstants.PROJECT_TYPE_SINGLE;
        singleCostAnalysisArray.push(singleCostAnalysisVO);
        return singleCostAnalysisArray;
    }


    /**
     * 获取单位工程造价分析
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns []
     */
    async generateUnitCostAnalysisData(constructId, singleId, unitId) {
        // 获取单位工程
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 单位造价分析模板
        let gsZjfx;
        if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
            gsZjfx = gsJzZjfx;
        } else {
            gsZjfx = gsAzZjfx;
        }
        let zjfxArray = [];
        for (let i in gsZjfx) {
            let obj = new GsCostAnalysisUnitVO();
            ConvertUtil.setDstBySrc(gsZjfx[i], obj);
            zjfxArray.push(obj);
        }

        // 获取该单位的费用汇总
        let param = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        }
        let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let unitcost = precision.COST_ANALYSIS.unitcost;
        let costProportion = precision.COST_ANALYSIS.costProportion;
        let je = precision.COST_ANALYSIS.je;

        // 数值数组
        let contexts = [];
        contexts.push(unitCostSummaryPriceMap.get("工程造价"));  // 工程造价  小写
        contexts.push(NumberUtil.numToCny(unitCostSummaryPriceMap.get("工程造价")));  // 工程造价  大写

        contexts.push(NumberUtil.divide(unitCostSummaryPriceMap.get("工程造价"), unitProject.average));  //单方造价(元/m、元/㎡)
        contexts.push(unitCostSummaryPriceMap.get("直接费")); // 预算书-直接费
        contexts.push(unitCostSummaryPriceMap.get("人工费"));   // 其中-人工费
        contexts.push(unitCostSummaryPriceMap.get("材料费"));   // 其中-材料费
        contexts.push(unitCostSummaryPriceMap.get("机械费"));   // 其中-机械费
        contexts.push(unitCostSummaryPriceMap.get("设备费"));   // 其中-设备费
        contexts.push(unitCostSummaryPriceMap.get("主材费"));   // 其中-主材费

        contexts.push(unitCostSummaryPriceMap.get("其他措施费"));   // 其他措施费
        contexts.push(unitCostSummaryPriceMap.get("其他措施费人工费"));   // 其中-其他措施费-人工费
        contexts.push(unitCostSummaryPriceMap.get("其他措施费材料费"));   // 其中-其他措施费-材料费
        // if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
        contexts.push(ObjectUtils.isNotEmpty(unitCostSummaryPriceMap.get("其他措施费机械费")) ? unitCostSummaryPriceMap.get("其他措施费机械费") : 0);   // 其中-其他措施费-机械费
        // }

        contexts.push(unitCostSummaryPriceMap.get("零星工程包干费"));   // 零星工程包干费
        contexts.push(unitCostSummaryPriceMap.get("零星工程包干费人工费"));   // 其中-零星工程包干费-人工费
        contexts.push(unitCostSummaryPriceMap.get("零星工程包干费材料费"));   // 其中-零星工程包干费-材料费
        // if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
        contexts.push(ObjectUtils.isNotEmpty(unitCostSummaryPriceMap.get("零星工程包干费机械费")) ? unitCostSummaryPriceMap.get("零星工程包干费机械费") : 0);   // 其中-零星工程包干费-机械费
        // }
        contexts.push(unitCostSummaryPriceMap.get("企业管理费"));   // 企业管理费
        contexts.push(unitCostSummaryPriceMap.get("规费"));   // 规费
        contexts.push(unitCostSummaryPriceMap.get("利润"));   // 利润
        contexts.push(unitCostSummaryPriceMap.get("独立费"));  // 独立费
        contexts.push(unitCostSummaryPriceMap.get("安全文明施工费"));   // 安全生产、文明施工费
        contexts.push(unitCostSummaryPriceMap.get("税金"));    // 税金

        // 给对应的模板放置数据值
        for (let i = 0; i < zjfxArray.length; i++) {
            zjfxArray[i].context = contexts[i];
        }
        // 遍历，进行DispNo序号添加
        let result = [];
        let parentItem = new GsCostAnalysisUnitVO();
        for (const item of zjfxArray) {
            if (item.dispNo.toString().indexOf(".") === -1) {
                // 如果没有小数点的行，就说明是父级
                parentItem = item;
                parentItem.childrenList = [];
                result.push(parentItem);
            } else {
                // 其他是子集
                parentItem.childrenList.push(item);
            }
        }
        return result;
    }

    /**
     * 获取多有单位的造价分析
     * @param unitProjects
     * @param singDispNo
     * @param constructId
     * @param commonCostAnalysis
     * @returns {any[]}
     */
    async generateUnitsCostAnalysisData(unitProjects, singDispNo, constructId, commonCostAnalysis) {
        //存放所有单位对应的造价分析
        let unitCostAnalysis = [];
        if (!ObjectUtils.isEmpty(unitProjects)) {

            // 获取该工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
            // 获取该单项下的所有单位
            let unitProjectsByConstruct = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
            // 整个项目的总工程造价值
            let constructCost = 0;
            for (let j = 0; j < unitProjectsByConstruct.length; j++) {
                let unitProject = unitProjectsByConstruct[j];
                // 获取该单位的费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);
                constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
            }

            // 小数点精度
            let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
            let unitcost = precision.COST_ANALYSIS.unitcost;
            let costProportion = precision.COST_ANALYSIS.costProportion;
            let je = precision.COST_ANALYSIS.je;

            let azCostSummaryTotal = 0, jzCostSummaryTotal = 0;
            // 遍历所有单位，进行赋值
            for (let j = 0; j < unitProjects.length; j++) {
                let costAnalysisSingleVO = new GsCostAnalysisSingleVO();
                let unitProject = unitProjects[j];

                // 获取该单位的费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);

                if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                    costAnalysisSingleVO.azCostSummary = ObjectUtils.isNotEmpty(unitCostSummaryPriceMap.get("工程造价")) ? unitCostSummaryPriceMap.get("工程造价") : 0;   // 工程费用汇总-安装工程
                    azCostSummaryTotal = NumberUtil.add(azCostSummaryTotal, costAnalysisSingleVO.azCostSummary);
                }
                if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                    costAnalysisSingleVO.jzCostSummary = ObjectUtils.isNotEmpty(unitCostSummaryPriceMap.get("工程造价")) ? unitCostSummaryPriceMap.get("工程造价") : 0;   // 工程费用汇总-建筑工程
                    jzCostSummaryTotal = NumberUtil.add(jzCostSummaryTotal, costAnalysisSingleVO.jzCostSummary);
                }
                costAnalysisSingleVO.budgetzjf = unitCostSummaryPriceMap.get("直接费"); // 预算书-直接费
                costAnalysisSingleVO.qzrgf = unitCostSummaryPriceMap.get("人工费");   // 其中-人工费
                costAnalysisSingleVO.qzclf = unitCostSummaryPriceMap.get("材料费");   // 其中-材料费
                costAnalysisSingleVO.qzjxf = unitCostSummaryPriceMap.get("机械费");   // 其中-机械费
                costAnalysisSingleVO.qzsbf = unitCostSummaryPriceMap.get("设备费");   // 其中-设备费
                costAnalysisSingleVO.qzzcf = unitCostSummaryPriceMap.get("主材费");   // 其中-主材费

                costAnalysisSingleVO.qtcsf = unitCostSummaryPriceMap.get("其他措施费");   // 其他措施费
                costAnalysisSingleVO.qtcsrgf = unitCostSummaryPriceMap.get("其他措施费人工费");   // 其中-其他措施费-人工费
                costAnalysisSingleVO.qtcsclf = unitCostSummaryPriceMap.get("其他措施费材料费");   // 其中-其他措施费-材料费
                costAnalysisSingleVO.qtcsjxf = unitCostSummaryPriceMap.get("其他措施费机械费") ? unitCostSummaryPriceMap.get("其他措施费机械费") : 0;   // 其中-其他措施费-机械费

                costAnalysisSingleVO.lxgcbgf = unitCostSummaryPriceMap.get("零星工程包干费");   // 零星工程包干费
                costAnalysisSingleVO.lxgcbgrgf = unitCostSummaryPriceMap.get("零星工程包干费人工费");   // 其中-零星工程包干费-人工费
                costAnalysisSingleVO.lxgcbgclf = unitCostSummaryPriceMap.get("零星工程包干费材料费");   // 其中-零星工程包干费-材料费
                costAnalysisSingleVO.lxgcbgsbf = unitCostSummaryPriceMap.get("零星工程包干费机械费") ? unitCostSummaryPriceMap.get("零星工程包干费机械费") : 0;   // 其中-零星工程包干费-机械费

                costAnalysisSingleVO.qyglf = unitCostSummaryPriceMap.get("企业管理费");   // 企业管理费
                costAnalysisSingleVO.gf = unitCostSummaryPriceMap.get("规费");   // 规费
                costAnalysisSingleVO.lr = unitCostSummaryPriceMap.get("利润");   // 利润
                costAnalysisSingleVO.dlf = unitCostSummaryPriceMap.get("独立费");  // 独立费
                costAnalysisSingleVO.aqwmsgf = unitCostSummaryPriceMap.get("安全文明施工费");   // 安全生产、文明施工费
                costAnalysisSingleVO.sj = unitCostSummaryPriceMap.get("税金");    // 税金

                // 造价占比 = 当前行（建筑工程造价+安装工程造价）/总工程造价
                costAnalysisSingleVO.costProportion = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(costAnalysisSingleVO.azCostSummary, costAnalysisSingleVO.jzCostSummary), constructCost) * 100, costProportion);
                // 不同位置相同层级中的工程规模应保持一致。
                costAnalysisSingleVO.average = ObjectUtils.isNotEmpty(unitProject.average) ? unitProject.average : 0;
                // 单方造价 = 当前行（建筑工程造价+安装工程造价）/当前行工程规模
                costAnalysisSingleVO.unitcost = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(costAnalysisSingleVO.azCostSummary, costAnalysisSingleVO.jzCostSummary), costAnalysisSingleVO.average), unitcost);
                costAnalysisSingleVO.levelType = ProjectTypeConstants.PROJECT_TYPE_UNIT;
                costAnalysisSingleVO.projectName = unitProject.name;
                costAnalysisSingleVO.sequenceNbr = unitProject.sequenceNbr;
                if (singDispNo !== null) {
                    costAnalysisSingleVO.dispNo = singDispNo + '.' + (j + 1);
                } else {
                    costAnalysisSingleVO.dispNo = (j + 1);
                }
                unitCostAnalysis.push(costAnalysisSingleVO);
            }
            commonCostAnalysis.azCostSummary = azCostSummaryTotal
            commonCostAnalysis.jzCostSummary = jzCostSummaryTotal;
        }
        return unitCostAnalysis;
    }

    /**
     * 工程项目造价分析
     * @returns {Promise<GsCostAnalysisSingleVO[]>}
     * @param constructId
     */
    async generateConstructCostAnalysisData(constructId) {
        // 获取工程项目
        let projectObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        let costAnalysisVOList = [];
        // 获取工程项目下所有的单项造价分析数据
        if (projectObj.children && projectObj.children.length > 0) {
            let constructCostAnalysiss = [];
            for (let i = 0; i < projectObj.children.length; i++) {
                //获取该单项工程对应的造价分析
                let singleCostAnalysiss = await this.generateSingleCostAnalysisData(constructId, projectObj.children[i], 1 + i + 1 + "");
                constructCostAnalysiss.push(...singleCostAnalysiss);
            }

            let constructCostAnalysisVO = new GsCostAnalysisConstructVO();
            let jzCostSummary = 0;  // 工程费用汇总-建筑工程
            let azCostSummary = 0;  // 工程费用汇总-安装工程

            let budgetzjf = 0; // 预算书-直接费 合计
            let qzrgf = 0;   // 其中-人工费
            let qzclf = 0;   // 其中-材料费
            let qzjxf = 0;   // 其中-机械费
            let qzsbf = 0;   // 其中-设备费
            let qzzcf = 0;   // 其中-主材费

            let qtcsf = 0;  // 其他措施费
            let qtcsrgf = 0;  // 其中-其他措施费-人工费
            let qtcsclf = 0;  // 其中-其他措施费-材料费
            let qtcsjxf = 0;  // 其中-其他措施费-机械费

            let lxgcbgf = 0;  // 零星工程包干费
            let lxgcbgrgf = 0;  // 其中-零星工程包干费-人工费
            let lxgcbgclf = 0;  // 其中-零星工程包干费-材料费
            let lxgcbgsbf = 0;  // 其中-零星工程包干费-机械费
            let qyglf = 0;  // 企业管理费
            let gf = 0;  // 规费
            let lr = 0;  // 利润
            let dlf = 0; // 独立费
            let aqwmsgf = 0; // 安全生产、文明施工费
            let sj = 0;  // 税金

            // 整个项目的总工程造价值
            let constructCost = 0;
            // 获取该单项下的所有单位
            let unitProjectsByConstruct = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(projectObj.children, unitProjectsByConstruct);

            for (let j = 0; j < unitProjectsByConstruct.length; j++) {
                let unitProject = unitProjectsByConstruct[j];
                // 获取该单位的费用汇总
                let param = {
                    constructId: constructId,
                    singleId: unitProject.parentId,
                    unitId: unitProject.sequenceNbr
                }
                let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);
                constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
            }

            for (let j = 0; j < constructCostAnalysiss.length; j++) {
                let singleCostAnalysis = constructCostAnalysiss[j];
                //单项合计计算
                budgetzjf = NumberUtil.add(budgetzjf, singleCostAnalysis.budgetzjf);  // 预算书-直接费
                qzrgf = NumberUtil.add(qzrgf, singleCostAnalysis.qzrgf); // 其中-人工费
                qzclf = NumberUtil.add(qzclf, singleCostAnalysis.qzclf); // 其中-材料费
                qzjxf = NumberUtil.add(qzjxf, singleCostAnalysis.qzjxf); // 其中-机械费
                qzsbf = NumberUtil.add(qzsbf, singleCostAnalysis.qzsbf); // 其中-设备费
                qzzcf = NumberUtil.add(qzzcf, singleCostAnalysis.qzzcf); // 其中-主材费
                qtcsf = NumberUtil.add(qtcsf, singleCostAnalysis.qtcsf);  // 其他措施费
                qtcsrgf = NumberUtil.add(qtcsrgf, singleCostAnalysis.qtcsrgf);  // 其中-其他措施费-人工费
                qtcsclf = NumberUtil.add(qtcsclf, singleCostAnalysis.qtcsclf);  // 其中-其他措施费-材料费
                qtcsjxf = NumberUtil.add(qtcsjxf, singleCostAnalysis.qtcsjxf);   // 其中-其他措施费-机械费
                lxgcbgf = NumberUtil.add(lxgcbgf, singleCostAnalysis.lxgcbgf);  // 零星工程包干费
                lxgcbgrgf = NumberUtil.add(lxgcbgrgf, singleCostAnalysis.lxgcbgrgf);  // 其中-零星工程包干费-人工费
                lxgcbgclf = NumberUtil.add(lxgcbgclf, singleCostAnalysis.lxgcbgclf);  // 其中-零星工程包干费-材料费
                lxgcbgsbf = NumberUtil.add(lxgcbgsbf, singleCostAnalysis.lxgcbgsbf);   // 其中-零星工程包干费-机械费
                qyglf = NumberUtil.add(qyglf, singleCostAnalysis.qyglf);   // 零星工程包干费
                gf = NumberUtil.add(gf, singleCostAnalysis.gf);   // 其中-零星工程包干费-人工费
                lr = NumberUtil.add(lr, singleCostAnalysis.lr);   // 其中-零星工程包干费-材料费
                dlf = NumberUtil.add(dlf, singleCostAnalysis.dlf);  // 独立费
                aqwmsgf = NumberUtil.add(aqwmsgf, singleCostAnalysis.aqwmsgf); // 安全生产、文明
                sj = NumberUtil.add(sj, singleCostAnalysis.sj);  // 税金

                // 建筑工程费 = ∑各建筑单位工程造价合价
                jzCostSummary = NumberUtil.add(jzCostSummary, singleCostAnalysis.jzCostSummary);
                // 安装工程费 = ∑各安装单位工程造价合价
                azCostSummary = NumberUtil.add(azCostSummary, singleCostAnalysis.azCostSummary);
            }

            // 小数点精度
            let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
            let unitcost = precision.COST_ANALYSIS.unitcost;
            let costProportion = precision.COST_ANALYSIS.costProportion;
            let je = precision.COST_ANALYSIS.je;

            constructCostAnalysisVO.dispNo = "1";
            constructCostAnalysisVO.projectName = projectObj.name;
            constructCostAnalysisVO.sequenceNbr = projectObj.sequenceNbr;
            constructCostAnalysisVO.jzCostSummary = jzCostSummary;
            constructCostAnalysisVO.azCostSummary = azCostSummary;

            constructCostAnalysisVO.budgetzjf = budgetzjf;
            constructCostAnalysisVO.qzrgf = qzrgf;
            constructCostAnalysisVO.qzclf = qzclf;
            constructCostAnalysisVO.qzjxf = qzjxf;
            constructCostAnalysisVO.qzsbf = qzsbf;
            constructCostAnalysisVO.qzzcf = qzzcf;

            constructCostAnalysisVO.qtcsf = qtcsf;
            constructCostAnalysisVO.qtcsrgf = qtcsrgf;
            constructCostAnalysisVO.qtcsclf = qtcsclf;
            constructCostAnalysisVO.qtcsjxf = qtcsjxf;

            constructCostAnalysisVO.lxgcbgf = lxgcbgf;
            constructCostAnalysisVO.lxgcbgrgf = lxgcbgrgf;
            constructCostAnalysisVO.lxgcbgclf = lxgcbgclf;
            constructCostAnalysisVO.lxgcbgsbf = lxgcbgsbf;

            constructCostAnalysisVO.qyglf = qyglf;
            constructCostAnalysisVO.gf = gf;
            constructCostAnalysisVO.lr = lr;
            constructCostAnalysisVO.dlf = dlf;
            constructCostAnalysisVO.aqwmsgf = aqwmsgf;
            constructCostAnalysisVO.sj = sj;
            constructCostAnalysisVO.average = ObjectUtils.isNotEmpty(projectObj.average) ? projectObj.average : 0  // 工程规模
            constructCostAnalysisVO.unitcost = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(jzCostSummary, azCostSummary), Number(constructCostAnalysisVO.average)), unitcost); // 单方造价(元/m、元/㎡)
            constructCostAnalysisVO.costProportion = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.add(jzCostSummary, azCostSummary), constructCost) * 100, costProportion); // 造价占比

            //存放所有单项对应的造价分析
            constructCostAnalysisVO.childrenList = constructCostAnalysiss;
            constructCostAnalysisVO.levelType = ProjectTypeConstants.PROJECT_TYPE_PROJECT;
            costAnalysisVOList.push(constructCostAnalysisVO);
        }
        return costAnalysisVOList;
    }

    // /**
    //  * 递归获取该单项下的所有单位
    //  * @param singleProject
    //  * @param unitProjects
    //  */
    // getUnitProjectsByCurrentNode(singleProjects, unitProjects) {
    //     if (ObjectUtils.isNotEmpty(singleProjects)) {
    //         singleProjects.forEach(item => {
    //             if (item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
    //                 unitProjects.push(item);
    //             } else {
    //                 this.getUnitProjectsByCurrentNode(item.children, unitProjects);
    //             }
    //         });
    //     }
    // }

    /**
     * 修改造价分析
     * @param args
     * @returns {Promise<void>}
     */
    async updateCostAnalysis(args) {
        // sequenceNbr:修改工程规模的单项或者单位id   flag:是否修改该单项下的所有子项工程规模  average:工程规模  type：单位、单项类型
        let {constructId, sequenceNbr, average, type, flag} = args;

        if (ProjectTypeConstants.PROJECT_TYPE_PROJECT === type) {
            // 获取当前工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(sequenceNbr);
            constructProject.average = average;
            ProjectDomain.getDomain(constructId).updateProject(constructProject);
            if (flag) {
                // 获取费用代码
                let costCodeCodePriceMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
                // 修改该单项下的所有子项工程规模
                await this.updateAverageByNode(constructId, constructProject, average, costCodeCodePriceMap);
            }
        }
        if (ProjectTypeConstants.PROJECT_TYPE_SINGLE === type) {
            // 获取当前单项
            let singleProject = ProjectDomain.getDomain(constructId).getProjectById(sequenceNbr);
            singleProject.average = average;
            ProjectDomain.getDomain(constructId).updateProject(singleProject);
            if (flag) {
                // 获取费用代码
                let costCodeCodePriceMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
                // 修改该单项下的所有子项工程规模
                await this.updateAverageByNode(constructId, singleProject, average, costCodeCodePriceMap);
            }
        }
        if (ProjectTypeConstants.PROJECT_TYPE_UNIT === type) {
            // 获取当前单位
            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(sequenceNbr);
            let oldAverage = unitProject.average;
            unitProject.average = average;
            ProjectDomain.getDomain(constructId).updateProject(unitProject);

            // 获取费用代码
            let costCodeCodePriceMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            for (let [key, value] of costCodeCodePriceMap) {
                if (ObjectUtils.isEmpty(value)) {
                    continue;
                }
                if (key.includes(unitProject.sequenceNbr)) {
                    value.find(item => item.code === "GCGM").price = average;
                }
            }
            //判断是否通知定额修改
            if (oldAverage != average) {
                await this._notifyUpdateQuantity(unitProject);
            }
        }
    }
    async _notifyUpdateQuantity(unitProject){
        let deGclMap = ProjectDomain.getDomain(unitProject.constructId).functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
        if (ObjectUtils.isNotEmpty(deGclMap)) {
            let deNotifySet = deGclMap.get(unitProject.sequenceNbr);
            if (ObjectUtils.isNotEmpty(deNotifySet)) {
                //校验quantity
                let codeArgs = {
                    constructId: unitProject.constructId,
                    type: "变量表",
                    unitId: unitProject.sequenceNbr,
                    constructMajorType: unitProject.constructMajorType
                }
                let priceCodes = await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.costCodePrice(codeArgs);
                for (let de of deNotifySet) {
                    await ProjectDomain.getDomain(unitProject.constructId).deDomain.notifyQuantity(de, true, true, priceCodes);
                }
                // 更新费用代码工程规模
                await this.service.PreliminaryEstimate.gsUnitCostCodePriceService.countCostCodePrice({
                    constructId: unitProject.constructId,
                    unitId: unitProject.sequenceNbr,
                    constructMajorType: unitProject.constructMajorType
                });
            }
            // 同步计算工程量明细
            await this.service.PreliminaryEstimate.gsQuantitiesService.recaculateQuantityByUnit(unitProject.constructId, unitProject.sequenceNbr, true);
        }
    }
    /**
     * 修改该单项下的所有子项工程规模
     * @param constructId
     * @param node  指定节点
     * @param average  工程规模
     * @param costCodeCodePriceMap
     */
    async updateAverageByNode(constructId, node, average, costCodeCodePriceMap) {
        if (ObjectUtils.isNotEmpty(node.children)) {
            for (let i = 0; i < node.children.length; i++) {
                let childNode = node.children[i];
                let oldAverage = childNode.average;
                childNode.average = average;
                ProjectDomain.getDomain(constructId).updateProject(childNode);
                // 修改费用代码
                if (ProjectTypeConstants.PROJECT_TYPE_UNIT === childNode.type) {
                    for (let [key, value] of costCodeCodePriceMap) {
                        if (ObjectUtils.isEmpty(value)) {
                            continue;
                        }
                        if (key.includes(childNode.sequenceNbr)) {
                            value.find(item => item.code === "GCGM").price = average;
                        }
                    }
                    //判断是否通知定额修改
                    if (oldAverage != average) {
                        await this._notifyUpdateQuantity(childNode);
                    }
                }
                await this.updateAverageByNode(constructId, childNode, average, costCodeCodePriceMap)
            }
        }
    }

    // /**
    //  * 从单项工程中获取一个单项工程
    //  * @param singleProjects  所有的单项
    //  * @param singleId  查找的单项id
    //  * @returns {*|null}
    //  */
    // getOneFromSingleProjects(singleProjects, singleId) {
    //     if (ObjectUtils.isEmpty(singleProjects)) {
    //         return null;
    //     }
    //     for (let i = 0; i < singleProjects.length; i++) {
    //         if (singleProjects[i].sequenceNbr == singleId) {
    //             return singleProjects[i];
    //         }
    //         if (!ObjectUtils.isEmpty(singleProjects[i].subSingleProjects)) {
    //             let result = this.getOneFromSingleProjects(singleProjects[i].subSingleProjects, singleId);
    //             if (result != null) {
    //                 return result;
    //             }
    //         }
    //     }
    // }

    /**
     * 导出造价分析
     * @param costAnalyses
     */
    async exportUnitCostAnalysis(costAnalyses) {
        if (ObjectUtils.isEmpty(costAnalyses)) {
            return
        }
        let copyCostAnalyses = ConvertUtil.deepCopy(costAnalyses);
        let rowList = [];
        for (let costAnalysis of copyCostAnalyses) {
            rowList.push(costAnalysis)
            if (ObjectUtils.isNotEmpty(costAnalysis.childrenList) && costAnalysis.childrenList.length > 0) {
                let childrenList = costAnalysis.childrenList
                for (let child of childrenList) {
                    rowList.push(child)
                }
            }
        }

        // 存放造价分析文件的路径
        const exportDir = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\造价分析';
        FileUtils.ensurePathExists(exportDir)
        let count = FileUtils.countDirectories(exportDir);
        let options = {
            title: '保存文件',
            defaultPath: exportDir + '\\造价分析' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: ['xlsx']} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.endsWith('xlsx')) {
                filePath += 'xlsx';
            }

            // 提取需要的字段
            const filteredData = rowList.map(item => ({
                '序号': item.dispNo,
                '名称': item.name,
                '内容': item.context,
            }));

            // 转换数据为工作表
            const worksheet = XLSX.utils.json_to_sheet(filteredData);

            // 创建工作簿并添加工作表
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
            // 写入Excel文件
            XLSX.writeFile(workbook, filePath);
            return ResponseData.success(filePath);
        }
    }

    getRowList(copyCostAnalyses, rowList, storey, prefix = '') {
        let i = 1;
        for (let costAnalysis of copyCostAnalyses) {
            costAnalysis.dispNo = prefix ? `${prefix}.${i}` : `${i}`;
            rowList.push(costAnalysis);
            if (ObjectUtils.isNotEmpty(costAnalysis.childrenList) && costAnalysis.childrenList.length > 0) {
                let childrenList = costAnalysis.childrenList;
                rowList = this.getRowList(childrenList, rowList, storey + 1, costAnalysis.dispNo);
            }
            i++;
        }
        return rowList;
    }

    /**
     * 导出造价分析
     * @param costAnalyses
     */
    async exportProjectCostAnalysis(costAnalyses) {
        if (ObjectUtils.isEmpty(costAnalyses)) {
            return
        }
        let copyCostAnalyses = ConvertUtil.deepCopy(costAnalyses);
        let rowList = [];
        rowList = this.getRowList(copyCostAnalyses, rowList, 0, '');
        // 存放造价分析文件的路径
        const exportDir = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\造价分析';
        FileUtils.ensurePathExists(exportDir)
        let count = FileUtils.countDirectories(exportDir);
        let options = {
            title: '保存文件',
            defaultPath: exportDir + '\\造价分析' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: ['xlsx']} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.endsWith('xlsx')) {
                filePath += 'xlsx';
            }

            // 提取需要的字段
            const filteredData = rowList.map(item => ([
                item.dispNo,
                item.projectName,
                item.jzCostSummary,
                item.azCostSummary,
                item.qzrgf,
                item.qzclf,
                item.qzjxf,
                item.qzsbf,
                item.qzzcf,
                item.budgetzjf,
                item.qtcsrgf,
                item.qtcsclf,
                item.qtcsjxf,
                item.lxgcbgrgf,
                item.lxgcbgclf,
                item.lxgcbgsbf,
                item.qyglf,
                item.gf,
                item.lr,
                item.dlf,
                NumberUtil.numberScale(item.costProportion, 2),
                item.aqwmsgf,
                item.sj,
                item.average,
                item.unitcost,
            ]));

            // 创建表头
            let headerRow1 = ['序号', '名称', '工程费用汇总', '工程费用汇总', '预算书', '预算书', '预算书', '预算书', '预算书', '预算书', '其他措施费', '其他措施费', '其他措施费', '零星工程包干费', '零星工程包干费', '零星工程包干费', '企业管理费', '规费', '利润', '独立费', '造价占比（%）', '安全生产、文明施工费', '税金', '工程规模(m³/㎡/m）', '单方造价(元/㎡、元/m）'];
            let headerRow2 = ['序号', '名称', '建筑工程', '安装工程', '人工费', '材料费', '机械费', '设备费', '主材费', '合计', '人工费', '材料费', '机械费', '人工费', '材料费', '机械费', '企业管理费', '规费', '利润', '独立费', '造价占比（%）', '安全生产、文明施工费', '税金', '工程规模(m³/㎡/m）', '单方造价(元/㎡、元/m）'];
            // 转换数据为工作表
            let worksheet = XLSX.utils.aoa_to_sheet([headerRow1, headerRow2, ...filteredData]);

            // 定义合并的单元格
            let merges = [];

            // 检查每一列的第一行和第二行是否相等，如果相等则添加到合并数组中
            for (let i = 0; i < headerRow1.length; i++) {
                if (headerRow1[i] === headerRow2[i]) {
                    merges.push({s: {r: 0, c: i}, e: {r: 1, c: i}});
                }
            }
            // 检查第一行中的相等单元格并进行合并
            let startCol = -1; // 合并的起始列
            for (let i = 0; i < headerRow1.length; i++) {
                if (i === 0 || headerRow1[i] !== headerRow1[i - 1]) {
                    // 如果当前列的值与前一列不同，重置起始列
                    startCol = i;
                }

                // 检查是否是最后一列或下一个单元格不同
                if (i === headerRow1.length - 1 || headerRow1[i] !== headerRow1[i + 1]) {
                    if (i > startCol) {
                        // 如果起始列和当前列不同，添加合并信息
                        merges.push({s: {r: 0, c: startCol}, e: {r: 0, c: i}});
                    }
                }
            }

            // 设置合并单元格
            worksheet['!merges'] = merges;

            // 创建工作簿并添加工作表
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
            // 写入Excel文件
            XLSX.writeFile(workbook, filePath);
            return ResponseData.success(filePath);
        }
    }

}

GsCostAnalysisService.toString = () => '[class GsCostAnalysisService]';
module.exports = GsCostAnalysisService;