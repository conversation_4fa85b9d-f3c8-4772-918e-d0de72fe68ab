const {FreeRateModel} = require("../models/FreeRateModel");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ObjectUtils} = require('../utils/ObjectUtils');
const {Service} = require('../../../core');
const WildcardMap = require("../core/container/WildcardMap");
const BusinessConstants = require("../constants/BusinessConstants");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const GsjRateKindEnum = require("../enums/GsjRateKindEnum");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const {Snowflake} = require("../utils/Snowflake");
const FreeRateType = require('../enums/FreeRateType');
const CommonConstants = require("../../gongLiaoJiProject/constants/CommonConstants");
const {NumberUtil} = require("../utils/NumberUtil");
const {MainMaterialSetting} = require("../models/MainMaterialSetting");
const ConstantUtil = require("../enums/ConstantUtil");


/**
 * 单位工程初始化服务  service
 */
class GljInitUnitProjectService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 初始化单位
     */
    async init(projectModel, constructId) {

        let qfMajorTypeMoneyMap = new Map();
        // 查询该专业对应默认的取费表code
        // let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByLibraryCode(projectModel.constructMajorType);
        let baseFeeFileProject = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFileProject();
        baseFeeFileProject = baseFeeFileProject.filter(item => item.libraryCode === projectModel.constructMajorType && item.code.includes('0'));
        projectModel.isSingleMajorFlag = false;  // 费用汇总是否进行单专业汇总  true 单专业  false 多专业汇总
        projectModel.isPartSingleMajorFlag = false;  // 局部汇总是否进行单专业汇总  true 单专业  false 多专业汇总
        projectModel.qfMainMajorType = baseFeeFileProject[0].qfCode;
        projectModel.qfMajorType = baseFeeFileProject[0].qfCode;
        projectModel.qfPartMajorType = baseFeeFileProject[0].qfCode;
        qfMajorTypeMoneyMap.set(baseFeeFileProject[0].qfCode, 0);
        projectModel.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
        // 初始化取费表
        await this.initUnitProjectFreeRates(projectModel, constructId);
        // 初始化费用代码
        await this.initUnitCostCodePrice(projectModel, constructId);
        // 初始化费用汇总
        await this.initUnitCostSummary(projectModel, constructId);
        // // 初始化费用汇总合计
        // await this.initUnitCostSummaryTotal(projectModel, constructId);
        // 初始化工程量明细
        await this.initQuantities(projectModel, constructId);
        // 初始化标准换算
        this.initDeConversion(projectModel, constructId);
        // 初始化单位基本信息
        await this.initOverView(projectModel, constructId);
        // // 初始化表格列信息
        // this.initTableColumn(projectModel, constructId);
        // 初始化定额说明信息
        this.initDeContent(projectModel, constructId);

        //初始化 主要材料 设置
        await this.initMainMaterialSetting(projectModel, constructId);

        //初始化独立费数据
        await this.initIndependentCostsData(projectModel, constructId);

        // 初始化水电费计算
        await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.initWaterElectricCostData({
            constructId: constructId,
            singleId: projectModel.parentId,
            unitId: projectModel.sequenceNbr
        });

    }


    /**
     * 初始化独立费数据
     */
    async initIndependentCostsData(unit, constructId) {
        let functionMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY);
        if (ObjectUtils.isEmpty(functionMap)) {
            functionMap = new Map();
        }
        let list = functionMap.get(this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unit.sequenceNbr));
        if (ObjectUtils.isEmpty(list)) {
            let args = {};
            args.constructId = constructId;
            args.unitId = unit.sequenceNbr;
            list = await this.service.gongLiaoJiProject.gljIndependentCostsService.initData(args);
            functionMap.set(this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unit.sequenceNbr), list);
            // await this.service.gongLiaoJiProject.gljIndependentCostsService.initChildData(args, list);
        }
    }


    /**
     * 主要材料 设置
     */
    async initMainMaterialSetting(unit, constructId) {

        let mainMaterialSetting = new MainMaterialSetting();
        //设置方式  0 自动设置
        mainMaterialSetting.type = 0;

        //自动设置 方式
        mainMaterialSetting.pattern = 1;

        //数量
        mainMaterialSetting.proportion = 50;

        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let objMap = businessMap.get(FunctionTypeConstants.MAIN_MATERIAL_SETTING);
        if (ObjectUtils.isEmpty(objMap)) {
            objMap = new Map();
            objMap.set(unit.sequenceNbr, mainMaterialSetting);
            businessMap.set(FunctionTypeConstants.MAIN_MATERIAL_SETTING, objMap);
        } else {
            objMap.set(unit.sequenceNbr, mainMaterialSetting);
        }
    }


    /**
     * 移除单位工程
     * @param pid
     * @param constructId
     */
    async remove(pid, constructId, singleId) {
        //删除 取费表 UNIT_QFB、PROJECT_QFB
        await this.removeQfb(pid, constructId, singleId);
        //删除 工程量 UNIT_QUANTITIES
        await this.removeQuantities(pid, constructId);
        //删除 标准换算 UNIT_CONVERSION
        await this.removeConversion(pid, constructId);
        //删除 费用查看 PROJECT_FYCK、SINGLE_FYCK、UNIT_FYCK
        await this.removeFyck(pid, constructId);
        //删除 费用汇总 UNIT_COST_SUMMARY
        await this.removeCostSummary(pid, constructId);
        //删除 费用代码 UNIT_COST_CODE
        await this.removeCostCode(pid, constructId);
        // 删除 费用汇总合计 UNIT_COST_SUMMARY_TOTAL
        // await this.removeCostSummaryTotal(pid, constructId);
        //删除 工程基本信息 JBXX_KEY
        await this.removeJbxxKey(pid, constructId);
        //删除 单位独立费 UNIT_DLF_KEY
        await this.removeUnitDlfKey(pid, constructId);
        //删除 用户人材机 PROJECT_USER_RCJ
        await this.removeUserRcj(pid, constructId);
        //删除 人材机汇总 RCJ_COLLECT
        await this.removeRcjCollect(pid, constructId);
        //删除 说明信息 UNIT_DE_CONTENT
        await this.removeDeContent(pid, constructId);

        //删除 定额
        await this.removeDe(pid, constructId);
        //删除 页签缓存
        await this.removeTableSetting(pid, constructId);
        //删除 定额操作缓存
        await this.removeDeSetting(pid, constructId);
    }

    /**
     * 初始化标准换算
     * @param projectModel
     * @param constructId
     * @returns {Promise<void>}
     */
    async initDeConversion(projectModel, constructId) {
        if (ObjectUtils.isEmpty(projectModel)) {
            return;
        }
        let conversionMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        conversionMap?.set(projectModel.sequenceNbr, new Map());
    }


    /**
     * 初始化工程量明细
     * @param projectModel
     * @param constructId
     * @returns {Promise<void>}
     */
    async initQuantities(projectModel, constructId) {
        if (ObjectUtils.isEmpty(projectModel)) {
            return;
        }
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        quantitiesMap.set(projectModel.sequenceNbr, new Map());
    }


    /**
     * 初始化定额说明
     * @param projectModel
     * @param constructId
     * @returns {Promise<void>}
     */
    async initDeContent(projectModel, constructId) {
        if (ObjectUtils.isEmpty(projectModel)) {
            return;
        }
        let contentMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DE_CONTENT);
        contentMap.set(projectModel.sequenceNbr, new Map());
    }

    /**
     * 初始化基本信息
     */
    async initOverView(projectModel, constructId) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        if (ObjectUtils.isEmpty(projectDomain)) {
            return;
        }
        await this.service.gongLiaoJiProject.gljOverviewService.initData({
            constructId: constructId,
            unitId: projectModel.sequenceNbr, type: FunctionTypeConstants.JBXX_KEY_TYPE_11
        });
        await this.service.gongLiaoJiProject.gljOverviewService.initData({
            constructId: constructId,
            unitId: projectModel.sequenceNbr, type: FunctionTypeConstants.JBXX_KEY_TYPE_13
        });
    }

    /**
     * 初始化单位工程的费率
     * @param projectModel
     * @param constructId
     */
    async initUnitProjectFreeRates(projectModel, constructId) {
        if (ObjectUtils.isEmpty(projectModel)) {
            return;
        }
        let libraryCode = projectModel.constructMajorType
        if (ObjectUtils.isEmpty(libraryCode)) {
            return;
        }
        let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        //需要判断父级是不是单项
        let singleId = projectModel.parentId;
        let singleProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);

        let deLibrary = await this.service.gongLiaoJiProject.gljBaseDeLibraryService.getByLibraryCode(libraryCode);
        let sfreeRateProjectModelMap = null;
        let param = null;
        if (singleProject.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
            let sfreeKey = WildcardMap.generateKey(projectModel.parentId, FunctionTypeConstants.SINGLE_FLSM);
            sfreeRateProjectModelMap = singleQfbMap.get(sfreeKey);
            param = {
                libraryCode: deLibrary.libraryCode,
                constructId: constructId,
                singleId: projectModel.parentId,
                unitId: projectModel.sequenceNbr,
                projectType: sfreeRateProjectModelMap.projectType,
                projectLocation: sfreeRateProjectModelMap.projectLocation,
                roadSurfaceNum: sfreeRateProjectModelMap.roadSurfaceNum,
                floorSpace: sfreeRateProjectModelMap.floorSpace,
                municipalConstructionCost: sfreeRateProjectModelMap.municipalConstructionCost,
                taxCalculationMethod: taxCalculationMethod,
            };
        } else if (singleProject.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            sfreeRateProjectModelMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FLSM);
            param = {
                libraryCode: deLibrary.libraryCode,
                constructId: constructId,
                singleId: projectModel.parentId,
                unitId: projectModel.sequenceNbr,
                projectType: sfreeRateProjectModelMap.projectType,
                projectLocation: sfreeRateProjectModelMap.projectLocation,
                roadSurfaceNum: sfreeRateProjectModelMap.roadSurfaceNum,
                floorSpace: sfreeRateProjectModelMap.floorSpace,
                municipalConstructionCost: sfreeRateProjectModelMap.municipalConstructionCost,
                taxCalculationMethod: taxCalculationMethod,
            };
        }


        // 如果工程项目下存在该取费专业的费率，则直接同步数据
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        if (ObjectUtils.isEmpty(freeRateProjectModel)) {
            return;
        }
        if (ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate)) {
            freeRateProjectModel.childFreeRate = new Map();
        }
        let freeRate = ConvertUtil.deepCopy(freeRateProjectModel.childFreeRate.get(libraryCode));
        // let flsm = await this.service.gongLiaoJiProject.gljFreeRateService.initFreeDescribe();
        let flsm = ObjectUtils.cloneDeep(sfreeRateProjectModelMap);
        if (ObjectUtils.isNotEmpty(freeRateProjectModel) && ObjectUtils.isEmpty(freeRate)) {
            if (ObjectUtils.isEmpty(freeRate)) {
                //获取工程项目的费率说明
                param.projectType = flsm.projectType;
                param.projectLocation = flsm.projectLocation;
                param.roadSurfaceNum = flsm.roadSurfaceNum;
                param.floorSpace = flsm.floorSpace;
                param.municipalConstructionCost = flsm.municipalConstructionCost;
                param.precision = precision;

                //分别查询管理费利润、税金、安文费
                // freeRate = await this.service.gongLiaoJiProject.gljFreeRateService.getBaseFreeRate(param);
                freeRate = await this.getFeeData(param);
            }
        } else {
            freeRate.unitId = param.unitId;
        }

        //初始化费率说明数据  改为同步单项工程的费率说明


        let freeRateProjectModelMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM);

        flsm.constructId = constructId;
        flsm.singleId = projectModel.parentId;
        flsm.unitId = param.unitId;
        flsm.sequenceNbr=Snowflake.nextId();
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_FLSM, freeRateProjectModelMap.set(param.unitId, flsm));

        return;  // 只创建单位工程费率说明信息

        // 同步至单项取费率
        if (singleProject.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeKeySingle = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle)) ? singleQfbMap.get(freeKeySingle) : {"childFreeRate": new Map()};
            if (ObjectUtils.isEmpty(freeRateSingleModel.childFreeRate.get(freeRate.qfCode))) {
                let s = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
                let sKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_FLSM);
                let m = s.get(sKey);
                let args = {
                    libraryCode: freeRate.libraryCode,
                    projectType: m.projectType,
                    projectLocation: m.projectLocation,
                    roadSurfaceNum: m.roadSurfaceNum,
                    floorSpace: m.floorSpace,
                    municipalConstructionCost: m.municipalConstructionCost,
                    taxCalculationMethod: taxCalculationMethod,
                    precision: precision,
                };
                //分别查询管理费利润、税金、安文费
                let freeRateModelSingle = await this.getFeeData1(args);
                freeRateSingleModel.childFreeRate.set(freeRate.qfCode, freeRateModelSingle);
                ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

                //多层级单项要处理不同层级单项数据
                await this.dealLevelSingleFee(constructId, singleId, libraryCode, taxCalculationMethod);
            } else {
                //如果单项中有该取费文件使用单项中的取费文件数据
                let v1 = freeRateSingleModel.childFreeRate.get(freeRate.qfCode);
                freeRate=ObjectUtils.cloneDeep(v1);
                freeRate.constructId = constructId;
                freeRate.singleId =singleId;
                freeRate.unitId = param.unitId;
                freeRate.sequenceNbr=Snowflake.nextId();
            }
        }
        // 初始化单位工程取费率
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let freeKey = WildcardMap.generateKey(param.unitId, freeRate.qfCode);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRate));

        // 同步至工程项目取费率
        if (ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate.get(freeRate.qfCode))) {
            //获取工程项目的费率说明
            let v = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FLSM);
            let args = {
                libraryCode: freeRate.libraryCode,
                projectType: v.projectType,
                projectLocation: v.projectLocation,
                roadSurfaceNum: v.roadSurfaceNum,
                floorSpace: v.floorSpace,
                municipalConstructionCost: v.municipalConstructionCost,
                taxCalculationMethod: taxCalculationMethod,
                precision: precision,
            };
            //分别查询管理费利润、税金、安文费
            let freeRateModel = await this.getFeeData1(args);
            freeRateProjectModel.childFreeRate.set(freeRate.qfCode, freeRateModel);
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        }


    }

    /**
     * 处理单项层级取费费率
     * @param constructId
     * @param singleId
     * @param libraryCode
     * @param taxCalculationMethod
     * @returns {Promise<void>}
     */
    async dealLevelSingleFee(constructId, singleId, libraryCode, taxCalculationMethod) {
        let singleProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        if (ObjectUtils.isNotEmpty(singleProject) && singleProject.type !== ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            // 同步至单项取费率
            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeKeySingle = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
            // let freeRateSingleModel = singleQfbMap.get(freeKeySingle);
            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle)) ? singleQfbMap.get(freeKeySingle) : {"childFreeRate": {}};
            let s = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
            let sKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_FLSM);
            let m = s.get(sKey);
            let args = {
                libraryCode: libraryCode,
                projectType: m.projectType,
                projectLocation: m.projectLocation,
                roadSurfaceNum: m.roadSurfaceNum,
                floorSpace: m.floorSpace,
                municipalConstructionCost: m.municipalConstructionCost,
                taxCalculationMethod: taxCalculationMethod,
                precision: precision,
            };
            //分别查询管理费利润、税金、安文费
            let freeRateModelSingle = await this.getFeeData1(args);
            freeRateSingleModel.childFreeRate.set(freeRateModelSingle.qfCode, freeRateModelSingle);
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

            if (ObjectUtils.isNotEmpty(singleProject.parentId)) {
                await this.dealLevelSingleFee(constructId, singleProject.parentId, libraryCode, taxCalculationMethod);
            }
        }
    }


    async getFeeData(param) {
        let freeRate = new FreeRateModel();
        freeRate.libraryCode = param.libraryCode;
        freeRate.init();
        freeRate.constructId = param.constructId;
        freeRate.unitId = param.unitId;
        freeRate.sequenceNbr = Snowflake.nextId();
        freeRate.projectType = param.projectType;
        freeRate.projectLocation = param.projectLocation;
        freeRate.roadSurfaceNum = param.roadSurfaceNum;
        freeRate.floorSpace = param.floorSpace;
        freeRate.municipalConstructionCost = param.municipalConstructionCost;

        param.taxCalculationMethod = ProjectDomain.getDomain(param.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

        //查找管理费和利润
        freeRate = await this.setManageFeeRateProfitRate(param, freeRate);
        //查找税金
        freeRate = await this.setTaxRate(param, freeRate);
        //查找安文费
        freeRate = await this.setAnwenRate(param, freeRate);
        let baseFeeFile = await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFile(freeRate.qfCode);
        freeRate.freeProfession = baseFeeFile.qfName;
        freeRate.sortNo = baseFeeFile.sortNo;
        freeRate.diffFreeRate = [];
        return freeRate;
    }

    async getFeeData1(param) {
        let freeRate = new FreeRateModel();
        freeRate.libraryCode = param.libraryCode;
        freeRate.projectType = param.projectType;
        freeRate.projectLocation = param.projectLocation;
        freeRate.roadSurfaceNum = param.roadSurfaceNum;
        freeRate.floorSpace = param.floorSpace;
        freeRate.municipalConstructionCost = param.municipalConstructionCost;
        freeRate.sequenceNbr = Snowflake.nextId();

        //查找管理费和利润
        freeRate = await this.setManageFeeRateProfitRate(param, freeRate);
        //查找税金
        freeRate = await this.setTaxRate(param, freeRate);
        //查找安文费
        freeRate = await this.setAnwenRate(param, freeRate);
        let baseFeeFile = await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFile(freeRate.qfCode);
        freeRate.freeProfession = baseFeeFile.qfName;
        freeRate.sortNo = baseFeeFile.sortNo;
        freeRate.diffFreeRate = [];
        return freeRate;
    }


    async setManageFeeRateProfitRate(param, freeRate) {
        let manageFeeRate = "";
        let profitRate = "";

        if (ObjectUtils.isNotEmpty(param.libraryCode)) {
            //工程类别查询管理费和利润
            let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByLibraryCode(param.libraryCode);
            if (ObjectUtils.isNotEmpty(param.projectType)) {
                if (param.projectType === "一类工程") {
                    manageFeeRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.managementFee1Ybjs : gsBaseFreeRate.managementFee1Jyjs;
                    profitRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.profit1Ybjs : gsBaseFreeRate.profit1Jyjs;
                } else if (param.projectType === "二类工程") {
                    manageFeeRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.managementFee2Ybjs : gsBaseFreeRate.managementFee2Jyjs;
                    profitRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.profit2Ybjs : gsBaseFreeRate.profit2Jyjs;
                } else if (param.projectType === "三类工程") {
                    manageFeeRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.managementFee3Ybjs : gsBaseFreeRate.managementFee3Jyjs;
                    profitRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.profit3Ybjs : gsBaseFreeRate.profit3Jyjs;
                }
            }
            freeRate.qfCode = gsBaseFreeRate.qfCode;
        }

        freeRate.manageFeeRate = NumberUtil.numberScale(manageFeeRate, param.precision.manageFeeRate);
        freeRate.profitRate = NumberUtil.numberScale(profitRate, param.precision.profitRate);
        freeRate.manageFeeRateUpdate = false;
        freeRate.profitRateUpdate = false;

        return freeRate;
    }

    async setManageFeeRateProfitRateByQfCode(param, freeRate) {
        let manageFeeRate = "";
        let profitRate = "";

        if (ObjectUtils.isNotEmpty(param.qfCode)) {
            //工程类别查询管理费和利润
            let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByQfCode(param.qfCode);
            if (ObjectUtils.isNotEmpty(param.projectType)) {
                if (param.projectType === "一类工程") {
                    manageFeeRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.managementFee1Ybjs : gsBaseFreeRate.managementFee1Jyjs;
                    profitRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.profit1Ybjs : gsBaseFreeRate.profit1Jyjs;
                } else if (param.projectType === "二类工程") {
                    manageFeeRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.managementFee2Ybjs : gsBaseFreeRate.managementFee2Jyjs;
                    profitRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.profit2Ybjs : gsBaseFreeRate.profit2Jyjs;
                } else if (param.projectType === "三类工程") {
                    manageFeeRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.managementFee3Ybjs : gsBaseFreeRate.managementFee3Jyjs;
                    profitRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.profit3Ybjs : gsBaseFreeRate.profit3Jyjs;
                }
            }
            freeRate.qfCode = gsBaseFreeRate.qfCode;
        }

        freeRate.manageFeeRate = parseFloat(manageFeeRate);
        freeRate.profitRate = parseFloat(profitRate);

        return freeRate;
    }

    /**
     * 查詢費率信息
     * @param param
     * @returns {Promise<*[]>}
     */
    async setManageFeeRateProfitRateAll(param) {
        let manageFeeRate = "";
        let profitRate = "";
        let freeRateList = [];
        // if (ObjectUtils.isNotEmpty(param.libraryCode)) {
        //     //工程类别查询管理费和利润
        //     let glfBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByLibraryCode(param.libraryCode);
        //     manageFeeRate = param.taxCalculationMethod == 1 ? glfBaseFreeRate.managementFee1Ybjs : glfBaseFreeRate.managementFee1Jyjs;
        //     profitRate = param.taxCalculationMethod == 1 ? glfBaseFreeRate.profit1Ybjs : glfBaseFreeRate.profit1Jyjs;
        //     let freeRate = {
        //         dispNo: 1,
        //         name: "一类工程",
        //         description: ""
        //     };
        //     if (param.freeRateType == FreeRateType.MANAGE_FEE_RATE) {
        //         freeRate.freeRate = NumberUtil.numberScale2(parseFloat(manageFeeRate));
        //     } else {
        //         freeRate.freeRate = NumberUtil.numberScale2(parseFloat(profitRate));
        //     }
        //     freeRateList.push(freeRate);
        //
        //     manageFeeRate = param.taxCalculationMethod == 1 ? glfBaseFreeRate.managementFee2Ybjs : glfBaseFreeRate.managementFee2Jyjs;
        //     profitRate = param.taxCalculationMethod == 1 ? glfBaseFreeRate.profit2Ybjs : glfBaseFreeRate.profit2Jyjs;
        //     freeRate = {
        //         dispNo: 2,
        //         name: "二类工程",
        //         description: ""
        //     };
        //     if (param.freeRateType == FreeRateType.MANAGE_FEE_RATE) {
        //         freeRate.freeRate = NumberUtil.numberScale2(parseFloat(manageFeeRate));
        //     } else {
        //         freeRate.freeRate = NumberUtil.numberScale2(parseFloat(profitRate));
        //     }
        //     freeRateList.push(freeRate);
        //
        //
        //     manageFeeRate = param.taxCalculationMethod == 1 ? glfBaseFreeRate.managementFee3Ybjs : glfBaseFreeRate.managementFee3Jyjs;
        //     profitRate = param.taxCalculationMethod == 1 ? glfBaseFreeRate.profit3Ybjs : glfBaseFreeRate.profit3Jyjs;
        //     freeRate = {
        //         dispNo: 3,
        //         name: "三类工程",
        //         description: ""
        //     };
        //     if (param.freeRateType == FreeRateType.MANAGE_FEE_RATE) {
        //         freeRate.freeRate = NumberUtil.numberScale2(parseFloat(manageFeeRate));
        //     } else {
        //         freeRate.freeRate = NumberUtil.numberScale2(parseFloat(profitRate));
        //     }
        //     freeRateList.push(freeRate);
        //
        // }


        if (ObjectUtils.isNotEmpty(param.libraryCode)) {
            //工程类别查询管理费和利润
            let glfBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByLibraryCodeAll(param.libraryCode);
            if (ObjectUtils.isNotEmpty(glfBaseFreeRate)) {
                // 获取取费专业
                let qfList = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();
                let qfCodeNameMap = qfList.reduce((acc, item) => {
                    acc.set(item.qfCode, item.qfName);
                    return acc;
                }, new Map());

                let freeRate;
                for (let i = 0; i < glfBaseFreeRate.length; i++) {
                    let baseFreeRate = glfBaseFreeRate[i];
                    // 添加取费专业名称
                    if (qfCodeNameMap.has(baseFreeRate.qfCode)) {
                        baseFreeRate.name = qfCodeNameMap.get(baseFreeRate.qfCode);
                    }
                    if (param.freeRateType === FreeRateType.MANAGE_FEE_RATE) {
                        if (param.taxCalculationMethod == 1) {
                            freeRate = {
                                dispNo: i + 1,
                                name: baseFreeRate.name,
                                freeRate1: NumberUtil.numberScale(baseFreeRate.managementFee1Ybjs, param.precision.manageFeeRate),
                                freeRate2: NumberUtil.numberScale(baseFreeRate.managementFee2Ybjs, param.precision.manageFeeRate),
                                freeRate3: NumberUtil.numberScale(baseFreeRate.managementFee3Ybjs, param.precision.manageFeeRate),
                                description: ""
                            };
                        } else {
                            freeRate = {
                                dispNo: i + 1,
                                name: baseFreeRate.name,
                                freeRate1: NumberUtil.numberScale(baseFreeRate.managementFee1Jyjs, param.precision.manageFeeRate),
                                freeRate2: NumberUtil.numberScale(baseFreeRate.managementFee2Jyjs, param.precision.manageFeeRate),
                                freeRate3: NumberUtil.numberScale(baseFreeRate.managementFee3Jyjs, param.precision.manageFeeRate),
                                description: ""
                            };
                        }
                        freeRateList.push(freeRate);
                    }
                    if (param.freeRateType === FreeRateType.PROFIT_RATE) {
                        let profitRate = param.taxCalculationMethod == 1 ? baseFreeRate.profit1Ybjs : baseFreeRate.profit1Jyjs;
                        freeRate = {
                            dispNo: i + 1,
                            name: baseFreeRate.name,
                            freeRate: NumberUtil.numberScale(profitRate, param.precision.profitRate),
                            description: ""
                        };
                        freeRateList.push(freeRate);
                    }
                }
            }
        }
        return freeRateList;
    }


    async setTaxRate(param, freeRate) {
        let taxRate = "";
        if (ObjectUtils.isNotEmpty(param.libraryCode)) {
            let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseGsjRateService.queryByKindAndMethodAndRegion(GsjRateKindEnum.SJ.code, param.taxCalculationMethod, param.projectLocation, true);
            taxRate = gsBaseFreeRate.rate;
        }
        freeRate.taxRate = NumberUtil.numberScale(taxRate, param.precision.taxRate);
        freeRate.taxRateUpdate = false;
        return freeRate;
    }

    async setAnwenRate(param, freeRate) {
        let anwenRate = "";
        if (ObjectUtils.isNotEmpty(param.libraryCode)) {
            //工程类别查询管理费和利润
            let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseAnwenRateService.queryByLibraryCodeGlj(param.libraryCode, freeRate.projectLocation, freeRate.roadSurfaceNum, freeRate.floorSpace, freeRate.municipalConstructionCost);
            if (param.libraryCode === ConstantUtil.SZSSWXYHGC) {
                //市政设施维修养护工程 特殊处理，增加qfCode查询条件
                gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseAnwenRateService.queryByLibraryCodeGlj1(param.libraryCode, freeRate.qfCode, freeRate.projectLocation, freeRate.roadSurfaceNum, freeRate.floorSpace, freeRate.municipalConstructionCost);
            }
            anwenRate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.anwenRateYbjs : gsBaseFreeRate.anwenRateJyjs;
        }
        freeRate.anwenRate = NumberUtil.numberScale(anwenRate, param.precision.anwenRate);
        freeRate.anwenRateUpdate = false;
        return freeRate;
    }


    /**
     * 移除 工程量明细
     * @param pid
     * @param constructId
     */
    async removeQuantities(pid, constructId) {
        // 获取工程量明细
        let quantitiesMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        if (ObjectUtils.isEmpty(quantitiesMap)) {
            return
        }
        for (const [key, value] of quantitiesMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                quantitiesMap.delete(key);
            }
        }
    }


    /**
     * 移除 定额信息说明
     * @param pid
     * @param constructId
     */
    async removeDeContent(pid, constructId) {
        // 获取定额信息说明
        let deContentMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DE_CONTENT);
        if (ObjectUtils.isEmpty(deContentMap)) {
            return
        }
        for (const [key, value] of deContentMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                deContentMap.delete(key);
            }
        }
    }

    /**
     * 移除 标准换算
     * @param pid
     * @param constructId
     */
    async removeConversion(pid, constructId) {
        // 获取标准换算
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        if (ObjectUtils.isEmpty(conversionMap)) {
            return
        }
        for (const [key, value] of conversionMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                conversionMap.delete(key);
            }
        }
    }

    /**
     * 移除 费用查看
     * @param pid
     * @param constructId
     */
    async removeFyck(pid, constructId) {
        // 费用查看
        let unitFyck = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FYCK);
        if (ObjectUtils.isNotEmpty(unitFyck)) {
            for (const [key, value] of unitFyck) {
                if (ObjectUtils.isNotEmpty(key) && key.endsWith(pid)) {
                    unitFyck.delete(key);
                }
            }
        }
    }


    /**
     * 移除 费用汇总
     * @param pid
     * @param constructId
     */
    async removeCostSummary(pid, constructId) {
        // 获取费用汇总
        let unitCostCodeSummarys = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
        if (ObjectUtils.isEmpty(unitCostCodeSummarys)) {
            return
        }
        for (const [key, value] of unitCostCodeSummarys) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                unitCostCodeSummarys.delete(key);
            }
        }
    }

    /**
     * 移除 费用代码
     * @param pid
     * @param constructId
     */
    async removeCostCode(pid, constructId) {
        // 获取费用代码
        let unitCostCode = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
        if (ObjectUtils.isEmpty(unitCostCode)) {
            return
        }
        for (const [key, value] of unitCostCode) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                unitCostCode.delete(key);
            }
        }
    }


    /**
     * 移除 工程基本信息 JBXX_KEY
     * @param pid
     * @param constructId
     */
    async removeJbxxKey(pid, constructId) {
        // 获取工程基本信息 JBXX_KEY
        let functionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY)
        if (ObjectUtils.isEmpty(functionMap)) {
            return
        }
        for (const [key, value] of functionMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith("JBXX-" + pid)) {
                functionMap.delete(key);
            }
        }
    }

    /**
     * 移除 单位独立费 UNIT_DLF
     *
     * @param pid
     * @param constructId
     */
    async removeUnitDlfKey(pid, constructId) {
        // 单位独立费 UNIT_DLF
        let functionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
        if (ObjectUtils.isEmpty(functionMap)) {
            return
        }
        for (const [key, value] of functionMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith("INDCOST-" + pid)) {
                functionMap.delete(key);
            }
        }
    }

    /**
     * 移除 用户人材机 PROJECT_USER_RCJ
     *
     * @param pid
     * @param constructId
     */
    async removeUserRcj(pid, constructId) {
        // 单位独立费 UNIT_DLF
        let functionDataMap = await ProjectDomain.getDomain(constructId).functionDataMap
        let rcjUserList = functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ)
        if (ObjectUtils.isEmpty(rcjUserList) || rcjUserList.size === 0) {
            return
        }
        functionDataMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, rcjUserList.filter(item => pid !== item.unitId))
    }

    /**
     * 移除 单人材机汇总 RCJ_COLLECT
     *
     * @param pid
     * @param constructId
     */
    async removeRcjCollect(pid, constructId) {
        // 颜色删除
        let functionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT)
        if (ObjectUtils.isEmpty(functionMap)) {
            return
        }
        for (let [key, value] of functionMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(FunctionTypeConstants.UNIT_COLOR)) {
                value?.delete(pid);
            }
        }
    }

    /**
     * 删除定额
     * @param pid
     * @param constructId
     * @returns {Promise<void>}
     */
    async removeDe(pid, constructId) {
        let deList = await ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === pid && item.parentId === 0)
        if (ObjectUtils.isEmpty(deList)) {
            return
        }
        for (let de of deList) {
            await ProjectDomain.getDomain(constructId).deDomain.removeDeRow(de.sequenceNbr);
        }

        let csxmList = await ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === pid && item.parentId === 0)
        if (ObjectUtils.isEmpty(csxmList)) {
            return
        }
        for (let de of csxmList) {
            await ProjectDomain.getDomain(constructId).csxmDomain.removeDeRow(de.sequenceNbr);
        }
    }

    /**
     * 移除 页签缓存
     * @param pid
     * @param constructId
     * @returns {Promise<void>}
     */
    async removeTableSetting(pid, constructId) {
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let tableSettingObj = businessMap.get(FunctionTypeConstants.TABLE_SETTING_CACHE);
        if (ObjectUtils.isNotEmpty(tableSettingObj)) {
            delete tableSettingObj[pid];
        }
    }

    /**
     * 移除 定额操作缓存
     * @param pid
     * @param constructId
     * @returns {Promise<void>}
     */
    async removeDeSetting(pid, constructId) {
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let deSettingObj = businessMap.get(FunctionTypeConstants.DE_SETTING_CACHE);
        if (ObjectUtils.isNotEmpty(deSettingObj)) {
            delete deSettingObj[pid];
        }
    }

    /**
     * 移除 取费表
     * @param pid
     * @param constructId
     * @param singleId
     */
    async removeQfb(pid, constructId, singleId) {
        //删除单位工程取费表
        let unitQfbMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isEmpty(unitQfbMap)) {
            return
        }

        for (const [key, value] of unitQfbMap) {
            if (ObjectUtils.isNotEmpty(key) && key.startsWith(pid)) {
                unitQfbMap.delete(key);
            }
        }
        //删除工程项目取费表
        let result = new Set();
        for (const str of unitQfbMap.keys()) {
            const [key, value] = str.split("--");
            if (value) {
                result.add(value.trim());
            }
        }
        let unitTypes = Array.from(result);
        let projectQfbMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        const projectTypes = [];
        for (const key of projectQfbMap.childFreeRate.keys()) {
            projectTypes.push(key);
        }
        let differentTypes = projectTypes.filter(item => !unitTypes.includes(item));
        for (let differentType of differentTypes) {
            projectQfbMap.childFreeRate.delete(differentType);
        }

        //多层级单项要处理不同层级单项数据
        await this.dealLevelSingleFeeChild(constructId, singleId, unitTypes);
    }


    async dealLevelSingleFeeChild(constructId, singleId, unitTypes) {
        let singleProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);
        if (ObjectUtils.isNotEmpty(singleProject) && singleProject.type !== ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            //删除单项工程取费表
            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
            let projectQfbMapSingle = singleQfbMap.get(freeKey);
            const projectTypesSingle = [];
            for (const key of projectQfbMapSingle.childFreeRate.keys()) {
                projectTypesSingle.push(key);
            }
            let differentTypesSingle = projectTypesSingle.filter(item => !unitTypes.includes(item));
            for (let differentType of differentTypesSingle) {
                projectQfbMapSingle.childFreeRate.delete(differentType);
            }

            if (ObjectUtils.isNotEmpty(singleProject.parentId)) {
                await this.dealLevelSingleFeeChild(constructId, singleProject.parentId, unitTypes);
            }
        }
    }


    /**
     * 初始化费用代码
     * @param projectModel
     * @param constructId
     */
    async initUnitCostCodePrice(projectModel, constructId) {

        let param = {
            constructId: constructId,
            singleId: projectModel.parentId,
            unitId: projectModel.sequenceNbr,
            qfMajorType: "TOTAL"
        };
        let unitCostCodePrices = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.defaultUnitCostCodePrice(param);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
            .set(param.unitId + FunctionTypeConstants.SEPARATOR + "TOTAL", unitCostCodePrices);
    }

    /**
     * 初始化费用汇总
     * @param projectModel
     * @param constructId
     */
    async initUnitCostSummary(projectModel, constructId) {

        let param = {
            constructId: constructId,
            singleId: projectModel.parentId,
            unitId: projectModel.sequenceNbr,
            qfMajorType: "TOTAL"
        };
        let unitCostSummarys = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary(param);
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(param.unitId + FunctionTypeConstants.SEPARATOR + "TOTAL", unitCostSummarys);
    }


    /**
     * 初始化表格列
     * @param projectModel
     * @param constructId
     */
    initTableColumn(projectModel, constructId) {
        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // 如果表格列是全局配置
        if (!constructProject.isDefault) {
            let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;

            // 获取该单项下的所有单位
            let unitProjectsByConstruct = new Array();
            if (ObjectUtils.isNotEmpty(constructProject.children)) {
                PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
            }

            // 获取曾经全局设置的单位的id
            let unitProject = unitProjectsByConstruct[0];
            let unitId1 = unitProject.sequenceNbr;

            //设置预算书表头
            if (ObjectUtils.isNotEmpty(constructProject.businessIdArray)) {
                if (constructProject.businessIdArray.includes(BusinessConstants.UNIT_YSH_ID)) {
                    let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
                    if (ObjectUtils.isEmpty(objMap)) {
                        objMap = new Map();
                        businessMap.set(FunctionTypeConstants.YSH_TABLELIST, objMap);
                    }
                    let header = objMap.get(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId1);
                    if (ObjectUtils.isNotEmpty(header)) {
                        objMap.set(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + projectModel.sequenceNbr, header);
                    }
                }
                // 单位工程-独立费
                if (constructProject.businessIdArray.includes(BusinessConstants.UNIT_DLF_ID)) {
                    let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
                    if (ObjectUtils.isEmpty(objMap)) {
                        objMap = new Map();
                        businessMap.set(FunctionTypeConstants.DLF_TABLELIST, objMap);
                    }
                    let header = objMap.get(unitId1);
                    if (ObjectUtils.isNotEmpty(header)) {
                        objMap.set(projectModel.sequenceNbr, header);
                    }
                }
            }
        }
    }
}

GljInitUnitProjectService.toString = () => '[class GljInitUnitProjectService]';
module.exports = GljInitUnitProjectService;
