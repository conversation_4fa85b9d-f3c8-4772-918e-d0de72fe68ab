const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const DeBaseDomain = require("../domains/DeBaseDomain");
const DeTypeConstants = require("../constants/DeTypeConstants")
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { Snowflake } = require('../utils/Snowflake');
const StandardDeModel = require('../domains/deProcessor/models/StandardDeModel');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const _ = require("lodash");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectLevelConstant = require("../constants/ProjectLevelConstant");
const {ConvertUtil} = require("../utils/ConvertUtils");
const DeCommonConstants = require("../constants/DeCommonConstants");
const DeQualityUtils = require("../domains/utils/DeQualityUtils");
const WildcardMap = require('../core/container/WildcardMap');
const DeUtils = require('../domains/utils/DeUtils');

/**
 * 措施项目controller
 */
class GljStepItemCostController extends Controller {


    /**
     * 获取措施模板
     * @param args
     * @returns {*}
     */
    async getMeasureTemplates(args){
        return ResponseData.success(await this.service.gongLiaoJiProject.gljStepItemCostService.getMeasureTemplates(args));
    }

    /**
     * 通过措施模板名称获得清单列表
     * @param args
     * @returns {*}
     */
    async getBaseListByTemplate(args){
        return ResponseData.success(await this.service.gongLiaoJiProject.gljStepItemCostService.getBaseListByTemplate(args));
    }

    /**
     * 应用措施模板
     * @param args
     * @returns {*}
     */
    async applyMeasureTemplate(args){
        let {constructId, singleId, unitId,templateName} = args;
        return ResponseData.success( await this.service.gongLiaoJiProject.gljStepItemCostService.initItemCost(constructId, singleId, unitId,templateName));
    }



    /**
     * 保存措施模板
     * @param args
     * @returns {*}
     */
    saveMeasureTemplate(args) {
        return ResponseData.success(this.service.gongLiaoJiProject.gljStepItemCostService.saveMeasureTemplate(args));
    }

    /**
     * 获取措施项目类型列表
     * @returns {ResponseData}
     */
    getMeasureTypes() {
        let returnArry = ["安全文明施工", "脚手架", "空"];
        return ResponseData.success(returnArry);
    }

    /**
     * 初始化
     * @param args
     * @returns {ResponseData}
     */
    async initStepItemCost(args) {
        let {constructId, singleId, unitId} = args;
        await this.service.gongLiaoJiProject.gljStepItemCostService.initItemCost(constructId, singleId, unitId);
        return ResponseData.success(true);
    }

    /**
     * 复制行
     * 复制前端来处理
     * @param args
     * @returns {ResponseData}
     */
    copy(args) {
        let {sequenceNbrs} = args;
        if(!_.isArray(sequenceNbrs)){
            return ResponseData.fail("参数异常sequenceNbrs 应为数组");
        }
        // const result = this.service.gongLiaoJiProject.gljStepItemCostService.copyLine(sequenceNbrs);
        return ResponseData.success();
    }

    async pasteLine(args){
        let {constructId, singleId, unitId,pointLine} = args;
        if(_.isEmpty(pointLine)){
            return ResponseData.fail("请选择要粘贴的行");
        }
        const result = await this.service.gongLiaoJiProject.gljStepItemCostService.pasteLine(constructId, singleId, unitId,pointLine);

        // await this.service.management.sycnTrigger("unitDeChange");
        // await this.service.management.trigger("itemChange");
        return ResponseData.success(result);
    }

    async pasteDe(args){
        
        let {constructId,unitId,oUnitId,prevDeRowId,idList,type} = args;
        let prevDeRow;   
        if(ObjectUtil.isEmpty(idList)){
            return ResponseData.fail("参数错误");
        }
    
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        if(ObjectUtil.isNotEmpty(prevDeRowId))
        {
            prevDeRow = csxmDomain.getDeById(prevDeRowId);
        }
        if(ObjectUtil.isEmpty(prevDeRow)){
            return ResponseData.fail("未选择行");
        }
        let checkDeRows = [];
        let maxLevel = 0;
        for(let id of idList){
            let deRow = csxmDomain.getDeById(id);
            if(ObjectUtil.isNotEmpty(deRow)){
            let curLevel = this._calculateLevel(deRow,true,false);//本身检查分部层级
            maxLevel = maxLevel<curLevel?curLevel:maxLevel;
            checkDeRows.push(deRow.sequenceNbr);
            }
        }
        if(ObjectUtil.isEmpty(checkDeRows) || checkDeRows.length === 0){
            return ResponseData.fail("主材设备数据无法粘贴或原数据已删除");
        }
        //计算层级
        let allLevel = 0
        if(type === "child" || type === "childNoCircle"){
            allLevel = this._calculateLevel(prevDeRow) + maxLevel;
        }else{
            allLevel = this._calculateLevel(prevDeRow) + maxLevel -1;
        }
        if(allLevel>4){
            return ResponseData.fail("粘贴操作将导致分部的层级超过4级，请重新选择位置！");      
        }
        
        await csxmDomain.pasteDe(unitId, oUnitId, checkDeRows,prevDeRow,type, false);
        
        return ResponseData.success();
    }
    
    _calculateLevel(deRow,checkChild=false,checkParent = true){
    let count = 0;
    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.type)){
        count++
        if(checkChild){
        let max = 0;
        for(let child of deRow.children){
            let childLevel = this._calculateLevel(child,checkChild,false);
            max = max>childLevel?max:childLevel;
        }
        count += max;
        }
    }
    if(checkParent && ObjectUtil.isNotEmpty(deRow.parent)){
        count+=this._calculateLevel(deRow.parent);
    }
    return count;
    }

    searchForsequenceNbr(args) {
        let {constructId, singleId, unitId,sequenceNbr} = args;
        const result = this.service.gongLiaoJiProject.gljStepItemCostService.searchForsequenceNbr(constructId, singleId, unitId, sequenceNbr);
        return ResponseData.success(result);
    }
    /**
     * 批量删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDelete(args) {
        let {constructId, singleId, unitId, sequenceNbrs} = args;
        if(!_.isArray(sequenceNbrs)){
            return ResponseData.fail("参数异常sequenceNbrs 应为数组");
        }
        try {
            const result = await this.service.gongLiaoJiProject.gljStepItemCostService.batchDelete(constructId, singleId, unitId, sequenceNbrs);
            // await this.service.management.sycnTrigger("unitDeChange");
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail(e.message);
        }


    }
    /**
     * 批量删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteDeLine(args) {
        let {constructId, unitId, deRowIdList, isDelZmDe} = args;
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        for (let deRowId of deRowIdList) {
            let pointLine = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(deRowId)
            if (ObjectUtil.isEmpty(pointLine) || pointLine.type === DeTypeConstants.DE_TYPE_DEFAULT) {
                continue
            }
            let params = {
                constructId,
                singleId: unitProject.parentId,
                unitWorkId: unitId,
                pointLine: pointLine,
                isBlock: false,
                isRemoveRelationDe: true
            }
            this.remove(params)
        }
        return ResponseData.success(true);
    }

    /**
     * 批量删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteDes(args) {
        let {constructId, unitId, deRowIdList, isDelZmDe} = args;
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        for (let deRowId of deRowIdList) {
            let pointLine = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(deRowId)
            if (ObjectUtil.isEmpty(pointLine) || pointLine.type === DeTypeConstants.DE_TYPE_DEFAULT || pointLine.type === DeTypeConstants.DE_TYPE_FB
             || pointLine.type === DeTypeConstants.DE_TYPE_ZFB || pointLine.type === DeTypeConstants.DE_TYPE_DELIST) {
                continue
            }
            let params = {
                constructId,
                singleId: unitProject.parentId,
                unitWorkId: unitId,
                pointLine: pointLine,
                isBlock: false,
                isRemoveRelationDe: true
            }
            this.remove(params)
        }
        return ResponseData.success(true);
    }



    /**
     * 结构新增(空行数据)
     * @param args
     * @returns {ResponseData}
     */
    async save(args) {
        let {constructId, singleId, unitId, pointLine, newLine} = args;
        const result =  await this.service.gongLiaoJiProject.gljStepItemCostService.save(constructId, singleId, unitId, pointLine, newLine);
        //状态数据处理
        let allNodes = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId === unitId);
        this._setItemDtaStatus(allNodes);
        return ResponseData.success(result);
    }

     /**
     * 通过标准编码插入定额
     * pointLine 选中行信息 全量给回来，记得反序列化
     * code      用户输入的编码
     */
     async updateDeByCode(args) {
        let {constructId, singleId, unitId, pointLine, type, code} = args;
        //true 标记前端 传来修改
        if(ObjectUtil.isNotEmpty(code)){
            code = code.toUpperCase();
        }
        let res = await this.service.gongLiaoJiProject.gljStepItemCostService.updateDeByCode(constructId, singleId, unitId, pointLine, type, code);

        return ResponseData.success(res);
    }

    /**
     * 措施项目更新定额
     * @param args
     * @returns {ResponseData}
     */
    async updateDe(args) {
        let {deRow} = args;

        let csxmDomain = ProjectDomain.getDomain(deRow.constructId).csxmDomain;
        await csxmDomain.updateDe(deRow,true);
        //顶顶顶顶顶顶顶顶顶顶
        return ResponseData.success();
    }

    /**
     * 行内数据修改
     * @param args
     */
    async update(args) {
        let {constructId, singleId, unitWorkId, pointLineId,  column,value} = args;
        let upDateInfo={
            column,value
        }
        let projectDomain = ProjectDomain.getDomain(constructId);
        let csxmDomain = projectDomain.getCsxmDomain();
        let deRow = csxmDomain.getDeById(pointLineId);
        if(ObjectUtil.isEmpty(deRow)){
            return ResponseData.fail("定额不存在")
        }
        let addFunctionAvalible = false;
        //修改工程量验证编码方式是否存在
        if(upDateInfo.column === 'quantity' || upDateInfo.column === 'originalQuantity' || upDateInfo.column === 'quantityExpression'){
            let quantity = value;
            if(ObjectUtil.isNotEmpty(quantity)&& !ObjectUtil.isNumber(quantity)){
                quantity = quantity.toUpperCase();
                upDateInfo.value = quantity;
            }
            let priceCodes = await csxmDomain.getQuantityExpressionCodes(constructId,unitWorkId,projectDomain.functionDataMap);
            if(ObjectUtil.isEmpty(priceCodes)){
                priceCodes = [];
            }
            //增加定额费用代码
            await this.service.gongLiaoJiProject.gljDeService.addPriceCodes(constructId, unitWorkId, pointLineId, priceCodes);

            let tokens = DeQualityUtils.evalQualityTokens(quantity);

            let reduceCodes = DeUtils.reduceCostDe(deRow,priceCodes);//用于判断编码是否存在
            let tokenUnexist = false;
            let errorCodes = [];
            if(ObjectUtil.isNotEmpty(tokens) && ObjectUtil.isEmpty(reduceCodes)){
                tokenUnexist = true;
                errorCodes = tokens;
            }else if(ObjectUtil.isNotEmpty(tokens) && ObjectUtil.isNotEmpty(reduceCodes)){
                for(let token of tokens){
                    let priceCode = reduceCodes.find(item=>item.code == token);
                    if(ObjectUtil.isEmpty(priceCode)){
                        tokenUnexist = true;
                        errorCodes.push(token);
                    }
                    addFunctionAvalible = true;
                }
            }
            if(tokenUnexist){
                return ResponseData.fail('工程量表达式错误：费用代码【'+errorCodes.join(',')+'】不存在或循环引用');
            }            
        }
        
        let res = null;
        try{
            res = await this.service.gongLiaoJiProject.gljStepItemCostService.upDateOnList(constructId, singleId, unitWorkId, pointLineId, upDateInfo);
        }catch (error){
            return ResponseData.fail(error.message);
        }
        if(addFunctionAvalible){
            let deGclMap = projectDomain.functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
            if(ObjectUtil.isEmpty(deGclMap)){
                deGclMap = new Map();
                projectDomain.functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY,deGclMap);
            }
            let deId = pointLineId;
            let deRowBak = {constructId, unitId:unitWorkId, deRowId:deId};
            let deSet = deGclMap.get(unitWorkId);
            if(ObjectUtil.isEmpty(deSet)){
                deSet = [];
                deGclMap.set(unitWorkId,deSet);
            }
            let deExist = deSet.find(item=>item.unitId === unitWorkId && item.deRowId === deId);
            if(ObjectUtil.isNotEmpty(deExist)){
                let index = deSet.indexOf(deExist);
                deSet.splice(index,1);
            }
            deSet.push(deRowBak);
        }
        return ResponseData.success(res);
    }

    /**
     * 特征及项目编辑
     */
    updateQdFeature(args) {
        let {constructId, singleId, unitId, pointLine, updateStr} = args;
        this.service.gongLiaoJiProject.gljStepItemCostService.updateQdFeature(constructId, singleId, unitId, pointLine, updateStr);
    }

    /**
     * 从清单定额索引中点击插入
     */
    async fillFromIndexPage(args) {
        let {constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag,libraryCode} = args;
        let res = await this.service.gongLiaoJiProject.gljStepItemCostService.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag,null,null,null,libraryCode);
        //状态数据处理
        let allNodes = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId === unitId);
        this._setItemDtaStatus(allNodes);
        // await this.service.management.sycnTrigger("unitDeChange");
        return ResponseData.success(res);
    }

    /**
     * 批量处理索引中点击插入
     */
    async batchFillFromIndexPage(args) {
        let {constructId, singleId, unitId, pointLine, kind, indexIds, unit, rcjFlag,libraryCode} = args;
        let results = [];
        let lastDe = pointLine;
        for(let indexId of indexIds){

            let curLine = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(pointLine.sequenceNbr);
            let res = null;
            if(ObjectUtils.isNotEmpty(curLine) && curLine.type === DeTypeConstants.DE_TYPE_EMPTY){
                res = await this.service.gongLiaoJiProject.gljStepItemCostService.replaceFromIndexPage(constructId, singleId, unitId, indexId, lastDe.sequenceNbr, rcjFlag, null, kind, unit,libraryCode);  
                lastDe.sequenceNbr = res.sequenceNbr;
            }else{
                res = await this.service.gongLiaoJiProject.gljStepItemCostService.fillDataFromIndexPage(constructId, singleId, unitId, lastDe, kind, indexId, unit, rcjFlag,null,null,null,libraryCode);
                lastDe.sequenceNbr = res.sequenceNbr;
            }

            results.push(res);
        }
        //状态数据处理
        let allNodes = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId === unitId);
        this._setItemDtaStatus(allNodes);
        return ResponseData.success(results);
    }

    /**
     * 从清单定额索引中点替换
     */
    async replaceFromIndexPage(args) {
        let {constructId, singleId, unitWorkId, unitId, selectId, replaceId, rcjFlag, conversionCoefficient, kind, unit,libraryCode} = args;
        if (!unitWorkId) {
            unitWorkId = unitId;
        }
        let res = await this.service.gongLiaoJiProject.gljStepItemCostService.replaceFromIndexPage(constructId, singleId, unitWorkId, selectId, replaceId, rcjFlag, conversionCoefficient, kind, unit,libraryCode);
        // await this.service.management.sycnTrigger("unitDeChange");
        return ResponseData.success(res);
    }

    /**
     * 锁定解锁
     * @param args
     * @return {ResponseData}
     */
    lockQd(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        this.service.gongLiaoJiProject.gljStepItemCostService.lockQd(constructId, singleId, unitId, pointLine.sequenceNbr);

        return ResponseData.success(true);
    }
    unLockQd(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        this.service.gongLiaoJiProject.gljStepItemCostService.unLockQd(constructId, singleId, unitId, pointLine.sequenceNbr);

        return ResponseData.success(true);
    }
    lockAll(args) {
        let {constructId, singleId, unitId} = args;
        let all = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId === unitId);
        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    all[i].isLocked = 1;
                }
            }
        }
        return ResponseData.success(true);
    }
    unLockAll(args) {
        let {constructId, singleId, unitId} = args;
        let all = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId === unitId);

        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    delete all[i].isLocked;
                }
            }
        }
        return ResponseData.success(true);
    }

    /**
     * 分页查询
     * @param args
     */
    async page(args) {
        let {constructId, singleId, unitId, pageNum, pageSize, sequenceNbr,isAllFlag,colorList} = args;
        //获取单位数据
        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let res = await this.service.gongLiaoJiProject.gljStepItemCostService.pageSearch(constructId, singleId, unitId, sequenceNbr,pageNum, pageSize,isAllFlag,unit.screenCondition,colorList);
        return ResponseData.success(res);
    }

    /**
     * 展开
     */
    open(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        const result = this.service.gongLiaoJiProject.gljStepItemCostService.open(constructId, singleId, unitId, pointLine);
        return ResponseData.success(true);
    }

    /**
     * 折叠
     */
    close(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        const result = this.service.gongLiaoJiProject.gljStepItemCostService.close(constructId, singleId, unitId, pointLine);
        return ResponseData.success(true);
    }

    /**
     * 删除
     * @param args
     * @returns {ResponseData}
     */
    async remove(args) {
        let {constructId, singleId, unitWorkId, pointLine, isBlock, isRemoveRelationDe} = args;
        try {
            const result = await this.service.gongLiaoJiProject.gljStepItemCostService.removeLine(constructId, singleId, unitWorkId, pointLine, isBlock);
            if (isRemoveRelationDe === true) {
                let relationDeList = await ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.fDeId === pointLine.sequenceNbr && item.isRelationDe === true && String(item.quantityExpression).includes('GCL') && !String(item.quantityExpression).includes('GCLMXHJ'));
                for (let relationDe of relationDeList) {
                    await ProjectDomain.getDomain(constructId).csxmDomain.removeDeRow(relationDe.sequenceNbr);
                }
            }
            //状态数据处理
            let allNodes = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId === unitWorkId);
            this._setItemDtaStatus(allNodes);
            // await this.service.management.sycnTrigger("unitDeChange");
            // await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
            //     constructId: constructId,
            //     unitId: unitWorkId,
            //     qfMajorType: allNodes[0].qfCode
            // });   
             
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail(e.message);
        }

    }


    /**
     * 措施项目数据行颜色设置  批量设置
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateDataColorCsxmColl(args) {
        let {constructId, singleId, unitWorkId, idList, column,value} = args;
        for(const pointLineId of idList){
            let upDateInfo={
                column,value
            }
            let res = await this.service.gongLiaoJiProject.gljStepItemCostService.upDateOnList(constructId, singleId, unitWorkId, pointLineId, upDateInfo);
        }
        await this.service.itemBillProjectOptionService.updateUnitColorList(constructId, singleId, unitWorkId,"csxm");
        return ResponseData.success(true);
    }


    
    _setItemDtaStatus(mockAllData) {
        let returnArray = mockAllData;
        let map = new Map();
        //是否可以上下移动判断
        for (let i = 0; i < returnArray.length; i++) {
            let element = returnArray[i];

            let filter = [];
            if (map.get(element.parentId) != null) {
                filter = map.get(element.parentId);
            } else {
                filter = returnArray.filter(item => item.parentId == element.parentId);
                map.set(element.parentId, filter);
            }
            if (filter.indexOf(element) == 0) {
                element['isFirst'] = true;
            } else {
                element['isFirst'] = false;
            }

            if (filter.indexOf(element) == filter.length - 1) {
                element['isLast'] = true;
            } else {
                element['isLast'] = false;
            }
            //分部是否可以上调下调判断
            //给所有的数据设置不可以升降级
            element.isUpFb = true;
            element.isDownFb = true;
            //如果是分部 不可升级
            if (element.type == DeTypeConstants.DE_TYPE_FB) {
                element.isUpFb = false;
                //获取同层级数据 如果没有同层级其他数据将不可以进行降级操作
                //如果同层级有数据  但是当前分部是第一行数据 不可降级操作
                let ts = returnArray.filter(fb => fb.parentId == element.parentId);
                if (ts.length > 1) {
                    if (element.index==0) {
                        element.isDownFb = false;
                    } else {
                        let b = false;//this.isOnlyQd(element.parent,returnArray);
                        element.isDownFb = b?false:true;
                    }
                } else {
                    element.isDownFb = false;
                }
            }

            //判断若下移会导致分部层级超过四层则【降级】按钮置灰  暂未处理TODO SUNPO
            if (element.type == DeTypeConstants.DE_TYPE_ZFB) {
                let ts = returnArray.filter(fb => fb.parentId == element.parentId);
                if (ts.length > 1) {
                    if (element.index==0) {
                        element.isDownFb = false;
                    } else {
                        let b = false;//this.isOnlyQd(element.parent,returnArray);
                        element.isDownFb = b?false:true;
                    }
                } else {
                    element.isDownFb = false;
                }
                //如果选中分部的层级结构是第四级 或者分部中包含第四级分部数据不可以降级
                // let fourLeve = this.isFourLeve(element, returnArray);
                // if (fourLeve) {
                //     element.isDownFb = false;
                // }

            }
        }

    }

    /**
     * 通过编码查询03定额是否存在
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async checkAndQueryDe(args)
  {
    let {constructId,unitId,deCode} = args;
    
    if(ObjectUtil.isNotEmpty(deCode)){
      deCode = deCode.toUpperCase();
      if(deCode.indexOf('#')){
        deCode = deCode.split('#')[0];
      }
    }
    
    let projectDomain = ProjectDomain.getDomain(constructId);
    let deBaseDomain = projectDomain.getDeDomain();
    let data = await deBaseDomain.checkAndQueryDe(constructId, unitId, deCode, projectDomain.functionDataMap);
    if(ObjectUtil.isEmpty(data.local)&&(ObjectUtil.isEmpty(data.db)||(ObjectUtil.isNotEmpty(data.db)&&data.db.length<=1))){
      return  ResponseData.fail();
    }
    if(data.local.length === 1 && ObjectUtil.isEmpty(data.db)){
      return  ResponseData.fail();
    }
    //判断如果//编码-名称-单位-单价  一样不返回
    if(data.local.length === 1 && data.db.length === 1){
      let localDe = data.local[0];
      let dbDe = data.db[0];
      if(localDe.deCode === dbDe.deCode && localDe.deName === dbDe.deName && localDe.displayType === dbDe.displayType && localDe.unit === dbDe.unit
        && localDe.price === dbDe.price
      ){
        return  ResponseData.fail();
      }
    }
    return ResponseData.success(data);
  }

  
  /**
   * 编码重复选择定额处理
   */
  async selectDe(args){
    let { constructId, unitId, deStandardId, deRowId, deRow} = args;
    if(ObjectUtil.isEmpty(deStandardId) || ObjectUtil.isEmpty(deRowId)){
      return ResponseData.fail("请选择定额");
    }
    let deRowModel = await this.service.gongLiaoJiProject.gljStepItemCostService.selectDe(constructId, unitId, deStandardId, deRowId, deRow);
    return ResponseData.success(DeBaseDomain.filter4DeTree(deRowModel));
  }

    /**
     * 展开至那级集合查询
     */
    async openLevelCheckList(args) {
        let {constructId, singleId, unitId} = args;
        let newVar = await this.service.gongLiaoJiProject.gljStepItemCostService.openLevelCheckList(constructId, unitId);
        return ResponseData.success(newVar);
    }

    /**
     * 展开所有、一级分部、二级分部、三级分部、四级分部、子目、主材/设备
     */
    async openLevel(args) {
        let {constructId, singleId, unitId, type} = args;

        let newVar = await this.service.gongLiaoJiProject.gljStepItemCostService.openLevel(constructId, unitId, type);
        return ResponseData.success(true);
    }

    
    /**
     * 创建 用户定额人材机
     * @param args
     * @returns {Promise<ResponseData>}
     */
  async appendUserResource(args)
  {
      let {constructId, unitId, prevDeRowId,userResource} = args;
      let index;
      let prevDeRow;
      let parentId;
      let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
      if(ObjectUtil.isNotEmpty(prevDeRowId))
      {
        prevDeRow = csxmDomain.getDeById(prevDeRowId)
        if (
          [
            DeTypeConstants.DE_TYPE_DEFAULT,
            DeTypeConstants.DE_TYPE_FB,
            DeTypeConstants.DE_TYPE_ZFB,
            DeTypeConstants.DE_TYPE_DELIST
          ].includes(prevDeRow.type)
        ) {
          parentId = prevDeRowId
        } else {
          index = prevDeRow.index + 1
          parentId = prevDeRow.parentId
        }
      }
      else
      {
          parentId = userResource.parentId;
      }
      let deRowModel = new StandardDeModel(constructId,unitId,Snowflake.nextId(),parentId,DeTypeConstants.DE_TYPE_USER_RESOURCE);
      await csxmDomain.createDeRow(deRowModel,index);
      userResource.deRowId = deRowModel.sequenceNbr;
      userResource.deId = deRowModel.sequenceNbr;
      await csxmDomain.appendUserResource(constructId, unitId, userResource.deRowId, userResource);
      await csxmDomain.extendQuantity(constructId, unitId, userResource.deRowId)
      // 不需要更新人材机定额的人材机的消耗量
    //   await this.service.gongLiaoJiProject.gljStepItemCostService._updateRcjResQty(deRow,csxmDomain,1,this.service);
      return ResponseData.success(true);
  }

  /**
   * 创建用户定额
   * @param args
   */
  async appendUserDe(args)
  {
    let {constructId,unitId,userDe,deRowId} = args;
    let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain
    let deRow = csxmDomain.getDeById(deRowId);
    userDe.deRowId = deRowId;
    await csxmDomain.appendUserDe(constructId,unitId,deRowId,userDe);
    await csxmDomain.extendQuantity(constructId, unitId, deRow.sequenceNbr);

    await this.service.gongLiaoJiProject.gljStepItemCostService._updateRcjResQty(deRow,csxmDomain,1,this.service);
    return ResponseData.success(true);
  }

  
    /**
     * 创建用户定额
     * @param args
     */
    async appendUserDeNextRow(args)
    {
        let {constructId, unitId,prevDeRowId,userDe} = args;
        let index;
        let prevDeRow;
        let parentId;
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        if(ObjectUtil.isNotEmpty(prevDeRowId))
        {
          prevDeRow = csxmDomain.getDeById(prevDeRowId)
          if (
            [
              DeTypeConstants.DE_TYPE_DEFAULT,
              DeTypeConstants.DE_TYPE_FB,
              DeTypeConstants.DE_TYPE_ZFB,
              DeTypeConstants.DE_TYPE_DELIST,
            ].includes(prevDeRow.type)
          ) {
            parentId = prevDeRowId
          } else {
            index = prevDeRow.index + 1
            parentId = prevDeRow.parentId
          }
        }

        let deRowModel = new StandardDeModel(constructId,unitId,Snowflake.nextId(),parentId,DeTypeConstants.DE_TYPE_USER_DE);
        await csxmDomain.createDeRow(deRowModel,index);
        let deRow = await csxmDomain.appendUserDe(constructId,unitId,deRowModel.sequenceNbr,userDe);
        await this.service.gongLiaoJiProject.gljStepItemCostService._updateRcjResQty(deRow,csxmDomain,1,this.service);
        return ResponseData.success(true);
    }

 /**
   * 临时删除定额行
   * @param args
   * @returns {ResponseData}
   */
 async tempRemoveDeRow(args)
 {
   let {constructId,idList,deRowId,deList} = args;
   let deDomain = ProjectDomain.getDomain(constructId).csxmDomain;
   let zcsbRcjs = [];
   //临时删除人材机还是定额，用是否传了定额id来处理
   if(ObjectUtil.isEmpty(deRowId)){
     for(let de of deList){
        if(de.type == DeTypeConstants.SUB_DE_TYPE_DE){ //不存在与定额，假设为人材机
            zcsbRcjs.push(de);
            continue;
        }
       await deDomain.tempRemoveDeRow(de.id,true)
     }
   }else{
     for(let id of idList){
       await this.service.gongLiaoJiProject.gljRcjService.tempRemoveRcjRow(constructId,deRowId,id);
     }
   }
   
    //处理虚拟定额/主材设备
    if(ObjectUtils.isNotEmpty(zcsbRcjs)){
        for(let rcj of zcsbRcjs){
            //存在父级无需处理主材设备
            let de = deList.find(item=>item.id == rcj.parentId);
            if(ObjectUtil.isNotEmpty(de)){
                continue;
            }
            await this.service.gongLiaoJiProject.gljRcjService.tempRemoveRcjRow(constructId,rcj.parentId,rcj.id);
        }
    }
   return ResponseData.success();
 }
  /**
  * 取消临时删除定额行
  * @param args
  * @returns {ResponseData}
  */
  async cancelTempRemoveDeRow(args)
  {
    let {constructId,idList,deRowId,deList} = args;
    let deDomain = ProjectDomain.getDomain(constructId).csxmDomain;
    let zcsbRcjs=[];
    //临时删除人材机还是定额，用是否传了定额id来处理
   if(ObjectUtil.isEmpty(deRowId)){
      for(let de of deList){
        if(de.type == DeTypeConstants.SUB_DE_TYPE_DE){ //不存在与定额，假设为人材机
            zcsbRcjs.push(de);
            continue;
        }
       await deDomain.cancelTempRemoveDeRow(de.id);
     }
   }else{
     for(let id of idList){
       await this.service.gongLiaoJiProject.gljRcjService.cancelTempRemoveRcjRow(constructId,deRowId,id);
     }
   }
   //处理虚拟定额/主材设备
   if(ObjectUtils.isNotEmpty(zcsbRcjs)){

     for(let rcj of zcsbRcjs){
       //存在父级无需处理主材设备
       let de = deList.find(item=>item.id == rcj.parentId);
       if(ObjectUtil.isNotEmpty(de)){
           continue;
       }
       await this.service.gongLiaoJiProject.gljRcjService.cancelTempRemoveRcjRow(constructId,rcj.parentId,rcj.id);
     }
   }
    return ResponseData.success();
  }
  
/**
 * 调整人材机单价
 * @param args
 * @returns {Promise<ResponseData>}
 */
async changeRcjPrice(args) {
    let {constructId, unitId, deRowId, price,rSum,cSum,jSum} = args;
    let deDomain = ProjectDomain.getDomain(constructId).csxmDomain;
    try{
        
        await deDomain.updatePriceWithRCJ(constructId, unitId, deRowId,price,rSum,cSum,jSum);
        //联动计取安装费
        await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "update");
        await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
        });
    }catch(error){
        return ResponseData.fail(error.message);      
    }
    return ResponseData.success();
    }
  /**
  * 批量取消临时删除定额行
  * @param args
  * @returns {ResponseData}
  */
  async batchCancelTempRemoveDeRow(args)
  {
    let {constructId,unitId} = args; 
    let unitProjects = [];
    if(ObjectUtil.isNotEmpty(unitId)){
       let unitProject = await ProjectDomain.getDomain(constructId).getProjectById(unitId);
       unitProjects.push(unitProject);
    }else{
       unitProjects = await ProjectDomain.getDomain(constructId).getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
    }
    let allData = await ProjectDomain.getDomain(constructId).csxmDomain.batchCancelTempRemoveDeRow(constructId,unitProjects)
    let checkOperate = true; //
    if(ObjectUtil.isNotEmpty(allData.de)){
     for(let id of allData.de){
       checkOperate = false;
       await ProjectDomain.getDomain(constructId).csxmDomain.cancelTempRemoveDeRow(id); 
     }
    }
    if(ObjectUtil.isNotEmpty(allData.rcj)){
     for(let rcjDetail of allData.rcj){
       checkOperate = false;
       await this.service.gongLiaoJiProject.gljRcjService.cancelTempRemoveRcjRow(constructId,rcjDetail.deRowId,rcjDetail.sequenceNbr);
     } 
    }
    if(checkOperate){
     return  ResponseData.fail("未找到临时删除子目");
    }
    return ResponseData.success();
  }
  /**
  * 批量删除临时删除定额行
  * @param args
  * @returns {ResponseData}
  */
  async realTempRemoveDeRow(args)
  {
   //无需调用await service.gongLiaoJiProject.gsUnitCostCodePriceService.countCostCodePrice
   //因为临时删除已经处理了，避免重复通知
    let {constructId,unitId} = args; 
    let unitProjects = [];
    if(ObjectUtil.isNotEmpty(unitId)){
       let unitProject = await ProjectDomain.getDomain(constructId).getProjectById(unitId);
       unitProjects.push(unitProject);
    }else{
       unitProjects = await ProjectDomain.getDomain(constructId).getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
    }
    let allData = await ProjectDomain.getDomain(constructId).csxmDomain.realTempRemoveDeRow(constructId,unitProjects);
    let checkOperate = true; //

    if(ObjectUtil.isNotEmpty(allData.de)){
     for(let deRowId of allData.de){
       checkOperate = false;
       await ProjectDomain.getDomain(constructId).csxmDomain.removeDeRow(deRowId,false)
     }
    }
    if(ObjectUtil.isNotEmpty(allData.rcj)){
     for(let rcjDetail of allData.rcj){
       checkOperate = false;
       await this.service.gongLiaoJiProject.gljRcjService.deleteRcjByCodeData(rcjDetail.deRowId,constructId,rcjDetail.unitId,rcjDetail.sequenceNbr,false,{});
     } 
    }
    if(checkOperate){
     return  ResponseData.fail("未找到临时删除子目");
    }
    return ResponseData.success();
  }

  
    /**
     * 批量删除工程量为0的数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDeleteQuantityZeroDe(args) {
        let {constructId, unitId, applyConstruct} = args;
        if (applyConstruct) {
            let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
            let unitList = projectTree.filter(o => o.type === ProjectLevelConstant.unit);
            let originalQuantityNoZeroList = [];
            if (ObjectUtil.isNotEmpty(unitList)) {
                for (let unit of unitList) {
                    let deTree = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unit.sequenceNbr);
                    if (ObjectUtil.isNotEmpty(deTree)) {
                        let originalQuantityZeroList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_DE
                        || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                        || o.type === DeTypeConstants.DE_TYPE_USER_DE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE) && (ObjectUtil.isEmpty(o.originalQuantity) || o.originalQuantity == 0 || o.originalQuantity == 0.00));
                        if (ObjectUtil.isNotEmpty(originalQuantityZeroList)) {
                            originalQuantityZeroList.forEach(o=>{
                                originalQuantityNoZeroList.push(o);
                            })
                        }
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(originalQuantityNoZeroList)) {
                for (const o of originalQuantityNoZeroList) {
                    await ProjectDomain.getDomain(constructId).csxmDomain.removeDeRow(o.deRowId);
                }
            } else {
                return ResponseData.fail("未找到工程量为0子目！");
            }
        } else {
            let deTree = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId);
            if (ObjectUtil.isNotEmpty(deTree)) {
                let originalQuantityZeroList = deTree.filter(o => (o.type === DeTypeConstants.DE_TYPE_DELIST || o.type === DeTypeConstants.DE_TYPE_DE
                        || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                        || o.type === DeTypeConstants.DE_TYPE_USER_DE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE) && (ObjectUtil.isEmpty(o.originalQuantity) || o.originalQuantity == 0 || o.originalQuantity == 0.00));
                if (ObjectUtil.isNotEmpty(originalQuantityZeroList)) {
                    for (const o of originalQuantityZeroList) {
                        await ProjectDomain.getDomain(constructId).csxmDomain.removeDeRow(o.deRowId);
                    }
                } else {
                    return ResponseData.fail("未找到工程量为0子目！");
                }
            }
        }
        return ResponseData.success(true);
    }

    /**
     * 查找
     */
    async search(args) {
        let result = await this.service.gongLiaoJiProject.gljStepItemCostService.search(args);
        return ResponseData.success(result);
    }
   
    /**
     * 过滤
     */
    async filterDe(args) {
        let result = await this.service.gongLiaoJiProject.gljStepItemCostService.filterDe(args);
        return ResponseData.success(result);
    }

    /**
     * 插入子目定额
     * @param args
     * @returns {Promise<void>}
     */
    async insertZmDe(args) {
        let {constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag,libraryCode, fDeId, zmDeList, zmVariableRuleList} = args;
        let fQuantity = zmVariableRuleList?.find(item => item.variableCode === 'GCL').resultValue;
        // 修改父级工程量
        let upDateInfo={
            column: "originalQuantity",
            value: fQuantity,
        }
        await this.service.gongLiaoJiProject.gljStepItemCostService.upDateOnList(constructId, singleId, unitId, fDeId, upDateInfo);
        let result = {deList: [], unPricedDeList: [], conversionList: []}
        if (ObjectUtil.isNotEmpty(zmDeList)) {
            // 记录zmVariableRuleList
            let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
            let unitQuantiesMap = quantitiesMap.get(unitId);
            let qua = unitQuantiesMap.get(fDeId);
            qua.zmVariableRuleList = zmVariableRuleList
            for (let zmDe of zmDeList) {
                if (ObjectUtil.isEmpty(zmDe.deIdZ)) {
                    continue
                }
                let res = await this.service.gongLiaoJiProject.gljStepItemCostService.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, zmDe.deIdZ, unit, 0,null,null,null,libraryCode);
                let relationDe = ProjectDomain.getDomain(constructId).csxmDomain.getDe(item=>item.sequenceNbr === res.sequenceNbr);
                relationDe.isRelationDe = true
                relationDe.fDeId = fDeId
                let upDateInfo2={
                    column: "originalQuantity",
                    value: zmDe.quantity,
                }
                await this.service.gongLiaoJiProject.gljStepItemCostService.upDateOnList(constructId, singleId, unitId, relationDe.sequenceNbr, upDateInfo2);
                // relationDe.quantityExpression = zmDe.quantityExpression

                //只保存费用代码给GCL
                let newExpression = zmDe.quantityExpression;
                zmVariableRuleList.forEach(rule => {
                    if (rule.variableCode !== "GCL") {
                        // 转义正则表达式特殊字符并添加单词边界
                        const escapedCode = rule.variableCode.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const regex = new RegExp(`\\b${escapedCode}\\b`, 'g');
                        newExpression = newExpression.replace(regex, rule.resultValue);
                    }
                });
                relationDe.quantityExpression = newExpression

                //更新定额费用代码
                let zmPriceCodes = [
                    {code: 'GCL',price: ObjectUtils.isNotEmpty(fQuantity) ? fQuantity: 0}
                ]
                await this.service.gongLiaoJiProject.gljDeService.setDeCostCode(constructId, unitId, relationDe.sequenceNbr, zmPriceCodes);

                let de = ConvertUtil.deepCopy(relationDe);
                result.deList.push(de);
                //未计价材料
                let unPriced = await this.service.gongLiaoJiProject.gljDeService.getMainMaterialAndEquipment({
                    constructId: constructId,
                    unitId: unitId,
                    deStandardId: zmDe.deIdZ
                });
                if (ObjectUtils.isNotEmpty(unPriced)) {
                    de.unPriced = unPriced
                    let rcjList = await this.service.gongLiaoJiProject.gljProjectCommonService.getAllRcjByDeId(constructId, unitId, de.sequenceNbr);
                    for (let item of unPriced) {
                        item.rcjDetailId = rcjList.find(item2 => item2.materialCode === item.materialCode).sequenceNbr
                    }
                    result.unPricedDeList.push(de);
                }
                //标准换算
                let conversion = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvertList(constructId, unitId, de.sequenceNbr);
                if (ObjectUtils.isNotEmpty(conversion)) {
                    result.conversionList.push(conversion);
                }
            }
            //状态数据处理
            let allNodes = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId === unitId);
            this._setItemDtaStatus(allNodes);
        }
        return ResponseData.success(result);
    }


    /**
     * 定额是否关联子目
     * @param args
     * @returns {Promise<void>}
     */
    async existRelationDe(args) {
        let {constructId, deRowIdList} = args
        if (ObjectUtil.isNotEmpty(deRowIdList)) {
            for (let deRowId of deRowIdList) {
                let relationDeList = await ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.fDeId === deRowId && item.isRelationDe === true && String(item.quantityExpression)?.includes('GCL') && !String(item.quantityExpression).includes('GCLMXHJ'));
                if (ObjectUtils.isNotEmpty(relationDeList)) {
                    return ResponseData.success(true);
                }
            }
        }
        return ResponseData.success(false);
    }

    /**
     * 获取所有定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getDeAllDepth(args) {
        let {constructId, unitId, deRowId} = args;
        let deLists = await this.service.gongLiaoJiProject.gljStepItemCostService.getDeAllDepth(constructId, unitId, deRowId);
        return ResponseData.success(deLists);
    }

    /**
     * 定额删除所有批注
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteAllDeAnnotations(args) {
        let {constructId, unitId, applyConstruct} = args;
        if (applyConstruct) {
            let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
            let unitList = projectTree.filter(o => o.type === ProjectLevelConstant.unit);
            if (ObjectUtil.isNotEmpty(unitList)) {
                for (let unit of unitList) {
                    let deTree = ProjectDomain.getDomain(constructId).csxmDomain.getDeTreeDepth(constructId, unit.sequenceNbr, null, null);
                    if (ObjectUtil.isNotEmpty(deTree)) {
                        let annotationsList = deTree.filter(o => ObjectUtil.isNotEmpty(o.annotations));
                        if (ObjectUtil.isNotEmpty(annotationsList)) {
                            for (let o of annotationsList) {
                                o.annotations = undefined;
                                o.isShowAnnotations = false;
                                ProjectDomain.getDomain(constructId).csxmDomain.updateDe(o);
                            }
                        }
                    }
                }
            }
        } else {
            let deTree = ProjectDomain.getDomain(constructId).csxmDomain.getDeTreeDepth(constructId, unitId, null, null);
            if (ObjectUtil.isNotEmpty(deTree)) {
                let annotationsList = deTree.filter(o => ObjectUtil.isNotEmpty(o.annotations));
                if (ObjectUtil.isNotEmpty(annotationsList)) {
                    for (let o of annotationsList) {
                        o.annotations = undefined;
                        o.isShowAnnotations = false;
                        ProjectDomain.getDomain(constructId).csxmDomain.updateDe(o);
                    }
                }
            }
        }
    }


    /**
     * 获取所有定额
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDelBySeachList(args) {
        let deLists = await this.service.gongLiaoJiProject.gljStepItemCostService.batchDelBySeachList(args);
        return ResponseData.success(deLists);
    }


}
GljStepItemCostController.toString = () => '[class GljStepItemCostController]';
module.exports = GljStepItemCostController;
