const {Service} = require("../../../core");
const MenuBarEnum = require("../enums/MenuBarEnum");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require('../domains/ProjectDomain');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const BusinessConstants = require('../constants/BusinessConstants');
const GsProjectSettingEnum = require("../enums/GljProjectSettingEnum");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const os = require("os");
const fs = require("fs");
const _ = require("lodash");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme,BrowserWindow
} = require('electron');
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {toJsonYsfString} = require("../../../electron/main_editor/util");
const {CryptoUtils} = require("../utils/CrypUtils");
const YGLJOperator = require("../core/tools/fileOperator/YGLJOperator");
const AppContext = require("../core/container/APPContext");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const CommonConstants = require("../constants/CommonConstants");
const FileOperatorType = require("../constants/FileOperatorType");
const FileOperator = require("../core/tools/fileOperator/FileOperator");
const {ConvertUtil} = require("../utils/ConvertUtils");
const ProjectLevelConstant = require("../constants/ProjectLevelConstant");
const {Snowflake} = require("../utils/Snowflake");
const LabelConstants = require("../constants/LabelConstants");
const WildcardMap = require("../core/container/WildcardMap");
const {ConstructOperationUtil} = require("../../../electron/utils/ConstructOperationUtil");
const YGSOperator = require("../core/tools/fileOperator/YGLJOperator");
const GljPrecisionSetting = require("../enums/GljPrecisionSetting");
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const ConstantUtil = require("../enums/ConstantUtil");
const {NumberUtil} = require("../utils/NumberUtil");
const {ConversionInfoUtil} = require("../standard_conversion/util/ConversionInfoUtil");
const DeTypeConstants = require("../constants/DeTypeConstants");
const {DeFlattener} = require("../domains/calculators/de/DeFlattener")
const {ResourceCalculator} = require("../domains/calculators/resource/ResourceCalculator");
const DeQualityUtils = require("../domains/utils/DeQualityUtils");
const UnitUtils = require('../core/tools/UnitUtils');
const DeUtils = require("../domains/utils/DeUtils");

/**
 * 菜单栏  service
 */
class GljCommonService extends Service {

    constructor(ctx) {
        super(ctx);
        let {
            gljRcjCollectService,
            gljOverviewService,
            // gljEquipmentCostsService,
            gljProjectCommonService,
            gljUnitCostSummaryService
        } = this.service.gongLiaoJiProject

        this.map = new Map();
        // //項目概況-项目
        this.map.set(MenuBarEnum.PROJECT_OVERVIEW, {
            obj: gljOverviewService,
            fun: gljOverviewService.getChildrenMenuList
        });
        //項目设备购置费
        // this.map.set(MenuBarEnum.PROJECT_SBGZF, {
        //     obj: gljEquipmentCostsService,
        //     fun: gljEquipmentCostsService.getChildrenMenuList
        // });
        //項目概況-单位
        this.map.set(MenuBarEnum.UNIT_PROJECT_OVERVIEW, {
            obj: gljOverviewService,
            fun: gljOverviewService.getChildrenMenuList
        });
        this.map.set(MenuBarEnum.UNIT_MEASURE_PROJECT, {obj: gljProjectCommonService, fun: gljProjectCommonService.getCsxmFBTree})//措施项目，左侧目录树
        this.map.set(MenuBarEnum.UNIT_ITEM_BILL, {obj: gljProjectCommonService, fun: gljProjectCommonService.getFBTree});//分部分項-左側目錄書
        // this.map.set(MenuBarEnum.UNIT_OTHER_PROJECT,{obj:otherProjectService,fun:otherProjectService.menuData});//分部分項-左側目錄書
        this.map.set(MenuBarEnum.UNIT_COST_AGGREGATION, {
            obj: gljUnitCostSummaryService,
            fun: gljUnitCostSummaryService.getCostSummaryMajorMenuList
        });//费用汇总-单位
        /**
         * 人材机汇总左侧树
         */
        this.map.set(MenuBarEnum.RCJ_SUMMARY, {
            obj: gljRcjCollectService,
            fun: gljRcjCollectService.getRcjCellectMenuData
        });
        this.map.set(MenuBarEnum.SINGLE_RCJ_SUMMARY, {
            obj: gljRcjCollectService,
            fun: gljRcjCollectService.getRcjCellectMenuData
        });
        this.map.set(MenuBarEnum.UNIT_RCJ_SUMMARY, {
            obj: gljRcjCollectService,
            fun: gljRcjCollectService.getRcjCellectMenuData
        });
    }


    /**
     * 获取菜单栏数据
     * @param args
     * @return {Promise<*|ResponseData>}
     */
    async getMenuData(args) {
        let type = args.type;
        let levelType = args.levelType;
        let code = args.code;
        let menuBarEnum = this.getMenuBarEnum(type, levelType, code);
        if (ObjectUtils.is_Undefined(menuBarEnum) || ObjectUtils.isNull(menuBarEnum)) {
            return ResponseData.fail("参数错误");
        }
        let objDefinition = this.map.get(menuBarEnum);
        if (ObjectUtils.isEmpty(objDefinition)) {
            return;
        }
        let result = await objDefinition.fun.call(objDefinition.obj, args);
        // if (args.code === "3" &&
        //     result.itemList && result.itemList.length>0 &&
        //     args.constructId && args.constructId !== "" &&
        //     args.singleId && args.singleId !== "" &&
        //     args.unitId && args.unitId !== "" ) {
        //
        //     let baseFileId = PricingFileFindUtils.getMainFeeFile(args.constructId, args.singleId, args.unitId).feeFileId;
        //     for (let i = 0 ; i<result.itemList.length;++i) {
        //         if(result.itemList[i][baseFileId]) {
        //             result.itemList[i].defaultFeeFlag = 1;
        //         }
        //     }
        // }
        //
        // if (args.code === "3" &&
        //     (!args.singleId || args.singleId === "") &&
        //     result.itemList && result.itemList.length>0) {
        //     let unitFeeFiles;
        //     if (!args.unitId || args.unitId === "") {
        //         // 找全部的
        //         unitFeeFiles = [];
        //         let units = PricingFileFindUtils.getUnitList(args.constructId);
        //         for (let i = 0 ; i < units.length ; ++ i) {
        //             unitFeeFiles = unitFeeFiles.concat(units[i].feeFiles);
        //         }
        //     } else {
        //          // 找特定的 unit
        //         let unitp = PricingFileFindUtils.getUnitList(args.constructId).filter(f=>f.sequenceNbr === args.unitId);
        //         if (unitp.length > 0) {
        //             unitFeeFiles =  unitp[0].feeFiles;
        //         } else {
        //             unitFeeFiles =  PricingFileFindUtils.getUnitList(args.constructId)[0].feeFiles;
        //         }
        //     }
        //
        //     let defFeeFile = unitFeeFiles.filter(f=>f.defaultFeeFlag && f.defaultFeeFlag === 1)[0];
        //     let baseFileId = defFeeFile.feeFileId;
        //     for (let i = 0 ; i<result.itemList.length;++i) {
        //         if(result.itemList[i][baseFileId]) {
        //             result.itemList[i].defaultFeeFlag = 1;
        //         }
        //     }
        // }

        //console.log(result);
        return result;
    }


    /**
     * 获取枚举值
     * @param type
     * @param levelType
     * @param code
     * @return {object | string | bigint | number | boolean | symbol}
     */
    getMenuBarEnum(type, levelType, code) {
        for (let menuBarEnumKey in MenuBarEnum) {
            let Bartype = MenuBarEnum[menuBarEnumKey].type;
            let BarlevelType = MenuBarEnum[menuBarEnumKey].levelType;
            let Barcode = MenuBarEnum[menuBarEnumKey].code;
            if (Bartype == type && BarlevelType == levelType && Barcode == code) {
                return MenuBarEnum[menuBarEnumKey];
            }
        }
    }


    /**
     * 获取外层菜单栏数据
     * @param args
     * @return {ResponseData}
     */
    getMenuList(args) {
        let type = args.type;
        let levelType = args.levelType;
        let values = Object.values(MenuBarEnum);
        let result = values.filter(item => item.type == type && item.levelType == levelType);
        return ResponseData.success(result);
    }


    /**
     * 表格列设置查询
     * @param args
     * @returns {Promise<void>}
     */
    async getTableList(args) {
        let {constructId, singleId, unitId, businessId} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;

        //工程项目rcj表头
        if (businessId === BusinessConstants.PROJECT_RCJ_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.RCJ_TABLELIST + constructId);
        }
        //单位项目rcj表头
        if (businessId === BusinessConstants.UNIT_RCJ_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        //单位项目rcj表头
        if (businessId === BusinessConstants.SINGLE_RCJ_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + singleId);
        }

        //设置预算书表头
        if (businessId === BusinessConstants.UNIT_YSH_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        //设置措施项目表头
        if (businessId === BusinessConstants.UNIT_CSXM_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.CSXM_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.CSXM_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        // // 概算汇总
        // if (businessId === BusinessConstants.PROJECT_GS_SUMMARY_ID) {
        //     let objMap = businessMap.get(FunctionTypeConstants.PROJECT_GS_TABLELIST);
        //     if (ObjectUtils.isEmpty(objMap)) {
        //         return null;
        //     }
        //     return objMap.get(FunctionTypeConstants.PROJECT_GS_TABLELIST + constructId);
        // }
        //单位工程-造价分析 表头
        if (businessId === BusinessConstants.UNIT_ZJFX_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.UNIT_BGLSZ);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(unitId);
        }
        // 单位工程-独立费
        if (businessId === BusinessConstants.UNIT_DLF_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(unitId);
        }

        //单位工程-三材 表头
        if (businessId === BusinessConstants.UNIT_SC_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.SC_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.SC_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        //单位工程-费用汇总 表头
        if (businessId === BusinessConstants.UNIT_FYGZ_ID) {
            let objMap = businessMap.get(FunctionTypeConstants.FYHZ_TABLELIST);
            if (ObjectUtils.isEmpty(objMap)) {
                return null;
            }
            return objMap.get(FunctionTypeConstants.FYHZ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
        }
        return null
    }

    /**
     * 表格列设置保存
     * @param args
     * @returns {Promise<void>}
     */
    async saveTableList(args) {
        let {constructId, singleId, unitId, businessId, header, isDefault} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;

        if (isDefault) {  // 当前单位
            //工程项目rcj表头
            if (businessId === BusinessConstants.PROJECT_RCJ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.RCJ_TABLELIST + constructId, map);
            }
            //单位项目rcj表头
            if (businessId === BusinessConstants.UNIT_RCJ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }
            //单位项目rcj表头
            if (businessId === BusinessConstants.SINGLE_RCJ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.RCJ_COLLECT, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.RCJ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + singleId, map);
            }

            //设置预算书表头
            if (businessId === BusinessConstants.UNIT_YSH_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.YSH_TABLELIST, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }
            //设置措施项目表头
            if (businessId === BusinessConstants.UNIT_CSXM_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.CSXM_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.CSXM_TABLELIST, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.CSXM_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }
            //单位工程-造价分析 表头
            if (businessId === BusinessConstants.UNIT_ZJFX_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.UNIT_BGLSZ);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.UNIT_BGLSZ, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(unitId, map);
            }
            // 单位工程-独立费
            if (businessId === BusinessConstants.UNIT_DLF_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.DLF_TABLELIST, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(unitId, map);
            }
            //单位工程-三材 表头
            if (businessId === BusinessConstants.UNIT_SC_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.SC_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.SC_TABLELIST, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.SC_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }

            //单位工程-费用汇总 表头
            if (businessId === BusinessConstants.UNIT_FYGZ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.FYHZ_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.FYHZ_TABLELIST, objMap);
                }

                let map = {};
                map.isDefault = isDefault
                map.header = header
                objMap.set(FunctionTypeConstants.FYHZ_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
            }
            return null;
        } else {  // 全局

            // 获取该工程项目
            let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);

            // 获取该单项下的所有单位
            let unitProjectsByConstruct = new Array();
            if (ObjectUtils.isNotEmpty(constructProject.children)) {
                PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
            }

            //设置预算书表头
            if (businessId === BusinessConstants.UNIT_YSH_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.YSH_TABLELIST, objMap);
                }
                // 遍历设置所有单位
                for (let i = 0; i < unitProjectsByConstruct.length; i++) {
                    let unitProject = unitProjectsByConstruct[i];
                    let unitId = unitProject.sequenceNbr;

                    let map = {};
                    map.isDefault = true
                    map.header = header
                    objMap.set(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
                }
                return objMap;
            }
            if (businessId === BusinessConstants.UNIT_CSXM_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.CSXM_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.CSXM_TABLELIST, objMap);
                }
                // 遍历设置所有单位
                for (let i = 0; i < unitProjectsByConstruct.length; i++) {
                    let unitProject = unitProjectsByConstruct[i];
                    let unitId = unitProject.sequenceNbr;

                    let map = {};
                    map.isDefault = true
                    map.header = header
                    objMap.set(FunctionTypeConstants.CSXM_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId, map);
                }
                return objMap;
            }
            if (businessId === BusinessConstants.UNIT_DLF_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.DLF_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.DLF_TABLELIST, objMap);
                }
                // 遍历设置所有单位
                for (let i = 0; i < unitProjectsByConstruct.length; i++) {
                    let unitProject = unitProjectsByConstruct[i];
                    let unitId = unitProject.sequenceNbr;

                    let map = {};
                    map.isDefault = true
                    map.header = header
                    objMap.set(unitId, map);
                }
                return objMap;
            }
            //单位工程-费用汇总 表头
            if (businessId === BusinessConstants.UNIT_FYGZ_ID) {
                let objMap = businessMap.get(FunctionTypeConstants.FYHZ_TABLELIST);
                if (ObjectUtils.isEmpty(objMap)) {
                    objMap = new Map();
                    businessMap.set(FunctionTypeConstants.FYHZ_TABLELIST, objMap);
                }
                // 遍历设置所有单位
                for (let i = 0; i < unitProjectsByConstruct.length; i++) {
                    let unitProject = unitProjectsByConstruct[i];
                    let unitId = unitProject.sequenceNbr;

                    let map = {};
                    map.isDefault = true
                    map.header = header
                    objMap.set(unitId, map);
                }
                return objMap;
            }
        }
    }

    async changeTableList(constructId, unitId) {
        //不同组价方式显示取值不同 0 不按市场价， 1 按市场价
        let pricingMethod = ProjectDomain.getDomain(constructId).getRoot().pricingMethod;

        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let objMap = businessMap.get(FunctionTypeConstants.YSH_TABLELIST);
        if (ObjectUtils.isNotEmpty(objMap)) {
            let yssTableList = objMap.get(FunctionTypeConstants.YSH_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
            if (ObjectUtils.isNotEmpty(yssTableList) && ObjectUtils.isNotEmpty(yssTableList.header)) {
                for (let item of yssTableList.header) {
                    await this.changeTableListValue(item, pricingMethod);
                }
            }
        }

        let objMapCsxm = businessMap.get(FunctionTypeConstants.CSXM_TABLELIST);
        if (ObjectUtils.isNotEmpty(objMapCsxm)) {
            let yssTableList = objMapCsxm.get(FunctionTypeConstants.CSXM_TABLELIST + constructId + FunctionTypeConstants.SEPARATOR + unitId);
            if (ObjectUtils.isNotEmpty(yssTableList) && ObjectUtils.isNotEmpty(yssTableList.header)) {
                for (let item of yssTableList.header) {
                    await this.changeTableListValue(item, pricingMethod);
                }
            }
        }
    }

    async changeTableListValue(item, pricingMethod) {
        if (item.field === "baseJournalPrice" || item.field === "price") {
            item.field = pricingMethod === 0 ? "baseJournalPrice" : "price";
            item.dataIndex = pricingMethod === 0 ? "baseJournalPrice" : "price";
        } else if (item.field === "RDSum" || item.field === "RSum") {
            item.field = pricingMethod === 0 ? "RDSum" : "RSum";
            item.dataIndex = pricingMethod === 0 ? "RDSum" : "RSum";
        } else if (item.field === "rdTotalSum" || item.field === "rTotalSum") {
            item.field = pricingMethod === 0 ? "rdTotalSum" : "rTotalSum";
            item.dataIndex = pricingMethod === 0 ? "rdTotalSum" : "rTotalSum";
        } else if (item.field === "CDSum" || item.field === "CSum") {
            item.field = pricingMethod === 0 ? "CDSum" : "CSum";
            item.dataIndex = pricingMethod === 0 ? "CDSum" : "CSum";
        } else if (item.field === "cdTotalSum" || item.field === "cTotalSum") {
            item.field = pricingMethod === 0 ? "cdTotalSum" : "cTotalSum";
            item.dataIndex = pricingMethod === 0 ? "cdTotalSum" : "cTotalSum";
        } else if (item.field === "JDSum" || item.field === "JSum") {
            item.field = pricingMethod === 0 ? "JDSum" : "JSum";
            item.dataIndex = pricingMethod === 0 ? "JDSum" : "JSum";
        } else if (item.field === "jdTotalSum" || item.field === "jTotalSum") {
            item.field = pricingMethod === 0 ? "jdTotalSum" : "jTotalSum";
            item.dataIndex = pricingMethod === 0 ? "jdTotalSum" : "jTotalSum";
        } else if (item.field === "ZDSum" || item.field === "ZSum") {
            item.field = pricingMethod === 0 ? "ZDSum" : "ZSum";
            item.dataIndex = pricingMethod === 0 ? "ZDSum" : "ZSum";
        } else if (item.field === "zdTotalSum" || item.field === "zTotalSum") {
            item.field = pricingMethod === 0 ? "zdTotalSum" : "zTotalSum";
            item.dataIndex = pricingMethod === 0 ? "zdTotalSum" : "zTotalSum";
        } else if (item.field === "SDSum" || item.field === "SSum") {
            item.field = pricingMethod === 0 ? "SDSum" : "SSum";
            item.dataIndex = pricingMethod === 0 ? "SDSum" : "SSum";
        } else if (item.field === "sdTotalSum" || item.field === "sTotalSum") {
            item.field = pricingMethod === 0 ? "sdTotalSum" : "sTotalSum";
            item.dataIndex = pricingMethod === 0 ? "sdTotalSum" : "sTotalSum";
        } else if (item.field === "baseJournalTotalNumber" || item.field === "totalNumber") {
            item.field = pricingMethod === 0 ? "baseJournalTotalNumber" : "totalNumber";
            item.dataIndex = pricingMethod === 0 ? "baseJournalTotalNumber" : "totalNumber";
        }
    }


    /**
     * 工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async getProjectSetting(arg) {
        let {constructId} = arg;
        let projectDomain = ProjectDomain.getDomain(constructId);
        let businessMap = projectDomain.functionDataMap;

        let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
        if (ObjectUtils.isEmpty(objMap)) {
            objMap = new Map();
            businessMap.set(FunctionTypeConstants.PROJECT_SETTING, objMap);

            // 标准换算设置
            objMap.set(GsProjectSettingEnum.STANDARD_CONVERSION, GsProjectSettingEnum.STANDARD_CONVERSION_SETTING);
            // 关联子目弹窗设置
            objMap.set(GsProjectSettingEnum.RELATION_DE, GsProjectSettingEnum.RELATION_DE_SETTING);
            // 未计价材料
            objMap.set(GsProjectSettingEnum.UNPRICED, GsProjectSettingEnum.UNPRICED_SETTING);
            //主材设备计取价差
             objMap.set(GsProjectSettingEnum.PRICING_ZSDIFF,true);
            //补人材机计取价差
             objMap.set(GsProjectSettingEnum.PRICING_RCJDIFF,true);
            //市政设施维修养护工程是否执行中修,默认不勾选
             objMap.set(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR,false);
             //税率设置
             objMap.set(GsProjectSettingEnum.TAXRATE_SETTING,false);


        } else {
            //市政设施维修养护工程是否执行中修,默认不勾选
            if(ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR))){
                objMap.set(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR,false);
            }
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.RCJ_RELATION_DE))) {
            // 人材机关联定额设置
            objMap.set(GsProjectSettingEnum.RCJ_RELATION_DE, GsProjectSettingEnum.RCJ_RELATION_DE_SETTING);
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.FACTOR_EFFECT))) {
            // 主材设备受系数影响
            objMap.set(GsProjectSettingEnum.FACTOR_EFFECT, false);
        }

        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.STANDARD_CONVERSION))) {
            // 标准换算设置
            objMap.set(GsProjectSettingEnum.STANDARD_CONVERSION, GsProjectSettingEnum.STANDARD_CONVERSION_SETTING);
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.RELATION_DE))) {
            // 关联子目弹窗设置
            objMap.set(GsProjectSettingEnum.RELATION_DE, GsProjectSettingEnum.RELATION_DE_SETTING);
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.UNPRICED))) {
            // 未计价材料
            objMap.set(GsProjectSettingEnum.UNPRICED, GsProjectSettingEnum.UNPRICED_SETTING);
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.PRICING_ZSDIFF))) {
            //主材设备计取价差
            objMap.set(GsProjectSettingEnum.PRICING_ZSDIFF, true);
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.PRICING_RCJDIFF))) {
            //补人材机计取价差
            objMap.set(GsProjectSettingEnum.PRICING_RCJDIFF, true);
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR))) {
            //市政设施维修养护工程是否执行中修,默认不勾选
            objMap.set(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR, false);
        }
        if (ObjectUtils.isEmpty(objMap.get(GsProjectSettingEnum.TAXRATE_SETTING))) {
            //税率设置
            objMap.set(GsProjectSettingEnum.TAXRATE_SETTING, false);
        }
        let pricingFlag = projectDomain.getRoot().pricingMethod == 1;
        objMap.set(GsProjectSettingEnum.PRICING_METHOD,pricingFlag);
        return objMap;
    }

    /**
     * 保存工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async saveProjectSetting(arg) {
        let {constructId, gsProjectSettingCode, setting} = arg;
        let projectDomain = ProjectDomain.getDomain(constructId);
        let businessMap = projectDomain.functionDataMap;

        // 标准换算设置
        if (gsProjectSettingCode === GsProjectSettingEnum.STANDARD_CONVERSION) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }

        // 按市场价设置
        if (gsProjectSettingCode === GsProjectSettingEnum.PRICING_METHOD) {
            projectDomain.getRoot().pricingMethod = setting?1:0;
            // 人材机汇总甲供价格重新计算
            let projects = projectDomain.getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT)
            for(let project of projects){
                try {
                    await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                        constructId: constructId,
                        unitId: project.sequenceNbr,
                        qfMajorType: project.qfMajorType
                    });
                } catch (error) {
                    console.error("捕获到异常:", error);
                }

                //修改组价方式后，联动修改预算书、措施项目表格列设置取值
                try {
                    await this.changeTableList(constructId, project.sequenceNbr);
                } catch (error) {
                    console.error("联动修改预算书、措施项目表格列设置取值；捕获到异常:", error);
                }
            }
            // await this.service.gongLiaoJiProject.gljInitProjectService.recalculateRcjCollectDonorMaterialPrice(constructId);
            //初始化直接费
            //await this.service.gongLiaoJiProject.gljRcjCollectService.dropShareCost({"constructId":constructId});
        }

        // 关联子目弹窗设置
        if (gsProjectSettingCode === GsProjectSettingEnum.RELATION_DE) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }

        // 未计价材料设置
        if (gsProjectSettingCode === GsProjectSettingEnum.UNPRICED) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }
        //主材设备计取价差
        if (gsProjectSettingCode === GsProjectSettingEnum.PRICING_ZSDIFF) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
            await this.service.gongLiaoJiProject.gljRcjService.rcjDiffSwitchover(constructId);
        }
        //补人材机计取价差
        if (gsProjectSettingCode === GsProjectSettingEnum.PRICING_RCJDIFF) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
            await this.service.gongLiaoJiProject.gljRcjService.rcjDiffSwitchover(constructId);
        }

        // 人材机关联定额设置
        if (gsProjectSettingCode === GsProjectSettingEnum.RCJ_RELATION_DE) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }
        // 主材设备受系数影响
        if (gsProjectSettingCode === GsProjectSettingEnum.FACTOR_EFFECT) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
            await this.service.gongLiaoJiProject.gljRuleDetailFullService.switchConversionMainMatModAll(constructId, setting);
        }

        //市政设施维修养护工程是否执行中修
        if (gsProjectSettingCode === GsProjectSettingEnum.SZSS_MEDIUM_REPAIR){
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
            //TODO 修改是否执行中修后刷新对应消耗量

            let projects = projectDomain.getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT)
            for(let project of projects){
                let unitId = project.sequenceNbr;
                // 中修换算信息
                let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
                let unitConversionMap = conversionMap?.get(unitId);
                if (ObjectUtils.isNotEmpty(unitConversionMap)) {
                    for (const [deId, deConversion] of unitConversionMap) {
                        let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
                        if (ObjectUtils.isEmpty(deModel)) {
                            continue;
                        }
                        if (setting === true) {
                            // 添加中修换算信息并执行
                            await this.service.gongLiaoJiProject.gljConversionInfoService.addInfoForSzsswxyhzxzx22(deModel);
                        }else {
                            // 删除中修换算信息并执行
                            await this.service.gongLiaoJiProject.gljConversionInfoService.deleteInfoForSzsswxyhzxzx22(deModel);
                        }
                    };
                }
            }
        }
        // 税率设置
        if (gsProjectSettingCode === GsProjectSettingEnum.TAXRATE_SETTING) {
            let objMap = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            objMap.set(gsProjectSettingCode, setting);
        }
    }


    /**
     * 设置存储路径
     * @param arg
     * @return {string|null}
     */
    async setSaveTime(arg) {
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);

        if (ObjectUtils.isNotEmpty(arg)) {
            //for (let item of arg) {
                userHistoryData[arg.paramsTag] = arg.content;
            // }
        }

        let obj = ObjectUtils.toJsonString(userHistoryData);
        fs.writeFileSync(baseDataDir, obj);
        return ResponseData.success(1);
    }



    /**
     * 查询存储路径
     * @param arg
     * @return {string|null}
     */
    async selectFolder(args) {
        let {constructId} = args;
        // let project = PricingFileFindUtils.getProjectObjById(constructId);
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        let array = [];
        array.push({paramsTag: "GLJ_DEF_SAVE_PATH", content: userHistoryData.GLJ_DEF_SAVE_PATH});
        array.push({paramsTag: "GLJ_FILE_DOWNLOAD_PATH", content: userHistoryData.GLJ_FILE_DOWNLOAD_PATH});
        // array.push({paramsTag: "PROJECTATTR", content: project.projectAttrRelateMergeScheme});
        // array.push({
        //     paramsTag: "RGFINMEASUREANDRPRICEINMECHANICALACTION",
        //     content: project.rgfInMeasureAndRPriceInMechanicalAction
        // });
        // array.push({paramsTag: "deGlTcFlag", content: project.deGlTcFlag});
        // array.push({paramsTag:"mainRcjShowFlag",content:project.mainRcjShowFlag});
        // array.push({paramsTag:"standardConversionShowFlag",content:project.standardConversionShowFlag});

        array.push({
            paramsTag: "SAVE_TIME",
            content: ObjectUtils.isNotEmpty(userHistoryData.SAVE_TIME) ? userHistoryData.SAVE_TIME : 5
        });

        return ResponseData.success(array);
    }



    async diffProject(arg) {
        let constructId = arg.constructId;
        let result = false;

        //todo 内存中如果已经没有该项目数据，不进行比对，不然又把constructId加载到了内存中
        let contextMap = AppContext.getAllContexts();   //当前窗口打开的项目
        if (!contextMap.has(constructId)) {
            return ResponseData.success(!result);
        }

        let projectDomain = ProjectDomain.getDomain(constructId);
        let root = ProjectDomain.getDomain(constructId).getRoot();
        let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomain));
        let cacheProStr = await this.toJsonYsfString(jsonObj);

        let cacheDara = CryptoUtils.objectHash(cacheProStr, root.sequenceNbr, false);
        let fileData = global.editProMap.get(root.sequenceNbr);
        if (cacheDara == fileData) {
            result = true;
        }
        // let localFilePro = await PricingFileFindUtils.getProjectObjByPath(cachePro.path);
        // let localFileProStr = JSON.stringify(localFilePro)
        // let result = ObjectUtils.deepEqual(cacheProStr,localFileProStr);
        return ResponseData.success(!result);
    }

    async toJsonYsfString(obj) {
        return JSON.stringify(obj, (key, value) => {
            return typeof value === 'undefined' ? null : value;
        });
    }



    async importGljFile(args) {
        let defaultStoragePath = await this.service.gongLiaoJiProject.gljAppService.getSetStoragePath();

        const options = {
            properties: ['openFile'],
            //defaultPath: defaultStoragePath.toString(), // 默认保存路径
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [CommonConstants.GAISUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return;
        }
        //获取选中的路径
        let filePath = result[0];
        let ygsOperator = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
        if (!await this.service.gongLiaoJiProject.gljProjectService.checkFileExistence(filePath)) {
            console.log("路径有误");
            return;
        }
        //导入项目
        let importProjectObj = await PricingFileFindUtils.getProjectObjByPath(filePath);

        //当前项目
        let projectObjById = ProjectDomain.getDomain(args.constructId).getRoot();

        if (importProjectObj.ProjectTree[0].deStandardReleaseYear !== projectObjById.deStandardReleaseYear) {
            return ResponseData.fail('定额标准不一致，无法导入');
        }

        if ((importProjectObj.ProjectTree[0].projectTaxCalculation.taxCalculationMethod + '') !== (projectObjById.projectTaxCalculation.taxCalculationMethod + '')) {
            return ResponseData.fail('计税方式不一致，无法导入');
        }

        let constructIdImport = importProjectObj.ProjectTree[0].sequenceNbr;     //导入文件的项目
        let contextMap = AppContext.getAllContexts();   //当前窗口打开的项目
        if (contextMap.has(constructIdImport)) {
            return ResponseData.fail('不支持导入正在使用中的工程文件');
        }

        let fileProjectDomain = await ygsOperator.openFile(filePath, false);
        if (fileProjectDomain) {
            let projectTreeResult = [];
            fileProjectDomain.getRoot().path = filePath;
            let projectTree = fileProjectDomain.getProjectTree();
            if (ObjectUtils.isNotEmpty(projectTree)) {
                let projectTreeCopy = ConvertUtil.deepCopy(projectTree);
                for (let item of projectTreeCopy) {
                    item.select = false;
                    projectTreeResult.push(item);
                }
            }
            return ResponseData.success(projectTreeResult);
        } else {
            return ResponseData.fail('导入失败');
        }

    }


    async saveImportProject(args) {
        let constructObj = ProjectDomain.getDomain(args.constructId).getRoot();
        let importConstructObj = ProjectDomain.getDomain(args.importConstructId).getRoot();

        //获取页面选择的结果树
        let newConstructStructureTree = args.projectStructureTree;
        if (ObjectUtils.isNotEmpty(newConstructStructureTree.children)) {
            let constructId = args.constructId;
            // 新增两个bak熟悉用于保存处理后的数据
            constructObj.singleProjectsBak = [];
            constructObj.unitProjectArrayBak = [];

            // 2. 单项工程处理
            for (const item of newConstructStructureTree.children) {
                if (item.type === ProjectLevelConstant.single) {
                    await this._editSingleProjectStructure(args.constructId, args.importConstructId, item, constructObj, true, constructObj);
                } else if (item.type === ProjectLevelConstant.unit) {
                    await this._editUnitStructure(args.constructId, args.importConstructId, item, constructObj, true, constructObj);
                }
            }

            //处理后的数据回填
            if (ObjectUtils.isNotEmpty(constructObj.singleProjectsBak)) {
                constructObj.children = constructObj.singleProjectsBak;
            } else if (ObjectUtils.isNotEmpty(constructObj.unitProjectArrayBak)) {
                constructObj.children = constructObj.unitProjectArrayBak;
            }

            // await this.service.gongLiaoJiProject.gljProjectService.calProjectTree(constructObj);        //编辑机构节点到树结构

            //已经存在的不用刷线费用汇总;导入的在复制单位时只有小数点不一致重新计算需要刷新费用汇总
            // await this.service.gongLiaoJiProject.gljProjectService.doCostCodeOtherProject(constructId);        //编辑完后刷新费用汇总和建设其他费

            delete constructObj.singleProjectsBak;
            return ResponseData.success();
        }
    }

    async _editSingleProjectStructure(constructId, importConstructId, singleParam, parent, ifParentIsConstruct, parentSame) {
        if (ObjectUtils.isNotEmpty(singleParam.import) && singleParam.import) {
            let oldSingle = ProjectDomain.getDomain(importConstructId).getProjectById(singleParam.id);

            let newSingle = await ConvertUtil.deepCopy(oldSingle);
            newSingle.sequenceNbr = Snowflake.nextId();
            newSingle.parentId = parent.sequenceNbr;
            // newSingle.constructId = constructId;
            newSingle.name = singleParam.name;
            newSingle.scopeFlag = true;

            newSingle.childrenCopy = newSingle.children;
            newSingle.children = [];
            //添加单项
            let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newSingle.parentId);
            ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newSingle, parentNode);

            //重新计算取费表单项的值
            await this.dealSingleQfData(constructId, importConstructId, oldSingle, newSingle);

            //重新赋值单项下的单位id和单项spId
            await this.repeatInitSingleItemId(constructId, importConstructId, newSingle);

            if (ifParentIsConstruct) {
                parent.singleProjectsBak.push(newSingle);
            } else {
                parent.subSingleProjectsBak.push(newSingle);
            }
        } else {
            let newSingle = ProjectDomain.getDomain(constructId).getProjectById(singleParam.id);
            if (ifParentIsConstruct) {
                parent.singleProjectsBak.push(newSingle);
            } else {
                parent.subSingleProjectsBak.push(newSingle);
            }

            // 新增两个bak熟悉用于保存处理后的数据
            newSingle.unitProjectsBak = new Array();
            newSingle.subSingleProjectsBak = new Array();
            // newSingle.projectName = singleParam.name;

            if (ObjectUtils.isNotEmpty(singleParam.children)) {
                for (const item of singleParam.children) {
                    if (item.type === ProjectLevelConstant.single) {
                        await this._editSingleProjectStructure(constructId, importConstructId, item, newSingle, false, parentSame);
                    } else if (item.type === ProjectLevelConstant.unit) {
                        await this._editUnitStructure(constructId, importConstructId, item, newSingle, false, parentSame);
                    }
                }
            }

            //处理后的数据回填
            newSingle.unitProjects = newSingle.unitProjectsBak;
            newSingle.subSingleProjects = newSingle.subSingleProjectsBak;
            delete newSingle.unitProjectsBak;
            delete newSingle.subSingleProjectsBak;
        }
    }

    async _editUnitStructure(constructId, importConstructId,  unitParam, parent, ifParentIsConstruct, constructObj) {
        if (ObjectUtils.isNotEmpty(unitParam.import) && unitParam.import) {
            let oldUnit = ProjectDomain.getDomain(importConstructId).getProjectById(unitParam.id);
            //代表是复制的单位
            let newUnit = await ConvertUtil.deepCopy(oldUnit);
            let oldUnitId = newUnit.sequenceNbr;
            newUnit.sequenceNbr = Snowflake.nextId();
            newUnit.parentId = parent.sequenceNbr;
            newUnit.name = unitParam.name;

            newUnit.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
            newUnit.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
            //添加单位
            let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(newUnit.parentId);
            ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(newUnit, parentNode);
            //重新赋值分部分项等数据的unitId和spId
            await this.repeatInitUnitItemId(newUnit, oldUnitId, constructId, importConstructId);
            if (ifParentIsConstruct) {
                constructObj.unitProjectArrayBak.push(newUnit);
            } else {
                parent.unitProjectsBak.push(newUnit);
            }

            let projectModel = ProjectDomain.getDomain(constructId).getProjectById(newUnit.sequenceNbr);
            let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByLibraryCode(projectModel.constructMajorType);
            projectModel.qfMajorType = gsBaseFreeRate.qfCode;
            let qfMajorTypeMoneyMap = new Map();
            qfMajorTypeMoneyMap.set(gsBaseFreeRate.qfCode, 0);
            projectModel.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);

            //重新计算精度和市政养护的中修
            await this.againCalPrecisionSzyh(constructId, importConstructId, projectModel);
        } else {
            let newUnit = ProjectDomain.getDomain(constructId).getProjectById(unitParam.id);
            // 将处理后的unit加入父级中
            if (ifParentIsConstruct) {
                constructObj.unitProjectArrayBak.push(newUnit);
            } else {
                parent.unitProjectsBak.push(newUnit);
            }
        }
    }


    async repeatInitSingleItemId(constructId, importConstructId, newSingle) {
        let unitProjects = newSingle.childrenCopy.filter(o => o.type === ProjectLevelConstant.unit);
        let singleProjects = newSingle.childrenCopy.filter(o => o.type === ProjectLevelConstant.single);
        if (ObjectUtils.isNotEmpty(unitProjects)) {
            //单项下有单位
            for (let i = 0; i < unitProjects.length; i++) {
                let item = unitProjects[i];
                let oldUnitId = item.sequenceNbr;
                item.parentId = newSingle.sequenceNbr;
                item.sequenceNbr = Snowflake.nextId();
                item.defaultDeId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());
                item.defaultCsxmId = LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId());

                //重新赋值分部分项等数据的unitId
                await this.repeatInitUnitItemId(item, oldUnitId, constructId, importConstructId);

                // newSingle.children.push(item);
                //添加单位节点
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(item.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(item, parentNode);

                let projectModel = ProjectDomain.getDomain(constructId).getProjectById(item.sequenceNbr);
                let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseManageRateService.queryByLibraryCode(projectModel.constructMajorType);
                projectModel.qfMajorType = gsBaseFreeRate.qfCode;
                let qfMajorTypeMoneyMap = new Map();
                qfMajorTypeMoneyMap.set(gsBaseFreeRate.qfCode, 0);
                projectModel.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);

                //重新计算精度和市政养护的中修
                await this.againCalPrecisionSzyh(constructId, importConstructId, projectModel);
            }
        } else if (ObjectUtils.isNotEmpty(singleProjects)) {
            //单项下有子单项
            for (let i = 0; i < singleProjects.length; i++) {
                let item = singleProjects[i];
                let itemOld = await ConvertUtil.deepCopy(item);
                item.sequenceNbr = Snowflake.nextId();
                item.parentId = newSingle.sequenceNbr
                item.childrenCopy = item.children;

                //添加单项节点
                let parentNode = ProjectDomain.getDomain(constructId).ctx.treeProject.getNodeById(item.parentId);
                ProjectDomain.getDomain(constructId).ctx.treeProject.addNode(item, parentNode);

                //重新计算取费表单项的值
                await this.dealSingleQfData(constructId, importConstructId, itemOld, item);

                await this.repeatInitSingleItemId(constructId, importConstructId, item);
            }
        }
    }

    async againCalPrecisionSzyh(constructId, importConstructId, projectModel) {
        //判断是否需要重新计算精度
        let precisionImport = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(importConstructId);
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precisionImportStr = await this.toJsonYsfString(precisionImport);
        let precisionStr = await this.toJsonYsfString(precision);
        if (precisionImportStr !== precisionStr) {
            //重新计算小数点
            let unitProjectsCal = [];
            unitProjectsCal.push(projectModel);
            await this.service.gongLiaoJiProject.gljProjectService.repeatCalPrecision(constructId, unitProjectsCal);
        }

        //导入文件设置中修和当前文件设置中修不一致，需要刷新人材机消耗量*0.9（执行换算信息）
        if (projectModel.deLibrary === ConstantUtil.SZSSWXYHGC) {
            let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
            let setting = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
            let szssMediumRepair = ObjectUtils.isNotEmpty(setting.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR)) ? setting.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR) : false;

            let businessMapImp = ProjectDomain.getDomain(importConstructId).functionDataMap;
            let settingImp = businessMapImp.get(FunctionTypeConstants.PROJECT_SETTING);
            let szssMediumRepairImp = ObjectUtils.isNotEmpty(settingImp.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR)) ? settingImp.get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR) : false;

            if (szssMediumRepair !== szssMediumRepairImp) {
                let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
                let unitConversionMap = conversionMap?.get(projectModel.sequenceNbr);
                if (ObjectUtils.isNotEmpty(unitConversionMap)) {
                    for (const [deId, deConversion] of unitConversionMap) {
                        let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, projectModel.sequenceNbr, deId);
                        if (ObjectUtils.isEmpty(deModel) || deModel.libraryCode !== ConstantUtil.SZSSWXYHGC || ObjectUtils.isEmpty(deModel.standardId)) {
                            continue;
                        }

                        try {
                            if (szssMediumRepair) {
                                await this.service.gongLiaoJiProject.gljConversionInfoService.addInfoForSzsswxyhzxzx22(deModel);
                            } else {
                                await this.service.gongLiaoJiProject.gljConversionInfoService.deleteInfoForSzsswxyhzxzx22(deModel);
                            }
                        } catch (e) {
                            console.log("----------执行中修异常:" + e.message)
                        }
                    }
                }
            }
        }
    }


    async repeatInitUnitItemId(newUnit, oldUnitId, constructId, importConstructId) {
        let oldUnit = ProjectDomain.getDomain(importConstructId).getProjectById(oldUnitId);

        //拷贝预算书定额数据
        let oldIdNewIdMap = await this.repeatInitUnitYssDe(constructId,importConstructId, oldUnit, newUnit);
        //处理措施项目定额数据
        let oldIdNewIdCsxmMap = await this.repeatInitUnitCsxmDe(constructId,importConstructId, oldUnit, newUnit);
        //拷贝预算书人和措施项目材机数据
        let oldIdNewIdRcjMap = await this.repeatInitUnitYssResource(constructId,importConstructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap);
        //拷贝预算书定额数据下的initChildCodes替换新的人材机id
        await this.service.gongLiaoJiProject.gljProjectService.repeatInitUnitYssDeInitChildCodes(constructId, newUnit, oldIdNewIdRcjMap);


        //拷贝functionMap数据
        let functionDataMap = ProjectDomain.getDomain(importConstructId).functionDataMap;

        //复制单位新人材机缓存map
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
        if (ObjectUtils.isEmpty(objMap)) {
            businessMap.set(FunctionTypeConstants.RCJ_MEMORY, new Map());
        }
        //复制单位新建局部汇总map
        // let objMap1 = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL);
        // if (ObjectUtils.isEmpty(objMap1)) {
        //     businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL, new Map());
        // }
        //设置key
        let objMap2 = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
        if (ObjectUtils.isEmpty(objMap2)) {
            businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, []);
        }


        for (let item of functionDataMap) {
            if (ObjectUtils.isNotEmpty(item)) {
                if (item[0] === FunctionTypeConstants.JBXX_KEY) {
                    //基本信息
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);

                            let lastCommaIndex = key.lastIndexOf("-");
                            let result = key.substring(lastCommaIndex + 1);
                            if (result === FunctionTypeConstants.JBXX_KEY_TYPE_12) {

                            } else {
                                for (let jbxxItem of newVar) {
                                    if (jbxxItem.name === "工程名称") {
                                        jbxxItem.remark = newUnit.name;
                                    }
                                }
                            }

                            // await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_DLF_KEY) {
                    //独立费
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);

                            let oldIdNewIdDlfMap = new Map();
                            for (let item of newVar) {
                                let oldId = item.sequenceNbr;
                                item.sequenceNbr = Snowflake.nextId();
                                oldIdNewIdDlfMap.set(oldId, item.sequenceNbr);
                                if (ObjectUtils.isNotEmpty(item.parentId)) {
                                    item.parentId = oldIdNewIdDlfMap.get(item.parentId);
                                }
                            }

                            // await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_COST_SUMMARY) {
                    //费用汇总
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            await this.copyFunctionDataMap(constructId, newVar);
                            for (let item of newVar) {
                                item.unitId = newUnit.sequenceNbr;
                            }
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_COST_CODE) {
                    //费用代码
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_QFB) {
                    //单位取费表
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);

                            if (ObjectUtils.isEmpty(newVar.qfCode)) {
                                //历史文件出现取费表数据无qfCode属性，这种错误数据摈弃
                                continue;
                            }


                            let args = {};
                            args.libraryCode = oldVar.libraryCode;
                            args.type = ProjectTypeConstants.PROJECT_TYPE_UNIT;
                            args.freeFileOld = oldVar;
                            args.qfCode = oldVar.qfCode;
                            args.constructId = importConstructId;
                            args.unitId = oldUnit.sequenceNbr;
                            let freeRate = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);

                            newVar.unitId = newUnit.sequenceNbr;
                            newVar.constructId = constructId;

                            if(ObjectUtils.isNotEmpty(freeRate)){
                                if (ObjectUtils.isEmpty(newVar.manageFeeRateUpdate) || freeRate.manageFeeRate === newVar.manageFeeRate) {
                                    newVar.manageFeeRateUpdate = false;
                                }
                                if (ObjectUtils.isEmpty(newVar.profitRateUpdate) || freeRate.profitRate === newVar.profitRate) {
                                    newVar.profitRateUpdate = false;
                                }
                                if (ObjectUtils.isEmpty(newVar.taxRateUpdate) || freeRate.taxRate === newVar.taxRate) {
                                    newVar.taxRateUpdate = false;
                                }
                                if (ObjectUtils.isEmpty(newVar.anwenRateUpdate) || freeRate.anwenRate === newVar.anwenRate) {
                                    newVar.anwenRateUpdate = false;
                                }
                            }


                            // newVar.manageFeeRateUpdate = ObjectUtils.isEmpty(newVar.manageFeeRateUpdate) ? true : newVar.manageFeeRateUpdate;
                            // newVar.profitRateUpdate = ObjectUtils.isEmpty(newVar.profitRateUpdate) ? true : newVar.profitRateUpdate;
                            // newVar.taxRateUpdate = ObjectUtils.isEmpty(newVar.taxRateUpdate) ? true : newVar.taxRateUpdate;
                            // newVar.anwenRateUpdate = ObjectUtils.isEmpty(newVar.anwenRateUpdate) ? true : newVar.anwenRateUpdate;

                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB).set(keyNew, newVar);

                            //导入单位后同步到项目
                            let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
                            if (ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate)) {
                                freeRateProjectModel.childFreeRate = new Map();
                            }
                            if (ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate.get(newVar.qfCode))) {
                                //查询旧项目数据
                                let importQfbMap = functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
                                let importQfbDataQfCode = importQfbMap.childFreeRate.get(newVar.qfCode);
                                // importQfbDataQfCode.manageFeeRateUpdate = ObjectUtils.isEmpty(importQfbDataQfCode.manageFeeRateUpdate) ? true : importQfbDataQfCode.manageFeeRateUpdate;
                                // importQfbDataQfCode.profitRateUpdate = ObjectUtils.isEmpty(importQfbDataQfCode.profitRateUpdate) ? true : importQfbDataQfCode.profitRateUpdate;
                                // importQfbDataQfCode.taxRateUpdate = ObjectUtils.isEmpty(importQfbDataQfCode.taxRateUpdate) ? true : importQfbDataQfCode.taxRateUpdate;
                                // importQfbDataQfCode.anwenRateUpdate = ObjectUtils.isEmpty(importQfbDataQfCode.anwenRateUpdate) ? true : importQfbDataQfCode.anwenRateUpdate;

                                if (ObjectUtils.isNotEmpty(freeRate) && ObjectUtils.isNotEmpty(importQfbDataQfCode)) {
                                    if (ObjectUtils.isEmpty(importQfbDataQfCode.manageFeeRateUpdate) || freeRate.manageFeeRate === importQfbDataQfCode.manageFeeRate) {
                                        importQfbDataQfCode.manageFeeRateUpdate = false;
                                    }
                                    if (ObjectUtils.isEmpty(importQfbDataQfCode.profitRateUpdate) || freeRate.profitRate === importQfbDataQfCode.profitRate) {
                                        importQfbDataQfCode.profitRateUpdate = false;
                                    }
                                    if (ObjectUtils.isEmpty(importQfbDataQfCode.taxRateUpdate) || freeRate.taxRate === importQfbDataQfCode.taxRate) {
                                        importQfbDataQfCode.taxRateUpdate = false;
                                    }
                                    if (ObjectUtils.isEmpty(importQfbDataQfCode.anwenRateUpdate) || freeRate.anwenRate === importQfbDataQfCode.anwenRate) {
                                        importQfbDataQfCode.anwenRateUpdate = false;
                                    }
                                }

                                freeRateProjectModel.childFreeRate.set(newVar.qfCode, importQfbDataQfCode);
                                ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
                            }

                            //导入单位后同步到单项(这个只是上一层级的单项取费数据)
                            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
                            let freeKeySingle = WildcardMap.generateKey(newUnit.parentId, FunctionTypeConstants.SINGLE_QFB);
                            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle)) ? singleQfbMap.get(freeKeySingle) : {"childFreeRate": new Map()};
                            if (ObjectUtils.isEmpty(freeRateSingleModel.childFreeRate.get(newVar.qfCode))) {
                                freeRateSingleModel.childFreeRate.set(newVar.qfCode, newVar);
                                ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

                                //再迭代处理多层级单项的取费数据
                                let singleProject = ProjectDomain.getDomain(constructId).getProjectById(newUnit.parentId);
                                await this.dealLevelSingleFeeImport(constructId, singleProject.parentId, newVar);
                            }
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_FLSM) {
                    //单位取费表
                    let itemElementMap = item[1];
                    for (let key of itemElementMap.keys()) {
                        if (key.includes(oldUnitId)) {
                            let oldVar = itemElementMap.get(key);
                            let newVar = await ConvertUtil.deepCopy(oldVar);
                            newVar.unitId = newUnit.sequenceNbr;
                            newVar.constructId = constructId;
                            // await this.copyFunctionDataMap(constructId, newVar);
                            let keyNew = key.replace(oldUnitId, newUnit.sequenceNbr);
                            ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM).set(keyNew, newVar);
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.RCJ_MEMORY) {
                    //人材机缓存
                    let itemElementMap = item[1];
                    let oldVar = itemElementMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + importConstructId + FunctionTypeConstants.SEPARATOR + oldUnitId);
                    if (ObjectUtils.isNotEmpty(oldVar)) {
                        let newVar = await ConvertUtil.deepCopy(oldVar);
                        // await this.copyFunctionDataMap(constructId, newVar);
                        let keyNew = FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId + FunctionTypeConstants.SEPARATOR + newUnit.sequenceNbr;
                        for (let itemVar of newVar) {
                            itemVar.unitId = newUnit.sequenceNbr;
                            itemVar.sequenceNbr = Snowflake.nextId();
                            let newVar2 = oldIdNewIdMap.get(itemVar.parentId);
                            let newVar3 = oldIdNewIdRcjMap.get(itemVar.parentId);
                            itemVar.parentId = ObjectUtils.isNotEmpty(newVar2) ? newVar2 : newVar3;
                            itemVar.deRowId = oldIdNewIdMap.get(itemVar.deRowId);
                            itemVar.deId = oldIdNewIdMap.get(itemVar.deId);
                        }
                        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_MEMORY).set(keyNew, newVar);
                    }
                } else if (item[0] === FunctionTypeConstants.PROJECT_USER_RCJ) {
                    //补充人材机编码
                    let itemElementList = item[1];
                    if (ObjectUtils.isNotEmpty(itemElementList)) {
                        let newVar1 = await ConvertUtil.deepCopy(itemElementList);
                        for (let item of newVar1) {
                            item.unitId = newUnit.sequenceNbr;
                            if (oldIdNewIdMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdMap.get(item.deRowId);
                                item.deId = oldIdNewIdMap.get(item.deRowId);
                                item.parentId = oldIdNewIdMap.get(item.deRowId);
                            } else if (oldIdNewIdCsxmMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdCsxmMap.get(item.deRowId);
                                item.deId = oldIdNewIdCsxmMap.get(item.deRowId);
                                item.parentId = oldIdNewIdCsxmMap.get(item.deRowId);
                            }

                            if (oldIdNewIdRcjMap.get(item.sequenceNbr)) {
                                item.sequenceNbr = oldIdNewIdRcjMap.get(item.sequenceNbr);
                            }
                        }
                        let nowRcjUser = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
                        if (ObjectUtils.isEmpty(nowRcjUser)) {
                            nowRcjUser = [];
                        }
                        nowRcjUser = nowRcjUser.concat(newVar1);
                        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, nowRcjUser);
                    }
                } else if (item[0] === FunctionTypeConstants.UNIT_CONVERSION) {
                    //定额标准换算
                    let itemElementMap = item[1];
                    let oldUnitConversionMap = itemElementMap?.get(oldUnitId);
                    if (ObjectUtils.isNotEmpty(oldUnitConversionMap)) {
                        for (let key of oldUnitConversionMap.keys()) {
                            let newDeId;
                            if (oldIdNewIdMap.has(key)) {
                                newDeId = oldIdNewIdMap.get(key);
                            } else if (oldIdNewIdCsxmMap.has(key)) {
                                newDeId = oldIdNewIdCsxmMap.get(key);
                            }

                            let deConversionMapCopy = ConvertUtil.deepCopy(oldUnitConversionMap.get(key));
                            deConversionMapCopy.constructId = constructId;
                            deConversionMapCopy.unitId = newUnit.sequenceNbr;
                            deConversionMapCopy.deId = newDeId;
                            deConversionMapCopy.sequenceNbr = newDeId;
                            let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
                            let unitConversionMap = conversionMap?.get(newUnit.sequenceNbr);
                            if (ObjectUtils.isEmpty(unitConversionMap)) {
                                let unitConversionMapNew = new Map();
                                unitConversionMapNew.set(newDeId, deConversionMapCopy);
                                conversionMap.set(newUnit.sequenceNbr, unitConversionMapNew);
                            } else {
                                unitConversionMap.set(newDeId, deConversionMapCopy);
                            }
                        }
                    }
                } else if (item[0] === FunctionTypeConstants.YSH_GCL_EXP_NOTIFY) {
                    //定额引用工程规模的数据
                    let itemElementMap = item[1];
                    let oldGCLList = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                    if (ObjectUtils.isNotEmpty(oldGCLList)) {
                        for (let item of oldGCLList) {
                            item.unitId = newUnit.sequenceNbr;
                            if (oldIdNewIdMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdMap.get(item.deRowId);
                            } else if (oldIdNewIdCsxmMap.has(item.deRowId)) {
                                item.deRowId = oldIdNewIdCsxmMap.get(item.deRowId);
                            }
                        }
                        itemElementMap.set(newUnit.sequenceNbr, oldGCLList);
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY, itemElementMap);
                    }
                } else if (item[0] === FunctionTypeConstants.MAIN_MATERIAL_SETTING) {
                    //单位级别人材机主要材料设置数据
                    let itemElementMap = item[1];
                    let oldMainSettingObj = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                    if (ObjectUtils.isNotEmpty(oldMainSettingObj)) {
                        itemElementMap.set(newUnit.sequenceNbr, oldMainSettingObj);
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.MAIN_MATERIAL_SETTING, itemElementMap);
                    }
                // } else if(item[0] === FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL){
                //     //单位级别局部汇总
                //     let itemElementMap = item[1];
                //     let oldMainSettingObj = ConvertUtil.deepCopy(itemElementMap.get(oldUnitId));
                //     itemElementMap.set(newUnit.sequenceNbr, oldMainSettingObj);
                //     await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL, itemElementMap);
                // } else if (item[0] === FunctionTypeConstants.RCJ_COLLECT) {
                //     let itemElementMap = item[1];
                //     if (ObjectUtils.isNotEmpty(itemElementMap)) {
                //
                //         let nowRcjMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
                //         if(ObjectUtils.isEmpty(nowRcjMap)){
                //             nowRcjMap = new Map();
                //         }
                //
                //         for (const [key, value] of itemElementMap) {
                //             if (key.includes(oldUnitId)) {
                //                 let newKey = ConvertUtil.deepCopy(key).replace(oldUnitId, newUnit.sequenceNbr);
                //                 nowRcjMap.set(newKey, value);
                //             }
                //         }
                //         await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, nowRcjMap);
                //     }
                } else if (item[0] === FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA) {
                    let itemElementObj = item[1];
                    if (ObjectUtils.isNotEmpty(itemElementObj) && itemElementObj[oldUnitId]) {
                        let itemElementObjElement = itemElementObj[oldUnitId];
                        let itemElementObjElementCopy = ConvertUtil.deepCopy(itemElementObjElement);
                        itemElementObj[newUnit.sequenceNbr] = itemElementObjElementCopy;
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_WATER_ELECTRIC_COST_DATA, itemElementObj);
                    }
                } else if (item[0] === FunctionTypeConstants.RCJ_COLLECT) {
                    //单位人材机汇总排序、涂色
                    let itemElementMap1 = item[1];
                    let itemElementMap = ConvertUtil.deepCopy(itemElementMap1);
                    if (ObjectUtils.isEmpty(itemElementMap)) {
                        itemElementMap = new Map();
                    }

                    let itemElementMapNow = businessMap.get(FunctionTypeConstants.RCJ_COLLECT);
                    if (ObjectUtils.isEmpty(itemElementMapNow)) {
                        itemElementMapNow = new Map();
                    }

                    if (ObjectUtils.isNotEmpty(itemElementMap)) {
                        for (const [key, value] of itemElementMap) {
                            if (ObjectUtils.isNotEmpty(value)) {
                                if (value instanceof Map) {
                                    //单位人材机涂色
                                    if (ObjectUtils.isNotEmpty(value.get(oldUnit.sequenceNbr))) {
                                        let unitColorUnit = value.get(oldUnit.sequenceNbr);
                                        let unitColorUnitCopy = ConvertUtil.deepCopy(unitColorUnit);
                                        value.set(newUnit.sequenceNbr, unitColorUnitCopy);

                                        if (ObjectUtils.isNotEmpty(itemElementMapNow.get(key))) {
                                            itemElementMapNow.get(key).set(newUnit.sequenceNbr, unitColorUnitCopy);
                                        }

                                    }
                                } else if (value instanceof Array || value instanceof Object) {
                                    if (key.includes(oldUnit.sequenceNbr)) {
                                        //单位人材机排序、表格列设置
                                        let keyUnitNew = key.replace(oldUnit.sequenceNbr, newUnit.sequenceNbr);
                                        if (keyUnitNew.includes(importConstructId)) {
                                            keyUnitNew = keyUnitNew.replace(importConstructId, constructId);
                                        }
                                        let valueUnitCopy = ConvertUtil.deepCopy(value);
                                        itemElementMap.set(keyUnitNew, valueUnitCopy);
                                        itemElementMapNow.set(keyUnitNew, valueUnitCopy);
                                    }
                                    if (key.includes(oldUnit.parentId)) {
                                        //单项人材机排序、表格列设置
                                        let keySingleNew = key.replace(oldUnit.parentId, newUnit.parentId);
                                        if (keySingleNew.includes(importConstructId)) {
                                            keySingleNew = keySingleNew.replace(importConstructId, constructId);
                                        }
                                        let valueSingleCopy = ConvertUtil.deepCopy(value);
                                        itemElementMap.set(keySingleNew, valueSingleCopy);
                                        itemElementMapNow.set(keySingleNew, valueSingleCopy);
                                    }
                                }
                            }
                        }
                        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, itemElementMapNow);
                    }
                } else if (item[0] === FunctionTypeConstants.YSH_TABLELIST || item[0] === FunctionTypeConstants.UNIT_BGLSZ || item[0] === FunctionTypeConstants.DLF_TABLELIST
                    || item[0] === FunctionTypeConstants.SC_TABLELIST) {
                    //表格列设置
                    let itemElementMap = item[1];

                    let itemElementMapNow = businessMap.get(FunctionTypeConstants.TABLE_SETTING_CACHE);
                    if(ObjectUtils.isEmpty(itemElementMapNow)){
                        itemElementMapNow = new Map();
                    }

                    for (const [key, value] of itemElementMap) {
                        if (key.includes(oldUnit.sequenceNbr)) {
                            let tableListKey = key.replace(oldUnit.sequenceNbr, newUnit.sequenceNbr);
                            if(tableListKey.includes(importConstructId)){
                                tableListKey = tableListKey.replace(importConstructId, constructId);
                            }
                            itemElementMap.set(tableListKey, ConvertUtil.deepCopy(value));
                            itemElementMapNow[tableListKey] = ConvertUtil.deepCopy(value);
                            await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.TABLE_SETTING_CACHE, itemElementMapNow);
                        }
                    }
                }
            }
        }

        //拷贝定额工程量明细
        await this.repeatInitUnitQuantities1(constructId, importConstructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap);
    }



    /**
     * 拷贝定额工程量明细
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @param oldIdNewIdMap
     * @returns {Promise<void>}
     */
    async repeatInitUnitQuantities1(constructId, importConstructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap) {
        //处理定额工程量明细
        let quantitiesMap = ProjectDomain.getDomain(importConstructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let oldUnitQuantiesMap = quantitiesMap.get(oldUnit.sequenceNbr);
        if (ObjectUtils.isNotEmpty(oldUnitQuantiesMap)) {
            let oldMapCopy = await ConvertUtil.deepCopy(oldUnitQuantiesMap);
            let keysToDelete = [];

            oldMapCopy.forEach((value, oldkey) => {
                if (oldIdNewIdMap.has(oldkey)) {
                    keysToDelete.push(oldkey);
                    let newkey = oldIdNewIdMap.get(oldkey);
                    value.constructId = constructId;
                    value.unitId = newUnit.sequenceNbr;
                    value.quotaListId = newkey;
                    if (ObjectUtils.isNotEmpty(value.quantities)) {
                        value.quantities.forEach(o => {
                            o.quotaListId = newkey;
                        });
                    }
                    oldMapCopy.set(newkey, value);
                } else if (oldIdNewIdCsxmMap.has(oldkey)) {
                    keysToDelete.push(oldkey);
                    let newkey = oldIdNewIdCsxmMap.get(oldkey);
                    value.constructId = constructId;
                    value.unitId = newUnit.sequenceNbr;
                    value.quotaListId = newkey;
                    if (ObjectUtils.isNotEmpty(value.quantities)) {
                        value.quantities.forEach(o => {
                            o.quotaListId = newkey;
                        });
                    }
                    oldMapCopy.set(newkey, value);
                }
            });
            keysToDelete.forEach(key => {
                oldMapCopy.delete(key);
            });

            let quantitiesMapNow = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
            quantitiesMapNow.set(newUnit.sequenceNbr, oldMapCopy);
        }
    }




    /**
     * 处理单项层级取费费率
     * @param constructId
     * @param singleId
     * @param freeRateUnitModel
     * @returns {Promise<void>}
     */
    async dealLevelSingleFeeImport(constructId, singleId, freeRateUnitModel) {
        let singleProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);

        if (ObjectUtils.isNotEmpty(singleProject) && singleProject.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeKeySingle = WildcardMap.generateKey(singleProject.parentId, FunctionTypeConstants.SINGLE_QFB);
            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle)) ? singleQfbMap.get(freeKeySingle) : {"childFreeRate": new Map()};
            if (ObjectUtils.isEmpty(freeRateSingleModel.childFreeRate.get(freeRateUnitModel.qfCode))) {
                freeRateSingleModel.childFreeRate.set(freeRateUnitModel.qfCode, freeRateUnitModel);
                ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));
            }

            await this.dealLevelSingleFeeImport(constructId, singleProject.parentId, freeRateUnitModel);
        }
    }



    /**
     * 拷贝预算书定额数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssDe(constructId, importConstructId, oldUnit, newUnit) {
        let oldDeAll = ProjectDomain.getDomain(importConstructId).deDomain.ctx.deMap.getNodeById(oldUnit.defaultDeId);
        let oldDeAllCopy = await ConvertUtil.deepCopy(oldDeAll);

        oldDeAllCopy.sequenceNbr = newUnit.defaultDeId;
        oldDeAllCopy.deRowId = newUnit.defaultDeId;
        this.updatePropertyValue(oldDeAllCopy, 'unitId', newUnit.sequenceNbr);
        this.updatePropertyValue(oldDeAllCopy, 'constructId', constructId);
        ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.addNode(oldDeAllCopy);
        let oldIdNewIdMap = new Map();
        oldIdNewIdMap = await this.copyDeData(constructId, oldDeAllCopy, oldIdNewIdMap);
        return oldIdNewIdMap;
    }

    /**
     * 拷贝措施项目定额数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitCsxmDe(constructId,importConstructId, oldUnit, newUnit) {
        let oldDeAll = ProjectDomain.getDomain(importConstructId).csxmDomain.ctx.csxmMap.getNodeById(oldUnit.defaultCsxmId);
        let oldDeAllCopy = await ConvertUtil.deepCopy(oldDeAll);

        oldDeAllCopy.sequenceNbr = newUnit.defaultCsxmId;
        oldDeAllCopy.deRowId = newUnit.defaultCsxmId;
        this.updatePropertyValue(oldDeAllCopy, 'unitId', newUnit.sequenceNbr);
        this.updatePropertyValue(oldDeAllCopy, 'constructId', constructId);
        ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.addNode(oldDeAllCopy);
        let oldIdNewIdMap = new Map();
        oldIdNewIdMap = await this.copyCsxmData(constructId, oldDeAllCopy, oldIdNewIdMap);
        return oldIdNewIdMap;
    }

    async copyDeData(constructId, oldDeAllCopy, oldIdNewIdMap) {
        if (ObjectUtils.isNotEmpty(oldDeAllCopy.children)) {
            for (const o of oldDeAllCopy.children) {
                let oldId = o.sequenceNbr;
                o.sequenceNbr = Snowflake.nextId();
                o.deRowId = o.sequenceNbr;
                o.parentId = oldDeAllCopy.sequenceNbr;
                oldIdNewIdMap.set(oldId, o.sequenceNbr);
                ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.nodeMap.set(o.sequenceNbr, o); // 将节点添加到节点映射表中

                if (ObjectUtils.isNotEmpty(o.children)) {
                    oldIdNewIdMap = await this.copyDeData(constructId, o, oldIdNewIdMap);
                }
            }
        }
        return oldIdNewIdMap;
    }

    async copyCsxmData(constructId, oldDeAllCopy, oldIdNewIdMap) {
        if (ObjectUtils.isNotEmpty(oldDeAllCopy.children)) {
            for (const o of oldDeAllCopy.children) {
                let oldId = o.sequenceNbr;
                o.sequenceNbr = Snowflake.nextId();
                o.deRowId = o.sequenceNbr;
                o.parentId = oldDeAllCopy.sequenceNbr;
                oldIdNewIdMap.set(oldId, o.sequenceNbr);
                ProjectDomain.getDomain(constructId).csxmDomain.ctx.csxmMap.nodeMap.set(o.sequenceNbr, o); // 将节点添加到节点映射表中

                if (ObjectUtils.isNotEmpty(o.children)) {
                    oldIdNewIdMap = await this.copyCsxmData(constructId, o, oldIdNewIdMap);
                }
            }
        }
        return oldIdNewIdMap;
    }


    /**
     * 拷贝预算书人材机数据
     * @param constructId
     * @param oldUnit
     * @param newUnit
     * @returns {Promise<Map<any, any>>}
     */
    async repeatInitUnitYssResource(constructId,importConstructId, oldUnit, newUnit, oldIdNewIdMap, oldIdNewIdCsxmMap) {
        let oldIdNewIdRcjMap = new Map();
        let rcjKey = WildcardMap.generateKey(oldUnit.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjList = ProjectDomain.getDomain(importConstructId).resourceDomain.getResource(rcjKey);
        if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let item of rcjList) {
                let itemNew = await ConvertUtil.deepCopy(item);
                this.updatePropertyValue(itemNew, 'unitId', newUnit.sequenceNbr);
                let deRowIdOld = itemNew.deRowId;
                if (oldIdNewIdMap.has(deRowIdOld)) {
                    itemNew.deRowId = oldIdNewIdMap.get(deRowIdOld);
                } else if (oldIdNewIdCsxmMap.has(deRowIdOld)) {
                    itemNew.deRowId = oldIdNewIdCsxmMap.get(deRowIdOld);
                }
                itemNew.deId = itemNew.deRowId;
                itemNew.parentId = itemNew.deRowId;
                itemNew.sequenceNbr = Snowflake.nextId();
                itemNew.constructId = constructId;

                //处理人材机单项批注
                if (ObjectUtils.isNotEmpty(itemNew.annotationsSingleObj)) {
                    if (oldUnit.parentId != newUnit.parentId) {
                        let annotationsSingleObjElement = itemNew.annotationsSingleObj[oldUnit.parentId];
                        if (ObjectUtils.isNotEmpty(annotationsSingleObjElement)) {
                            itemNew.annotationsSingleObj[newUnit.parentId] = ConvertUtil.deepCopy(annotationsSingleObjElement);
                        }
                    }
                }

                if (ObjectUtils.isNotEmpty(itemNew.pbs)) {
                    itemNew.pbs.forEach(m => {
                        m.parentId = itemNew.sequenceNbr;
                        m.constructId = constructId;
                    });
                }

                oldIdNewIdRcjMap.set(item.sequenceNbr, itemNew.sequenceNbr);
                ProjectDomain.getDomain(constructId).resourceDomain.ctx.resourceMap.set(WildcardMap.generateKey(newUnit.sequenceNbr, itemNew.deRowId, itemNew.sequenceNbr), itemNew);
            }
        }
        return oldIdNewIdRcjMap;
    }



    async copyFunctionDataMap(constructId, obj) {
        if (Array.isArray(obj)) {
            //Array
            if (ObjectUtils.isNotEmpty(obj)) {
                for (let o of obj) {
                    o.prentId = obj.sequenceNbr;
                    o.parentId = obj.sequenceNbr;
                    o = await this.copyFunctionDataMap(constructId, o);
                }
            }
        } else {
            //object
            obj.sequenceNbr = Snowflake.nextId();
            if (ObjectUtils.isNotEmpty(obj.children)) {
                for (let o of obj.children) {
                    o = await this.copyFunctionDataMap(constructId, o);
                }
            }
        }
        return obj;
    }

    async dealSingleQfData(constructId, importConstructId, oldSingle, newSingle) {
        let singleQfbMap = ProjectDomain.getDomain(importConstructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let oldSingleFeeKey = WildcardMap.generateKey(oldSingle.sequenceNbr, FunctionTypeConstants.SINGLE_QFB);
        let oldSingleFeeY = singleQfbMap.get(oldSingleFeeKey);
        let oldSingleFee = await ConvertUtil.deepCopy(oldSingleFeeY);
        if (ObjectUtils.isNotEmpty(oldSingleFee.childFreeRate)) {
            for (let [key, value] of oldSingleFee.childFreeRate) {
                let args = {};
                args.libraryCode = value.libraryCode;
                args.type = ProjectTypeConstants.PROJECT_TYPE_SINGLE;
                args.freeFileOld = value;
                args.qfCode = value.qfCode;
                args.constructId = importConstructId;
                args.singleId = oldSingle.sequenceNbr;
                let freeRate = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);
                if (ObjectUtils.isNotEmpty(freeRate)) {
                    if (ObjectUtils.isEmpty(value.manageFeeRateUpdate) || freeRate.manageFeeRate === value.manageFeeRate) {
                        value.manageFeeRateUpdate = false;
                    }
                    if (ObjectUtils.isEmpty(value.profitRateUpdate) || freeRate.profitRate === value.profitRate) {
                        value.profitRateUpdate = false;
                    }
                    if (ObjectUtils.isEmpty(value.taxRateUpdate) || freeRate.taxRate === value.taxRate) {
                        value.taxRateUpdate = false;
                    }
                    if (ObjectUtils.isEmpty(value.anwenRateUpdate) || freeRate.anwenRate === value.anwenRate) {
                        value.anwenRateUpdate = false;
                    }
                }
            }
        }

        oldSingleFee.singleId = newSingle.sequenceNbr;
        oldSingleFee.constructId = constructId;
        let newSingleFeeKey = WildcardMap.generateKey(newSingle.sequenceNbr, FunctionTypeConstants.SINGLE_QFB);

        let singleQfbMapNow = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMapNow.set(newSingleFeeKey, oldSingleFee));



        let singleQfbMap1 = ProjectDomain.getDomain(importConstructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
        let oldSingleFeeKey1 = WildcardMap.generateKey(oldSingle.sequenceNbr, FunctionTypeConstants.SINGLE_FLSM);
        let oldSingleFee1Y = singleQfbMap1.get(oldSingleFeeKey1);
        let oldSingleFee1 = await ConvertUtil.deepCopy(oldSingleFee1Y);
        oldSingleFee1.singleId = newSingle.sequenceNbr;
        oldSingleFee1.constructId = constructId;
        let newSingleFeeKey1 = WildcardMap.generateKey(newSingle.sequenceNbr, FunctionTypeConstants.SINGLE_FLSM);

        let newSingleFeeKeyNow1 = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_FLSM, newSingleFeeKeyNow1.set(newSingleFeeKey1, oldSingleFee1));


        let importRcjSortMap = ProjectDomain.getDomain(importConstructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
        let rcjSortMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.RCJ_COLLECT);
        if (ObjectUtils.isEmpty(rcjSortMap)) {
            rcjSortMap = new Map();
        }
        if (ObjectUtils.isNotEmpty(importRcjSortMap)) {
            for (const [key, value] of importRcjSortMap) {
                if (key.includes(oldSingle.sequenceNbr)) {
                    let newKey = ConvertUtil.deepCopy(key).replace(oldSingle.sequenceNbr, newSingle.sequenceNbr);
                    rcjSortMap.set(newKey, value);
                }
            }
        }
        await ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.RCJ_COLLECT, rcjSortMap);
    }

    updatePropertyValue(obj, propertyKey, newValue) {
        for (let key in obj) {
            if (key == "parent" || key == "prev" || key == "next") {
                continue;
            }

            if (typeof obj[key] === 'object') {
                if (Array.isArray(obj[key])) {
                    // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
                    obj[key].forEach((item) => {
                        this.updatePropertyValue(item, propertyKey, newValue);
                    });
                } else {
                    // 如果属性的值是对象，则递归调用更新函数
                    this.updatePropertyValue(obj[key], propertyKey, newValue);
                }
            } else if (key === propertyKey && !ObjectUtils.isEmpty(obj[key])) {
                // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
                obj[key] = newValue;
            }
        }
    }

    async copyDeData(constructId, oldDeAllCopy, oldIdNewIdMap) {
        if (ObjectUtils.isNotEmpty(oldDeAllCopy.children)) {
            for (const o of oldDeAllCopy.children) {
                let oldId = o.sequenceNbr;
                o.sequenceNbr = Snowflake.nextId();
                o.deRowId = o.sequenceNbr;
                o.parentId = oldDeAllCopy.sequenceNbr;
                oldIdNewIdMap.set(oldId, o.sequenceNbr);
                ProjectDomain.getDomain(constructId).deDomain.ctx.deMap.nodeMap.set(o.sequenceNbr, o); // 将节点添加到节点映射表中

                if (ObjectUtils.isNotEmpty(o.children)) {
                    oldIdNewIdMap = await this.copyDeData(constructId, o, oldIdNewIdMap);
                }
            }
        }
        return oldIdNewIdMap;
    }

    async exportGljFile(args) {
        let {name, id} = args;
        name = name + "（导出项目）";
        let defaultStoragePath = await this.service.gongLiaoJiProject.gljAppService.getSetStoragePath(name);
        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.toString(),
            filters: [{name: '云算房文件', extensions: [CommonConstants.GAISUAN_FILE_SUFFIX]}],
        };
        let filePath = dialog.showSaveDialogSync(null, dialogOptions);

        if (filePath && !filePath.canceled) {
            if (!filePath.toUpperCase().endsWith(CommonConstants.GAISUAN__FILE_DOT_SUFFIX)) {
                filePath += CommonConstants.GAISUAN__FILE_DOT_SUFFIX;
            }
            //查询选择的路径是否已经有被打开的文件
            // let result = await this.service.constructProjectFileService.getOneProDataByPath(filePath);
            // if (!ObjectUtils.isEmpty(result)) {
            //     let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
            //     if (!ObjectUtils.isEmpty(projectObj)) {
            //         return ResponseData.success(2);
            //     }
            // }

            if (fs.existsSync(filePath)) {
                let obj = await PricingFileFindUtils.getProjectObjByPath(filePath);
                if (!ObjectUtils.isEmpty(obj)) {
                    let projectObj = ProjectDomain.getDomain(obj.ProjectTree[0].sequenceNbr).getRoot();
                    if (!ObjectUtils.isEmpty(projectObj)) {
                        return ResponseData.success(2);
                    }
                }
            }
            //导出的ysf
            let copyObj = await this.exportGljHandler(args, filePath, null);
            await YGLJOperator.writeFileWithPath1(copyObj);
            AppContext.removeContext(copyObj.sequenceNbr);

            return ResponseData.success(1);
        }
        return ResponseData.success(0);
    }

    async exportGljHandler(args, filePath, newConstructId) {
        let filePathName = filePath.slice(filePath.lastIndexOf("\\") + 1, filePath.length);
        if (filePathName.includes(".")) {
            filePathName = filePathName.split(".")[0];
        }


        //获取到内存中的原始项目
        let projectDomainOld = ProjectDomain.getDomain(args.id);
        let jsonObjOld = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomainOld));
        //拷贝一份作为导出新项目
        let jsonObjNew = ConvertUtil.deepCopy(jsonObjOld);
        jsonObjNew.path = filePath;
        //重新刷新所有的项目ID
        let constructId = ObjectUtils.isEmpty(newConstructId)?Snowflake.nextId():newConstructId;
        jsonObjNew.sequenceNbr = constructId;
        jsonObjNew.ProjectTree[0].sequenceNbr = constructId;
        jsonObjNew.ProjectTree[0].name = filePathName;
        jsonObjNew.ProjectTree[0].path = filePath;
        const regex = new RegExp(args.id, 'g');
        jsonObjNew = JSON.parse(JSON.stringify(jsonObjNew).replace(regex, constructId));
        let projectDomain = await YGSOperator.destructuringFile(jsonObjNew);

        let arr = [];
        this.getUnitList(arr, args);
        //去除单位后对象
        let projectUnits = projectDomain.getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT || item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE);
        if (!ObjectUtils.isEmpty(projectUnits)) {
            let unitIdList = arr.map(k => k.id);
            for (let item of projectUnits) {
                if (!unitIdList.includes(item.sequenceNbr)) {
                    await ProjectDomain.getDomain(constructId).removeProject(item.sequenceNbr);
                }
            }
        }

        let projectDomainResult = ProjectDomain.getDomain(constructId);

        let porjectJbxxMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.JBXX_KEY);
        let porjectJbxxList = porjectJbxxMap.get(await this.service.gongLiaoJiProject.gljOverviewService.getDataMapKey(null, FunctionTypeConstants.JBXX_KEY_TYPE_11));
        if (ObjectUtils.isNotEmpty(porjectJbxxList)) {
            for (let i = 0; i < porjectJbxxList.length; i++) {
                if (porjectJbxxList[i].name === "项目名称") {
                    porjectJbxxList[i].remark = filePathName;
                    break;
                }
            }
        }

        let jsonObjResult = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomainResult));
        jsonObjResult.path = filePath;
        jsonObjResult.sequenceNbr = constructId;
        return jsonObjResult;
    }

    getUnitList(arr, args) {
        let children = args.children;
        if (ObjectUtils.isEmpty(children)) {
            return;
        }
        for (const item of children) {
            if (item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT && item.selected) {
                arr.push(item);
            } else if (item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE && item.selected) {
                arr.push(item);
                this.getUnitList(arr, item);
            } else {
                this.getUnitList(arr, item);
            }
        }
    }

    getSelected(args) {
        let children = args.children;
        if (!ObjectUtils.isEmpty(children)) {
            for (let i = children.length - 1; i >= 0; i--) {
                if (!children[i].selected) {
                    children.splice(i, 1);
                } else {
                    let delFlag = this.getSelected(children[i]);
                }

            }
        }
    }

    /**
     * 所有页签缓存
     * @param args
     * @returns {Promise<void>}
     */
    async getTableSettingCache(args) {
        let {constructId, currentId} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let tableSettingObj = businessMap.get(FunctionTypeConstants.TABLE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(tableSettingObj)) {
            return null;
        }
        if (ObjectUtils.isEmpty(currentId)) {
            currentId = tableSettingObj.currentId;
        }
        return tableSettingObj[currentId];
    }

    /**
     * 设置所有页签缓存
     * @param args
     * @returns {Promise<void>}
     */
    async setTableSettingCache(args) {
        let {constructId, tableSetting, currentId} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let tableSettingObj = businessMap.get(FunctionTypeConstants.TABLE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(tableSettingObj)) {
            tableSettingObj = {}
        }
        if (ObjectUtils.isNotEmpty(currentId)) {
            tableSettingObj.currentId = currentId
        }
        if (ObjectUtils.isNotEmpty(tableSetting)) {
            tableSettingObj[tableSetting.selLeftTreeId] = tableSetting;
        }
        businessMap.set(FunctionTypeConstants.TABLE_SETTING_CACHE, tableSettingObj);
    }

    /**
     * 定额操作缓存
     * @param args
     * @returns {Promise<void>}
     */
    async getDeSettingCache(args) {
        let {constructId, sequenceNbr} = args;
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let deSettingObj = businessMap.get(FunctionTypeConstants.DE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(deSettingObj)) {
            return null;
        }
        if (ObjectUtils.isEmpty(sequenceNbr)) {
            sequenceNbr = constructId;
        }
        return deSettingObj[sequenceNbr];
    }

    /**
     * 获取精度设置
     * @returns {Promise<void>}
     */
    getDefaultPrecisionSetting() {
        return ConvertUtil.deepCopy(GljPrecisionSetting);
    }

    /**
     * 获取精度设置
     * @param constructId
     * @returns {Promise<void>}
     */
    getPrecisionSetting(constructId) {
        if (ObjectUtils.isEmpty(constructId)) {
            return ConvertUtil.deepCopy(GljPrecisionSetting);
        }
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let precisionSettingObj = businessMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
        if (ObjectUtils.isEmpty(precisionSettingObj)) {
            precisionSettingObj = ConvertUtil.deepCopy(GljPrecisionSetting)
            businessMap.set(FunctionTypeConstants.PROJECT_PRECISION_SETTING, precisionSettingObj);
        } else if (ObjectUtils.isEmpty(precisionSettingObj?.PRECISION_SETTING_UI)) {
            precisionSettingObj.PRECISION_SETTING_UI = ConvertUtil.deepCopy(GljPrecisionSetting)?.PRECISION_SETTING_UI;
            businessMap.set(FunctionTypeConstants.PROJECT_PRECISION_SETTING, precisionSettingObj);
        }
        return ConvertUtil.deepCopy(precisionSettingObj);
    }

    /**
     * 精度设置
     * @param constructId
     * @param precisionSetting
     * @returns {Promise<void>}
     */
    async setPrecisionSetting(constructId, precisionSetting) {
        if (ObjectUtils.isEmpty(constructId)) {
            return;
        }
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let precisionSettingObj = businessMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
        if (ObjectUtils.isNotEmpty(precisionSettingObj)) {
            let new_totalNumber = precisionSetting.RCJ.totalNumber;
            let new_quantitySelect = precisionSetting.DE.quantitySelect;
            let new_quantity = precisionSetting.DE.quantity;
            let new_unitQuantity = precisionSetting.DE.unitQuantity;
            let new_freeRate = precisionSetting.FREE_RATE.freeRate;
            let old_totalNumber = precisionSettingObj.PRECISION_SETTING_UI.RCJ.totalNumber;
            let old_quantitySelect = precisionSettingObj.PRECISION_SETTING_UI.DE.quantitySelect;
            let old_quantity = precisionSettingObj.PRECISION_SETTING_UI.DE.quantity;
            let old_unitQuantity = precisionSettingObj.PRECISION_SETTING_UI.DE.unitQuantity;
            let old_freeRate = precisionSettingObj.PRECISION_SETTING_UI.FREE_RATE.freeRate;
            precisionSettingObj.PRECISION_SETTING_UI = precisionSetting;

            // 更新精度
            // 人材机 - 数量精度统一设置
            if (ObjectUtils.isNotEmpty(new_totalNumber)) {
                precisionSettingObj.DETAIL.RCJ.totalNumber = new_totalNumber;
                precisionSettingObj.RCJ_COLLECT.totalNumber = new_totalNumber;
            }

            // 定额 - 工程量精度统一设置
            if (ObjectUtils.isNotEmpty(new_quantity)) {
                precisionSettingObj.EDIT.DE.quantity = new_quantity; // 工程量 - 定额
                precisionSettingObj.EDIT.DERCJ.quantity = new_quantity; // 工程量 - 定额人材机
                precisionSettingObj.EDIT.DEZS.quantity = new_quantity; // 工程量 - 定额主材/设备
            }
            if (new_quantitySelect === 'unitQuantity') {
                precisionSettingObj.EDIT.DE.isUnitQuantity = true;
                precisionSettingObj.EDIT.DE.unitQuantity = ConvertUtil.deepCopy(new_unitQuantity);
            } else {
                precisionSettingObj.EDIT.DE.isUnitQuantity = false;
            }

            // 费率 - 费率精度统一设置
            if (ObjectUtils.isNotEmpty(new_freeRate)) {
                precisionSettingObj.FREE_RATE.manageFeeRate = new_freeRate;
                precisionSettingObj.FREE_RATE.profitRate = new_freeRate;
                precisionSettingObj.FREE_RATE.taxRate = new_freeRate;
                precisionSettingObj.FREE_RATE.anwenRate = new_freeRate;

                precisionSettingObj.COST_SUMMARY.awf = new_freeRate;
                precisionSettingObj.COST_SUMMARY.jqsdf = new_freeRate;
                precisionSettingObj.COST_SUMMARY.SUMMARY.freeRate = new_freeRate;
                precisionSettingObj.COST_SUMMARY.AWMX.freeRate = new_freeRate;
                precisionSettingObj.COST_SUMMARY.JQSD.kcRate = new_freeRate;
            }


            if (new_totalNumber !== old_totalNumber
                || new_quantitySelect !== old_quantitySelect
                || new_quantity !== old_quantity
                || new_unitQuantity !== old_unitQuantity
                || new_freeRate !== old_freeRate
            ) {
                
                // 所有单项工程
                let singleList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectSingleAll(constructId);
                // 所有单位工程
                let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectUnitAll(constructId);
                // 精度重新计算
                await this.recalculatePrecision(constructId, singleList, unitList, precisionSettingObj);
                
                //--------- 重新计算工程量------start------
                
                //重新计算工程量及数据
                let projectDomain = ProjectDomain.getDomain(constructId);
                let ctx = projectDomain.ctx;
                let functionDataMap = projectDomain.functionDataMap;
                let allNodes = ctx.allDeMap.getAllNodes();
                let changeQuantityNodes = [];
                //数量变化 不处理  工程量 部署
                if (new_quantitySelect !== old_quantitySelect
                || new_quantity !== old_quantity
                || new_unitQuantity !== old_unitQuantity) {
                
                    let parentTypes = [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DELIST];
                    changeQuantityNodes = allNodes.filter(item=>{
                        if(!parentTypes.includes(item.type)){
                            if(item.parent && parentTypes.includes(item.parent.type)){
                                return true;
                            }
                        }
                        return false;
                    });
                }

                for (let unit of unitList) {
                    let deBaseDomain = projectDomain.getDeDomain();

                    //单位下有需要变更的数据  
                    let unitChangeNodes = changeQuantityNodes.filter(item=>item.unitId === unit.sequenceNbr);
                    if(ObjectUtils.isNotEmpty(unitChangeNodes)){
                        let priceCodes = await deBaseDomain.getQuantityExpressionCodes(constructId,unit.sequenceNbr,functionDataMap);

                        for(let changeNode of unitChangeNodes){
                            let unitPriceCodes = priceCodes?[...priceCodes]:[];
                            //增加定额费用代码
                            let  unitNbr = UnitUtils.removeCharter(changeNode.unit);
                            await this.service.gongLiaoJiProject.gljDeService.addPriceCodes(constructId, unit.sequenceNbr, changeNode.sequenceNbr, unitPriceCodes);
                            let newQuantity = DeQualityUtils.evalQualityWithCodes(changeNode.quantityExpression, unitPriceCodes);
                            let digital = DeUtils.getQuantiyPrecision(precisionSettingObj,changeNode);
                            if(ObjectUtils.isNotEmpty(unitNbr))
                            {
                                changeNode.quantity = NumberUtil.numberFormat(NumberUtil.divide(newQuantity,unitNbr),digital);
                            }
                            else {
                                changeNode.quantity =  NumberUtil.numberFormat(newQuantity,digital);
                            }
                            let df = DeFlattener.getInstance(changeNode, ctx,true,unitPriceCodes,functionDataMap);
                            await df.analyze();
                            //重新计算当前定额或者清单下所有人材机
                            for(let relatedDeDeRow of df.relatedDeRows){
                                //只有定额下有人才机明细需要重新计算 而清单不用
                                if(ObjectUtils.isNotEmpty(relatedDeDeRow) && (relatedDeDeRow.type != DeTypeConstants.DE_TYPE_ZFB
                                && relatedDeDeRow.type != DeTypeConstants.DE_TYPE_FB
                                && relatedDeDeRow.type != DeTypeConstants.DE_TYPE_DEFAULT )) {
                                    //如果人材机存在数量锁定，先更新消耗量
                                    let rcjDeKey = WildcardMap.generateKey(unit.sequenceNbr, relatedDeDeRow.sequenceNbr) + WildcardMap.WILDCARD;
                                    let rcjlist =  ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjDeKey);
                                    let rcjDetails = rcjlist.filter(item=>item.isNumLock);
                                    for(let rcjDetail of rcjDetails){
                                        await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail({
                                            constructId,
                                            singleId:null,
                                            unitId: unit.sequenceNbr,
                                            deId: relatedDeDeRow.sequenceNbr,
                                            rcjDetailId:rcjDetail.sequenceNbr,
                                            constructRcj:{
                                                resQty : NumberUtil.divide(rcjDetail.totalNumber, relatedDeDeRow.quantity)
                                            }
                                        });
                                    }
                                    //1.重新计算人材机
                                    let rc = ResourceCalculator.getInstance({constructId: relatedDeDeRow.constructId, unitId: relatedDeDeRow.unitId, deRowId:relatedDeDeRow.sequenceNbr}, ctx);
                                    await rc.analyze();
                                }
                            }
                        }               
                    }
                    await deBaseDomain.notifyAll(constructId,unit.sequenceNbr);
                }
                 //--------- 重新计算工程量------end------
            }
        }
    }

    /**
     * 精度重新计算
     * @param constructId
     * @returns {Promise<void>}
     */
    async recalculatePrecision(constructId,singleList, unitList, precision) {
        
        for (let single of singleList) {
        }
        for (let unit of unitList) {
            // TODO 各业务精度重新计算
            //重新计算独立费
            await this.service.gongLiaoJiProject.gljProjectService.calDlf(constructId, unit, precision.UNIT_DLF);

            // 重新计算费用汇总
            try {
                await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: unit.parentId,
                    unitId: unit.sequenceNbr,
                    qfMajorType: null
                });

            } catch (error) {
                console.error("捕获到异常:", error);
            }

        }
    }

    /**
     * 设置定额操作缓存
     * @param args
     * @returns {Promise<void>}
     */
    async setDeSettingCache(args) {
        let {constructId, operateCache, sequenceNbr} = args;
        if (ObjectUtils.isEmpty(sequenceNbr)) {
            sequenceNbr = constructId
        }
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let deSettingObj = businessMap.get(FunctionTypeConstants.DE_SETTING_CACHE);
        if (ObjectUtils.isEmpty(deSettingObj)) {
            deSettingObj = {}
        }
        if (ObjectUtils.isNotEmpty(operateCache)) {
            deSettingObj[sequenceNbr] = operateCache;
        }

        businessMap.set(FunctionTypeConstants.DE_SETTING_CACHE, deSettingObj);
    }


    /**
     * 设置定额操作缓存
     * @param args
     * @param bussnissId
     *  我文件管理-文件下载鲁行          ：11
     *  我文件管理-默认数据存储路径       ：12
     *  便捷性设置-单位工程标准换算痰喘    ：21
     *  便捷性设置-按不含税市场价组价      ：22
     *  便捷性设置-展示定额关联子目        ：23
     *  便捷性设置-展示未计价材料         ：24
     *  计算设置-主材设备计取价差        ：31
     *  计算设置-补充人材机计取价差       ：32
     *  计算设置-主材设备受系数调整影响    ：33
     *  其他设置-文件定时存储            ：41
     *  地区特性-市政设施执行中修         ：51
     *  地区特性-税率可编辑              ：52
     *  计算精度设置                    ：61
     * @returns {Promise<void>}
     */
    async allScopeSetting(args) {
         // args = new Map(Object.entries(args));
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_11))){
           await this.setSelectFolder(args.get(GsProjectSettingEnum.BUSSNISSID_11));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_12))){
            await  this.setSelectFolder(args.get(GsProjectSettingEnum.BUSSNISSID_12));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_21))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_21));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_22))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_22));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_23))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_23));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_24))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_24));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_31))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_31));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_32))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_32));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_33))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_33));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_41))){
            await  this.setSaveTime(args.get(GsProjectSettingEnum.BUSSNISSID_41));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_51))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_51));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_52))){
            await  this.saveProjectSetting(args.get(GsProjectSettingEnum.BUSSNISSID_52));
        }
        if(ObjectUtils.isNotEmpty(args.get(GsProjectSettingEnum.BUSSNISSID_61))){
            let {constructId, precisionSetting} = args.get(GsProjectSettingEnum.BUSSNISSID_61);
            await this.setPrecisionSetting(constructId, precisionSetting);
        }
    }


    async setSelectFolder(arg) {
        let {paramsTag,filePaths} = arg;
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        if (_.isEmpty(filePaths)) {
            return ResponseData.success(0);
        }
        userHistoryData[paramsTag] = filePaths;
        let obj = ObjectUtils.toJsonString(userHistoryData);
        fs.writeFileSync(baseDataDir, obj);
        return ResponseData.success(1);
    }

    async getSelectFolderPath(arg) {
        let {paramsTag} = arg;
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        const filePaths = dialog.showOpenDialogSync({
            properties: ['openDirectory', 'createDirectory'],
            defaultPath: userHistoryData[paramsTag]
        });
        if (_.isEmpty(filePaths)) {
            return ResponseData.success(0);
        }
        // userHistoryData[paramsTag] = filePaths[0];
        let obj = ObjectUtils.toJsonString(userHistoryData);
        // fs.writeFileSync(baseDataDir, obj);
        return filePaths[0];
    }

}

GljCommonService.toString = () => '[class GljCommonService]';
module.exports = GljCommonService;
