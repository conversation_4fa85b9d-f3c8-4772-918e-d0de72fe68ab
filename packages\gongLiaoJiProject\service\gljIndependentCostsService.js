const {Service} = require("../../../core");
const ProjectDomain = require('../domains/ProjectDomain');
const {ObjectUtils} = require('../utils/ObjectUtils');
const {GljIndependentCosts} = require('../models/GljIndependentCosts');
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../../../common/NumberUtil");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ResponseData} = require("../utils/ResponseData");
const TaxCalculationMethodEnum = require("../enums/TaxCalculationMethodEnum");

/**
 * 独立费 service
 */
class GljIndependentCostsService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 排序
     * @param array
     * @private
     */
    _reorder(array) {
        if (ObjectUtils.isEmpty(array)) {
            return;
        }
        array.forEach(item => {
            this.generateIndexes(array, item)
        });
    }

    /**
     *  生成序号的函数
     * @param list
     * @param item
     */
    generateIndexes(list, item) {
        // 过滤出当前层级的元素  
        const currentLevelItems = list.filter(itemList => itemList.parentId === item.sequenceNbr);
        // 遍历当前层级的元素并生成序号  
        for (let i = 0; i < currentLevelItems.length; i++) {
            const itemChild = currentLevelItems[i];
            // 否则，使用默认序号  
            itemChild.dispNo = `${item.dispNo || ''}.${i + 1}`;

            // 递归处理子元素  
            this.generateIndexes(list, itemChild);
        }
    }

    /**
     * 保存列表
     * @param args
     */
    async save(args) {
        const {constructId, unitId, iCosts, operateType} = args;
        let newEqCosts = new GljIndependentCosts();
        ConvertUtil.setDstBySrc(iCosts, newEqCosts);
        let majorTypeSet = new Set();
        //初始化id
        let functionMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY);
        //小数点精度
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId);
        let precision = precision1.UNIT_DLF;
        let list = functionMap.get(this.getDataMapKey(unitId));

        if (operateType === 'insert') {
            let dispNo = newEqCosts.dispNo;
            let indexSequenceNbr = newEqCosts.sequenceNbr;
            newEqCosts.sequenceNbr = Snowflake.nextId();
            newEqCosts.name = ObjectUtils.isEmpty(newEqCosts.name) ? null : newEqCosts.name;
            newEqCosts.unit = ObjectUtils.isEmpty(newEqCosts.unit) ? '元' : newEqCosts.unit;
            newEqCosts.customIndex = null;
            newEqCosts.quantity = null;
            newEqCosts.price = ObjectUtils.isEmpty(newEqCosts.price) ? null : newEqCosts.price;
            newEqCosts.totalPrice = 0;
            newEqCosts.remark = null;
            newEqCosts.costMajorCode = null;
            newEqCosts.costMajorName = null;
            if (ObjectUtils.isEmpty(dispNo)) {
                list.push(newEqCosts);
            } else {
                let findIndex = list.findIndex(item => item.sequenceNbr === indexSequenceNbr);
                list.splice(findIndex + 1, 0, newEqCosts);
            }
            let parentItem = list.find(item => item.sequenceNbr === newEqCosts.parentId);
            if (parentItem.levelType === 2) {
                newEqCosts.costMajorName = parentItem.costMajorName;
                newEqCosts.costMajorCode = parentItem.costMajorCode;
            }
            await this.setCostMajorName(constructId, unitId, newEqCosts);
            majorTypeSet.add(newEqCosts.costMajorCode);
        }
        if (operateType === 'insertChild') {
            newEqCosts.parentId = newEqCosts.sequenceNbr;
            //先保存parentid在 生成新的 id
            newEqCosts.sequenceNbr = Snowflake.nextId();
            newEqCosts.levelType += 1
            let dispNo = newEqCosts.dispNo;
            //获取当前节点下标
            if (ObjectUtils.isEmpty(dispNo)) {
                list.push(newEqCosts);
            } else {
                let parentIndex = 0
                let findIndex = 0;
                list.forEach((item, index) => {
                    if (item.sequenceNbr === newEqCosts.parentId) {
                        parentIndex = index;
                    }
                    if (item.parentId === newEqCosts.parentId) {
                        findIndex = index;
                    }
                });
                let insertIndex = findIndex > 0 ? findIndex + 1 : parentIndex + 1;
                list.splice(insertIndex, 0, newEqCosts);
            }
            newEqCosts.name = null;
            newEqCosts.unit = '元';
            newEqCosts.customIndex = null;
            newEqCosts.quantity = null;
            newEqCosts.price = null;
            newEqCosts.totalPrice = 0;
            newEqCosts.remark = null;
            //父级此此段为空
            let parentItem = list.find(item => item.sequenceNbr === newEqCosts.parentId);
            if (parentItem.levelType === 2) {
                parentItem.price = null;
                parentItem.unit = null;
                parentItem.quantity = null;
                newEqCosts.costMajorName = parentItem.costMajorName;
                newEqCosts.costMajorCode = parentItem.costMajorCode;
                newEqCosts.isCalculateAwf = false;
            }
            if(parentItem.levelType === 1){
                newEqCosts.isCalculateAwf = true;
            }
            await this.setCostMajorName(constructId, unitId, newEqCosts);
            majorTypeSet.add(newEqCosts.costMajorCode);
        }
        if (operateType === 'edit') {
            let item = list.find(item => item.sequenceNbr === newEqCosts.sequenceNbr);
            if (ObjectUtils.isEmpty(item)) {
                return;
            }
            // if (item.dispNo !== newEqCosts.dispNo) {
                item.customIndex = newEqCosts.dispNo;
            // }
            let oldCostMajorCode = item.costMajorCode;
            item.name = newEqCosts.name;
            item.unit = newEqCosts.unit;
            item.quantity = newEqCosts.quantity;
            item.price = newEqCosts.price;
            item.remark = newEqCosts.remark;
            item.costMajorName = newEqCosts.costMajorName;
            item.costMajorCode = newEqCosts.costMajorCode;
            item.isCalculateAwf = newEqCosts.isCalculateAwf;
            if (oldCostMajorCode !== newEqCosts.costMajorCode) {
                list.forEach(lItem => {
                    if (lItem.parentId === item.sequenceNbr) {
                        lItem.costMajorName = item.costMajorName;
                        lItem.costMajorCode = item.costMajorCode;
                    }
                });
            }
            majorTypeSet.add(oldCostMajorCode);
            majorTypeSet.add(newEqCosts.costMajorCode);
        }

        if (ObjectUtils.isEmpty(newEqCosts.parentId)) {
            newEqCosts.parentId = list[0].sequenceNbr
        }
        this._reorder(list);

        //计算总的独立费
        this.caculatorTreeTotal(list, null, precision);
        //保存数据
        functionMap.set(this.getDataMapKey(unitId), list);

        let untiProject = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === unitId);
        //同步取费文件
        await this.service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorAddFeeRate(constructId, unitId, untiProject[0].constructMajorType, newEqCosts.costMajorCode);
        //通知费用汇总变动消息
        for (let item of majorTypeSet) {
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: untiProject[0].parentId,
                unitId: unitId,
                constructMajorType: item
            });
        }
        //同步取费文件
        await this.service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorDelFeeRate(constructId, unitId, untiProject[0].constructMajorType, newEqCosts.costMajorCode);
    }

    /**
     * 设置工程专业名称
     * @param constructId
     * @param unitId
     * @param newEqCosts
     * @returns {Promise<void>}
     */
    async setCostMajorName(constructId, unitId, newEqCosts) {
        if (newEqCosts.levelType === 1 || ObjectUtils.isNotEmpty(newEqCosts.costMajorName)) {
            return;
        }
        // 获取单位工程
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 获取取费表费率当前工程专业的默认取费专业
        // let rates = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList({
        //     constructId: constructId,
        //     singleId: unitProject.parentId,
        //     unitId: unitId
        // });
        // newEqCosts.costMajorName = rates[0] ? rates[0].freeProfession : null;
        // newEqCosts.costMajorCode = rates[0] ? rates[0].qfCode : null;

        let baseFeeFiles = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();
        let baseFeeFile = baseFeeFiles.filter(item => item.qfCode === unitProject.qfMainMajorType);
        newEqCosts.costMajorName = baseFeeFile[0] ? baseFeeFile[0].qfName : null;
        newEqCosts.costMajorCode = baseFeeFile[0] ? baseFeeFile[0].qfCode : null;
    }

    /**
     * 字符串转数字
     * @param priceStr
     * @returns {number}
     */
    convertNumber(priceStr) {
        let price = priceStr;
        if (ObjectUtils.isEmpty(price) || !ObjectUtils.isNumberStr(price)) {
            price = 0;
        }
        if (ObjectUtils.isNumberStr(price)) {
            price = Number(price);
        }
        return price;
    }

    /**
     * 计算当前节点的汇总数据
     * @param list
     * @param item
     * @param precision
     */
    caculatorTreeTotal(list, item, precision) {
        if (item === null) {
            item = list[0];
        }
        let allCost = 0;
        let newList = list.filter(itemList => itemList.parentId === item.sequenceNbr)
        if (ObjectUtils.isNotEmpty(newList)) {
            newList.forEach(itemList => {
                    this.caculatorTreeTotal(list, itemList, precision);
                    allCost = NumberUtil.add(allCost, NumberUtil.numberScale(itemList.totalPrice, precision.totalPrice));
                }
            );
        } else {
            allCost = NumberUtil.multiply(NumberUtil.numberScale(this.convertNumber(item.price), precision.price), NumberUtil.numberScale(this.convertNumber(item.quantity), precision.quantity));
        }
        item.totalPrice = allCost;
    }

    /**
     * 删除列表
     * @param args
     */
    async delete(args) {
        const {constructId, unitId, sequenceNbr} = args;
        let functionMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY);
        //小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId).UNIT_DLF;
        let list = functionMap.get(this.getDataMapKey(unitId));
        //不能删除第一个
        if (list[0].sequenceNbr === sequenceNbr) {
            return;
        }
        //没有删除的对象返回
        let deleteItem = list.find(item => item.sequenceNbr === sequenceNbr);
        if (ObjectUtils.isEmpty(deleteItem)) {
            return;
        }
        let parentItem = list.find(item => item.sequenceNbr === deleteItem.parentId);
        if (ObjectUtils.isEmpty(parentItem)) {
            return;
        }
        let majorTypeSet = new Set();
        //只对二级三级能删除，其他无法使用
        let newList = [];
        for (let i = 0; i < list.length; i++) {
            let item = list[i];
            if (item.sequenceNbr !== sequenceNbr && item.parentId !== sequenceNbr) {
                newList.push(item);
            } else {
                majorTypeSet.add(list[i].costMajorCode);
            }
        }
        //重新排序
        this._reorder(newList);
        //设置数据
        let parentHasChild = newList.find(item => item.parentId === parentItem.sequenceNbr);
        if (ObjectUtils.isEmpty(parentHasChild) || parentHasChild.length === 0) {
            parentItem.unit = '元';
        }
        //计算总的独立费
        this.caculatorTreeTotal(newList, null, precision);
        functionMap.set(this.getDataMapKey(unitId), newList);

        let untiProject = ProjectDomain.getDomain(constructId).getProject(item => item.sequenceNbr === unitId);
        //同步取费文件
        await this.service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorDelFeeRate(constructId, unitId, untiProject[0].constructMajorType, deleteItem.costMajorCode);
        //通知费用汇总变动消息
        for (let item of majorTypeSet) {
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: untiProject[0].parentId,
                unitId: unitId,
                constructMajorType: item
            });
        }
    }

    /**
     * 获取信息列表
     * @param args
     */
    async getList(args) {
        const {constructId, unitId} = args;
        //获取数据
        let functionMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_DLF_KEY);
        let list = functionMap.get(this.getDataMapKey(unitId));
        if (ObjectUtils.isEmpty(list)) {
            list = await this.initData(args);
            functionMap.set(this.getDataMapKey(unitId), list);
            // await this.initChildData(args, list);
        } else {
            // 兼容map对象处理
            if (list[0] instanceof Map) {
                for (let index = 0; index < list.length; index++) {
                    list[index] = Object.fromEntries(list[index].entries())
                }
            }
        }
        this.editConfig(list);
        //获取取费专业数据,默认河北石家庄,
        let costMajorList = await this.service.gongLiaoJiProject.gljBaseDeLibraryService.getDeLibrariesByDirection('130000', '130100');
        return {list: list, costMajorList: costMajorList};
    }

    /**
     * 编辑配置
     * @param list
     */
    editConfig(list) {
        list.forEach(item => {
            let childreds = list.filter(pItem => pItem.parentId === item.sequenceNbr);
            item.editConfig = childreds && childreds.length > 0 ? false : true;
        });
    }

    /**
     * 独立费初始化数据
     * @param args
     * @returns {Promise<*[]>}
     */
    async initData(args) {
        let list = [];
        let root = new GljIndependentCosts(Snowflake.nextId(), "1", "独立费", "元", null, null, 0, false, null, null, null, '1', 1, '1', null);
        list.push(root);
        return list;
    }

    /**
     * 独立费初始化子级数据
     * @param args
     * @param list
     * @returns {Promise<*[]>}
     */
    async initChildData(args,list) {
        let root = list[0];
        //初始化子级
        let saveParams = {};
        saveParams.constructId = args.constructId;
        saveParams.unitId = args.unitId;
        saveParams.iCosts = root;
        saveParams.operateType = "insertChild";
        try {
            await this.save(saveParams);
        } catch (e){
            console.log(e);
        }
    }

    /**
     * 11 基本信息 12 编制说明 13 特征
     * @param {*} unitId
     * @param {*} type
     * @returns
     */
    getDataMapKey(unitId) {
        if (ObjectUtils.isEmpty(unitId)) {
            unitId = "0";//保持key风格一致性
        }
        return "INDCOST-" + unitId;
    }

    /**
     * 独立费-查询人材机数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async insertRcj(args) {
        const {constructId, unitId, levelType, iCosts, operateType, rcj} = args;
        if (ObjectUtils.isEmpty(iCosts)) {
            return;
        }

        iCosts.name = rcj.materialName + (ObjectUtils.isNotEmpty(rcj.specification) ? " " + rcj.specification : "");
        iCosts.unit = rcj.unit;
        iCosts.isRcj = true;

        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        //判断计税
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            //简易计税
            iCosts.price = rcj.baseJournalTaxPrice;
        }else{
            iCosts.price = rcj.baseJournalPrice;
        }

        let param = {
            constructId,
            unitId,
            levelType,
            iCosts,
            operateType,
        }
        await this.save(param);
    }
}

GljIndependentCostsService.toString = () => '[class GljIndependentCostsService]';
module.exports = GljIndependentCostsService;