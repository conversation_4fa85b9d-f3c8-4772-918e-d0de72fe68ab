const _ = require("lodash");
const DeTypeConstants = require('../../constants/DeTypeConstants');
const {Snowflake} = require("../../utils/Snowflake");
const PropertyUtil = require("./PropertyUtil");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const UnitUtils = require("../../core/tools/UnitUtils");

class DeUtils {

    static findAllQdParendIds(deRowId, ctx){
      let qdIds = [];
      let qd = ctx.deMap.getNodeById(deRowId);
      if(qd.type === DeTypeConstants.DE_TYPE_DELIST){
        qdIds.push(deRowId);
        qdIds = qdIds.concat(this.findAllQdParendIds(qd.parentId,ctx));
      }
      return qdIds;
    }

    static findAllParentIds(deRowId,ctx){
        let ids = [];
        let qd = ctx.deMap.getNodeById(deRowId);
        if(qd.type !== DeTypeConstants.DE_TYPE_DEFAULT){
            ids.push(qd.parentId);
            ids = ids.concat(this.findAllParentIds(qd.parentId,ctx));
        }
        return ids;
    }

    /**
     * 获取定额的工程量保留小数位数值
     * @param {*} precision 
     * @param {*} deRow 
     * @returns 
     */
    static getQuantiyPrecision(precision,deRow){

        let unitPrecision = precision.EDIT.DE.quantity;
        if(precision.EDIT.DE.isUnitQuantity == true && ObjectUtils.isNotEmpty(deRow.unit)){
            let  unitNbr = UnitUtils.removeCharter(deRow.unit);
            let realUnit = deRow.unit.replace(unitNbr,'');
            let tepmUnitPrecision = precision.EDIT.DE.unitQuantity[realUnit];
            if(ObjectUtils.isNotEmpty(tepmUnitPrecision)){
                unitPrecision = tepmUnitPrecision;
            }
        }
        if(ObjectUtils.isEmpty(unitPrecision)){
            unitPrecision = 5;
        }
        return unitPrecision;
    }

    /**
     * 构建新的定额树
     * @param {*} sourceDes  不要该里面的值
     * @param {*} targetDeMap 
     * @param {*} targetConstructId 
     * @param {*} targetUnitId 
     * @param {*} maxAttempts 
     * @returns 
     */
    static buildTreeWithParents( sourceDes, targetDeMap, targetConstructId,targetUnitId,maxAttempts = sourceDes.length * 2) {
        const createdNodes = new Map(); // 存储已创建的节点
        const checkedNodes = new Map(); // 存储已检查的节点
        const queue = []; // 临时队列，用于处理待创建的节点
        // 将所有对象加入队列
        for (let de of sourceDes) {
            
            let newDe = _.cloneDeep(de);
            newDe.constructId = targetConstructId;
            newDe.unitId = targetUnitId;
            newDe.children = [];// 设置为空，防止double
            newDe.importSequenceNbr = newDe.sequenceNbr;
            newDe.sequenceNbr = Snowflake.nextId();
            newDe.deRowId = newDe.sequenceNbr;
            queue.push(newDe);
        }

        let attemptCount = 0;

        while (queue.length > 0 && attemptCount < maxAttempts) {
            let currentNode = queue.shift();//不要更改其属性

            attemptCount++;

            if (checkedNodes.has(currentNode.sequenceNbr)) {
                console.error(`Detected infinite loop or data inconsistency: Node with id ${currentNode.id} is already checked.`);
                break;
            }

            if (createdNodes.has(currentNode.importSequenceNbr)) {
                continue; // 跳过已创建的节点
            }

            if (currentNode.parentId === null || currentNode.parentId === undefined || currentNode.parentId === 0)
            {
                let root = targetDeMap.find(item=>item.unitId === targetUnitId && item.type === DeTypeConstants.DE_TYPE_DEFAULT);
                PropertyUtil.copyProperties(currentNode,root,["sequenceNbr","deRowId","updateDate"]); //父级处理
                currentNode = root;
            } else {
                let parentNode = createdNodes.get(currentNode.parentId);
                if(!parentNode){
                    parentNode = targetDeMap.getAllNodes().filter(item=>item.importSequenceNbr === currentNode.parentId)
                }
                if (parentNode) {
                    currentNode.parentId = parentNode.sequenceNbr;//重置父级的id
                    targetDeMap.addNode(currentNode, parentNode);
                } else if (queue.includes(currentNode.parentId)) {
                    console.error(`Data inconsistency: Parent node with id ${currentNode.parentId} not found in the queue.`);
                    break;
                } else {
                    queue.push(currentNode); // 父节点未创建，放回队列
                    continue;
                }
            }

            createdNodes.set(currentNode.importSequenceNbr, currentNode);
            checkedNodes.set(currentNode.importSequenceNbr,currentNode.sequenceNbr);
        }

        if (attemptCount >= maxAttempts) {
            console.error('Maximum number of attempts reached, possible data inconsistency or infinite loop.');
        }

        return checkedNodes;
    }

    static calculateLevel(deRow,checkChild=false,checkParent = true,isImport = false){
        let count = 0;
        if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.type)){
            if(isImport &&(deRow.importYgsDeDRGCFB || deRow.importYgsDeYYGCFB)){ //如果是导入不计算自主生成的数据
            }else{
              count++;
            }
            if(checkChild){
                let max = 0;
                for(let child of deRow.children){
                let childLevel = this.calculateLevel(child,checkChild,false,isImport);
                max = max>childLevel?max:childLevel;
                }
                count += max;
            }
        }
        if(DeTypeConstants.DE_TYPE_DEFAULT === deRow.type){
            let max = 0;
            for(let child of deRow.children){
              let childLevel = this.calculateLevel(child,checkChild,false,isImport);
              max = max>childLevel?max:childLevel;
            }
            count += max;
        }
        if(checkParent && ObjectUtils.isNotEmpty(deRow.parent)){
          count+=this.calculateLevel(deRow.parent,checkChild,checkParent,isImport);
        }
        return count;
    }
    
    static getDePrecision(key,precision){
        let digital = precision.EDIT.DE[key];
        if(ObjectUtils.isEmpty(digital)){
            switch (key) {
                case "baseJournalPrice":
                digital = precision.EDIT.DE.price;
                break;
                case "baseJournalTotalNumber":
                digital = precision.EDIT.DE.totalNumber;
                break;
                case "RDSum":
                digital = precision.EDIT.DE.RSum;
                break;
                case "CDSum":
                digital = precision.EDIT.DE.CSum;
                break;
                case "JDSum":
                digital = precision.EDIT.DE.JSum;
                break;
                case "ZDSum":
                digital = precision.EDIT.DE.ZSum;
                break;
                case "SDSum":
                digital = precision.EDIT.DE.SSum;
                break;
                case "zdTotalSum":
                digital = precision.EDIT.DE.ZTotalSum;
                break;
                case "sdTotalSum":
                digital = precision.EDIT.DE.STotalSum;
                break;
                default:
            }
        }
        return digital;
    }

  
    static getRCJPrecision(key, precision) {
        switch (key) {
        case "resQty":
            return precision.DETAIL.RCJ.resQty;
        case "totalNumber":
            return precision.DETAIL.RCJ.totalNumber;
        case "total":
            return precision.DETAIL.PTRCJZS.total;
        case "dePrice":
            return precision.DETAIL.PTRCJZS.dePrice;
        case "marketPrice":
            return precision.DETAIL.PTRCJZS.marketPrice;
        default:
            return null; // 默认精度
        }
    }
}
DeUtils.toString = () => 'DeUtils';
module.exports = DeUtils;