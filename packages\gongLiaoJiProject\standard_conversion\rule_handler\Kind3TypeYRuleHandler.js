const Kind3RuleHandler = require("./Kind3RuleHandler");

class Kind3TypeYRuleHandler extends Kind3RuleHandler {
    _getMath(){
        let inputValue = this.rule.selectedRule;

        let tmpMath = this.rule.math;
        let maths = this.rule.math.split(";");
        for (let i = 0; i < maths.length; i++) {
            let mathKV = maths[i].split(":");
            if(mathKV[0] == inputValue){
                tmpMath = mathKV[1];
                break;
            }
        }
        return tmpMath;
    }
    analysisRule(){

        let oriRuleMath = this.rule.math;
        let tmpMath = this._getMath();
        this.rule.math = tmpMath;
        let mathHandlers = super.analysisRule();
        this.rule.math = oriRuleMath;
        return mathHandlers;
    }
    dealConversionInfo(conversionInfoItem) {
        let tmpMath = this._getMath();
        conversionInfoItem.conversionString = `${tmpMath}`;
        super.dealConversionInfo(conversionInfoItem);
    }
}
module.exports = Kind3TypeYRuleHandler;
