/**
 * 工程项目设置
 */
class GljProjectSettingEnum {

    // 标准换算设置
    static STANDARD_CONVERSION = 'STANDARD_CONVERSION';
    static STANDARD_CONVERSION_SETTING = {
        conversionPop: true
    };

    // 关联子目弹窗设置
    static RELATION_DE = 'RELATION_DE';
    static RELATION_DE_SETTING = {
        relationDePop: true
    };

    //按市场价组价，0 不按 1 按
    static PRICING_METHOD='PRICING_METHOD';

    //未计价材料
    static UNPRICED='UNPRICED';
    static UNPRICED_SETTING = {
        unPricedPop: true
    };


    //主材设备计取价差
    static PRICING_RCJDIFF='PRICING_RCJDIFF';

    //市政设施维修养护工程是否执行中修
    static SZSS_MEDIUM_REPAIR='SZSS_MEDIUM_REPAIR';

    //补人材机计取价差
    static PRICING_ZSDIFF='PRICING_ZSDIFF';
    //人材机关联定额
    static RCJ_RELATION_DE='RCJ_RELATION_DE';
    static RCJ_RELATION_DE_SETTING = {
        relationDePop: true
    };

    //主材设备受系数影响
    static FACTOR_EFFECT='FACTOR_EFFECT';

    //税率配置
    static TAXRATE_SETTING='TAXRATE_SETTING';

    /**
     *  我文件管理-文件下载鲁行          ：11
     *  我文件管理-默认数据存储路径       ：12
     *  便捷性设置-单位工程标准换算痰喘    ：21
     *  便捷性设置-按不含税市场价组价      ：22
     *  便捷性设置-展示定额关联子目        ：23
     *  便捷性设置-展示未计价材料         ：24
     *  计算设置-主材设备计取价差        ：31
     *  计算设置-补充人材机计取价差       ：32
     *  计算设置-主材设备受系数调整影响    ：33
     *  其他设置-文件定时存储            ：41
     *  地区特性-市政设施执行中修         ：51
     *  地区特性-税率可编辑              ：52
     *  计算精度设置                    ：61
     */
    static BUSSNISSID_11="11";
    static BUSSNISSID_12="12";
    static BUSSNISSID_21="21";
    static BUSSNISSID_22="22";
    static BUSSNISSID_23="23";
    static BUSSNISSID_24="24";
    static BUSSNISSID_31="31";
    static BUSSNISSID_32="32";
    static BUSSNISSID_33="33";
    static BUSSNISSID_41="41";
    static BUSSNISSID_51="51";
    static BUSSNISSID_52="52";
    static BUSSNISSID_61="61";
}

module.exports = GljProjectSettingEnum;
