const _ = require("lodash");
const ConstantUtil = require("../../enums/ConstantUtil");
const {ObjectUtil} = require("../../../../common/ObjectUtil");
const {NumberUtil} = require("../../utils/NumberUtil");
const ProjectDomain = require("../../domains/ProjectDomain");
const RcjTypeEnum = require('../../../../electron/enum/RcjTypeEnum');
const CommonConstants = require("../../constants/CommonConstants");
const {RCJKind} = require("../../enums/ConversionSourceEnum");
const DeCommonConstants = require("../../constants/DeCommonConstants");

class MathItemHandler {
    static MATH_RANGE_REGEX = /RANG\([^)]*\)/g;
    constructor(ruleCtx, math) {
        this.ctx = ruleCtx.ctx;
        this.ruleCtx = ruleCtx;
        this.app = ruleCtx.app;
        this.service = this.app.service;
        this.conversionService = ruleCtx.ctx.conversionService;
        this.effectDeRCJ = ruleCtx.effectDeRCJ;
        this.rule = ruleCtx.rule;
        this.notStandardActiveRcjCodes = ruleCtx.notStandardActiveRcjCodes;
        this.oriMath = math;
        // this.formatMath = this.conversionService.mathFormat(math, this.rule);
        // this.mathItem = this._initMathItem();

        let {formatMath, mathRange} = this._disassemblyFormula(this.oriMath);
        this.mathRange = mathRange;
        this.formatMath = this.conversionService.mathFormat(formatMath, this.rule);
        this.mathItem = this._initMathItem();
        this.deRcjs = ruleCtx.deRcjs;
        this.tcRCJResQty = ruleCtx.tcRCJResQty;

        this.deQuantity = this.ctx.deLine.quantity;

        this.pricingMethod = ProjectDomain.getDomain(this.ctx.constructId).getRoot().pricingMethod;      //组价方式
        // this.taxCalculationMethod = ProjectDomain.getDomain(this.ctx.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;   //计税方式

        this.rcjKindMap = new Map([
            ["R", [RCJKind.人工]],
            ["C", [RCJKind.材料, RCJKind.砼, RCJKind.商砼, RCJKind.浆, RCJKind.商浆, RCJKind.配比]],
            ["J", [RCJKind.机械]],
            ["ALL", [RCJKind.人工,RCJKind.材料, RCJKind.砼, RCJKind.商砼, RCJKind.浆, RCJKind.商浆, RCJKind.配比, RCJKind.设备,RCJKind.主材,RCJKind.机械]]
        ]);
        this.jd = this.ctx.jd
    }

    /**
     * 准备数据：根据不同规则准备定额、人材机、规则的取消处理等
     */
    async prepare() {
        this.analysisMath();
        await this.activeRCJ();
    }

    /**
     * 逐条执行换算规则
     */
    async execute() {
        await this.prepare();
        await this.dealResQty();
        this.after();
    }

    async dealResQty(){
        await this.computeResQty();
        // 处理特殊人材机消耗量最高、最低限制
        if(this.mathRange){
            let deRcjs = this.deRcjs;
            for(let rcj of deRcjs){
                let range = this.mathRange[rcj.materialCode];
                if(range){
                    let resQty = rcj.resQty;
                    resQty = ObjectUtil.isNotEmpty(range.min) && resQty < range.min ? range.min : resQty;
                    resQty = ObjectUtil.isNotEmpty(range.max) && resQty > range.max ? range.max : resQty;
                    rcj.resQty = resQty;
                }
            }
        }
    }

    after() {

    }

    _initMathItem() {
        return {
            /**
             * type取值 代表那种规则：
             *   1. 修改消耗量;
             *   2. 替换指定材料修改消耗量；
             *   3. 替换指定材料消耗量不变；
             *   4. 新增指定材料，修改消耗量；
             *   5. 修改指定材料消耗量
             * */
            type: null,
            math: this.formatMath,
            operator: "",
            parseMath: "",
            RJCSymbol: "",  //取值： R,C,J,ALL
            activeRCJKind: [],
            activeRCJs:[],
            fromRCJCode: null,
            fromRCJLibraryCode: null,
            toRCJCode: null,
            toRCJLibraryCode: null,
            addRCJCode: null,
            addRCJLibraryCode: null,
            delRCJCode: null,
            oriResQty:null
        };
    }

    analysisMath() {
        throw new Error("MathItemHandler: 公式解析需要子类实现")
    }

    async activeRCJ() {
        throw new Error("MathItemHandler: 影响的人材机子类实现")
    }

    findActiveRCJByCode(materialCode) {
        let activeRCjS = this.effectDeRCJ.filter((rcj) => materialCode === rcj.materialCode);
        if(ObjectUtil.isEmpty(activeRCjS)){
            let notStandardCode = materialCode + "#";
            activeRCjS = this.effectDeRCJ.filter((rcj) => rcj.materialCode.startsWith(notStandardCode));
        }
        return activeRCjS;
    }

    async computeResQty() {
        this.mathItem.activeRCJs.forEach((rcj) => {
            // 判断人材机临时删除状态，获取处理前消耗量
            let resQtyBefore = rcj.isTempRemove == CommonConstants.COMMON_YES ? rcj.changeResQty : rcj.resQty;
            let parseMathAfterCalculation = this.conversionService.mathAfterCalculation(this.mathItem.parseMath);
            let finalMath = ObjectUtil.isEmpty(this.mathItem.operator)
                ? parseMathAfterCalculation
                : `${NumberUtil.numberScale(resQtyBefore, this.jd.resQty)}${parseMathAfterCalculation}`

            let resQtyAfter = eval(finalMath);
            if (isNaN(resQtyAfter)) {
                throw new Error("计算失败！");
            }

            if(!rcj.isNumLock || this.deQuantity == 0){
                if(rcj.isNumLock && this.deQuantity == 0){
                    rcj.resQtyForNumLockAndDeQuantityZero = rcj.resQty;
                }
                this.ctx.conversionService.updateTempRemoveRCJResQty(rcj, resQtyAfter);
            }



            if(this.deQuantity != 0 && rcj.isNumLock){
                // 当人材机锁定数量时，将执行标准换算后的消耗量单独存储，不改变消耗量
                {
                    let resQtyConversionLock = rcj.resQtyConversionLock ; // || NumberUtil.numberScale(NumberUtil.divide(rcj.numLockNum, this.deQuantity), 3);
                    let finalMathLock = ObjectUtil.isEmpty(this.mathItem.operator)
                        ? parseMathAfterCalculation
                        : `${NumberUtil.numberScale(resQtyConversionLock, this.jd.resQty)}${parseMathAfterCalculation}`

                    let resQtyConversionAfterLock = eval(finalMathLock);
                    rcj.resQtyConversionLock = resQtyConversionAfterLock;
                }

                // 只有对RJC或单价执行运算时才会处理锁定数量的情况
                if(!DeCommonConstants.isCodeTZ(rcj.materialCode)
                    && this.ctx.deLine.isTempRemove != 1
                    &&  rcj.isTempRemove != 1
                    && ["R","C","J","ALL"].includes(this.mathItem.RJCSymbol)){

                    let resQtyLock = rcj.resQty;

                    let finalMathLock = ObjectUtil.isEmpty(this.mathItem.operator)
                        ? parseMathAfterCalculation
                        : `${NumberUtil.numberScale(resQtyLock, this.jd.resQty)}${parseMathAfterCalculation}`

                    let addedResQtyAfterLock = eval(finalMathLock);

                    let curPrice = rcj.marketPrice;

                    if(this.rcjKindMap.get("R").includes(rcj.kind)){
                        this.tcRCJResQty.R = NumberUtil.numberScale(this.tcRCJResQty.R, this.jd.resQty) + NumberUtil.numberScale(addedResQtyAfterLock - resQtyLock, this.jd.resQty) * NumberUtil.numberScale(curPrice, this.jd.deRcjPrice);
                    }else if(this.rcjKindMap.get("C").includes(rcj.kind)){
                        this.tcRCJResQty.C = NumberUtil.numberScale(this.tcRCJResQty.C, this.jd.resQty) + NumberUtil.numberScale(addedResQtyAfterLock - resQtyLock, this.jd.resQty) * NumberUtil.numberScale(curPrice, this.jd.deRcjPrice);
                    }else{
                        this.tcRCJResQty.J = NumberUtil.numberScale(this.tcRCJResQty.J, this.jd.resQty) + NumberUtil.numberScale(addedResQtyAfterLock - resQtyLock, this.jd.resQty) * NumberUtil.numberScale(curPrice, this.jd.deRcjPrice);
                    }
                }
            }

            if (ObjectUtil.isEmpty(rcj.changeResQtyRuleIds)) {
                rcj.changeResQtyRuleIds = [this.rule.sequenceNbr];
            } else {
                rcj.changeResQtyRuleIds.push(this.rule.sequenceNbr);
            }

            // 非标准换算、统一换算规则
            if(this.rule.kind != "5"){
                if (ObjectUtil.isEmpty(rcj.changeResQtyStdIds)) {
                    rcj.changeResQtyStdIds = [this.rule.sequenceNbr];
                } else {
                    rcj.changeResQtyStdIds.push(this.rule.sequenceNbr);
                }
            }else{
                if (ObjectUtil.isEmpty(rcj.changeResQtyCunsumerIds)) {
                    rcj.changeResQtyCunsumerIds = [this.rule.sequenceNbr];
                } else {
                    rcj.changeResQtyCunsumerIds.push(this.rule.sequenceNbr);
                }
            }
        })
    }

    async addNewRCJ(libraryCode, materialCode, initResQty = 0) {
        const {constructId, singleId, unitId, unitProject, de} = this.ctx;

        const rcj = await this.conversionService.getRCJ(constructId, unitId, libraryCode, materialCode, de);
        if (!rcj) {
            throw new Error(`找不到替换的人材机(${libraryCode}  ${materialCode})`);
        }

        rcj.deId = de.sequenceNbr;
        rcj.parentId = de.sequenceNbr;
        rcj.deRowId = de.sequenceNbr;
        rcj.constructId = constructId;
        rcj.unitId = unitId;
        rcj.initResQty = rcj.initResQty || initResQty;
        rcj.resQty = 0 || initResQty;
        rcj.originalQty = rcj.originalQty || initResQty;
        rcj.marketPrice = rcj.baseJournalPrice ;
        rcj.marketTaxPrice = rcj.baseJournalTaxPrice ;

        for (let key in RcjTypeEnum) {
            if (RcjTypeEnum[key].code == rcj.kind) {
                rcj.type = RcjTypeEnum[key].desc;
            }
        }

        await this.service.PreliminaryEstimate.gsRcjService.processingMarketPrice(rcj);

        let resourceDomain = ProjectDomain.getDomain(constructId).getResourceDomain();
        resourceDomain.createResource(unitId, de.sequenceNbr, rcj);

        this.ctx.dealLockNumberOneRcj(rcj);

        rcj.addFromConversionRuleId = this.rule.sequenceNbr;
        // 非标准换算、统一换算规则
        if(this.rule.kind != "5"){
            rcj.addFromConversionStdRuleId = this.rule.sequenceNbr;
        }else{
            rcj.addFromConversionCunsumerRuleId = this.rule.sequenceNbr;
        }
        this.deRcjs.push(rcj);

        return rcj;
    }

    async editRcj(fromRcj, toRcj) {
        let resultRcj = await this.conversionService.editRcj(fromRcj, toRcj, this.ctx)
        if(fromRcj.addFromConversionRuleId){
            resultRcj.addFromConversionRuleId = fromRcj.addFromConversionRuleId;
        }
        resultRcj.editFromConversion = {
            ruleId : this.rule.sequenceNbr,
            fromRCJCode: fromRcj.materialCode,
            fromRCJLibraryCode: fromRcj.libraryCode
        };
        return resultRcj;
    }

    isOtherRCj(rcj){
        return ConstantUtil.SPECIAL_RCJ.includes(rcj.materialCode) && rcj.unit == ConstantUtil.BAIFENHAO;
    }


    mathOperator(mathStr){
        let firstCharacter = mathStr.charAt(0);
        if("+-*/".includes(firstCharacter)){
            return firstCharacter;
        }
        return "";
    }

    _disassemblyFormula(oriMath) {
        if(ObjectUtil.isEmpty(oriMath)){
            return {};
        }
        let matches = oriMath.match(MathItemHandler.MATH_RANGE_REGEX);
        let mathRange = null
        if(ObjectUtil.isNotEmpty(matches)){
            mathRange = {}
            for(let r of matches){
                r = r.replace("RANG(", "").replace(")", "")
                let rs = r.split(";");
                for(let codeRs of rs){
                    let rTmp = codeRs.split(":");
                    let code = rTmp[0];
                    let minMax = rTmp[1].split("~");
                    let min = ObjectUtil.isNotEmpty(minMax[0]) ? Number(minMax[0]) : null
                    let max = ObjectUtil.isNotEmpty(minMax[1]) ? Number(minMax[1]) : null
                    mathRange[code] = {min, max}
                }
            }
        }
        return {
            formatMath: oriMath.replace(MathItemHandler.MATH_RANGE_REGEX, "").replace(/^\s+|\s+$/g, ""),
            mathRange: mathRange
        }
    }
}

module.exports = MathItemHandler;
