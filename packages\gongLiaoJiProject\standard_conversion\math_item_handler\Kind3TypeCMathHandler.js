const { NumberUtil } = require("../../../../common/NumberUtil");
const {ObjectUtil} = require("../../../../common/ObjectUtil");
const RCJPriceMathHandler = require("./RCJPriceMathHandler");
const CommonConstants = require("../../constants/CommonConstants");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. *n 整体乘
 *    2. R/C/J*n 对应R/C/J 乘
 */
class Kind3TypeCMathHandler extends RCJPriceMathHandler{
    constructor(ruleCtx, math) {
        super(ruleCtx, math);
        this.otherRcj = [];
        this.jd = this.ctx.jd
    }
    async computeResQty() {
        await super.computeResQty();
        // 其他材料不参与标准换算
        this.mathItem.activeRCJs.push(...this.otherRcj);

        let deRcjs = this.deRcjs;
        for(let relationRcj of this.mathItem.activeRCJs){
            //let deRcj = deRcjs.find((rcj) => rcj.materialCode == relationRcj.materialCode && rcj.libraryCode == relationRcj.libraryCode);
            let deRcj = deRcjs.find((rcj) => rcj.materialCode == relationRcj.materialCode);
            if(ObjectUtil.isNotEmpty(deRcj)){
                if(!this.isOtherRCj(deRcj)){
                    if(!deRcj.isNumLock || this.deQuantity == 0){
                        this.ctx.conversionService.addTempRemoveRCJResQty(deRcj, relationRcj.resQty);
                    }

                    if(this.deQuantity != 0 && deRcj.isNumLock){
                        // 当人材机锁定数量时，将执行标准换算后的消耗量单独存储，不改变消耗量
                        {
                            let resQtyConversionLock = deRcj.resQtyConversionLock || NumberUtil.divide(NumberUtil.numberScale(deRcj.numLockNum, this.jd.totalNumber), NumberUtil.numberScale(this.deQuantity, this.jd.quantity));
                            deRcj.resQtyConversionLock = NumberUtil.numberScale(resQtyConversionLock, this.jd.resQty) + NumberUtil.numberScale(relationRcj.resQty, this.jd.resQty);
                        }

                        // 只有对RJC或单价执行运算时才会处理锁定数量的情况
                        if(this.ctx.deLine.isTempRemove != 1 &&  deRcj.isTempRemove != 1){
                            if(this.rcjKindMap.get("R").includes(deRcj.kind)){
                                this.tcRCJResQty.R = NumberUtil.numberScale(this.tcRCJResQty.R, this.jd.resQty) + NumberUtil.numberScale(relationRcj.resQty, this.jd.resQty) * NumberUtil.numberScale(deRcj.price, this.jd.deRcjPrice);
                            }else if(this.rcjKindMap.get("C").includes(deRcj.kind)){
                                this.tcRCJResQty.C = NumberUtil.numberScale(this.tcRCJResQty.C, this.jd.resQty) + NumberUtil.numberScale(relationRcj.resQty, this.jd.resQty) * NumberUtil.numberScale(deRcj.price, this.jd.deRcjPrice);
                            }else{
                                this.tcRCJResQty.J = NumberUtil.numberScale(this.tcRCJResQty.J, this.jd.resQty) + NumberUtil.numberScale(relationRcj.resQty, this.jd.resQty) * NumberUtil.numberScale(deRcj.price, this.jd.deRcjPrice);
                            }
                        }
                    }

                    if (ObjectUtil.isEmpty(deRcj.changeResQtyRuleIds)) {
                        deRcj.changeResQtyRuleIds = [this.rule.sequenceNbr];
                    } else {
                        deRcj.changeResQtyRuleIds.push(this.rule.sequenceNbr);
                    }
                    // 中修
                    await this.service.gongLiaoJiProject.gljRcjService.handleSzyhRcjAndCal(deRcj);

                    // 非标准换算、统一换算规则
                    if(this.rule.kind != "5"){
                        if (ObjectUtil.isEmpty(deRcj.changeResQtyStdIds)) {
                            deRcj.changeResQtyStdIds = [this.rule.sequenceNbr];
                        } else {
                            deRcj.changeResQtyStdIds.push(this.rule.sequenceNbr);
                        }
                    }else{
                        if (ObjectUtil.isEmpty(deRcj.changeResQtyCunsumerIds)) {
                            deRcj.changeResQtyCunsumerIds = [this.rule.sequenceNbr];
                        } else {
                            deRcj.changeResQtyCunsumerIds.push(this.rule.sequenceNbr);
                        }
                    }
                }
            }else{
                await this.addNewRCJ(relationRcj.libraryCode, relationRcj.materialCode, relationRcj.resQty);
            }
        }
    }

    // activeRCJ() {
    //     // 默认主材、设备受影响
    //     this.mathItem.activeRCJs = this.effectDeRCJ.filter((rcj) => {
    //         return this.isRcjActive(rcj, true);
    //     });
    // }

    isRcjActive(rcj, isMainMatConvertMod){
        // kind=3,type=c 默认主材、设备受、其他材料都不受mainMatConvertMod的影响


        // if (rcj.kind == RCJKind.主材 && !isMainMatConvertMod) {
        //     return false;
        // }
        // if (rcj.kind == RCJKind.设备 && !isMainMatConvertMod) {
        //     return false;
        // }
        // 其他材料费需要特殊处理，既不修改消耗量，又要添加到当前定额下
        if (this.isOtherRCj(rcj)) {
            this.otherRcj.push(rcj);
            return false;
        }
        // 费用人材机处理
        if (rcj.isFyrcj === 0) {
            return false;
        }

        return this.mathItem.activeRCJKind.includes(rcj.kind);
    }
}

module.exports = Kind3TypeCMathHandler;