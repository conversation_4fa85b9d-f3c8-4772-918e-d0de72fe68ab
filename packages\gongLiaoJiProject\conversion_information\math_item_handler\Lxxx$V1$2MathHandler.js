const MathItemHandler = require("../../standard_conversion/math_item_handler/mathItemHandler");
const {ObjectUtils} = require("../../utils/ObjectUtils");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. LXXXX n m 对应材料编码且消耗量为n，将消耗量改为m
 */
class Lxxx$V1$2MathHandler extends MathItemHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 2;
        let mathSubArr = mathItem.math.substring(1).split(/\s+/);
        mathItem.fromRCJCode = mathSubArr[0];
        mathItem.fromRCJLibraryCode = this.rule.libraryCode;
        mathItem.oriResQty = mathSubArr[1];
        mathItem.parseMath = mathSubArr[2];
        mathItem.operator = this.mathOperator(mathItem.parseMath);
    }

    async activeRCJ() {
        let item = this.mathItem;
        let fromRCjS = this.findActiveRCJByCode(item.fromRCJCode) || [];
        let updateRcj = fromRCjS.find(r => r.materialCode == this.mathItem.fromRCJCode && (r.resQty == this.mathItem.oriResQty || (r.isNumLock && r.resQtyConversionLock == this.mathItem.oriResQty)))
        if(ObjectUtils.isEmpty(updateRcj)){
            updateRcj = fromRCjS.find(r => r.materialCode === this.mathItem.fromRCJCode)
        }
        if(ObjectUtils.isNotEmpty(updateRcj)){
            item.activeRCJs.push(updateRcj);
        }
    }
}

module.exports = Lxxx$V1$2MathHandler;