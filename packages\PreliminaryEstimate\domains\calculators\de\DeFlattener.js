const { CalculateEngine } = require('../../../core/CalculateEngine/CalculateEngine');
const DeTypeConstants = require("../../../constants/DeTypeConstants");
const {ObjectUtil} = require("../../../../../common/ObjectUtil");
const {deFlattenerBaseFn, deFlattenerRules} = require("./DeFlattenerCodes");
const {NumberUtil} = require("../../../utils/NumberUtil");
const LogUtil = require("../../../core/tools/logUtil");
const CommonConstants = require('../../../constants/CommonConstants');
const FunctionTypeConstants = require('../../../constants/FunctionTypeConstants');
const DeUtils = require('../../utils/DeUtils');

/**
 * 工程量铺设器
 */
class DeFlattener extends CalculateEngine{
    static SPLITOR = "_";
    constructId;
    deRowId;
    unitId;
    resourceId;
    priceCodes = [];
    precision;
    ctx;
    filterTempRemoveRow;//是否过滤临时删除项
    relatedDeRows ;
    digitPropertyMap = new Map();
    static  DebMap = [
        "quantity"
        ,"originalQuantity",
    ];
    initDigitPropertyMap()
    {
        // this.digitPropertyMap.set("resQty",3);
        // this.digitPropertyMap.set("quantity",5);
        // this.digitPropertyMap.set("originalQuantity",5);移除
    }
    convertValue(value,param) {
        // let paramArray = param.split(DeFlattener.SPLITOR);
        // let digits = this.digitPropertyMap.get(paramArray[0]);
        // if(ObjectUtil.isEmpty(digits)) {
        //     digits = 2;
        // }

        // return NumberUtil.numberScale(value, digits);
        return value;
    }
    static getInstance({constructId, unitId,deRowId},ctx,filterTempRemoveRow,priceCodes,functionDataMap){
        return new DeFlattener(constructId,unitId,deRowId,null,ctx,filterTempRemoveRow,priceCodes,functionDataMap);
    }

    /**
     *
     * @param constructId 当前工程
     * @param unitId 当前修改的人材机所属的单位工程
     * @param deRowId 当前修改的人材机所属的定额
     * @param resourceId 为当前修改的人材机ID
     * @param ctx
     */
    constructor(constructId,unitId,deRowId,resourceId,ctx,filterTempRemoveRow,priceCodes,functionDataMap) {
        super(ctx);
        this.ctx = ctx;
        this.constructId = constructId;
        this.unitId = unitId;
        this.deRowId = deRowId;
        this.resourceId = resourceId;
        this.relatedDeRows = [];
        this.initDigitPropertyMap();
        this.filterTempRemoveRow = filterTempRemoveRow;
        this.priceCodes = priceCodes;
        this.precision = functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING);
    }

    prepare()
    {
    }

    async analyze() {
        this.preload(deFlattenerBaseFn);
        this.buildRules();
        await this.render()
    }

    /**
     * 回填数据
     * @returns {Promise<void>}
     */
    async render() {

        for(let currentDeRow of this.relatedDeRows)
        {
            if([DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(currentDeRow.type)){
                continue;
            }
            if(ObjectUtil.isNotEmpty(currentDeRow)) {
                for (let key of DeFlattener.DebMap) {
                    if(key == 'originalQuantity' && (currentDeRow.sequenceNbr == this.deRowId || [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(currentDeRow.parent.type))) {
                        //当前定额不计算原始工程量，子级不允许编辑工程量，存在计算编码表达式的问题比较耗时
                        continue;
                    }
                    let digits = this.digitPropertyMap.get(key);
                    if(ObjectUtil.isEmpty(digits)) {
                        digits = 2;
                    }
                    //工程量精度获取
                    if(key == 'quantity'){
                        digits = DeUtils.getQuantiyPrecision(this.precision,currentDeRow);
                    }
                    let columnKey = key + "_" + currentDeRow.sequenceNbr;
                    let result = NumberUtil.numberScale( this.parser(columnKey),digits);
                    if(currentDeRow.type == DeTypeConstants.DE_TYPE_DELIST){
                        this.instanceMap[columnKey] = result;//刷新最新的数据
                    }
                    currentDeRow[key] = result;
                    LogUtil.renderLogger("DeFCalculator :" + currentDeRow.sequenceNbr + "-------------key :" + key + "-------------value :" + currentDeRow[key]);
                }
            }
        }
    }

    /**
     * 根据设置工程量规则
     * @param rules
     */
    fill4QuantityRules(rules)
    {
        for(let currentDeRow of this.relatedDeRows) {
            if(ObjectUtil.isNotEmpty(currentDeRow)) {
                let parent = this.ctx.deMap.getNodeById(currentDeRow.parentId);
                if(ObjectUtil.isEmpty(currentDeRow.parent)){
                    currentDeRow.parent = parent;
                }
                if (parent.type === DeTypeConstants.DE_TYPE_DEFAULT || parent.type === DeTypeConstants.DE_TYPE_FB || parent.type === DeTypeConstants.DE_TYPE_ZFB)
                {
                    rules[this.getChildQuantityKey(currentDeRow.sequenceNbr)] = deFlattenerRules["quantity"].mathFormula;//初始化工程量规则
                    // rules[this.getChildOriginalQuantityKey(currentDeRow.sequenceNbr)] =  deFlattenerRules["original"].mathFormula;
                }
                else
                {
                    //当前最顶级定额，不随父变更工程量， 用消耗量（减少或增加）满足  当前工程量=消耗量*父工程量 规则
                    if(this.deRowId === currentDeRow.sequenceNbr){
                        rules[this.getChildQuantityKey(currentDeRow.sequenceNbr)] = deFlattenerRules["quantity"].mathFormula;
                    }else{
                        rules[this.getChildQuantityKey(currentDeRow.sequenceNbr)] = this.getResQtyKey(currentDeRow.sequenceNbr) + "*" + this.getParentQuantityKey(currentDeRow.parentId);
                        rules[this.getChildOriginalQuantityKey(currentDeRow.sequenceNbr)] = this.getChildUnitKey(currentDeRow.sequenceNbr) + "*" + this.getChildQuantityKey(currentDeRow.sequenceNbr);
                    }
                }
                rules[this.getChildUnitKey(currentDeRow.sequenceNbr)] = deFlattenerRules["unit"].mathFormula;
                rules[this.getResQtyKey(currentDeRow.sequenceNbr)] = deFlattenerRules["resQty"].mathFormula;//初始化消耗量规则

            }
        }

    }

    /**
     * 递归查找子定额
     * 子定额临时删除的不重新计算
     * @param deRow
     * @param relatedDeRows
     */
    findChildren(deRow,relatedDeRows)
    {
        if(ObjectUtil.isNotEmpty(deRow.children))
        {
            for (let childRow of deRow.children)
            {
                if((this.filterTempRemoveRow && childRow.isTempRemove === CommonConstants.COMMON_YES)
                    || (childRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtil.isNotEmpty(childRow.calculateMethod))
                ){
                    continue;
                }else{
                    relatedDeRows.push(childRow);
                    this.findChildren(childRow,relatedDeRows);
                }
            }
        }

    }


    buildRules() {
        let currentDeRow = this.ctx.deMap.find(item => item.sequenceNbr === this.deRowId );
        if(currentDeRow.type != DeTypeConstants.DE_TYPE_ZFB && currentDeRow.type != DeTypeConstants.DE_TYPE_FB && currentDeRow.type != DeTypeConstants.DE_TYPE_DEFAULT ) {
            this.relatedDeRows.push(currentDeRow);
        }
        let rules = {};
        this.findChildren(currentDeRow,this.relatedDeRows);
        this.fill4QuantityRules(rules);
        this.loadRules(rules);
    }


    getValue({type,kind,column})
    {
        let currentDeRow = this.ctx.deMap.getNodeById(this.deRowId);
        let value;
        switch (type) {
            case `QD`:{
                if (typeof column == 'function') {
                    value = column(currentDeRow);
                } else {
                    value = currentDeRow[column];
                }
                break;
            }
            case `DE`:{
                if (typeof column == 'function') {
                    value = column(currentDeRow);
                } else {
                    value = currentDeRow[column];
                }
                break;
            }
            default:{
                value = {type,kind,column};
                break;
            }
        }
        return value;
    }
    getRuntimeValue({type,kind,column},param)
    {
        let value= 0;
        let key = param.split(DeFlattener.SPLITOR)[1];

        switch (type) {
            case `DE`: {
                let item  = this.relatedDeRows.find(item => item.sequenceNbr === key);
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
                let digits = this.precision.EDIT.DE[column];
                if(key === 'quantity'){
                   digits = DeUtils.getQuantiyPrecision(this.precision,item);
                }
                if(ObjectUtil.isNotEmpty(value)&&ObjectUtil.isNotEmpty(digits)){
                   value = NumberUtil.numberFormat(value,digits);
                }
                break;
            }
            case `runtime`: {
                let item  = this.relatedDeRows.find(item => item.sequenceNbr === key);
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
                let digits = this.precision.EDIT.DE[column];
                if(key === 'quantity'){
                   digits = DeUtils.getQuantiyPrecision(this.precision,item);
                }
                if(ObjectUtil.isNotEmpty(value)&&ObjectUtil.isNotEmpty(digits)){
                   value = NumberUtil.numberFormat(value,digits);
                }
                break;
            }
            case `Resource`: {
                let item  = this.ctx.resourceMap.find(item => item.sequenceNbr === key);
                if (typeof column == "function") {
                    value = column(item);
                } else {
                    value = item[column]
                }
                let digits = this.precision.DETAIL.RCJ[column];
                if(ObjectUtil.isNotEmpty(value)&&ObjectUtil.isNotEmpty(digits)){
                   value = NumberUtil.numberFormat(value,digits);
                }
                break;
            }
        }

        // if(column == "originalQuantity"){
        //     return DeQualityUtils.evalQualityWithCodes(value,this.priceCodes);
        // }
        return eval(value); //兼容表达式
    }
    getResQtyKey(sequenceNbr){
        return "resQty_" + sequenceNbr;
    }

    getParentQuantityKey = (sequenceNbr) => {
        return "quantity_" + sequenceNbr;
    }
    getChildQuantityKey = (sequenceNbr) => {
        return "quantity_" + sequenceNbr;
    }
    getChildOriginalQuantityKey = (sequenceNbr) => {
        return "originalQuantity_" + sequenceNbr;
    }
    getChildUnitKey = (sequenceNbr) => {
        return "unit_" + sequenceNbr;
    }
}
module.exports = {DeFlattener: DeFlattener};