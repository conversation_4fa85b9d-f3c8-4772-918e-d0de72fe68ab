const EE = require('../../../core/ee');
const ConversionInfoStrategy = require("../conversion_information/conversionInfoStrategy");
const {ConversionInfoUtil} = require("./util/ConversionInfoUtil");
const {StandardConvertMod} = require("../enums/ConversionSourceEnum");
const ConversionService = require("./util/ConversionService");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const WildcardMap = require("../core/container/WildcardMap");
const {ObjectUtils} = require("../utils/ObjectUtils");

class ConversionStrategy{

    static UNITE_RULE_KIND = "4";

    constructor() {
        this.service = EE.app.service;
        this.app = EE.app;
        this.commonService = this.service.gongLiaoJiProject.gljProjectCommonService;
    }

   /* async init(constructId, singleId, unitId, deId, currentRules, uniteRules) {
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.deId = deId;
        this.uniteRules = uniteRules;
        // 如果是统一换算，换算规则设置kind
        this.uniteRules?.forEach((v) => {v.kind = ConversionStrategy.UNITE_RULE_KIND});

        this.unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        this.deLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
        const belong = "fbfx";
        this.deBeLong = belong;

        this.de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        this.de.deName = this.deLine.deName;
        this.de.originalDeName = this.de.originalDeName? this.de.originalDeName: this.deLine.deName;
        this.conversionList = this.de.conversionList;


        this.currentRules = ObjectUtils.isNotEmpty(this.uniteRules)
            ? this._formatRuleByUniteRules(this.uniteRules, this.de)
            : currentRules;

        this.deAllRules = this._combineRules(this.unitProject, this.de, this.currentRules);
        this.changedRules = this._getChangedRules(this.deAllRules);
        // 从所有规则中筛选  未启用的规则ID
        this.cancelRuleIds = this.deAllRules.filter((v) => !this.changedRules.find((c) => c.sequenceNbr == v.sequenceNbr))
            .map((v) => v.sequenceNbr);

        if(ObjectUtils.isNotEmpty(this.currentRules) && !this.currentRules[0].isUniteRule){
            // 标准换算设置换算修改
            this._conversionSnapshot(this.de, this.currentRules);
        }else if(ObjectUtils.isNotEmpty(this.uniteRules)){
            // 统一换算设置换算修改
            this.de.defaultConcersions = this.uniteRules;
        }

        this.deUpDateObj = {
            // 换算信息
            conversionInfo: [],
            redArray: [],
            blackArray: [],
            nameSuffixArray: [],
            addedDes: [],
            tcPrice: null,
        }

        this.conversionService = new ConversionService();
    }*/

    async init(constructId, singleId, unitId, deId, currentRules, uniteRules) {
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.deId = deId;
        this.uniteRules = uniteRules;
        // 如果是统一换算，换算规则设置kind
        this.uniteRules?.forEach((v) => {v.kind = ConversionStrategy.UNITE_RULE_KIND});

        this.unitProject = await this.commonService.getUnit(constructId, unitId);
        this.deLine = await this.commonService.findDeByDeId2(constructId, unitId, deId);

        this.de = this.de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        this.isCsxmDe = !!this.deLine.isCsxmDe;

        // 兼容历史：当时补充定额时不执行标准换算
        if(this.de.isSupplement == 1){
            console.log("********Supplementary quota does not follow standard conversion");
            return;
        }

        this.de.conversionList = this.de.conversionList || [];

        this.currentRules = ObjectUtils.isNotEmpty(this.uniteRules)
            ? this._formatRuleByUniteRules(this.uniteRules, this.de)
            : currentRules;

        this.deAllRules = this._combineRules(this.unitProject, this.de, this.currentRules);
        this.changedRules = this._getChangedRules(this.deAllRules);

        if(ObjectUtils.isNotEmpty(this.currentRules) && !this.currentRules[0].isUniteRule){
            // 标准换算设置换算修改
            this._conversionSnapshot(this.de, this.currentRules);
        }else if(ObjectUtils.isNotEmpty(this.uniteRules)){
            // 统一换算设置换算修改
            this.de.defaultConcersions = this.uniteRules;
        }

        this.deUpDateObj = {
            // 换算信息
            conversionInfo: []
        }
        this.conversionInfoStrategy = new ConversionInfoStrategy();
        await this.conversionInfoStrategy.init(this.constructId, this.singleId, this.unitId, this.deId);

        this.conversionService = new ConversionService();

        // 精度
        this.precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        this.jd = {
            resQty: this.precision.DETAIL.RCJ.resQty, // 人材机消耗量精度
            totalNumber: this.precision.DETAIL.RCJ.totalNumber, //人材机数量精度
            quantity: this.precision.EDIT.DE.quantity, //定额工程量精度
            deRcjPrice: this.precision.EDIT.DERCJ.price, //定额人材机单价
        }
    }

    async executeConvInfo(){
        // 兼容历史：当时补充定额时不执行标准换算
        if(this.deLine?.isSupplement == 1){
            console.log("********Supplementary quota does not follow standard conversion");
            return;
        }
        if(ObjectUtils.isEmpty(this.changedRules) && ObjectUtils.isEmpty(this.de.conversionInfo)){
            return;
        }
        for(let rule of this.changedRules){
            // 执行标准换算规则时可能会新增、替换人材机数据，所以在每次执行规则前重新获取定额的人材机数据
            let handler = this._getRuleHandler(rule);
            await handler.addConversionInfo();
        }
        //生成换算信息
        await this._dealConversionInfo(this.de, this.deUpDateObj.conversionInfo, this.de.standardConvertMod);

        //根据换算信息执行标准换算数据计算
        let addedDes = await this.conversionInfoStrategy.execute();
        if(ObjectUtils.isNotEmpty(addedDes)){
            for(let addedDe of addedDes){
                // await new ConversionStrategy(this.constructId,this.singleId,this.unitId,addedDe.sequenceNbr, [], null).executeConvInfo();
                const conversionStrategy = new ConversionStrategy();
                await conversionStrategy.init(
                    this.constructId,
                    this.singleId,
                    this.unitId,
                    addedDe.sequenceNbr,
                    [],
                    null)
                await conversionStrategy.executeConvInfo();

            }
        }
    }

    _dealConversionInfo(curDe, cacheConversionInfo, standardConvertMod){

        //换算信息
        if(ObjectUtils.isEmpty(curDe.conversionInfo)){
            curDe.conversionInfo=[];
        }

        // 以标准数据执行换算，删除人材机操作生成的换算信息
        if(standardConvertMod == StandardConvertMod.Default){
            curDe.conversionInfo = curDe.conversionInfo.filter(info=>info.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE);
        }

        let standardConversionInfoIndex = curDe.conversionInfo.findIndex(info=>info.conversionString == ConversionInfoUtil.STARDARD_CONVERSION_SOURCE);

        // 如果没有执行标准换算，则删除换算信息中标准换算父级节点
        if(ObjectUtils.isEmpty(cacheConversionInfo)){
            if(standardConversionInfoIndex != -1){
                curDe.conversionInfo.splice(standardConversionInfoIndex, 1);
            }
        }else{ // 如果有执行标准换算
            let standardConversionInfo = null;
            if(standardConversionInfoIndex == -1){
                standardConversionInfo = ConversionInfoUtil.initConversionInfo(
                    {},
                    null,
                    ConversionInfoUtil.STARDARD_CONVERSION_SOURCE,
                    curDe.conversionInfo.length
                );
                curDe.conversionInfo.push(standardConversionInfo);
                standardConversionInfoIndex = curDe.conversionInfo.length - 1;
            }else{
                standardConversionInfo = curDe.conversionInfo[standardConversionInfoIndex];
            }

            standardConversionInfo.children = ObjectUtils.cloneDeep(cacheConversionInfo);
        }
    }

    _getRuleHandler(rule){
        return this.conversionInfoStrategy.getRuleHandler(this, rule)
    }

    _getChangedRules(deAllRules){
       return deAllRules.filter((v) => {
           return this._isRuleChanged(v);
        }).sort((a, b) => a.index - b.index);

    }

    _isRuleChanged(rule){
        if(rule.kind == "0"){
            return ObjectUtils.isNotEmpty(rule.selectedRule)
                && rule.selectedRule != "NaN"
        }

        if(rule.kind == "1"){
            return !!rule.selected;
        }

        if(rule.kind=="2"){
            return ObjectUtils.isNotEmpty(rule.clpb?.detailsCode)
                ? (rule.defaultRcjCode != rule.clpb.detailsCode || rule.defaultRcjLibraryCode != rule.clpb.libraryCode)
                : (rule.defaultRcjCode != rule.currentRcjCode || rule.defaultRcjLibraryCode != rule.currentRcjLibraryCode)

        }

        if(rule.kind == "3" || rule.kind == "4" || rule.kind == "0"){
            return ObjectUtils.isNotEmpty(rule.selectedRule)
                && rule.selectedRule != "NaN"
                && rule.selectedRule != rule.defaultValue;
        }

        throw new Error("错误的规则类型，kind=" + rule.kind);
    }

    _combineRules(
        unit,
        de,
        // 有可能是标准 有可能是统一
        currentRules
    ) {
        const combine = [];

        // kind=3,type=b规则新增的换算规则
        if(de.conversionAddByRule){
            combine.push(de.conversionAddByRule);
        }

        if(ObjectUtils.isNotEmpty(currentRules)) {
            combine.push(...currentRules);

            // 是否包含统一换算
            let ifUnified = !!combine?.find(item => item.isUniteRule === true)?.isUniteRule;
            if (ifUnified) {
                combine.push(...de.conversionList);
            } else {
                combine.push(
                    ...this._formatRuleByUniteRules(de.defaultConcersions, de)
                );
            }
        }

        return combine.sort((a, b) => a.index - b.index);
    }


    _formatRuleByUniteRules(unitRules, de) {

        if(ObjectUtils.isEmpty(unitRules)){
            return [];
        }

        const typeMaps = new Map([
            ["人工费", "R"],
            ["机械费", "J"],
            ["材料费", "C"],
            ["主材费", "Z"],
            ["设备费", "S"],
            ["单价", ""]
        ]);

        return unitRules.map((unitRule, index) => {
                const type = typeMaps.get(unitRule.type);

                // 拼接 R*n C*n J*n
                let math = type + "*" + unitRule.val;
                // 默认的规则用 定额id + def +类型标识
                // 注释该seqNo, 直接使用主键newAlgorithm.sequenceNbr; //let seqNo = deId + "def" + type;
                return {
                    sequenceNbr: unitRule.sequenceNbr,
                    type: "0",
                    kind: unitRule.kind || ConversionStrategy.UNITE_RULE_KIND,
                    math: math,
                    relation: math,
                    defaultValue: 1,
                    selectedRule: unitRule.val,
                    fbFxDeId: de.sequenceNbr, // 分部分项或措施项目定额id; ps:标准换算中有fbFxDeId,这里在统一换算中也加上,用于BS端在处理ysf文件时,通过deId+ruleId反查出operatingRecord.
                    index: 999999 + index,
                    libraryCode: de.libraryCode,
                    isUniteRule: true,
                };
            });
    }

    _conversionSnapshot(de, currentRules) {
        // 查询列表时会初始化 如果这时未初始化 直接报错
        de.conversionList = de.conversionList || [];

        de.conversionList.forEach((target)=> {
            const srcRule = currentRules.find((r) => r.sequenceNbr == target.sequenceNbr);
            if(!srcRule){
                return;
            }

            if (typeof srcRule.selected == "string") {
                target.value = srcRule.selected;
            } else {
                target.selected = srcRule.selected;
            }
            target.index = srcRule.index;
            target.clpb = srcRule.clpb;
            target.selectedRule = srcRule.selectedRule;
            if(srcRule.clpb?.detailsCode){
                target.currentRcjCode =  srcRule.clpb.detailsCode;
                target.currentRcjLibraryCode = srcRule.clpb.libraryCode;
                target.rcjId = srcRule.clpb.standardId;
                target.ruleInfo = srcRule.clpb.details;
                if(ObjectUtils.isNotEmpty(target.ruleInfo) && ObjectUtils.isNotEmpty(srcRule.clpb.specification) && !target.ruleInfo.trim().endsWith(srcRule.clpb.specification)){
                    target.ruleInfo += " " + srcRule.clpb.specification;
                }
               srcRule.clpb.specification;
                target.selectedRuleGroup = srcRule.clpb.groupName;
                target.topGroupType = srcRule.clpb.groupName;
            }
        });

        de.conversionList = de.conversionList.sort((a, b) => a.index - b.index);
    }
}

module.exports = ConversionStrategy;
