const {Service} = require('../../../core');
const {GsConversionListItem} = require("../models/GsConversionListItem");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {GljUnitProject} = require("../models/GljUnitProject");
const { Snowflake } = require("../utils/Snowflake");
const {StandardConvertMod} = require("../enums/ConversionSourceEnum");
const { ConvertUtil } = require('../utils/ConvertUtils');
const WildcardMap = require("../core/container/WildcardMap");
const DeTypeConstants = require("../constants/DeTypeConstants");
const {ConversionInfoUtil} = require("../standard_conversion/util/ConversionInfoUtil");
const GsProjectSettingEnum = require("../enums/GljProjectSettingEnum");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");

/**
 * 标准换算 service
 * @class
 */
class GljRuleDetailFullService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    // 标准换算列表
    async initStandardConvertList(args) {
        const {
            standardDeId,
            fbFxDeId,
            constructId,
            unitId,
        } = args;

        let rawLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, fbFxDeId);
        if (!rawLine || !standardDeId) return [];
        const list = await this.service.gongLiaoJiProject.gljBaseRuleDetailFullService.getRuleDetailByDe(rawLine.deCode, rawLine.deName, rawLine.libraryCode);

        let rcjIds = list.filter((v) => v.rcjId != "0").map((v) => v.rcjId);
        let rcjObjs = await this.service.gongLiaoJiProject.gljBaseRcjService.getRcjListByRcjIdList(rcjIds);
        let rcjObjMap = new Map();
        rcjObjs.forEach((v) => rcjObjMap.set(v.sequenceNbr, v));

        let conversionList = list?.map((v, index) => {
            const conversionListItem = new GsConversionListItem();
            return Object.assign(conversionListItem, v, {
                isSelect: false,
                value: v.defaultValue,
                selectValue: v.defaultValue,
                index,
                // kind3 前端渲染KEY
                selectedRule: v.kind === "3" ? v.defaultValue : null,
                // kind2 前端渲染KEY
                ruleInfo: v.kind === "2" ? v.relation : null,
                // kind1 前端渲染KEY
                selected: v.kind === "1" ? false : null,
                defaultValue:
                    v.kind === "1" ? "-" : v.kind === "2" ? v.relation : v.defaultValue,
                selectedRuleGroup: v.topGroupType,
                currentRcjCode: rcjObjMap.get(v.rcjId)?.materialCode,
                currentRcjLibraryCode: rcjObjMap.get(v.rcjId)?.libraryCode,
                defaultRcjCode: rcjObjMap.get(v.rcjId)?.materialCode,
                defaultRcjLibraryCode: rcjObjMap.get(v.rcjId)?.libraryCode,
                defaultRcjName: rcjObjMap.get(v.rcjId)?.materialName,
                defaultRcjSpecification: rcjObjMap.get(v.rcjId)?.specification,
            });
        });
        return conversionList;
    }

    /**
     * 初始化换算数据
     * @returns {Promise<(*&{sequenceNbr: *})[]>}
     */
    async initDef() {
        return this.getDefPiaoZhunHuanSuan.map((v) => {
            return {
                ...v,
                // 设置统一换算的主键id, 后续该id将记录在换算信息以及人材机下挂规则中.BS需要该字段。
                sequenceNbr: Snowflake.nextId(),
            };
        });
    }

    /**
     * 标准换算中 默认换算的模板数据
     * 根据产品要求。只保留R/C/J 三类
     * @return {[{val: number, sort: number, type: string},{val: number, sort: number, type: string},{val: number, sort: number, type: string},{val: number, sort: number, type: string}]}
     */
    getDefPiaoZhunHuanSuan = Object.freeze([
        { sort: 1, type: "人工费", val: 1 },
        { sort: 2, type: "材料费", val: 1 },
        { sort: 3, type: "机械费", val: 1 },
        // 转测演示提出 主材不要了
        {sort: 4, type: "主材费", val: 1},
        {sort: 5, type: "设备费", val: 1},
        {sort: 6, type: "单价", val: 1 },
    ]);

    // 换算数据列表
    async getDefDonversion(constructId, unitId, deId) {
        let de = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
        let rcjList = await this.service.gongLiaoJiProject.gljProjectCommonService.getAllRcjByDeId(constructId, unitId, deId);
        if ((rcjList.length < 1 && de.type !== "03") || (de.type === "06")) {
        // if (de.type === "06") {
            return [];
        }
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        return unitConversionMap?.get(deId)?.defaultConcersions;
    }

    // 换算数据列表
    async setDefDonversion(constructId, unitId, deId, defaultConcersions) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        let conversion = unitConversionMap?.get(deId);
        conversion.defaultConcersions = defaultConcersions;
    }

    async zxgcInitConversion(){
        let initConversionInfo = {
            "sequenceNbr": Snowflake.nextId(),
            "type": "0",
            "kind": "6",
            "math": "R*0.9,J*0.9",
            "relation": "R*0.9,J*0.9",
            "defaultValue": 1,
            "selectedRule": 0.9,
            "fbFxDeId": "7351060228315545600",
            "index": 999999,
            "isUniteRule": true,
            "ruleId": "7351061588213760000",
            "source": "中修工程调整",
            "conversionExplain": "人工*0.9,机械*0.9",
            "conversionString": "R*0.9,J*0.9",
            "sortNo": 0,
            "children": []
        }
        return initConversionInfo;
    }


    // 标准换算列表
    async getStandardConvertList(constructId, unitId, deId, defaultConcersions) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        let conversionModel = unitConversionMap?.get(deId);

        let conversionList = conversionModel?.conversionList || [];

        let deRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD) || [];

        conversionList = conversionList.filter((rule) => {
            if(rule.kind != "2"){
                return true;
            }
            let index = deRcjs.findIndex(rcj => rcj.materialCode == rule.currentRcjCode && rcj.libraryCode == rule.currentRcjLibraryCode);
            if(index < 0){
                return false;
            }
            return true;
        });

        return {
            ...conversionModel,
            conversionList
        };
    }

    // 标准换算列表
    async getStandardConvertListAll(constructId, unitId) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        unitConversionMap = unitConversionMap?unitConversionMap:new Map();
        let conversionRuleList = Array.from(unitConversionMap.values());
        return conversionRuleList;
    }

    // 标准换算列表
    async getStandardConvert(constructId, unitId, deId) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        let deConversion = unitConversionMap?.get(deId);
        if (ObjectUtils.isEmpty(deConversion)) {
            let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
            if (ObjectUtils.isNotEmpty(deModel)) {
                await this.service.gongLiaoJiProject.gljInitDeService.initDeConversion(deModel);
                deConversion = unitConversionMap?.get(deId);
            }
        }
        return deConversion;
    }

    // 标准换算列表
    async setStandardConvert(constructId, unitId, de) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        unitConversionMap?.set(de.sequenceNbr, de);
    }

    // 切换标准换算模式 使用默认值或当前值
    async switchConversionMod(constructId, unitId, deId, standardConvertMod) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        let conversion = unitConversionMap?.get(deId);
        conversion.standardConvertMod = standardConvertMod;
    }

    // 切换主材换算模式
    async switchConversionMainMatMod(constructId, unitId, deId, mainMatConvertMod) {
        let conversionMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        let unitConversionMap = conversionMap?.get(unitId);
        let conversion = unitConversionMap?.get(deId);
        conversion.mainMatConvertMod = mainMatConvertMod;

        await this.switchConversionMainMatModAll(constructId, mainMatConvertMod)
    }

    // 切换主材换算模式
    async switchConversionMainMatModAll(constructId, mainMatConvertMod) {
        let projectDomain = ProjectDomain.getDomain(constructId);
        let conversionMap = projectDomain.functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);

        // 触发全局设置
        let businessMap = ProjectDomain.getDomain(constructId)?.functionDataMap;
        let objMap = businessMap?.get(FunctionTypeConstants.PROJECT_SETTING);
        if (ObjectUtils.isNotEmpty(objMap)) {
            objMap.set(GsProjectSettingEnum.FACTOR_EFFECT, mainMatConvertMod);
        }
        // 工程项目下受到影响的标准换算需要重新计算
        let projects = projectDomain.getProject(item=>item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT)
        for(let project of projects){
            let unitId = project.sequenceNbr;
            let unitConversionMap = conversionMap?.get(unitId);
            if (ObjectUtils.isNotEmpty(unitConversionMap)) {
                for (const [deId, deConversion] of unitConversionMap) {
                    let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
                    if (ObjectUtils.isEmpty(deModel)) {
                        continue;
                    }
                    deConversion.mainMatConvertMod = mainMatConvertMod;
                    let rcjKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
                    let rcjList = projectDomain.getResourceDomain().getResource(rcjKey);
                    let zcRcj = rcjList.find(item => item.kind === 4 || item.kind === 5);
                    if (ObjectUtils.isNotEmpty(zcRcj)) {
                        await this.service.gongLiaoJiProject.gljConversionInfoService.executeConversionInfo(constructId, project.parentId, unitId, deId)
                    }
                }
            }
        }
    }

    /**
     * 标准换算替换定额
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async replaceDe(constructId, unitId, deId, isSaveConversionInfo=false) {
        // 子级定额标准换算
        let deLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
        let childDeConversionMap = new Map();
        for (let child of deLine.children) {
            let childDeConversion = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, child.sequenceNbr);
            childDeConversionMap.set(child.standardId, childDeConversion);
        }

        let de = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
        let deRow = await this.service.gongLiaoJiProject.gljDeService.replaceDe(constructId, unitId, de.standardId, deId);
        let replaceDe = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deRow.sequenceNbr);
        replaceDe.originDefaultConcersions = ConvertUtil.deepCopy(de.originDefaultConcersions)
        replaceDe.defaultConcersions = ConvertUtil.deepCopy(de.defaultConcersions)
        replaceDe.originConversionList = ConvertUtil.deepCopy(de.originConversionList)
        replaceDe.conversionList = ConvertUtil.deepCopy(de.conversionList)
        replaceDe.standardConvertMod = de.standardConvertMod;
        if (isSaveConversionInfo) {
            replaceDe.conversionInfo = ConvertUtil.deepCopy(de.conversionInfo)
            for (let child of deLine.children) {
                let childDe = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, child.sequenceNbr);
                let oldChildDe = childDeConversionMap.get(child.standardId)
                if (ObjectUtils.isEmpty(oldChildDe)) {
                    continue
                }
                childDe.originDefaultConcersions = ConvertUtil.deepCopy(oldChildDe.originDefaultConcersions);
                childDe.defaultConcersions = ConvertUtil.deepCopy(oldChildDe.defaultConcersions)
                childDe.originConversionList = ConvertUtil.deepCopy(oldChildDe.originConversionList)
                childDe.conversionList = ConvertUtil.deepCopy(oldChildDe.conversionList)
                childDe.standardConvertMod = oldChildDe.standardConvertMod;
                childDe.conversionInfo = oldChildDe.conversionInfo?ConvertUtil.deepCopy(oldChildDe.conversionInfo):[]
                for (let conversion of childDe.conversionInfo) {
                    if (ObjectUtils.isNotEmpty(conversion.fbFxDeId) && conversion.fbFxDeId !== childDe.deId) {
                        conversion.fbFxDeId = childDe.deId
                    }
                }
            }
        }
        return deRow;
    }

    /**
     * 标准换算 定额人材机初始化
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async revertDe(constructId, unitId, deId) {
        // 定额
        let deLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);

        // 原始定额
        let originalLine = await this.service.gongLiaoJiProject.gljBaseDeService.getDeAndRcj(deLine.standardId);

        // 子级定额

    }

    /**
     * 人材机还原
     * @param constructId
     * @param unitId
     * @param deId
     * @returns {Promise<void>}
     */
    async revertRcj(constructId, unitId, deId) {
        // 定额

    }

    /**
     * 补丁：处理历史文件 标准换算 kind2 显示配合比材料分组不存在
     * @param constructId
     * @param unitProjects
     * @returns {Promise<void>}
     */
    async processPatch20250905(constructId, unitProjects) {
        let objMap = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
        if (ObjectUtils.isEmpty(objMap)) {
            return;
        }
        for (let unit of unitProjects) {
            let unitConversionMap = objMap?.get(unit.sequenceNbr);
            if (ObjectUtils.isEmpty(unitConversionMap)) {
                return;
            }
            for (const [key, value] of unitConversionMap) {
                if (ObjectUtils.isEmpty(value?.conversionList) && ObjectUtils.isEmpty(value?.originConversionList)) {
                    continue;
                }
                let params = {
                    constructId: constructId,
                    unitId: unit.sequenceNbr,
                    standardDeId: value.standardId,
                    fbFxDeId: key,
                };
                let conversionList = await this.initStandardConvertList(params);
                for (const conversionitem of value.conversionList) {
                    let newConversionItme = conversionList.find(item => item.sequenceNbr === conversionitem.sequenceNbr);
                    if (conversionitem?.kind === '2' &&
                        ObjectUtils.isNotEmpty(conversionitem?.topGroupType) &&
                        ObjectUtils.isNotEmpty(newConversionItme?.topGroupType)
                    ) {
                        conversionitem.topGroupType = newConversionItme.topGroupType;
                        conversionitem.selectedRuleGroup = newConversionItme.selectedRuleGroup;
                    }
                }
                for (const conversionitem of value.originConversionList) {
                    let newConversionItme = conversionList.find(item => item.sequenceNbr === conversionitem.sequenceNbr);
                    if (conversionitem?.kind === '2' &&
                        ObjectUtils.isNotEmpty(conversionitem?.topGroupType) &&
                        ObjectUtils.isNotEmpty(newConversionItme?.topGroupType)
                    ) {
                        conversionitem.topGroupType = newConversionItme.topGroupType;
                        conversionitem.selectedRuleGroup = newConversionItme.selectedRuleGroup;
                    }
                }
                unitConversionMap?.set(value.sequenceNbr, value);
            }
        }
    }

}

GljRuleDetailFullService.toString = () => '[class GljRuleDetailFullService]';
module.exports = GljRuleDetailFullService;
