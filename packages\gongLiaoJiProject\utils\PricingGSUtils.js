const {ObjectUtils} = require("./ObjectUtils");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");


class PricingGSUtils{

    /**
     * 递归获取该单项下的所有单位
     * @param singleProjects
     * @param allProjects
     */
    getAllProjectsByCurrentNode(singleProjects, allProjects) {
        if (ObjectUtils.isNotEmpty(singleProjects)) {
            singleProjects.forEach(item => {
                // if (item.type !== ProjectTypeConstants.PROJECT_TYPE_UNIT) {

                // item转object类型
                item = Object.assign({}, item);
                allProjects.push(item);
                // }
                this.getAllProjectsByCurrentNode(item.children, allProjects);
            });
        }
    }

    /**
     * 递归获取该单项下的所有单位
     * @param singleProjects
     * @param unitProjects
     */
    getUnitProjectsByCurrentNode(singleProjects, unitProjects) {
        if (ObjectUtils.isNotEmpty(singleProjects)) {
            singleProjects.forEach(item => {
                if (item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
                    unitProjects.push(item);
                } else {
                    this.getUnitProjectsByCurrentNode(item.children, unitProjects);
                }
            });
        }
    }

    /**
     * 获取当前节点的所有单项
     * @param singleObj
     * @param singleProjects
     */
    getSingleProjectsByCurrentNode(singleObj, singleProjects) {
        if (ObjectUtils.isNotEmpty(singleObj)) {
            if(singleObj.children){
                singleObj.children.forEach(item => {
                    if (item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
                        singleProjects.push(item);
                        this.getUnitProjectsByCurrentNode(item.children, singleProjects);
                    }
                });
            }
        }
    }


    /**
     * 过滤树结构数据，仅保留指定的字段。
     *
     * @param {Array|Object} tree - 树结构数据。
     * @param {Array} fieldsToKeep - 要保留的字段列表。
     * @return {Array|Object} - 过滤后的树结构数据。
     */
    filterTreeData(tree, fieldsToKeep = ['sequenceNbr', 'name', 'ifCheck']) {
        if (Array.isArray(tree)) {
            return tree.map(node => this.filterTreeData(node, fieldsToKeep));
        } else if (typeof tree === 'object' && tree !== null) {
            return fieldsToKeep.reduce((filteredNode, field) => {
                if (tree.hasOwnProperty(field)) {
                    filteredNode[field] = tree[field];
                }
                return filteredNode;
            }, {});
        } else {
            // 如果不是对象或数组，直接返回原值
            return tree;
        }
    }

}

module.exports = {
    PricingGSUtils: new PricingGSUtils()
};
