const {RCJKind} = require("../../enums/ConversionSourceEnum");
const MathItemHandler = require("./mathItemHandler");
const {ObjectUtil} = require("../../../../common/ObjectUtil");

/**
 * 单条规则处理math，包含以下情况（运算符以*为例子）：
 *    1. *n 整体乘
 *    2. R/C/J*n 对应R/C/J 乘
 */
class RCJPriceMathHandler extends MathItemHandler{
    constructor(ruleCtx, math) {
        super(ruleCtx, math);
        this.dealShowMath();
    }

    dealShowMath(){
        this.showMath = this.conversionService.mathAfterCalculation(this.formatMath, this.showMathDigits);
    }

    analysisMath() {
        const rcjActionKindMap = this.rcjKindMap;

        let mathItem = this.mathItem;
        mathItem.type = 1;

        let firstCharacter = mathItem.math.charAt(0);
        if("RCJZS".includes(firstCharacter)){
            mathItem.RJCSymbol = firstCharacter;
            mathItem.parseMath = mathItem.math.substring(1);
            mathItem.operator = this.mathOperator(mathItem.parseMath);
            mathItem.activeRCJKind = rcjActionKindMap.get(firstCharacter);

        }else if("+-*/".includes(firstCharacter)){
            mathItem.parseMath = mathItem.math;
            mathItem.RJCSymbol = "ALL";
            mathItem.operator = this.mathOperator(mathItem.parseMath);
            mathItem.activeRCJKind = rcjActionKindMap.get("ALL");
        }else{
            throw new Error(`错误的换算公式 ${mathItem.oriMath}`);
        }
    }

    activeRCJ() {
        this.mathItem.activeRCJs = this.effectDeRCJ.filter((rcj) => {
            return this.isRcjActive(rcj, this.ctx.de.mainMatConvertMod);
        });
    }

    isRcjActive(rcj, isMainMatConvertMod){
        if (rcj.kind == RCJKind.主材 && !isMainMatConvertMod) {
            return false;
        }
        if (rcj.kind == RCJKind.设备 && !isMainMatConvertMod) {
            return false;
        }
        if (this.isOtherRCj(rcj)) {
            return false;
        }
        if (rcj.isFyrcj === 0) {
            return false;
        }

        if(ObjectUtil.isNotEmpty(this.ruleCtx.excludeMaterialCodes)
            && rcj.libraryCode === this.rule.libraryCode
            && this.ruleCtx.excludeMaterialCodes.includes(rcj.materialCode)
        ){
            return false;
        }

        return this.mathItem.activeRCJKind.includes(rcj.kind);
    }
}

module.exports = RCJPriceMathHandler;