const {Service} = require('../../../core');
const ProjectDomain = require('../domains/ProjectDomain');
const StepItemCostLevelConstant = require("../../../electron/enum/StepItemCostLevelConstant");
const BranchProjectDisplayConstant = require("../../../electron/enum/BranchProjectDisplayConstant");
const {Snowflake} = require("../utils/Snowflake");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ParamUtils} = require("../../../core/core/lib/utils/ParamUtils");
const _ = require("lodash");
const ConstantUtil = require('../../../electron/enum/ConstantUtil');
const { BaseSpecialtyMeasures } = require('../../../electron/model/BaseSpecialtyMeasures');
const CommonConstants = require('../constants/CommonConstants');
const { BaseList } = require('../../../electron/model/BaseList');
const { In } = require('typeorm');
const os = require('os');
const fs = require('fs');
const xeUtils = require("xe-utils");
const ConstructionMeasureTypeConstant = require('../../../electron/enum/ConstructionMeasureTypeConstant');
const StandardDeModel = require('../domains/deProcessor/models/StandardDeModel');
const RemoveStrategy = require("../../../electron/main_editor/remove/removeStrategy");
const CalculationTool = require("../../../electron/unit_price_composition/compute/CalculationTool");
const {UPCContext} = require("../../../electron/unit_price_composition/core/UPCContext");
const DeTypeConstants = require('../constants/DeTypeConstants');
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const CsxmDomain = require('../domains/CsxmDomain');
const DeQualityUtils = require("../domains/utils/DeQualityUtils");
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ConvertUtil} = require("../utils/ConvertUtils");
const PropertyUtil = require("../domains/utils/PropertyUtil");
const {NumberUtil} = require('../utils/NumberUtil');
const WildcardMap = require('../core/container/WildcardMap');
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const ResourceKindConstants = require('../constants/ResourceKindConstants');
const UnitUtils = require("../core/tools/UnitUtils");
const CostDeMatchConstants = require('../constants/CostDeMatchConstants');
const {Tree} = require("../../../electron/main_editor/tree");
const {PricingGSUtils} = require("../../gongLiaoJiProject/utils/PricingGSUtils");
const csxmTemplateArr = require('../jsonData/glj_csxm_template.json');
const { MD5 } = require('crypto-js');

const OpenLevelList = {
    ALL: {type: 0, name: "展开所有"},
    ONE: {type: 1, name: "一级标题"},
    TWO: {type: 2, name: "二级标题"},
    THREE: {type: 3, name: "措施项"},
    DE: {type: 4, name: "子目"},
    ZHUCAI: {type: 5, name: "主材/设备"}
}

/**
 * 措施项目service
 */
class GljStepItemCostService extends Service {

    Empt_Line = () => {
        return {
            "kind": StepItemCostLevelConstant.qd,
            "name": "",
            isEmpData: StepItemCostLevelConstant.emptData
        }
    };
    DEFAULT_MEASURE_TYPES = ["安全生产、文明施工费", "其他总价措施项目", "单价措施项目"];
    DEFAULT_MEASURE_TYPES_ENUM = {
        "单价措施项目": 1,
        "安全生产、文明施工费": 2,
        "其他总价措施项目": 3
    }

    Default_Frist_Line = () => { // 安文费
        return {
            "kind": StepItemCostLevelConstant.bt,
            "name": this.DEFAULT_MEASURE_TYPES[0],
            "itemCategory": this.DEFAULT_MEASURE_TYPES[0],
            "constructionMeasureType": this.DEFAULT_MEASURE_TYPES_ENUM[this.DEFAULT_MEASURE_TYPES[0]],
            "measureType": "1",
            "defaultLine": 1,
        }
    };
    Default_SEC_Line = () => { // 其他总价
        return {
            "kind": StepItemCostLevelConstant.bt,
            "name": this.DEFAULT_MEASURE_TYPES[1],
            "itemCategory": this.DEFAULT_MEASURE_TYPES[1],
            "constructionMeasureType": this.DEFAULT_MEASURE_TYPES_ENUM[this.DEFAULT_MEASURE_TYPES[1]],
            "measureType": "2",
            "defaultLine": 2,
        }
    }
    Default_THR_Line = () => {   // 单价措施
        return {
            "kind": StepItemCostLevelConstant.bt,
            "name": this.DEFAULT_MEASURE_TYPES[2],
            "itemCategory": this.DEFAULT_MEASURE_TYPES[2],
            "constructionMeasureType": this.DEFAULT_MEASURE_TYPES_ENUM[this.DEFAULT_MEASURE_TYPES[2]],
            "measureType": "3",
            "defaultLine": 3,
        }
    };

    disPlayType = {
        "0": " ",
        "01": "部",
        "02": "部",
        "03": "清",
        "04": "定"
    }

    constructor(ctx) {
        super(ctx);
        this._baseBranchProjectOptionService = this.service.baseBranchProjectOptionService;
        this._listFeatureProcess = this.service.listFeatureProcess;
        this._baseFeeFileService = this.service.baseFeeFileService;
        this._unitPriceServices = this.service.unitPriceService;
        this._baseQdDeProcess = this.service.baseQdDeProcess;
        this.rcjProcess = this.service.rcjProcess;
        this.quantitiesService = this.service.quantitiesService;
    }

    itemBillProjectProcess = this.service.itemBillProjectProcess;

    lockQd(constructId, singleId, unitId, qdId) {
        let allDatas = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item.unitId === unitId);
        this._baseBranchProjectOptionService.lockLine(allDatas, qdId);
        return true;
    }

    unLockQd(constructId, singleId, unitId, qdId) {
        let allDatas = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item.unitId === unitId);
        this._baseBranchProjectOptionService.unLockLine(allDatas, qdId);
        return true;
    }

    /**
     * 初始化措施项目
     * 生成如下结构：
     * =====================
     *    ·措施项目
     *    ·单价措施项目
     *    ·安全生成、文明施工费
     *    ·其他总价措施项目
     * =====================
     * @param constructId
     * @param singleId
     * @param unitId
     * @param templateName
     */
    async initItemCost(constructId, unitId, parentId,bukeParentId, csxmDomain,templateName) {
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let constructMajorType = unitProject.constructMajorType;
        let measureList = [];
        for(let item of csxmTemplateArr){
            if(item.specialtyCode === constructMajorType){
                measureList.push(item);
            }
        }
        if(ObjectUtils.isNotEmpty(measureList)){
            measureList.sort((a,b)=>a.dataOrder-b.dataOrder);
            for (let index = 0; index < measureList.length; ++index) {
                let rowData = measureList[index];
                if( rowData.measuresType === '2'){
                    continue;
                }
                let curParentId =parentId;
                let lineData = new StandardDeModel(constructId,unitId,Snowflake.nextId(), curParentId,DeTypeConstants.DE_TYPE_DELIST);
                lineData.deName = rowData.description;
                lineData.deCode = rowData.dataOrder+"";
                lineData.pricingMethod = 2;
                lineData.price=0;
                lineData.baseJournalPrice=0;
                lineData.totalNumber=0;
                lineData.baseJournalTotalNumber=0;
                if (ObjectUtils.isNotEmpty(rowData.zjcsClassCode)) {
                    lineData.zjcsClassCode = rowData.zjcsClassCode;
                    if (lineData.deName == '土建修缮施工与生产同时进行增加费' && unitProject.deLibrary != '2023-FWXS-DEX-TJ') {
                        // 土建修缮施工与生产同时进行增加费，在土建修缮中会下挂到“土建修缮施工与生产同时进行增加费”的措施项下
                        // 土建修缮施工与生产同时进行增加费 的zjcsClassCode在配置中默认就是“22”
                        // 在其他专业中，会下挂到“施工与生产同时进行增加费”下， “施工与生产同时进行增加费”的zjcsClassCode为“11”
                        lineData.zjcsClassCode = '11';
                    }
                }
                let newModel = await csxmDomain.createDeRow(lineData);
                if(newModel.deName.indexOf('脚手架')>-1){
                    newModel.itemCategory = '脚手架';
                }
            }
        }
    }

    searchForsequenceNbr(constructId, singleId, unitWorkId, sequenceNbr) {
        let allNodes = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item.unitId === unitWorkId);
        return this._baseBranchProjectOptionService.searchForsequenceNbr(sequenceNbr, allNodes);
    }

    /**
     * 增加数据
     * @param {*} constructId 
     * @param {*} singleId 
     * @param {*} unitId 
     * @param {*} pointLine 
     * @param {*} newLine  01  分部，02 子项 分部，03 子分部，04定额
     * @param {*} bzhs 
     * @param {*} option 
     * @returns 
     */

    async save(constructId, singleId, unitId, pointLine, newLine, bzhs,option="save") {

        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        let curDe = csxmDomain.getDeById(pointLine.sequenceNbr);
        let parentId = null;
        let index = null;
        let deType = null;
        let child = [];
        let removedChild = false;
        let rootDe = csxmDomain.getRoot(unitId);
        if(newLine.kind === "04"){
            if([DeTypeConstants.DE_TYPE_EMPTY,DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_USER_RESOURCE,DeTypeConstants.DE_TYPE_RESOURCE,DeTypeConstants.DE_TYPE_ANZHUANG_FEE,DeTypeConstants.DE_TYPE_ZHUANSHI_FEE].includes(curDe.type)){
                parentId = curDe.parentId;
                index = curDe.index+1;
            }else if(curDe.type === DeTypeConstants.DE_TYPE_DELIST){
                parentId = curDe.sequenceNbr;
            }else{
                return;
            }
            deType = DeTypeConstants.DE_TYPE_DE;
        }else if(newLine.kind === "03"){
            parentId = curDe.sequenceNbr;
            deType = DeTypeConstants.DE_TYPE_DELIST;
            if(curDe.type === DeTypeConstants.DE_TYPE_DELIST){
                parentId = curDe.parentId;
                index = curDe.index+1;
            }            
        }else if(newLine.kind === "02"){
            deType = DeTypeConstants.DE_TYPE_ZFB;
            parentId = curDe.sequenceNbr;
            if(curDe.type === DeTypeConstants.DE_TYPE_ZFB){
                parentId = curDe.parentId;
            }else if(curDe.type === DeTypeConstants.DE_TYPE_DELIST){
                deType = DeTypeConstants.DE_TYPE_DELIST;
            }else{
                if(curDe.children){
                    curDe.children.forEach(item=>child.push(item));
                }
            }
        }else if(newLine.kind === "01"){
            parentId = rootDe.sequenceNbr;
            if(curDe === rootDe && curDe.children&&curDe.children.length>0&&curDe.children[0].type === DeTypeConstants.DE_TYPE_DELIST){
                if(curDe.children){
                    curDe.children.forEach(item=>child.push(item));
                }
                newLine.kind ="02"
            }
            if(curDe.type === DeTypeConstants.DE_TYPE_DELIST){
                if(rootDe.sequenceNbr===curDe.parentId){
                    rootDe.children.forEach(item=>child.push(item));     
                    newLine.kind ="02";
                    curDe = rootDe;                   //当前为rootDe
                }else{
                    
                    let parentDe = csxmDomain.getDeById(curDe.parentId);
                    newLine.kind = "02";
                    parentDe.children.forEach(item=>{
                        if(item.index>=curDe.index){
                        child.push(item)
                    }});
                    parentDe.children = parentDe.children.filter(item=>item.index<curDe.index);
                    parentId = parentDe.parentId;
                    curDe = parentDe;
                    removedChild = true;
                }
            }
            deType = DeTypeConstants.DE_TYPE_FB;
            if(DeTypeConstants.DE_TYPE_FB === curDe.type){
                index = curDe.index+1;
            }
            if(DeTypeConstants.DE_TYPE_ZFB === curDe.type){
                index = curDe.index+1;
                parentId = curDe.parentId;
                deType = DeTypeConstants.DE_TYPE_ZFB;
            }
        }else{
            return ;
        }
        let deModel = new StandardDeModel(constructId,unitId,Snowflake.nextId(),parentId,deType);
        
        //所有清单降级
        if(newLine.kind === "02" && child.length>0 && child[0].type === DeTypeConstants.DE_TYPE_DELIST){
            curDe.children = removedChild?curDe.children:[];

            deModel = await csxmDomain.createDeRow(deModel,index);
            deModel.pricingMethod=[DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deModel.type)?null:2;
            
            deModel.displaySign = BranchProjectDisplayConstant.open;
            child.forEach(item=>{item.parentId = deModel.sequenceNbr; item.parent = deModel;});
            deModel.children = child;
        }else{            
            if(deModel.type === DeTypeConstants.DE_TYPE_DE){
                deModel.type =DeTypeConstants.DE_TYPE_EMPTY;//新增空定额行
            }
            deModel = await csxmDomain.createDeRow(deModel,index);
            if(deType === DeTypeConstants.DE_TYPE_DELIST){
                deModel.pricingMethod=1;
            }
        }
        if(deModel.type === DeTypeConstants.DE_TYPE_DELIST && deModel.pricingMethod === 1){
            let feeItem  =  ObjectUtils.getMapWithKeysStartingWith2(csxmDomain.functionDataMap.get(FunctionTypeConstants.UNIT_QFB),unitId);
            if(ObjectUtils.isNotEmpty(feeItem)){
                deModel.costMajorName = feeItem[0].freeProfession;
                // 取费文件id
                deModel.costFileCode = feeItem[0].qfCode;
            }
        }
        await csxmDomain.notify(deModel,false);
        this._resetIndex(rootDe);
        return {
            "data": CsxmDomain.filter4DeTree(deModel),
            "index": deModel.index
        };
    }

    _resetIndex(rootDe){
        if(ObjectUtil.isEmpty(rootDe) || ObjectUtil.isEmpty(rootDe.children)){
            return;
        }
        let index = 0;
        for(let de of rootDe.children){
            de.index = index++;
            this._resetIndex(de);
        }
    }


    addDeTempDel(constructId, singleId, unitWorkId,de){
        //处理临时锁定数据  新增的是定额需要处理
        if(de.kind=="04"){
            let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
            //获取父级清单
            let itemBillProject = allData.filter(qd=>qd.sequenceNbr==de.parentId)[0];
            if(itemBillProject.tempDeleteFlag){
                de.tempDeleteFlag=true;
                //处理定额数据
                this.service.itemBillProjectOptionService.setDeOrQdTempDelStatust(constructId, singleId, unitWorkId,allData,de,true,unit);
            }
        }
    }

    /**
     * 由列表中更新行内元素
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @param kind
     * @param indexId
     * @param unit
     */
    async upDateOnList(constructId, singleId, unitId, pointLineId, upDateInfo) {
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        let deRow = csxmDomain.getDe(item=>item.unitId=== unitId && item.sequenceNbr === pointLineId);
        let isAutoCostMath = true;//与updatede  相关联，，不用让updatede调用自动通知接口了
        if(upDateInfo.column === 'costFileCode'){
            const costMajorItem = await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFile(upDateInfo.value);
            if(ObjectUtils.isNotEmpty(costMajorItem)){
                await csxmDomain.updateChargingDiscipline(constructId,unitId,deRow.sequenceNbr,costMajorItem.qfCode,costMajorItem.qfName);
            }
        }else if(upDateInfo.column === 'originalQuantity' || upDateInfo.column === 'quantityExpression'){
            let resetDeQuantities = upDateInfo.resetDeQuantities !== false;//先置为true，看后期需求
            let originalQuantity = upDateInfo.value;
            if(ObjectUtil.isNotEmpty(originalQuantity) && !ObjectUtil.isNumber(originalQuantity)){
                if (String(originalQuantity).toUpperCase().includes("GCLMXHJ")){
                    resetDeQuantities = false;
                }
            }
            await csxmDomain.updateQuantity(constructId, unitId, deRow.sequenceNbr, originalQuantity,true,true, resetDeQuantities,[], null);
            isAutoCostMath = false;

            // 修改关联子目工程量
            let relationDeList = await this.existRelationDe({constructId, deRowId: deRow.sequenceNbr});
            if (ObjectUtil.isNotEmpty(relationDeList)) {
                // 获取子目规则 zmVariableRuleList
                let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
                let unitQuantiesMap = quantitiesMap.get(unitId);
                let pointLine = unitQuantiesMap.get(deRow.sequenceNbr);
                let zmVariableRuleList = pointLine.zmVariableRuleList
                if (ObjectUtil.isNotEmpty(zmVariableRuleList)) {
                    let  unitNbr = UnitUtils.removeCharter(deRow.unit);
                    if(ObjectUtil.isEmpty(unitNbr)){
                        unitNbr = 1;
                    }
                    let originalQuantity2 = NumberUtil.numberScale(deRow.quantity * unitNbr,5);
                    zmVariableRuleList.find(item => item.variableCode === 'GCL' || item.variableCode === 'HSGCL').resultValue = originalQuantity2;
                    let zmVariableRule = zmVariableRuleList.find(item => item.variableCode === 'HSGCL')
                    for (let relationDe of relationDeList) {
                        // 修改子目关联的费用代码
                        let zmPriceCodes = [
                            {code: 'GCL',price: originalQuantity2}
                        ]
                        if (zmVariableRule.variableCode === 'HSGCL') {
                            zmPriceCodes = [
                                {code: 'HSGCL',price: originalQuantity2}
                            ]
                        }
                        await this.service.gongLiaoJiProject.gljDeService.setDeCostCode(constructId, unitId, relationDe.sequenceNbr, zmPriceCodes);

                        // 计算子目工程量
                        let result = await this.service.gongLiaoJiProject.gljBaseDeRelationService.zmCalculateQuantity(constructId, zmVariableRuleList, relationDe);
                        let relationDeQuantityExpression = relationDe.quantityExpression
                        await csxmDomain.updateQuantity(constructId, unitId, relationDe.sequenceNbr, result.quantity,true,true, resetDeQuantities,[], null);

                        let relationDe2 = ProjectDomain.getDomain(constructId).csxmDomain.getDe(item => item.unitId === unitId && item.sequenceNbr === relationDe.sequenceNbr);
                        if (ObjectUtil.isNotEmpty(relationDe2)){
                            relationDe2.quantityExpression = relationDeQuantityExpression
                        }
                    }
                }
            }
        }else if(upDateInfo.column === 'price' || upDateInfo.column === 'baseJournalPrice'){
            let price = upDateInfo.value;
            let deRowId = deRow.sequenceNbr;
            //单价修改按比列分配逻辑 
            await csxmDomain.updatePrice(constructId, unitId, deRowId, price);
         }else if(upDateInfo.column === 'RSum' || upDateInfo.column === 'CSum'||upDateInfo.column === 'JSum'||upDateInfo.column === 'SSum'||upDateInfo.column === 'ZSum'
            ||upDateInfo.column === 'RDSum' || upDateInfo.column === 'CDSum'||upDateInfo.column === 'JDSum'||upDateInfo.column === 'SDSum'||upDateInfo.column === 'ZDSum'
         ){
            let type = this._getTypeByColumn(upDateInfo.column);
            let price = upDateInfo.value;
            let deRowId = deRow.sequenceNbr;
            if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
                await csxmDomain.updatePrice(constructId, unitId, deRowId, price);
            }else{
                await csxmDomain.updateRCJPrice(constructId, unitId, deRow, price, type);
            }
         } else if(upDateInfo.column === 'calculateBase') {
            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            const codes = {
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                qfMajorType:unitProject.qfMajorType
            };
            let unitCostCodePrices = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.getUnitCostCodePrice(codes);
            let baseCodes = [];
            let baseValue = upDateInfo.value;
            let tokens = DeQualityUtils.evalQualityTokens(baseValue);
            if(ObjectUtils.isNotEmpty(tokens) && ObjectUtils.isNotEmpty(unitCostCodePrices)){
                tokens.sort((a,b)=>b.length-a.length);//包含编码先长后短
               for(let token of tokens){
                    let itemUnitCost = unitCostCodePrices.find(itemCost=>itemCost.code == token);
                    baseCodes.push(itemUnitCost);
                    baseValue = baseValue.replaceAll(token,itemUnitCost.name);
               }
            }
            
            deRow[upDateInfo.column] =  upDateInfo.value;
            deRow['baseDescription'] = baseValue;
            deRow['calculateBaseValue'] = DeQualityUtils.evalQualityWithCodes(upDateInfo.value, baseCodes);
            await csxmDomain.updateDe(deRow,true);
            await csxmDomain.notify(deRow, false);
         }else if(upDateInfo.column === 'adjustmentCoefficient'){
            if(ObjectUtils.isEmpty(upDateInfo.value)){
                return {
                    "enableUpdatePrice": false,
                    "lineInfo": {...deRow,parent:null,children:null,prev:null,next:null}
                };
            }
            // let pricingMethod = ProjectDomain.getDomain(constructId).getRoot().pricingMethod;
            
            // if(deRow.type === DeTypeConstants.DE_TYPE_FB || deRow.type === DeTypeConstants.DE_TYPE_ZFB){
            //     for(let child of deRow.children){
            //         //按市场价组价还是不按市场价组价
            //         let price = pricingMethod == '1'?child.price:child.baseJournalPrice;
            //         if(child.type === DeTypeConstants.DE_TYPE_DELIST && price <= 0){
            //             child[upDateInfo.column] = upDateInfo.value;

            //         }
            //     }
            // }
            
            let childs = [];
            let deRowLists = []
            let childOldAdjustMap = new Map();
            childOldAdjustMap.set(deRow.sequenceNbr,deRow.adjustmentCoefficient);
            if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DELIST].includes(deRow.type)){
                this._getUnAdjustChildren(deRow,childs);
                for(let child of childs ){
                    if([DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE, DeTypeConstants.DE_TYPE_RESOURCE, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_RESOURCE].includes(child.type)){
                        deRowLists.push(child);
                    }else{
                        
                        childOldAdjustMap.set(child.sequenceNbr,child.adjustmentCoefficient);
                        if(ObjectUtils.isNotEmpty(child.isAdjustmented)){
                            continue;
                        }
                        child[upDateInfo.column] = upDateInfo.value;
                        await csxmDomain.updateDe(deRow,true)
                    }
                }
            }else{
                deRowLists.push(deRow);
            }
            deRow.isAdjustmented = CommonConstants.COMMON_YES;
            let resultList = [];
            if(ObjectUtils.isNotEmpty(deRowLists)){

                deRowLists.forEach(item => {
                    let rcjs = csxmDomain.ctx.resourceMap.findByValueProperty('deRowId', item.sequenceNbr);
                    if(ObjectUtils.isNotEmpty(rcjs)){
                        childOldAdjustMap.set(item.sequenceNbr,childOldAdjustMap.get(item.parentId));
                        resultList = resultList.concat(rcjs.map(mItem=>mItem.value).filter(ritem => ObjectUtils.isEmpty(ritem.isDeResource) || ritem.isDeResource=== CommonConstants.COMMON_NO));
                    }
                });
            }
            //先更新定额，然后更新人材机消耗量为
            if(ObjectUtils.isNotEmpty(resultList)){
                for(let rcjDetail of resultList){
                    if(rcjDetail.isNumLock){//锁定数量不调整消耗量，防止影响数量
                        continue;
                    }
                    let oldAdjust = childOldAdjustMap.get(rcjDetail.deId);
                    if(ObjectUtils.isEmpty(oldAdjust) || oldAdjust==0){
                        oldAdjust = 1;
                    }
                    let updatedQty = NumberUtil.numberScale(NumberUtil.multiply(upDateInfo.value, rcjDetail.resQty/oldAdjust), 5);
                    rcjDetail.adjustmentCoefficient = upDateInfo.value;
                    await this.service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
                        , deId:rcjDetail.deRowId
                        , rcjDetailId:rcjDetail.sequenceNbr
                        , constructRcj:{
                          resQty : updatedQty
                        },
                        adjustmentCoefficient: true
                    });

                }
            }
            deRow[upDateInfo.column] = upDateInfo.value;
            await csxmDomain.updateDe(deRow,true);
            await csxmDomain.notify(deRow, false);

         }else if(upDateInfo.column === 'rate'){
            let inputValue = upDateInfo.value;
            let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(this.constructId);
            //限制位数
            inputValue = NumberUtil.numberFormat(inputValue,precision.EDIT.DE.freeRate);
            deRow[upDateInfo.column] = inputValue;

            await csxmDomain.updateDe(deRow,true);
            await csxmDomain.notify(deRow, false);

         }else if(upDateInfo.column === 'pricingMethod'){
            await this.updateDePricingMethod(deRow, csxmDomain, upDateInfo.value, pointLineId, constructId, singleId, unitId);
            
        } else if (upDateInfo.column === 'unit') {
            let resetDeQuantities = true;//先置为true，看后期需求
            await csxmDomain.updateUnit(deRow.constructId, deRow.unitId, deRow.deRowId, upDateInfo.value);
            //deRow[upDateInfo.column] = upDateInfo.value;
            let originalQuantity = deRow.quantityExpression;
            await csxmDomain.updateQuantity(constructId, unitId, deRow.sequenceNbr, originalQuantity,true,true, false,[], null);
         }else if(upDateInfo.column === 'annotations'){
            let copyDe = Object.assign({}, deRow);
            copyDe[upDateInfo.column] = upDateInfo.value;
            copyDe.isShowAnnotations = false;
            await csxmDomain.updateDe(copyDe,true);
         }else if(upDateInfo.column === 'type'){
            let copyDe = Object.assign({}, deRow);
            copyDe['deResourceKind'] = upDateInfo.value;
            await csxmDomain.updateDe(copyDe,true);
        } else if (upDateInfo.column === 'deName') {
            // 如果修改了deName，需要处理对应的zjcsClassCode
            let copyDe = Object.assign({}, deRow);
            copyDe[upDateInfo.column] = upDateInfo.value;
            if (ObjectUtils.isEmpty(deRow.zjcsClassCode)) {
                // zjcsClassCode不能被修改  只能被赋值一次
                copyDe['zjcsClassCode'] = await this.getZjcsClassCode(constructId, unitId, upDateInfo.value);
            }
            await csxmDomain.updateDe(copyDe, true);
        }else{
            let copyDe = Object.assign({}, deRow);
            copyDe[upDateInfo.column] = upDateInfo.value;
            await csxmDomain.updateDe(copyDe,true);
        }
        // 同步更新直接分摊费
        if(deRow.pricingMethod===1) {
            await this.service.gongLiaoJiProject.gljRcjCollectService.updateShareCost(deRow.constructId, deRow.unitId, deRow.deRowId, deRow.totalNumber, deRow.baseJournalTotalNumber);
        }
        //通知费用汇总更新
        try {
            
            if(isAutoCostMath){
                //自动计算费用定额
                await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
                    unitId: unitId,
                    singleId: null,
                    constructId: constructId
                });

            }else{
                await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
                  constructId: deRow.constructId,
                  unitId: deRow.unitId,
                  qfMajorType: deRow.costFileCode
                });
            }   
          } catch (error) {
            console.error("捕获到异常:", error);
          }
        
        let deModel = CsxmDomain.filter4DeTree(deRow);
        return {
            "enableUpdatePrice": true,
            "lineInfo": {...deModel,parent:null,children:null,prev:null,next:null}
        };
    }

    _getUnAdjustChildren(deRow,childs){
        if(ObjectUtils.isEmpty(deRow.children)){
            return ;
        }
        for(let child of deRow.children){
            childs.push(child);
            this._getUnAdjustChildren(child,childs);
        }
    }

    async getZjcsClassCode(constructId, unitId, name) {
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let constructMajorType = unitProject.constructMajorType;
        for (let item of csxmTemplateArr) {
            if (item.specialtyCode === constructMajorType && item.description == name) {
                if (name == '土建修缮施工与生产同时进行增加费' && unitProject.deLibrary != '2023-FWXS-DEX-TJ') {
                    // 土建修缮施工与生产同时进行增加费，在土建修缮中会下挂到“土建修缮施工与生产同时进行增加费”的措施项下
                    // 土建修缮施工与生产同时进行增加费 的zjcsClassCode在配置中默认就是“22”
                    // 在其他专业中，会下挂到“施工与生产同时进行增加费”下， “施工与生产同时进行增加费”的zjcsClassCode为“11”
                    return '11';
                }
                return item.zjcsClassCode;
            }
        }
        return null;
    }

    async updateDePricingMethod(deRow, csxmDomain, value, pointLineId, constructId, singleId, unitId) {
        deRow.calculateBase = '';
        deRow.calculateBaseValue = 0;
        deRow.rate = '';
        deRow.baseDescription = '';
        if (deRow.children) {
            csxmDomain.removeRowRelatedDatas(deRow);
            deRow.displaySign = BranchProjectDisplayConstant.noSign;
        }
        deRow.pricingMethod = value;
        await csxmDomain.updateDe(deRow, true, true);
        if (value === 3) {
            let pointLine = {
                sequenceNbr: pointLineId,
                parentId: deRow.parentId,
                kind: '03'
            };
            let newLine = {
                kind: '02'
            };
            await this.save(constructId, singleId, unitId, pointLine, newLine);
        }
        await csxmDomain.notify(deRow, false);
        if (deRow.pricingMethod !== 1) {
            await this.service.gongLiaoJiProject.gljRcjCollectService.dropShareCost(deRow);
        } else {
            await this.service.gongLiaoJiProject.gljInitDeService.initDeFree(deRow);
        }
    }

    _getTypeByColumn(columnName) {
        if(columnName == 'RSum' || columnName == 'RDSum'){
            return '1';
        }
        if(columnName == 'JSum' || columnName == 'JDSum'){
            return '3';
        }
        if(columnName == 'CSum' || columnName == 'CDSum'){
            return '2';
        }
        if(columnName == 'SSum' || columnName == 'SDSum'){
            return '4';
        }
        if(columnName == 'ZSum' || columnName == 'ZDSum'){
            return '5';
        }
        return 0;
    }

    /**
     * 分页查找数据
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sequenceNbr
     * @param pageNum
     * @param pageSize
     * @returns {*}
     */
    async pageSearch(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag=false,screenCondition,colorList) {
        
        //let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;

        let memoryDatas = null;
        if(isAllFlag){
            memoryDatas = csxmDomain.getDeAllTreeDepth(constructId,unitId,null);
        }else{
            memoryDatas = csxmDomain.getDeTreeDepth(constructId,unitId,null);
        }
        
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        // 初始化不可竞争措施信息
        
        let awfList = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getAWFSummary({constructId,singleId,unitId});
        if(ObjectUtils.isNotEmpty(awfList)){
            let baseDeAwfMap = new Map();
            let baseDeAwfRelations = await this.service.gongLiaoJiProject.gljBaseDeAwfRelationService.getByLibraryAll()
            for(let awfRelation of baseDeAwfRelations){
                baseDeAwfMap.set(awfRelation.qfCode,awfRelation);
            }
            //计算总额 
            let bukeTotalNumber = 0
            let virtualData = [];
            for(let awfee of awfList){
                let baseDeAwfRealtion = baseDeAwfMap.get(awfee.constructMajorTypeCode);
                let lineData = new StandardDeModel(constructId,unitId,Snowflake.nextId(), null,DeTypeConstants.DE_TYPE_DE);
                lineData.deName = ObjectUtils.isNotEmpty(baseDeAwfRealtion)?baseDeAwfRealtion.deName:'';
                lineData.deCode = ObjectUtils.isNotEmpty(baseDeAwfRealtion)?baseDeAwfRealtion.deCode:(awfee.dispNo+'');
                //lineData.pricingMethod = 1;
                //使用deName 生成md5
                lineData.sequenceNbr = MD5(lineData.deName+"-"+lineData.deCode).toString();
                lineData.quantityExpression="1";
                lineData.quantity=1;
                lineData.originalQuantity=1;
                lineData.awfType = 2
                lineData.isCostDe = CostDeMatchConstants.AWF_DE;
                lineData.displayType = DeTypeConstants.DE_TYPE_FEE_LABEL;
                //lineData.adjustmentCoefficient = 1;
                //lineData.calculateBase = awfee.calculateFormula;
                //lineData.calculateBaseValue = awfee.calculateMoney;
                //lineData.baseDescription = awfee.instructions;
                //lineData.rate =  awfee.rate
                lineData.totalNumber = lineData.baseJournalTotalNumber = lineData.price = lineData.baseJournalPrice =  awfee.price;
                lineData.costMajorName = awfee.constructMajorTypeName;
                lineData.costFileCode = awfee.constructMajorTypeCode;
                lineData.optionMenu = [];
                //都相等所以随意一个即可
                bukeTotalNumber += awfee.price;

                //增加数据中
                virtualData.push(lineData);
            }
            bukeTotalNumber = NumberUtil.numberScale(bukeTotalNumber,precision.EDIT.DE.totalNumber);            
            
            let bukejingzhengDe =  memoryDatas.find(item=>item.type === DeTypeConstants.DE_TYPE_DELIST && item.awfType&& item.awfType == 2);
            let baohanAwf = false;
            if(ObjectUtils.isNotEmpty(bukejingzhengDe)){ 
                //兼容么？
                if(ObjectUtils.isEmpty(bukejingzhengDe.deCode)){
                    bukejingzhengDe.deCode = "1";
                }
                baohanAwf = true;
                if(ObjectUtils.isNotEmpty(virtualData) && (bukejingzhengDe.displaySign == BranchProjectDisplayConstant.open
                    || bukejingzhengDe.displaySign == BranchProjectDisplayConstant.noSign)
                ){
                    bukejingzhengDe.displaySign = BranchProjectDisplayConstant.open
                    let curParentId = bukejingzhengDe.sequenceNbr;
                    virtualData.forEach(item=>{
                        item.parentId = curParentId;
                    })
                    memoryDatas = memoryDatas.concat(virtualData);
                }
                
                //重置安文费组件方式
                bukejingzhengDe.totalNumber = bukejingzhengDe.baseJournalTotalNumber = bukejingzhengDe.price = bukejingzhengDe.baseJournalPrice = bukeTotalNumber;
            }
            //不可竞争定额的总价
            let parentNode = memoryDatas.find(item=>item.type === DeTypeConstants.DE_TYPE_FB && item.awfType&& item.awfType == 2);
            if(ObjectUtils.isNotEmpty(parentNode)){
                //兼容旧的版本
                if(!baohanAwf && parentNode.displaySign == BranchProjectDisplayConstant.noSign){
                    parentNode.displaySign = BranchProjectDisplayConstant.open
                    //兼容
                    let defaultSecondDeChild = new StandardDeModel(constructId, unitId, Snowflake.nextId(), parentNode.sequenceNbr, DeTypeConstants.DE_TYPE_DELIST);
                    defaultSecondDeChild.deName = "安全生产、文明施工费";
                    defaultSecondDeChild.deCode = "1";
                    defaultSecondDeChild.displaySign = BranchProjectDisplayConstant.open;
                    defaultSecondDeChild.awfType = 2;
                    defaultSecondDeChild.pricingMethod = 2;
                    defaultSecondDeChild.totalNumber =  defaultSecondDeChild.baseJournalTotalNumber = bukeTotalNumber;
                    defaultSecondDeChild.price =  defaultSecondDeChild.baseJournalPrice = bukeTotalNumber;
                    await csxmDomain.createDeRow(defaultSecondDeChild);

                    let curParentId = defaultSecondDeChild.sequenceNbr;
                    virtualData.forEach(item=>{
                        item.parentId = curParentId;
                    })
                    let copydefaultChild = PropertyUtil.filterObjectProperties(defaultSecondDeChild,["children","parent"])
                    copydefaultChild.displaySign = BranchProjectDisplayConstant.open;
                    virtualData.push(copydefaultChild);
                    memoryDatas = memoryDatas.concat(virtualData);
                }
                parentNode.totalNumber =  parentNode.baseJournalTotalNumber = NumberUtil.numberScale(bukeTotalNumber,precision.EDIT.FBLINE.totalNumber);
            }            
            //重置根定额的总价
            let rootDe = memoryDatas.find(item=>item.type === DeTypeConstants.DE_TYPE_DEFAULT);
            rootDe.totalNumber = NumberUtil.numberScale(NumberUtil.add(rootDe.totalNumber,bukeTotalNumber),precision.EDIT.UNITLINE.totalNumber);
            rootDe.baseJournalTotalNumber = NumberUtil.numberScale(NumberUtil.add(rootDe.baseJournalTotalNumber,bukeTotalNumber),precision.EDIT.UNITLINE.totalNumber);
        }
        
        let beginIndex = pageSize * (pageNum - 1);
        let endIndex = pageSize * (pageNum - 1) + pageSize;

        let returnArray = memoryDatas.slice(beginIndex, endIndex);

        let pageData = {
        'data': returnArray,
        'total': memoryDatas.length,
        'pageNum': pageNum,
        'pageSize': pageSize
        };
        this.setReplaceStatus(pageData.data);
        
        //对数据进行颜色过滤
        if(ObjectUtils.isNotEmpty(colorList)){
            if(!colorList.includes(null)){
                this.getCsxmFilter(pageData,constructId, singleId, unitId,colorList);
            }

        }
        // let deLists = returnArray;
        // 转树，为了给措施项目的定额排序
        let arrayTree = xeUtils.toArrayTree(returnArray, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        let deLists = xeUtils.toTreeArray(arrayTree);

        let sets = new Set();
        //重置检查单位类型
        if(ObjectUtil.isNotEmpty(deLists)){
            for(let element of deLists) {
                if(element.type === DeTypeConstants.DE_TYPE_DEFAULT || element.type === DeTypeConstants.DE_TYPE_FB
                    || element.type === DeTypeConstants.DE_TYPE_ZFB || element.type === DeTypeConstants.DE_TYPE_DELIST){
                    sets.add(element.sequenceNbr);
                }
                if(element.unitChanged == CommonConstants.COMMON_YES){
                    continue;
                }
                //标题及措施项为空变更为项
                if (element.type === DeTypeConstants.DE_TYPE_FB 
                    || element.type === DeTypeConstants.DE_TYPE_ZFB
                    || element.type === DeTypeConstants.DE_TYPE_DELIST
                    || element.isCostDe === CostDeMatchConstants.ZJCS_DE
                    || element.isCostDe === CostDeMatchConstants.AWF_DE) {
                        if(element.unit !== DeTypeConstants.DE_UNIT_LABEL){
                            element.unit =  DeTypeConstants.DE_UNIT_LABEL;
                            if(element.awfType == 2){

                            }else{
                                csxmDomain.updateUnit(constructId, unitId, element.sequenceNbr,  DeTypeConstants.DE_UNIT_LABEL);
                            }
                        }
                        continue;
                }
                //主材及设备不关心
                if (element.type === DeTypeConstants.SUB_DE_TYPE_DE) {
                    continue;
                }
                // 普通定额没有‘%’或‘元’的项
                if (ObjectUtil.isNotEmpty(element.isCostDe) && element.isCostDe !== CostDeMatchConstants.NON_COST_DE) {
                    if( ObjectUtil.isNotEmpty(element.unitChanged) &&element.unitChanged ==CommonConstants.COMMON_YES){
                        continue;
                    }
                    if(element.unit == '元' || element.unit == '%'){
                        element.unit = '项';
                        csxmDomain.updateUnit(constructId, unitId, element.sequenceNbr, '项');
                    }
                }
            }
        }
        let index = 1;
        //重置展开与关闭
        if(ObjectUtil.isNotEmpty(deLists) && !isAllFlag){    
            let type04CloseList = [];
            deLists.forEach(element => {
                if(element.type !== DeTypeConstants.DE_TYPE_DEFAULT && element.type !== DeTypeConstants.DE_TYPE_FB
                    && element.type !== DeTypeConstants.DE_TYPE_ZFB && element.type !== DeTypeConstants.DE_TYPE_DELIST && sets.has(element.parentId)) {
                    element.dispNo = index;
                    index++;
                }
                //重置主材与设备消耗量及数量精度
                if(element.type === DeTypeConstants.SUB_DE_TYPE_DE){
                    // element.resQty = NumberUtil.numberFormat(element.resQty,precision.DETAIL.RCJ.resQty);
                    element.quantity = NumberUtil.numberFormat(element.quantity,precision.DETAIL.RCJ.totalNumber);
                }
                if (ObjectUtil.isEmpty(element.isShowAnnotations)) {
                    element.isShowAnnotations = false;
                }
    
                if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && (ObjectUtil.isEmpty(element.displaySign) || element.displaySign === BranchProjectDisplayConstant.noSign)) {
                    //如果定额无箭头，则展开
                    let filter = deLists.filter(o => o.parentId === element.sequenceNbr);
                    if (ObjectUtil.isNotEmpty(filter)) {
                        element.displaySign = BranchProjectDisplayConstant.open;
                    }
                }
    
                if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && element.displaySign === BranchProjectDisplayConstant.close) {
                    //如果定额收起，则不展示05类型
                    type04CloseList.push(element.deRowId);
                }
    
                //人材机定额，费用人材机单位显示元
                if(element.isFyrcj == 0){
                    element.unit = "元";
                }
            });
    
            if(ObjectUtil.isNotEmpty(type04CloseList)){
                deLists = deLists.filter(p=>ObjectUtil.isEmpty(p.parentId) || !type04CloseList.includes(p.parentId));
            }
        }
        if(ObjectUtil.isNotEmpty(deLists)) {
            // 人材机下拉修改类型
            for (let element of deLists) {
                if (ObjectUtils.isNotEmpty(element.deResourceKind)) {
                    this.service.gongLiaoJiProject.gljBaseRcjService.typeListByKind(element);
                }
            }
        }
        pageData.data = deLists;
        
        return {...pageData, data: pageData.data};
    }

    //查询全量数据  而非只查询 displaySign 状态为open的数据
    pageSearchForAll(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag,screenCondition) {
        let memoryDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        let pageData = this._baseBranchProjectOptionService.pageSearchForAllData(pageNum, pageSize, sequenceNbr, memoryDatas, this.disPlayType, isAllFlag,screenCondition);
        this.setReplaceStatus(pageData.data);
        return pageData;//this._pageDataAdaptor(pageData, memoryDatas);
    }
    /**
     * 颜色过滤
     */
    getCsxmFilter(result,constructId, singleId, unitWorkId,colorList){
        let data = result.data;
        //获取所有的数据颜色数据
        let allNodes = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item=>item.unitId===unitWorkId);
        let mea=[];
        mea = allNodes.filter(m=>colorList.includes(m.color));
        //如果包含none标识需要没有颜色的
        if(colorList.includes("none") ){
            mea.push(...allNodes.filter(m=>ObjectUtils.isEmpty(m.color)));
        }
        //获取每一个数据的父级数据
        let set =new Set();
        mea.forEach(item=>{
            getParnetNode(item,set);
            //如果是清单需要获取他的子集定额数据    如果是定额需要获取他的同级别其他数据
            if(item.kind==BranchProjectLevelConstant.qd){
                let filter = allNodes.filter(de=> de.parentId==item.sequenceNbr);
                filter.forEach(f=> set.add(f.sequenceNbr));
            }
            if(item.kind==BranchProjectLevelConstant.de){
                let filter = allNodes.filter(de=> de.parentId==item.parentId);
                filter.forEach(f=> set.add(f.sequenceNbr));
            }
        });

        //过滤分页查询数据
        let filter1 = data.filter(d=> set.has(d.sequenceNbr));
        result.data=filter1;

        function getParnetNode(node,set) {
            set.add(node.sequenceNbr);
            if(ObjectUtils.isEmpty(node.parentId)){
                return;
            }
            let filter = allNodes.find(n=>n.sequenceNbr==node.parentId);
            getParnetNode(filter,set);
        }

    }


    /**
     * 设置措施项目总数据是否可以替换
     */
    setReplaceStatus(measureProjectTables){
        let measure = measureProjectTables.filter(k => !ObjectUtils.isEmpty(k.constructionMeasureType) && k.constructionMeasureType === ConstructionMeasureTypeConstant.DJCS);
        for (const item of measure) {
            let { datas } = this.service.baseBranchProjectOptionService._getInfluence(measureProjectTables, item);
            if (!ObjectUtils.isEmpty(datas)) {
               for(const mp of datas){
                   if(mp.kind==BranchProjectLevelConstant.qd || mp.kind==BranchProjectLevelConstant.de){
                         //费用定额不可以替换
                        if(ObjectUtils.isEmpty(mp.isCostDe) || mp.isCostDe==0 ||  mp.isCostDe==4){
                             mp.replaceFlag=true;
                        }
                   }
               }
            }
        }
    }

     /**
     * 展开
     */
     open(constructId, singleId, unitId, pointLine) {
        let de = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(pointLine.sequenceNbr);
        de.displaySign = BranchProjectDisplayConstant.open;
        return true;
    }

    /**
     * 折叠
     */
    close(constructId, singleId, unitId, pointLine) {
        let de = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(pointLine.sequenceNbr);
        de.displaySign = BranchProjectDisplayConstant.close;
        ProjectDomain.getDomain(constructId).csxmDomain.updateDe(de);
        return true;
    }

    /**
     * 批量删
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param sequenceNbrs
     */
    async batchDelete(constructId, singleId, unitWorkId, sequenceNbrs) {
        let self = this;
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        let lines = allData.filter((item) => _.includes(sequenceNbrs, item.sequenceNbr));
        let removeStrategy = new RemoveStrategy({constructId, singleId, unitId: unitWorkId, pageType: "csxm"})
        let pset = new Set();
        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            if (allData.hasNodeById(line.sequenceNbr)) {
                let parent = await removeStrategy.execute({
                    pointLine: line,
                    isBlock: line.kind != BranchProjectLevelConstant.de
                });
                pset.add(parent.sequenceNbr);
                //将定额的子定额数据进行批量删除
                if (line.kind == BranchProjectLevelConstant.de) {
                    let measureProjectTables = allData.filter(de=>de.parentDeId==line.sequenceNbr);
                    if(ObjectUtils.isNotEmpty(measureProjectTables)){
                        let sequenceNbrs = measureProjectTables.map(de=>de.sequenceNbr);
                        await this.batchDelete(constructId, singleId, unitWorkId,sequenceNbrs);
                    }
                }
            }
        }
        let arr = [...pset];
        let calculationTool = new CalculationTool({constructId, singleId, unitId: unitWorkId, allData: allData});
        arr.forEach(id => {
            if (allData.hasNodeById(id)) {
                calculationTool.calculationChian({sequenceNbr: id})
            }
        })

        return true;
    }

    deepFindMeasureProject(mapMeasureProjectTables, measureProject) {
        let item = mapMeasureProjectTables[measureProject.parentId];
        if (_.isEmpty(item)) {
            return true;
        }
        if (item.kind == BranchProjectLevelConstant.fb && item.constructionMeasureType == 2) {
            return false;
        } else {
            return this.deepFindMeasureProject(mapMeasureProjectTables, item);
        }
    }


    /**
     * 复制
     * @param sequenceNbrs
     */
    copyLine(sequenceNbrs) {
        let constructId = ParamUtils.getPatram("commonParam").constructId;
        let singleId = ParamUtils.getPatram("commonParam").singleId;
        let unitId = ParamUtils.getPatram("commonParam").unitId;
        let { measureProjectTables } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let filter = (list) => {
            return _.filter(list, (item) => _.includes(sequenceNbrs, item.sequenceNbr));
        }

        let mapMeasureProjectTables = _.mergeWith({}, ..._.map(measureProjectTables, (item) => {
            return { [item.sequenceNbr]: item };
        }), (objValue, srcValue) => {
            return srcValue;
        })
        let copeMeasureProjectTables = filter(measureProjectTables);
        for (let i = 0; i < copeMeasureProjectTables.length; i++) {
            if (!this.deepFindMeasureProject(mapMeasureProjectTables, copeMeasureProjectTables[i])) {
                return false;
            }
        }
        this._baseBranchProjectOptionService.copyLine(sequenceNbrs, "csxm");
        return true;
    }

    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine 粘贴的当前行
     * @returns {Promise<boolean>}
     */
    async pasteLine(constructId, singleId, unitId, pointLine) {
        let copeUnitProject = PricingFileFindUtils.getProjectBuffer();
        return await this.addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine);
    }

    _getMaxQdCodeNum(baseCode, constructId, singleId, unitId) {
        //这里的 itemBillProjects 和measureProjectTables都是树形结构
        let {
            itemBillProjects,
            measureProjectTables
        } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let codeList = [];
        let matchListA = itemBillProjects.getAllNodes().filter(item => item.bdCode && item.bdCode.startsWith(baseCode));
        let max = null;
        //条件获取到相对应的清单集合
        //根据清单编码排序
        if (matchListA && matchListA.length > 0) {
            max = _.maxBy(matchListA, function (o) {
                return o.bdCode;
            }).bdCode;
        }
        let matchListB = measureProjectTables.getAllNodes().filter(item => item.fxCode && item.fxCode.startsWith(baseCode));
        if (matchListB && matchListB.length > 0) {
            let b = _.maxBy(matchListB, function (o) {
                return o.fxCode;
            }).fxCode;
            if (max && max < b) max = b;
            if (!max) max = b;
        }
        if (!max) return 0;
        return max.length > 9 ? Number.parseInt(max.substring(9)) : 0;
    }
    /**
     * 抽取公共方法（粘贴、复用组价公用）
     * @param copeUnitProject
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @returns {Promise<boolean>}
     */
    async addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine, resultMap = {}) {
        if (_.isEmpty(copeUnitProject)) return false;
        let {
            measureProjectTables,
            feeFiles,
            listFeatureList,
            constructProjectRcjs,
            rcjDetailList,
            rcjRules
        } = copeUnitProject;

        if (_.isEmpty(measureProjectTables)) return false;
        //取费文件
        let {
            feeFiles: oldFeefles,
            listFeatureList: listFeatureListold
        } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let diffFeeFiles = [];
        _.forEach(feeFiles, (item) => {
            let isOk = false;
            for (let i = 0; i < oldFeefles.length; i++) {
                let oItem = oldFeefles[i];
                if (item.feeFileCode == oItem.feeFileCode) {
                    isOk = true;
                }
            }
            if (!isOk) {
                diffFeeFiles.push({...item});
            }
        });
        if (!_.isEmpty(diffFeeFiles)) {
            PricingFileFindUtils.getUnit(constructId, singleId, unitId).feeFiles = oldFeefles.concat(diffFeeFiles);
        }
        //分组
        let groupMeasureProjectTables = _.groupBy(measureProjectTables, (item) => item.kind);
        //清单列表
        let qdlist = groupMeasureProjectTables[BranchProjectLevelConstant.qd];
        //定额列表
        let delist = groupMeasureProjectTables[BranchProjectLevelConstant.de];
        let qdKeyMap = new Map();
        if (!_.isEmpty(qdlist)) {
            //for(let i = qdlist.length - 1; i >= 0; i--){
            for (const item of qdlist) {
                //let item = qdlist[i];
                let fxCode = item.fxCode;
                if (item.fxCode.length > 9) {
                    let baseCode = item.fxCode.substring(0, 9);
                    let maxCodeNum = this._getMaxQdCodeNum(item.fxCode.substring(0, 9), constructId, singleId, unitId);
                    let newCode = maxCodeNum + 1;
                    fxCode = baseCode + _.padStart(newCode, 3, '0');
                }
                let newLine = {...item, sequenceNbr: "", fxCode, bdCode: fxCode};
                let key = item.sequenceNbr + "";
                newLine.sequenceNbr = "";
                //添加清单
                let {data} =await this.save(constructId, singleId, unitId, pointLine, newLine);
                qdKeyMap.set(key, data);
                try {
                    resultMap[data.sequenceNbr] = data;
                } catch (e) {
                }
                //更新清单特征集合
                let itemRcjDetailList = _.filter(listFeatureList, (i) => i.qdId == key);
                for (let i = 0; i < itemRcjDetailList.length; i++) {
                    itemRcjDetailList[i].qdId = data.sequenceNbr;
                }
                if (_.isEmpty(listFeatureListold)) {
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).listFeatureList = itemRcjDetailList;
                } else {
                    PricingFileFindUtils.getUnit(constructId, singleId, unitId).listFeatureList = listFeatureListold.concat(itemRcjDetailList);
                }

            }
        }
        if (!_.isEmpty(delist)) {
            //拿到人材机明细
            let before = null;//处理顺序
            let beforeItem = null;
            let beforeQdItem = null;
            let fromFeeFileMap = UPCContext.getfeeFilebyPath(constructId + "," + copeUnitProject.sequenceNbr);

            let fromIncrementTemplateListMap = UPCContext.getTemplateListbyPath(constructId, copeUnitProject.sequenceNbr);
            //单价构成下拉列表
            let tagterFeeFileMap = UPCContext.getfeeFilebyPath(constructId + "," + unitId);
            //单价构成模板
            let tagterIncrementTemplateListMap = UPCContext.getTemplateListbyPath(constructId, unitId);
            //拿到人材机明细
            for (let i = 0; i < delist.length; i++) {
                let item = delist[i];
                let newLine = { ...item, sequenceNbr: '', isAutoCost: false };
                let key = item.sequenceNbr + "";
                let qdItem = qdKeyMap.get(item.parentId);
                //如果清单不存在
                let res = {};
                //清单不存在的情况下 当前行就是传进来的行
                if (_.isEmpty(qdItem)) {
                    res =await this.save(constructId, singleId, unitId, pointLine, newLine);

                } else {
                    //清单存在 则插入的当前行是 清单行
                    res =await this.save(constructId, singleId, unitId, qdItem, newLine);
                }
                try {
                    resultMap[res.data.sequenceNbr] = res.data;
                } catch (e) {
                }
                if (!res.data.bdCode) {
                    continue;
                }
                //处理单价构成模板
                if (res.data.qfCode && res.data.qfCode.includes("_")) {
                    let t = _.cloneDeep(fromFeeFileMap.get(res.data.qfCode));
                    //如果目标单位里不存在 则添加
                    if (!tagterFeeFileMap.has(res.data.qfCode)) {
                        UPCContext.qfCodeMap.set(constructId + "," + unitId + res.data.qfCode, res.data.costFileCode);
                        tagterFeeFileMap.set(res.data.qfCode, t);
                        let t1 = _.cloneDeep(fromIncrementTemplateListMap.get(res.data.qfCode));
                        tagterIncrementTemplateListMap.set(res.data.qfCode, t1);
                    }
                }
                //处理人材机
                if (!_.isEmpty(constructProjectRcjs)) {
                    //获取人材机
                    let itemConstructProjectRcjs = _.filter(constructProjectRcjs, (i) => i.deId == key) || [];
                    //获取人材机明细
                    let itemRcjDetailList = _.filter(rcjDetailList, (i) => i.deId == key) || [];
                    let result = await this.service.rcjProcess.pasteRcj(_.cloneDeep(itemConstructProjectRcjs), _.cloneDeep(itemRcjDetailList), constructId, singleId, unitId, res.data.sequenceNbr);
                    let map = new Map();
                    if (ObjectUtils.isNotEmpty(result)) {
                        map = result.map;
                    }
                    //如果复制的存在标准换算信息
                    if (!_.isEmpty(rcjRules)) {
                        let copeRcjRules = {};
                        _.forEach(itemConstructProjectRcjs, (item) => {
                            let copeRules = rcjRules[item.sequenceNbr];
                            if (!_.isEmpty(copeRules)) {
                                copeRcjRules[map.get(item.sequenceNbr).sequenceNbr] = _.cloneDeep(copeRules);
                            }
                        })
                        let rcnewjRules = PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules;
                        if (_.isEmpty(rcnewjRules)) {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules = copeRcjRules;
                        } else {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).rcjRules = {...rcnewjRules, ...copeRcjRules};
                        }
                    }
                }
                //处理换算记录
                if (!_.isEmpty(copeUnitProject.conversionInfoList)) {
                    //换算信息数据
                    let itemConversionInfoList = _.filter(copeUnitProject.conversionInfoList, (i) => i.deId == key);
                    for (let i = 0; i < itemConversionInfoList.length; i++) {
                        itemConversionInfoList[i].deId = res.data.sequenceNbr;
                    }
                    let conversionInfoList = PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList;
                    if (_.isEmpty(conversionInfoList)) {
                        PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList = itemConversionInfoList;
                    } else {
                        PricingFileFindUtils.getUnit(constructId, singleId, unitId).conversionInfoList = conversionInfoList.concat(itemConversionInfoList);
                    }
                }

                if (!_.isEmpty(copeUnitProject.defaultConcersions)) {
                    if (copeUnitProject.defaultConcersions[key]) {
                        let item = copeUnitProject.defaultConcersions[key];
                        let defaultConcersions = PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions;
                        if (_.isEmpty(defaultConcersions)) {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions = {[res.data.sequenceNbr]: item};
                        } else {
                            PricingFileFindUtils.getUnit(constructId, singleId, unitId).defaultConcersions[res.data.sequenceNbr] = item;
                        }
                    }
                }
            }
        }
        //处理复制粘贴后的 QDL关联
        let newitemBillProjects = PricingFileFindUtils.getUnit(constructId, singleId, unitId).measureProjectTables;
        let alldata = newitemBillProjects.getAllNodes()
        if (!_.isEmpty(newitemBillProjects)) {
            //处理清单 QDL
            for (let i = 0; i < alldata.length; i++) {
                let item = alldata[i];
                if (item.kind === BranchProjectLevelConstant.qd) {
                    await this._baseBranchProjectOptionService.updateQdToDeQDL(constructId, singleId, unitId, newitemBillProjects, item.sequenceNbr);
                    this.service.unitPriceService.caculateQDUnitPrice(constructId, singleId, unitId, item.sequenceNbr,
                        true, newitemBillProjects);
                }
            }
        }

        await this.service.autoCostMathService.autoCostMath({
            constructId,
            singleId,
            unitId
        });
        return true;
    }
   async removeLine(constructId, singleId, unitWorkId, pointLine, isBlock){
       ProjectDomain.getDomain(constructId).csxmDomain.removeDeRow(pointLine.sequenceNbr,true)
       return true;
   }

    clearQdData(pointLineDe,constructId, singleId, unitWorkId){
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        //获取定额对应清单
        let pointLine = allData.getNodeById(pointLineDe.parentId);
        if(ObjectUtils.isEmpty(pointLine)){
            return pointLine;
        }
        if(pointLine.tempDeleteFlag){
            let children = pointLine.children;
            if(children.length==1){
                return pointLine;
            }
        }
        return pointLineDe;

    }

    /**
     * 删除
     * @param constructId
     * @param singleId
     * @param unitWorkId
     * @param pointLine
     * @param isBlock
     * @returns {boolean}
     */
    async removeLineOld(constructId, singleId, unitWorkId, pointLine, isBlock) {
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
        let removedRes
        let args = {};
        args["constructId"] = constructId;
        args["singleId"] = singleId;
        args["unitId"] = unitWorkId;
        if (!isBlock) {

            removedRes =  await this._baseBranchProjectOptionService.removeLine(pointLine, allData, args);
            // 删除 措施项目处理单独行 后续逻辑
            this.itemBillProjectProcess.delLineRelevantData(pointLine, allData, constructId, singleId, unitWorkId);
            this._unitPriceServices.deleteFee(constructId, singleId, unitWorkId, pointLine.sequenceNbr);
        } else {
            removedRes = await this._baseBranchProjectOptionService.removeLineBlock(pointLine, allData, influenceLines => {
                // 删除 措施项目处理多行 后续逻辑
                this.itemBillProjectProcess.delBlockRelevantData(pointLine, allData, constructId, singleId, unitWorkId);
                for (let i = 0; i < influenceLines.length; ++i) {
                    this._unitPriceServices.deleteFee(constructId, singleId, unitWorkId, influenceLines[i].sequenceNbr);
                }
            }, args);
        }
        if (pointLine.kind === BranchProjectLevelConstant.qd || pointLine.kind === BranchProjectLevelConstant.de) {
            let parentLine = this._findLine(allData, pointLine.parentId);
            if (parentLine) {
                if (parentLine.kind === BranchProjectLevelConstant.qd) {
                    this._unitPriceServices.caculateQDUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, allData);
                } else {
                    this._unitPriceServices.caculateFBUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, allData);
                }
            }
        }
        this._reSortBlockDisplayNum(removedRes, 0, true);
        PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).measureProjectTables = removedRes;

        // 单价构成
        if (pointLine.kind === BranchProjectLevelConstant.qd || pointLine.kind === BranchProjectLevelConstant.de) {
            let parentLine = this._findLine(allData, pointLine.parentId);
            if (parentLine) {
                if (parentLine.kind === BranchProjectLevelConstant.qd) {
                    this._unitPriceServices.caculateQDUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, removedRes);
                } else {
                    this._unitPriceServices.caculateFBUnitPrice(constructId, singleId, unitWorkId, parentLine.sequenceNbr, true, removedRes);
                }
            }
        }
        return true;
    }

    updateQdFeature(constructId, singleId, unitId, pointLine, updateStr) {
        pointLine = this._findLine(PricingFileFindUtils.getCSXM(constructId, singleId, unitId), pointLine.sequenceNbr);
        this.service.baseBranchProjectOptionService.updateQdFeature(constructId, singleId, unitId, pointLine, updateStr);
    }

    async selectDe(constructId, unitId, deStandardId, deRowId, deRow){
        
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        let deRowModel = null;
        if(ObjectUtil.isNotEmpty(deRowId) && (deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)){
            
            let deRowCode = deRow.deCode;
            let baseRCJDetail = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:deRow.standardId});

            if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE && ObjectUtil.isNotEmpty(baseRCJDetail)){
                deRowModel = await csxmDomain.appendDeResource(constructId, unitId, deRow.standardId, deRowId);
            }else{
                let userDeBases = csxmDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
                if(ObjectUtil.isNotEmpty(userDeBases)) {

                    let resultUs = userDeBases.filter(item => DeTypeCheckUtil._fixCode(item.materialCode) === DeTypeCheckUtil._fixCode(deRowCode) && item.unitId === unitId);
                    if (ObjectUtil.isNotEmpty(resultUs)) {
                        if(resultUs.length > 1){
                        resultUs.sort((a,b)=>b.updateDate-a.updateDate);
                        }
                        let result = resultUs[0];
                        let newResult = ConvertUtil.deepCopy(result);
                        newResult.unitId=unitId;
                        newResult.deId=deRowId;
                        newResult.deRowId=deRowId;
                        newResult.sequenceNbr=null;
                        newResult.parentId = null;
                        deRowModel = await csxmDomain.appendUserResource(constructId, unitId, deRowId, newResult, false);
                    }
                }
                if(ObjectUtil.isEmpty(deRowModel)){
                    //所有定额的定额
                    let de = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item=>item.sequenceNbr === deStandardId&&item.unitId === unitId);
                    if(ObjectUtil.isNotEmpty(de)){
                        deRowModel = await csxmDomain.appendBaseDeByLocal(constructId, unitId, de, deRowId,false);
                    }
                }
            }

            if(deRowCode.indexOf('#') > -1){
                let unitAllMemory = await this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId, unitId);
                
                if(ObjectUtil.isNotEmpty(unitAllMemory)){
                let querySequnceNbr = deRow.sequenceNbr;
                let resultCache = null;
                if(querySequnceNbr.indexOf('__')>-1){
                    let querySequnceNbrs = querySequnceNbr.split('__');
                    let resultCaches = unitAllMemory.filter(item=>item.sequenceNbr === querySequnceNbrs[0]);
                    resultCache = resultCaches[querySequnceNbrs[1]];
                }else{
                    resultCache = unitAllMemory.find(item=>item.materialCode === deRowCode);
                }
                let rcjDeKey = WildcardMap.generateKey(unitId, deRowModel.sequenceNbr) + WildcardMap.WILDCARD;
                let rcjs =  csxmDomain.ctx.resourceMap.getValues(rcjDeKey);
                let deRcj = rcjs.find(item=>item.isDeResource === CommonConstants.COMMON_YES);
                PropertyUtil.copyProperties(resultCache,deRcj,["sequenceNbr","deRowId","deId","parentId","resQty","originalQty"]);
                deRcj.isDeResource = CommonConstants.COMMON_YES;
                deRowModel.deCode = deRcj.materialCode;
                deRowModel.deName = deRcj.materialName;
                deRowModel.unit = deRcj.unit;
                deRowModel.deResourceKind = deRcj.kind;
                deRowModel.specification = deRcj.specification;
                deRowModel.ifLockStandardPrice = deRcj.ifLockStandardPrice;
                deRowModel.markSum = deRcj.markSum;
                }
            } 
            
        }else{
            //替换查询原来定额的章节
            let de = csxmDomain.getDe(item=>item.sequenceNbr === deStandardId);
            //尝试从用户定额缓存中获取
            if(ObjectUtil.isEmpty(de)){
                let userDes = csxmDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
                if(ObjectUtil.isNotEmpty(userDes)){
                let result = userDes.find(item=>item.sequenceNbr === deStandardId && item.unitId === unitId);
                if(ObjectUtil.isNotEmpty(result)){
                    deRowModel = await csxmDomain.appendUserDe(constructId, unitId, deRowId, result,false);
                }
                }
            }
            if(ObjectUtil.isEmpty(deRowModel)){
                //尝试从其他地方查询定额
                if(ObjectUtil.isEmpty(de))
                {
                    de = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item=>item.sequenceNbr === deStandardId);
                    if(ObjectUtil.isNotEmpty(de) && ObjectUtil.isEmpty(de.measureType)){
                        let cslbItems = await this.service.gongLiaoJiProject.gljBaseCslbService.getByCode(de.cslbCode);
                        if(cslbItems){
                            de.measureType = cslbItems[0].cslbName;
                        }
                    }
                }
                if(ObjectUtil.isEmpty(de))
                {
                    deRowModel = await csxmDomain.appendBaseDe(constructId, unitId, deStandardId, deRowId,true);
                }else{
                    deRowModel = await csxmDomain.appendBaseDeByLocal(constructId, unitId, de, deRowId,false);
                }
            }
        }
        let originalAdjust = 1;
        let csxmDe = csxmDomain.getDeById(deStandardId);
        if(ObjectUtil.isNotEmpty(csxmDe) ){
            let parent = csxmDomain.getDeById(csxmDe.parentId);
            originalAdjust = parent.adjustmentCoefficient;
        }
        //消耗量处理
        await this._updateRcjResQty(deRowModel,csxmDomain,originalAdjust,this.service);
        await csxmDomain.extendQuantity(constructId, unitId, deRowModel.sequenceNbr);
        return deRowModel;
    }

    async replaceFromIndexPage(constructId, singleId, unitId, selectId, replaceId, rcjFlag, conversionCoefficient, kind, unit, libraryCode) {
        let isDe = rcjFlag === 1?false:true;
        let deRowId = replaceId;
        let deStandardId = selectId;
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        //

        let deRow = csxmDomain.getDeById(deRowId);
        let originalQuantity = deRow.originalQuantity;
        if(isDe)
        {
            deRow = await csxmDomain.appendBaseDe(constructId, unitId, deStandardId, deRowId,true);
        }
        else
        {
            let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: deStandardId});
            if (ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
                return ResponseData.fail("错误，存在循环引用！");
            }
            deRow = await csxmDomain.appendDeResource(constructId, unitId, deStandardId, deRowId);
        }
        //处理人材机消耗量
        await this._updateRcjResQty(deRow,csxmDomain,1,this.service);

        //保持工程量不变
        deRow.originalQuantity = originalQuantity;
        await csxmDomain.extendQuantity(constructId, unitId, deRow.sequenceNbr);
        return  CsxmDomain.filter4DeTree(deRow);
    }
    async _updateRcjResQty(deRowReturn,csxmDomain,orignalAdjustment,service, adjustmentCoefficient){
        if(ObjectUtil.isNotEmpty(deRowReturn) && [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE, DeTypeConstants.DE_TYPE_RESOURCE, DeTypeConstants.DE_TYPE_DELIST, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_RESOURCE].includes(deRowReturn.type)){
            let csxmDe = csxmDomain.getDeById(deRowReturn.parentId)
            if(ObjectUtil.isNotEmpty(csxmDe)){
                let resultList = [];
                let rcjs = csxmDomain.ctx.resourceMap.findByValueProperty('deRowId', deRowReturn.sequenceNbr);
                resultList = rcjs.map(mItem=>mItem.value).filter(ritem => ObjectUtil.isEmpty(ritem.isDeResource)|| ritem.isDeResource=== CommonConstants.COMMON_NO);
                
                //先更新定额，然后更新人材机消耗量为
                if(ObjectUtils.isNotEmpty(resultList)){
                    for(let rcjDetail of resultList){
                        if(rcjDetail.isNumLock){
                            continue;
                        }
                        let updateResQty = NumberUtil.numberScale(NumberUtil.multiply(csxmDe.adjustmentCoefficient, rcjDetail.resQty/orignalAdjustment),5);
                        rcjDetail.adjustmentCoefficient = adjustmentCoefficient;//记录最新的调整系数
                        await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId:deRowReturn.constructId, singleId:null, unitId:deRowReturn.unitId
                            , deId:rcjDetail.deRowId
                            , rcjDetailId:rcjDetail.sequenceNbr
                            , constructRcj:{
                                resQty : updateResQty
                            },
                            adjustmentCoefficient
                        });
                    }
                }
            }
        }
    }
    
    /**
     * 从清单定额索引中点替换-完整版，包含插入数据后触发的事件
     * @param args 详见stepItemCostController.replaceFromIndexPage的args
     */
    async replaceFromIndexPageFullEdition(args) {
        let {
            constructId,
            singleId,
            unitWorkId,
            unitId,
            selectId,
            replaceId,
            type,
            conversionCoefficient,
            kind,
            unit,
            libraryCode
        } = args;
        if (!unitWorkId) {
            unitWorkId = unitId;
        }
        let res = await this.replaceFromIndexPage(constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit, libraryCode);
        await this.service.management.sycnTrigger("unitDeChange");
        return res;
    }

    async updateDeByCode(constructId, singleId, unitId, pointLine, type, code){
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain;
        let deRowReturn = await csxmDomain.getDesAndAppendDe(constructId, unitId, code, pointLine.sequenceNbr);

        //处理人材机消耗量
        await this._updateRcjResQty(deRowReturn,csxmDomain,1,this.service)

        return deRowReturn;
    }


    async fillDataFromIndexPage(constructId, singleId, unitId, pointLine, createType, indexId, unit, rcjFlag, bzhs, type, isInvokeCountCostCodePrice, libraryCode) {
        let isDe = rcjFlag === 1?false:true;
        let deRow = pointLine;
        let csxmDomain = ProjectDomain.getDomain(deRow.constructId).csxmDomain;
        let deRowReturn;
        let prevDeRow;
        let index;
        if(deRow.type == DeTypeConstants.SUB_DE_TYPE_DE){
            prevDeRow = csxmDomain.getDeById(deRow.deRowId);
            if([DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_USER_DE].includes(prevDeRow.type)){
                let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: indexId});
                if (ObjectUtil.isNotEmpty(baseRCJ) && ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
                    return ResponseData.fail("错误，存在循环引用！");
                }
                await this.service.gongLiaoJiProject.gljRcjService.addRcjData(deRow.deRowId, baseRCJ, deRow.constructId, null, deRow.unitId,deRow.deRowId,null, {});
            }
            return CsxmDomain.filter4DeTree(prevDeRow);
        }
        if(ObjectUtils.isNotEmpty(deRow.sequenceNbr))
        {
            prevDeRow = csxmDomain.getDeById(deRow.sequenceNbr);
            if(prevDeRow.type === DeTypeConstants.DE_TYPE_DELIST && deRow.parentId === deRow.sequenceNbr){
                index = null;
            }else{
                index = prevDeRow.index + 1;
            }
        }
        if(isDe){
            let baseDeId = indexId;
            if(deRow.parentId === deRow.sequenceNbr){
                deRow.type = DeTypeConstants.DE_TYPE_DE;
            }
            let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
            await csxmDomain.createDeRow(deRowModel,index);
            deRowReturn = await csxmDomain.appendBaseDe(deRowModel.constructId,deRowModel.unitId,baseDeId,deRowModel.sequenceNbr,true);
            
        }else{
            let resourceId = indexId;            
        
            let baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId: resourceId});
            if (ObjectUtils.isNotEmpty(baseRCJ) && ZSFeeConstants.ZS_RCJ_LIST.includes(baseRCJ.materialCode)) {
                return ResponseData.fail("错误，存在循环引用！");
            }
            let deRowType = deRow.type;
            if(DeTypeConstants.DE_TYPE_DELIST === deRowType){
                deRowType = DeTypeConstants.DE_TYPE_DE;
            }
            let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRowType);
            await csxmDomain.createDeRow(deRowModel,index);
            deRowReturn = await csxmDomain.appendDeResource(deRowModel.constructId,deRowModel.unitId,resourceId,deRowModel.sequenceNbr);
        }
        //处理人材机消耗量
        await this._updateRcjResQty(deRowReturn,csxmDomain,1,this.service, true);
        return  CsxmDomain.filter4DeTree(deRowReturn);
    }

    /**
     * 从清单定额索引中点击插入-完整版，包含插入数据后触发的事件
     * @param args 详见stepItemCostController.fillFromIndexPage的args
     */
    async fillDataFromIndexPageFullEdition(args) {
        let { constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag, libraryCode } = args;
        let res = await this.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag, null, null, null, libraryCode);
        await this.service.management.sycnTrigger("unitDeChange");
        return res;
    }

    /**
     * 对数据重新排序号
     * @param res
     * @param beginListItemNum
     * @private
     */
    _reSortBlockDisplayNum(res, beginIndex, rebuildAll) {
        let lastListItemNum = 0;
        let lastQuotaLastNum = 0;
        let loopBegin = beginIndex;
        if (beginIndex !== 0) {
            for (; loopBegin > 0; --loopBegin) {
                if (res[loopBegin].kind === StepItemCostLevelConstant.bt) {
                    break;
                }
            }
        }
        if (!res[++loopBegin]) return;

        for (let index = loopBegin; index < res.length; ++index) {
            let lineData = res[index];
            if (lineData.kind === StepItemCostLevelConstant.bt) {
                if (!rebuildAll) {
                    break
                }
                lastListItemNum = 0;
                lastQuotaLastNum = 0;
            }
            if (lineData.kind === StepItemCostLevelConstant.qd) {
                lastListItemNum += 1;
                lineData.dispNo = lastListItemNum + "";
                lastQuotaLastNum = 0
                continue;
            }
            if (lineData.kind === StepItemCostLevelConstant.de) {
                lastQuotaLastNum += 1;
                lineData.dispNo = lastListItemNum + "." + lastQuotaLastNum;
            }
        }
    }

    _dealEmpData(memoryDatas, pointLine, newLine) {
        return memoryDatas;
    }

    getInsertPointLineByCurrentName(name) {
        let constructId = ParamUtils.getPatram("commonParam").constructId;
        let singleId = ParamUtils.getPatram("commonParam").singleId;
        let unitId = ParamUtils.getPatram("commonParam").unitId;
        let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

        let index = {
            "安全生产、文明施工费": 0,
            "其他总价措施项目": 1,
            "单价措施项目": 2,
        }
        let findLineNameIndex = index[name] - 1;
        if (findLineNameIndex < 0) {
            return allData[0];
        }

        let qds = allData.filter(f => f.kind === "01");
        return qds.filter(f => f.name === this.DEFAULT_MEASURE_TYPES[index[name] - 1])[0];
    }

    _findLine(allData, id) {
        if (!allData) {
            return
        }
        return  allData.getNodeById(id);
        /*for (let i = 0; i < allData.length; ++i) {
            if (allData[i].sequenceNbr === id) {
                return allData[i];
            }
        }*/
    }

    async getMeasureTemplates(args) {
        let { constructId,singleId, unitId } = args;
        let quota_standard = PricingFileFindUtils.getConstructDeStandard(constructId);


        let result = this.app.betterSqlite3DataSource
          .prepare('SELECT template_name FROM base_specialty_measures where quota_standard = ? group by template_name')
          .all(quota_standard);
        return result;
    }

    async getBaseListByTemplate(args)
    {
        let {constructId,singleId, unitId, templateName } = args;
        let measureBaseMap = new Map();
        measureBaseMap.set(ConstructionMeasureTypeConstant.AWF,await this._getMeasuresBaseList(constructId,singleId, unitId, templateName,ConstructionMeasureTypeConstant.AWF));
        measureBaseMap.set(ConstructionMeasureTypeConstant.ZJCS,await this._getMeasuresBaseList(constructId,singleId, unitId, templateName,ConstructionMeasureTypeConstant.ZJCS));
        return measureBaseMap;

    }
    async _getMeasuresBaseList(constructId,singleId, unitId, templateName,type)
    {
        let measureList = await this.app.appDataSource.getRepository(BaseSpecialtyMeasures).find({
            where: {
                quotaStandard: PricingFileFindUtils.is22UnitById(constructId,singleId, unitId) ? ConstantUtil.DE_STANDARD_22 : ConstantUtil.DE_STANDARD_12,
                templateName: templateName,
                measuresType: type,
            }
        });
        let bdCodes = measureList.map(obj => obj.bdCode);
        let sqlres = await this.app.appDataSource.getRepository(BaseList).find({ where: { bdCodeLevel04: In(bdCodes) } });
        //排序
        sqlres.sort((a, b)=> bdCodes.indexOf(a.bdCodeLevel04) - bdCodes.indexOf(b.bdCodeLevel04));
        return sqlres;
    }
    /**
     * 打开系统级保存文件位置选择框
     */
    async openSaveDialog(defaultPath) {

    }

    async getTemplateBaseDir()
    {
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        let result = {content:userHistoryData.DEF_SAVE_PATH}
        if (!fs.existsSync(directoryPath)) {
            await fs.mkdir(directoryPath, { recursive: true });
        }
        return `${templateDir}\\template`;
    }

    /**
     * to-do 本方法来源自 : commonService.getSetStoragePath(constructName);
     *          在时间允许的情况下 需要重构
     * 获取默认存储路径
     */
    async getSetStoragePath(fileName) {

        let templateDir = this.getTemplateBaseDir();

        //读取数据

        if (!fs.existsSync(templateDir)) {
            await fs.mkdir(templateDir, { recursive: true });
        }
        //此处需要判断是否登录 正常登录  离线登录  未登录
        if(ObjectUtils.isEmpty(global.idInformation)){
            //未登录
            if (ObjectUtils.isEmpty(fileName)){
                return `${templateDir}`;
            }else {
                return `${templateDir}\\${fileName}.ysc`;
            }
        }else {
            //获取用户信息
            let sequenceNbr  = global.idInformation.sequenceNbr;
            let identity  = global.idInformation.identity;
            if (ObjectUtils.isEmpty(fileName)){
                return `${templateDir}\\${sequenceNbr}\\${identity}`;
            }else {
                return `${templateDir}\\${sequenceNbr}\\${identity}\\${fileName}.ysc`;
            }
        }


    }




    /**
     * 展开至那级集合查询
     * @param constructId
     * @param unitId
     */
    async openLevelCheckList(constructId, unitId) {
        let result = OpenLevelList;
        result.ALL.hasCheck = false;
        result.ONE.hasCheck = false;
        result.TWO.hasCheck = false;
        result.THREE.hasCheck = false;
        result.DE.hasCheck = false;
        result.ZHUCAI.hasCheck = false;

        let deList = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId);
        if (deList.length > 1) {
            result.ALL.hasCheck = true;
            result.ZHUCAI.hasCheck = true;
        }

        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let top = deList.find(o => o.sequenceNbr === unit.defaultCsxmId);
        let ONEList = deList.filter(o => o.parentId === top.sequenceNbr && (o.type === DeTypeConstants.DE_TYPE_FB || o.type === DeTypeConstants.DE_TYPE_ZFB));
        if (ObjectUtil.isNotEmpty(ONEList)) {
            result.ONE.hasCheck = true;
            ONEList.forEach(item => {
                let TWOList = deList.filter(o => o.parentId === item.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_ZFB);
                if (ObjectUtil.isNotEmpty(TWOList)) {
                    result.TWO.hasCheck = true;
                    TWOList.forEach(item => {
                        let THREEList = deList.filter(o => o.parentId === item.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_DELIST);
                        if (ObjectUtil.isNotEmpty(THREEList)) {
                            result.THREE.hasCheck = true;
                        }
                    })
                }


                let THREEList = deList.filter(o => o.parentId === item.sequenceNbr && o.type === DeTypeConstants.DE_TYPE_DELIST);
                if(ObjectUtils.isNotEmpty(THREEList)){
                    result.THREE.hasCheck = true;
                }

            })
        }

        let DEList00 = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_DELIST);
        if (ObjectUtil.isNotEmpty(DEList00)) {
            result.THREE.hasCheck = true;
        }

        let DEList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_EMPTY || o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_USER_DE);
        if (ObjectUtil.isNotEmpty(DEList)) {
            for (const o of DEList) {
                let find = deList.find(p => p.sequenceNbr === o.parentId);
                if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DE) {
                    //只有最父级才展开
                    if (o.type === DeTypeConstants.DE_TYPE_EMPTY || o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_USER_DE) {
                        result.DE.hasCheck = true;
                        break;
                    } else if (o.type === DeTypeConstants.DE_TYPE_DE) {
                        result.DE.hasCheck = true;
                    }
                }
            }
        }
        let DEList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
        if (ObjectUtil.isNotEmpty(DEList1)) {
            for (const o of DEList1) {
                let find = deList.find(p => p.sequenceNbr === o.parentId);
                if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DE) {
                    result.DE.hasCheck = true;
                    break;
                }
            }
        }


        // let ZHUCAIList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_USER_DE);
        // if (ObjectUtil.isNotEmpty(ZHUCAIList)) {
        //     for (const o of ZHUCAIList) {
        //         let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, o.deRowId, o.type);
        //         if (ObjectUtil.isNotEmpty(rcjLists)) {
        //             let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
        //             if (ObjectUtil.isNotEmpty(filter)) {
        //                 result.ZHUCAI.hasCheck = true;
        //             }
        //         }
        //
        //     }
        // }
        //
        // let ZHUCAIList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
        // if (ObjectUtil.isNotEmpty(ZHUCAIList1)) {
        //     ZHUCAIList1.forEach(p => {
        //         let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(p.sequenceNbr);
        //         if (de.deResourceKind == 4 || de.deResourceKind == 5) {
        //             result.ZHUCAI.hasCheck = true;
        //         }
        //         result.DE.hasCheck = true;
        //     });
        // }

        return result;
    }


    /**
     * 展开至
     * @param constructId
     * @param unitId
     * @param type
     */
    async openLevel(constructId, unitId, type) {
        let ONEList = [];
        let TWOList = [];
        let THREEList = [];
        let DEList = [];

        let unit = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        let deList = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId);
        let top = deList.find(o => o.sequenceNbr === unit.defaultCsxmId);

        if (type == OpenLevelList.ALL.type) {
            //展开所有
            deList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.close) {
                    o.displaySign = BranchProjectDisplayConstant.open;
                    ProjectDomain.getDomain(constructId).csxmDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.ONE.type) {
            //展开至一级
            ONEList = await this.openLevelOne(constructId, top, deList);
            //二级的数据折叠
            ONEList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.open || o.displaySign === BranchProjectDisplayConstant.noSign) {
                    o.displaySign = BranchProjectDisplayConstant.close;
                    ProjectDomain.getDomain(constructId).csxmDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.TWO.type) {
            //展开至一级
            ONEList = await this.openLevelOne(constructId, top, deList);
            //展开至二级
            TWOList = await this.openLevelTwo(constructId, ONEList, deList);

            //三级的数据折叠
            TWOList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.open) {
                    o.displaySign = BranchProjectDisplayConstant.close;
                    ProjectDomain.getDomain(constructId).csxmDomain.updateDe(o);
                }
            })
        } else if (type == OpenLevelList.THREE.type) {

            let DEList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_DELIST);
            let list = [];
            for (let item of DEList) {
                let find = deList.find(p => p.sequenceNbr === item.parentId);
                if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DELIST) {
                    if (item.type === DeTypeConstants.DE_TYPE_DELIST) {
                        let openData = await this.openLineAndParent1(item, deList);
                        openData.forEach(p => {
                            list.push(p.sequenceNbr);
                        });
                    }
                }
            }

            for (let item of deList) {
                if (!list.includes(item.sequenceNbr)) {
                    let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(item.sequenceNbr);
                    if (de.displaySign === BranchProjectDisplayConstant.open) {
                        de.displaySign = BranchProjectDisplayConstant.close;
                    } else if(de.displaySign === BranchProjectDisplayConstant.noSign){
                        if(de.type === DeTypeConstants.DE_TYPE_DELIST && de.awfType&& de.awfType == 2){
                            de.displaySign = BranchProjectDisplayConstant.close;
                        }
                    }
                }
            }

            //安全生产、文明施工费是查询加入的，所以只能这里判断上一次是否展开
            // let bukejingzhengDe = deList.find(item => item.type === DeTypeConstants.DE_TYPE_FB && item.awfType && item.awfType == 2);
            // if (ObjectUtils.isNotEmpty(bukejingzhengDe) && ObjectUtils.isNotEmpty(bukejingzhengDe.displaySign) && bukejingzhengDe.displaySign === BranchProjectDisplayConstant.close) {
            //     let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(bukejingzhengDe.sequenceNbr);
            //     de.displaySign = BranchProjectDisplayConstant.open;
            // }
        } else if (type == OpenLevelList.DE.type) {
            let DEList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_EMPTY || o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_USER_DE);
            let list = [];
            for (let item of DEList) {
                let find = deList.find(p => p.sequenceNbr === item.parentId);
                if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DE) {
                    if (item.type === DeTypeConstants.DE_TYPE_EMPTY || item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_USER_DE) {
                        let openData = await this.openLineAndParent1(item, deList);
                        openData.forEach(p => {
                            list.push(p.sequenceNbr);
                        });
                    }

                    // else if (item.type === DeTypeConstants.DE_TYPE_DE) {
                    //     if (item.libraryCode !== AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE && item.libraryCode !== AnZhuangJiQqConstants.AZ_JIQU_NAME_CODE_JZ) {
                    //         let openData = await this.openLineAndParent1(item, deList);
                    //         openData.forEach(p => {
                    //             list.push(p.sequenceNbr);
                    //         });
                    //     } else {
                    //         if (ObjectUtils.isNotEmpty(item.classlevel02) && item.classlevel02.includes("补充定额")) {
                    //             let openData = await this.openLineAndParent1(item, deList);
                    //             openData.forEach(p => {
                    //                 list.push(p.sequenceNbr);
                    //             });
                    //         }
                    //     }
                    // }
                }
            }
            let DEList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE || o.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
            if (ObjectUtil.isNotEmpty(DEList1)) {
                for (const item1 of DEList1) {
                    let find = deList.find(p => p.sequenceNbr === item1.parentId);
                    if (ObjectUtil.isNotEmpty(find) && find.type !== DeTypeConstants.DE_TYPE_DE) {
                        let openData = await this.openLineAndParent1(item1, deList);
                        openData.forEach(p => {
                            list.push(p.sequenceNbr);
                        });
                    }
                }
            }


            let noFbList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_FB && !list.includes(o.sequenceNbr));
            if (ObjectUtil.isNotEmpty(noFbList)) {
                noFbList.forEach(t => {
                    let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(t.sequenceNbr);
                    if (de.displaySign === BranchProjectDisplayConstant.open) {
                        de.displaySign = BranchProjectDisplayConstant.close;
                    }
                });
            }

            for (let item of DEList) {
                if (!list.includes(item.sequenceNbr)) {
                    let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(item.sequenceNbr);
                    if (de.displaySign === BranchProjectDisplayConstant.open) {
                        de.displaySign = BranchProjectDisplayConstant.close;
                    }

                    let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, item.sequenceNbr, item.type);
                    if (ObjectUtil.isNotEmpty(rcjLists)) {
                        let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
                        if (ObjectUtil.isNotEmpty(filter)) {
                            de.displaySign = BranchProjectDisplayConstant.close;
                        }
                    }
                }
            }
        } else if (type == OpenLevelList.ZHUCAI.type) {

            //展开所有
            deList.forEach(o => {
                if (o.displaySign === BranchProjectDisplayConstant.close) {
                    o.displaySign = BranchProjectDisplayConstant.open;
                    ProjectDomain.getDomain(constructId).csxmDomain.updateDe(o);
                }
            })

            // let ZHUCAIList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_USER_DE);
            // let openDataList = [];
            // if (ObjectUtil.isNotEmpty(ZHUCAIList)) {
            //     for (let item of ZHUCAIList) {
            //         let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(item.sequenceNbr);
            //
            //         let rcjLists = await this.service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, item.deRowId, item.type);
            //         if (ObjectUtil.isNotEmpty(rcjLists)) {
            //             let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
            //             if (ObjectUtil.isNotEmpty(filter)) {
            //                 let openData = await this.openLineAndParent1(item, deList);
            //                 openData.forEach(p => {
            //                     openDataList.push(p.sequenceNbr);
            //                 });
            //                 openDataList.push(item.sequenceNbr);
            //                 de.displaySign = BranchProjectDisplayConstant.open;
            //             } else {
            //                 if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                     de.displaySign = BranchProjectDisplayConstant.close;
            //                 }
            //             }
            //         } else {
            //             if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                 de.displaySign = BranchProjectDisplayConstant.close;
            //             }
            //         }
            //     }
            // }
            //
            //
            // let ZHUCAIList1 = deList.filter(o => o.type === DeTypeConstants.SUB_DE_TYPE_DE || o.type === DeTypeConstants.DE_TYPE_RESOURCE || o.type === DeTypeConstants.DE_TYPE_USER_RESOURCE);
            // if (ObjectUtil.isNotEmpty(ZHUCAIList1)) {
            //     for (const p of ZHUCAIList1) {
            //         let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(p.sequenceNbr);
            //         if (de.deResourceKind == 4 || de.deResourceKind == 5) {
            //             let openData = await this.openLineAndParent1(p, deList);
            //             openData.forEach(p => {
            //                 openDataList.push(p.sequenceNbr);
            //             });
            //             openDataList.push(p.sequenceNbr);
            //             de.displaySign = BranchProjectDisplayConstant.noSign;
            //         } else {
            //             if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                 de.displaySign = BranchProjectDisplayConstant.noSign;
            //             }
            //         }
            //     }
            // }
            //
            // if (ObjectUtil.isNotEmpty(openDataList)) {
            //     let noFbList = deList.filter(o => o.type === DeTypeConstants.DE_TYPE_FB && !openDataList.includes(o.sequenceNbr));
            //     if (ObjectUtil.isNotEmpty(noFbList)) {
            //         noFbList.forEach(t => {
            //             let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(t.sequenceNbr);
            //             if (de.displaySign === BranchProjectDisplayConstant.open) {
            //                 de.displaySign = BranchProjectDisplayConstant.close;
            //             }
            //         });
            //     }
            // }
        }
    }


    async openLevelOne(constructId, top, deList) {
        top.displaySign = BranchProjectDisplayConstant.open;
        ProjectDomain.getDomain(constructId).csxmDomain.updateDe(top);
        return  deList.filter(o => o.parentId === top.sequenceNbr && (o.type === DeTypeConstants.DE_TYPE_FB || o.type === DeTypeConstants.DE_TYPE_ZFB));
    }

    async openLevelTwo(constructId, ONEList, deList) {
        let resultList = [];
        //展开至二级
        ONEList.forEach(item => {
            let TWOListLishi = deList.filter(o => o.parentId === item.sequenceNbr && (o.type === DeTypeConstants.DE_TYPE_ZFB));
            let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(item.sequenceNbr);
            if (ObjectUtil.isNotEmpty(TWOListLishi)) {
                de.displaySign = BranchProjectDisplayConstant.open;
                resultList = resultList.concat(TWOListLishi);
            } else {
                if (de.displaySign === BranchProjectDisplayConstant.open) {
                    de.displaySign = BranchProjectDisplayConstant.close;
                }
            }
        });
        return resultList;
    }

    async openLevelThree(constructId, ONEList, deList) {
        let resultList = [];
        //展开至二级
        ONEList.forEach(item => {
            let TWOListLishi = deList.filter(o => o.parentId === item.sequenceNbr && (o.type === DeTypeConstants.DE_TYPE_DELIST));
            let de = ProjectDomain.getDomain(constructId).ctx.csxmMap.getNodeById(item.sequenceNbr);
            if (ObjectUtil.isNotEmpty(TWOListLishi)) {
                de.displaySign = BranchProjectDisplayConstant.open;
                resultList = resultList.concat(TWOListLishi);
            } else {
                if (de.displaySign === BranchProjectDisplayConstant.open) {
                    de.displaySign = BranchProjectDisplayConstant.close;
                }
            }
        });
        return resultList;
    }

    async openLineAndParent1(pointLine, mockAllData) {
        let allData = mockAllData;
        let selectData = [];
        await this.getAllSelectData1(pointLine, mockAllData, selectData);
        let parent = allData.find(item => item.sequenceNbr === pointLine.parentId);
        await this.doChangeLineDisplayOne(selectData, parent, BranchProjectDisplayConstant.open);
        return selectData;
    }


    async getAllSelectData1(pointLine, mockAllData, selectData) {
        if (ObjectUtil.isNotEmpty(pointLine.parentId)) {
            let parentDe = mockAllData.find(o => o.sequenceNbr === pointLine.parentId);
            if (ObjectUtil.isNotEmpty(parentDe)) {
                selectData.push(parentDe);
                await this.getAllSelectData1(parentDe, mockAllData, selectData);
            }
        }
    }

    async doChangeLineDisplayOne(influenceLines, parent, sign) {
        for (let index = 0; index < influenceLines.length; ++index) {
            if (influenceLines[index].type != BranchProjectLevelConstant.de) {
                if (influenceLines[index].sequenceNbr == parent.sequenceNbr) {
                    influenceLines[index].displaySign = sign;
                    // influenceLines[index].displayStatu = 10;
                    let de = ProjectDomain.getDomain(parent.constructId).ctx.csxmMap.getNodeById(influenceLines[index].sequenceNbr);
                    de.displaySign = BranchProjectDisplayConstant.open;
                } else if (influenceLines[index].parentId == parent.parentId) {
                    // influenceLines[index].displayStatu = 10;
                } else {
                    influenceLines[index].displaySign = sign;
                    // influenceLines[index].displayStatu = 10;
                    let de = ProjectDomain.getDomain(parent.constructId).ctx.csxmMap.getNodeById(influenceLines[index].sequenceNbr);
                    de.displaySign = BranchProjectDisplayConstant.open;
                }
            }
        }
    }

    /**
     * 查找
     * @param args
     * @returns {Promise<void>}
     */
    async search(args) {
        let {constructId, unitId, deRowId, category, logicalOperator, conditions, pageArgs} = args;

        let result = [];
        let deListByDe = [];
        let deListByRcj = [];
        let fieldConditions = {};
        for (let field of Object.keys(conditions)) {
            let conditionsFilter = conditions[field].filter(item => ObjectUtils.isNotEmpty(item.value));
            // 添加逻辑运算符
            conditionsFilter = ObjectUtils.insertElement(conditionsFilter, 'AND')
            if (ObjectUtils.isNotEmpty(conditionsFilter)) {
                fieldConditions[field] = conditionsFilter;
            }
        }
        let deData = await this.getDeAllDepth(constructId, unitId, deRowId);
        let deList = ConvertUtil.deepCopy(deData);
        // deList.shift();
        deList = deList.filter(item => item.type !== DeTypeConstants.DE_TYPE_EMPTY && item.type !== DeTypeConstants.DE_TYPE_DEFAULT);
        for (let item of deList) {
            if (ObjectUtils.isNotEmpty(item.deCode)) {
                item.deCode = String(item.deCode)
            }
        }
        // 分部&&子目
        let types = []
        if (category.includes("分部")) {
            let typeConditions = [
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_FB
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_ZFB
                }
            ]
            types = types.concat(typeConditions)
        }
        if (category.includes("子目")) {
            let typeConditions = [
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_DELIST
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_DE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.SUB_DE_TYPE_DE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_RESOURCE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_USER_DE
                },
                {
                    operator: '===',
                    value: DeTypeConstants.DE_TYPE_USER_RESOURCE
                }
            ]
            types = types.concat(typeConditions)
        }
        types = ObjectUtils.insertElement(types, 'OR')

        // 执行搜索
        deListByDe = ObjectUtils.filterData(deList, fieldConditions, logicalOperator);
        deListByDe = ObjectUtils.filterData(deListByDe, {type: types}, 'AND');
        return {filterDe: deListByDe, deAll: deData};
    }

    /**
     * 过滤
     * @param args
     * @returns {Promise<void>}
     */
    async filterDe(args) {
        let {constructId, unitId, deRowId, conditions} = args;
        if (ObjectUtils.isEmpty(deRowId)) {
            let unitDe = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId).find(item => item.type === DeTypeConstants.DE_TYPE_DEFAULT);
            deRowId = unitDe?.sequenceNbr
        }
        // 转换为新的结构
        let newConditions = {
            color: conditions.color.map(value => ({ operator: '===', value })),
        };
        let deData = await this.getDeAllDepth(constructId, unitId, deRowId);
        let deList = ConvertUtil.deepCopy(deData);
        // deList.shift();
        // 执行搜索
        newConditions.annotations = []
        let formulas = [];
        for (let annotation of conditions.annotations) {
            let annotationConditions = []
            if (annotation) {
                annotationConditions.push({operator: "!==", value: undefined})
                annotationConditions.push({operator: "!==", value: null})
                annotationConditions.push({operator: "!==", value: ''})
                annotationConditions = ObjectUtils.insertElement(annotationConditions, 'AND')
                formulas.push(ObjectUtils.buildItemFormula("annotations", annotationConditions));
            } else {
                annotationConditions.push({operator: "===", value: undefined})
                annotationConditions.push({operator: "===", value: null})
                annotationConditions.push({operator: "===", value: ''})
                annotationConditions = ObjectUtils.insertElement(annotationConditions, 'OR')
                formulas.push(ObjectUtils.buildItemFormula("annotations", annotationConditions));
            }
        }

        let colorConditions = conditions.color.map(value => ({ operator: '===', value }))
        colorConditions = ObjectUtils.insertElement(colorConditions, 'OR')
        formulas.push(ObjectUtils.buildItemFormula("color", colorConditions));
        let formula = formulas.filter(item => item !== "()").join(' || ')

        let parentList = [];
        let csxmDomain = ProjectDomain.getDomain(constructId).csxmDomain
        let des = deList.filter(item => {
            return eval(formula);
        });

        // 新增: 查找所有符合条件的项的子级
        let desIds = des.map(item => item.sequenceNbr);
        let childrenList = [];
        // 递归查找所有子级
        const findAllChildren = (parentIds) => {
            if (!parentIds || parentIds.length === 0) return;
            let directChildren = deList.filter(
                item => parentIds.includes(item.parentId)
                    && !desIds.includes(item.sequenceNbr)
                    && !childrenList.some(child => child.sequenceNbr === item.sequenceNbr)
            );
            if (directChildren.length > 0) {
                childrenList.push(...directChildren);
                // 继续查找这些子级的子级
                findAllChildren(directChildren.map(item => item.sequenceNbr));
            }
        };

        // 开始查找子级
        findAllChildren(desIds);
        for (let de of des) {
            csxmDomain.findParents(de, parentList, [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
        }
        let parentIds = parentList.map(item => item.sequenceNbr)

        // 颜色标记行为项的父级Id
        let xParentIds = parentList.filter(item => item.type === DeTypeConstants.DE_TYPE_DELIST).map(item => item.sequenceNbr)
        let result = deList.filter(item => {
            let formulaFlag = eval(formula)
            // 增加子级判断条件
            let isChild = childrenList.some(child => child.sequenceNbr === item.sequenceNbr);
            let result = formulaFlag || isChild || xParentIds.includes(item.parentId) || (parentIds.includes(item.sequenceNbr)
                && (item.type === DeTypeConstants.DE_TYPE_DEFAULT
                    || item.type === DeTypeConstants.DE_TYPE_FB
                    || item.type === DeTypeConstants.DE_TYPE_ZFB
                    || item.type === DeTypeConstants.DE_TYPE_DELIST
                )
            );
            let show = formulaFlag || isChild || xParentIds.includes(item.parentId) || (parentIds.includes(item.sequenceNbr)
                && (item.type === DeTypeConstants.DE_TYPE_DEFAULT
                    || item.type === DeTypeConstants.DE_TYPE_FB
                    || item.type === DeTypeConstants.DE_TYPE_ZFB
                    || item.type === DeTypeConstants.DE_TYPE_DELIST
                )
            )
            if (show) {
                item.isFilter = true
            }
            return result;
        })
        let deLine = deList.find(item => item.sequenceNbr === deRowId)
        let resultUnitLine = result.find(item => item.sequenceNbr === deRowId)
        if (ObjectUtils.isNotEmpty(deLine) && ObjectUtils.isEmpty(resultUnitLine)) {
            deLine.isFilter = true;
            result.unshift(deLine)
        }
        // 删掉没有父级的伪定额
        let deLineId = result.map(item => item.sequenceNbr)
        result = result.filter(item => !(item.type === DeTypeConstants.SUB_DE_TYPE_DE && !deLineId.includes(item.parentId)))
        // 添加没有显示的伪定额
        for (let item1 of deList) {
            if (item1.type === DeTypeConstants.SUB_DE_TYPE_DE
                && !deLineId.includes(item1.sequenceNbr)
                && deLineId.includes(item1.parentId)
            ) {
                item1.isFilter = true
                result.push(item1)
            }
        }
        return result;
    }

    /**
     * 获取所有定额
     * @param constructId
     * @param unitId
     * @param deRowId
     * @returns {Promise<void>}
     */
    async getDeAllDepth(constructId, unitId, deRowId) {
        let deList = await this.service.gongLiaoJiProject.gljProjectCommonService.findCsxmDeByUnit(constructId, unitId);
        if (ObjectUtil.isEmpty(deList)) {
            return [];
        }
        deRowId = ObjectUtils.isNotEmpty(deRowId)? deRowId : deList[0].sequenceNbr
        // let deLists = ProjectDomain.getDomain(constructId).csxmDomain.getDeAllTreeDepth(constructId,unitId,deRowId,undefined);
        let unitData = ProjectDomain.getDomain(constructId).getProjectTree().find(o => o.sequenceNbr === unitId);
        let singleId = unitData.parentId;
        let pageData = await this.pageSearch(constructId, singleId, unitId, deRowId, 1, 300000, true, undefined,undefined)
        let deLists = ObjectUtils.cloneDeep(pageData.data);
        //重置序号
        if(ObjectUtil.isNotEmpty(deLists)){
            let index = 1;
            let sets = new Set();

            deLists.forEach(element => {
                if(element.type === DeTypeConstants.DE_TYPE_DEFAULT
                    || element.type === DeTypeConstants.DE_TYPE_FB
                    || element.type === DeTypeConstants.DE_TYPE_ZFB
                ){
                    sets.add(element.sequenceNbr);
                }
            });

            let type04CloseList = [];

            deLists.forEach(element => {
                if(element.type !== DeTypeConstants.DE_TYPE_DEFAULT
                    && element.type !== DeTypeConstants.DE_TYPE_FB
                    && element.type !== DeTypeConstants.DE_TYPE_ZFB
                    && sets.has(element.parentId)
                ){
                    element.dispNo = index;
                    index++;
                }

                if (ObjectUtil.isEmpty(element.isShowAnnotations)) {
                    element.isShowAnnotations = false;
                }

                if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && element.displaySign === BranchProjectDisplayConstant.noSign) {
                    //如果定额无箭头，则展开
                    let filter = deLists.filter(o => o.parentId === element.sequenceNbr);
                    if (ObjectUtil.isNotEmpty(filter)) {
                        element.displaySign = BranchProjectDisplayConstant.open;
                    }
                }

                if ((element.type === DeTypeConstants.DE_TYPE_DE || element.type === DeTypeConstants.DE_TYPE_USER_DE) && element.displaySign === BranchProjectDisplayConstant.close) {
                    //如果定额收起，则不展示05类型
                    type04CloseList.push(element.deRowId);
                }

            });

            // if(ObjectUtil.isNotEmpty(type04CloseList)){
            //     deLists = deLists.filter(p=>ObjectUtil.isEmpty(p.parentId) || !type04CloseList.includes(p.parentId));
            // }
        }
        //pageData

        return deLists;
    }

    /**
     * 定额是否关联子目
     * @param args
     * @returns {Promise<void>}
     */
    async existRelationDe(args) {
        let {constructId, deRowId} = args
        let relationDeList = await ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.fDeId === deRowId && item.isRelationDe === true && String(item.quantityExpression)?.includes('GCL'));
        return relationDeList;
    }



    /**
     * 批量删除子目-查询列表
     */
    async batchDelBySeachList(args) {
        let {constructId, singleId, unitId, scopeType, showType, code, name} = args;
        let result = [];
        let projectObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        if (scopeType != 1) {
            let array = [];

            result.push(projectObj);
            // result = await this.service.constructProjectService.generateLevelTreeNode(projectObj, array);
            // 获取该单项下的所有单位
            PricingGSUtils.getAllProjectsByCurrentNode(projectObj.children, array);
            result.push(...array);
            result.forEach(item => {
                item.deName = item.name;
            });
        }
        let unitList = [];
        if (scopeType == 1) {
            // 获取当前单位
            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            unitList.push(unitProject);
        } else {
            // 获取该单项下的所有单位
            PricingGSUtils.getUnitProjectsByCurrentNode(projectObj.children, unitList);
        }
        for (let unit of unitList) {
            let {constructId, spId, sequenceNbr} = unit;

            // 1. 获取预算书定额的所有取费专业类型
            let fbFx = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === sequenceNbr);
            // 2. 获取措施项目定额的所有取费专业类型
            let csxm = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === sequenceNbr);

            let f = (item) => {
                // if (item.type != BranchProjectLevelConstant.de && !item.children.length) return false;
                if (item.isTempRemove === CommonConstants.COMMON_YES) return false;  // 临时删除的
                if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE || item.type === DeTypeConstants.DE_TYPE_ZHUANSHI_FEE
                    || item.type === DeTypeConstants.DE_TYPE_USER_DE) {
                    if (ObjectUtils.isNotEmpty(name)) {
                        if (!item.deName.includes(name)) return false;
                    }
                    if (ObjectUtils.isNotEmpty(code)) {
                        if (!item.deCode.includes(code)) return false;
                    }
                }
                return true;
            }

            // 将数组按照f条件进行过滤
            fbFx[0].deName = '单位工程';
            let fbFxAllData = fbFx.filter(f).sort((a, b) => a.index - b.index);

            // 排序
            let sets = new Set();
            fbFxAllData.forEach(element => {
                if(element.type === DeTypeConstants.DE_TYPE_DEFAULT || element.type === DeTypeConstants.DE_TYPE_FB
                    || element.type === DeTypeConstants.DE_TYPE_ZFB || element.type === DeTypeConstants.DE_TYPE_DELIST){
                    sets.add(element.sequenceNbr);
                }
            });

            let index = 1;
            fbFxAllData.forEach(element => {
                if(element.type !== DeTypeConstants.DE_TYPE_DEFAULT && element.type !== DeTypeConstants.DE_TYPE_FB
                    && element.type !== DeTypeConstants.DE_TYPE_ZFB && element.type !== DeTypeConstants.DE_TYPE_DELIST && sets.has(element.parentId)){
                    element.dispNo = index;
                    index++;
                }
            });


            let filterArr = [], filterArr2 = [...new Set(csxm.filter(item => item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE
                || item.type === DeTypeConstants.DE_TYPE_ZHUANSHI_FEE || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_EMPTY).map(item2 => item2.parentId))];
            let f2 = (item) => {
                // if (item.isTempRemove === CommonConstants.COMMON_YES) return false;  // 临时删除的
                // if (item.type === DeTypeConstants.DE_TYPE_DE || item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE || item.type === DeTypeConstants.DE_TYPE_ZHUANSHI_FEE
                //     || item.type === DeTypeConstants.DE_TYPE_USER_DE) {
                //     if (ObjectUtils.isNotEmpty(name)) {
                //         if (!item.deName.includes(name)) return false;
                //     }
                //     if (ObjectUtils.isNotEmpty(code)) {
                //         if (!item.deCode.includes(code)) return false;
                //     }
                // }
                if ((item.awfType && item.awfType == 2)) {
                    filterArr.push(item.sequenceNbr);
                    return false
                }
                if (filterArr.includes(item.parentId)) {
                    filterArr.push(item.sequenceNbr);
                    return false
                }
                return true;
            }
            let csxmAllData = csxm.filter(f2).filter(f).sort((a, b) => a.index - b.index);

            // 转树，为了给措施项目的定额排序
            let arrayTree = xeUtils.toArrayTree(csxmAllData, {
                key: 'sequenceNbr',
                parentKey: 'parentId',
            });
            csxmAllData = xeUtils.toTreeArray(arrayTree);
            // 排序
            let sets2 = new Set();
            csxmAllData.forEach(element => {
                if(element.type === DeTypeConstants.DE_TYPE_DEFAULT || element.type === DeTypeConstants.DE_TYPE_FB
                    || element.type === DeTypeConstants.DE_TYPE_ZFB || element.type === DeTypeConstants.DE_TYPE_DELIST){
                    sets2.add(element.sequenceNbr);
                }
            });

            // 当前单位的工程专业
            let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
            let qfMajorTypeMoneyMap = Array.from(new Map(Object.entries(unitProject.qfMajorTypeMoneyMap)).values());
            let index2 = qfMajorTypeMoneyMap.length + 1;  // 是几个取费专业的安文费数据
            csxmAllData.forEach(element => {
                if(element.type !== DeTypeConstants.DE_TYPE_DEFAULT && element.type !== DeTypeConstants.DE_TYPE_FB
                    && element.type !== DeTypeConstants.DE_TYPE_ZFB && element.type !== DeTypeConstants.DE_TYPE_DELIST && sets2.has(element.parentId)){
                    element.dispNo = index2;
                    index2++;
                }
            });

            //分部分项
            for (let item of fbFxAllData) {
                if (item.parentId == "0" || !item.parentId) {
                    item.parentId = sequenceNbr;
                }
                item.pageType = "fbfx";
                result.push(item);
            }

            //措施项目
            for (let item of csxmAllData) {
                if (item.parentId == "0" || !item.parentId) {
                    item.parentId = sequenceNbr;
                }
                // 添加定额的措施项
                if (item.type === DeTypeConstants.DE_TYPE_DELIST) {
                    if (filterArr2.includes(item.sequenceNbr)) {
                        item.pageType = "csxm";
                        result.push(item);
                    }
                } else {
                    item.pageType = "csxm";
                    result.push(item);
                }
            }
        }


        // for (let unit of unitList) {
        //     let {constructId, spId, sequenceNbr} = unit;
        //
        //     // 1. 获取预算书定额的所有取费专业类型
        //     let fbFx = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === sequenceNbr); // && ObjectUtils.isNotEmpty(item.costFileCode)
        //     // 2. 获取措施项目定额的所有取费专业类型
        //     let csxm = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === sequenceNbr);  //  && !['03'].includes(item.type)
        //
        //     let f = (item) => {
        //         // if (item.type != BranchProjectLevelConstant.de && !item.children.length) return false;
        //         if (item.isTempRemove === CommonConstants.COMMON_YES) return false;  // 临时删除的
        //         if (item.type == BranchProjectLevelConstant.de) {
        //             if (ObjectUtils.isNotEmpty(name)) {
        //                 if (!item.deName.includes(name)) return false;
        //             }
        //             if (ObjectUtils.isNotEmpty(code)) {
        //                 if (!item.deCode.includes(code)) return false;
        //             }
        //         }
        //         return true;
        //     }
        //
        //     // 将数组按照f条件进行过滤
        //     fbFx[0].deName = '单位工程';
        //     let fbFxAllData = fbFx.filter(f);
        //
        //     let filterArr = [], filterArr2 = [];
        //     let f2 = (item) => {
        //         if (item.isTempRemove === CommonConstants.COMMON_YES) return false;  // 临时删除的
        //         if (item.type == BranchProjectLevelConstant.de) {
        //             if (ObjectUtils.isNotEmpty(name)) {
        //                 if (!item.deName.includes(name)) return false;
        //             }
        //             if (ObjectUtils.isNotEmpty(code)) {
        //                 if (!item.deCode.includes(code)) return false;
        //             }
        //         }
        //         // if (item.type === DeTypeConstants.DE_TYPE_DELIST) {
        //         //     filterArr2.push(item.sequenceNbr);
        //         //     // return false
        //         // }
        //         if (item.type === DeTypeConstants.DE_TYPE_DE) {
        //             filterArr2.push(item.parentId);
        //             // return false
        //         }
        //         if ((item.awfType && item.awfType == 2)) {
        //             filterArr.push(item.sequenceNbr);
        //             return false
        //         }
        //         if (filterArr.includes(item.parentId)) {
        //             filterArr.push(item.sequenceNbr);
        //             return false
        //         }
        //         return true;
        //     }
        //     let csxmAllData = csxm.filter(f2);
        //
        //     //分部分项
        //     for (let item of fbFxAllData) {
        //         if (item.parentId == "0" || !item.parentId) {
        //             item.parentId = sequenceNbr;
        //         }
        //         item.pageType = "fbfx";
        //         result.push(item);
        //     }
        //
        //     //措施项目
        //     let resultId = [];
        //     for (let item of csxmAllData) {
        //         if (item.parentId == "0" || !item.parentId) {
        //             item.parentId = sequenceNbr;
        //         }
        //         // // 添加定额的措施项
        //         // resultId = result.map(item2 => item2.sequenceNbr);
        //         // if (!filterArr2.includes(item.sequenceNbr) && item.type === DeTypeConstants.DE_TYPE_DE && !resultId.includes(item.parentId)) {
        //         //     let parent = csxm.find(p => p.sequenceNbr === item.parentId);
        //         //     // result数组的sequenceNbr不包含
        //         //     parent.pageType = "csxm";
        //         //     result.push(parent);
        //         // }
        //         // item.pageType = "csxm";
        //         // result.push(item);
        //         //
        //         if (item.type === DeTypeConstants.DE_TYPE_DELIST) {
        //             if (filterArr2.includes(item.sequenceNbr)) {
        //                 item.pageType = "csxm";
        //                 result.push(item);
        //             }
        //         } else {
        //             item.pageType = "csxm";
        //             result.push(item);
        //         }
        //     }
        // }
        return result;
    }


    /**
     * 从清单定额索引中点击插入
     */
    async fillFromIndexPageByConversion(args) {
        let {constructId, singleId, unitId, pointLine, indexId} = args;
        let deRow = pointLine;
        let csxmDomain = ProjectDomain.getDomain(deRow.constructId).csxmDomain;
        let deRowReturn;
        let prevDeRow;
        let index;
        if(ObjectUtils.isNotEmpty(deRow.sequenceNbr))
        {
            prevDeRow = csxmDomain.getDeById(deRow.sequenceNbr);
            index = prevDeRow.index + 1;
        }
        let baseDeId = indexId;
        if(deRow.parentId === deRow.sequenceNbr){
            deRow.type = DeTypeConstants.DE_TYPE_DE;
        }
        let deRowModel = new StandardDeModel(deRow.constructId,deRow.unitId,Snowflake.nextId(),deRow.parentId,deRow.type);
        await csxmDomain.createDeRow(deRowModel,index);
        deRowReturn = await csxmDomain.appendBaseDe(deRowModel.constructId,deRowModel.unitId,baseDeId,deRowModel.sequenceNbr,true);
        //处理人材机消耗量
        await this._updateRcjResQty(deRowReturn,csxmDomain,1,this.service, true);
        let res =  CsxmDomain.filter4DeTree(deRowReturn);
        return res;
    }

}




GljStepItemCostService.toString = () => '[class GljStepItemCostService]';
module.exports = GljStepItemCostService;
