const {
    BzhjbgsAndbgbCalculationBasis,
    GcjlfCalculationBasis,
    GcjlfhbCalculationBasis,
    GcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis,
    ZbdlfhbCalculationBasis,
    StbcJszxCalculationBasis,
    StbcYtsjCalculationBasis,
    JsqdklxDebjCalculationBasis,
    DzzhwxxpgfCalculationBasis,
    ShfxwdpgbgBzshwdfxpgbgfCalculationBasis,
    ShfxwdpgbgPjshwdfxpgbgfCalculationBasis,
    StbcfStbcbcfCalculationBasis,
    StbcfJspgbgbfCalculationBasis,
    StbcfStbcjlfCalculationBasis,
    JsqdklxDebxCalculationBasis,
    ZbdlftyCalculationBasis,
    XmjsglfCalculationBasis,
    StbcfCalculationBasis,
    ZjzxfCalculationBasis,
    JsxmqqgzzxfCalculationBasis
} = require('../models/GsOtherProjectCostCalculationBasis');

const {Service} = require('../../../core');
const gsJsqtf = require('../jsonData/gs_jsqtf.json');
const gsJsqtfFydm = require('../jsonData/gs_jsqtf_fydm.json');
const gsJsqtfFyjsq = require('../jsonData/gs_jsqtf_fyjsq.json');
const {Snowflake} = require('../utils/Snowflake');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const ProjectDomain = require('../domains/ProjectDomain');
const OtherProjectCostOptionMenuConstants = require('../constants/OtherProjectCostOptionMenuConstants');
const CommonConstants = require('../constants/CommonConstants');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const {GsOtherProjectCostCode} = require('../models/GsOtherProjectCostCode');
const {GsOtherProjectCost} = require('../models/GsOtherProjectCost');
const {ConvertUtil} = require('../utils/ConvertUtils');
const {ObjectUtils} = require('../utils/ObjectUtils');
const {getConnection, getRepository, getManager} = require('typeorm');
const gsJsqtfFyjsyj = require('../jsonData/gs_jsqtf_fyjsyj.json');
const xeUtils = require("xe-utils");
const {ResponseData} = require("../../../common/ResponseData");
const {NumberUtil} = require('../utils/NumberUtil');
const {ComputationalFormula} = require('../constants/ComputationalFormula');
const fs = require('fs');
const XLSX = require('xlsx');
const FileOperatorType = require("../constants/FileOperatorType");
const UtilsPs = require("../../../core/ps");
const OtherProjectCostCategoryEnum = require("../enums/OtherProjectCostCategoryEnum");
const {BrowserWindow, dialog} = require('electron');
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {GsOtherProjectCostCalculators} = require("../models/GsOtherProjectCostCalculators");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const {GsBasePolicyDocument} = require("../models/GsBasePolicyDocument");

/**
 * 建设其他费
 */
class GsOtherProjectCostService extends Service {

    constructor(ctx) {
        super(ctx);
        this.gsBasePolicyDocumentDao = this.app.db.PreliminaryEstimate.manager.getRepository(GsBasePolicyDocument);
    }

    /**
     * 初始化建设其他费代码
     * @returns {any[]}
     */
    defaultOtherProjectCostCode(args) {
        let gsqtfFydmArray = [];
        // 费用代码
        for (let i in gsJsqtfFydm) {
            let otherProjectCostCode = new GsOtherProjectCostCode();
            let jsqtfFydm = gsJsqtfFydm[i];
            // 根据model属性写入数据
            ConvertUtil.setDstBySrc(jsqtfFydm, otherProjectCostCode);
            gsqtfFydmArray.push(otherProjectCostCode);
        }
        return gsqtfFydmArray;
    }

    /**
     * 遍历整个树结构并输出所有节点名称的拼接结果
     * @param node
     * @param separator
     * @returns {string}
     */
    concatenateNames(node, separator = '-') {
        // 如果当前节点为空，则直接返回空字符串
        if (!node) return '';

        // 如果当前节点有子节点，递归调用自身处理子节点，并将结果连接起来
        if (node.children && node.children.length > 0) {
            node.children.forEach(child => {
                child.nameBackUp = node.nameBackUp + separator + child.name;
                this.concatenateNames(child, separator);
            });
        }
    }

    /**
     * 添加节点属性
     * @param gsJsqtfFyjsq
     */
    addPropertyToTree(gsJsqtfFyjsq) {
        gsJsqtfFyjsq.forEach(node => {
            // 给树结构添加属性
            node["sequenceNbr"] = Snowflake.nextId();
            if (node.children && node.children.length) {
                this.addPropertyToTree(node.children);
            }
        });
    }

    /**
     * 初始化建设其他费费用计算器
     * @param args
     * @returns {any[]}
     */
    defaultOtherProjectCostCalculators(args) {
        // 复制，防止修改原值
        let gsJsqtfFyjsqCopy = ConvertUtil.deepCopy(gsJsqtfFyjsq);
        // 添加节点属性
        this.addPropertyToTree(gsJsqtfFyjsqCopy);

        // 遍历整个树结构并输出所有节点名称的拼接结果
        gsJsqtfFyjsqCopy.forEach(node => {
            node.nameBackUp = node.name;
            this.concatenateNames(node);
        });

        // 费用代码
        let gsqtfFyjsqArray = [];
        for (let i in gsJsqtfFyjsqCopy) {
            let gsOtherProjectCostCalculator = new GsOtherProjectCostCalculators();
            let jsqtfFyjsq = gsJsqtfFyjsqCopy[i];
            // 根据model属性写入数据
            ConvertUtil.setDstBySrc(jsqtfFyjsq, gsOtherProjectCostCalculator);
            gsqtfFyjsqArray.push(gsOtherProjectCostCalculator);
        }
        return gsqtfFyjsqArray;
    }

    /**
     * 获取建设其他费费用代码
     * @param args
     */
    async getOtherProjectCostCode(args) {
        let constructId = args.projectId;
        // 获取建设其他费费用代码
        let otherProjectCostCodes = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_CODE);
        // 获取建设其他费
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.OTHER_PROJECT_COST.je;

        // 更新联合运转费 = 建设其他费中联合运转费的费用额
        let otherProjectCost = otherProjectCosts.find(item => item.name === "联合试运转费");
        otherProjectCostCodes[5].price = ObjectUtils.isEmpty(otherProjectCost) ? 0 : NumberUtil.numberScale(parseFloat(otherProjectCost.amount), je);

        // 更新建设其他费费用代码
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF_CODE, otherProjectCostCodes);
        return otherProjectCostCodes;
    }

    /**
     * 获取建设其他费费用类别下拉框
     * @param args
     */
    getOtherProjectCostCategoryEnum(args) {
        let CategoryEnum = [];
        for (let enumKey in OtherProjectCostCategoryEnum) {
            CategoryEnum.push(OtherProjectCostCategoryEnum[enumKey].desc);
        }
        return CategoryEnum;
    }

    /**
     * 获取建设其他费费用代码
     * @param args
     */
    async countOtherProjectCostCode(args) {
        let constructId = args.projectId;

        // 获取当前工程项目下所有的单位
        let unitProjects = ProjectDomain.getDomain(constructId).getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.OTHER_PROJECT_COST.je;

        let jagcf = 0, jzgcf = 0, azgcf = 0;
        // 获取该项目的所有单位工程
        for (let i in unitProjects) {
            let unitProject = unitProjects[i];
            // 建筑工程费 = ∑各建筑单位工程造价合价
            if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ) {
                jzgcf = NumberUtil.add(jzgcf, NumberUtil.numberScale(unitProject.projectCost, je));
            }
            // 安装工程费 = ∑各安装单位工程造价合价
            if (unitProject.constructMajorType === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ) {
                azgcf = NumberUtil.add(azgcf, NumberUtil.numberScale(unitProject.projectCost, je));
            }
            // 建安工程费
            jagcf = NumberUtil.add(jagcf, NumberUtil.numberScale(unitProject.projectCost, je));
        }
        // 建安工程费 = ∑各建筑单位工程造价合价+各安装单位工程造价合价
        // jagcf = NumberUtil.addParams(jagcf, jzgcf, azgcf);
        // 整个项目的总工程造价值
        let unitSbf = 0;
        for (let j = 0; j < unitProjects.length; j++) {
            let unitProject = unitProjects[j];
            // 获取该单位的费用汇总
            let param = {
                constructId: constructId,
                singleId: unitProject.parentId,
                unitId: unitProject.sequenceNbr
            }
            let unitCostSummaryPriceMap = await this.service.PreliminaryEstimate.gsUnitCostSummaryService.getUnitCostSummary(param);
            unitSbf = NumberUtil.add(unitSbf, NumberUtil.numberScale(unitCostSummaryPriceMap.get("设备费"), je));
        }
        let sbf = unitSbf;

        // 设备购置费 = ∑设备购置费汇总合计
        let equipmentCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SBGZF_KEY)
            .get(this.service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null, FunctionTypeConstants.SBGZF_KEY_TYPE_HZ));
        let sbgzf = NumberUtil.numberScale(equipmentCosts[0].price, je);

        // 联合运转费 = 建设其他费中联合运转费的费用额
        let otherProjectCosts = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        let otherProjectCost = otherProjectCosts.find(item => item.name === "联合试运转费");
        let lhyzf = ObjectUtils.isEmpty(otherProjectCost) ? 0 : NumberUtil.numberScale(parseFloat(otherProjectCost.amount), je);

        // 获取建设其他费费用代码
        let otherProjectCostCodes = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_CODE);
        otherProjectCostCodes[0].price = jagcf;
        otherProjectCostCodes[1].price = jzgcf;
        otherProjectCostCodes[2].price = azgcf;
        otherProjectCostCodes[3].price = ObjectUtils.isEmpty(sbf) ? 0 : sbf;
        otherProjectCostCodes[4].price = ObjectUtils.isEmpty(sbgzf) ? 0 : sbgzf;
        otherProjectCostCodes[5].price = ObjectUtils.isEmpty(lhyzf) ? 0 : lhyzf;
        // 更新建设其他费费用代码
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF_CODE, otherProjectCostCodes);

        // 调用计算建设其他费
        await this.countOtherProjectCost(constructId, otherProjectCostCodes, otherProjectCosts);
        return otherProjectCostCodes;
    }


    /**
     * 初始化建设其他费列表
     * @param args
     */
    defaultOtherProjectCost(args) {
        // 复制，防止修改原值
        let gsJsqtfCopy = ConvertUtil.deepCopy(gsJsqtf);
        // this.addLevelNumbers(gsJsqtfCopy[0], '1');
        // 添加节点属性
        this.addPropertiesToTree(gsJsqtfCopy, null);
        // 树结构转数组
        let otherProjectCosts = xeUtils.toTreeArray(gsJsqtfCopy);
        otherProjectCosts.forEach(item => {
            item.children = [];
        });
        return otherProjectCosts;
    }

    /**
     * 添加节点属性
     * @param gsJsqtf
     * @param parentId
     */
    addPropertiesToTree(gsJsqtf, parentId) {
        gsJsqtf.forEach(node => {
            // 添加sequenceNbr
            node["sequenceNbr"] = Snowflake.nextId();
            // 添加父节点
            node["parentId"] = parentId;
            // 添加层级权限
            if (node.levelType === CommonConstants.JSQTF_LEVEL_TYPE_0) {
                node["permission"] = OtherProjectCostOptionMenuConstants.FirstSubItem;
            }
            if (node.levelType === CommonConstants.JSQTF_LEVEL_TYPE_1) {
                node["permission"] = OtherProjectCostOptionMenuConstants.TwoSubItem;
            }
            if (node.levelType === CommonConstants.JSQTF_LEVEL_TYPE_2) {
                node["permission"] = OtherProjectCostOptionMenuConstants.ThreeSubItem;
            }
            if (node.levelType === CommonConstants.JSQTF_LEVEL_TYPE_3) {
                node["permission"] = OtherProjectCostOptionMenuConstants.fourSubItem;
            }
            // if (ObjectUtils.isEmpty(node.category)) {
            //     // 移除权限：计价文件查询 = 3
            //     node["permission"] = node["permission"].filter(item => item !== OtherProjectCostOptionMenuConstants.qtfPolicyDocumentMenu);
            // }
            node.adopted = false;  // 是否被引用
            node.fields = null;
            // 备份默认值
            node.calculationMethodBackUp = node.calculationMethod;
            node.priceBackUp = node.price;
            node.quantityBackUp = node.quantity;
            if (node.children && node.children.length) {
                this.addPropertiesToTree(node.children, node.sequenceNbr);
            }
        });
    }

    /**
     * 获取建设其他费列表
     * @param args
     */
    async getOtherProjectCostList(args) {
        let constructId = args.projectId;
        // 获取工程项目的建设其他费
        let otherProjectCostArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);

        if (ObjectUtils.isNotEmpty(otherProjectCostArray)) {
            // 复制 添加是否被引用标识
            let otherProjectCosts = ConvertUtil.deepCopy(otherProjectCostArray);
            for (let i = 0; i < otherProjectCostArray.length; i++) {
                //判断需要删除的费用汇总中的费用代号是否被引用
                let deleteItem = otherProjectCostArray[i];
                if (ObjectUtils.isNotEmpty(deleteItem)) {
                    for (let i = 0; i < otherProjectCosts.length; i++) {
                        if (!ObjectUtils.isEmpty(otherProjectCosts[i].price) && otherProjectCosts[i].calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2) {
                            if (isNaN(Number(otherProjectCosts[i].price))) {
                                let codeList = otherProjectCosts[i].price.split(/[+\-*/]/);
                                if (!ObjectUtils.isEmpty(codeList) && !ObjectUtils.isEmpty(deleteItem.code)) {
                                    if (this.stringInArray(codeList, deleteItem.code)) {
                                        deleteItem.adopted = true;  // 该行已被引用，不可删除
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 转树
        let arrayTree = xeUtils.toArrayTree(otherProjectCostArray, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        // 添加序号
        this.addLevelNumbers(arrayTree[0], '1');
        return xeUtils.toTreeArray(arrayTree);
    }

    /**
     * 树结构,每一级的值向上一级汇总
     * @param node 树节点
     * @param je  金额精度
     * @returns {*}
     */
    sumChildNodeAmounts(node, je) {
        if (!node.children || node.children.length === 0) {
            // return; // 叶子节点不做任何操作
            if (ObjectUtils.isNotEmpty(node.amount)) {
                node.amount = NumberUtil.numberScale(parseFloat(node.amount), 2);
            } else {
                node.amount = 0.00;
            }
            return node;
        }
        // 累加子节点的金额
        node.amount = node.children.reduce((sum, child) => {
            if (child.ifCalculate) {
                this.sumChildNodeAmounts(child, je);
                return NumberUtil.numberScale(NumberUtil.add(sum, (parseFloat(child.amount) || 0)), 2);  // 加上当前子节点金额
            } else {
                this.sumChildNodeAmounts(child, je);
                return NumberUtil.numberScale(NumberUtil.add(sum, 0), 2);  // 加上当前子节点金额
            }
        }, 0);
        return node; // 返回当前节点累计的金额
    }

    /**
     * 添加概算汇总
     * @param args
     */
    async addOtherProjectCost(args) {
        let {projectId, otherProjectCostSummary} = args;
        let parentId = otherProjectCostSummary.parentId;   // 添加元素的父id

        // 获取工程项目的建设其他费   工程项目id
        let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(projectId);
        let je = precision.OTHER_PROJECT_COST.je;

        otherProjectCosts.forEach(item => {
            item.children = [];
        });
        // 转树
        let arrayTree = xeUtils.toArrayTree(otherProjectCosts, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });

        // 遍历建设其他费，找到点击行
        let projectCostParentNode = this.getNodeById(arrayTree[0], parentId);

        // 在父级node上新增同级，单位默认为元，计算方式默认为单价*数量，金额为计算值，其余数据置空
        otherProjectCostSummary.sequenceNbr = Snowflake.nextId();
        otherProjectCostSummary.unit = "元";
        otherProjectCostSummary.amount = 0;
        otherProjectCostSummary.fields = null;
        otherProjectCostSummary.ifCalculate = true;
        otherProjectCostSummary.parentId = parentId;
        otherProjectCostSummary.levelType = projectCostParentNode.levelType + 1;
        if (otherProjectCostSummary.levelType === 3) {
            otherProjectCostSummary.permission = OtherProjectCostOptionMenuConstants.fourSubItem;
        } else {
            otherProjectCostSummary.permission = OtherProjectCostOptionMenuConstants.ThreeSubItem;
        }
        if (ObjectUtils.isEmpty(otherProjectCostSummary.category)) {
            // 移除权限：计价文件查询 = 3
            otherProjectCostSummary.permission = otherProjectCostSummary.permission.filter(item => item !== OtherProjectCostOptionMenuConstants.qtfPolicyDocumentMenu);
        }
        otherProjectCostSummary.calculationMethod = OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_1;
        otherProjectCostSummary.adopted = false;   // 是否被引用
        projectCostParentNode.children.push(otherProjectCostSummary);

        // 一级子项“计算方式”、“单价/计算基数”、“数量/费率”置空，且不可修改，“金额”为其子项“金额”汇总；
        projectCostParentNode.calculationMethod = null;
        projectCostParentNode.price = null;
        projectCostParentNode.quantity = null;
        projectCostParentNode.priceDescription = null;
        projectCostParentNode.fields = null;
        projectCostParentNode.calculationProcess = null;
        projectCostParentNode.category = null;
        projectCostParentNode.ifCalculate = true;
        projectCostParentNode.amount = projectCostParentNode.children.reduce((accumulator, currentValue) => {
            return accumulator + NumberUtil.numberScale(currentValue.amount, je);
        }, 0);

        // this.addLevelNumbers(arrayTree[0], '1');
        // 树结构,每一级的值向上一级汇总
        let otherProjectCostList = this.sumChildNodeAmounts(arrayTree[0], je);
        let newList = xeUtils.toTreeArray(new Array(otherProjectCostList));
        // 更新建设其他费
        ProjectDomain.getDomain(projectId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF, newList);
        return newList;
    }


    /**
     * 获取指定的节点
     * @param nodeList
     * @param sequenceNbr
     * @returns {null|*}
     */
    getNodeById(nodeList, sequenceNbr) {
        if (nodeList.sequenceNbr === sequenceNbr) {
            return nodeList;
        }
        for (let i = 0; i < nodeList.children.length; i++) {
            const found = this.getNodeById(nodeList.children[i], sequenceNbr);
            if (found) {
                return found;
            }
        }
        return null;
    }


    /**
     * 删除建设其他费列表
     * @param args
     */
    async delOtherProjectCost(args) {
        let {projectId} = args;
        // 删除行的id
        let otherProjectCostId = args.otherProjectCostId;
        // let parentId = args.parentId;

        // 获取工程项目的建设其他费
        let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(projectId);
        let je = precision.OTHER_PROJECT_COST.je;

        //判断需要删除的建设其他费中的费用代号是否被引用
        let deleteItem = otherProjectCosts.find(item => item.sequenceNbr === otherProjectCostId);
        if (ObjectUtils.isNotEmpty(deleteItem)) {
            for (let i = 0; i < otherProjectCosts.length; i++) {
                if (ObjectUtils.isNotEmpty(otherProjectCosts[i].price)) {
                    if (!ObjectUtils.isNumberStr(otherProjectCosts[i].price)) {
                        let codeList = otherProjectCosts[i].price.split(/[+\-*/]/);
                        if (ObjectUtils.isNotEmpty(codeList) && ObjectUtils.isNotEmpty(deleteItem.code)) {
                            if (this.stringInArray(codeList, deleteItem.code)) {
                                // return ResponseData.fail('该行已被引用，不可删除');
                                // 使用正则表达式匹配独立的变量名，假设变量名由字母和下划线组成，且前后不紧邻其他字母、数字或下划线
                                const regex = new RegExp(`(?<![a-zA-Z0-9_])${deleteItem.code}(?![a-zA-Z0-9_])`, 'g');
                                otherProjectCosts[i].price = otherProjectCosts[i].price.replace(regex, "※");
                                // otherProjectCosts[i].price = otherProjectCosts[i].price.replaceAll(deleteItem.code, "※");
                                otherProjectCosts[i].priceDescription = otherProjectCosts[i].priceDescription.replaceAll(deleteItem.name, "0");
                                // otherProjectCosts[i].amount = NumberUtil.subtract(otherProjectCosts[i].amount, deleteItem.amount);
                            }
                        }
                    }
                }
            }

            // 删除指定节点
            let otherProjectCostSummaryArray = otherProjectCosts.filter(item => item.sequenceNbr !== otherProjectCostId);
            // 获取同级
            let otherProjectCostNodes = otherProjectCostSummaryArray.filter(item => item.parentId === deleteItem.parentId);
            // 父节点下所有二级子项均被删除时，此时一级子项恢复为标准行，所有数据恢复为默认值。
            if (ObjectUtils.isEmpty(otherProjectCostNodes)) {
                // 获取父节点
                let otherProjectCostParent = otherProjectCostSummaryArray.find(item => item.sequenceNbr === deleteItem.parentId);
                otherProjectCostParent.amount = 0;
                otherProjectCostParent.calculationMethod = deleteItem.calculationMethodBackUp;
                otherProjectCostParent.price = deleteItem.priceBackUp;
                otherProjectCostParent.quantity = deleteItem.quantityBackUp;
            }

            // otherProjectCostSummaryArray.forEach(item => {
            //     item.children = [];
            // });
            // // 转树
            // let arrayTree = xeUtils.toArrayTree(otherProjectCostSummaryArray, {
            //     key: 'sequenceNbr',
            //     parentKey: 'parentId',
            // });
            // // this.addLevelNumbers(arrayTree[0], '1');
            // // 树结构,每一级的值向上一级汇总
            // let otherProjectCostList = this.sumChildNodeAmounts(arrayTree[0], je);
            // let newList = xeUtils.toTreeArray(new Array(otherProjectCostList));
            // // 更新建设其他费
            // ProjectDomain.getDomain(projectId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF, newList);


            // 获取工程项目的建设其他费费用代码
            let otherProjectCostCodeArray = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_CODE);
            // 调用计算建设其他费
            let newList = await this.countOtherProjectCost(projectId, otherProjectCostCodeArray, otherProjectCostSummaryArray)
            return ResponseData.success(newList);
        } else {
            return ResponseData.fail('该行不存在');
        }
    }


    /**
     * 判断 target中是否包含 arr
     * @param arr
     * @param target
     * @returns {boolean}
     */
    stringInArray(arr, target) {
        for (let item of arr) {
            if (item.includes(target)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 调用计算建设其他费
     * @param constructId
     * @param otherProjectCostCodeArray
     * @param otherProjectCostSummaryArray
     * @returns {*}
     */
    async countOtherProjectCost(constructId, otherProjectCostCodeArray, otherProjectCostSummaryArray) {
        //费用代码<费用代码,price>
        let priceMap = new Map();
        //计算基数 <费用汇总费用代号,calculateFormula>
        let codeFormulaMap = new Map();
        //费用汇总费率
        let codeRateMap = new Map();

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let je = precision.OTHER_PROJECT_COST.je;
        let freeRate = precision.OTHER_PROJECT_COST.SUMMARY.freeRate;
        let quantity = precision.OTHER_PROJECT_COST.SUMMARY.quantity;

        //费用代码
        for (let i = 0; i < otherProjectCostCodeArray?.length; i++) {
            let otherProjectCostCode = otherProjectCostCodeArray[i];
            let code = ObjectUtils.isEmpty(otherProjectCostCode.code) ? otherProjectCostCode.code : otherProjectCostCode.code.toLowerCase();
            priceMap.set(code, otherProjectCostCode.price);
        }
        //建设其他费汇总
        for (let i = 0; i < otherProjectCostSummaryArray.length; i++) {
            let otherProjectCost = otherProjectCostSummaryArray[i];
            let code = ObjectUtils.isEmpty(otherProjectCost.code) ? otherProjectCost.code : otherProjectCost.code.toLowerCase();

            if (ObjectUtils.isNotEmpty(otherProjectCost.price) && isNaN(Number(otherProjectCost.price))) {
                codeFormulaMap.set(code, otherProjectCost.price.toLowerCase());
            } else {
                codeFormulaMap.set(code, otherProjectCost.price);
            }
            if (ObjectUtils.isNotEmpty(otherProjectCost.quantity)) {
                let quantity = NumberUtil.numberScale(parseFloat(otherProjectCost.quantity), freeRate);
                codeRateMap.set(code, quantity);
            } else {
                codeRateMap.set(code, 100);
            }
            priceMap.set(code, otherProjectCost.amount);
        }
        for (let i = 0; i < otherProjectCostSummaryArray.length; i++) {
            let otherProjectCost = otherProjectCostSummaryArray[i];
            // if (ObjectUtils.isEmpty(calculateFormula)) {
            //     continue;
            // }

            if (otherProjectCost.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2) {
                if (otherProjectCost.price === '0' || otherProjectCost.price === 0 || ObjectUtils.isEmpty(otherProjectCost.price)) {
                    continue;
                }
                //计算基数
                let calculateFormula = otherProjectCost.price.toLowerCase();
                let afterCalculateFormula;
                if (!isNaN(Number(calculateFormula))) {
                    afterCalculateFormula = calculateFormula;
                } else {
                    // 分解字符串成表达式和变量名
                    const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
                    //存放替换后的计算公式
                    afterCalculateFormula = calculateFormula;
                    //递归计算费用汇总
                    afterCalculateFormula = await this.recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je);
                }
                let result;
                if (ObjectUtils.isNotEmpty(otherProjectCost.quantity)) {
                    result = NumberUtil.numberScale(eval(afterCalculateFormula), je) * NumberUtil.numberScale(otherProjectCost.quantity / 100, freeRate);
                } else {
                    result = ObjectUtils.isEmpty(afterCalculateFormula) ? 0.00 : NumberUtil.numberScale(eval(afterCalculateFormula), je);
                }
                otherProjectCost.amount = result;
            }
            if (otherProjectCost.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_3) {
                // 获取费用计算器
                let otherProjectCostCalculators = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_JSQ);

                // 更新费用计算器中值的变化,并将变化更新到建设其他费中
                await this.updateCalculatorToTree(otherProjectCostCalculators, constructId, otherProjectCost);
                // 获取当前费用计算器的参数、计算表达式
                // let fields = ConvertUtil.deepCopy(otherProjectCost.fields);
                // if (ObjectUtils.isNotEmpty(fields)) {
                //     // 检查是否为普通的对象且不是 Map
                //     if (Object.prototype.toString.call(fields) === '[object Object]' && !(fields instanceof Map)) {
                //         fields = new Map(Object.entries(fields));
                //     }
                //     let size = fields.size;
                //     if (size !== 0) {
                //         // 判断是否为空 & (引用费用代码，重新给金额赋值  或 如果费用计算器涉及造价咨询费，则需要单独处理钢筋数量)
                //         if (ObjectUtils.isNotEmpty(fields.get('calculatorBasis')) && (ObjectUtils.isNotEmpty(fields.get('source')) || ObjectUtils.isNotEmpty(fields.get('weightSource')))) {
                //             // 获取费用代码
                //             let moneyList = await this.service.PreliminaryEstimate.gsOtherProjectCostConfigService.getMoney({
                //                 constructId: constructId,
                //                 calculatorBasis: fields.get('calculatorBasis')
                //             });
                //             // 引用费用代码，重新给金额赋值
                //             if (ObjectUtils.isNotEmpty(moneyList)) {
                //                 let sourceArray = fields.get('source').replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
                //                 let moneyTotal = ConvertUtil.deepCopy(fields.get('source'));
                //                 for (let i = 0; i < sourceArray.length; i++) {
                //                     let money = moneyList.find(item => item.name === sourceArray[i]);
                //                     if (ObjectUtils.isNotEmpty(money)) {
                //                         moneyTotal = moneyTotal.replace(sourceArray[i], money.money);
                //                     }
                //                 }
                //                 fields.set('money', eval(moneyTotal));
                //             }
                //
                //             // 查询三彩汇总-钢筋数量
                //             let args = {constructId: constructId};
                //             const scGj = await this.service.PreliminaryEstimate.gsRcjCollectService.getScCountGJ(args);
                //             if (ObjectUtils.isNotEmpty(scGj) && ObjectUtils.isNotEmpty(fields.get('weightSource'))) { // 三材钢筋数量：下拉框选择的   空：则是手动输入的
                //                 fields.set('weight', ObjectUtils.isNotEmpty(scGj.scCount) ? scGj.scCount : 0);
                //             }
                //
                //             // 调用计算方式计算
                //             let otherProjectCostCalculatorFormula = await this.getOtherProjectCostCalculatorFormula(Object.fromEntries(fields));
                //             let calculationProcess = otherProjectCostCalculatorFormula.calculationProcess;
                //             fields.set('amount', NumberUtil.multiply(calculationProcess.split('=').pop(), 10000));
                //             // 获取公式的计算结果，赋值
                //             otherProjectCost.fields = fields;
                //             otherProjectCost.calculationProcess = calculationProcess;
                //             otherProjectCost.amount = NumberUtil.multiply(calculationProcess.split('=').pop(), 10000);
                //         }
                //     }
                // }
            }
            if (otherProjectCost.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_1) {
                otherProjectCost.amount = NumberUtil.numberScale(parseFloat(otherProjectCost.price), je) * NumberUtil.numberScale(otherProjectCost.quantity, quantity);
            }
        }

        otherProjectCostSummaryArray.forEach(item => {
            item.children = [];
        });
        // 转树
        let arrayTree = xeUtils.toArrayTree(otherProjectCostSummaryArray, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        // this.addLevelNumbers(arrayTree[0], '1');
        // 树结构,每一级的值向上一级汇总
        let otherProjectCostList = await this.sumChildNodeAmounts(arrayTree[0], je);
        let newList = xeUtils.toTreeArray(new Array(otherProjectCostList));

        //  因为建安工程费、建设其他费，更新计算概算费用代码
        await this.service.PreliminaryEstimate.gsEstimateCodeService.countEstimateCode({
            constructId: constructId
        });

        // 更新工程项目的建设其他费
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF, newList);
        return newList;
    }

    /**
     * 更新费用计算器中值的变化,并将变化更新到建设其他费中
     * @param otherProjectCostCalculators
     * @param constructId
     * @param otherProjectCost
     */
    async updateCalculatorToTree(otherProjectCostCalculators, constructId, otherProjectCost) {
        for (const node of otherProjectCostCalculators) {
            // 检查是否为普通的对象且不是 Map
            if (Object.prototype.toString.call(node.fields) === '[object Object]' && !(node.fields instanceof Map)) {
                node.fields = new Map(Object.entries(node.fields));
            }
            if (ObjectUtils.isNotEmpty(node.fields) && node.fields.size !== 0 && (ObjectUtils.isNotEmpty(node.fields.get('source')) || ObjectUtils.isNotEmpty(node.fields.get('weightSource')))) {
                try {
                    // 获取费用计算器费用代码
                    let moneyList = await this.service.PreliminaryEstimate.gsOtherProjectCostConfigService.getMoney({
                        constructId: constructId,
                        calculatorBasis: node.fields.get('calculatorBasis')
                    });
                    // 引用费用代码，重新给金额赋值
                    if (ObjectUtils.isNotEmpty(moneyList)) {
                        let source = node.fields.get('source');
                        if (ObjectUtils.isNotEmpty(source)) {
                            let sourceArray = source.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
                            let moneyTotal = ConvertUtil.deepCopy(node.fields.get('source'));
                            for (let i = 0; i < sourceArray.length; i++) {
                                let money = moneyList.find(item => item.name === sourceArray[i]);
                                if (ObjectUtils.isNotEmpty(money)) {
                                    moneyTotal = moneyTotal.replace(sourceArray[i], money.money);
                                }
                            }
                            node.fields.set('money', eval(moneyTotal));
                        }
                    }
                } catch (error) {
                    console.error("获取费用计算器费用代码失败...", error);
                }

                try {
                    // 查询三材汇总-钢筋数量
                    let args = {constructId: constructId};
                    const scGj = await this.service.PreliminaryEstimate.gsRcjCollectService.getScCountGJ(args);
                    if (ObjectUtils.isNotEmpty(scGj) && ObjectUtils.isNotEmpty(node.fields.get('weightSource'))) { // 三材钢筋数量：下拉框选择的   空：则是手动输入的
                        node.fields.set('weight', ObjectUtils.isNotEmpty(scGj[0].scCount) ? scGj[0].scCount : 0);
                    }
                } catch (error) {
                    console.error("获取三材汇总-钢筋数量失败...", error);
                }

                try {
                    // 获取费用计算器-计算过程
                    let otherProjectCostCalculatorFormula = await this.getOtherProjectCostCalculatorFormula(Object.fromEntries(node.fields));
                    let calculationProcess = otherProjectCostCalculatorFormula.calculationProcess;
                    node.fields.set('calculationProcess', calculationProcess);
                    node.fields.set('amount', calculationProcess.split('=').pop());  // NumberUtil.multiply(
                } catch (error) {
                    console.error("获取费用计算器-计算过程失败...", error);
                }

                // 费用计算器中值的变化更新到建设其他费中
                for (let enumKey in OtherProjectCostCategoryEnum) {
                    const enumValue = OtherProjectCostCategoryEnum[enumKey];
                    if (node.nameBackUp === enumValue.code && enumValue.desc === otherProjectCost.category && ObjectUtils.isNotEmpty(node.fields) && node.fields.size !== 0) {
                        otherProjectCost.amount = NumberUtil.multiply(node.fields.get('amount'), 10000);
                        otherProjectCost.calculationProcess = node.fields.get('calculationProcess');
                    }
                }
            }
            if (node.children && node.children.length) {
                await this.updateCalculatorToTree(node.children, constructId, otherProjectCost);
            }
        }
    }

    /**
     * 递归计算费用汇总
     * @param calculateFormula 计算基数
     * @param afterCalculateFormula  存放替换后的计算公式
     * @param codeFormulaMap 计算基数 <费用汇总费用代号,calculateFormula>
     * @param priceMap 费用代码<费用代码,amount>
     * @param variablesToReplace 拆分计算基数后的数组
     * @param codeRateMap
     * @param je  金额精度
     * @returns {*}
     */
    async recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap, je) {
        for (let variable of variablesToReplace) {
            if (priceMap.has(variable) && ObjectUtils.isNotEmpty(afterCalculateFormula)) {
                if (priceMap.get(variable) < 0) {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + NumberUtil.numberScale(priceMap.get(variable).je) + ')');
                } else {
                    afterCalculateFormula = afterCalculateFormula.replace(variable, NumberUtil.numberScale(priceMap.get(variable), je));
                }
            } else {
                if (isNaN(Number(variable))) {
                    if (codeFormulaMap.has(variable) && ObjectUtils.isNotEmpty(codeFormulaMap.get(variable)) && ObjectUtils.isNotEmpty(afterCalculateFormula)) {
                        let variablesToReplace1 = codeFormulaMap.get(variable).replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                        //说明当前行引用了费用代号 此处需要加上费率
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '(' + variable + ')/100*' + codeRateMap.get(variable));
                        afterCalculateFormula = afterCalculateFormula.replace(variable, codeFormulaMap.get(variable));
                        afterCalculateFormula = await this.recursionSummary(codeFormulaMap.get(variable), afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace1, codeRateMap, je);
                    } else {
                        afterCalculateFormula = afterCalculateFormula.replace(variable, '0');
                    }
                }
            }
        }
        return afterCalculateFormula;
    }

    /**
     * 修改建设其他费列表
     * @param args
     * @returns {ResponseData}
     */
    async updateOtherProjectCost(args) {
        let constructId = args.projectId;
        let param = args.otherProjectCost;
        let otherProjectCostList = args.otherProjectCostList;

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let price = precision.OTHER_PROJECT_COST.price;
        let je = precision.OTHER_PROJECT_COST.je;
        let quantity = precision.OTHER_PROJECT_COST.SUMMARY.quantity;
        let freeRate = precision.OTHER_PROJECT_COST.SUMMARY.freeRate;

        if (ObjectUtils.isEmpty(param)) {
            return ResponseData.fail('参数错误');
        }
        // 获取费用代码
        let otherProjectCostCodeArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_CODE);
        for (let i = 0; i < otherProjectCostCodeArray.length; i++) {
            if (param.code === otherProjectCostCodeArray[i].code) {
                return ResponseData.fail('当前费用代号与费用代码重复，请修改');
            }
        }
        // 获取费用汇总
        let otherProjectCostSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        for (let i = 0; i < otherProjectCostSummaryArray.length; i++) {
            if (param.sequenceNbr !== otherProjectCostSummaryArray[i].sequenceNbr && ObjectUtils.isNotEmpty(param.code)
                && ObjectUtils.isNotEmpty(otherProjectCostSummaryArray[i].code) && param.code.toLowerCase() === otherProjectCostSummaryArray[i].code.toLowerCase()) {
                return ResponseData.fail('当前费用代码已被使用');
            }
            if (otherProjectCostSummaryArray[i].category === param.category && otherProjectCostSummaryArray[i].sequenceNbr !== param.sequenceNbr
                && ObjectUtils.isNotEmpty(otherProjectCostSummaryArray[i].category)) {
                return ResponseData.fail('该费用已存在');
            }
        }

        let copyCode = param.code;
        let copyName = param.name;
        if (ObjectUtils.isEmpty(param.code)) {
            param.code = "※";
            param.name = "0";
        }
        let codeCalculationMethodMap = new Map(); //1费用代号、代码存放<费用代号、代码,calculationMethod>
        let codePriceMap = new Map();//1费用代号、代码存放<费用代号、代码,amount>
        let codeFormulaMap = new Map();//计算基数
        let codeInstructionsMap = new Map();//基数说明、费用名称<费用代号、代码,priceDescription>
        let codeRateMap = new Map();
        let codeNameMap = new Map();
        let currentId = param.sequenceNbr;
        // let code = param.code;  // 数据备份
        // let calculateFormula = param.price; // 数据备份
        let priceChangeArray = [];
        if (ObjectUtils.isNotEmpty(param.sequenceNbr)) {
            // 修改当前行
            let otherProjectCostSummary = otherProjectCostSummaryArray.find(item => item.sequenceNbr === param.sequenceNbr);
            if (otherProjectCostSummary == null) {
                return ResponseData.fail('该行不存在');
            }

            // 获取所有的Map集合
            await this.getAllCodeFormulaPriceMap(constructId, otherProjectCostCodeArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, otherProjectCostSummaryArray);
            let otherProjectCostSummaryCodeMap = [];
            const parentCodeMap = await this.getParentCodeMap(otherProjectCostSummaryArray, otherProjectCostSummary.parentId, otherProjectCostSummaryCodeMap);
            if (ObjectUtils.isNotEmpty(param.price) && param.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2) {
                // if (ObjectUtils.isEmpty(param.quantity)) {
                //     param.quantity = "100";
                // }

                let lowerParamCalculateFormula = ObjectUtils.isNotEmpty(param.price) && isNaN(Number(param.price)) ? param.price.toLowerCase() : param.price;  // 转小写
                if (isNaN(Number(lowerParamCalculateFormula))) {
                    // param.code = param.code.toLowerCase();  // 转化小写
                    // param.price = param.price.toLowerCase();  // 转化小写
                    let codeList = lowerParamCalculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
                    if (ObjectUtils.isNotEmpty(codeList)) {
                        for (let i = 0; i < codeList.length; i++) {
                            // 判断公式中的费用代码是不是引用了父级的
                            if (parentCodeMap.includes(codeList[i])) {
                                return ResponseData.fail("公式存在循环引用，请检查并修改");
                            }
                            // 判断公式中引用的费用代码是不是引用的列表不存在的
                            let keys = [...codeNameMap.keys()].map(function (item) {
                                if (ObjectUtils.isNotEmpty(item)) {
                                    return item.toLowerCase();
                                }
                            });
                            if (!keys.includes(codeList[i]) && isNaN(Number(codeList[i]))) {
                                return ResponseData.fail("费用代码输入有误！");
                            }
                        }
                    }
                }
            }

            // 定义四则运算符数组
            const arithmeticOperators = ['+', '-', '*', '/'];
            // 检查传入的参数是否在四则运算符数组中
            if (arithmeticOperators.includes(param.price)) {
                // 当选择“单价*数量”时，单价列仅能直接输入数值，若此时输入四则运算时，计算方式将自动调整为“计算基数*费率”
                param.calculationMethod = OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2;
            }
            if (ObjectUtils.isNotEmpty(param.category)) {
                // 移除权限：计价文件查询 = 3
                param.permission.splice(2, 0, OtherProjectCostOptionMenuConstants.qtfPolicyDocumentMenu);
                param.permission = [...new Set(param.permission)]; // 去重
            } else {
                // 移除权限：计价文件查询 = 3
                param.permission = param.permission.filter(item => item !== OtherProjectCostOptionMenuConstants.qtfPolicyDocumentMenu);
            }

            // 当修改更换计算方式时，清空单价/计算基数和 数量/费率
            if (param.calculationMethod !== otherProjectCostSummary.calculationMethod) {
                param.price = "";  // 必须是空字符串，而不是null,否则影响后面的方法
                param.quantity = null;
                // param.priceDescription = null;  // 切换费用类型后，计算方式变为手动输入，计算结果不变，计算过程变成结果的数字
                // param.amount = 0;
            }

            // 修改计算方式时
            if (ObjectUtils.isNotEmpty(param.calculationMethod) && param.calculationMethod !== otherProjectCostSummary.calculationMethod) {
                param.amount = 0;
                param.priceDescription = null;
                param.calculationProcess = param.amount;
            }
            if (ObjectUtils.isEmpty(param.price) && param.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2) {
                param.price = null;
            }
            // 当计算方式为费用计算器的时，当改变费用类别时，修改计算方式为手动输入，同时其他数据全部保留，除计算过程显示成金额
            if (param.category !== otherProjectCostSummary.category) {
                if (otherProjectCostSummary.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_3) {
                    if (otherProjectCostSummary.amount > 0) {
                        param.calculationMethod = OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_4;
                        param.amount = otherProjectCostSummary.amount;
                        param.calculationProcess = param.amount;
                        // param.fields = null;
                    } else {
                        param.calculationMethod = OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_4;
                        param.calculationProcess = param.amount;
                    }
                } else {
                    // 当修改费用类别时，清空计算过程和金额
                    param.amount = 0;
                    param.calculationProcess = null;
                }
            }

            // 判断code
            if (ObjectUtils.isNotEmpty(otherProjectCostSummary.code) && param.code !== otherProjectCostSummary.code) {
                // 如果修改了code  那么就要去看这个老的code是不有地方引用了他  如果有引用  那么不能修改这个code  后面产品又说改了code其他引用了code的地方也要同步变更...
                await this.inspectionCode(param.code, otherProjectCostSummary.code, codeFormulaMap);
            }
            // 判断name
            if (ObjectUtils.isNotEmpty(otherProjectCostSummary.name) && param.name !== otherProjectCostSummary.name) {
                // 如果修改了name  那么就要去看这个老的name是不有地方引用了他  如果有引用  那么不能修改这个name  后面产品又说改了name其他引用了name的地方也要同步变更...
                await this.inspectionName(param.name, otherProjectCostSummary.name, codeInstructionsMap);
            }
            // 更新是否计算标识
            if (ObjectUtils.isNotEmpty(otherProjectCostList)) {
                otherProjectCostSummaryArray = await this.inspectionIfCalculate(otherProjectCostSummaryArray, otherProjectCostList);
            }

            // 判断 计算基数和费率修改    计算方式为 2.“计算基数*费率”、
            if (param.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2
                && (param.price !== otherProjectCostSummary.price || (param.quantity !== otherProjectCostSummary.quantity && ObjectUtils.isNotEmpty(param.price)))) {
                // 如果参数传来的计算公式或者费率不一样  那么说明修改了计算公式或者费率   就需要对计算公式进行验证并计算结果
                let responseData = await this.handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap);
                if (!ObjectUtils.isEmpty(responseData)) {
                    return ResponseData.fail(responseData.message);
                }

                // param.code = code; // 还原数据备份
                // param.price = calculateFormula; // 还原数据备份
                // 把本次计算的结果存入map  留待后续计算使用
                codePriceMap.set(param.code, NumberUtil.numberScale(param.amount, je));
                codeFormulaMap.set(param.code, param.price);
                codeInstructionsMap.set(param.code, param.priceDescription);
                codeCalculationMethodMap.set(param.code, param.calculationMethod);
                // 如果公式进行了修改  那么需要对引用了这个条公式对应的code的所有公式重新计算，并且对扩散影响的所有公式都需要重新计算
                await this.handleCodePriceChange(param.code, codePriceMap, codeFormulaMap, codeRateMap);
            } else {
                if (param.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_1) {
                    if (ObjectUtils.isEmpty(param.price)) {
                        param.price = null;
                    }
                    if (ObjectUtils.isEmpty(param.quantity)) {
                        param.quantity = null;
                    } else {
                        // 当数量是计算表达式时
                        try {
                            // 使用 eval() 计算表达式的值
                            param.quantity = NumberUtil.numberScale(eval(param.quantity), quantity);
                        } catch (error) {
                            // 处理可能出现的错误，例如无效的表达式
                            return ResponseData.fail("输入表达式错误！");
                        }
                    }
                    param.amount = NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(parseFloat(param.price), price), NumberUtil.numberScale(parseFloat(param.quantity), quantity)), je);
                    if (ObjectUtils.isNotEmpty(param.price) && ObjectUtils.isNotEmpty(param.quantity)) {
                        param.calculationProcess = param.price + "*" + param.quantity;
                    } else {
                        param.calculationProcess = null;
                    }
                }
                if (param.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_4) {
                    param.calculationProcess = param.amount;
                }
                // 把本次计算的结果存入map  留待后续计算使用
                codePriceMap.set(param.code, NumberUtil.numberScale(param.amount, je));
                codeCalculationMethodMap.set(param.code, param.calculationMethod);
            }
            // 根据上面这步的计算 得出有哪些数据需要更新
            for (let i = 0; i < otherProjectCostSummaryArray.length; i++) {
                let item = otherProjectCostSummaryArray[i];
                let amount = codePriceMap.get(item.code);
                let formula = codeFormulaMap.get(item.code);
                let priceDescription = codeInstructionsMap.get(item.code);
                let calculationMethod = codeCalculationMethodMap.get(item.code);

                let updateOtherProjectCost = new GsOtherProjectCost();
                updateOtherProjectCost.sequenceNbr = item.sequenceNbr;
                let flag = false;
                if (ObjectUtils.isNotEmpty(amount) && amount !== item.amount &&
                    (ObjectUtils.isNotEmpty(item.code) && updateOtherProjectCost.sequenceNbr === item.sequenceNbr)) { // 避免不同方式，无费用代号，公用金额
                    updateOtherProjectCost.amount = amount;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(formula) && formula !== item.price) {
                    updateOtherProjectCost.price = formula;
                    flag = true;
                }
                // else {
                //     updateOtherProjectCost.amount = 0;
                //     flag = true;
                // }
                if (ObjectUtils.isNotEmpty(priceDescription) && priceDescription !== item.priceDescription) {
                    updateOtherProjectCost.priceDescription = priceDescription;
                    flag = true;
                }
                if (ObjectUtils.isNotEmpty(calculationMethod) && calculationMethod !== item.calculationMethod) {
                    updateOtherProjectCost.calculationMethod = calculationMethod;
                    // if (calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_3
                    //     || calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_4) {
                    //     updateUnitCostSummary.price = null;
                    //     updateUnitCostSummary.priceDescription = null;
                    // }
                    flag = true;
                }
                if (flag === true) {
                    priceChangeArray.push(updateOtherProjectCost);
                }
            }

        } else {
            // 新增
            let find = otherProjectCostSummaryArray.find(item => item.code === param.code);
            if (ObjectUtils.isNotEmpty(find)) {
                return ResponseData.fail('当前code已存在');
            }
            // 获取统一的map数据
            await this.getAllCodeFormulaPriceMap(constructId, otherProjectCostCodeArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, otherProjectCostSummaryArray);
            // 处理公式校验和计算
            await this.handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap);
        }

        // args.otherProjectCost.code = code;  // 还原数据备份
        // args.otherProjectCost.price = calculateFormula;  // 还原数据备份
        if (ObjectUtils.isNotEmpty(param.code) && param.code === '※') {
            args.otherProjectCost.code = copyCode;
            args.otherProjectCost.name = copyName;
        }
        // 进行数据更新
        await this.setUnitCostSummaryData(args, otherProjectCostSummaryArray, priceChangeArray);

        // 调用计算建设其他费
        let newList = await this.countOtherProjectCost(constructId, otherProjectCostCodeArray, otherProjectCostSummaryArray);
        //
        // otherProjectCostSummaryArray.forEach(item => {
        //     item.children = [];
        // });
        // // 转树
        // let arrayTree = xeUtils.toArrayTree(otherProjectCostSummaryArray, {
        //     key: 'sequenceNbr',
        //     parentKey: 'parentId',
        // });
        //
        // // 树结构,每一级的值向上一级汇总
        // let otherProjectCostList = this.sumChildNodeAmounts(arrayTree[0]);
        // let newList = xeUtils.toTreeArray(new Array(otherProjectCostList));

        // ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF, newList);
        return ResponseData.success(newList);
    }


    /**
     * 递归获取array中的指定id数据行的等于父级id的所有父级、父级的父级、父级的父级的父级的集合
     *
     * @param otherProjectCostSummaryArray
     * @param parentId
     * @param otherProjectCostSummaryCodeMap
     * @returns {*}
     */
    async getParentCodeMap(otherProjectCostSummaryArray, parentId, otherProjectCostSummaryCodeMap) {
        for (let i = 0; i < otherProjectCostSummaryArray.length; i++) {
            const item = otherProjectCostSummaryArray[i];
            // 如果当前项的id与目标id相同
            if (item.sequenceNbr === parentId && ObjectUtils.isNotEmpty(item.code)) {
                // 将当前项的父id添加到结果数组中
                otherProjectCostSummaryCodeMap.push(item.code.toLowerCase());
                // 递归地在数据集中查找当前项的父级
                await this.getParentCodeMap(otherProjectCostSummaryArray, item.parentId, otherProjectCostSummaryCodeMap);
                break;
            }
            // // 如果当前项有子节点，递归地在子节点中查找
            // if (item.children && item.children.length > 0) {
            //     this.getParentCodeMap(item.children, parentId, otherProjectCostSummaryCodeMap);
            // }
        }
        return otherProjectCostSummaryCodeMap;
    }

    /**
     * 修改code - 修改计算基数
     * @param newCode
     * @param oldCode
     * @param codeFormulaMap
     */
    async inspectionCode(newCode, oldCode, codeFormulaMap) {
        let lowerOldCode = oldCode.toLowerCase();
        for (let [key, value] of codeFormulaMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let codeList = value.toLowerCase().split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(codeList) && this.stringInArray(codeList, lowerOldCode)) {
                    let reg = new RegExp("\\b" + lowerOldCode + "\\b", "gi")
                    let replace = value.replaceAll(reg, newCode);
                    codeFormulaMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 修改name - 修改基数说明
     * @param newName
     * @param oldName
     * @param codeInstructionsMap
     */
    async inspectionName(newName, oldName, codeInstructionsMap) {
        for (let [key, value] of codeInstructionsMap) {
            if (ObjectUtils.isNotEmpty(value)) {
                let nameList = value.toLowerCase().split(/[+\-*/]/);
                if (!ObjectUtils.isEmpty(nameList) && this.stringInArray(nameList, oldName)) {
                    let replace = value.replaceAll(oldName, newName);
                    codeInstructionsMap.set(key, replace);
                }
            }
        }
    }

    /**
     * 勾选或者不勾选 - 修改勾选状态
     * @param otherProjectCostSummaryArray
     * @param otherProjectCostList
     */
    async inspectionIfCalculate(otherProjectCostSummaryArray, otherProjectCostList) {
        // id，是否勾选Map
        const otherProjectCostMap = otherProjectCostList.reduce((accumulator, current) => {
            if (accumulator.has(current.sequenceNbr)) {
                // 如果已经存在该id，则将其添加到现有值的数组中
                const ifCalculates = accumulator.get(current.sequenceNbr);
                if (Array.isArray(ifCalculates)) {
                    ifCalculates.push(current.ifCalculate);
                } else {
                    accumulator.set(current.sequenceNbr, [ifCalculates, current.ifCalculate]);
                }
            } else {
                // 如果不存在，则设置新值
                accumulator.set(current.sequenceNbr, current.ifCalculate);
            }
            return accumulator;
        }, new Map());

        // 给是否勾选赋值
        otherProjectCostSummaryArray.forEach(item => {
            if (otherProjectCostMap.has(item.sequenceNbr)) {
                item.ifCalculate = otherProjectCostMap.get(item.sequenceNbr);
            }
        });
        return otherProjectCostSummaryArray;
    }


    /**
     * 获取单位工程的所有已有的费用代码基数和对应的价格
     * @param constructId
     * @param otherProjectCostCodeArray
     * @param currentId
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeInstructionsMap
     * @param codeRateMap
     * @param codeNameMap
     * @param otherProjectCostSummaryArray
     */
    async getAllCodeFormulaPriceMap(constructId, otherProjectCostCodeArray, currentId, codePriceMap, codeFormulaMap, codeInstructionsMap, codeRateMap, codeNameMap, otherProjectCostSummaryArray) {

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let price = precision.OTHER_PROJECT_COST.price;
        let je = precision.OTHER_PROJECT_COST.je;
        let freeRate = precision.OTHER_PROJECT_COST.SUMMARY.freeRate;

        if (ObjectUtils.isNotEmpty(otherProjectCostCodeArray)) {
            for (let i = 0; i < otherProjectCostCodeArray.length; i++) {
                let otherProjectCostCode = otherProjectCostCodeArray[i];
                let code = otherProjectCostCode.code;
                codePriceMap.set(code, NumberUtil.numberScale(otherProjectCostCode.price, je));
                codeFormulaMap.set(code, otherProjectCostCode.code);
                codeInstructionsMap.set(code, otherProjectCostCode.name);
                codeNameMap.set(code, otherProjectCostCode.name);
            }
        }
        if (ObjectUtils.isNotEmpty(otherProjectCostSummaryArray)) {
            for (let i = 0; i < otherProjectCostSummaryArray.length; i++) {
                let otherProjectCost = otherProjectCostSummaryArray[i];
                let code = otherProjectCost.code;
                if (!ObjectUtils.isEmpty(currentId) && currentId === otherProjectCost.sequenceNbr) {
                    continue;
                }
                codePriceMap.set(code, NumberUtil.numberScale(otherProjectCost.amount, je));
                if (ObjectUtils.isNotEmpty(code) && otherProjectCost.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2) {
                    codeFormulaMap.set(code, otherProjectCost.price);
                    codeInstructionsMap.set(code, otherProjectCost.priceDescription);
                }
                if (ObjectUtils.isNotEmpty(code)) {
                    codeNameMap.set(code, otherProjectCost.name);
                }
                if (ObjectUtils.isNotEmpty(otherProjectCost.quantity)) {
                    codeRateMap.set(code, NumberUtil.numberScale(otherProjectCost.quantity, freeRate));
                }
            }
        }
    }

    /**
     * 费用代码修改 - 计算基数修改
     * @param calculateFormula
     * @param codePriceMap
     * @returns {any}
     */
    async doCalculator(calculateFormula, codePriceMap) {
        if (ObjectUtils.isEmpty(calculateFormula)) {
            return calculateFormula;
        }
        // 分解字符串成表达式和变量名
        const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/).filter(item => ObjectUtils.isNotEmpty(item));
        //存放替换后的计算公式
        let afterCalculateFormula = calculateFormula;

        // 创建一个新的 Map 来存储小写的键和对应的值
        const keysLowercase = new Map();
        // 使用 forEach 遍历原始的 Map
        codePriceMap.forEach((value, key) => {
            // 将键转换为小写
            const lowerCaseKey = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            // 将新的键值对添加到新的 Map 中
            keysLowercase.set(lowerCaseKey, value);
        });

        //替换费用代码和费用代号
        for (let variable of variablesToReplace) {
            let variableLowerCase = variable.toLowerCase(); // 转小写
            if (keysLowercase.has(variableLowerCase)) {
                afterCalculateFormula = afterCalculateFormula.replace(variable, keysLowercase.get(variableLowerCase));
            }
        }

        let flag = this.isValidExpression(afterCalculateFormula);
        if (!flag) {
            throw new Error("表达式有误，请重新编辑！");
        }
        // 匹配表达式中的※为0，进行检验
        afterCalculateFormula = afterCalculateFormula.replaceAll("※", "0");
        return eval(afterCalculateFormula);
    }

    /**
     * 效验取费基数：A+B+V+
     * @param expression
     * @returns {boolean}
     */
    isValidExpression(expression) {
        // 匹配表达式中的※为0，进行检验
        expression = expression.replaceAll("※", "0");
        // 匹配四则运算表达式的正则表达式
        const regex = /^[\d\+\-\*\/\(\)\.]+$/;

        // 检查表达式是否匹配正则表达式
        if (!regex.test(expression)) {
            return false;
        }

        try {
            // 使用 eval() 函数计算表达式的值
            eval(expression);
            return true;
        } catch (e) {
            // 如果表达式有语法错误，eval() 函数会抛出异常
            return false;
        }
    }

    /**
     * 计算基数修改 - 基数说明修改
     * @param formula
     * @param codeNameMap
     * @returns {string|*}
     */
    async getFormulaInstructions(formula, codeNameMap) {
        if (ObjectUtils.isEmpty(formula)) {
            return formula;
        }
        // codeNameMap的key转小写
        let codeNameMapLowerCase = new Map();
        for (let [key, value] of codeNameMap) {
            let keyNew = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            codeNameMapLowerCase.set(keyNew, value);
        }
        // 把公式进行分割
        formula = formula.toLowerCase();
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (!codeList.length) {
            return "";
        }
        for (let i = 0; i < codeList.length; i++) {
            let code = codeList[i];
            let instruction = codeNameMapLowerCase.get(code);
            if (ObjectUtils.isNotEmpty(instruction) && instruction.trim()) {
                formula = formula.replace(code, instruction);
            } else {
                if (isNaN(Number(code))) { // code不是一个数字
                    formula = formula.replace(code, "");  // formula.replace(code, "") todo 解决引用有费用代号无名称，导致的基数说明错误问题   formula.replace("+" + code, "");
                } else {
                    formula = formula.replace(code, code);
                }
            }
        }
        return formula;
    }

    /**
     * 处理公式修改逻辑
     * @param constructId
     * @param param
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeNameMap
     */
    async handleUpdateCalculateFormula(constructId, param, codePriceMap, codeFormulaMap, codeNameMap) {

        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(constructId);
        let price = precision.OTHER_PROJECT_COST.price;
        let je = precision.OTHER_PROJECT_COST.je;
        let freeRate = precision.OTHER_PROJECT_COST.SUMMARY.freeRate;
        let quantity = precision.OTHER_PROJECT_COST.SUMMARY.quantity;

        // 当计算基数删除后，金额等字段未清空
        if (!isNaN(Number(param.price))) {
            let res;

            // if (param.quantity && param.price) {
            //     res = parseFloat((param.price * param.quantity / 100).toFixed(3));
            // } else {
            //     res = parseFloat((param.price * 100 / 100).toFixed(3));
            // }
            if (param.quantity && param.price) {
                res = NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(param.price, price), NumberUtil.numberScale(param.quantity / 100, quantity)), je);
            } else {
                res = NumberUtil.numberScale(param.price, je);
            }
            param.amount = Math.round(res * 100) / 100;
            param.priceDescription = param.price;
            if (ObjectUtils.isNotEmpty(param.priceDescription) && ObjectUtils.isNotEmpty(param.quantity)) {
                param.calculationProcess = param.priceDescription + "*" + param.quantity + "%";
            }
            return;
        }
        // 对本次新增或者修改进行正确性检测
        let set = new Set();
        // set.add(param.code);
        try {
            await this.doInspection(set, param.price, codeFormulaMap, param.code);

            let res;
            if (ObjectUtils.isEmpty(param.price)) {
                res = null;
            } else {
                // 如果没有抛出异常  说明检查通过了
                res = await this.doCalculator(param.price, codePriceMap);
            }
            // if (param.quantity) {
            //     res = parseFloat((res * param.quantity / 100).toFixed(3));
            // } else {
            //     res = parseFloat((res * 100 / 100).toFixed(3));
            // }

            if (param.quantity) {
                res = NumberUtil.numberScale(parseFloat(res * param.quantity / 100), freeRate);
            } else {
                res = parseFloat(res);
            }

            param.amount = Math.round(res * 100) / 100;
            param.priceDescription = await this.getFormulaInstructions(param.price, codeNameMap);
            if (ObjectUtils.isNotEmpty(param.quantity)) {
                param.calculationProcess = "(" + param.priceDescription + ")*" + param.quantity + "%";
            } else {
                param.calculationProcess = param.priceDescription;
            }
        } catch (e) {
            return ResponseData.fail(e.message);
        }
    }

    /**
     * 获取所有引用code
     * @param calculateFormula
     * @param codeFormulaMap
     * @param set
     */
    async getCodes(calculateFormula, codeFormulaMap, set) {
        if (codeFormulaMap.has(calculateFormula) && ObjectUtils.isNotEmpty(codeFormulaMap.get(calculateFormula)) && isNaN(Number(codeFormulaMap.get(calculateFormula)))) {
            // 获取公式calculateFormula中的code   获取公式所有的变量 y+L7*费用代号34-B_9+y哈 y、L7、费用代号34、B_9、y哈
            let codeList = codeFormulaMap.get(calculateFormula).replace(/[+\-*/]/g, ',').split(',');
            if (ObjectUtils.isNotEmpty(codeList)) {
                if (codeList.length === 1 && codeList[0] === calculateFormula) {
                    return;
                }
                for (let i = 0; i < codeList.length; i++) {
                    let code = codeList[i];
                    if (code !== "※") {
                        set.add(code);
                    }
                    await this.getCodes(code, codeFormulaMap, set);
                }
            }
        }
    }

    /**
     * 对公式的正确性进行检查 检查是否有错误引用或者循环引用
     * ps：
     * 这个有个简单的做法  就是从修改的公式code开始  把每一层的每一个元素都分解到一个二叉树里面
     * 如果这个二叉树的任何一条从顶层到底层的分支中出现重复的元素  那就说明这个公式存在循环引用   但是这样做的话错误提示不明确
     * @param codes
     * @param formula
     * @param codeFormulaMap
     * @param code
     */
    async doInspection(codes, formula, codeFormulaMap, code) {

        let formulaLowerCase = ObjectUtils.isNotEmpty(formula) ? formula.toLowerCase() : formula;
        let codeLowerCase = ObjectUtils.isNotEmpty(code) ? code.toLowerCase() : code;

        if (ObjectUtils.isEmpty(formula)) {
            return;
        }
        if (formulaLowerCase === codeLowerCase) {
            throw new Error("公式存在循环引用，请检查并修改");
        }

        // 创建一个新的 Map 来存储小写的键和对应的值
        const codeFormulaMapLowercase = new Map();
        // 使用 forEach 遍历原始的 Map
        codeFormulaMap.forEach((value, key) => {
            // 将键转换为小写
            const lowerCaseKey = ObjectUtils.isNotEmpty(key) ? key.toLowerCase() : key;
            const lowerCaseValue = ObjectUtils.isNotEmpty(value) ? value.toLowerCase() : value;
            // 将新的键值对添加到新的 Map 中
            codeFormulaMapLowercase.set(lowerCaseKey, lowerCaseValue);
        });

        // 获取应用取费基数下所有的子code
        await this.getCodes(formulaLowerCase, codeFormulaMapLowercase, codes);
        if (codes.has(codeLowerCase)) {
            throw new Error("公式存在循环引用，请检查并修改");
        }
        // 根据 加减乘除 分割计算公式
        let codeList = formula.split(/[\+\-\*\/\(\)]+/);
        if (codeList.length === 0) {
            throw new Error("运算公式格式错误，请检查并修改");
        }
        // if (codeList.length === 1) {
        //     let codeFormula = codeFormulaMap.get(codeList[0].replace(/\s/g, ''));
        //     if (ObjectUtils.isNotEmpty(codeFormula)) {  // ObjectUtils.isNotEmpty(codeFormula)
        //         // 在map里面没找到  那就是引用了一个不存在的基数code
        //         //判断是否为数字
        //         if (isNaN(Number(codeList[0]))) {
        //             throw new Error("公式存在未知引用，请检查并修改");
        //         }
        //     }
        //     return;
        // }
        // for (let i = 0; i < codeList.length; i++) {
        //     const c = codeList[i];
        //     if (codes.has(c)) {
        //         // 说明是自己的公式引用了自己
        //         throw new Error('公式存在循环引用，请检查并修改');
        //     }
        //     let codeFormula = codeFormulaMap.get(c.replace(/\s/g, ''));
        //     if (!codeFormula) {
        //         if (ObjectUtils.isEmpty(codeFormula)) {  // E默认没有计算基数，会导致引用E的时候报错
        //             codeFormula = 0;
        //         } else {
        //             // 在map里面没找到  那就是引用了一个不存在的基数code
        //             if (isNaN(Number(c))) {
        //                 throw new Error("公式存在未知引用，请检查并修改");
        //             } else {
        //                 codeFormula = c;
        //             }
        //             let newCodes = new Set(codes);
        //             if (c !== codeFormula) {
        //                 newCodes.add(c);
        //             }
        //             this.doInspection(newCodes, codeFormula, codeFormulaMap);
        //         }
        //     }
        // }
    }

    /**
     * 计算费率
     * @param code
     * @param codePriceMap
     * @param codeFormulaMap
     * @param codeRateMap
     */
    async handleCodePriceChange(code, codePriceMap, codeFormulaMap, codeRateMap) {
        for (let [key, value] of codeFormulaMap.entries()) {
            if (ObjectUtils.isNotEmpty(value) && isNaN(Number(value))) {
                // 对公式进行分解
                let codeList = value.split(/[\+\-\*\/\(\)]+/);
                if (codeList.length > 1) {
                    if (codeList.includes(code)) {
                        let res = this.doCalculator(value, codePriceMap);
                        let quantity = codeRateMap.get(key);
                        if (quantity) {
                            res = parseFloat((res * quantity / 100));
                            codePriceMap.set(key, Math.round(res * 100) / 100)
                        } else {
                            codePriceMap.set(key, res);
                        }
                        await this.handleCodePriceChange(key, codePriceMap, codeFormulaMap, codeRateMap);
                    }
                }
            }
        }
    }

    /**
     * 设置数据
     * @param args
     * @param otherProjectCostSummaryArray
     * @param priceChangeArray
     */
    async setUnitCostSummaryData(args, otherProjectCostSummaryArray, priceChangeArray) {
        //新增的数据
        let newOtherProjectCost = args.otherProjectCost;
        if (ObjectUtils.isEmpty(newOtherProjectCost.sequenceNbr)) {
            //新增
            // newUnitCostSummary.sequenceNbr = Snowflake.nextId();
            // for (let i = otherProjectCostSummaryArray.length - 1; i >= 0; i--) {
            //     const item = otherProjectCostSummaryArray[i];
            //     if (item.sortNum >= newUnitCostSummary.sortNum) {
            //         item.sortNum += 1;
            //     }
            // }
            otherProjectCostSummaryArray.push(newOtherProjectCost);
        } else {
            //修改
            for (let i = 0; i < otherProjectCostSummaryArray.length; i++) {
                let element = otherProjectCostSummaryArray[i];
                if (element.sequenceNbr === newOtherProjectCost.sequenceNbr) {
                    otherProjectCostSummaryArray[i] = newOtherProjectCost;
                }
            }
        }
        //更新修改后的金额
        if (!ObjectUtils.isEmpty(priceChangeArray)) {
            for (let i = 0; i < priceChangeArray.length; i++) {
                let item = priceChangeArray[i];
                for (let j = 0; j < otherProjectCostSummaryArray.length; j++) {
                    let element = otherProjectCostSummaryArray[j];
                    if (element.sequenceNbr === item.sequenceNbr) {
                        if (!ObjectUtils.isEmpty(item.amount)) {
                            otherProjectCostSummaryArray[j].amount = item.amount;
                        }
                        if (!ObjectUtils.isEmpty(item.price)) {
                            otherProjectCostSummaryArray[j].price = item.price;
                        }
                        if (!ObjectUtils.isEmpty(item.priceDescription)) {
                            otherProjectCostSummaryArray[j].priceDescription = item.priceDescription;
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取建设其他费的计算器左侧树
     * @param args
     */
    async getOtherProjectCostCalculators(args) {
        let {projectId} = args;
        // 获取建设其他费费用计算器
        return ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_JSQ);
    }

    /**
     * 修改建设其他费的计算器左侧树
     * @param args
     */
    async updateOtherProjectCostCalculatorTree(args) {
        let {projectId, calculatorBasis, amount, fields, calculationProcess} = args;

        let category;
        for (let enumKey in OtherProjectCostCategoryEnum) {
            if (OtherProjectCostCategoryEnum[enumKey].code === calculatorBasis) {
                category = OtherProjectCostCategoryEnum[enumKey].desc;
            }
        }

        // 检查是否为普通的对象且不是 Map
        if (ObjectUtils.isNotEmpty(fields)) {
            if (Object.prototype.toString.call(fields) === '[object Object]' && !(fields instanceof Map)) {
                fields = new Map(Object.entries(fields));
            }
            fields.set("amount", amount);
            fields.set("calculationProcess", calculationProcess);
        }

        // 获取建设其他费费用计算器
        let otherProjectCostCalculators = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_JSQ);

        // 给费用计算器左侧树种添加参数
        this.findCalculatorToTree(otherProjectCostCalculators, calculatorBasis, fields);

        // 修改建设其他费费用计算器
        ProjectDomain.getDomain(projectId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF_JSQ, otherProjectCostCalculators);
        return otherProjectCostCalculators;
    }

    /**
     * 获取建设其他费计算器依据
     * @param args
     * @returns {any[]}
     */
    async getOtherProjectCostCalculatorBasis(args) {
        let calculatorBasis = args.calculatorBasis;
        let zbdlftyCalculationBasis, zbdlfhbCalculationBasis;
        switch (calculatorBasis) {
            case "项目建设管理费":
                let xmjsglfCalculationBasis = new XmjsglfCalculationBasis();
                return xmjsglfCalculationBasis.getCalculationBasis(calculatorBasis);
            case "招标代理费-招标代理费（通用）-工程招标代理费":
                zbdlftyCalculationBasis = new ZbdlftyCalculationBasis();
                return zbdlftyCalculationBasis.getCalculationBasis(calculatorBasis);
            case "招标代理费-招标代理费（通用）-货物招标代理费":
                zbdlftyCalculationBasis = new ZbdlftyCalculationBasis();
                return zbdlftyCalculationBasis.getCalculationBasis(calculatorBasis);
            case "招标代理费-招标代理费（通用）-服务招标代理费":
                zbdlftyCalculationBasis = new ZbdlftyCalculationBasis();
                return zbdlftyCalculationBasis.getCalculationBasis(calculatorBasis);
            case "招标代理费-招标代理费（河北）-工程招标代理费（河北）":
                zbdlfhbCalculationBasis = new ZbdlfhbCalculationBasis();
                return zbdlfhbCalculationBasis.getCalculationBasis(calculatorBasis);
            case "招标代理费-招标代理费（河北）-货物招标代理费（河北）":
                zbdlfhbCalculationBasis = new ZbdlfhbCalculationBasis();
                return zbdlfhbCalculationBasis.getCalculationBasis(calculatorBasis);
            case "招标代理费-招标代理费（河北）-服务招标代理费（河北）":
                zbdlfhbCalculationBasis = new ZbdlfhbCalculationBasis();
                return zbdlfhbCalculationBasis.getCalculationBasis(calculatorBasis);
            case "工程设计费-工程设计费":
            case "工程设计费-总体设计费":
            case "工程设计费-施工图预算编制费":
            case "工程设计费-竣工图编制费":
            case '工程设计费-岩土工程设计费':
                let gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis = new GcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis();
                return gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis.getCalculationBasis(calculatorBasis);
            case "工程监理费-工程监理费（通用）":
                let gcjlfCalculationBasis = new GcjlfCalculationBasis();
                return gcjlfCalculationBasis.getCalculationBasis(calculatorBasis);
            case "工程监理费-工程监理费（河北）":
                let gcjlfhbCalculationBasis = new GcjlfhbCalculationBasis();
                return gcjlfhbCalculationBasis.getCalculationBasis(calculatorBasis);
            case "环境影响咨询服务费-编制环境影响报告书（含大纲）":
            case "环境影响咨询服务费-编制环境影响报告表":
            case "环境影响咨询服务费-评估环境影响报告书（含大纲）":
            case "环境影响咨询服务费-评估环境影响报告表":
                let bzhjbgsAndbgbCalculationBasis = new BzhjbgsAndbgbCalculationBasis();
                return bzhjbgsAndbgbCalculationBasis.getCalculationBasis(calculatorBasis);
            case '建设项目前期工作咨询费-编制项目建议书':
                return this.getCalculationBasis(calculatorBasis);
            case '建设项目前期工作咨询费-编制可行性研究报告':
                return this.getCalculationBasis(calculatorBasis);
            case '建设项目前期工作咨询费-评估项目建议书':
                return this.getCalculationBasis(calculatorBasis);
            case '建设项目前期工作咨询费-评估可行性研究报告':
                return this.getCalculationBasis(calculatorBasis);
            case '造价咨询费-预算编制咨询费':
                return this.getCalculationBasis(calculatorBasis);
            case '造价咨询费-工程量清单编制咨询费':
                return this.getCalculationBasis(calculatorBasis);
            case '造价咨询费-招标控制价编制咨询费':
                return this.getCalculationBasis(calculatorBasis);
            case '造价咨询费-施工阶段造价咨询费':
                return this.getCalculationBasis(calculatorBasis);
            case '造价咨询费-结算审核咨询费':
                return this.getCalculationBasis(calculatorBasis);
            case '水土保持费-水土保持方案编制费':
                return this.getCalculationBasis(calculatorBasis);
            case '水土保持费-水土保持监理费':
                return this.getCalculationBasis(calculatorBasis);
            case '水土保持费-技术评估报告编制费':
                return this.getCalculationBasis(calculatorBasis);
            case '水土保持费-技术咨询服务费':
                return this.getCalculationBasis(calculatorBasis);
            case '水土保持补偿费':
                return this.getCalculationBasis(calculatorBasis);
            case '社会稳定风险评估费-编制社会稳定风险评估报告':
                return this.getCalculationBasis(calculatorBasis);
            case '社会稳定风险评估费-评价社会稳定风险评估报告':
                return this.getCalculationBasis(calculatorBasis);
            case '建设期贷款利息-等额本金':
                return this.getCalculationBasis(calculatorBasis);
            case '建设期贷款利息-等额本息':
                return this.getCalculationBasis(calculatorBasis);
            case '地质灾害危险性评估费':
                return this.getCalculationBasis(calculatorBasis);
            default:
                return null;
        }
    }

    getCalculationBasis(calculatorBasis) {
        let arr = calculatorBasis.split('-');
        if (arr.length === 1) {
            return gsJsqtfFyjsyj[arr[0]];
        }
        return gsJsqtfFyjsyj[arr[0]][arr[1]];
    }

    /**
     * 获取建设其他费计算器公式
     * @param args
     * @returns {{calculationProcess: string, fields: Map<*, *>}|null}
     */
    async getOtherProjectCostCalculatorFormula(args) {
        let calculatorBasis = args.calculatorBasis;  // 栏目选择名称拼接
        let source = args.source;   // 来源
        let money = args.money;   // 金额、估算投资额、计算额、岩土工程概算额、建安工程造价
        let floatRate = args.floatRate;   // floatRate：浮动值

        let zyRate = args.zyRate;  // zyRate  专业调整系数
        let gcfzRate = args.gcfzRate;   // gcfzRate   工程复杂调整系数
        let fjRate = args.fjRate;  // fjRate  附加调整系数
        let qtRate = args.qtRate;   // qtRate     其他调整系数
        let ztsjfRate = args.ztsjfRate;  // ztsjfRate  总设计费费率
        let sgtysRate = args.sgtysRate;  // sgtysRate  施工图预算编制费费率
        let jgtbzfRate = args.jgtbzfRate; // 竣工图编制费费率

        let fzRate = args.fzRate;  // 复杂调整系数
        let gcRate = args.gcRate;   // 高程调整系数

        let hytzRate = args.hytzRate;  // 行业调整系数
        let hjmgRate = args.hjmgRate;  // 环境敏感调整系数

        //建设项目前期咨询费
        let buildingProject = {'hytzRate': hytzRate, 'money': money, 'gcfzRate': gcfzRate, 'floatRate': floatRate};

        let sde = args.sde;  // 审定额
        let hzhjcgb = args.hzhjcgb;   //核增核减超过比（%）
        let sfRate = args.sfRate;    //收费费率
        let jlMethod = args.jlMethod;  // 计量方式
        let weight = args.weight;  // 钢筋重量
        let weightSource = args.weightSource; // 钢筋重量来源

        //造价咨询费
        let projectCost = {
            'money': money,
            'zyRate': zyRate,
            'floatRate': floatRate,
            "sde": sde,
            "hzhjcgb": hzhjcgb,
            "sfRate": sfRate,
            "weight": weight,
            "weightSource": weightSource
        };
        if (ObjectUtils.isNotEmpty(jlMethod)) {
            // 给projectCost添加一个属性jlMethod
            projectCost.jlMethod = jlMethod;
        } else {
            projectCost.jlMethod = "";
        }

        //水土保持费-方案编制费
        let stCost = {'money': money, 'floatRate': floatRate}
        //水土保持费—水土保持监理费
        let stbcfStbcjlfCost = {money, floatRate}
        //水土保持费—技术评估报告编制费
        let stbcfJspgbgbfCost = {money, floatRate}
        //水土保持费—技术咨询服务费
        let stbcJszxCost = {money, floatRate}
        let projectType = args.projectType; //工程类别、项目
        let stage = args.subProjectType; //阶段
        let landArea = args.landArea; //土地面积
        //水土保持补偿费
        let stbcfStbcbcfCost = {projectType, stage, landArea, floatRate}

        let complexity = args.complexity; //复杂程度
        //工程设计费—岩土工程设计费
        let stbcYtsjCost = {complexity, money, floatRate}

        let hytzxs = args.hytzRate; //行业调整系数
        let mgcdtzxh = args.mgcdtzxh; //敏感程度调整系数
        let qyfwtzxx = args.qyfwtzxx; //区域范围调整系数
        //社会风险稳定评估报告-编制社会稳定风险评估报告费
        let shfxwdpgbgBzshwdfxpgbgfCost = {money, hytzxs, mgcdtzxh, qyfwtzxx, floatRate}
        //社会风险稳定评估报告-评价社会稳定风险评估报告费
        let shfxwdpgbgPjshwdfxpgbgfCost = {money, hytzxs, mgcdtzxh, qyfwtzxx, floatRate}

        let subProjectType = args.subProjectType; // 子类工程类别
        let evaluationLevel = args.evaluationLevel; //评估等级
        let dqtzxs = args.dqtzxs; //地区调整系数
        let landOrArea = args.landOrArea; //评估长度/面积
        let projectTypeK1 = args.projectTypeK1; //工程累呗调整系数K1
        let projectTypeK2 = args.projectTypeK2; //工程累呗调整系数K2

        //地质灾害危险性评估费
        let dzzhwxxpgfCost = {
            projectType,
            subProjectType,
            projectTypeK1,
            projectTypeK2,
            evaluationLevel,
            complexity,
            landOrArea,
            dqtzxs,
            floatRate
        }

        let hkqh = args.hkqh; //还款期数
        // let money = args.money; //贷款金额
        let dkll = args.dkll; //贷款利率
        //建设期贷款利息——等额本息
        let jsqdklxDebjCost = {hkqh, money, dkll}

        //建设期贷款利息——等额本息
        let jsqdklxDebxCost = {hkqh, money, dkll}

        let zbdlftyCalculationBasis, zbdlfhbCalculationBasis, gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis,
            jsxmqqgzzxfCalculationBasis, zjzxfCalculationBasis;

        let map = new Map();
        map.set("calculatorBasis", calculatorBasis);
        map.set("source", source);

        switch (calculatorBasis) {
            case '项目建设管理费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                let xmjsglfCalculationBasis = new XmjsglfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: xmjsglfCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate)
                };
            case '招标代理费-招标代理费（通用）-工程招标代理费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                zbdlftyCalculationBasis = new ZbdlftyCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zbdlftyCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate)
                };
            case '招标代理费-招标代理费（通用）-货物招标代理费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                zbdlftyCalculationBasis = new ZbdlftyCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zbdlftyCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate)
                };
            case '招标代理费-招标代理费（通用）-服务招标代理费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                zbdlftyCalculationBasis = new ZbdlftyCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zbdlftyCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate)
                };
            case '招标代理费-招标代理费（河北）-工程招标代理费（河北）':
                map.set("money", money);
                map.set("floatRate", floatRate);
                zbdlfhbCalculationBasis = new ZbdlfhbCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zbdlfhbCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate)
                };
            case '招标代理费-招标代理费（河北）-货物招标代理费（河北）':
                map.set("money", money);
                map.set("floatRate", floatRate);
                zbdlfhbCalculationBasis = new ZbdlfhbCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zbdlfhbCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate)
                };
            case '招标代理费-招标代理费（河北）-服务招标代理费（河北）':
                map.set("money", money);
                map.set("floatRate", floatRate);
                zbdlfhbCalculationBasis = new ZbdlfhbCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zbdlfhbCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate)
                };
            case '工程设计费-工程设计费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                map.set("zyRate", zyRate);
                map.set("gcfzRate", gcfzRate);
                map.set("fjRate", fjRate);
                map.set("qtRate", qtRate);
                gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis = new GcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate, zyRate, gcfzRate, fjRate, qtRate, null)
                };
            case '工程设计费-总体设计费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                map.set("zyRate", zyRate);
                map.set("gcfzRate", gcfzRate);
                map.set("fjRate", fjRate);
                map.set("qtRate", qtRate);
                map.set("ztsjfRate", ztsjfRate);
                gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis = new GcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate, zyRate, gcfzRate, fjRate, qtRate, ztsjfRate)
                };
            case '工程设计费-施工图预算编制费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                map.set("zyRate", zyRate);
                map.set("gcfzRate", gcfzRate);
                map.set("fjRate", fjRate);
                map.set("qtRate", qtRate);
                map.set("sgtysRate", sgtysRate);
                gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis = new GcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate, zyRate, gcfzRate, fjRate, qtRate, sgtysRate)
                };
            case '工程设计费-竣工图编制费':
                map.set("money", money);
                map.set("floatRate", floatRate);
                map.set("zyRate", zyRate);
                map.set("gcfzRate", gcfzRate);
                map.set("fjRate", fjRate);
                map.set("qtRate", qtRate);
                map.set("jgtbzfRate", jgtbzfRate);
                gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis = new GcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: gcsjfOrztsjfOrsgtysbzfOrjgtbzfCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate, zyRate, gcfzRate, fjRate, qtRate, jgtbzfRate)
                };
            case '工程设计费-岩土工程设计费':
                for (let key in stbcYtsjCost) {
                    if (stbcYtsjCost.hasOwnProperty(key)) {
                        map.set(key, stbcYtsjCost[key]);
                    }
                }
                let stbcYtsjCalculationBasis = new StbcYtsjCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: stbcYtsjCalculationBasis.getCalculationProcess(stbcYtsjCost)
                };
            case '工程监理费-工程监理费（通用）':
                map.set("money", money);
                map.set("floatRate", floatRate);
                map.set("zyRate", zyRate);
                map.set("fzRate", fzRate);
                map.set("gcRate", gcRate);
                let gcjlfCalculationBasis = new GcjlfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: gcjlfCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate, zyRate, fzRate, gcRate)
                };
            case '工程监理费-工程监理费（河北）':
                map.set("money", money);
                map.set("floatRate", floatRate);
                map.set("zyRate", zyRate);
                map.set("fzRate", fzRate);
                let gcjlfhbCalculationBasis = new GcjlfhbCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: gcjlfhbCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate, zyRate, fzRate)
                };
            case '环境影响咨询服务费-编制环境影响报告书（含大纲）':
            case '环境影响咨询服务费-编制环境影响报告表':
            case '环境影响咨询服务费-评估环境影响报告书（含大纲）':
            case '环境影响咨询服务费-评估环境影响报告表':
                map.set("money", money);
                map.set("floatRate", floatRate);
                map.set("hytzRate", hytzRate);
                map.set("hjmgRate", hjmgRate);
                let bzhjbgsAndbgbCalculationBasis = new BzhjbgsAndbgbCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: bzhjbgsAndbgbCalculationBasis.getCalculationProcess(calculatorBasis, money, floatRate, hytzRate, hjmgRate)
                };

            case ComputationalFormula.JXXM_BZXM_TITLE:  // 建设项目前期工作咨询费-编制项目建议书
                for (let key in buildingProject) {
                    if (buildingProject.hasOwnProperty(key)) {
                        map.set(key, buildingProject[key]);
                    }
                }
                jsxmqqgzzxfCalculationBasis = new JsxmqqgzzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: jsxmqqgzzxfCalculationBasis.getCalculationProcess1(money, buildingProject)
                };
            case ComputationalFormula.JXXM_KXXBG_TITLE:  // 建设项目前期工作咨询费-编制可行性研究报告
                for (let key in buildingProject) {
                    if (buildingProject.hasOwnProperty(key)) {
                        map.set(key, buildingProject[key]);
                    }
                }
                jsxmqqgzzxfCalculationBasis = new JsxmqqgzzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: jsxmqqgzzxfCalculationBasis.getCalculationProcess2(money, buildingProject)
                };
            case ComputationalFormula.JXXM_PGJY_TITLE:  // 建设项目前期工作咨询费-评估项目建议书
                for (let key in buildingProject) {
                    if (buildingProject.hasOwnProperty(key)) {
                        map.set(key, buildingProject[key]);
                    }
                }
                jsxmqqgzzxfCalculationBasis = new JsxmqqgzzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: jsxmqqgzzxfCalculationBasis.getCalculationProcess3(money, buildingProject)
                };
            case ComputationalFormula.JXXM_PGKXX_TITLE:  // 建设项目前期工作咨询费-评估可行性研究报告
                for (let key in buildingProject) {
                    if (buildingProject.hasOwnProperty(key)) {
                        map.set(key, buildingProject[key]);
                    }
                }
                jsxmqqgzzxfCalculationBasis = new JsxmqqgzzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: jsxmqqgzzxfCalculationBasis.getCalculationProcess4(money, buildingProject)
                };

            // 水土保持费-水土保持方案编制费
            case ComputationalFormula.STBC_FABZ_TITLE:
                for (let key in stCost) {
                    if (stCost.hasOwnProperty(key)) {
                        map.set(key, stCost[key]);
                    }
                }
                let stbcfCalculationBasis = new StbcfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: stbcfCalculationBasis.getCalculationProcess(money, stCost)
                };
            //水土保持费——水土保持监理费
            case ComputationalFormula.STBCF_STBCJLF_TITLE:
                for (let key in stbcfStbcjlfCost) {
                    if (stbcfStbcjlfCost.hasOwnProperty(key)) {
                        map.set(key, stbcfStbcjlfCost[key]);
                    }
                }
                let stbcfStbcjlfCalculationBasis = new StbcfStbcjlfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: stbcfStbcjlfCalculationBasis.getCalculationProcess(stbcfStbcjlfCost)
                };
            //水土保持费-技术评估报告编制费
            case ComputationalFormula.STBCF_JSPGBGBZF_TITLE:
                for (let key in stbcfJspgbgbfCost) {
                    if (stbcfJspgbgbfCost.hasOwnProperty(key)) {
                        map.set(key, stbcfJspgbgbfCost[key]);
                    }
                }
                let stbcfJspgbgbfCalculationBasis = new StbcfJspgbgbfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: stbcfJspgbgbfCalculationBasis.getCalculationProcess(stbcfJspgbgbfCost)
                };
            //水土保持费计算——技术咨询服务费
            case ComputationalFormula.STBC_JSZX_TITLE:
                for (let key in stbcJszxCost) {
                    if (stbcJszxCost.hasOwnProperty(key)) {
                        map.set(key, stbcJszxCost[key]);
                    }
                }
                let stbcJszxCalculationBasis = new StbcJszxCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: stbcJszxCalculationBasis.getCalculationProcess(stbcJszxCost)
                };
            //水土保持补偿费
            case ComputationalFormula.STBCF_STBCBCF_TITLE:
                for (let key in stbcfStbcbcfCost) {
                    if (stbcfStbcbcfCost.hasOwnProperty(key)) {
                        map.set(key, stbcfStbcbcfCost[key]);
                    }
                }
                let stbcfStbcbcfCalculationBasis = new StbcfStbcbcfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: stbcfStbcbcfCalculationBasis.getCalculationProcess(stbcfStbcbcfCost)
                };

            // 造价咨询费-预算编制
            case  ComputationalFormula.ZJZX_YSBZZX_TITLE:
                map.set("money", money);
                for (let key in projectCost) {
                    if (projectCost.hasOwnProperty(key)) {
                        map.set(key, projectCost[key]);
                    }
                }
                zjzxfCalculationBasis = new ZjzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zjzxfCalculationBasis.getCalculationProcess1(money, jlMethod, projectCost)
                };
            // 造价咨询费-工程量清单编制
            case ComputationalFormula.ZJZX_GCLQDBZ_TITLE:
                map.set("money", money);
                for (let key in projectCost) {
                    if (projectCost.hasOwnProperty(key)) {
                        map.set(key, projectCost[key]);
                    }
                }
                zjzxfCalculationBasis = new ZjzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zjzxfCalculationBasis.getCalculationProcess2(money, jlMethod, projectCost)
                };
            // 造价咨询费-招标控制价编制
            case ComputationalFormula.ZJZX_ZBKZJ_TITLE:
                map.set("money", money);
                for (let key in projectCost) {
                    if (projectCost.hasOwnProperty(key)) {
                        map.set(key, projectCost[key]);
                    }
                }
                zjzxfCalculationBasis = new ZjzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zjzxfCalculationBasis.getCalculationProcess3(money, jlMethod, projectCost)
                };
            // 造价咨询费-施工阶段造价咨询
            case ComputationalFormula.ZJZX_SGJDZJ_TITLE:
                map.set("money", money);
                for (let key in projectCost) {
                    if (projectCost.hasOwnProperty(key)) {
                        map.set(key, projectCost[key]);
                    }
                }
                zjzxfCalculationBasis = new ZjzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zjzxfCalculationBasis.getCalculationProcess4(money, jlMethod, projectCost)
                };
            // 造价咨询费-结算审核
            case ComputationalFormula.ZJZX_JSSH_TITLE:
                map.set("money", money);
                for (let key in projectCost) {
                    if (projectCost.hasOwnProperty(key)) {
                        map.set(key, projectCost[key]);
                    }
                }
                zjzxfCalculationBasis = new ZjzxfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: zjzxfCalculationBasis.getCalculationProcess5(money, jlMethod, projectCost)
                };

            case ComputationalFormula.SHFXWDPGBG_BZSHWDFXPGBGF_TITLE: //社会稳定风险评估费-编制社会稳定风险评估报告
                for (let key in shfxwdpgbgBzshwdfxpgbgfCost) {
                    if (shfxwdpgbgBzshwdfxpgbgfCost.hasOwnProperty(key)) {
                        map.set(key, shfxwdpgbgBzshwdfxpgbgfCost[key]);
                    }
                }
                let shfxwdpgbgBzshwdfxpgbgfCalculationBasis = new ShfxwdpgbgBzshwdfxpgbgfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: shfxwdpgbgBzshwdfxpgbgfCalculationBasis.getCalculationProcess(shfxwdpgbgBzshwdfxpgbgfCost)
                };
            //社会稳定风险评估费-评价社会稳定风险评估报告
            case ComputationalFormula.SHFXWDPGBG_PJSHWDFXPGBGF_TITLE:
                for (let key in shfxwdpgbgPjshwdfxpgbgfCost) {
                    if (shfxwdpgbgPjshwdfxpgbgfCost.hasOwnProperty(key)) {
                        map.set(key, shfxwdpgbgPjshwdfxpgbgfCost[key]);
                    }
                }
                let shfxwdpgbgPjshwdfxpgbgfCalculationBasis = new ShfxwdpgbgPjshwdfxpgbgfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: shfxwdpgbgPjshwdfxpgbgfCalculationBasis.getCalculationProcess(shfxwdpgbgPjshwdfxpgbgfCost)
                };

            //地质灾害危险性评估费
            case ComputationalFormula.DZZHWXXPGF_TITLE:
                for (let key in dzzhwxxpgfCost) {
                    if (dzzhwxxpgfCost.hasOwnProperty(key)) {
                        map.set(key, dzzhwxxpgfCost[key]);
                    }
                }
                let dzzhwxxpgfCalculationBasis = new DzzhwxxpgfCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: dzzhwxxpgfCalculationBasis.getCalculationProcess(dzzhwxxpgfCost)
                };

            case ComputationalFormula.JSQDKLX_DEBJ_TITLE: //建设期贷款利息——等额本金
                for (let key in jsqdklxDebjCost) {
                    if (jsqdklxDebjCost.hasOwnProperty(key)) {
                        map.set(key, jsqdklxDebjCost[key]);
                    }
                }
                let jsqdklxDebjCalculationBasis = new JsqdklxDebjCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: jsqdklxDebjCalculationBasis.getCalculationProcess(jsqdklxDebjCost)
                };

            case ComputationalFormula.JSQDKLX_DEBX_TITLE: //建设期贷款利息——等额本息
                for (let key in jsqdklxDebxCost) {
                    if (jsqdklxDebxCost.hasOwnProperty(key)) {
                        map.set(key, jsqdklxDebxCost[key]);
                    }
                }
                let jsqdklxDebxCalculationBasis = new JsqdklxDebxCalculationBasis();
                return {
                    fields: map,
                    calculationProcess: jsqdklxDebxCalculationBasis.getCalculationProcess(jsqdklxDebxCost)
                };
            default:
                return null;
        }
    }

    /**
     * 给费用计算器左侧树种添加参数
     * @param otherProjectCostCalculators  费用计算器
     * @param calculatorBasis  计算器连级名称
     * @param fields  参数
     */
    findCalculatorToTree(otherProjectCostCalculators, calculatorBasis, fields) {
        otherProjectCostCalculators.forEach(node => {
            for (let enumKey in OtherProjectCostCategoryEnum) {
                if (node.nameBackUp === OtherProjectCostCategoryEnum[enumKey].code && node.nameBackUp === calculatorBasis) {
                    node["fields"] = fields;
                    break;
                }
            }
            if (node.children && node.children.length) {
                this.findCalculatorToTree(node.children, calculatorBasis, fields);
            }
        });
    }

    /**
     * 获取建设其他费计算器数据
     * @param args
     * @returns {ResponseData}
     */
    async getOtherProjectCostCalculatorData(args) {
        let {projectId, calculatorBasis, amount, fields, calculationProcess} = args;

        let category;
        for (let enumKey in OtherProjectCostCategoryEnum) {
            if (OtherProjectCostCategoryEnum[enumKey].code === calculatorBasis) {
                category = OtherProjectCostCategoryEnum[enumKey].desc;
            }
        }

        // 小数点精度
        let precision = await this.service.PreliminaryEstimate.gsCommonService.getPrecisionSetting(projectId);
        let je = precision.OTHER_PROJECT_COST.je;
        let jsqJe = precision.OTHER_PROJECT_COST.jsqJe;

        let total = NumberUtil.numberScale(NumberUtil.multiply(amount, 10000), je);  // 万元到单位元的转化
        // 获取该工程项目的建设其他费
        let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        // 过滤满足条件的数据
        let otherProjectCostArray = otherProjectCosts.filter(item => item.category === category);
        if (ObjectUtils.isEmpty(otherProjectCostArray)) {
            return ResponseData.fail("该费用类别不存在，请先设置该费用类别");
        }

        // 检查是否为普通的对象且不是 Map
        if (ObjectUtils.isNotEmpty(fields)) {
            if (Object.prototype.toString.call(fields) === '[object Object]' && !(fields instanceof Map)) {
                fields = new Map(Object.entries(fields));
            }
            fields.set("amount", amount);
            fields.set("calculationProcess", calculationProcess);
        }

        // 获取建设其他费费用计算器
        let otherProjectCostCalculators = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_JSQ);
        // 给费用计算器左侧树种添加参数
        this.findCalculatorToTree(otherProjectCostCalculators, calculatorBasis, fields);
        // 更新费用计算器
        ProjectDomain.getDomain(projectId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF_JSQ, otherProjectCostCalculators);

        // 修改节点属性值
        let sequenceNbr;
        otherProjectCostArray.forEach(node => {
            if (node.category === category) {
                sequenceNbr = node.sequenceNbr;
                // 费用计算器独有的
                node.fields = fields;
                node.amount = total;
                node.calculationProcess = calculationProcess;
                node.calculationMethod = OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_3;
                // 更改计算方式
                if (ObjectUtils.isNotEmpty(calculationProcess)) {
                    // 当变换计算方式时，则清空相关数据
                    node.price = "";
                    node.quantity = null;
                    node.priceDescription = null;
                }
            }
        });
        otherProjectCosts.forEach(item => {
            item.children = [];
        });
        // 转树  TODO: 需要这段汇总，以防别处行引用汇总行的amount
        let arrayTree = xeUtils.toArrayTree(otherProjectCosts, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });
        // 树结构,每一级的值向上一级汇总
        let otherProjectCostList = this.sumChildNodeAmounts(arrayTree[0], je);
        let otherProjectCostSummaryArray = xeUtils.toTreeArray(new Array(otherProjectCostList));

        // 获取费用代码
        let otherProjectCostCodeArray = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_CODE);

        // 调用计算建设其他费
        let newList = await this.countOtherProjectCost(projectId, otherProjectCostCodeArray, otherProjectCostSummaryArray);

        // 更新建设其他费
        ProjectDomain.getDomain(projectId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF, newList);
        return ResponseData.success(sequenceNbr);
    }

    /**
     * 回显指定行建设其他费计算器参数
     * @param args
     * @returns {{calculatorTree: {}, calculatorParams}}
     */
    async getOtherProjectCostCalculatorParams(args) {
        let {projectId, sequenceNbr} = args;

        // 获取建设其他费费用计算器
        let otherProjectCostCalculators = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_JSQ);

        // 判断是否指定行
        if (ObjectUtils.isNotEmpty(sequenceNbr)) {
            // 获取建设其他费
            let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
            // 获取当前选择行数据
            let otherProjectCost = otherProjectCosts.find(item => item.sequenceNbr === sequenceNbr);

            if (ObjectUtils.isNotEmpty(otherProjectCost.category)) {
                // 添加树的选中标识
                this.addFlagPropertyToTree(otherProjectCostCalculators, otherProjectCost.category);
            }

            // 判断是否是费用计算器类型，需要被选定
            // if (ObjectUtils.isNotEmpty(otherProjectCost.calculationMethod) && otherProjectCost.calculationMethod !== OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_3) {
            //     return {
            //         // fields: null,
            //         // calculationProcess: null,
            //         // amount: null,
            //         calculatorTree: otherProjectCostCalculators
            //     };
            // }
            // if (ObjectUtils.isNotEmpty(otherProjectCost.fields)) {
            //     // 判断otherProjectCost.fields是对象时，转成map
            //     if (Object.prototype.toString.call(otherProjectCost.fields) === '[object Object]' && !(otherProjectCost.fields instanceof Map)) {
            //         otherProjectCost.fields = new Map(Object.entries(otherProjectCost.fields));
            //     }
            // }
            // return {
            //     // fields: otherProjectCost.fields,
            //     calculatorTree: otherProjectCostCalculators
            // };
            return otherProjectCostCalculators;
        }
    }

    // /**
    //  * 通过计算器树结构名称，获取建设其他费计算器参数
    //  * @param args
    //  */
    // async getOtherProjectCostCalculatorParamsByName(args) {
    //     let {projectId, calculatorBasis} = args;
    //
    //     // 获取建设其他费
    //     let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
    //     let sequenceNbr;
    //     otherProjectCosts.forEach(item => {
    //         for (let enumKey in OtherProjectCostCategoryEnum) {
    //             if (calculatorBasis === OtherProjectCostCategoryEnum[enumKey].code && OtherProjectCostCategoryEnum[enumKey].desc === item.category) {
    //                 sequenceNbr = item.sequenceNbr;
    //             }
    //         }
    //     });
    //
    //     // 判断是否指定行
    //     if (ObjectUtils.isNotEmpty(sequenceNbr)) {
    //         // 获取当前选择行数据
    //         let otherProjectCost = otherProjectCosts.find(item => item.sequenceNbr === sequenceNbr);
    //         // 遍历otherProjectCost.fields
    //         // if (ObjectUtils.isNotEmpty(otherProjectCost.fields) && otherProjectCost.fields.size > 0) {
    //         //     for (let [key, value] of otherProjectCost.fields) {
    //         //         if (key === 'jlMethod') {
    //         //             switch (value) {
    //         //                 case gsZjzx.钢筋工程量精细计量方式[0].value:
    //         //                     otherProjectCost.fields.set(key, gsZjzx.钢筋工程量精细计量方式[0].name);
    //         //                     break;
    //         //                 case gsZjzx.钢筋工程量精细计量方式[1].value:
    //         //                     otherProjectCost.fields.set(key, gsZjzx.钢筋工程量精细计量方式[1].name);
    //         //                     break;
    //         //                 case gsZjzx.钢筋工程量精细计量方式[2].value:
    //         //                     otherProjectCost.fields.set(key, gsZjzx.钢筋工程量精细计量方式[2].name);
    //         //                     break;
    //         //                 default:
    //         //                     break;
    //         //             }
    //         //         }
    //         //     }
    //         // }
    //         if (ObjectUtils.isNotEmpty(otherProjectCost.fields)) {
    //             // 判断otherProjectCost.fields是对象时，转成map
    //             if (Object.prototype.toString.call(otherProjectCost.fields) === '[object Object]' && !(otherProjectCost.fields instanceof Map)) {
    //                 otherProjectCost.fields = new Map(Object.entries(otherProjectCost.fields));
    //             }
    //         }
    //         return {
    //             fields: otherProjectCost.fields,
    //         };
    //     }
    // }


    /**
     * 添加树的选中标识
     * @param otherProjectCostCalculators  建设其他费-费用计算器
     * @param category
     */
    addFlagPropertyToTree(otherProjectCostCalculators, category) {
        otherProjectCostCalculators.forEach(node => {
            for (let enumKey in OtherProjectCostCategoryEnum) {
                if (node.nameBackUp === OtherProjectCostCategoryEnum[enumKey].code && OtherProjectCostCategoryEnum[enumKey].desc === category) {
                    node["ifCheck"] = true;
                    break;
                } else {
                    node["ifCheck"] = false;
                }
            }
            if (node.children && node.children.length) {
                this.addFlagPropertyToTree(node.children, category);
            }
        });
    }


    /**
     * 获取建设其他费计价文件
     * @param args
     */
    async getPolicyDocuments(args) {
        let {projectId} = args;

        // 数据库获取：建设其他费-计价依据
        let basePolicyDocuments = await this.gsBasePolicyDocumentDao.find({
            where: {recStatus: "A"}
        });
        // 根据第一子类聚合
        const groupedData01 = this.groupByUniqueField(basePolicyDocuments, 'costCatalogLevel01');
        // 遍历groupedData
        let basePolicyDocumentArray01 = [];
        for (const key in groupedData01) {
            // 获取当前key对应的数组
            const array = groupedData01[key];
            let basePolicyDocument01 = {};
            basePolicyDocument01.name = key;

            let childBasePolicyDocuments = array.filter(item => item.costCatalogLevel01 === key && item.costCatalogLevel02 !== null);
            if (childBasePolicyDocuments.length > 0) {
                // 根据第二子类聚合
                const groupedData02 = this.groupByUniqueField(childBasePolicyDocuments, 'costCatalogLevel02');
                let basePolicyDocumentArray02 = [];
                for (const key2 in groupedData02) {
                    // 获取当前key对应的数组
                    const array2 = groupedData02[key2];
                    // 文件层
                    let childBasePolicyDocumentArray = [];
                    for (let i = 0; i < array2.length; i++) {
                        const element = array2[i];
                        let childBasePolicyDocument = {};
                        childBasePolicyDocument.name = element.documentName;
                        childBasePolicyDocument.url = element.url;
                        childBasePolicyDocument.category = element.costCatalogLevel03;
                        childBasePolicyDocumentArray.push(childBasePolicyDocument);
                    }
                    // 二级目录层
                    let basePolicyDocument02 = {};
                    basePolicyDocument02.name = key2;
                    basePolicyDocument02.url = null;
                    basePolicyDocument02.category = null;
                    basePolicyDocument02.children = null;
                    basePolicyDocument02.documentArray = childBasePolicyDocumentArray;
                    basePolicyDocumentArray02.push(basePolicyDocument02);
                }
                // 一级目录层
                basePolicyDocument01.url = null;
                basePolicyDocument01.category = null;
                basePolicyDocument01.children = basePolicyDocumentArray02;
                basePolicyDocumentArray01.push(basePolicyDocument01);
            } else {
                // 文件层
                let basePolicyDocumentArray02 = [];
                for (let i = 0; i < array.length; i++) {
                    const element = array[i];
                    let childBasePolicyDocument = {};
                    childBasePolicyDocument.name = element.documentName;
                    childBasePolicyDocument.url = element.url;
                    childBasePolicyDocument.category = element.costCatalogLevel03;
                    basePolicyDocumentArray02.push(childBasePolicyDocument);
                }
                // 一级目录层
                basePolicyDocument01.url = null;
                basePolicyDocument01.category = null;
                basePolicyDocument01.children = null;
                basePolicyDocument01.documentArray = basePolicyDocumentArray02;
                basePolicyDocumentArray01.push(basePolicyDocument01);
            }
        }
        return basePolicyDocumentArray01;
    }

    /**
     * 聚合指定字段的数据
     * @param array
     * @param key
     * @returns {*}
     */
    groupByUniqueField(array, key) {
        return array.reduce((accumulator, current) => {
            // 初始化或获取当前key对应的数组
            const group = accumulator[current[key]] || [];
            // 将当前对象添加到对应的组中
            group.push(current);
            // 更新累加器，确保下次循环时可以正确访问或创建新的组
            accumulator[current[key]] = group;
            return accumulator;
        }, {});
    }

    /**
     * 根据建设其他费行名称，获取建设其他费计价文件
     * @param args
     */
    async getPolicyDocumentsByName(args) {
        let {projectId, name} = args;

        // 获取建设其他费计价文件
        let basePolicyDocumentArray01 = await this.getPolicyDocuments({projectId: projectId});
        if (ObjectUtils.isEmpty(name)) {
            return ResponseData.success(basePolicyDocumentArray01);
        }
        // 获取指定建设其他费行的计价依据文件，并标识
        let flag = await this.markNodeByName(basePolicyDocumentArray01, name);

        let responseData = new ResponseData();
        if (flag) {
            return ResponseData.success(basePolicyDocumentArray01);
        } else {
            responseData.code = 200;
            responseData.message = "暂未收录相关文件！";
            responseData.result = basePolicyDocumentArray01;
            return responseData;
        }
    }


    /**
     * 遍历树结构，判断每个节点属性name当等于某值，则标识
     * @param tree
     * @param name
     */
    markNodeByName(tree, name) {
        if (!tree) return null;
        let flag = false;
        // 递归遍历所有子节点
        for (const child of tree) {
            if (ObjectUtils.isNotEmpty(child.category)) {
                let categorys = child.category.split('、');
                // 检查当前节点的name是否匹配
                for (let i = 0; i < categorys.length; i++) {
                    if (name.includes(categorys[i])) {
                        child.marked = true;
                        tree.marked = true;
                        flag = true;
                        console.log(`节点 ${child.category} 已被标记`);
                        break;
                    }
                }
            }
            if (flag) {
                return flag;
            }
            if (ObjectUtils.isNotEmpty(child.children)) {
                flag = this.markNodeByName(child.children, name, flag);
                if (flag) {
                    child.checked = true;
                }
            }
            if (ObjectUtils.isNotEmpty(child.documentArray)) {
                flag = this.markNodeByName(child.documentArray, name, flag);
                if (flag) {
                    child.marked = true;
                    child.checked = true;
                }
            }
        }
        return flag;
    }


    /**
     * 导入建设其他费
     * @param args
     */
    async importOtherProjectCost(args) {
        let {projectId} = args;

        // 获取建设其他费模版存放路径  D:\IdeaProjects\gaiSuan\pricing-cs\build\extraResources\excelTemplate\gs\建设其他费
        const jsqtfTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\建设其他费';
        let options = {
            properties: ['openFile'],
            defaultPath: jsqtfTemplatePath, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_QTF]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            return ResponseData.fail('未选中任何文件');
        }

        // 建设其他费文件的路径
        const qtfFilePath = result[0];
        try {
            const data = fs.readFileSync(qtfFilePath, 'utf8');
            // 使用JSON.parse()方法将JSON字符串转换为JavaScript数组
            const lines = JSON.parse(data);

            // 假设每行是一个独立的对象，以逗号分隔字段
            const otherProjectCostSummaryArray = [];
            lines.forEach(line => {
                // 这里需要根据实际的.qtf格式进行解析
                const obj = {}; // 创建一个对象来存储这一行的数据
                obj.dispNo = line.dispNo;
                obj.code = line.code;
                obj.name = line.name;
                obj.unit = line.unit;
                obj.calculationMethod = line.calculationMethod;
                obj.calculationMethodBackUp = obj.calculationMethod;  // 备份默认值
                obj.category = line.category;
                obj.calculationBasis = line.calculationBasis;
                obj.ifCalculate = line.ifCalculate;
                obj.permission = line.permission;
                obj.remark = line.remark;
                obj.fields = line.fields;
                obj.sequenceNbr = line.sequenceNbr;
                obj.parentId = line.parentId;
                obj.price = line.price;
                obj.priceDescription = line.priceDescription;
                obj.quantity = line.quantity;
                obj.levelType = line.levelType;
                obj.priceBackUp = null;
                obj.quantityBackUp = null;
                otherProjectCostSummaryArray.push(obj);
            });

            let otherProjectCostCodeArray = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF_CODE);
            // 调用计算建设其他费
            let otherProjectCosts = await this.countOtherProjectCost(projectId, otherProjectCostCodeArray, otherProjectCostSummaryArray);
            return ResponseData.success(otherProjectCosts);
        } catch (err) {
            return ResponseData.fail('导入失败');
        }
    }

    /**
     * 导出建设其他费
     * @param args
     */
    async exportOtherProjectCost(args) {
        let {projectId} = args;

        // 获取工程项目的建设其他费
        let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        // 复制一份数据
        let otherProjectCostCopy = ConvertUtil.deepCopy(otherProjectCosts);
        otherProjectCostCopy.forEach(item => {
            if (!(ObjectUtils.isNotEmpty(item.calculationMethod) && item.calculationMethod === OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2)) {
                item.price = null;
                item.quantity = null;
                item.priceDescription = null;
            }
        });
        // 指定导出的列名
        const columns = ["dispNo", "code", "name", "unit", "calculationMethod", "category", "calculationBasis", "ifCalculate", "permission", "remark", "fields",
            "sequenceNbr", "parentId", "levelType", "price", "priceDescription", "quantity"];
        // 根据指定的列名来重组数据，确保导出的JSON只包含这些列
        const formattedData = otherProjectCostCopy.map(item => {
            return columns.reduce((acc, col) => {
                acc[col] = item[col];
                return acc;
            }, {});
        });
        // 将数组转换为JSON字符串   const jsonData = JSON.stringify(formattedData, null, 2);
        const jsonData = JSON.stringify(formattedData); // 第三个参数是缩进量，使输出更易读

        // 存放费用汇总文件的路径
        const jsqtfTemplatePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\建设其他费';
        const count = this.countDirectories(jsqtfTemplatePath);
        let options = {
            title: '保存文件',
            defaultPath: jsqtfTemplatePath + '\\建设其他费模板' + count, // 默认保存路径或者模版获取路径
            filters: [
                {name: '云算房', extensions: [FileOperatorType.File_TYPE_QTF]} // 可选的文件类型
            ]
        };
        let filePath = dialog.showSaveDialogSync(null, options);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(FileOperatorType.File_TYPE_QTF)) {
                filePath += FileOperatorType.File_TYPE_QTF;
            }
            // 写入文件
            fs.writeFile(filePath, jsonData, (err) => {
                if (err) {
                    ResponseData.fail('写入文件时发生错误');
                } else {
                    ResponseData.success('数据已成功导出');
                }
            });
            return ResponseData.success(filePath);
        }
    }

// countDirectories(dirPath) {
//     let count = 0;
//     fs.readdirSync(dirPath).forEach((item) => {
//         const fullPath = path.join(dirPath, item);
//         // if (fs.statSync(fullPath).isDirectory()) {
//         //     // 递归调用自己，统计子目录
//         //     count += 1 + this.countDirectories(fullPath);
//         // }
//         count = count + 1;
//     });
//     return count;
// }

    /**
     * 获取当前文件夹路径下文件个数
     * @param dirPath
     * @returns {number}
     */
    countDirectories(dirPath) {
        let count = 1;
        let numbers = [];
        fs.readdirSync(dirPath).forEach((item) => {
            if (item.match(/\d+/g) !== null) {
                numbers.push(item.match(/\d+/g)[0]);
            }
        });
        if (ObjectUtils.isNotEmpty(numbers)) {
            count = Math.max(...numbers) + 1;
        }
        return count;
    }


    /**
     * 上移下移建设其他费
     * @param args
     * @returns {*}
     */
    async moveUpAndDownProjectCost(args) {
        let {projectId, sequenceNbrArray, moveType} = args;

        // 获取建设其他费，并转化成树结构
        let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);
        let arrayTree = xeUtils.toArrayTree(otherProjectCosts, {
            key: 'sequenceNbr',
            parentKey: 'parentId',
        });

        sequenceNbrArray.forEach(sequenceNbr => {
            // 获取选中行
            let otherProjectCost = otherProjectCosts.find(item => item.sequenceNbr === sequenceNbr);

            // 获取当前选定行的父节点，获取他的子集
            let projectCostParentNode = this.getNodeById(arrayTree[0], otherProjectCost.parentId);
            if (ObjectUtils.isNotEmpty(projectCostParentNode) && ObjectUtils.isNotEmpty(projectCostParentNode.children)) {
                // 遍历建设其他费，找到点击行
                let projectCostNode = projectCostParentNode.children.find(item => item.sequenceNbr === sequenceNbr);
                // 将指定行，在集合中向上/向下移一个位置
                if (moveType === "up") {
                    this.moveItemUp(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                } else if (moveType === "down") {
                    this.moveItemDown(projectCostParentNode.children, projectCostParentNode.children.indexOf(projectCostNode));
                }
            }
        });
        // 平铺建设其他费列表数据，并返回
        let newList = xeUtils.toTreeArray(arrayTree);

        // 更新建设其他费
        ProjectDomain.getDomain(projectId).functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF, newList);
        return ResponseData.success(newList);
    }

    /**
     * 向上移位
     * @param array
     * @param index
     */
    moveItemUp(array, index) {
        // 检查索引是否大于0，因为不能移动第一个元素到更前面去
        if (index > 0) {
            // 保存要移动的元素
            let item = array.splice(index, 1)[0];
            // 在当前位置之前插入元素
            array.splice(index - 1, 0, item);
        }
    }


    /**
     * 向下移位
     * @param array
     * @param index
     */
    moveItemDown(array, index) {
        // 检查index是否在数组的有效范围内并且不是最后一个元素
        if (index >= 0 && index < array.length - 1) {
            // 使用splice取出要移动的元素
            const element = array.splice(index, 1)[0];
            // 将取出的元素插入到其下方的位置
            array.splice(index + 1, 0, element);
        }
        // return array;
    }


    addLevelNumbers(node, prefix = '') {
        // 当前节点的编号为当前prefix
        node.sortIndex = prefix;

        if (node.children) {
            // 遍历当前节点的所有子节点
            for (let i = 0; i < node.children.length; i++) {
                // 为子节点生成新的编号，基于当前节点的编号
                const childPrefix = `${prefix}${prefix ? '.' : ''}${i + 1}`;
                this.addLevelNumbers(node.children[i], childPrefix);
            }
        }
    }

    /**
     * 导出建设其他费列表excel
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async exportOtherProjectCostListExcel(args) {
        let {projectId} = args;

        // 获取建设其他费，并转化成树结构
        let otherProjectCosts = ProjectDomain.getDomain(projectId).functionDataMap.get(FunctionTypeConstants.PROJECT_JSQTF);

        let defaultStoragePath = UtilsPs.getExtraResourcesDir() + '\\excelTemplate\\gs\\建设其他费';
        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.toString(),
            filters: [{name: '云算房文件', extensions: ['xlsx']}],
        };

        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (ObjectUtils.isEmpty(filePath)) {
            return ResponseData.fail("未选中任何文件");
        }
        if (filePath && !filePath.canceled) {
            if (!filePath.endsWith(".xlsx")) {
                filePath += ".xlsx";
            }
            // 获取所有对象的所有属性名以生成动态列
            const allKeys = [];
            otherProjectCosts.forEach(obj => {
                Object.keys(obj).forEach(key => {
                    if (!allKeys.includes(key) && (key === 'dispNo' || key === 'code' || key === 'name' || key === 'unit' || key === 'calculationMethod'
                        || key === 'price' || key === 'quantity' || key === 'priceDescription' || key === 'amount' || key === 'calculationBasis'
                        || key === 'calculationProcess' || key === 'category' || key === 'ifCalculate' || key === 'remark')) {
                        allKeys.push(key);
                    }
                });
            });

            // 创建一个二维数组来表示Excel表格的内容
            const tableData = [allKeys].concat(otherProjectCosts.map(obj => allKeys.map(key => obj[key] || '')));

            // 遍历数组并转换指定列的数据    calculationMethod(1：单价*数量；2：计算基数*费率；3：费用计算器；4：手动输入)
            const columnToTranslateIndex = 4; // 假设 "columnToTranslate" 是数组中的第 5 列
            tableData.forEach(item => {
                switch (item[columnToTranslateIndex]) {
                    case OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_1:
                        item[columnToTranslateIndex] = "单价*数量";
                        break;
                    case OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_2:
                        item[columnToTranslateIndex] = "计算基数*费率";
                        break;
                    case OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_3:
                        item[columnToTranslateIndex] = "费用计算器";
                        break;
                    case OtherProjectCostOptionMenuConstants.CALCULATIONMETHOD_4:
                        item[columnToTranslateIndex] = "手动输入";
                        break;
                    default:
                        break;
                }
            });

            // 定义英文标题与中文标题的映射关系
            const titleMap = {
                dispNo: '编码',
                code: '费用代号',
                name: '名称',
                unit: '单位',
                price: '单价/计算基数',
                quantity: '数量/费率',
                priceDescription: '基数说明',
                calculationMethod: '计算方式',
                amount: '金额',
                calculationBasis: '计算依据',
                calculationProcess: '计算过程',
                category: '费用类别',
                ifCalculate: '是否计算',
                remark: '备注'
            };
            // 将二维数组的第一行（标题）转换为汉字标题
            const translatedHeaders = tableData[0].map((title) => {
                // 如果找到了，则返回对应的中文标题；如果没找到，则保持原样返回
                return titleMap[title] || title;
            });
            // 创建新的二维数组，包含转换后的标题和原数据
            const translatedData = [translatedHeaders].concat(tableData.slice(1));

            // 将数据转换为工作表对象
            const worksheet = XLSX.utils.aoa_to_sheet(translatedData);
            // 创建工作簿并填充数据
            const workbook = XLSX.utils.book_new();

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

            // 导出为Excel文件  当前单位工程名称+分期工程量
            // XLSX.writeFile(workbook,  unitName + '分期工程量.xlsx');

            // 将工作簿转换为二进制数据流
            const wbBuffer = XLSX.write(workbook, {bookType: 'xlsx', type: 'buffer'});

            // 将二进制数据流保存到桌面，需要指定保存的路径和文件名
            fs.writeFileSync(filePath, wbBuffer);
        }
    }
}

GsOtherProjectCostService.toString = () => '[class GsOtherProjectCostService]';
module.exports = GsOtherProjectCostService;