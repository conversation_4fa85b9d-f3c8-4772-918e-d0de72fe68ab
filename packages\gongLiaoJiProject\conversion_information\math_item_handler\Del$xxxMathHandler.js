
const {ObjectUtils} = require("../../utils/ObjectUtils");
const MathItemHandler = require("../../standard_conversion/math_item_handler/mathItemHandler");

/**
 * 单条规则处理math
 *    1. Del xxx n 删除材料
 */
class Del$xxxMathHandler extends MathItemHandler{
    analysisMath() {
        let mathItem = this.mathItem;
        mathItem.type = 5;
        let arr = mathItem.math.substring(1).split(/\s+/);
        mathItem.delRCJCode = arr[1];
        mathItem.oriResQty = arr[2];
    }
    async execute() {
        this.analysisMath();
        console.log("------Del$xxxMathHandler---", "删除人材机");
        let rcjsForDel = this.findActiveRCJByCode(this.mathItem.delRCJCode) || [];
        let rcjForDel = rcjsForDel.find(r => r.materialCode == this.mathItem.delRCJCode && r.resQty == this.mathItem.oriResQty)
        if (ObjectUtils.isEmpty(rcjForDel)) {
            rcjForDel = rcjsForDel.find(r => r.materialCode == this.mathItem.delRCJCode)
        }
        if(ObjectUtils.isNotEmpty(rcjForDel)){
            this.conversionService.deleteDeRcjs(this.ctx.constructId, this.ctx.unitId, this.ctx.de.deId, [rcjForDel]);
        }
    }
}

module.exports = Del$xxxMathHandler;