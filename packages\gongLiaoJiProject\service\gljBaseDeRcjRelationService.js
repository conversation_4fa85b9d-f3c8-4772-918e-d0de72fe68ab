const {Service} = require('../../../core');
const {BaseDeRcjRelation2022} = require("../models/BaseDeRcjRelation2022");
const {ObjectUtils} = require('../utils/ObjectUtils');

/**
 * 定额人材机关系表 service
 * @class
 */
class GljBaseDeRcjRelationService extends Service{

    constructor(ctx) {
        super(ctx);
        this.baseDeRcjRelation2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDeRcjRelation2022);
    }

    /**
     * 查询定额人材机
     * @returns {Promise<GsBaseDeRelation[]|Error>}
     * @param sequenceNbr
     */
    async getBySequenceNbr(sequenceNbr) {
        if (ObjectUtils.isEmpty(sequenceNbr)) {
            throw new Error("必传参数定额人材机sequenceNbr为空");
        }

        let deRcjRelation = await this.baseDeRcjRelation2022Dao.findOne({
            where: {sequenceNbr: sequenceNbr}
        });
        return deRcjRelation;
    }

    /**
     * 根据定额id查定额和人材机的关联关系
     * @param deId 定额id
     * @return {Promise<BaseDeRcjRelation[]>}
     */
    async getDeRcjRelationByDeId(deId) {
        let findBy = await this.baseDeRcjRelation2022Dao.findBy({quotaId: deId});
        if (!ObjectUtils.isEmpty(findBy)){
            findBy.forEach(k =>{
                k.resQty =parseFloat(k.resQty);

            })
        }
        return findBy;
    }


    /**
     * 根据人材机 查询所有定额
     * @param params
     * @returns {Promise<ResponseData>}
     */
    async queryBaseDeRcjRelationByRcj(params) {
        let { libraryCode, baseRcjId, page = 1, pageSize = 10 } = params;
        // 计算跳过的条数
        const skip = (page - 1) * pageSize;
        // 分页查询数据和总数
        const [baseDes, total] = await this.baseDeRcjRelation2022Dao.findAndCount({
            where: {
                libraryCode: libraryCode,
                rcjId: baseRcjId
            },
            skip: skip,
            take: pageSize
        });
        return {
            data: baseDes,
            total: total,
            page: page,
            pageSize: pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    }

}

GljBaseDeRcjRelationService.toString = () => '[class GljBaseDeRcjRelationService]';
module.exports = GljBaseDeRcjRelationService;
