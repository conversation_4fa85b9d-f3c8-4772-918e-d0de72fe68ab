const Kind1RuleHandler = require("../../standard_conversion/rule_handler/Kind1RuleHandler");
const {ConversionInfoUtil} = require("../../standard_conversion/util/ConversionInfoUtil");

class Kind6RuleHandler extends Kind1RuleHandler {

    constructor(strategyCtx, rule) {
        super(strategyCtx, rule);
        this.rule.math = this.rule.conversionString;
    }

    async initEffectRCJ(){
        //return this.ctx.deInitialRcjs;
        let deRcjs = await super.initEffectRCJ();
        deRcjs = deRcjs || [];
        return deRcjs.filter(rcj => ConversionInfoUtil.isSzsswxyhzxzxRCJ22(rcj));
    }

    deNameUpdateInfo(rule) {
        return this.rule.conversionExplain;
    }
}

module.exports = Kind6RuleHandler;
