const { Service } = require('../../../core');
const { ObjectUtils } = require('../utils/ObjectUtils');
const { NumberUtil } = require('../../../electron/utils/NumberUtil');
const { Snowflake } = require('../../../electron/utils/Snowflake');
const WildcardMap = require('../core/container/WildcardMap');
const ProjectDomain = require('../domains/ProjectDomain');
const ResourceModel = require('../domains/deProcessor/models/ResourceModel');
const PropertyUtil = require('../domains/utils/PropertyUtil');
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const ResourceConstants = require('../constants/ResourceConstants');
const { ConvertUtil } = require('../../../electron/utils/ConvertUtils');
const BranchProjectLevelConstant = require('../constants/BranchProjectLevelConstant');
const DeTypeConstants = require('../constants/DeTypeConstants');
const EE = require('../../../core/ee');
const CommonConstants = require('../constants/CommonConstants');
const { SqlUtils } = require('../../../electron/utils/SqlUtils');
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const RcjTypeEnum = require('../../../electron/enum/RcjTypeEnum');
const { DeTypeCheckUtil } = require('../domains/utils/DeTypeCheckUtil');
const ZSFeeConstants = require("../constants/ZSFeeConstants");
const DeCommonConstants = require("../constants/DeCommonConstants");
const {ResponseData} = require("../utils/ResponseData");
const {RCJKind} = require("../enums/ConversionSourceEnum");
const ProjectTaxCalculationConstants = require("../constants/ProjectTaxCalculationConstants");
const {BaseRcj2022} = require("../models/BaseRcj2022");
const YGLJOperator = require('../core/tools/fileOperator/YGLJOperator');
const UnitConstructMajorTypeConstants = require('../constants/UnitConstructMajorTypeConstants');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const { PricingGSUtils } = require('../utils/PricingGSUtils');
const AppContext = require('../core/container/APPContext');
const GsProjectSettingEnum = require('../enums/GljProjectSettingEnum');
const CostDeMatchConstants = require('../constants/CostDeMatchConstants');
const Decimal = require('decimal.js');
const GljCheckResultBizMoudleTypeConstants = require("../check/GljCheckResultBizMoudleTypeConstants");


class GljRcjService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   *  增加人材机明细
   * @param deId
   * @param baseRcjModel
   * @param constructId
   * @param singleId
   * @param unitId
   * @return {Promise<string>} 人材机id
   */
  async addRcjData(deId, baseRcjModel, constructId, singleId, unitId, deRowId, rcjId, param={}) {
    let  taxMethod=ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    if (ObjectUtils.isEmpty(deId) || ObjectUtils.isEmpty(baseRcjModel)) {
      // 定额id, baseRcj为空
      return false;
    }
    // let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let deModel =  ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    let rcjDetailKind;
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      if (ObjectUtils.isNotEmpty(rcjList)) {
        if (ObjectUtils.isNotEmpty(rcjList[0].pbs)) {
          rcjDetailKind = rcjList[0]?.pbs[0]?.kind;
        } else {
          rcjDetailKind = rcjList[0].kind;
        }
      }
      if (rcjDetailKind === 2 && baseRcjModel.kind === 5) {

      } else if (baseRcjModel.levelMark != RcjCommonConstants.LEVELMARK || rcjDetailKind != baseRcjModel.kind) {
        return false;
      }
    }
    let alikeRcj = constructProjectRcjs.find(item => item.materialCode === baseRcjModel.materialCode);
    let sequence = Snowflake.nextId();
    let resource = new ResourceModel(deModel.constructId, deModel.unitId, sequence, deRowId, baseRcjModel.kind);
    PropertyUtil.copyProperties(baseRcjModel, resource, ['sequenceNbr']);
    resource.rcjId = baseRcjModel.sequenceNbr;
    resource.kind = baseRcjModel.kind;
    resource.kindSc = baseRcjModel.kindSc;
    resource.transferFactor = baseRcjModel.transferFactor;
    resource.parentId = deModel.sequenceNbr;
    resource.deId = deModel.sequenceNbr;
    resource.deRowId = deModel.sequenceNbr;
    resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
    resource.specification = baseRcjModel.specification;
    resource.producer = null;
    resource.manufactor = null;
    resource.brand = null;
    resource.deliveryLocation = null;
    resource.qualityGrade = null;
    resource.remark = null;
    resource.markSum = ObjectUtils.isEmpty(alikeRcj) ? RcjCommonConstants.MARKSUM_JX : alikeRcj.markSum;
    resource.total = RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
    resource.libraryCode = baseRcjModel.libraryCode;
    resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
    resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
    resource.numLockNum = 0;
    resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
    resource.isTempRemove = deModel.isTempRemove;

    //主材设备类型判断
    if(resource.kind == 4
        || resource.kind == 5){
      deModel.isExistedZcSb = CommonConstants.COMMON_YES;
    }
    let unitRcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    unitRcjList.find(item=>{
      item.materialName ===resource.materialName
      && item.specification===resource.specification
      && item.unit===resource.unit
      && (taxMethod===1?item.baseJournalPrice===resource.baseJournalPrice :item.baseJournalTaxPrice===resource.baseJournalTaxPrice)
      && item.kind===resource.kind
    });
    resource.marketPrice=baseRcjModel.price;

    //工料机含税价格
    resource.baseJournalPrice = baseRcjModel.baseJournalPrice ;
    resource.baseJournalTaxPrice = baseRcjModel.baseJournalTaxPrice ;
    resource.marketPrice = baseRcjModel.baseJournalPrice ;
    resource.marketTaxPrice = baseRcjModel.baseJournalTaxPrice ;
    resource.isDataTaxRate = baseRcjModel.isDataTaxRate ;
    resource.taxRate = baseRcjModel.taxRate ;
    resource.taxRateInit = baseRcjModel.taxRate ;
    resource.taxRateStandard = baseRcjModel.taxRateStandard ;
    this.rcjCalculateOriginalData(resource,baseRcjModel);
    resource.originKind = resource.kind ;
    this.rcjDiffInit(resource);

    resource.isFyrcj = baseRcjModel.isFyrcj ;

    if (ObjectUtil.isNotEmpty(baseRcjModel)) {
      resource.levelMark = baseRcjModel.levelMark;
      baseRcjModel.levelMark =  resource.levelMark ;
    }
    for (let key in RcjTypeEnum) {
      if (RcjTypeEnum[key].code == resource.kind) {
        resource.type=   RcjTypeEnum[key].desc;
      }
    }
    resource.resQty=RcjCommonConstants.DEFAULT_RESQTY;
    resource.initResQty = RcjCommonConstants.DEFAULT_RESQTY;
    resource.originalQty = RcjCommonConstants.DEFAULT_RESQTY;
    resource.originalConsumeQty = RcjCommonConstants.DEFAULT_RESQTY_ONE;
    resource.resQtyFactor = RcjCommonConstants.ORIGINALQTY;
    if (resource.isTempRemove === CommonConstants.COMMON_YES) {
      resource.changeResQty = resource.resQty
    }
    let   parentRcj;
    if(ObjectUtil.isNotEmpty(rcjId)){
      parentRcj = rcjList.find(item=>item.sequenceNbr ===rcjId)
    }else {
      if (!ObjectUtil.isEmpty(resource.levelMark) && resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) {
        await  ProjectDomain.getDomain(constructId).getDeDomain().attachPBs(resource, deModel.constructId, deModel.unitId, deModel.sequenceNbr, resource.sequenceNbr,ProjectDomain.getDomain(constructId).resourceDomain);
        let subSourcePrice ='';
        resource.pbs.forEach(item => {
          item.taxRateInit = item.taxRate;
          item.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
          item.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
          item.markSum = RcjCommonConstants.MARKSUM_JX;
          item.isTempRemove = resource.isTempRemove;
          item.total = RcjCommonConstants.TOTALNUMBER_DEFAULT;
          item.unitId = resource.unitId;
          item.constructId = resource.constructId;
          //工料机
          item.marketPrice = item.baseJournalPrice ;
          item.marketTaxPrice = item.baseJournalTaxPrice ;
          item.deRowId = resource.deRowId;
          item.originalConsumeQty =  item.originalQty;
          this.rcjCalculateOriginalData(item,item)
          for (let key in RcjTypeEnum) {
            if (RcjTypeEnum[key].code == item.kind) {
              item.type=   RcjTypeEnum[key].desc;
            }
          }
          this.processingMarketPrice(item);
          if(ObjectUtils.isNotEmpty(item.sourcePrice)){
            subSourcePrice =  item.sourcePrice;
          }
        });
        resource.sourcePrice = subSourcePrice
      }
    }
    this.processingMarketPrice(resource);
    this.processingDonorMaterial(resource);
    await this.handleSzyhRcj(resource);
    let  constructRcjArray=this.getAllRcj(resource);
    let   rcj =constructRcjArray.find( item=> item.materialCode === resource.materialCode && ObjectUtils.isNotEmpty(item.levelMark));
    if(ObjectUtils.isNotEmpty(rcj)){
      resource.rcjId  =  rcj.rcjId
    }
    await this.service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(resource, true, constructRcjArray);
    if (ObjectUtil.isNotEmpty(parentRcj)) {
      // 浆-商浆-砼-商砼-配比下子项只能新插入材料
      if (parentRcj.kind === 6 || parentRcj.kind === 7 || parentRcj.kind === 8 || parentRcj.kind === 9) {
        if (resource.kind !== 2) {
          return ResponseData.fail("明细材料的费用类别必须与主材料相同，请重新输入！");
        }
      }
      // 非机械类型的二次解析材料下子项中，仅能新增材料费类型的材料
      if (parentRcj.levelMark === ResourceConstants.LEVEL_MARK_PB_CL && resource.kind !== RCJKind.材料) {
        return ResponseData.fail("非机械类型的二次解析材料下子项中，仅能新增材料费类型的材料，请重新输入！");
      }
      if (ObjectUtils.isNotEmpty(parentRcj.pbs)) {
        rcjDetailKind = parentRcj?.pbs[0]?.kind;
      } else {
        rcjDetailKind = parentRcj.kind;
      }
      if (rcjDetailKind === 2 && resource.kind === 5) {

      } else if ((ObjectUtils.isEmpty(rcjDetailKind) ? parentRcj.kind : rcjDetailKind) != resource.kind || resource.levelMark != RcjCommonConstants.LEVELMARK_ZERO) {
        return false;
      }
      delete resource.levelMark;
      resource.parentId = parentRcj.sequenceNbr;
      resource.isTempRemove = parentRcj.isTempRemove;
      // 二次解析材料中插入标准编码段为QTCLF1（其他材料费）时，其单位将调整为“元”
      if (resource.materialCode.includes('QTCLF1')) {
        resource.unit = '元'
      }
      //处理内存 新增
      let  unitAllMemory=this.getRcjMemory(constructId,unitId);
      if(ObjectUtils.isNotEmpty(unitAllMemory)){
        let  memoryRcj = unitAllMemory.find(item=>item.materialCode ===parentRcj.materialCode);
        if(ObjectUtils.isNotEmpty(memoryRcj)){
          let useRcjArray = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
          let  userBeforeRcj= this.service.gongLiaoJiProject.gljRcjCollectService.findAlikeRcjArray(useRcjArray, parentRcj);
          if(userBeforeRcj.length===1){
            memoryRcj.pbs.push(resource);
          }
        }
      }

      parentRcj.pbs.push(resource);
      ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
      await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        await deDomain.updateDe(de);
      }
    } else {
      ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);

      //插入父级人材机，初始化定额人材机名称集合
      let rcjNameObj = {};
      rcjNameObj.sequenceNbr = resource.sequenceNbr;
      rcjNameObj.initMaterialName = resource.materialName;
      rcjNameObj.code = resource.materialCode;
      rcjNameObj.kind = resource.kind;
      let initDeRcjNameList = ObjectUtils.isNotEmpty(deModel.initDeRcjNameList) ? deModel.initDeRcjNameList : [];
      initDeRcjNameList.push(rcjNameObj);
      deModel.initDeRcjNameList = initDeRcjNameList;
    }
    //调用人材机的notify
    await ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId, unitId, deRowId }, false);
    //此处需要处理定额标识
    if(ObjectUtil.isNotEmpty(deModel)){
      DeTypeCheckUtil.checkAndUpdateDeType(deModel,ProjectDomain.getDomain(constructId).ctx);
    }
    try {
    // 处理换算信息
    if (param.isConversionDeal !== true) {
      await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'add', null, null);
    }

      await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        qfMajorType: deModel.costFileCode
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
    return resource;
  }


  /**
   *  粘贴人材机明细
   * @param deId
   * @param baseRcjModels
   * @param constructId
   * @param singleId
   * @param unitId
   * @param deRowId
   * @param rcjId
   * @param param
   * @return {Promise<ResponseData>} 人材机id
   */
  async pasteRcjData(deId, baseRcjModels, constructId, singleId, unitId, deRowId, rcjId, param = {}) {

    // 为挂定额或者没有复制的人材机，则返回
    if (ObjectUtils.isEmpty(deId) || ObjectUtils.isEmpty(baseRcjModels)) {
      return false;
    }

    // 获取当前单位的所有人材机
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    // 获取二级材料，消除粘贴选中颜色标识
    for (let t of constructProjectRcjs) {
      let ts2 = t.pbs;
      t.isTempAdd = false;
      if (ObjectUtils.isNotEmpty(ts2)) {
        ts2.forEach(item => {
              item.isTempAdd = false;
            }
        );
      }
    }

    // 过滤掉二级材料
    let newBaseRcjModels = [];
    for (let baseRcjModel of baseRcjModels) {
      let parent = baseRcjModels.find(item => item.sequenceNbr === baseRcjModel.parentId);
      if (ObjectUtils.isEmpty(parent)) {
        newBaseRcjModels.push(baseRcjModel);
      }
    }

    // 重新添加sequenceNbr和parentId
    for (let baseRcjModel of newBaseRcjModels) {
      let ts2 = baseRcjModel.pbs;
      if (ObjectUtils.isNotEmpty(ts2)) {
        baseRcjModel.sequenceNbr = Snowflake.nextId();
        ts2.forEach(item => {
              item.sequenceNbr = Snowflake.nextId();
              item.parentId = baseRcjModel.sequenceNbr;
            }
        );
      }
    }

    // 获取当前单位、当前定额的人材机
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);

    // 获取当前deId指定的定额
    let deModel = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;

    let rcjDetailKind;
    for (let baseRcjModel of newBaseRcjModels) {
      //人材机数据去除#
      baseRcjModel.materialCode= baseRcjModel.materialCode.replace(/#\d+/g, '');

      if (deModel.isDeResource === CommonConstants.COMMON_YES) {
        if (ObjectUtils.isNotEmpty(rcjList)) {
          if (ObjectUtils.isNotEmpty(rcjList[0].pbs)) {
            rcjDetailKind = rcjList[0]?.pbs[0]?.kind;
          } else {
            rcjDetailKind = rcjList[0].kind;
          }
        }
      }
      let alikeRcj = constructProjectRcjs.find(item => item.materialCode === baseRcjModel.materialCode);
      let sequence = Snowflake.nextId();
      let resource = new ResourceModel(deModel.constructId, deModel.unitId, sequence, deRowId, baseRcjModel.kind);
      PropertyUtil.copyProperties(baseRcjModel, resource, ['sequenceNbr']);
      resource.rcjId = baseRcjModel.sequenceNbr;
      resource.kind = baseRcjModel.kind;
      resource.kindSc = baseRcjModel.kindSc;
      resource.transferFactor = baseRcjModel.transferFactor;
      resource.parentId = deModel.sequenceNbr;
      resource.deId = deModel.sequenceNbr;
      resource.deRowId = deModel.sequenceNbr;
      resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
      resource.specification = baseRcjModel.specification;
      resource.producer = null;
      resource.manufactor = null;
      resource.brand = null;
      resource.deliveryLocation = null;
      resource.qualityGrade = null;
      resource.remark = null;
      resource.markSum = ObjectUtils.isEmpty(alikeRcj) ? RcjCommonConstants.MARKSUM_JX : alikeRcj.markSum;
      resource.total = RcjCommonConstants.TOTALNUMBER_DEFAULT;
      resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
      resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
      resource.libraryCode = baseRcjModel.libraryCode;
      resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
      resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
      resource.numLockNum = 0;
      resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
      resource.isTempRemove = deModel.isTempRemove;
      resource.isTempAdd = true;  // 添加粘贴数据的颜色标识

      // 主材设备类型判断
      if (resource.kind == 4 || resource.kind == 5) {
        deModel.isExistedZcSb = CommonConstants.COMMON_YES;
      }
      resource.marketPrice = baseRcjModel.price;
      //工料机含税价格
      resource.baseJournalPrice = baseRcjModel.baseJournalPrice;
      resource.baseJournalTaxPrice = baseRcjModel.baseJournalTaxPrice;
      resource.marketPrice = baseRcjModel.baseJournalPrice;
      resource.marketTaxPrice = baseRcjModel.baseJournalTaxPrice;
      resource.isDataTaxRate = baseRcjModel.isDataTaxRate;
      resource.taxRate = baseRcjModel.taxRate;
      resource.taxRateInit = baseRcjModel.taxRate;
      resource.taxRateStandard = baseRcjModel.taxRateStandard;
      // 重新评估原始数据
      this.rcjCalculateOriginalData(resource, baseRcjModel);
      resource.originKind = resource.kind;
      // 人材机价差进行初始化
      this.rcjDiffInit(resource);
      resource.isFyrcj = baseRcjModel.isFyrcj;

      // if (ObjectUtil.isNotEmpty(baseRcjModel)) {
      //   resource.levelMark = baseRcjModel.levelMark;
      //   baseRcjModel.levelMark = resource.levelMark;
      // }
      for (let key in RcjTypeEnum) {
        if (RcjTypeEnum[key].code == resource.kind) {
          resource.type = RcjTypeEnum[key].desc;
        }
      }
      resource.resQty = RcjCommonConstants.DEFAULT_RESQTY;
      resource.initResQty = RcjCommonConstants.DEFAULT_RESQTY;
      resource.originalQty = RcjCommonConstants.DEFAULT_RESQTY;
      resource.originalConsumeQty = RcjCommonConstants.DEFAULT_RESQTY_ONE;
      resource.resQtyFactor = RcjCommonConstants.ORIGINALQTY;
      if (resource.isTempRemove === CommonConstants.COMMON_YES) {
        resource.changeResQty = resource.resQty
      }

      let parentRcj;
      // 是否是二级人材机
      if (ObjectUtil.isNotEmpty(rcjId) && deId !== rcjId) {
        parentRcj = rcjList.find(item => item.sequenceNbr === rcjId)
      } else {
        if (!ObjectUtil.isEmpty(resource.levelMark) && resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) {
          await ProjectDomain.getDomain(constructId).getDeDomain().attachPBs(resource, deModel.constructId, deModel.unitId, deModel.sequenceNbr, resource.sequenceNbr, ProjectDomain.getDomain(constructId).resourceDomain);
          let subSourcePrice = '';
          resource.pbs.forEach(item => {
            item.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
            item.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
            item.markSum = RcjCommonConstants.MARKSUM_JX;
            item.isTempRemove = resource.isTempRemove;
            item.total = RcjCommonConstants.TOTALNUMBER_DEFAULT;
            item.unitId = resource.unitId;
            item.constructId = resource.constructId;
            //工料机
            item.marketPrice = item.baseJournalPrice;
            item.marketTaxPrice = item.baseJournalTaxPrice;
            item.deRowId = resource.deRowId;
            this.rcjCalculateOriginalData(item, item)
            for (let key in RcjTypeEnum) {
              if (RcjTypeEnum[key].code == item.kind) {
                item.type = RcjTypeEnum[key].desc;
              }
            }
            // 处理市场价，初始化市场价
            this.processingMarketPrice(item);
            if (ObjectUtils.isNotEmpty(item.sourcePrice)) {
              subSourcePrice = item.sourcePrice;
            }
          });
          resource.sourcePrice = subSourcePrice
        }
      }
      // 处理市场价，初始化市场价
      this.processingMarketPrice(resource);
      // 处理甲供材料
      this.processingDonorMaterial(resource);

      // 获取所有人材机
      let constructRcjArray = this.getAllRcj(resource);
      let rcj = constructRcjArray.find(item => item.materialCode === resource.materialCode && ObjectUtils.isNotEmpty(item.levelMark));
      if (ObjectUtils.isNotEmpty(rcj)) {
        resource.rcjId = rcj.rcjId
      }
      await this.service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(resource, true, constructRcjArray);
      if (ObjectUtil.isNotEmpty(parentRcj)) {
        if (ObjectUtils.isNotEmpty(parentRcj.pbs)) {
          rcjDetailKind = parentRcj?.pbs[0]?.kind;
        } else {
          rcjDetailKind = parentRcj.kind;
        }
        delete resource.levelMark;
        resource.parentId = parentRcj.sequenceNbr;
        resource.isTempRemove = parentRcj.isTempRemove;
        resource.isTempAdd = true;  // 添加粘贴数据的颜色标识

        //处理内存 新增
        let unitAllMemory = this.getRcjMemory(constructId, unitId);
        if (ObjectUtils.isNotEmpty(unitAllMemory)) {
          let memoryRcj = unitAllMemory.find(item => item.materialCode === parentRcj.materialCode);
          if (ObjectUtils.isNotEmpty(memoryRcj)) {
            let useRcjArray = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
            let userBeforeRcj = this.service.gongLiaoJiProject.gljRcjCollectService.findAlikeRcjArray(useRcjArray, parentRcj);
            if (userBeforeRcj.length === 1) {
              memoryRcj.pbs.push(resource);
            }
          }
        }

        parentRcj.pbs.push(resource);
        ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
        await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
        if (parentRcj.isDeResource === 1) {
          let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
          let de = deDomain.getDeById(parentRcj.deId);
          de.deCode = parentRcj.materialCode;
          await deDomain.updateDe(de);
        }
      } else {
        ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
        // //插入父级人材机，初始化定额人材机名称集合
        // let rcjNameObj = {};
        // rcjNameObj.sequenceNbr = resource.sequenceNbr;
        // rcjNameObj.initMaterialName = resource.materialName;
        // rcjNameObj.code = resource.materialCode;
        // rcjNameObj.kind = resource.kind;
        // let initDeRcjNameList = ObjectUtils.isNotEmpty(deModel.initDeRcjNameList) ? deModel.initDeRcjNameList : [];
        // initDeRcjNameList.push(rcjNameObj);
        // deModel.initDeRcjNameList = initDeRcjNameList;
      }
      // 调用人材机的notify，修改定额
      await ProjectDomain.getDomain(constructId).getDeDomain().notify({constructId, unitId, deRowId}, false);
      // 此处需要处理定额标识
      if (ObjectUtil.isNotEmpty(deModel)) {
        DeTypeCheckUtil.checkAndUpdateDeType(deModel, ProjectDomain.getDomain(constructId).ctx);
      }
      try {
        // 处理换算信息
        if (param.isConversionDeal !== true) {
          await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'add', null, null);
        }
        // 更新费用汇总信息
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          unitId: unitId,
          qfMajorType: deModel.costFileCode
        });
      } catch (error) {
        console.error('捕获到异常:', error);
      }
    }
    return deRowId;
  }


  async pasteRcjData2(deId, baseRcjModels, constructId, singleId, unitId, deRowId, rcjId, param = {}) {

    // 为挂定额或者没有复制的人材机，则返回
    if (ObjectUtils.isEmpty(deId) || ObjectUtils.isEmpty(baseRcjModels)) {
      return false;
    }

    // 获取当前单位的所有人材机
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    // 获取二级材料，消除粘贴选中颜色标识
    for (let t of constructProjectRcjs) {
      let ts2 = t.pbs;
      t.isTempAdd = false;
      if (ObjectUtils.isNotEmpty(ts2)) {
        ts2.forEach(item => {
              item.isTempAdd = false;
            }
        );
      }
    }

    // 过滤掉二级材料
    let newBaseRcjModels = [];
    for (let baseRcjModel of baseRcjModels) {
      let parent = baseRcjModels.find(item => item.sequenceNbr === baseRcjModel.parentId);
      if (ObjectUtils.isEmpty(parent)) {
        newBaseRcjModels.push(baseRcjModel);
      }
    }

    // 获取当前单位、当前定额的人材机
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);

    // 获取当前deId指定的定额
    let deModel = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;

    let parentRcj;
    for (let baseRcjModel of newBaseRcjModels) {

      let resource = baseRcjModel;
      resource.sequenceNbr = Snowflake.nextId();
      resource.deId = deModel.sequenceNbr;
      resource.deRowId = deModel.sequenceNbr;
      // 添加粘贴数据的颜色标识
      resource.isTempAdd = true;

      // 暂时存放当前人材机的unitId
      let currentUnitId = resource.unitId;
      // 重新添加sequenceNbr和parentId
      resource.unitId = unitId;
      if (ObjectUtils.isNotEmpty(resource.pbs)) {
        resource.pbs.forEach(item => {
              item.sequenceNbr = Snowflake.nextId();
              item.parentId = resource.sequenceNbr;
              item.unitId = unitId;
            }
        );
      }
      // 判断是否是当前单位的人材机复制
      if (unitId !== currentUnitId) {

        // 判断是否有父级人材机
        if (ObjectUtil.isNotEmpty(rcjId) && deId !== rcjId) {
          parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
          if (ObjectUtils.isNotEmpty(parentRcj)) {
            resource.parentId = parentRcj.sequenceNbr;
          } else {
            resource.parentId = deModel.sequenceNbr;
          }
        } else {
          resource.parentId = deModel.sequenceNbr;
        }

        // 人材机处理,并返回处理后数据（跨单位复制）: 1. 并去除当前数据#序号； 2.获取内存； 3.判断普通人材机内存中是否存在，存在则使用，不存在则内存新增; 4.补充人材机，判断用户人材机内存中是否存在，存在，则不做处理，不存在则内存新增
        await this.service.gongLiaoJiProject.gljRcjCollectService.rcjAndMemoryHandle(constructId, unitId, resource);
        // 处理市场价，初始化市场价
        this.processingMarketPrice(resource);
        if(ObjectUtils.isEmpty(resource.pbs)) {
          ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
        }

        // // 插入人材机
        // ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
        //插入父级人材机，初始化定额人材机名称集合
        // let rcjNameObj = {};
        // rcjNameObj.sequenceNbr = resource.sequenceNbr;
        // rcjNameObj.initMaterialName = resource.materialName;
        // rcjNameObj.code = resource.materialCode;
        // rcjNameObj.kind = resource.kind;
        // let initDeRcjNameList = ObjectUtils.isNotEmpty(deModel.initDeRcjNameList) ? deModel.initDeRcjNameList : [];
        // initDeRcjNameList.push(rcjNameObj);
        // deModel.initDeRcjNameList = initDeRcjNameList;
      } else {

        // 判断是否有父级人材机
        if (ObjectUtil.isNotEmpty(rcjId) && deId !== rcjId) {
          parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
        }
        // 添加父级Id
        if (ObjectUtils.isNotEmpty(parentRcj)) {
          resource.parentId = parentRcj.sequenceNbr;

          //处理内存 新增
          let unitAllMemory = this.getRcjMemory(constructId, unitId);
          if (ObjectUtils.isNotEmpty(unitAllMemory)) {
            let memoryRcj = unitAllMemory.find(item => item.materialCode === parentRcj.materialCode);
            if (ObjectUtils.isNotEmpty(memoryRcj)) {
              let useRcjArray = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
              let userBeforeRcj = this.service.gongLiaoJiProject.gljRcjCollectService.findAlikeRcjArray(useRcjArray, parentRcj);
              if (userBeforeRcj.length === 1) {
                memoryRcj.pbs.push(resource);
              }
            }
          }

          parentRcj.pbs.push(resource);
          ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
          await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
          if (parentRcj.isDeResource === 1) {
            let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
            let de = deDomain.getDeById(parentRcj.deId);
            de.deCode = parentRcj.materialCode;
            await deDomain.updateDe(de);
          }
        } else {
          resource.parentId = deModel.sequenceNbr;
          // 插入人材机
          ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
          //插入父级人材机，初始化定额人材机名称集合
          // let rcjNameObj = {};
          // rcjNameObj.sequenceNbr = resource.sequenceNbr;
          // rcjNameObj.initMaterialName = resource.materialName;
          // rcjNameObj.code = resource.materialCode;
          // rcjNameObj.kind = resource.kind;
          // let initDeRcjNameList = ObjectUtils.isNotEmpty(deModel.initDeRcjNameList) ? deModel.initDeRcjNameList : [];
          // initDeRcjNameList.push(rcjNameObj);
          // deModel.initDeRcjNameList = initDeRcjNameList;
        }
      }

      // 调用人材机的notify，修改定额
      await ProjectDomain.getDomain(constructId).getDeDomain().notify({constructId, unitId, deRowId}, false);
      // 此处需要处理定额标识
      if (ObjectUtil.isNotEmpty(deModel)) {
        DeTypeCheckUtil.checkAndUpdateDeType(deModel, ProjectDomain.getDomain(constructId).ctx);
      }
      try {
        // 处理换算信息
        if (param.isConversionDeal !== true) {
          await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'add', null, null);
        }
        // 更新费用汇总信息
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          unitId: unitId,
          qfMajorType: deModel.costFileCode
        });
      } catch (error) {
        console.error('捕获到异常:', error);
      }
    }
    return deRowId;
  }



  /**
   *  替换人材机
   * @param deId
   * @param baseRcjModel
   * @param constructId
   * @param singleId
   * @param unitId
   * @return {Promise<string>} 人材机id
   */
  async replaceRcjData(deId, baseRcjModel, constructId, singleId, unitId, deRowId, rcjDetailId, rcjId,factor, param= {}) {
    let rcjDetailList = new Array();
    let  taxMethod=ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let precision =  this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    if (rcjList.length === 1 && rcjList[0].isDeResource === 1 && ObjectUtils.isNotEmpty(rcjList[0].pbs)  && singleId !== RcjCommonConstants.RCJ_MERGE_REPLACE) {
      rcjId = rcjList[0].sequenceNbr;
    }
    let original = null;
    if (ObjectUtils.isNotEmpty(rcjId)) {
      let parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
      parentRcj.pbs.forEach(item => {
        if (item.sequenceNbr === rcjDetailId) {
          original = item;
        }
      });
    } else {
      original = rcjList.find(item => item.sequenceNbr === rcjDetailId);
    }

    if (ObjectUtils.isEmpty(deId) || ObjectUtils.isEmpty(baseRcjModel)) {
      // 定额id, baseRcj为空
      return false;
    }
    // let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let deModel =  ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      let rcjDetailKind;
      if (ObjectUtils.isNotEmpty(rcjList)) {
        rcjDetailKind = rcjList[0].kind;
      }
      if ((rcjDetailKind === 7 || rcjDetailKind === 8)) {
        if (baseRcjModel.kind !== 2) {
          return false;
        }
      } else if (rcjDetailKind === 10 && baseRcjModel.kind === 5) {

      } else if (rcjDetailKind === 10 && baseRcjModel.kind === 2) {

      } else if ( rcjDetailKind != baseRcjModel.kind) {
        return false;
      }
    }
    //let nextId = Snowflake.nextId();
    let resource = new ResourceModel(deModel.constructId, deModel.unitId, rcjDetailId, deRowId, baseRcjModel.kind);
    PropertyUtil.copyProperties(baseRcjModel, resource, ['sequenceNbr']);
    let alikeRcj = constructProjectRcjs.find(item => item.materialCode === baseRcjModel.materialCode);
    resource.rcjId = baseRcjModel.sequenceNbr;
    resource.kind = baseRcjModel.kind;
    resource.parentId = deModel.sequenceNbr;
    resource.deRowId = deModel.sequenceNbr;
    resource.deId = deModel.sequenceNbr;
    resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
    resource.specification = baseRcjModel.specification;
    resource.producer = null;
    resource.manufactor = null;
    resource.brand = null;
    resource.deliveryLocation = null;
    resource.qualityGrade = null;
    resource.remark = null;
    resource.markSum = ObjectUtils.isEmpty(alikeRcj) ? RcjCommonConstants.MARKSUM_JX : alikeRcj.markSum;
    resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
    resource.libraryCode = baseRcjModel.libraryCode;
    resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
    resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
    resource.numLockNum = 0;
    resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
    resource.marketPrice = baseRcjModel.baseJournalPrice;
    resource.marketTaxPrice = baseRcjModel.baseJournalTaxPrice;
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    if (ObjectUtil.isNotEmpty(baseRcjModel)) {
      resource.levelMark = baseRcjModel.levelMark;
      baseRcjModel.levelMark = resource.levelMark;
    }
    for (let key in RcjTypeEnum) {
      if (RcjTypeEnum[key].code == resource.kind) {
        resource.type = RcjTypeEnum[key].desc;
      }
    }
    resource.resQty = original.resQty;
    resource.consumerResQty = original.consumerResQty;
    resource.changeResQty = original.changeResQty;
    resource.initResQty = RcjCommonConstants.DEFAULT_RESQTY;
    resource.originalQty = original.originalQty;
    resource.originalConsumeQty = original.originalConsumeQty;
    resource.isTempRemove = original.isTempRemove;

    //标准换算相关继承
    resource.editFromConversion = original.editFromConversion;
    resource.addFromConversionStdRuleId = original.addFromConversionStdRuleId;
    resource.changeResQtyStdIds = original.changeResQtyStdIds;
    resource.changeResQtyCunsumerIds = original.changeResQtyCunsumerIds;

    resource.taxRateInit = baseRcjModel.taxRate ;
    if(ObjectUtils.isNotEmpty(factor)){
      resource.resQty = NumberUtil.multiply(NumberUtil.numberScale(original.resQty,precision.DETAIL.RCJ.resQty), factor);
      resource.resQtyFactor = NumberUtil.numberScale(NumberUtil.divide(resource.resQty,resource.originalConsumeQty), precision.DETAIL.RCJ.resQtyFactor);
      if(resource.isTempRemove  ===  CommonConstants.COMMON_YES){
         let  changeResQty =NumberUtil.numberScale(resource.changeResQty,precision.DETAIL.RCJ.resQty)
        resource.changeResQty  = NumberUtil.multiply(changeResQty, factor);
      }
    }

    if (singleId === RcjCommonConstants.RCJ_MEMORY_REPLACE) {
      resource.rcjId =baseRcjModel.rcjId;
      resource.kind = baseRcjModel.kind;
      resource.ifDonorMaterial = baseRcjModel.ifDonorMaterial;
      resource.specification = baseRcjModel.specification;
      resource.producer = baseRcjModel.producer;
      resource.manufactor = baseRcjModel.manufactor;
      resource.brand = baseRcjModel.brand;
      resource.deliveryLocation = baseRcjModel.deliveryLocation;
      resource.qualityGrade = baseRcjModel.qualityGrade;
      resource.remark = baseRcjModel.remark;
      resource.markSum = baseRcjModel.markSum;
      resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
      resource.ifLockStandardPrice = baseRcjModel.ifLockStandardPrice;
      resource.libraryCode = baseRcjModel.libraryCode;
      resource.sourcePrice = baseRcjModel.sourcePrice;
      resource.isNumLock = baseRcjModel.isNumLock;
      resource.numLockNum = baseRcjModel.numLockNum;
      //resource.rcjDetailEdit = baseRcjModel.rcjDetailEdit;
      //工料机
      resource.baseJournalPrice = baseRcjModel.baseJournalPrice ;
      resource.baseJournalTaxPrice = baseRcjModel.baseJournalTaxPrice ;
      resource.marketPrice = baseRcjModel.marketPrice ;
      resource.marketTaxPrice = baseRcjModel.marketTaxPrice ;
      resource.isDataTaxRate = baseRcjModel.isDataTaxRate ;
      resource.taxRate = baseRcjModel.taxRate ;
      resource.taxRateStandard = baseRcjModel.taxRateStandard ;
      this.rcjCalculateOriginalData(resource,baseRcjModel)
      resource.isFyrcj = baseRcjModel.isFyrcj ;

      resource.initResQty = baseRcjModel.initResQty;
      //copy
      let    pbArray  =new Array();
      if(ObjectUtils.isNotEmpty( baseRcjModel.pbs)){
        baseRcjModel.pbs.forEach(item =>
        {
          let sequence = Snowflake.nextId();
          item.sequenceNbr =sequence;
          item.parentId  =resource.sequenceNbr;
          pbArray.push(ConvertUtil.deepCopy(item))
        } );
      }
      resource.pbs = pbArray;
    }
    //专用于材料合并替换
    if (singleId === RcjCommonConstants.RCJ_MERGE_REPLACE) {
      resource.rcjId = baseRcjModel.rcjId;
      resource.unitId = unitId;
      resource.materialCode = baseRcjModel.materialCode;
      resource.materialName = baseRcjModel.materialName;
      resource.unit = baseRcjModel.unit;
      resource.specification = baseRcjModel.specification;
      resource.ifLockStandardPrice = baseRcjModel.ifLockStandardPrice;
      //工料机
      resource.baseJournalPrice = baseRcjModel.baseJournalPrice ;
      resource.baseJournalTaxPrice = baseRcjModel.baseJournalTaxPrice ;
      resource.marketPrice = baseRcjModel.marketPrice ;
      resource.marketTaxPrice = baseRcjModel.marketTaxPrice ;
      resource.isDataTaxRate = baseRcjModel.isDataTaxRate ;
      resource.taxRate = baseRcjModel.taxRate ;
      resource.taxRateStandard = baseRcjModel.taxRateStandard ;
      this.rcjCalculateOriginalData(resource,baseRcjModel);
      resource.isFyrcj = baseRcjModel.isFyrcj ;

      resource.markSum = baseRcjModel.markSum;
      resource.ifDonorMaterial = baseRcjModel.ifDonorMaterial;
      resource.donorMaterialPrice = baseRcjModel.donorMaterialPrice;
      resource.ifProvisionalEstimate = baseRcjModel.ifProvisionalEstimate;
      resource.producer = baseRcjModel.producer;
      resource.manufactor = baseRcjModel.manufactor;
      resource.brand = baseRcjModel.brand;
      resource.deliveryLocation = baseRcjModel.deliveryLocation;
      resource.qualityGrade = baseRcjModel.qualityGrade;
      resource.kindSc = baseRcjModel.kindSc;
      resource.transferFactor = baseRcjModel.transferFactor;
      resource.isDeResource = original.isDeResource;
      resource.sourcePrice = baseRcjModel.sourcePrice;
    }
    resource.originKind = baseRcjModel.originKind || resource.kind ;
    this.rcjDiffInit(resource);
    await this.handleSzyhRcj(resource);
    let parentRcj;
    if (ObjectUtil.isNotEmpty(rcjId)) {
      parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
    } else {
      if (  (!ObjectUtil.isEmpty(resource.levelMark) && resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) && singleId !== RcjCommonConstants.RCJ_MEMORY_REPLACE){
        await ProjectDomain.getDomain(constructId).getDeDomain().attachPBs(resource, deModel.constructId, deModel.unitId, deModel.sequenceNbr, resource.sequenceNbr,ProjectDomain.getDomain(constructId).resourceDomain);
        resource.pbs.forEach(item => {
          //item.marketPrice = item.dePrice;
          item.marketPrice = item.baseJournalPrice ;
          item.marketTaxPrice = item.baseJournalTaxPrice ;
          item.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
          item.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
          item.markSum = RcjCommonConstants.MARKSUM_JX;
          item.isTempRemove = resource.isTempRemove;
          item.deRowId = resource.deRowId;
          item.unitId = resource.unitId;
          item.constructId = resource.constructId;
          this.rcjCalculateOriginalData(item,item);
          if (singleId === RcjCommonConstants.RCJ_MEMORY_REPLACE) {
            //item.dePrice = item.dePrice;
            item.marketPrice = item.marketPrice;
          }
          for (let key in RcjTypeEnum) {
            if (RcjTypeEnum[key].code == item.kind) {
              item.type = RcjTypeEnum[key].desc;
            }
          }
          this.processingMarketPrice(item);
        });
        //合并材料专用
        if (singleId === RcjCommonConstants.RCJ_MERGE_REPLACE ) {
          let  pbs =new Array();
          if(ObjectUtils.isNotEmpty(baseRcjModel.pbs)){
            baseRcjModel.pbs.forEach(item=>{
              let pb = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(), deRowId, item.kind);
              PropertyUtil.copyProperties(item, pb, ['sequenceNbr']);
              pb.parentId =resource.sequenceNbr;
              pbs.push(pb);
            }) ;
          }
          resource.pbs = pbs;
        }
      }
    }

    this.processingMarketPrice(resource);
    this.processingDonorMaterial(resource);
    this.calculateTax(resource,taxMethod);
    if (ObjectUtil.isNotEmpty(parentRcj)) {
      if ((parentRcj.kind === 7 || parentRcj.kind === 8)) {
        if (resource.kind !== 2) {
          return false;
        }
      } else if (parentRcj.kind === 10 && resource.kind === 5) {

      } else if (parentRcj.kind === 10 && resource.kind === 2) {

      } else if (parentRcj.kind != resource.kind || resource.levelMark != RcjCommonConstants.LEVELMARK_ZERO) {
        return false;
      }
      // 子项材料中主材费只能替换为主材费，设备费只能替换为设备费，材料费只能替换为材料费
      if (original.kind === RCJKind.主材 && resource.kind !== RCJKind.主材) {
        return ResponseData.fail("子项材料中主材费只能替换为主材费，请重新输入！");
      } else if (original.kind === RCJKind.设备 && resource.kind !== RCJKind.设备) {
        return ResponseData.fail("子项材料中设备费只能替换为设备费，请重新输入！");
      } else if (original.kind === RCJKind.材料 && resource.kind !== RCJKind.材料) {
        return ResponseData.fail("子项材料中材料费只能替换为材料费，请重新输入！");
      }
      delete resource.levelMark;
      resource.parentId = parentRcj.sequenceNbr;
      resource.sequenceNbr = Snowflake.nextId();
      parentRcj.pbs.push(resource);
      parentRcj.pbs = parentRcj.pbs.filter(item => item.sequenceNbr !== original.sequenceNbr);
      // if(ObjectUtils.isNotEmpty(factor)){
      //   parentRcj.resQty = original.resQty * factor ;
      // }
      ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
      await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs,  parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        de.specification = parentRcj.specification;
        de.deName = parentRcj.materialName;
        de.unit = parentRcj.unit;
        de.price = parentRcj.dePrice;
        de.marketPrice = parentRcj.marketPrice;
        await deDomain.updateDe(de);
      }
    } else {
      let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
      let de = deDomain.getDeById(resource.deId);
      if (de.type !== DeTypeConstants.DE_TYPE_USER_DE && de.type !== DeTypeConstants.DE_TYPE_USER_RESOURCE && resource.isDeResource !== 1 && singleId !== RcjCommonConstants.RCJ_MERGE_REPLACE) {
        // if ((ObjectUtils.isEmpty(param) || param.source !== CommonConstants.BZHS) && ObjectUtils.isEmpty(param.isConversionDeal)) {
        //   await this.calDeNameByReplaceRcj(de, original, resource);
        // }
        await deDomain.updateDe(de);
      }
      if((resource.isDeResource === 1  ||  ( ObjectUtils.isNotEmpty(original) && original.isDeResource ===1)) && singleId === RcjCommonConstants.RCJ_MERGE_REPLACE){
        de.deCode = resource.materialCode
        de.specification = resource.specification;
        de.deName = resource.materialName;
        de.unit = resource.unit;
        de.price = resource.baseJournalPrice;
        de.marketPrice = resource.marketPrice;
        await deDomain.updateDe(de);
      }
      ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
    }
    await ProjectDomain.getDomain(constructId).getResourceDomain().notify(resource);
    try {
    // 处理换算信息
    if (param.isConversionDeal !== true) {
      let conversionInfo = await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'replace', original, null);
      let devTmp = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
      devTmp.nameSuffixHistory = devTmp.nameSuffixHistory || [];
      devTmp.nameSuffixHistory.push(conversionInfo.conversionNameExplain);
      devTmp.deName = `${devTmp.deName} ${conversionInfo.conversionNameExplain}`;
      let de = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId)
      // let de = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(resource.deId);
      if(de.type !== DeTypeConstants.DE_TYPE_RESOURCE){
        de.deName = `${de.deName} ${conversionInfo.conversionNameExplain}`;
      }

    }

      await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        qfMajorType: deModel.costFileCode
      });

      //联动计算装饰超高人材机数量
      await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
      let deDomain1 = ProjectDomain.getDomain(constructId).getDeDomain();
      await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, deDomain1.getDeById(deId).unitId, deId, "update");
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: singleId,
        constructId: constructId
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
    return true;

  }

  /**
   * 按定额生成主材/设备
   * @param deId
   * @param constructId
   * @param singleId
   * @param unitId
   * @param deRowId
   * @return {Promise<string>} 人材机id
   */
  async supplementRcjDataByDe(deId, constructId, singleId, unitId, deRowId, kind, materialCode) {
    let deModel =  ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    // let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let detail = {
      materialName: deModel.deName,
      materialCode: materialCode || (Number(kind) === 4 ? '补充设备001' : '补充主材001'),
      specification: null,
      unit: deModel.unit?.replace(/^\d+/, ''),
      resQty: this.extractUnit(deModel.unit),
      initResQty: this.extractUnit(deModel.unit),
      totalNumber: this.extractUnit(deModel.unit) * deModel.quantity,
      baseJournalPrice: 0,
      baseJournalTaxPrice: 0,
      marketPrice: 0,
      marketTaxPrice: 0,
      taxRemoval: '',
      taxRate: 0,
      kind: Number(kind)
    };
    if (deModel.isTempRemove === 1) {
      detail.resQty = 0
      detail.changeResQty = this.extractUnit(deModel.unit)
    }
    await this.supplementRcjData(deId, constructId, singleId, unitId, deRowId, detail, null, null);
    deModel.isExistedZcSb = CommonConstants.COMMON_YES;
  }

  extractUnit(str) {
    // 使用正则表达式匹配字符串开头的数字
    const match = str.match(/^\d+/);
    // 如果找到匹配项,返回数字部分
    if (match) {
      return parseFloat(match[0]);
    }
    // 如果没有找到匹配项,返回 NaN
    return 1;
  }

  /**
   * 将主材/设备同步到子目
   * @param deId
   * @param constructId
   * @param singleId
   * @param unitId
   * @param deRowId
   * @return {Promise<string>} 人材机id
   */
  async syncDeDataByRcj(deId, constructId, singleId, unitId, deRowId, content, addType) {
    // let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let deModel =  ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    switch (addType) {
      case 'replace': //替换
        deModel.deName = content;
        break;
      case 'append': //追加
        deModel.deName = deModel.deName + ' ' + content;
        break;
    }
  }

  /**
   *  补充 人材机明细
   * @param deId
   * @param baseRcjModel
   * @param constructId
   * @param singleId
   * @param unitId
   * @return {Promise<string>} 人材机id
   */
  async supplementRcjData(deId, constructId, singleId, unitId, deRowId, detail, rcjId, rcjDetailId, replaceFlag) {
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }

    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    let { materialName, materialCode, specification, unit, resQty, initResQty, totalNumber, changeResQty, baseJournalPrice, marketPrice, kind,taxRate } = detail;
    marketPrice  =  Number (marketPrice);
    resQty =  Number (resQty);
    taxRate  = ObjectUtils.isEmpty(taxRate)? 0: taxRate;
    //精度设置
    // let precision =  this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    // marketPrice = NumberUtil.numberScale(marketPrice,precision.DETAIL.PTRCJZS.marketPrice);
    // taxRate =ObjectUtils.isEmpty(taxRate)? taxRate: NumberUtil.numberScale(taxRate,precision.DETAIL.BCRCJ.taxRate);

    let sequence = Snowflake.nextId();
    // let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let deModel =  ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      let rcjs = rcjList.filter(item => item.deId === deId);
      for (let rcj of rcjs) {
        rcj.pbs?.forEach(item => {
          if (item.sequenceNbr === rcjDetailId) {
            rcjId = item.parentId
          }
        });
        if (ObjectUtils.isNotEmpty(rcjId)) {
          break
        }
      }
    }

    let original = null;
    if (ObjectUtils.isNotEmpty(rcjId)) {
      let parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
      parentRcj?.pbs.forEach(item => {
        if (item.sequenceNbr === rcjDetailId) {
          original = item;
        }
      });
    } else {
      original = rcjList.find(item => item.sequenceNbr === rcjDetailId);
    }
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    let rcjDetailKind;
    if (ObjectUtils.isNotEmpty(rcjList)) {
      if (ObjectUtils.isNotEmpty(rcjList[0].pbs)) {
        rcjDetailKind = rcjList[0]?.pbs[0]?.kind;
      } else {
        rcjDetailKind = rcjList[0].kind;
      }
    }
    if (deModel.isDeResource === CommonConstants.COMMON_YES) {
      if (rcjDetailKind === 10 && kind === 2) { //配比材料 里只能补充材料类型
      } else if (rcjDetailKind != kind) {
        return RcjCommonConstants.ERROR_MESSAGE;
      }
    }

    let resource = new ResourceModel(deModel.constructId, deModel.unitId, replaceFlag === 1 ? rcjDetailId : sequence, deRowId, kind);
    //let resource = new ResourceModel(deModel.constructId, deModel.unitId, sequence, deRowId, kind);
    resource.deRowId = deModel.sequenceNbr;
    resource.kind = kind;
    resource.rcjId = Snowflake.nextId();
    resource.parentId = deModel.sequenceNbr;
    resource.deId = deModel.sequenceNbr;
    resource.materialName = materialName;
    resource.materialCode = materialCode;
    resource.specification = specification;
    resource.unit = unit;
    //工料机
    resource.baseJournalPrice = marketPrice;
    resource.baseJournalTaxPrice = marketPrice;
    resource.marketPrice = marketPrice;
    resource.marketTaxPrice = marketPrice;
    resource.price = marketPrice;
    resource.taxRate = taxRate;
    resource.taxRateInit = taxRate;
    resource.taxRateStandard = taxRate;
    resource.isDataTaxRate = ObjectUtils.isEmpty(taxRate)? 2:1;
    resource.isFyrcj = 1 ;
    resource.resQty = ObjectUtils.isEmpty(resQty)?0:resQty;
    resource.supplementRcjFlag = RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
    let  taxMethod=ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    this.calculateTax(resource,taxMethod);
    resource.baseJournalPrice = resource.marketPrice;
    resource.baseJournalTaxPrice = resource.marketTaxPrice;
    this.rcjCalculateOriginalData(resource,resource);
    resource.originKind = resource.kind;
    this.rcjDiffInit(resource);

    resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
    resource.producer = null;
    resource.manufactor = null;
    resource.brand = null;
    resource.deliveryLocation = null;
    resource.qualityGrade = null;
    resource.remark = null;
    resource.markSum = RcjCommonConstants.MARKSUM_JX;
    resource.totalNumber = totalNumber?totalNumber:RcjCommonConstants.TOTALNUMBER_DEFAULT;
    resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
    resource.libraryCode = deModel.libraryCode;
    resource.initResQty = initResQty?initResQty:RcjCommonConstants.DEFAULT_RESQTY;
    resource.changeResQty = changeResQty;
    resource.originalQty = initResQty?initResQty:RcjCommonConstants.DEFAULT_RESQTY;
    resource.originalConsumeQty = RcjCommonConstants.DEFAULT_RESQTY_ONE;
    resource.resQtyFactor = NumberUtil.numberScale(NumberUtil.divide(resQty,resource.originalConsumeQty), precision.DETAIL.RCJ.resQtyFactor);
    resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
    resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
    resource.numLockNum = 0;
    resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;
    resource.isTempRemove = deModel.isTempRemove;
    if(replaceFlag === 1){
      resource.baseJournalPrice = detail.baseJournalPrice;
      resource.baseJournalTaxPrice = detail.baseJournalTaxPrice;
      resource.marketPrice = detail.marketPrice;
      resource.marketTaxPrice = detail.marketPrice;
      resource.price = detail.price;
      resource.originalQty = detail.originalQty;
      resource.originalConsumeQty = detail.originalConsumeQty;
    }
    await this.handleSzyhRcj(resource);
    for (let key in RcjTypeEnum) {
      if (RcjTypeEnum[key].code == resource.kind) {
        resource.type = RcjTypeEnum[key].desc;
      }
    }
    if(deModel.isTempRemove === CommonConstants.COMMON_YES ){
      resource.isTempRemove = CommonConstants.COMMON_YES;
      resource.changeResQty = resource.resQty;
      resource.resQty = 0;
    }

    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList)) {
      rcjUserList = [];
      businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, rcjUserList);
    }
    this.processingMarketPrice(resource);
    this.processingDonorMaterial(resource);
    let parentRcj;
    if (ObjectUtil.isNotEmpty(rcjId)) {
      parentRcj = rcjList.find(item => item.sequenceNbr === rcjId);
    }
    let rcjNameObj = {};
    rcjNameObj.sequenceNbr = resource.sequenceNbr;
    rcjNameObj.initMaterialName = resource.materialName;
    rcjNameObj.code = resource.materialCode;
    rcjNameObj.kind = resource.kind;
    let initDeRcjNameList = ObjectUtils.isNotEmpty(deModel.initDeRcjNameList) ? deModel.initDeRcjNameList : [];
    initDeRcjNameList.push(rcjNameObj);
    deModel.initDeRcjNameList = initDeRcjNameList;
    if (ObjectUtil.isNotEmpty(parentRcj)) {
      let flag= true;
      if(parentRcj.kind != kind){
        if(parentRcj.kind==10 &&kind ==2){
          flag=false;
        }
        if(  (parentRcj.kind==6 || parentRcj.kind==7 || parentRcj.kind==8 || parentRcj.kind==9)  &&kind ==2){
          flag=false;
        }
      }else {
        flag=false;
      }
      if ((parentRcj.kind === 7 || parentRcj.kind === 8)) {
        if (resource.kind !== 2) {
          return false;
        }
      }
      if ( flag) {
        return false;
      }
      resource.parentId = parentRcj.sequenceNbr;
      if (ObjectUtils.isNotEmpty(rcjDetailId)) {
        parentRcj.pbs = parentRcj.pbs.filter(item => item.sequenceNbr !== rcjDetailId);
      }
      resource.isTempRemove = parentRcj.isTempRemove;
      if (replaceFlag === 1) {
        parentRcj.pbs = parentRcj.pbs.filter(item => item.sequenceNbr !== original.sequenceNbr);
      }
      let deepResource = ConvertUtil.deepCopy(resource);
      rcjUserList.push(deepResource);
      parentRcj.pbs.push(resource);
      ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, parentRcj);
      await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs,  parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        await deDomain.updateDe(de);
      }
      await ProjectDomain.getDomain(constructId).getResourceDomain().notify(parentRcj);
    } else {
      resource.levelMark = RcjCommonConstants.LEVELMARK_ZERO;
      let deepResource = ConvertUtil.deepCopy(resource);
      rcjUserList.push(deepResource);
      ProjectDomain.getDomain(constructId).getResourceDomain().createResource(deModel.unitId, deModel.sequenceNbr, resource);
      await ProjectDomain.getDomain(constructId).getResourceDomain().notify(resource);
      let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
      let de = deDomain.getDeById(resource.deId);
      // if (de.type !== DeTypeConstants.DE_TYPE_USER_DE && de.type !== DeTypeConstants.DE_TYPE_USER_RESOURCE && resource.isDeResource !== 1 && replaceFlag === 1) {
      //   await this.calDeNameByReplaceRcj(de, original, resource);
      // }
    }
    let deepResource2 = ConvertUtil.deepCopy(resource);
    //判定内存中不存在，则放入内存
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap) || objMap.size===0) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    let  memoryRcj=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
    if(ObjectUtils.isNotEmpty(memoryRcj)){
      let  existRcj =await this.service.gongLiaoJiProject.gljRcjCollectService.findAlikeRcj(memoryRcj,deepResource2);
      if(ObjectUtils.isEmpty(existRcj)){
        memoryRcj.push(deepResource2);
      }
    }else {
      let   memoryArray= new Array();
      memoryArray.push(deepResource2)
      objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId , memoryArray );
    }

    try {
    // 处理换算信息
    if(replaceFlag ==  1){
      let conversionInfo = await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'replace', original, null);
      let devTmp = await this.service.gongLiaoJiProject.gljRuleDetailFullService.getStandardConvert(constructId, unitId, deId);
      devTmp.nameSuffixHistory = devTmp.nameSuffixHistory || [];
      devTmp.nameSuffixHistory.push(conversionInfo.conversionNameExplain);
      devTmp.deName = `${devTmp.deName} ${conversionInfo.conversionNameExplain}`;
      let de = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
      de.deName = `${de.deName} ${conversionInfo.conversionNameExplain}`;
    }else{
      await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, resource, 'add', null, null);
    }

      await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        qfMajorType: deModel.costFileCode
      });

      //联动计算装饰超高人材机数量
      await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
      //联动计取安装费
      await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "delete");
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: singleId,
        constructId: constructId
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
    return resource;
  }


  async calDeNameByReplaceRcj(de, original, resource) {
    if (ObjectUtils.isNotEmpty(de.initDeRcjNameList) && de.initDeRcjNameList.length > 0) {
      let initDeRcjNameList = de.initDeRcjNameList;
      let initDeRcjName = initDeRcjNameList.find(o => o.sequenceNbr === resource.sequenceNbr);
      if (ObjectUtils.isNotEmpty(initDeRcjName)) {
        initDeRcjName.replaceMaterialName = " 换为【" + resource.materialName + "】";
        if (resource.materialName === original.materialName || resource.kind == 4 || resource.kind == 5) {
          //如果替换后名称==上一次名称，则取消替换信息、如果替换为主材/设备取消替换信息
          initDeRcjName.replaceMaterialName = "";
        }
        if ((original.kind == 4 || original.kind == 5) && resource.kind != 4 && resource.kind != 5) {
          //之前为主材/设备替换为不是,换算信息为空
          initDeRcjName.initMaterialName = resource.materialName;
          initDeRcjName.replaceMaterialName = "";
        }
        if (original.materialCode.includes("#") || resource.materialCode.includes("#")) {
          //替换前后编码段一致则不显示替换信息
          let indexOld = original.materialCode.indexOf("#");
          let indexNew = resource.materialCode.indexOf("#");
          let oldCode = indexOld >= 0 ? original.materialCode.slice(0, indexOld) : original.materialCode;
          let newCode = indexNew >= 0 ? resource.materialCode.slice(0, indexNew) : resource.materialCode;
          if (oldCode === newCode) {
            initDeRcjName.replaceMaterialName = "";
          }
        }
      } else {
        //编辑定额名称后会清除关联，此时再次替换相当于插入
        initDeRcjName = {};
        initDeRcjName.sequenceNbr = resource.sequenceNbr;
        initDeRcjName.initMaterialName = resource.materialName;
        initDeRcjName.replaceMaterialName = " 换为【" + resource.materialName + "】";
        initDeRcjName.code = resource.materialCode;
        initDeRcjName.kind = resource.kind;
        initDeRcjNameList.push(initDeRcjName);
        de.initDeRcjNameList = initDeRcjNameList;
      }
      await ProjectDomain.getDomain(de.constructId).getDeDomain().updateDe(de);
    }
  }



  /**
   *  通过编码 替换 人材机明细
   * @param deId
   * @param constructId
   * @param singleId
   * @param unitId
   * @param deRowId
   * @return {Promise<string>} 人材机id
   */
  async replaceRcjByCodeData(deId, constructId, singleId, unitId, deRowId, code, rcjDetailId, rcjId, replaceFlag) {
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let baseRcj = this.isMainLibStandRcj(constructId, singleId, unitId, code);
    if (ObjectUtils.isNotEmpty(baseRcj)) {
      return this.replaceRcjData(deId, baseRcj, constructId, singleId, unitId, deRowId, rcjDetailId, rcjId, null, {});
    }
    //查询内存
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap)) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    let unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
    if (ObjectUtils.isNotEmpty(unitMemory) ) {
      let  rcj=unitMemory.find(item => item.materialCode === code);
      if(ObjectUtils.isNotEmpty(rcj)){
        rcj.isDeResource=CommonConstants.COMMON_NO;
        return this.replaceRcjData(deId, rcj, constructId, RcjCommonConstants.RCJ_MEMORY_REPLACE, unitId, deRowId, rcjDetailId, rcjId, null, {});
      }
    }
    //用户rcj
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList)|| rcjUserList.size===0) {
      rcjUserList = [];
      businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ, rcjUserList);
    }
    let rcjUser = rcjUserList.find(item => code === item.materialCode && unitId ===  item.unitId);
    if (ObjectUtils.isNotEmpty(rcjUser)) {
      return this.supplementRcjData(deId, constructId, singleId, unitId, deRowId, rcjUser, rcjId, rcjDetailId, 1);
    }
    return false;
  }


  /**
   * 删除 人材机明细
   * @param deId
   * @param constructId
   * @param unitId
   * @param rcjDetailId
   * @returns {Promise<boolean>}
   */
  async deleteRcjByCodeData(deId, constructId, unitId, rcjDetailId, isCountCost = true, param= {}) {
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    let rcjRes = rcjList.find(item => item.sequenceNbr === rcjDetailId);
    let rcjListDetails = [];
    for (let t of rcjList) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjListDetails.push(item);
            }
        );
      }
    }
    // let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let deModel =  ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    let rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
    if (ObjectUtils.isNotEmpty(rcjRes)) {
        await ProjectDomain.getDomain(constructId).getResourceDomain().removeResource(WildcardMap.generateKey(unitId, deId, rcjDetailId));
        
        // 批量删除临时删除调用此方法，，无需通知，因为临时删除的时候已经通知过
        if (isCountCost) {
          try {
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
              constructId: constructId,
              singleId: '1',
              unitId: unitId,
              qfMajorType: deModel.costFileCode
            });

            //联动计算装饰超高人材机数量
            await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
            //联动计算安装计取费用
            await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deId, "update");
            await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
              unitId: unitId,
              singleId: null,
              constructId: constructId
            });
          } catch (error) {
            console.error('捕获到异常:', error);
          }
        }
        
        // 处理换算信息
        if (param.isConversionDeal !== true) {
          try {
            await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcjRes, 'del', null, null);
          } catch (error) {
            console.error('捕获到异常:', error);
          }
        }
      return true;
    }
    if (ObjectUtils.isNotEmpty(rcjListDetailsPb)) {
      await ProjectDomain.getDomain(constructId).getResourceDomain().removeResourcePb(WildcardMap.generateKey(unitId, deId, rcjListDetailsPb.parentId), rcjDetailId);
      let parentRcj = rcjList.find(item => item.sequenceNbr === rcjListDetailsPb.parentId);
      await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        await deDomain.updateDe(de);
      }
      // 批量删除临时删除调用此方法，，无需通知，因为临时删除的时候已经通知过
      if (isCountCost) {
        try {
          await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: '1',
            unitId: unitId,
            qfMajorType: deModel.costFileCode
          });

          //联动计算装饰超高人材机数量
          await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
          //联动计算安装计取费用
          await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deId, "update");
          await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
            unitId: unitId,
            singleId: null,
            constructId: constructId
          });
        } catch (error) {
          console.error('捕获到异常:', error);
        }
      }
    try{
      // 处理换算信息
      if (param.isConversionDeal !== true) {
        await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcjRes, 'del', null, null);
      }
    } catch (error) {
      console.error('捕获到异常:', error);
    }
      return true;
    }
    if (ObjectUtils.isNotEmpty(rcjListDetailsPb)) {
      await ProjectDomain.getDomain(constructId).getResourceDomain().removeResourcePb(WildcardMap.generateKey(unitId, deId, rcjListDetailsPb.parentId), rcjDetailId);
      let parentRcj = rcjList.find(item => item.sequenceNbr === rcjListDetailsPb.parentId);
      await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
      if (parentRcj.isDeResource === 1) {
        let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
        let de = deDomain.getDeById(parentRcj.deId);
        de.deCode = parentRcj.materialCode;
        await deDomain.updateDe(de);
      }
      // 批量删除临时删除调用此方法，，无需通知，因为临时删除的时候已经通知过
      if (isCountCost) {
        try {
          await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: '1',
            unitId: unitId,
            qfMajorType: deModel.costFileCode
          });

          //联动计算装饰超高人材机数量
          await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
          //联动计算安装计取费用
          await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deId, "update");
          await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
            unitId: unitId,
            singleId: null,
            constructId: constructId
          });
        } catch (error) {
          console.error('捕获到异常:', error);
        }
      }
    try{
      // 处理换算信息
      if (param.isConversionDeal !== true) {
        await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcjListDetailsPb, 'del', null, null);
      }
    } catch (error) {
      console.error('捕获到异常:', error);
    }
      return true;
    }
    return false;
  }


  /**
   * 批量删除 人材机明细
   * @param deId
   * @param constructId
   * @param unitId
   * @param rcjDetailIds
   * @returns {Promise<boolean>}
   */
  async batchDeleteRcjByCodeData(deId, constructId, unitId, rcjDetailIds, isCountCost = true, param= {}) {
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    let rcjDeKey = WildcardMap.generateKey(unitId, deId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
    // let rcjRes = rcjList.find(item => item.sequenceNbr === rcjDetailId);
    let rcjRes = rcjList.filter(item => rcjDetailIds.includes(item.sequenceNbr));
    let rcjListDetails = [];
    for (let t of rcjList) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjListDetails.push(item);
            }
        );
      }
    }
    // let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let deModel =  ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
    // let rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
    let rcjListDetailsPbs = rcjListDetails.filter(item => rcjDetailIds.includes(item.sequenceNbr));
    if (ObjectUtils.isNotEmpty(rcjRes)) {
      for (let rcjDetailId of rcjDetailIds) {
        await ProjectDomain.getDomain(constructId).getResourceDomain().removeResource(WildcardMap.generateKey(unitId, deId, rcjDetailId));
        // 批量删除临时删除调用此方法，，无需通知，因为临时删除的时候已经通知过
        if (isCountCost) {
          try {
            await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
              constructId: constructId,
              singleId: '1',
              unitId: unitId,
              qfMajorType: deModel.costFileCode
            });

            //联动计算装饰超高人材机数量
            await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
            //联动计算安装计取费用
            await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deId, "update");
            await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
              unitId: unitId,
              singleId: null,
              constructId: constructId
            });
          } catch (error) {
            console.error('捕获到异常:', error);
          }
        }
      }
      try {
        // 处理换算信息
        if (param.isConversionDeal !== true) {
          for (let rcjItem of rcjRes) {
            await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcjItem, 'del', null, null);
          }
        }
      } catch (error) {
        console.error('捕获到异常:', error);
      }
      return true;
    }
    if (ObjectUtils.isNotEmpty(rcjListDetailsPbs)) {

      let parentRcjArray = [];
      for(let rcjDetailId of rcjDetailIds) {
        let rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
        await ProjectDomain.getDomain(constructId).getResourceDomain().removeResourcePb(WildcardMap.generateKey(unitId, deId, rcjListDetailsPb.parentId), rcjDetailId);
        let parentRcj = rcjList.find(item => item.sequenceNbr === rcjListDetailsPb.parentId);
        parentRcjArray.push(parentRcj);
      }

      for(let parentRcj of parentRcjArray) {
        await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, parentRcj, true);
        if (parentRcj.isDeResource === 1) {
          let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
          let de = deDomain.getDeById(parentRcj.deId);
          de.deCode = parentRcj.materialCode;
          await deDomain.updateDe(de);
        }
      }
      // 批量删除临时删除调用此方法，，无需通知，因为临时删除的时候已经通知过
      if (isCountCost) {
        try {
          await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: '1',
            unitId: unitId,
            qfMajorType: deModel.costFileCode
          });

          //联动计算装饰超高人材机数量
          await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
          //联动计算安装计取费用
          await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deId, "update");
          await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
            unitId: unitId,
            singleId: null,
            constructId: constructId
          });
        } catch (error) {
          console.error('捕获到异常:', error);
        }
      }
      try{
        // 处理换算信息
        if (param.isConversionDeal !== true) {
          for(let rcjListDetailsPb of rcjListDetailsPbs){
            await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, rcjListDetailsPb, 'del', null, null);
          }
        }
      } catch (error) {
        console.error('捕获到异常:', error);
      }
      return true;
    }
    return false;
  }


  /**
   *  否是住定额库标准人材机
   * @param constructId
   * @param singleId
   * @param unitId
   * @param code
   * @returns {null}
   */
  isMainLibStandRcj(constructId, singleId, unitId, code) {
    let mainLibCode = ProjectDomain.getDomain(constructId).getProjectById(unitId).constructMajorType;
    let sql = 'select * from base_rcj_2022 where library_code = ? and material_code = ? limit 1';
    let res = this.app.gljSqlite3DataSource.prepare(sql).get(mainLibCode, code);
    if (!res) {
      res = null;
    } else {
      res = SqlUtils.convertToModel([res])[0];
    }
    return res;
  }

  /**
   * 获取定额下所有人材机不管是那一级别的定额
   * @param {*} constructId
   * @param {*} unitId
   * @param {*} deRowId
   * @returns
   */
  async getAllRcjDetailByDeId(constructId, unitId, deRowId) {
    let resultList = [];
    let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    let deRow = deDomain.getDeById(deRowId);
    if (ObjectUtils.isEmpty(deRow)) {
      return resultList;
    }
    let deRowIds = [deRowId];
    if (deRow.type === DeTypeConstants.DE_TYPE_DELIST) {

      let childRowList = [deRow];
      deDomain.findChilds(deRow, childRowList, [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE, DeTypeConstants.DE_TYPE_RESOURCE, DeTypeConstants.DE_TYPE_DELIST, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_RESOURCE]);
      childRowList.forEach(item => {
        deRowIds.push(item.rowId);
      });
    }
    deRowIds.forEach(item => {
      let rcjs = deDomain.ctx.resourceMap.findByValueProperty('deRowId', item);
      resultList = resultList.concat(rcjs.filter(item => item.isDeResource === CommonConstants.COMMON_NO));
    });
    return this.rcjSort(resultList);
  }

  /**
   *  获取各层级人材机明细
   * @params  constructId, unitId, deId,type
   * @returns {Promise<void>}
   */
  async getAllRcjDetail(constructId, unitId, deRowId, type) {
    let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

    let deRowObject = ProjectDomain.getDomain(constructId).deDomain.getDeById(deRowId);
    let csxmRowObject = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(deRowId);

    if (ObjectUtils.isEmpty(deRowObject) && ObjectUtils.isEmpty(csxmRowObject)) {
      return [];
    }

    let deTree = [];
    let csxmFlag = false;
    if (ObjectUtils.isNotEmpty(deRowObject)) {
      deTree = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
    } else if (ObjectUtils.isNotEmpty(csxmRowObject)) {
      deTree = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId);
      csxmFlag = true;
    }
    const deTreeIdList = deTree.map(obj => obj.sequenceNbr);

    let deIds = [];
    let rcjDeKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);

    if (ObjectUtils.isNotEmpty(rcjList)) {
      rcjList = rcjList.filter(o => deTreeIdList.includes(o.parentId));
    }

    //预算书、措施项目人材机 不同层级查询明细
    return await this.rcjTypeGroupYssCsxm(type, deRowId, deTree, rcjList, taxCalculationMethod, csxmFlag);
  }


  /**
   * 预算书人材机 不同层级查询
   * @param deRowId
   * @param deTree
   * @param rcjList
   * @param taxCalculationMethod
   * @param csxmFlag
   */
  async rcjTypeGroupYssCsxm(type, deRowId, deTree, rcjList, taxCalculationMethod, csxmFlag) {
    let deIds = [];

    let resultList = new Array();
    if (BranchProjectLevelConstant.top === type) {
      this.groupHandle(rcjList, resultList, taxCalculationMethod);
      return this.rcjSort(resultList);
    }
    if (BranchProjectLevelConstant.fb === type || BranchProjectLevelConstant.zfb === type) {
      let fbrjclist = new Array();
      this.findDeRows(deRowId, deTree, deIds);
      deIds.forEach(deId => {
        fbrjclist = fbrjclist.concat(rcjList.filter(item => item.deRowId === deId));
      });
      this.groupHandle(fbrjclist, resultList, taxCalculationMethod);
      return this.rcjSort(resultList);
    }
    if (BranchProjectLevelConstant.qd === type) {
      if (csxmFlag) {
        let fbrjclist = new Array();
        this.findDeRows(deRowId, deTree, deIds);
        deIds.forEach(deId => {
          fbrjclist = fbrjclist.concat(rcjList.filter(item => item.deRowId === deId));
        });
        this.groupHandle(fbrjclist, resultList, taxCalculationMethod);
      } else {
        resultList = rcjList.filter(item => item.deRowId === deRowId);
      }
      return this.rcjSort(resultList);
    }
    if (BranchProjectLevelConstant.de === type || DeTypeConstants.DE_TYPE_ANZHUANG_FEE === type || DeTypeConstants.DE_TYPE_ZHUANSHI_FEE === type) {
      let de = deTree.find(item => item.deRowId === deRowId);
      if (de.isDeResource == 1) {
        let rcj = rcjList.find(item => item.deRowId === deRowId);
        if (ObjectUtils.isNotEmpty(rcj.pbs) && rcj.levelMark !== RcjCommonConstants.LEVELMARK_ZERO) {
          resultList = rcj.pbs;
        }
      }
      if (de.isDeResource == 0) {
        resultList = rcjList.filter(item => item.deRowId === deRowId);
      }
      return this.rcjSort(resultList);
    }
    if (DeTypeConstants.DE_TYPE_USER_DE === type || DeTypeConstants.DE_TYPE_USER_RESOURCE === type) {
      return this.rcjSort(rcjList.filter(item => item.deRowId === deRowId));
    }
    if (DeTypeConstants.DE_TYPE_RESOURCE === type) {
      let result = rcjList.find(a => a.deRowId === deRowId);
      if (ObjectUtils.isNotEmpty(result) && result.markSum === 1) {
        resultList = result.pbs;
      }
      return this.rcjSort(resultList);
    }
  }


  /**
   * 人材机 排序
   * @param rcj
   */
  rcjSort(rcj) {
    if (ObjectUtils.isEmpty(rcj)) {
      return rcj;
    }
    rcj.sort((a, b) => {
      // if (a.kind === b.kind) {
      //     return b.materialCode.localeCompare(a.materialCode);
      // }
      return a.kind - b.kind;
    });
    let zcRcj = rcj.filter(item => item.kind == 5);
    let sbRcj = rcj.filter(item => item.kind == 4);
    let fzcRcj = rcj.filter(item => item.kind != 5 && item.kind != 4 && item.kind != 3);
    let jxRcj = rcj.filter(item => item.kind == 3);
    return zcRcj.concat(sbRcj).concat(fzcRcj).concat(jxRcj);
  }

  // rcjSort(rcj) {
  //   if (ObjectUtils.isEmpty(rcj)) {
  //     return rcj;
  //   }
  //   rcj.sort((a, b) => {
  //     // if (a.kind === b.kind) {
  //     //     return b.materialCode.localeCompare(a.materialCode);
  //     // }
  //     return a.kind - b.kind;
  //   });
  //   let zcRcj = rcj.filter(item => item.kind == 5);
  //   let sbRcj = rcj.filter(item => item.kind == 4);
  //   let fzcRcj = rcj.filter(item => item.kind == 1 || item.kind == 2 || item.kind == 10);
  //
  //   let sgRcj = rcj.filter(item => item.kind == 6);
  //   let gRcj = rcj.filter(item => item.kind == 7);
  //   let sjRcj = rcj.filter(item => item.kind == 8);
  //   let jRcj = rcj.filter(item => item.kind == 9);
  //   let jxRcj = rcj.filter(item => item.kind == 3);
  //   return zcRcj.concat(sbRcj).concat(fzcRcj).concat(sgRcj).concat(gRcj).concat(jRcj).concat(sjRcj).concat(jxRcj);
  // }

  /**
   * 分组处理并重新封装
   * @param constructRcjArray
   * @param array1
   * @param taxCalculationMethod
   * @returns {Promise<void>}
   */
  async groupHandle(constructRcjArray, array1, taxCalculationMethod) {
    //拼接相同材料
    if (!ObjectUtils.isEmpty(constructRcjArray)) {
      for (let arrayElement of constructRcjArray) {
        arrayElement.tempcol = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.baseJournalPrice) ? arrayElement.baseJournalPrice : '')
            .concat(!ObjectUtils.isEmpty(arrayElement.marketPrice) ? arrayElement.marketPrice : '');

        if (ProjectTaxCalculationConstants.TAX_MODE_0 === taxCalculationMethod) {
          arrayElement.tempcol = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName) ? arrayElement.materialName : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.kind) ? arrayElement.kind : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.specification) ? arrayElement.specification : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.unit) ? arrayElement.unit : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.baseJournalTaxPrice) ? arrayElement.baseJournalTaxPrice : '')
              .concat(!ObjectUtils.isEmpty(arrayElement.marketTaxPrice) ? arrayElement.marketTaxPrice : '');
        }
      }
    } else {
      return null;
    }
    //分组
    const grouped = constructRcjArray.reduce((accumulator, currentValue) => {
      // 将分组作为对象的 key，相同分组的项放入同一个数组
      (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
      return accumulator;
    }, {});

    //循环分组之后的人材机
    for (let group in grouped) {
      if (grouped.hasOwnProperty(group)) {
        let groupedElement = ConvertUtil.deepCopy(grouped[group][0]);
        // let groupedElement = grouped[group][0];
        let totalNumber = 0;
        let allPbs = new Array();
        //let set = new Set();
        grouped[group].forEach(item => {
          totalNumber = NumberUtil.add(totalNumber, item.totalNumber);
          if (ObjectUtils.isNotEmpty(item.pbs) && item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO) {
            allPbs = allPbs.concat(item.pbs);
            let pbsGroup = new Array();
            this.groupHandle(allPbs, pbsGroup, taxCalculationMethod);
            allPbs = pbsGroup;
          }
        });
       // let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(groupedElement.constructId);
       // groupedElement.unitTotalNumber = NumberUtil.numberScale(totalNumber, precision.DETAIL.RCJ.totalNumber);
        if (allPbs.length > 0) {
          groupedElement.pbs = allPbs;
        }
        groupedElement.type = this.service.gongLiaoJiProject.gljRcjCollectService.getRcjTypeEnumDescByCode(groupedElement.kind);
        groupedElement.totalNumber = totalNumber;
        array1.push(groupedElement);
      }
    }
  }

  /**
   * 编辑 人材机明细区
   * @param args
   */
  async updateRcjDetail(args) {
    let { constructId, singleId, unitId, deId, rcjDetailId, constructRcj ,adjustmentCoefficient, deType} = args;
    let  taxMethod=ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let szssMediumRepair = businessMap.get(FunctionTypeConstants.PROJECT_SETTING).get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR);

    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    //let deModel = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId(constructId, unitId, deId);
    let deModel = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    constructProjectRcjs.forEach(item => constructRcjArray.push(item));
    //Array.prototype.push.apply(constructRcjArray,constructProjectRcjs);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    //Array.prototype.push.apply(constructRcjArray,rcjDetailList);
    if (ObjectUtils.isNotEmpty(rcjDetailList)) {
      rcjDetailList.forEach(item => constructRcjArray.push(item));
    }
    let resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetailId);
    let kind = constructRcj.kind;
    let materialName = constructRcj.materialName;
    let specification = constructRcj.specification;
    let unitBj = constructRcj.unit;
    //let resQty = constructRcj.resQty? Number(constructRcj.resQty) : constructRcj.resQty;
    //消耗量
    let resQty = ObjectUtils.isEmpty(constructRcj.resQty) ? null :  Number(eval(constructRcj.resQty)) ;
    // 价
    let marketPrice = ObjectUtils.isEmpty( constructRcj.marketPrice)? constructRcj.marketPrice : parseFloat(constructRcj.marketPrice);
    let marketTaxPrice =  ObjectUtils.isEmpty(constructRcj.marketTaxPrice) ? constructRcj.marketTaxPrice : parseFloat(constructRcj.marketTaxPrice);
    let sourcePrice = constructRcj.sourcePrice;
    //  数量
    let totalNumber = constructRcj.totalNumber? Number(constructRcj.totalNumber) : constructRcj.totalNumber;
    // 税率
    let taxRate = constructRcj.taxRate? Number(constructRcj.taxRate) : constructRcj.taxRate;
    let isNumLock = constructRcj.isNumLock;
    let highlight = ObjectUtils.isEmpty(constructRcj.highlight)? null: constructRcj.highlight;
    let recoverTaxRate =ObjectUtils.isEmpty(constructRcj.recoverTaxRate)?false :constructRcj.recoverTaxRate;
    let ifProvisionalEstimate = constructRcj?.ifProvisionalEstimate;
    //精度处理
    // resQty = ObjectUtils.isEmpty(resQty)? resQty:  NumberUtil.numberScale(resQty, precision.DETAIL.RCJ.resQty);
    // totalNumber = ObjectUtils.isEmpty(totalNumber)? totalNumber: NumberUtil.numberScale(totalNumber, precision.DETAIL.RCJ.totalNumber);
    // marketPrice =ObjectUtils.isEmpty( marketPrice) ? marketPrice : NumberUtil.numberScale(marketPrice, precision.DETAIL.PTRCJZS.marketPrice);
    // marketTaxPrice = ObjectUtils.isEmpty(marketTaxPrice) ?marketTaxPrice : NumberUtil.numberScale(marketTaxPrice, precision.DETAIL.PTRCJZS.marketTaxPrice);
    // taxRate = ObjectUtils.isEmpty(taxRate) ?taxRate : NumberUtil.numberScale(Number(taxRate), precision.DETAIL.RCJ.taxRate);

    //查询修改 对象
    let t = constructRcjArray.find(i => i.sequenceNbr === rcjDetailId);

    //确定 已有编码
    let ts = constructRcjArray.filter(constructRcjArrayElement => !(constructRcjArrayElement.materialCode === t.materialCode
        && constructRcjArrayElement.materialName === t.materialName
        && constructRcjArrayElement.specification === t.specification
        && constructRcjArrayElement.unit === t.unit
    && (taxMethod === RcjCommonConstants.GENERAL_FORWARD?constructRcjArrayElement.baseJournalPrice===t.baseJournalPrice :constructRcjArrayElement.baseJournalTaxPrice===t.baseJournalTaxPrice)
    ));

    if (ObjectUtils.isEmpty(resRcj)) {
      let rcjDetail = rcjDetailList.find(item => item.sequenceNbr === rcjDetailId);
      resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetail.parentId);
    }
    if (!ObjectUtils.isEmpty(t)) {
      let constructRcjArrayElement = t;
      let ifChangeMaterialCode = false;
      let ifChangeMaterialNumber = false;
      let  updateMemory =false ;

      if (ObjectUtils.isNotEmpty(isNumLock)) {
        constructRcjArrayElement.isNumLock = isNumLock;
        if (isNumLock == RcjCommonConstants.ISNUMLOCK) {
          if (resRcj.hasOwnProperty("resQtyConversionLock")) {
            resRcj.resQty = resRcj.resQtyConversionLock;
            await  ProjectDomain.getDomain(constructId).getResourceDomain().notify(resRcj);
            delete resRcj.resQtyConversionLock;
          }else{
            let isTotalNumberChange = (constructRcjArrayElement.totalNumber != constructRcjArrayElement.numLockNum)
            //如果totalNumber&resQty为0 ，恢复其原始消耗量
            if(constructRcjArrayElement.totalNumber == 0){
              constructRcjArrayElement.resQty = constructRcjArrayElement.originalQty;
            }else{
              if (deModel.quantity !== 0) {
                constructRcjArrayElement.resQty = constructRcjArrayElement.numLockNum;
                constructRcjArrayElement.resQtyFactor = NumberUtil.divide(NumberUtil.numberScale(constructRcjArrayElement.resQty,precision.DETAIL.RCJ.resQty),constructRcjArrayElement.originalConsumeQty);
                if(szssMediumRepair === true  && constructRcjArrayElement.isMunicipal){
                  constructRcjArrayElement.resQtyFactor = NumberUtil.divide(constructRcjArrayElement.resQtyFactor,0.9);
                }
               // constructRcjArrayElement.resQty = NumberUtil.divide(NumberUtil.numberScale(constructRcjArrayElement.totalNumber, precision.DETAIL.RCJ.totalNumber),NumberUtil.numberScale( deModel.quantity, precision.EDIT.DE.quantity) );
              }
            }
            //措施项目算系数调整
            if(deModel.isCsxmDe && constructRcjArrayElement.resQty > 0){
              //获取父级定额的调整系数
              let parentDe = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deModel.parentId);
              if(ObjectUtils.isEmpty(constructRcjArrayElement.adjustmentCoefficient) && parentDe.adjustmentCoefficient>1) {
                constructRcjArrayElement.resQty = NumberUtil.multiply(NumberUtil.numberScale(constructRcjArrayElement.resQty, precision.DETAIL.RCJ.resQty),parentDe.adjustmentCoefficient);
              }else if(parentDe.adjustmentCoefficient != constructRcjArrayElement.adjustmentCoefficient){
                constructRcjArrayElement.resQty = NumberUtil.multiply(parentDe.adjustmentCoefficient,NumberUtil.numberScale(constructRcjArrayElement.resQty, precision.DETAIL.RCJ.resQty)/constructRcjArrayElement.adjustmentCoefficient);

              }
              //更新调整系数
              constructRcjArrayElement.adjustmentCoefficient = parentDe.adjustmentCoefficient;
            }
            // if(isTotalNumberChange) {
            //   await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'updateQty', null, null);
            // }
          }
        }
        if (isNumLock == RcjCommonConstants.ISNUMLOCK_TRUE) {
          constructRcjArrayElement.isNumLock = isNumLock;
          constructRcjArrayElement.numLockNum = constructRcjArrayElement.resQty;
        }
      }

      //类型
      if (ObjectUtils.isNotEmpty(kind) && constructRcjArrayElement.kind != kind) {
        let  baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
        if( kind ===2
            && (constructRcjArrayElement.kind === 4 || constructRcjArrayElement.kind === 5)
            && (constructRcjArrayElement.supplementRcjFlag!==RcjCommonConstants.SUPPLEMENT_RCJ_FLAG)
            && (baseRCJ?.kind !== 4 && baseRCJ?.kind !== 5)
        ){
          if(taxMethod === RcjCommonConstants.SIMPLE_REVERSE){
            constructRcjArrayElement.baseJournalPrice=constructRcjArrayElement.baseJournalPriceOriginalReverse;
            constructRcjArrayElement.baseJournalTaxPrice=constructRcjArrayElement.baseJournalTaxPriceOriginalReverse;
          }
          if(taxMethod === RcjCommonConstants.GENERAL_FORWARD){
            constructRcjArrayElement.baseJournalPrice=constructRcjArrayElement.baseJournalPriceOriginalForward;
            constructRcjArrayElement.baseJournalTaxPrice=constructRcjArrayElement.baseJournalTaxPriceOriginalForward;
          }

        }
        constructRcjArrayElement.kind = kind;
        for (let key in RcjTypeEnum) {
          if (RcjTypeEnum[key].code === kind) {
            constructRcjArrayElement.type=   RcjTypeEnum[key].desc;
          }
        }
        ifChangeMaterialCode =true;
        deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
        //主材设备类型判断
        if(constructRcjArrayElement.kind === 4 || constructRcjArrayElement.kind === 5){
          deModel.isExistedZcSb = CommonConstants.COMMON_YES;
          if(constructRcjArrayElement.marketPrice !== constructRcjArrayElement.baseJournalPrice){
            marketPrice = constructRcjArrayElement.marketPrice;
          }
          if(constructRcjArrayElement.marketTaxPrice !== constructRcjArrayElement.baseJournalTaxPrice){
            marketTaxPrice = constructRcjArrayElement.marketTaxPrice;
          }
        }
        // 修改人材机类型后，这条人材机就不算是标准换算修改的了
        constructRcjArrayElement.editFromConversion = undefined;
        this.rcjDiffSwitchType(constructRcjArrayElement);
      }
      if (!ObjectUtils.isEmpty(materialName)) {
        constructRcjArrayElement.materialName = materialName;
        ifChangeMaterialCode =true;
        deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
      }
      if (!ObjectUtils.is_Undefined(specification) && constructRcjArrayElement.specification !=specification) {
        if (specification !=""){
          constructRcjArrayElement.specification = specification;
          deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
        }else {
          constructRcjArrayElement.specification = null;
        }
        ifChangeMaterialCode =true;
      }
      if (!ObjectUtils.isEmpty(unitBj)) {
        constructRcjArrayElement.unit = unitBj;
        ifChangeMaterialCode =true;
        updateMemory =true;
        deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
      }
      if(ObjectUtils.isNotEmpty(resQty)){
        let oldResQty = constructRcjArrayElement.isTempRemove  ===  CommonConstants.COMMON_YES ? constructRcjArrayElement.changeResQty : constructRcjArrayElement.resQty;
        if(constructRcjArrayElement.isTempRemove  ===  CommonConstants.COMMON_YES){
          constructRcjArrayElement.lastResQty = resQty
          constructRcjArrayElement.changeResQty  = resQty;
          constructRcjArrayElement.consumerResQty = resQty;
        }else{
          constructRcjArrayElement.lastResQty = constructRcjArrayElement.resQty
          constructRcjArrayElement.resQtyFactor = NumberUtil.divide(NumberUtil.numberScale(resQty,precision.DETAIL.RCJ.resQty),constructRcjArrayElement.originalConsumeQty);
          constructRcjArrayElement.resQty =NumberUtil.numberScale(NumberUtil.multiply (constructRcjArrayElement.resQtyFactor ,constructRcjArrayElement.originalConsumeQty),precision.DETAIL.RCJ.resQty);
          if(szssMediumRepair === true  && constructRcjArrayElement.isMunicipal){
            constructRcjArrayElement.resQtyFactor = NumberUtil.divide(constructRcjArrayElement.resQtyFactor,0.9);
            constructRcjArrayElement.resQty = NumberUtil.numberScale(resQty,precision.DETAIL.RCJ.resQty);
          }
          //constructRcjArrayElement.resQty = NumberUtil.multiply (NumberUtil.numberScale(constructRcjArrayElement.resQtyFactor,precision.DETAIL.RCJ.resQtyFactor) ,constructRcjArrayElement.originalQty);
          //constructRcjArrayElement.resQty = resQty;
          constructRcjArrayElement.consumerResQty = resQty;
        }
        deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
        if (!constructRcjArrayElement.hasOwnProperty("levelMark")) {
          ifChangeMaterialCode =true;
          ifChangeMaterialNumber =true;
        }else {
          resRcj.updateCalcuTotalNumber = true;
          ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deId, resRcj,false)
          await  ProjectDomain.getDomain(constructId).getResourceDomain().notify(resRcj);
        }
        // 人材机修改消耗量，处理换算信息
        if (constructRcjArrayElement.isNumLock !== true && args.isConversionDeal !== true && adjustmentCoefficient !== true && resQty != oldResQty) {
          await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'updateQty', null, {resQty: oldResQty});
        }
      }

      if (ObjectUtils.isNotEmpty(totalNumber)) {
        let oldResQty = constructRcjArrayElement.resQty;
        constructRcjArrayElement.totalNumber = totalNumber;
        if (!ZSFeeConstants.ZS_RCJ_LIST.includes(resRcj.materialCode)) {
          constructRcjArrayElement.lastResQty = constructRcjArrayElement.resQty
          constructRcjArrayElement.resQtyFactor = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.divide(constructRcjArrayElement.totalNumber, deModel.quantity),constructRcjArrayElement.originalConsumeQty), precision.DETAIL.RCJ.resQtyFactor);
          constructRcjArrayElement.resQty =NumberUtil.multiply (NumberUtil.numberScale(constructRcjArrayElement.resQtyFactor,precision.DETAIL.RCJ.resQtyFactor) ,constructRcjArrayElement.originalConsumeQty);
          if(szssMediumRepair === true  && constructRcjArrayElement.isMunicipal){
            constructRcjArrayElement.resQtyFactor = NumberUtil.divide(constructRcjArrayElement.resQtyFactor,0.9);
            //constructRcjArrayElement.resQty = NumberUtil.multiply (NumberUtil.numberScale(constructRcjArrayElement.resQty,precision.DETAIL.RCJ.resQty),0.9);
          }
          // constructRcjArrayElement.resQty = NumberUtil.divide(NumberUtil.numberScale(constructRcjArrayElement.totalNumber, precision.DETAIL.RCJ.totalNumber), NumberUtil.numberScale( deModel.quantity, precision.EDIT.DE.quantity));
          let t2 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
          if (ObjectUtils.isNotEmpty(t2) && !constructRcjArrayElement.hasOwnProperty('levelMark')) {
            constructRcjArrayElement.resQtyFactor = NumberUtil.numberScale(NumberUtil.divide(NumberUtil.divide(constructRcjArrayElement.totalNumber, t2.totalNumber),constructRcjArrayElement.originalConsumeQty), precision.DETAIL.RCJ.resQtyFactor);
            constructRcjArrayElement.resQty =NumberUtil.multiply (NumberUtil.numberScale(constructRcjArrayElement.resQtyFactor,precision.DETAIL.RCJ.resQtyFactor) ,constructRcjArrayElement.originalConsumeQty);
            if(szssMediumRepair === true  && constructRcjArrayElement.isMunicipal){
              constructRcjArrayElement.resQtyFactor = NumberUtil.divide(constructRcjArrayElement.resQtyFactor,0.9);
              //constructRcjArrayElement.resQty = NumberUtil.multiply (NumberUtil.numberScale(constructRcjArrayElement.resQty,precision.DETAIL.RCJ.resQty),0.9);
            }
            //constructRcjArrayElement.resQty =NumberUtil.numberScale(NumberUtil.multiply (constructRcjArrayElement.resQtyFactor ,constructRcjArrayElement.originalQty),precision.DETAIL.RCJ.resQty);
            // constructRcjArrayElement.resQty = NumberUtil.divide(NumberUtil.numberScale(constructRcjArrayElement.totalNumber, precision.DETAIL.RCJ.totalNumber), NumberUtil.numberScale(t2.totalNumber, precision.DETAIL.RCJ.totalNumber) );
            ifChangeMaterialCode =true;
            ifChangeMaterialNumber =true;
          }
          if(constructRcjArrayElement.totalNumber === 0 && constructRcjArrayElement.isDeResource ===CommonConstants.COMMON_YES){
            constructRcjArrayElement.resQty = 1;
          }
          if(!resRcj.isNumLock && ![DeTypeConstants.DE_TYPE_RESOURCE,DeTypeConstants.DE_TYPE_USER_RESOURCE].includes(deType)) {
            await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'updateQty', null, {resQty:oldResQty});
          }
        }
        //若数量发生变化，可能会导致三材量发生变化
        if (ObjectUtils.isNotEmpty(constructRcjArrayElement.kindSc) && ObjectUtils.isNotEmpty(constructRcjArrayElement.transferFactor)) {
          await this.service.gongLiaoJiProject.gljRcjCollectService.updateOtherProjectScGJ(constructId);
        }
      }

      if (ObjectUtil.isNotEmpty(marketPrice)  &&　 constructRcjArrayElement.marketPrice!== marketPrice ) {
        constructRcjArrayElement.marketPrice = marketPrice;
        constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
        constructRcjArrayElement.highlight = highlight;
        await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,1,constructRcjArray);
        await this.service.gongLiaoJiProject.gljRcjCollectService.updateUnitRcjCellect({
          constructId: constructRcjArrayElement.constructId,
          unitId: constructRcjArrayElement.unitId,
          constructProjectRcj: { marketPrice: constructRcjArrayElement.marketPrice ,sourcePrice: constructRcjArrayElement.sourcePrice, highlight: constructRcjArrayElement.highlight},
          sequenceNbr: constructRcjArrayElement.sequenceNbr
        });
        updateMemory =true ;
      }

      if (ObjectUtil.isNotEmpty(marketTaxPrice) && constructRcjArrayElement.marketTaxPrice !== marketTaxPrice) {
        constructRcjArrayElement.marketTaxPrice = marketTaxPrice;
        constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
        constructRcjArrayElement.highlight = highlight;
        await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,0,constructRcjArray);
        await this.service.gongLiaoJiProject.gljRcjCollectService.updateUnitRcjCellect({
          constructId: constructRcjArrayElement.constructId,
          unitId: constructRcjArrayElement.unitId,
          constructProjectRcj: { marketTaxPrice: constructRcjArrayElement.marketTaxPrice ,sourcePrice: constructRcjArrayElement.sourcePrice, highlight: constructRcjArrayElement.highlight},
          sequenceNbr: constructRcjArrayElement.sequenceNbr
        });
        updateMemory =true ;
      }

      //税率处理
      if(ObjectUtils.isNotEmpty(taxRate) || taxRate ===''){
        constructRcjArrayElement.taxRate = taxRate;
       // this.calculateTax(constructRcjArrayElement,taxMethod);
        await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,taxMethod,constructRcjArray);
        await this.service.gongLiaoJiProject.gljRcjCollectService. updateUnitRcjCellect({
          constructId: constructRcjArrayElement.constructId,
          unitId: constructRcjArrayElement.unitId,
          constructProjectRcj: { taxRate:constructRcjArrayElement.taxRate ,sourcePrice: constructRcjArrayElement.sourcePrice, highlight: constructRcjArrayElement.highlight,recoverTaxRate:recoverTaxRate},
          sequenceNbr: constructRcjArrayElement.sequenceNbr
        });
        updateMemory = true  ;
      }

      // QTCLF1（其他材料费）编码排序影响要素仅包括：类型、规格型号
      if (constructRcjArrayElement.materialCode.includes('QTCLF1')){
        if(ObjectUtils.isNotEmpty(constructRcj.specification) || ObjectUtils.isNotEmpty(constructRcj.kind)){
          ifChangeMaterialCode = true;
        }else {
          ifChangeMaterialCode = false;
        }
      }

      if (!ObjectUtils.isEmpty(ifProvisionalEstimate)) {
        let constructRcjArray = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
        for (let constructRcjItem of constructRcjArray) {
          if (constructRcjItem.materialCode === constructRcjArrayElement.materialCode
              && constructRcjItem.materialName === constructRcjArrayElement.materialName
              && constructRcjItem.specification === constructRcjArrayElement.specification
              && constructRcjItem.unit === constructRcjArrayElement.unit
          ) {
            constructRcjItem.ifProvisionalEstimate = ifProvisionalEstimate;
            if (ifProvisionalEstimate === 1) {
              constructRcjItem.lastSourcePrice = constructRcjItem.sourcePrice;
              constructRcjItem.sourcePrice = "";
              constructRcjItem.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE;
            } else {
              constructRcjItem.sourcePrice = constructRcjItem.lastSourcePrice;
              constructRcjItem.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
            }
            await this.updateDeRcjHandle(constructRcjItem);
          }
          updateMemory = true;
        }
      }

      //修改子编码
      if (ifChangeMaterialCode) {
        let oldRcj = {...constructRcjArrayElement};
        if (constructProjectRcjs.ifDonorMaterial === 1) {
          constructProjectRcjs.donorMaterialNumber = 0;
        }
        deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
        //if (!((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark) ? constructRcjArrayElement.levelMark : ResourceConstants.LEVEL_MARK_NONE_PB) !== ResourceConstants.LEVEL_MARK_NONE_PB && constructRcjArrayElement.markSum === RcjCommonConstants.MARKSUM_JX)) {
        if (!((ObjectUtils.isNotEmpty(constructRcjArrayElement.levelMark) ? constructRcjArrayElement.levelMark : ResourceConstants.LEVEL_MARK_NONE_PB) !== ResourceConstants.LEVEL_MARK_NONE_PB )) {
          //await this.service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCode(constructRcjArrayElement, true, constructRcjArray);
          await this.service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(constructRcjArrayElement, true, constructRcjArray);
          this.processingMarketPrice(constructRcjArrayElement);
        }
        //修二次解析改父
        else {
          //await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChange(constructProjectRcjs, rcjDetailList, constructRcjArrayElement, true);
          await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs, constructRcjArrayElement, true);
        }
        //修改二次解析的子，触发修改父
        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
          let t1 = constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.parentId);
          //await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChange(constructProjectRcjs, rcjDetailList, t1, true);
          await this.service.gongLiaoJiProject.gljRcjCollectService.parentMaterialCodeChangeMemory(constructProjectRcjs,  t1, true);
        }

        if(oldRcj.materialCode != constructRcjArrayElement.materialCode){
          await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'update', null, oldRcj);
        }
      }

      if(updateMemory){
        this.service.gongLiaoJiProject.gljRcjCollectService.updateMemoryRcj(constructRcjArrayElement,unitId);
      }

      if(ifChangeMaterialNumber){
        this.processMemory(constructId, unitId, constructRcjArrayElement,constructProjectRcjs);
      }

    }
    try {
      await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        singleId: singleId,
        unitId: unitId,
        qfMajorType: resRcj.costFileCode
      });
    } catch (error) {
      console.error('捕获到异常:', error);
    }
    if (resRcj.isDeResource === 1) {
      let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
      let de = deDomain.getDeById(resRcj.deId);
      de.deCode = resRcj.materialCode;
      //de.specification = resRcj.specification;
      await deDomain.updateDe(de);
    }
    ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deId, resRcj, false);
    await ProjectDomain.getDomain(constructId).getResourceDomain().notify(resRcj);

    try {
      //联动计算装饰超高人材机数量
      await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
      let deDomain1 = ProjectDomain.getDomain(constructId).getDeDomain();
      await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, deDomain1.getDeById(deId).unitId, deId, "update");
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: singleId,
        constructId: constructId
      });
    } catch (error) {
      console.error("联动计算安装计取费用捕获到异常:", error);
    }
  }

  async updateRcjDetailMarketPrice(args) {
    let { constructId, singleId, unitId, deId, rcjDetailId, constructRcj ,adjustmentCoefficient, deType,baseAllRcj} = args;
    let  taxMethod=ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let szssMediumRepair = businessMap.get(FunctionTypeConstants.PROJECT_SETTING).get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR);
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let deModel = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deId);
    if (ObjectUtils.isEmpty(constructProjectRcjs)) {
      return null;
    }
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    constructProjectRcjs.forEach(item => constructRcjArray.push(item));
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
            item.parentId = t.sequenceNbr;
            rcjDetailList.push(item);
          }
        );
      }
    }
    if (ObjectUtils.isNotEmpty(rcjDetailList)) {
      rcjDetailList.forEach(item => constructRcjArray.push(item));
    }

    //消耗量
    let resQty = constructRcj.resQty? Number(constructRcj.resQty) : constructRcj.resQty;
    // 价
    let marketPrice = ObjectUtils.isEmpty( constructRcj.marketPrice)? constructRcj.marketPrice : parseFloat(constructRcj.marketPrice);
    let marketTaxPrice =  ObjectUtils.isEmpty(constructRcj.marketTaxPrice) ? constructRcj.marketTaxPrice : parseFloat(constructRcj.marketTaxPrice);
    let sourcePrice = constructRcj.sourcePrice;
    let highlight = ObjectUtils.isEmpty(constructRcj.highlight)? null: constructRcj.highlight;
    //查询修改 对象
    let t = constructRcjArray.find(i => i.sequenceNbr === rcjDetailId);
    //确定 已有编码
    let ts = constructRcjArray.filter(constructRcjArrayElement => constructRcjArrayElement.materialCode === t.materialCode);
    if (!ObjectUtils.isEmpty(t)) {
      for( let t1 of ts){
        let resRcj = constructProjectRcjs.find(item => item.sequenceNbr === t1.sequenceNbr);
        if (ObjectUtils.isEmpty(resRcj)) {
          let rcjDetail = rcjDetailList.find(item => item.sequenceNbr === t1.sequenceNbr);
          resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetail.parentId);
        }
        let deId = t1.deId ;
        let constructRcjArrayElement = t1;
        let  updateMemory =false ;
      if(ObjectUtils.isNotEmpty(resQty)){
        let oldResQty = constructRcjArrayElement.isTempRemove  ===  CommonConstants.COMMON_YES ? constructRcjArrayElement.changeResQty : constructRcjArrayElement.resQty;
        if(constructRcjArrayElement.isTempRemove  ===  CommonConstants.COMMON_YES){
          constructRcjArrayElement.lastResQty = resQty
          constructRcjArrayElement.changeResQty  = resQty;
          constructRcjArrayElement.consumerResQty = resQty;
        }else{
          constructRcjArrayElement.lastResQty = constructRcjArrayElement.resQty
          constructRcjArrayElement.resQtyFactor = NumberUtil.divide(NumberUtil.numberScale(resQty,precision.DETAIL.RCJ.resQty),constructRcjArrayElement.originalConsumeQty);
          constructRcjArrayElement.resQty =NumberUtil.numberScale(NumberUtil.multiply (constructRcjArrayElement.resQtyFactor ,constructRcjArrayElement.originalConsumeQty),precision.DETAIL.RCJ.resQty);
          if(szssMediumRepair === true  && constructRcjArrayElement.isMunicipal){
            constructRcjArrayElement.resQtyFactor = NumberUtil.divide(constructRcjArrayElement.resQtyFactor,0.9);
            constructRcjArrayElement.resQty = NumberUtil.numberScale(resQty,precision.DETAIL.RCJ.resQty);
          }
          // constructRcjArrayElement.resQty = resQty;
          constructRcjArrayElement.consumerResQty = resQty;
        }
        deModel.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT_TRUE;
        if (!constructRcjArrayElement.hasOwnProperty("levelMark")) {
        }else {
          resRcj.updateCalcuTotalNumber = true;
          ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deId, resRcj,false)
          await  ProjectDomain.getDomain(constructId).getResourceDomain().notify(resRcj);
        }
        // 人材机修改消耗量，处理换算信息
        if (constructRcjArrayElement.isNumLock !== true && args.isConversionDeal !== true && adjustmentCoefficient !== true && resQty != oldResQty) {
          await this.service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deId, constructRcjArrayElement, 'updateQty', null, {resQty: oldResQty});
        }
      }
      if (ObjectUtil.isNotEmpty(marketPrice)  &&　 constructRcjArrayElement.marketPrice!== marketPrice ) {
        constructRcjArrayElement.marketPrice = marketPrice;
        constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
        constructRcjArrayElement.highlight = highlight;
        //await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,1,constructRcjArray);
        constructRcjArrayElement.donorMaterialPrice = null;
        // 主材和设备，修改市场价，同步定额价
        constructRcjArrayElement.kind = constructRcjArrayElement.kind? Number(constructRcjArrayElement.kind): constructRcjArrayElement.kind
        this.calculateTax(constructRcjArrayElement,1);
        // let  baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
        let  baseRCJ =  baseAllRcj.find(item => item.sequenceNbr===constructRcjArrayElement.rcjId);
        if(ObjectUtils.isEmpty(baseRCJ)){
          let  rcjUserList =this.getUserRcj(constructRcjArrayElement.constructId);
          baseRCJ = rcjUserList.find(item => constructRcjArrayElement.materialCode.replace( /#\d+/g, '') === item.materialCode && constructRcjArrayElement.unitId ===  item.unitId);
        }
        if (this.rcjDiffEstimate(constructRcjArrayElement)) {
          constructRcjArrayElement.baseJournalPrice = constructRcjArrayElement.marketPrice;
          constructRcjArrayElement.baseJournalTaxPrice = constructRcjArrayElement.marketTaxPrice;
        }
        // 市场价来源判定
        if (constructRcjArrayElement.marketTaxPrice === constructRcjArrayElement.baseJournalTaxPriceOriginalReverse && taxMethod === RcjCommonConstants.SIMPLE_REVERSE ) {
          constructRcjArrayElement.sourcePrice = '';
        }
        if (constructRcjArrayElement.marketPrice === constructRcjArrayElement.baseJournalPriceOriginalForward && taxMethod === RcjCommonConstants.GENERAL_FORWARD ) {
          constructRcjArrayElement.sourcePrice = '';
        }
        updateMemory =true ;
      }
      if (ObjectUtil.isNotEmpty(marketTaxPrice) && constructRcjArrayElement.marketTaxPrice !== marketTaxPrice) {
        constructRcjArrayElement.marketTaxPrice = marketTaxPrice;
        constructRcjArrayElement.sourcePrice = ObjectUtils.isEmpty(sourcePrice)? '自行询价': sourcePrice;
        constructRcjArrayElement.highlight = highlight;
        // await this.marketPriceUpdate(constructRcjArrayElement,taxMethod,0,constructRcjArray);
        constructRcjArrayElement.donorMaterialPrice = null;
        // 主材和设备，修改市场价，同步定额价
        constructRcjArrayElement.kind = constructRcjArrayElement.kind? Number(constructRcjArrayElement.kind): constructRcjArrayElement.kind
        this.calculateTax(constructRcjArrayElement,0);
        // let  baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
        let  baseRCJ =  baseAllRcj.find(item => item.sequenceNbr===constructRcjArrayElement.rcjId);
        if(ObjectUtils.isEmpty(baseRCJ)){
          let  rcjUserList =this.getUserRcj(constructRcjArrayElement.constructId);
          baseRCJ = rcjUserList.find(item => constructRcjArrayElement.materialCode.replace( /#\d+/g, '') === item.materialCode && constructRcjArrayElement.unitId ===  item.unitId);
        }
        if (this.rcjDiffEstimate(constructRcjArrayElement)) {
          constructRcjArrayElement.baseJournalPrice = constructRcjArrayElement.marketPrice;
          constructRcjArrayElement.baseJournalTaxPrice = constructRcjArrayElement.marketTaxPrice;
        }
        // 市场价来源判定
        if (constructRcjArrayElement.marketTaxPrice === constructRcjArrayElement.baseJournalTaxPriceOriginalReverse && taxMethod === RcjCommonConstants.SIMPLE_REVERSE ) {
          constructRcjArrayElement.sourcePrice = '';
        }
        if (constructRcjArrayElement.marketPrice === constructRcjArrayElement.baseJournalPriceOriginalForward && taxMethod === RcjCommonConstants.GENERAL_FORWARD ) {
          constructRcjArrayElement.sourcePrice = '';
        }
        updateMemory =true ;
      }
      if(updateMemory){
        this.service.gongLiaoJiProject.gljRcjCollectService.updateMemoryRcj(constructRcjArrayElement,unitId);
      }
      ProjectDomain.getDomain(constructId).getResourceDomain().createResource(unitId, deId, t1, false);
      await ProjectDomain.getDomain(constructId).getResourceDomain().notifyRCj(t1);
      }
    }


  }

  async multiUpdateRcjDetail (args){
    let { constructId, singleId, rcjs } = args;
    if(ObjectUtils.isNotEmpty(rcjs)&& rcjs.length !==0){
      rcjs.forEach(item=>{
        let unitId= item.unitId;
        let deId= item.deId;
        let rcjDetailId= item.rcjDetailId;
        let constructRcj= item.constructRcj;
        let arg ={ constructId, singleId, unitId , deId, rcjDetailId, constructRcj };
        this.updateRcjDetail(arg);
      });
    }
  }

  /**
   *  处理二次解析情况下，内存子 更新total 与 消耗量
   * @param constructId
   * @param unitId
   * @param constructRcjArrayElement
   * @param constructProjectRcjs
   */
  processMemory(constructId, unitId, constructRcjArrayElement,constructProjectRcjs){
    //  1.找父，用父的code匹配出内存中父
    let  parentRcj=constructProjectRcjs.find(item=>item.sequenceNbr ===constructRcjArrayElement.parentId );
    let  unitAllMemory=this.getRcjMemory(constructId,unitId);
    if(ObjectUtils.isNotEmpty(unitAllMemory)){
      let  parentRcjMemory = unitAllMemory.find(item=>item.materialCode ===parentRcj.materialCode);
      let  parentRcjMemorys = unitAllMemory.filter(item=>item.materialCode ===parentRcj.materialCode);
      if(ObjectUtils.isNotEmpty(parentRcjMemory) && ObjectUtils.isNotEmpty(parentRcjMemorys) && parentRcjMemorys.length ===1 ){
        //  2.找出内存中父下的子
        let rcjMemory= parentRcjMemory.pbs.find(item=>item.materialCode ===constructRcjArrayElement.materialCode);
        rcjMemory.resQty= constructRcjArrayElement.resQty;
        rcjMemory.total= constructRcjArrayElement.total;
      }
    }
  }


  /**
   *  处理定额rcj
   * @param constructId
   * @param rcjDetailId
   * @param unitId
   */
  async processDeRcj(constructId, unitId, rcjDetailId) {
    let constructRcjArray = new Array();
    let rcjDetailList = new Array();
    let constructProjectRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    let rcjListHasDetail = constructProjectRcjs.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    constructProjectRcjs.forEach(item => constructRcjArray.push(item));
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    let resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetailId);
    if (ObjectUtils.isEmpty(resRcj)) {
      let rcjDetail = rcjDetailList.find(item => item.sequenceNbr === rcjDetailId);
      resRcj = constructProjectRcjs.find(item => item.sequenceNbr === rcjDetail.parentId);
    }
    let deDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    let de = deDomain.getDeById(resRcj.deId);
    de.materialCode = resRcj.materialCode;
    await deDomain.updateDe(de);
  }


  /**
   * 定额rowId收集
   * @param deRow
   * @param relatedRowIds
   */
  findDeRows(deRowId, deTree, deIds) {
    let arrayDe = deTree.filter(item => item.parentId === deRowId);
    if (ObjectUtils.isNotEmpty(arrayDe) && arrayDe.length > 0) {
      arrayDe.forEach(item => this.findDeRows(item.deRowId, deTree, deIds));
    }
    deIds.push(deRowId);
  }

  /**
   *  处理市场价，初始化市场价
   */
  processingMarketPrice(rcj) {
    let constructRcjArray =this.getAllRcj(rcj);
    let  taxMethod=ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    // 继承锁定数量 && 锁定市场价
    for (let item of constructRcjArray) {
      let code = item.materialCode;
      let sequenceNbr = item.sequenceNbr;
      if (rcj.materialCode === code && rcj.sequenceNbr !==sequenceNbr ) {
       // rcj.isNumLock = item.isNumLock;
        rcj.ifLockStandardPrice = item.ifLockStandardPrice;
        rcj.markSum = item.markSum;
        break;
      }
    }
    // 找出所有符合要求 rcj  明细
    for (let i = 0; i < constructRcjArray.length; i++) {
      let item = constructRcjArray[i];
      let code = item.materialCode;
      if (
          rcj.materialCode === code
          && rcj.kind === item.kind
          && rcj.materialName === item.materialName
          && rcj.specification === item.specification
          && rcj.unit === item.unit
          && rcj.sequenceNbr !== item.sequenceNbr
          && (this.rcjDiffEstimate(item) ? true : (taxMethod===1?rcj.baseJournalPrice===item.baseJournalPrice :rcj.baseJournalTaxPrice===item.baseJournalTaxPrice)
          )
      ) {
        if (this.rcjDiffEstimate(item)) {
          rcj.baseJournalPrice = item.baseJournalPrice;
          rcj.baseJournalTaxPrice = item.baseJournalTaxPrice;
        }
        if(item.markSum === RcjCommonConstants.MARKSUM_BJX && ObjectUtils.isNotEmpty(item.marketPriceCp)){
          rcj.marketPrice = item.marketPriceCp;
          rcj.marketTaxPrice = item.marketTaxPriceCp;
          rcj.sourcePrice = item.sourcePriceCp;
        }else {
          rcj.marketPrice = item.marketPrice;
          rcj.marketTaxPrice = item.marketTaxPrice;
          rcj.baseJournalPrice =  item.baseJournalPrice;
          rcj.baseJournalTaxPrice = item.baseJournalTaxPrice;
          rcj.sourcePrice = item.sourcePrice;
          rcj.taxRateStandard = item.taxRateStandard;
          rcj.taxRate= item.taxRate;
          rcj.isUpdateTaxRate= item.isUpdateTaxRate;
        }
        rcj.highlight = item.highlight;

        rcj.kindSc = item.kindSc;
        rcj.transferFactor = item.transferFactor;
        rcj.ifProvisionalEstimate = item.ifProvisionalEstimate;
        rcj.lastSourcePrice = item.lastSourcePrice;
        //this.calculateTax(rcj,taxMethod);
        break;
      }
    }
    if (rcj.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtil.isNotEmpty(rcj.pbs)) {
      for (let j = 0; j < rcj.pbs; j++) {
        let rcjDetail = rcj.pbs[j];
        constructRcjArray.forEach(item => {
          let code = item.materialCode;
          if (
              rcjDetail.materialCode === code
              && rcjDetail.kind === item.kind
              && rcjDetail.materialName === item.materialName
              && rcjDetail.specification === item.specification
              && rcjDetail.unit === item.unit
              && rcjDetail.sequenceNbr !== item.sequenceNbr
              && (taxMethod===1?rcj.baseJournalPrice===item.baseJournalPrice :rcj.baseJournalTaxPrice===item.baseJournalTaxPrice)
          ) {
            rcjDetail.marketPrice = item.marketPrice;
            rcjDetail.marketTaxPrice = item.marketTaxPrice;
            rcjDetail.baseJournalPrice =  item.marketPrice;
            rcjDetail.baseJournalTaxPrice = item.marketTaxPrice;
            rcjDetail.taxRateStandard = item.taxRateStandard;
            rcjDetail.taxRate= item.taxRate;
            rcjDetail.isUpdateTaxRate= item.isUpdateTaxRate;
            rcjDetail.sourcePrice = item.sourcePrice;
            rcjDetail.highlight = item.highlight;

            rcjDetail.kindSc = item.kindSc;
            rcjDetail.transferFactor = item.transferFactor;
            rcjDetail.ifProvisionalEstimate = item.ifProvisionalEstimate;
            rcjDetail.lastSourcePrice = item.lastSourcePrice;
            // this.calculateTax(rcjDetail,taxMethod);
          }
        });
      }
    }
  }

  processingDonorMaterial(rcj) {
    let rcjDeKey = WildcardMap.generateKey(rcj.unitId) + WildcardMap.WILDCARD;
    let  taxMethod=ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let rcjList = ProjectDomain.getDomain(rcj.constructId).getResourceDomain().getResource(rcjDeKey);
    if (ObjectUtils.isEmpty(rcjList)) {
      return null;
    }
    let rcjDetailList = new Array();
    let constructRcjArray = new Array();
    rcjList.forEach(item => {
      if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs)) {
        item.pbs.forEach(item2 => rcjDetailList.push(item2));
      }
    });
    Array.prototype.push.apply(constructRcjArray, rcjList);
    Array.prototype.push.apply(constructRcjArray, rcjDetailList);
    // 找出所有符合要求 rcj  明细
    for (let i = 0; i < constructRcjArray.length; i++) {
      let item = constructRcjArray[i];
      let code = item.materialCode;
      if (item.materialCode.includes('#')) {
        code = item.materialCode.substring(0, item.materialCode.lastIndexOf('#'));
      }
      if (
          rcj.materialCode === code
          && rcj.kind === item.kind
          && rcj.materialName === item.materialName
          && rcj.specification === item.specification
          && rcj.unit === item.unit
          && (taxMethod===1?rcj.baseJournalPrice===item.baseJournalPrice :rcj.baseJournalTaxPrice===item.baseJournalTaxPrice)
          && rcj.sequenceNbr !== item.sequenceNbr

      ) {
        rcj.updateFalg = ObjectUtils.isEmpty(item.updateFalg) ? 0 : item.updateFalg;
        rcj.ifDonorMaterial = item.ifDonorMaterial;
        rcj.donorMaterialNumber = item.donorMaterialNumber;
        rcj.donorMaterialPrice = item.donorMaterialPrice;
        break;
      }
    }
    if (rcj.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtil.isNotEmpty(rcj.pbs)) {
      for (let j = 0; j < rcj.pbs; j++) {
        let rcjDetail = rcj.pbs[j];
        constructRcjArray.forEach(item => {
          let code = item.materialCode;
          if (item.materialCode.includes('#')) {
            code = item.materialCode.substring(0, item.materialCode.lastIndexOf('#'));
          }
          if (
              rcjDetail.materialCode === code
              && rcjDetail.kind === item.kind
              && rcjDetail.materialName === item.materialName
              && rcjDetail.specification === item.specification
              && rcjDetail.unit === item.unit
              && (taxMethod===1?rcjDetail.baseJournalPrice===item.baseJournalPrice :rcjDetail.baseJournalTaxPrice===item.baseJournalTaxPrice)
              && rcjDetail.sequenceNbr !== item.sequenceNbr
          ) {
            rcjDetail.updateFalg = ObjectUtils.isEmpty(item.updateFalg) ? 0 : item.updateFalg;
            rcjDetail.ifDonorMaterial = item.ifDonorMaterial;
            rcjDetail.donorMaterialNumber = item.donorMaterialNumber;
            rcjDetail.donorMaterialPrice = item.donorMaterialPrice;
          }
        });
      }
    }
    if (rcj.updateFalg === 0 && rcj.ifDonorMaterial === 1) {
      let totalNumber = constructRcjArray.reduce((acc, item) => {
        let code = item.materialCode;
        if (item.materialCode.includes('#')) {
          code = item.materialCode.substring(0, item.materialCode.lastIndexOf('#'));
        }
        if (
            rcj.materialCode === code
            && rcj.kind === item.kind
            && rcj.materialName === item.materialName
            && rcj.specification === item.specification
            && rcj.unit === item.unit
            //&& rcj.dePrice === item.dePrice
            && (taxMethod===1?rcj.baseJournalPrice===item.baseJournalPrice :rcj.baseJournalTaxPrice===item.baseJournalTaxPrice)
        ) {
          return acc + item.totalNumber;
        }
        return acc;
      }, 0);
    }

  }


  /**
   * 临时删除
   * @param {*} deRowId
   * @param {*} rcjDetailId
   */
  async tempRemoveRcjRow(constructId, deRowId, rcjDetailId) {
    try {
      let deRow = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deRowId);
      if (deRow.isTempRemove === CommonConstants.COMMON_YES) {
        return;//父级临时删除，子级不能再临时删除了
      }
      let unitId = deRow.unitId;

      let existsId = false;
      let rcjDeKey = WildcardMap.generateKey(unitId, deRowId) + WildcardMap.WILDCARD;
      let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
      let rcjRes = rcjList.find(item => item.sequenceNbr === rcjDetailId);
      let rcjListDetailsPb = null;
      //定额人材机
      if (ObjectUtils.isNotEmpty(rcjRes)) {
        existsId = true;
        rcjRes.isTempRemove = CommonConstants.COMMON_YES;
        rcjRes.isFirstTempRemove = CommonConstants.COMMON_YES;
        rcjRes.changeResQty = rcjRes.resQty;
        rcjRes.resQty = 0;
        if (rcjRes.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(rcjRes.pbs) && rcjRes.markSum === RcjCommonConstants.MARKSUM_JX) {
          rcjRes.pbs.forEach(item2 => {
            item2.isTempRemove = CommonConstants.COMMON_YES;
            rcjRes.isFirstTempRemove = CommonConstants.COMMON_NO;
            // item2.changeResQty = item2.resQty;
            // item2.resQty = 0;

          });
        }
      } else {

        let rcjListDetails = [];
        for (let t of rcjList) {
          let ts2 = t.pbs;
          if (!ObjectUtils.isEmpty(ts2)) {
            ts2.forEach(item => {
                  item.parentId = t.sequenceNbr;
                  rcjListDetails.push(item);
                }
            );
          }
        }
        rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
        //配比材料
        if (ObjectUtils.isNotEmpty(rcjListDetailsPb)) {
          existsId = true;
          rcjListDetailsPb.isTempRemove = CommonConstants.COMMON_YES;
          rcjListDetailsPb.changeResQty = rcjListDetailsPb.resQty;
          rcjListDetailsPb.resQty = 0;

        }
      }

      if (existsId) {
        await ProjectDomain.getDomain(constructId).getResourceDomain().notify({ constructId, unitId, deRowId });
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          singleId: '1',
          unitId: unitId,
          constructMajorType: ObjectUtils.isNotEmpty(rcjRes) ? rcjRes.libraryCode : rcjListDetailsPb.libraryCode
        });

        //联动计算装饰超高人材机数量
        await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(deRow.constructId, deRow.unitId, true);
        //临时删除，联动计取安装费
        await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, deRow.unitId, deRowId, 'delete');
        await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
          unitId: unitId,
          singleId: null,
          constructId: constructId
        });
      }
    } catch (error) {
      console.error('捕获到异常:', error);
    }
  }

  /**
   * 取消临时删除
   * @param constructId
   * @param {*} deRowId
   * @param {*} rcjDetailId
   * @returns
   */
  async cancelTempRemoveRcjRow(constructId, deRowId, rcjDetailId) {
    try {

      let deRow = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(deRowId);
      if (deRow.isTempRemove === CommonConstants.COMMON_YES) {
        return;
      }
      let unitId = deRow.unitId;
      let existsId = false;
      let rcjDeKey = WildcardMap.generateKey(unitId, deRowId) + WildcardMap.WILDCARD;
      let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
      let rcjRes = rcjList.find(item => item.sequenceNbr === rcjDetailId);
      let rcjListDetailsPb = null;
      if (ObjectUtils.isNotEmpty(rcjRes) && rcjRes.isTempRemove === CommonConstants.COMMON_YES) {
        existsId = true;
        rcjRes.isTempRemove = CommonConstants.COMMON_NO;
        rcjRes.resQty = rcjRes.changeResQty;
        if (rcjRes.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(rcjRes.pbs) && rcjRes.markSum === RcjCommonConstants.MARKSUM_JX) {
          rcjRes.pbs.forEach(item2 => {
            item2.isTempRemove = CommonConstants.COMMON_NO;
            // item2.resQty =item2.changeResQty;
          });
        }
      } else {
        let rcjListDetails = [];
        for (let t of rcjList) {
          let ts2 = t.pbs;
          if (!ObjectUtils.isEmpty(ts2)) {
            ts2.forEach(item => {
                  item.parentId = t.sequenceNbr;
                  rcjListDetails.push(item);
                }
            );
          }
        }

        rcjListDetailsPb = rcjListDetails.find(item => item.sequenceNbr === rcjDetailId);
        //配比材料
        if (ObjectUtils.isNotEmpty(rcjListDetailsPb) && rcjListDetailsPb.isTempRemove === CommonConstants.COMMON_YES) {
          existsId = true;
          rcjListDetailsPb.isTempRemove = CommonConstants.COMMON_NO;
          rcjListDetailsPb.resQty = rcjListDetailsPb.changeResQty;

        }
      }

      if (existsId) {
        await ProjectDomain.getDomain(constructId).getResourceDomain().notify({ constructId, unitId, deRowId });
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          singleId: '1',
          unitId: unitId,
          constructMajorType: ObjectUtils.isNotEmpty(rcjRes) ? rcjRes.libraryCode : rcjListDetailsPb.libraryCode
        });

        //联动计算装饰超高人材机数量
        await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(deRow.constructId, deRow.unitId, true);
        //取消临时删除后，重新计算安装计取数据
        await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, deRow.unitId, deRowId, 'update');
        await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
          unitId: unitId,
          singleId: null,
          constructId: constructId
        });
      }

    } catch (error) {
      console.error('捕获到异常:', error);
    }
  }

  /**
   *
   * @param args
   * @returns {Promise<void>}
   */
  async getDefaultCode2(args) {
    let { constructId, unitId, prefix } = args;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList) ) {
      return '#1';
    } else {
      let result = rcjUserList.filter(item => {
            return prefix ===item.materialCode.replace( /#\d+/g, '') && item.unitId === unitId;
          }
      );
      let  max =await this.service.gongLiaoJiProject.gljRcjCollectService.getMaxNumber(result,{'materialCode':prefix});
      return   '#'+max ;
    }
  }
  async getDefaultCode(args) {
    let { constructId, unitId, prefix } = args;
    let regex = new RegExp(`${prefix}\\d{3}`);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if (ObjectUtils.isEmpty(rcjUserList)) {
      return String(1).padStart(3, '0');
    } else {
      let result = rcjUserList.filter(item => {
            return regex.test(item.materialCode) && item.unitId === unitId;
          }
      );
      let num = String(result.length + 1).padStart(3, '0');
      return this.handleAlike(result, num, result.length + 1, prefix);
    }
  }
  handleAlike(resultList, code, length, type) {
    let relust = resultList.find(item => item.materialCode === (type + code));
    if (ObjectUtils.isNotEmpty(relust)) {
      return this.handleAlike(resultList, String(length + 1).padStart(3, '0'), length + 1, type);
    }
    return String(code).padStart(3, '0');
  }



  getRcjMemory(constructId,unitId){
    //   unitId 为空
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    if (ObjectUtils.isEmpty(objMap)) {
      businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
      objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
    }
    let unitMemory =new Array();
    if(ObjectUtils.isEmpty(unitId)){
      let idArray = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3 && item.scopeFlag === true  ).map(item=>item.sequenceNbr);
      idArray.forEach(item => {
        let  unitMemorySub= objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + item )
        if(ObjectUtils.isNotEmpty(unitMemorySub)){
          unitMemory.push(unitMemorySub);
        }
      });
    }else {
      unitMemory=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
    }

    let unitAllMemory = new Array() ;
    let rcjDetailList = new Array();
    if(ObjectUtils.isEmpty(unitMemory) || unitMemory.length ===0  ){
      return  null
    }
    let rcjListHasDetail = unitMemory.filter(item => item.levelMark !== RcjCommonConstants.LEVELMARK_ZERO);
    for (let t of rcjListHasDetail) {
      let ts2 = t.pbs;
      if (!ObjectUtils.isEmpty(ts2)) {
        ts2.forEach(item => {
              item.parentId = t.sequenceNbr;
              rcjDetailList.push(item);
            }
        );
      }
    }
    unitAllMemory= unitMemory.concat(rcjDetailList);
    return  unitAllMemory;
  }

  getRcjByCode(args){
    let { constructId, unitId,materialCode } = args;
    let memoryRcj = this.getRcjMemory(constructId,unitId);
    if(ObjectUtils.isEmpty(memoryRcj)){
      return null;
    }
    return memoryRcj.find(item=> item.materialCode === materialCode);
  }

  getAllRcj(rcj){
    let rcjDeKey = WildcardMap.generateKey(rcj.unitId) + WildcardMap.WILDCARD;
    let rcjList = ProjectDomain.getDomain(rcj.constructId).getResourceDomain().getResource(rcjDeKey);
    if (ObjectUtils.isEmpty(rcjList)) {
      return [];
    }
    let rcjDetailList = new Array();
    let constructRcjArray = new Array();
    // rcjList = rcjList.filter(item => item.isDeResource !== 1);
    rcjList.forEach(item => {
      if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs)) {
        item.pbs.forEach(item2 => rcjDetailList.push(item2));
      }
    });
    Array.prototype.push.apply(constructRcjArray, rcjList);
    Array.prototype.push.apply(constructRcjArray, rcjDetailList);
    return   constructRcjArray;
  }


  /**
   * 查询内存下拉code
   * @param args
   * @returns {Promise<void>}
   */
  async getMemoryCode(args) {
    let { constructId, unitId,materialCode } = args;
    let  unitAllMemory=this.getRcjMemory(constructId,unitId);
    let  resultArray = new Array();
    materialCode = materialCode.replace(/#\d+/g, '');
    let memoryRcj = await this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId,unitId);

    let params = null ;
    let baseRcjModel =null ;
    if(ObjectUtil.isNotEmpty(memoryRcj)){
      let rcj= memoryRcj.find(item=>  item.materialCode === materialCode.concat('#1') );
      if(ObjectUtils.isNotEmpty(rcj)){
        params = {"materialCode": materialCode,"libraryCode":rcj.libraryCode};
        baseRcjModel = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjByCodeAndLib(params);
      }
      if(ObjectUtil.isEmpty(baseRcjModel)) {
        let  rcjList=this.getUserRcj(constructId);
        baseRcjModel=rcjList.find(item=>item.materialCode ===materialCode && item.unitId === unitId);
        if(ObjectUtils.isEmpty(baseRcjModel)){
          return  [] ;
        }
      }
    }else{
      return  [] ;
    }
    //let params = {"materialCode": materialCode};

    resultArray.push({"materialCode":baseRcjModel.materialCode,"materialName":baseRcjModel.materialName,"specification":baseRcjModel.specification,"unit":baseRcjModel.unit});
    //处理内存
    if(ObjectUtils.isNotEmpty(unitAllMemory)){
      let  rcjMemorys = unitAllMemory.filter(item=>item.materialCode.replace(/#\d+/g, '') ===materialCode);
      if(ObjectUtils.isNotEmpty(rcjMemorys)){
        rcjMemorys.forEach(item => {
          if(materialCode !==item.materialCode){
            resultArray.push({"materialCode":item.materialCode,"materialName":item.materialName,"specification":item.specification,"unit":item.unit});
          }
        });
      }
    }
    return resultArray;
  }

  getUserRcj(constructId){
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let rcjUserList = businessMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    // 放入用戶rcj
    if(ObjectUtils.isEmpty(rcjUserList)||rcjUserList.size===0){
      rcjUserList = new Array();
      businessMap.set(FunctionTypeConstants.PROJECT_USER_RCJ,rcjUserList);
    }
    return  rcjUserList;
  }

  /**
   *    简易  修改含税，使用 逆向计算
   *   一般 修改不含税 ，使用正向
   * @param rcj
   * @param type  修改税率 一般计税  1   简易0   || 修改市场价  市场价 传 1   含税市场价 传 0
   */
  calculateTax(rcj,type){
    //let  taxMethod=ProjectDomain.getDomain(rcj.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    if(rcj.isDataTaxRate===0){
      return null;
    }
    let precision =  this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(rcj.constructId);
    if(type === RcjCommonConstants.GENERAL_FORWARD){
      this.taxHandle(rcj,precision);
    }
    if(type === RcjCommonConstants.SIMPLE_REVERSE){
      this.taxHandleReverse(rcj,precision);
    }
    rcj.total = NumberUtil.multiply( NumberUtil.numberScale(rcj.marketPrice,precision.DETAIL.PTRCJZS.marketPrice) , NumberUtil.numberScale(rcj.totalNumber,precision.DETAIL.PTRCJZS.total) );
    rcj.totalTax =  NumberUtil.multiply( NumberUtil.numberScale(rcj.marketTaxPrice,precision.DETAIL.PTRCJZS.marketTaxPrice) , NumberUtil.numberScale(rcj.totalNumber,precision.DETAIL.PTRCJZS.total));
    rcj.baseJournalTotal =  NumberUtil.multiply( NumberUtil.numberScale(rcj.baseJournalPrice,precision.DETAIL.PTRCJZS.baseJournalPrice), NumberUtil.numberScale(rcj.totalNumber,precision.DETAIL.PTRCJZS.total));
    rcj.baseJournalTotalTax =  NumberUtil.multiply(NumberUtil.numberScale(rcj.baseJournalTaxPrice,precision.DETAIL.PTRCJZS.baseJournalTaxPrice), NumberUtil.numberScale(rcj.totalNumber,precision.DETAIL.PTRCJZS.total));
  }

  taxHandle(rcj,precision){
    if(rcj.isDataTaxRate===2){
      rcj.marketTaxPrice =rcj.marketPrice;
    }
    if(rcj.isDataTaxRate===1){
      // let taxRate= NumberUtil.divide(NumberUtil.numberScale(Number(rcj.taxRate),precision.DETAIL.RCJ.taxRate)  ,100)+1;
      let taxRate= NumberUtil.divide(Number(rcj.taxRate),100)+1;
      rcj.marketTaxPrice = NumberUtil.multiply( NumberUtil.numberScale(rcj.marketPrice,precision.DETAIL.PTRCJZS.marketPrice) ,  taxRate);
    }
    if(this.rcjDiffEstimate(rcj) ){
      rcj.baseJournalPrice =  rcj.marketPrice;
      rcj.baseJournalTaxPrice = rcj.marketTaxPrice;
    }
  }
  taxHandleReverse(rcj,precision){
    if(rcj.isDataTaxRate===2){
      rcj.marketPrice =rcj.marketTaxPrice;
    }
    if(rcj.isDataTaxRate===1){
      let taxRate= NumberUtil.divide( NumberUtil.numberScale(Number(rcj.taxRate),precision.DETAIL.RCJ.taxRate),100)+1;
      rcj.marketPrice = NumberUtil.divide( NumberUtil.numberScale(rcj.marketTaxPrice,precision.DETAIL.PTRCJZS.marketTaxPrice) ,taxRate);
    }
    if(this.rcjDiffEstimate(rcj) ){
      rcj.baseJournalPrice =  rcj.marketPrice;
      rcj.baseJournalTaxPrice = rcj.marketTaxPrice;
    }
  }

  async marketPriceUpdate(constructRcjArrayElement,taxMethod,type,constructRcjArray){
    constructRcjArrayElement.donorMaterialPrice = null;
     // 主材和设备，修改市场价，同步定额价
     constructRcjArrayElement.kind = constructRcjArrayElement.kind? Number(constructRcjArrayElement.kind): constructRcjArrayElement.kind
     this.calculateTax(constructRcjArrayElement,type);
     // 标准编码段为BCRGF、BCJXF、BCCLF、BCZCF、BCSBF 的人材机市场价=定额价
     // if (constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG)
     //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX)
     //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL)
     //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC)
     //     || constructRcjArrayElement.materialCode.includes(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB)
     // ) {
     //   constructRcjArrayElement.baseJournalPrice = constructRcjArrayElement.marketPrice;
     //   constructRcjArrayElement.baseJournalTaxPrice = constructRcjArrayElement.marketTaxPrice;
     //   await this.service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(constructRcjArrayElement, true, constructRcjArray);
     // }
    // let  baseRCJ = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:constructRcjArrayElement.rcjId});
    // if(ObjectUtils.isEmpty(baseRCJ)){
    //   let  rcjUserList =this.getUserRcj(constructRcjArrayElement.constructId);
    //   baseRCJ = rcjUserList.find(item => constructRcjArrayElement.materialCode.replace( /#\d+/g, '') === item.materialCode && constructRcjArrayElement.unitId ===  item.unitId);
    // }

     // 市场价来源判定
    if (constructRcjArrayElement.marketTaxPrice === constructRcjArrayElement.baseJournalTaxPriceOriginalReverse && taxMethod === RcjCommonConstants.SIMPLE_REVERSE ) {
      constructRcjArrayElement.sourcePrice = '';
    }
    if (constructRcjArrayElement.marketPrice === constructRcjArrayElement.baseJournalPriceOriginalForward && taxMethod === RcjCommonConstants.GENERAL_FORWARD ) {
      constructRcjArrayElement.sourcePrice = '';
    }
    if (!constructRcjArrayElement.hasOwnProperty('levelMark')){
      let  parentRcj =constructRcjArray.find(item =>constructRcjArrayElement.parentId ===item.sequenceNbr)
      ProjectDomain.getDomain(constructRcjArrayElement.constructId).getResourceDomain().createResource(constructRcjArrayElement.unitId, constructRcjArrayElement.deId, constructRcjArrayElement, false);
      await ProjectDomain.getDomain(constructRcjArrayElement.constructId).getResourceDomain().notifyRCj(parentRcj);
      if ( taxMethod === RcjCommonConstants.SIMPLE_REVERSE ) {
        if (parentRcj.marketTaxPrice === parentRcj.baseJournalTaxPrice) {
          parentRcj.sourcePrice = '';
        } else {
          if (parentRcj.sourcePrice === "") {
            parentRcj.sourcePrice = "自行询价";
          }
        }
      }
      if (taxMethod === RcjCommonConstants.GENERAL_FORWARD ) {
        if(parentRcj.marketPrice === parentRcj.baseJournalPrice){
          parentRcj.sourcePrice = '';
        }else{
          if(parentRcj.sourcePrice === ""){
            parentRcj.sourcePrice="自行询价";
          }
        }
      }
    }

   }

   rcjCalculateOriginalData(resource,baseRcjModel){
     let precision =  this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(resource.constructId);
     resource.baseJournalPriceOriginalForward = baseRcjModel.baseJournalPrice ;
     resource.baseJournalTaxPriceOriginalReverse = baseRcjModel.baseJournalTaxPrice ;
     if(resource.isDataTaxRate===2 || resource.isDataTaxRate===0 ){
       resource.baseJournalTaxPriceOriginalForward = resource.baseJournalPriceOriginalForward;
       resource.baseJournalPriceOriginalReverse = resource.baseJournalTaxPriceOriginalReverse;
     }
     if(resource.isDataTaxRate===1){
       let taxRate= NumberUtil.divide( NumberUtil.numberScale(Number(resource.taxRate),precision.DETAIL.RCJ.taxRate),100)+1;
       resource.baseJournalTaxPriceOriginalForward = NumberUtil.multiply( NumberUtil.numberScale(resource.baseJournalPriceOriginalForward,precision.DETAIL.PTRCJZS.baseJournalPrice) , taxRate);
       resource.baseJournalPriceOriginalReverse =  NumberUtil.divide(  NumberUtil.numberScale(resource.baseJournalTaxPriceOriginalReverse,precision.DETAIL.PTRCJZS.baseJournalTaxPrice), taxRate) ;
     }

   }

   async dealRcjTaxRateInit(constructId, unitProjects){
    let baseRcjDao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseRcj2022);
        for (let unitProject of unitProjects) {
            let rcjDeKey = WildcardMap.generateKey(unitProject.sequenceNbr) + WildcardMap.WILDCARD;
            let rcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjDeKey);
            if (ObjectUtil.isNotEmpty(rcjList)) {
                rcjList = rcjList.filter(p => undefined === p.taxRateInit || ObjectUtils.isEmpty(p.taxRateStandard));
                for (let rcj of rcjList) {
                    if (ObjectUtil.isEmpty(rcj.taxRateInit)) {
                        if (ObjectUtil.isNotEmpty(rcj.rcjId)) {
                            let dataBaseRcj = await baseRcjDao.findOne({
                                where: {sequenceNbr: rcj.rcjId}
                            });
                            if (ObjectUtil.isNotEmpty(dataBaseRcj)) {
                                rcj.taxRateInit = dataBaseRcj.taxRate;
                            } else {
                              if (rcj.kind == "5") {
                                rcj.taxRateInit = 0;
                              }
                            }
                        } else {
                            let deRow = ProjectDomain.getDomain(constructId).getDeDomain().getDeById(rcj.deRowId);
                            if (deRow.type === DeTypeConstants.DE_TYPE_USER_DE) {
                                //补充定额下人材机：人工费税率置空不能编辑，其他类型置0
                                if (rcj.kind == "1") {
                                    rcj.taxRateInit = null;
                                } else {
                                    rcj.taxRateInit = 0;
                                }
                            }
                            //调整rcj
                            if (['RGFTZ', 'CLFTZ', 'JXFTZ'].includes(rcj.materialCode)) {
                                if (rcj.kind == 1) {
                                    rcj.taxRateInit = null;
                                } else {
                                    rcj.taxRateInit = 0;
                                }
                            }
                        }
                    }

                  if (ObjectUtil.isEmpty(rcj.taxRateStandard)) {
                    if (ObjectUtil.isNotEmpty(rcj.rcjId)) {
                      let dataBaseRcj = await baseRcjDao.findOne({
                        where: {sequenceNbr: rcj.rcjId}
                      });
                      if (ObjectUtil.isNotEmpty(dataBaseRcj)) {
                        rcj.taxRateStandard = dataBaseRcj.taxRateStandard;
                      }
                    } else if (rcj.materialCode.startsWith('补充')) {
                      rcj.taxRateStandard = rcj.taxRate;
                    }
                  }

                }
            }
        }
   }

  /**
   *  造价系数调整 锁定查询
   * @param args
   * type  价格 1  量 2
   * @returns {Promise<void>}
   */
  async getRcjCoefficientLock(args){
    let { constructId, unitList} = args;
    let  result =null ;
    let rcjList = [] ;
    let   rcjs = null;
    // let rcjListOrg=ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray().filter(item=>unitList.includes(item.unitId));
    // let rcjList = ConvertUtil.deepCopy(rcjListOrg);
    // if(ObjectUtils.isEmpty(rcjList)){
    //   return   result;
    // }
    for (const unitID of unitList) {
      let args ={ levelType:3, kind:0, constructId:constructId, singleId:null, unitId:unitID , isShowAnnotations:false}
      rcjs = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(args);
      if(ObjectUtils.isNotEmpty(rcjs)){
        rcjList = rcjList.concat(rcjs);

      }
    }
    //  过滤费用人材机
    rcjList = rcjList.filter(item =>item.isFyrcj !==0
      && item.materialCode !== DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
      && item.materialCode !== DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
      && item.materialCode !== DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ
    );
    //  过滤临时删除人材机
    rcjList = rcjList.filter(item => item.isTempRemove !== CommonConstants.COMMON_YES);
    //处理编码
    rcjList.forEach(item => item.materialCode =item.materialCode.replace( /#\d+/g, ''));
    //机械费 组合
    rcjList.sort((a, b) => a.kind - b.kind || a.materialCode.localeCompare(b.materialCode));
    // 非父级材料_非补充
    let ts_z_fbc = rcjList.filter(i => !(i.levelMark === '1' || i.levelMark === '2') && i.supplementRcjFlag !== 1);
    // 非父级材料_补充
    let ts_z_bc = rcjList.filter(i => !(i.levelMark === '1' || i.levelMark === '2') && i.supplementRcjFlag === 1);
    // 父级材料
    let ts_f = rcjList.filter(i => i.levelMark === '1' || i.levelMark === '2');
    // 父级_不勾选
    let ts_f_0 = ts_f.filter(i => i.markSum === 0);
    // 父级_勾选
    let ts_f_1 = ts_f.filter(i => i.markSum === 1);
    result = ts_z_fbc
    .concat(ts_z_bc)
    .concat(ts_f_0)
    .concat(ts_f_1)
    ;
    return  result;

  }


  /**
   *  造价系数调整 锁定保存
   * @param args
   * @returns {Promise<void>}
   */
  async saveRcjCoefficientLock(args){
    let { constructId, priceList,priceCoe ,resQtyList,resQtyCoe } = args;
    let { rg,cl,jx,zc,sb } = priceCoe;
    //let { rg,cl,jx,zc,sb } = resQtyCoe;
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    if(ObjectUtils.isNotEmpty(priceList)){
      businessMap.set(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_PRICE,priceList);
      businessMap.set(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_PRICE_VALUE,priceCoe);
    }
    if(ObjectUtils.isNotEmpty(resQtyList)){
      businessMap.set(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_QTY,resQtyList);
      businessMap.set(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_QTY_VALUE,resQtyCoe);
    }
  }


  _calPrice(rcj,value, orgPrice ){
    let { rg,cl,jx,zc,sb } = value;
    let  price =orgPrice;
    if(ObjectUtils.isNotEmpty(rg) && rcj.kind === 1){
      price = NumberUtil.multiply(orgPrice,rg);
    }
    if(ObjectUtils.isNotEmpty(cl) && (rcj.kind === 2 || rcj.kind === 7 || rcj.kind === 8 ||  rcj.kind === 9 ||  rcj.kind === 10 )){
      price = NumberUtil.multiply(orgPrice,cl);
    }
    if(ObjectUtils.isNotEmpty(jx) && rcj.kind === 3){
      price =NumberUtil.multiply(orgPrice,jx);
    }
    if(ObjectUtils.isNotEmpty(zc) && rcj.kind === 5){
      price = NumberUtil.multiply(orgPrice,zc);
    }
    if(ObjectUtils.isNotEmpty(sb) && rcj.kind === 4){
      price =NumberUtil.multiply(orgPrice,sb);
    }
    return  price ;
  }


  /**
   *  造价系数调整 修改
   * @param args
   * @returns {Promise<void>}
   */
  async changeRcjCoefficient(args){
    let { constructId ,unitList} = args;
    let  taxMethod=ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    // 价格获取
    let   priceRcjs =businessMap.get(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_PRICE);
    let  priceRcjsValue = businessMap.get(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_PRICE_VALUE);
    // 消耗量获取
    let   qtyRcjs =businessMap.get(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_QTY);
    let  qtyRcjsValue = businessMap.get(FunctionTypeConstants.RCJ_COEFFICIENT_LOCK_QTY_VALUE);
    let priceRcjsDeSet  =new  Set();
    let qtyRcjsDeSet  =new  Set();
    let  baseAllRcj = await this.service.gongLiaoJiProject.gljBaseRcjService.queryAllRcjId();
    if(ObjectUtils.isNotEmpty(priceRcjs)){
      for (let i = 0; i < priceRcjs.length; i++) {
        let rcj = priceRcjs[i];
        if(taxMethod  === RcjCommonConstants.SIMPLE_REVERSE){
          let marketTaxPrice = NumberUtil.numberScale(rcj.marketTaxPrice, precision.DETAIL.PTRCJZS.marketTaxPrice);
          let price = this._calPrice(rcj,priceRcjsValue,marketTaxPrice);
          if(rcj.marketTaxPrice !== price){
            let constructRcj = {marketTaxPrice:price};
            let  args = { constructId, singleId: null, unitId:rcj.unitId, deId:rcj.deId, rcjDetailId:rcj.sequenceNbr, constructRcj ,adjustmentCoefficient:null,deType:null,baseAllRcj:baseAllRcj}
            await this.updateRcjDetailMarketPrice(args) ;
            priceRcjsDeSet.add({unitId:rcj.unitId,deRowId:rcj.deRowId});
          }
        }
        if(taxMethod  === RcjCommonConstants.GENERAL_FORWARD){
          let marketPrice = NumberUtil.numberScale(rcj.marketPrice, precision.DETAIL.PTRCJZS.marketPrice);
          let price = this._calPrice(rcj,priceRcjsValue,marketPrice);
          if(rcj.marketPrice !== price){
            let constructRcj = {marketPrice:price};
            let  args = { constructId, singleId: null, unitId:rcj.unitId, deId:rcj.deId, rcjDetailId:rcj.sequenceNbr, constructRcj ,adjustmentCoefficient:null,deType:null,baseAllRcj:baseAllRcj}
            await this.updateRcjDetailMarketPrice(args) ;
            priceRcjsDeSet.add({unitId:rcj.unitId,deRowId:rcj.deRowId});
          }
        }
      }
    }

    if(ObjectUtils.isNotEmpty(qtyRcjs)){
      for (let i = 0; i < qtyRcjs.length; i++) {
        let rcj = qtyRcjs[i];

        //二次解析，子级人次机不参与
        let filter = qtyRcjs.filter(p => p.sequenceNbr === rcj.parentId);
        if (ObjectUtils.isNotEmpty(filter)) {
          continue;
        }

        let qty = NumberUtil.numberScale(rcj.resQty, precision.DETAIL.RCJ.resQty);
        let resQty = this._calPrice(rcj,qtyRcjsValue,qty);
        if(rcj.resQty !== resQty){
          let constructRcj = {resQty:resQty};
          let  args = { constructId, singleId: null, unitId:rcj.unitId, deId:rcj.deId, rcjDetailId:rcj.sequenceNbr, constructRcj ,adjustmentCoefficient:null,deType:null,baseAllRcj:baseAllRcj}
          await this.updateRcjDetailMarketPrice(args) ;
          qtyRcjsDeSet.add({unitId:rcj.unitId,deRowId:rcj.deRowId});
        }
      }
    }
    // for( let de of priceRcjsDeSet ){
    //   await  ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId, unitId: de.unitId,deRowId :de.deRowId  },false);
    // }
    // for( let de of qtyRcjsDeSet ){
    //   await  ProjectDomain.getDomain(constructId).getDeDomain().notify({ constructId,unitId: de.unitId,deRowId :de.deRowId  },false);
    // }
    let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    for (let i = 0; i < unitList.length; i++) {
      let    unitId=unitList[i];
      deBaseDomain.notifyAll(constructId,unitId);
      try {
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          singleId: null,
          unitId: unitId,
          qfMajorType: null
        });
      } catch (error) {
        console.error('捕获到异常:', error);
      }
    }

  }


  async getCostPreview(args) {
    let {constructId, priceList, priceCoe, resQtyList, resQtyCoe, isPreview, unitList} = args;
    let project = ProjectDomain.getDomain(constructId);
    //预览的工程项目复制一份
    let copyConstruct = ConvertUtil.deepCopy(project);
    let newId = Snowflake.nextId();
    //复制并刷新内存中id
    copyConstruct = await this._updatePropertyValue(copyConstruct, newId);

    let oldProjectTree = ProjectDomain.getDomain(constructId).getProjectTree();
    //处理调价之前的数据
    for (let i = 0; i < oldProjectTree.length; i++) {
      let item = oldProjectTree[i];
      if(item.type === 1){
        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // 获取该单项下的所有单位
        let unitProjectsByConstruct = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
        // 整个项目的总工程造价值
        let constructCost = 0;
        for (let j = 0; j < unitProjectsByConstruct.length; j++) {
          let unitProject = unitProjectsByConstruct[j];
          // 获取该单位的费用汇总
          let param = {
            constructId: constructId,
            singleId: unitProject.parentId,
            unitId: unitProject.sequenceNbr
          }
          let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
          constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
        }
        item.beforePrice = constructCost;
        item.afterPrice = constructCost;
        item.difference = 0;
      }
      if(item.type === 2){
        let singlePrice = 0;

        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(item.sequenceNbr);
        // 获取该单项下的所有单位
        let unitProjectsByConstruct = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);

        for (let j = 0; j < unitProjectsByConstruct.length; j++) {
          let unitProject = unitProjectsByConstruct[j];
          // 获取该单位的费用汇总
          let param = {
            constructId: constructId,
            singleId: unitProject.parentId,
            unitId: unitProject.sequenceNbr
          }
          let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
          singlePrice = NumberUtil.add(singlePrice, unitCostSummaryPriceMap.get("工程造价"));
        }

        item.beforePrice = singlePrice;
        item.afterPrice = singlePrice;
        item.difference = 0;
      }
      if(item.type === 3){
        let unitCostSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
          .get(item.sequenceNbr + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL);
        if(ObjectUtils.isNotEmpty(unitCostSummaryArray)){
          let price = unitCostSummaryArray.filter(s=>s.name==='工程造价')[0].price;
          item.beforePrice = price;
          item.afterPrice = price;
          item.difference = 0;
        }
      }
    }

    if(isPreview === 1){
      //点击预览
      return await this.setAfterPrice(oldProjectTree, copyConstruct, newId, priceList, priceCoe, resQtyList, resQtyCoe, unitList);
    }else{
      //组装好map, 删除内存中的新项目
      AppContext.removeContext(newId);
      return oldProjectTree;
    }

  }

  async setAfterPrice(oldProjectTree, newProject, constructId, priceList, priceCoe, resQtyList, resQtyCoe, unitList){
    let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    //处理人材机单价
    let priceRcjsDeSet  =new  Set();
    let qtyRcjsDeSet  =new  Set();
    let baseAllRcj = await this.service.gongLiaoJiProject.gljBaseRcjService.queryAllRcjId();
    if(ObjectUtils.isNotEmpty(priceList)){
      for (let i = 0; i < priceList.length; i++) {
        let rcj = priceList[i];
        if(taxMethod  === RcjCommonConstants.SIMPLE_REVERSE){
          let price = this._calPrice(rcj, priceCoe, rcj.marketTaxPrice);
          if(rcj.marketTaxPrice !== price){
            let constructRcj = {marketTaxPrice:price};
            let  args = { constructId, singleId: null, unitId:rcj.unitId, deId:rcj.deId, rcjDetailId:rcj.sequenceNbr, constructRcj ,adjustmentCoefficient:null,deType:null,baseAllRcj:baseAllRcj}
            await this.updateRcjDetailMarketPrice(args) ;
            priceRcjsDeSet.add({unitId:rcj.unitId,deRowId:rcj.deRowId});
          }

        }
        if(taxMethod  === RcjCommonConstants.GENERAL_FORWARD){
          let price = this._calPrice(rcj,priceCoe,rcj.marketPrice);
          if(rcj.marketPrice !== price){
            let constructRcj = {marketPrice:price};
            let  args = { constructId, singleId: null, unitId:rcj.unitId, deId:rcj.deId, rcjDetailId:rcj.sequenceNbr, constructRcj ,adjustmentCoefficient:null,deType:null,baseAllRcj:baseAllRcj}
            await this.updateRcjDetailMarketPrice(args) ;
            priceRcjsDeSet.add({unitId:rcj.unitId,deRowId:rcj.deRowId});
          }

        }
      }
    }

    //处理人材机消耗量
    if(ObjectUtils.isNotEmpty(resQtyList)){
      for (let i = 0; i < resQtyList.length; i++) {
        let rcj = resQtyList[i];
        let resQty = this._calPrice(rcj, resQtyCoe, rcj.resQty);
        if(rcj.resQty !== resQty){
          let constructRcj = {resQty:resQty};
          let  args = { constructId, singleId: null, unitId:rcj.unitId, deId:rcj.deId, rcjDetailId:rcj.sequenceNbr, constructRcj ,adjustmentCoefficient:null,deType:null,baseAllRcj:baseAllRcj}
          await this.updateRcjDetailMarketPrice(args) ;
          qtyRcjsDeSet.add({unitId:rcj.unitId,deRowId:rcj.deRowId});
        }

      }
    }

    let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    for (let i = 0; i < unitList.length; i++) {
      let unitId = unitList[i];
      deBaseDomain.notifyAll(constructId,unitId);
      try {
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          singleId: null,
          unitId: unitId,
          qfMajorType: null
        });
      } catch (error) {
        console.error('捕获到异常:', error);
      }
    }


    let priceMap = new Map();
    //获取新项目的金额, 组装map
    let newProjectTree = ProjectDomain.getDomain(constructId).getProjectTree();
    for (let i = 0; i < newProjectTree.length; i++) {
      let item = newProjectTree[i];

      if(item.type === 1){
        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // 获取该单项下的所有单位
        let unitProjectsByConstruct = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);
        // 整个项目的总工程造价值
        let constructCost = 0;
        for (let j = 0; j < unitProjectsByConstruct.length; j++) {
          let unitProject = unitProjectsByConstruct[j];
          // 获取该单位的费用汇总
          let param = {
            constructId: constructId,
            singleId: unitProject.parentId,
            unitId: unitProject.sequenceNbr
          }
          let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
          constructCost = NumberUtil.add(constructCost, unitCostSummaryPriceMap.get("工程造价"));
        }
        priceMap.set('1', constructCost);
      }
      if(item.type === 2){
        let singlePrice = 0;

        // 获取该工程项目
        let constructProject = ProjectDomain.getDomain(constructId).getProjectById(item.sequenceNbr);
        // 获取该单项下的所有单位
        let unitProjectsByConstruct = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(constructProject.children, unitProjectsByConstruct);

        for (let j = 0; j < unitProjectsByConstruct.length; j++) {
          let unitProject = unitProjectsByConstruct[j];
          // 获取该单位的费用汇总
          let param = {
            constructId: constructId,
            singleId: unitProject.parentId,
            unitId: unitProject.sequenceNbr
          }
          let unitCostSummaryPriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary(param);
          singlePrice = NumberUtil.add(singlePrice, unitCostSummaryPriceMap.get("工程造价"));
        }
        priceMap.set(item.sequenceNbr, singlePrice);
      }

      if(item.type === 3){
        let unitCostSummaryArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
          .get(item.sequenceNbr + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL);
        if(ObjectUtils.isNotEmpty(unitCostSummaryArray)){
          let price = unitCostSummaryArray.filter(s=>s.name==='工程造价')[0].price;
          priceMap.set(item.sequenceNbr, price);
        }
      }
    }

    //把调整后的数据刷新到树
    for (let i = 0; i < oldProjectTree.length; i++) {
      let item = oldProjectTree[i];
      if(item.type === 1){
        let projectAfterPrice = priceMap.get('1');
        item.afterPrice = NumberUtil.numberScale2(projectAfterPrice);
        item.difference = NumberUtil.numberScale2(projectAfterPrice-item.beforePrice);
      }else{

        let singleunitAfterPrice = priceMap.get(item.sequenceNbr);
        item.afterPrice = NumberUtil.numberScale2(singleunitAfterPrice);
        item.difference = NumberUtil.numberScale2(singleunitAfterPrice-item.beforePrice);
      }
    }

    //删除内存中的新项目
    AppContext.removeContext(constructId);
    return oldProjectTree;
  }

  //刷新项目整体id
  async _updatePropertyValue(copyConstruct, newConstructId){
    let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(copyConstruct));
    let data = JSON.stringify(jsonObj);
    //  获取项目id
    let oldConstructId = await YGLJOperator.getFirstConstructId(data);
    // 随机生成一个sequence替换constructid,避免重复
    // 替换constructid
    console.log("----old:" + oldConstructId + ",new:" + newConstructId + "----")
    const regex = new RegExp(oldConstructId, 'g');
    data = data.replace(regex, newConstructId);
    // 尝试解析 JSON 数据
    let parsedData = JSON.parse(data);
    return await YGLJOperator.destructuringFile(parsedData);
  }

  /**
   *  人材机价差进行初始化
   * @param resource
   */
  rcjDiffInit(resource){
    let businessMap = ProjectDomain.getDomain(resource.constructId).functionDataMap;
    let setting = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
    let zsDiff = setting.get(GsProjectSettingEnum.PRICING_ZSDIFF);
    let  rcjDiff = setting.get(GsProjectSettingEnum.PRICING_RCJDIFF);
    let  taxMethod=ProjectDomain.getDomain(resource.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    resource.originBaseJournalPrice  = resource.baseJournalPrice ;
    resource.originBaseJournalTaxPrice = resource.baseJournalTaxPrice;
    resource.originMarketPrice  = resource.marketPrice ;
    resource.originMarketTaxPrice = resource.marketTaxPrice;

    if(resource.kind  === 4 ||  resource.kind  === 5 || resource.originKind  === 4 ||  resource.originKind  === 5){
      if(zsDiff ===true && resource.supplementRcjFlag !== RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
        if(resource.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
          resource.baseJournalPrice  = resource.marketPrice ;
          resource.baseJournalTaxPrice = resource.marketTaxPrice;
        }else{
          resource.baseJournalPrice = resource.originBaseJournalPrice;
          resource.baseJournalTaxPrice = resource.originBaseJournalTaxPrice;
        }
      }
    }else if(resource.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG   ){
        resource.baseJournalPrice  = resource.marketPrice ;
        resource.baseJournalTaxPrice = resource.marketTaxPrice;
     }

  }

  /**
   *
   * 人材机价差进行判定
   * @param resource
   * @returns {boolean}
   */
  rcjDiffEstimate(resource) {
    let result = false;
    let businessMap = ProjectDomain.getDomain(resource.constructId).functionDataMap;
    let setting = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
    let zsDiff = setting.get(GsProjectSettingEnum.PRICING_ZSDIFF);
    let rcjDiff = setting.get(GsProjectSettingEnum.PRICING_RCJDIFF);
    // 主材设备与补充人材机五要素 规则一致
    if (resource.kind === 4 || resource.kind === 5 || resource.originKind === 4 || resource.originKind === 5 ) {
      if (zsDiff === false  &&  resource.supplementRcjFlag !== RcjCommonConstants.SUPPLEMENT_RCJ_FLAG ) {
        result = true;
      }
      if (zsDiff === false  &&  (resource.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG && (resource.originKind === 4 || resource.originKind === 5))) {
        result = true;
      }
    }
    if( resource.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG && resource.kind !== 4 && resource.kind !== 5 ){
      if (rcjDiff === false) {
        result = true;
      }
    }
      return result;
  }


  async rcjDiffSwitchover(constructId){
    let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
    let setting = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
    let zsDiff = setting.get(GsProjectSettingEnum.PRICING_ZSDIFF);
    let  rcjDiff = setting.get(GsProjectSettingEnum.PRICING_RCJDIFF);
    let  rcjList=ProjectDomain.getDomain(constructId).resourceDomain.getResourceArray();
    let unitList = ProjectDomain.getDomain(constructId).getProjectTree().filter(item => item.type === 3).map(item=>item.sequenceNbr);
    if(ObjectUtils.isNotEmpty(rcjList)){
      for(let item of rcjList){
        if(item.kind  === 4 ||  item.kind  === 5 || item.originKind  === 4 ||  item.originKind  === 5){
          if(item.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
            if(item.kind  === 4 ||  item.kind  === 5){
              if( zsDiff === true){
                item.baseJournalPrice = item.originBaseJournalPrice;
                item.baseJournalTaxPrice = item.originBaseJournalTaxPrice;
              }else{
                item.baseJournalPrice = item.marketPrice;
                item.baseJournalTaxPrice = item.marketTaxPrice;
              }
            }else {
              if(rcjDiff === true){
                item.baseJournalPrice = item.originBaseJournalPrice;
                item.baseJournalTaxPrice = item.originBaseJournalTaxPrice;
              }else{
                item.baseJournalPrice = item.marketPrice;
                item.baseJournalTaxPrice = item.marketTaxPrice;
              }
            }

          }else{
            if( zsDiff ===true){
              item.baseJournalPrice =item.originBaseJournalPrice;
              item.baseJournalTaxPrice = item.originBaseJournalTaxPrice;
            }else{
              item.baseJournalPrice = item.marketPrice;
              item.baseJournalTaxPrice = item.marketTaxPrice;
            }
          }
          await ProjectDomain.getDomain(constructId).getResourceDomain().notifyRCj(item);
        }else if(item.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
          if(rcjDiff === true){
            item.baseJournalPrice = item.originBaseJournalPrice;
            item.baseJournalTaxPrice = item.originBaseJournalTaxPrice;
          }else{
            item.baseJournalPrice = item.marketPrice;
            item.baseJournalTaxPrice = item.marketTaxPrice;
          }
          await ProjectDomain.getDomain(constructId).getResourceDomain().notifyRCj(item);
        }
      }
    }
    let deBaseDomain = ProjectDomain.getDomain(constructId).getDeDomain();
    for (let i = 0; i < unitList.length; i++) {
      let    unitId=unitList[i];
      deBaseDomain.notifyAll(constructId,unitId);
      try {
        await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
          constructId: constructId,
          singleId: null,
          unitId: unitId,
          qfMajorType: null
        });
      } catch (error) {
        console.error('捕获到异常:', error);
      }
    }
  }

  /**
   * 人材机类型切换 处理价差
   * @param resource
   */
  rcjDiffSwitchType(resource){
    let businessMap = ProjectDomain.getDomain(resource.constructId).functionDataMap;
    let setting = businessMap.get(FunctionTypeConstants.PROJECT_SETTING);
    let zsDiff = setting.get(GsProjectSettingEnum.PRICING_ZSDIFF);
    let  rcjDiff = setting.get(GsProjectSettingEnum.PRICING_RCJDIFF);

    if((resource.kind  === 4 ||  resource.kind  === 5 || resource.originKind  === 4 ||  resource.originKind  === 5)  && resource.supplementRcjFlag !== RcjCommonConstants.SUPPLEMENT_RCJ_FLAG) {
      if( zsDiff ===true){
        resource.baseJournalPrice =  resource.originBaseJournalPrice;
        resource.baseJournalTaxPrice = resource.originBaseJournalTaxPrice;
      }else{
        resource.baseJournalPrice = resource.marketPrice;
        resource.baseJournalTaxPrice = resource.marketTaxPrice;
      }
    } else  if(resource.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
      if( resource.kind  === 4 ||  resource.kind  === 5 ){
        if(zsDiff === true){
          resource.baseJournalPrice = resource.originBaseJournalPrice;
          resource.baseJournalTaxPrice = resource.originBaseJournalTaxPrice;
        }else {
          resource.baseJournalPrice = resource.marketPrice;
          resource.baseJournalTaxPrice = resource.marketTaxPrice;
        }
      }else{
         if(rcjDiff === true){
           resource.baseJournalPrice = resource.originBaseJournalPrice;
           resource.baseJournalTaxPrice = resource.originBaseJournalTaxPrice;
         }else {
           resource.baseJournalPrice = resource.marketPrice;
           resource.baseJournalTaxPrice = resource.marketTaxPrice;
         }

      }
    }else  if(resource.kind  === 2 ){
      resource.baseJournalPrice = resource.originBaseJournalPrice;
      resource.baseJournalTaxPrice = resource.originBaseJournalPrice;
    }


  }

  /**
   *   市政养护 定额下增加人材机进行标记
   * @param rcj
   * @returns {Promise<void>}
   */
  async handleSzyhRcj(rcj) {
    rcj.isMunicipal = false ;
    // let precision =  this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(rcj.constructId);
    // let businessMap = ProjectDomain.getDomain(rcj.constructId).functionDataMap;
    // let szssMediumRepair = businessMap.get(FunctionTypeConstants.PROJECT_SETTING).get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR);
    // rcj.resQtyFactor = NumberUtil.divide(NumberUtil.numberScale(rcj.resQty,precision.DETAIL.RCJ.resQty),rcj.originalConsumeQty);
    // let de = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(rcj.constructId, rcj.unitId, rcj.deId);
    // let unitProject = await ProjectDomain.getDomain(rcj.constructId).getProjectById(rcj.unitId);
    // if(unitProject.constructMajorType === "2024-SZSS-DEX"
    //     && unitProject.deLibrary === "2024-SZSS-DEX"
    //     && de.libraryCode === "2024-SZSS-DEX"
    //     && (ObjectUtils.isEmpty(de?.isCostDe) || de?.isCostDe === CostDeMatchConstants.NON_COST_DE)
    //     && (rcj.kind === 1 || rcj.kind === 3)
    //     && (rcj.isFyrcj !==0  || (rcj.isFyrcj ===0 && rcj.unit !=='%'))
    //   ){
    //     rcj.isMunicipal = true ;
    //   }
    // if(szssMediumRepair === true  && rcj.isMunicipal){
    //   rcj.resQtyFactor = NumberUtil.divide(rcj.resQtyFactor,0.9);
    // }

    }


  /**
   * 中修计算
   * @param rcj
   * @returns {Promise<void>}
   */
  async handleSzyhRcjAndCal(rcj){
    rcj.isMunicipal = false ;
    let precision =  this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(rcj.constructId);
    let businessMap = ProjectDomain.getDomain(rcj.constructId).functionDataMap;
    let szssMediumRepair = businessMap.get(FunctionTypeConstants.PROJECT_SETTING).get(GsProjectSettingEnum.SZSS_MEDIUM_REPAIR);
    rcj.resQtyFactor = NumberUtil.divide(NumberUtil.numberScale(Number(rcj.resQty), precision.DETAIL.RCJ.resQty),rcj.originalConsumeQty);
    let de = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(rcj.constructId, rcj.unitId, rcj.deId);
    let unitProject = await ProjectDomain.getDomain(rcj.constructId).getProjectById(rcj.unitId);
    if(unitProject.constructMajorType === "2024-SZSS-DEX"
      && unitProject.deLibrary === "2024-SZSS-DEX"
      && de.libraryCode === "2024-SZSS-DEX"
      && (ObjectUtils.isEmpty(de?.isCostDe) || de?.isCostDe === CostDeMatchConstants.NON_COST_DE)
      && (rcj.kind === 1 || rcj.kind === 3)
      && (rcj.isFyrcj !==0  || (rcj.isFyrcj ===0 && rcj.unit !=='%'))
    ){
      rcj.isMunicipal = true ;
    }
    if(szssMediumRepair === true  && rcj.isMunicipal){
      rcj.resQtyFactor = NumberUtil.divide(rcj.resQtyFactor,0.9);
    }

  }

  /**
   * 人材机 消耗量 系数计算  先计算 消耗量 然后反算系数
   * @param rcj
   */
  rcjCalculateCoefficient(rcj){
    if (ObjectUtils.isEmpty(rcj)){
      return;
    }

    if (!rcj.hasOwnProperty("updateFormattedCoefficient")) {
      if (rcj.initResQty != 0) {
        let coefficient = this.divideAndTruncate(rcj.resQty, rcj.initResQty);
        // 这里直接使用rcj.resQty除rcj.initResQty   忽略了清单的调整系数这个值   导致formattedCoefficient表达式没有拼接到调整系数
        // 在此处临时处理一下这个问题， 使用rcj.resQty除rcj.initResQty得到的coefficient实际是调整系数*0.9的值，所以这里再除一个0.9  最后拼接值
        // 这个有点临时处理   后续需要考虑优化
        let value = this.divideAndTruncate(coefficient, 0.9);
        rcj.resQtyFactor = rcj.initResQty + '*' + value + '*0.9';

      } else {

        rcj.resQtyFactor = 1 + "*" + rcj.resQty;

      }
    }else {
      if(!ObjectUtils.isEmpty(rcj.resQtyFactor)){
        rcj.resQtyFactor = rcj.resQtyFactor + "*" + rcj.updateFormattedCoefficient;
      }else {
        rcj.resQtyFactor = rcj.initResQty + "*" + rcj.updateFormattedCoefficient;
      }

    }

  }

  /**
   * 人材机 消耗量 系数计算  先计算 消耗量 然后反算系数 增加一个0.9
   * @param rcj
   */
  rcjCalculateCoefficientIsSzgcWxyh(rcj){
    if (ObjectUtils.isEmpty(rcj)){
      return;
    }

    if (!(rcj.kind ==1 || rcj.kind ==3)){
      return;
    }

    if (rcj.initResQty != 0) {
      let coefficient = this.divideAndTruncate(rcj.resQty, 0.9);
      let coefficient1 = this.divideAndTruncate(coefficient, rcj.initResQty);

      rcj.resQtyFactor = rcj.initResQty + "*" + coefficient1 +"*0.9";

    } else {
      let coefficient = this.divideAndTruncate(rcj.resQty, 0.9);
      rcj.resQtyFactor = "1*" + coefficient+"*0.9" ;

    }

  }

  /**
   * 两个数相除保留15位小数 第16位舍去
   * @param a
   * @param b
   * @returns {*}
   */
  divideAndTruncate(a, b, precision = 15) {
    if (new Decimal(b).equals(0)) {
      throw new Error("Division by zero");
    }
    // 使用 ROUND_DOWN 模式截断多余小数位
    return new Decimal(a)
        .dividedBy(b)
        .toDecimalPlaces(precision, Decimal.ROUND_DOWN)
        .toNumber();
  }


  async switchProjectTaxCheck(args) {
    let {constructId} = args;
    let projectTree = ProjectDomain.getDomain(constructId).getProjectTree();
    let calculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
    let changeRcjUnitList = [];
    let projectTreeDeepCopy = ConvertUtil.deepCopy(projectTree);
    for (let project of projectTreeDeepCopy) {
      if (project.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
        let rcjUnitKey = WildcardMap.generateKey(project.sequenceNbr) + WildcardMap.WILDCARD;
        let rcjUnitList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(rcjUnitKey);

        let changeRcjList1 = [];
        for (let iii of rcjUnitList) {
          if (iii.isDeResource === CommonConstants.COMMON_YES) {
            if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
              if (iii.marketPrice != iii.baseJournalPrice) {
                changeRcjList1.push(iii);
              }
            } else {
              if (iii.marketPrice != iii.baseJournalPrice) {
                changeRcjList1.push(iii);
              }
            }
          } else {
            if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
              if (iii.resQty != iii.originalQty || iii.taxRate != iii.taxRateInit || iii.marketPrice != iii.baseJournalPrice) {
                changeRcjList1.push(iii);
              }
            } else {
              if (iii.resQty != iii.originalQty || iii.taxRate != iii.taxRateInit || iii.marketPrice != iii.baseJournalPrice) {
                changeRcjList1.push(iii);
              }
            }
          }
        }
        // let changeRcjList1 = rcjUnitList.filter(o => o.resQty != o.originalQty || o.taxRate != o.taxRateInit || o.marketPrice != o.baseJournalPrice);
        // if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
        //   changeRcjList1 = rcjUnitList.filter(o => o.resQty != o.originalQty || o.taxRate != o.taxRateInit || o.marketTaxPrice != o.baseJournalTaxPrice);
        // }
        let changeRcjList = ConvertUtil.deepCopy(changeRcjList1);

        if (ObjectUtils.isNotEmpty(changeRcjList)) {
          changeRcjUnitList.push(project);
          for (let rcj of changeRcjList) {
            if (rcj.materialCode?.includes('QTCLF1')) {
              rcj.unit = '%'
            }
            if (rcj.isFyrcj === 0
                && (rcj.materialCode.replace(/#\d+/g, '') !== 'RGFTZ' && rcj.materialCode.replace(/#\d+/g, '') !== 'JXFTZ' && rcj.materialCode.replace(/#\d+/g, '') !== 'CLFTZ')
            ) {
              rcj.unit = '%'
            }

            rcj.bizType = GljCheckResultBizMoudleTypeConstants.BIZTYPE_MOUDLETYPE_RCJ;
            rcj.moduleType = GljCheckResultBizMoudleTypeConstants.BIZTYPE_MOUDLETYPE_YSS;

            // let deRowObject = ProjectDomain.getDomain(constructId).deDomain.getDeById(rcj.deRowId);
            let csxmRowObject = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(rcj.deRowId);
            if (ObjectUtils.isNotEmpty(csxmRowObject)) {
              rcj.moduleType = GljCheckResultBizMoudleTypeConstants.BIZTYPE_MOUDLETYPE_CSXM;
            }

          }
        } else {
          continue;
        }

        let deTree = ProjectDomain.getDomain(constructId).getDeDomain().getDeTree(item => item.unitId === project.sequenceNbr).map(obj => ({
          code: obj.deCode,
          name: obj.deName,
          sequenceNbr: obj.sequenceNbr,
          deRowId: obj.deRowId,
          parentId: obj.parentId,
          type: obj.type,
          quantity: obj.quantity,
          topFlag: false
        }))

        let deRowIds = new Set();
        changeRcjList.forEach(item => {
          let deLine = ProjectDomain.getDomain(constructId).deDomain.getDeById(item.deRowId);
          let isCsxmDe = 0;
          if (ObjectUtils.isEmpty(deLine)) {
            deLine = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(item.deRowId);
            isCsxmDe = 1;
          }
          deRowIds.add({deRowId: item.deRowId, totalNumber: 0, isCsxmDe: isCsxmDe});

          if (item.levelMark != RcjCommonConstants.LEVELMARK_ZERO && ObjectUtils.isNotEmpty(item.pbs) && item.markSum === RcjCommonConstants.MARKSUM_JX) {
            item.pbs.forEach(item2 => {
              deRowIds.add({deRowId: item.deRowId, totalNumber: 0, isCsxmDe: isCsxmDe});
            });
          }
        });
        deRowIds = Array.from(deRowIds);
        deRowIds.sort((a, b) => {
          return a.isCsxmDe - b.isCsxmDe;
        });
        let allDeRowIds = new Set();
        let allDeMap = new Map();
        //向上查询所有deRowId/ 找到所有de子分部
        let zfbs = new Set();
        let dfsMap = new Map();
        deRowIds.forEach(item => {
          //动态多叉哈弗曼
          this.service.gongLiaoJiProject.gljRcjCollectService.findAllDe(deTree, item, allDeRowIds, zfbs, allDeMap, dfsMap);
        });
        //子分部收集器
        let allDeArray = Array.from(allDeMap.values());
        allDeArray.forEach(item => item.totalNumber = NumberUtil.numberScale(item.totalNumber, 5))
        //let zfbAndSub=new Array();
        let SubZFbArray = new Array();
        zfbs.forEach(item => {
          //向上收集
          let zfb = allDeArray.find(item2 => item2.sequenceNbr === item);
          let name = this.findFb1(allDeArray, zfb, SubZFbArray);
          // SubZFbArray.push({code:zfb.code, name: name, totalNumber: zfb.totalNumber, topFlag: true});
          //向下收集
          this.service.gongLiaoJiProject.gljRcjCollectService.findFBDe(allDeArray, item, SubZFbArray);
        });

        let disNo = 1;
        let SubZFbArrayResult = [];
        if (ObjectUtils.isNotEmpty(SubZFbArray)) {
          for (let i = 0; i < SubZFbArray.length; i++) {
            let ppp = SubZFbArray[i];
            if (ppp.type === DeTypeConstants.DE_TYPE_DELIST || ppp.type === DeTypeConstants.DE_TYPE_DE || ppp.type === DeTypeConstants.DE_TYPE_RESOURCE) {
              if(ppp.type === DeTypeConstants.DE_TYPE_DE || ppp.type === DeTypeConstants.DE_TYPE_RESOURCE){
                ppp.dispNo = String(disNo++);
              }
              ppp.topFlag = true;
              if (ObjectUtils.isNotEmpty(ppp.parentId)) {
                let find = SubZFbArray.find(i => i.sequenceNbr === ppp.parentId);
                if(ObjectUtils.isNotEmpty(find)){
                  ppp.topFlag = false;
                }
              }
            }

            SubZFbArrayResult.push(ppp);
            let deChangeRcjList = changeRcjList.filter(o => o.deRowId === ppp.sequenceNbr);
            if (ObjectUtils.isNotEmpty(deChangeRcjList)) {
              deChangeRcjList.forEach(m => {
                let marketPrice = m.marketPrice;
                let baseJournalPrice = m.baseJournalPrice;
                if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
                  marketPrice = m.marketTaxPrice;
                  baseJournalPrice = m.baseJournalTaxPrice;
                }
                let aa = {
                  sequenceNbr: m.sequenceNbr,
                  parentId: m.parentId,
                  code: m.materialCode,
                  name: m.materialName,
                  specification: m.specification,
                  unit: m.unit,
                  resQty: m.resQty,
                  marketPrice: marketPrice,
                  isDataTaxRate: m.isDataTaxRate,
                  taxRate: m.taxRate,
                  topFlag: false,
                  bizType: m.bizType,
                  moduleType: m.moduleType,
                  resQtyChange: false,
                  taxRateChange: false,
                  marketPriceChange: false,
                  deId: ppp.sequenceNbr,
                };

                if (m.resQty != m.originalQty) {
                  aa.resQtyChange = true;
                }
                if (m.taxRate != m.taxRateInit) {
                  aa.taxRateChange = true;
                }
                if (ObjectUtils.isEmpty(m.isUpdateTaxRate) || !m.isUpdateTaxRate) {
                  aa.taxRate = m.taxRateStandard;
                }

                if (m.marketPrice != baseJournalPrice) {
                  aa.marketPriceChange = true;
                }

                if (ObjectUtils.isNotEmpty(m.isDeResource) && m.isDeResource === CommonConstants.COMMON_YES) {
                  ppp.resQty = null;
                  ppp.taxRate = aa.taxRate;
                  ppp.marketPrice = aa.marketPrice;
                  ppp.resQtyChange = false;
                  ppp.taxRateChange = aa.taxRateChange;
                  ppp.marketPriceChange = aa.marketPriceChange;
                  if(!aa.marketPriceChange){
                    SubZFbArrayResult.pop();
                  }
                } else {
                  SubZFbArrayResult.push(aa);
                }


                // if (ObjectUtils.isNotEmpty(m.pbs)) {
                //   m.pbs.forEach(ooo => {
                //     let marketPrice = ooo.marketPrice;
                //     let baseJournalPrice = ooo.baseJournalPrice;
                //     if (calculationMethod === ProjectTaxCalculationConstants.TAX_MODE_0) {
                //       marketPrice = ooo.marketTaxPrice;
                //       baseJournalPrice = ooo.baseJournalTaxPrice;
                //     }
                //     let bb = {
                //       sequenceNbr: ooo.sequenceNbr,
                //       parentId: ooo.parentId,
                //       code: ooo.materialCode,
                //       name: ooo.materialName,
                //       specification: ooo.specification,
                //       unit: ooo.unit,
                //       resQty: ooo.resQty,
                //       marketPrice: marketPrice,
                //       isDataTaxRate: ooo.isDataTaxRate,
                //       taxRate: ooo.taxRate,
                //       topFlag: false,
                //       bizType: m.bizType,
                //       moduleType: m.moduleType,
                //       resQtyChange: false,
                //       taxRateChange: false,
                //       marketPriceChange: false,
                //       deId: ppp.sequenceNbr,
                //     };
                //
                //     if (ooo.resQty != ooo.originalQty) {
                //       bb.resQtyChange = true;
                //     }
                //     if (ooo.taxRate != ooo.taxRateInit) {
                //       bb.taxRateChange = true;
                //     }
                //     if (ObjectUtils.isEmpty(ooo.isUpdateTaxRate) || !ooo.isUpdateTaxRate) {
                //       bb.taxRate = ooo.taxRateStandard;
                //     }
                //
                //     if (ooo.marketPrice != baseJournalPrice) {
                //       bb.marketPriceChange = true;
                //     }
                //
                //     if(bb.resQtyChange || bb.taxRateChange || bb.marketPriceChange){
                //       SubZFbArrayResult.push(bb);
                //     }
                //   })
                // }
                ppp.bizType = GljCheckResultBizMoudleTypeConstants.BIZTYPE_MOUDLETYPE_YSS;
                ppp.moduleType = aa.moduleType;
                let csxmRowObject = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(ppp.sequenceNbr);
                if (ObjectUtils.isNotEmpty(csxmRowObject)) {
                  ppp.bizType = GljCheckResultBizMoudleTypeConstants.BIZTYPE_MOUDLETYPE_CSXM;
                }
              })
            }
          }
        }

        let topUnit = {};
        topUnit.code = "";
        topUnit.name = "单位工程";
        topUnit.topFlag = true;
        topUnit.type = DeTypeConstants.DE_TYPE_DEFAULT;
        SubZFbArrayResult.unshift(topUnit);
        project.SubZFbArray = SubZFbArrayResult;
      }
    }

    //过滤没有人材机的单位
    let changeRcjUnitSingleList = [];
    if (ObjectUtils.isNotEmpty(changeRcjUnitList)) {
      for (let iii of changeRcjUnitList) {
        changeRcjUnitSingleList.push(iii.sequenceNbr);
        await this.calUnitParentIdList(iii, changeRcjUnitSingleList, projectTreeDeepCopy);
      }
      let changeRcjUnitProjectList = projectTreeDeepCopy.filter(p => changeRcjUnitSingleList.includes(p.sequenceNbr));
      return changeRcjUnitProjectList;
    } else {
      return [];
    }
  }


  async calUnitParentIdList(unit, unitParentIdList, allProjectData) {
    if (ObjectUtils.isNotEmpty(unit.parentId)) {
      let findOne = allProjectData.find(o => o.sequenceNbr === unit.parentId);
      if (ObjectUtils.isNotEmpty(findOne)) {
        unitParentIdList.push(findOne.sequenceNbr);
        await this.calUnitParentIdList(findOne, unitParentIdList, allProjectData);
      }
    }
  }

  async updateDeRcjHandle(constructRcjArrayElement){
    if (constructRcjArrayElement.isDeResource === 1) {
      let deDomain = ProjectDomain.getDomain(constructRcjArrayElement.constructId).getDeDomain();
      let de = deDomain.getDeById(constructRcjArrayElement.deId);
      de.deCode = constructRcjArrayElement.materialCode;
      de.deName = constructRcjArrayElement.materialName;
      de.ifLockStandardPrice = constructRcjArrayElement.ifLockStandardPrice;
      de.ifProvisionalEstimate = constructRcjArrayElement.ifProvisionalEstimate;
      de.markSum = constructRcjArrayElement.markSum;
      de.specification = constructRcjArrayElement.specification;
      de.deResourceKind = constructRcjArrayElement.kind;
      await deDomain.updateDe(de);
    }
  }
  /**
   * 收集分部信息
   * @param allDeArray
   * @param sequenceNbr
   */
  findFb1(allDeArray, zfb, SubZFbArray) {
    if (ObjectUtils.isEmpty(zfb)) {
      return '';
    }
    if (zfb.type === '0') {
      return '';
    }


    let parentFb = allDeArray.find(item => item.sequenceNbr === zfb.parentId);
    if (zfb.type === '01') {
      SubZFbArray.push({sequenceNbr:zfb.sequenceNbr, parentId:zfb.parentId, type:zfb.type, code:zfb.code, name: ObjectUtils.isEmpty(zfb.name) ? ' ' : zfb.name, totalNumber: zfb.totalNumber, topFlag: true});
      return ;
    } else {
      this.findFb1(allDeArray, parentFb, SubZFbArray);
      SubZFbArray.push({sequenceNbr:zfb.sequenceNbr, parentId:zfb.parentId, type:zfb.type, code:zfb.code, name: ObjectUtils.isEmpty(zfb.name) ? ' ' : zfb.name, totalNumber: zfb.totalNumber, topFlag: false});
      return ;
    }
  }


}


GljRcjService.toString = () => '[class GljRcjService]';
module.exports = GljRcjService;
