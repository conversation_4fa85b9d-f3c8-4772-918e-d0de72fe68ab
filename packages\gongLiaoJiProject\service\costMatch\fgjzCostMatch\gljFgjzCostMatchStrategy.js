'use strict';

const EE = require('../../../../../core/ee');
const { ObjectUtil } = require('../../../../../common/ObjectUtil');
const DeTypeConstants = require('../../../constants/DeTypeConstants');
const CostDeMatchConstants = require('../../../constants/CostDeMatchConstants');
const ProjectDomain = require('../../../domains/ProjectDomain');
const FunctionTypeConstants = require('../../../constants/FunctionTypeConstants');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const WildcardMap = require('../../../core/container/WildcardMap');
const { NumberUtil } = require('../../../utils/NumberUtil');

/**
 * 房修土建费用记取策略
 */
class GljFgjzCostMatchStrategy {

  constructor() {
    this.baseDeTypeArr = [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_USER_DE, DeTypeConstants.DE_TYPE_RESOURCE, DeTypeConstants.DE_TYPE_USER_RESOURCE, DeTypeConstants.DE_TYPE_ANZHUANG_FEE, DeTypeConstants.DE_TYPE_ZHUANSHI_FEE];
  }

  async getViewData(args) {
    const { constructId, singleId, unitId } = args;
    // 查询公共的基数定额数据
    const baseDeList = await this.findBaseDeList(args);
    if (ObjectUtil.isEmpty(baseDeList)) {
      return null;
    }
    // 子类针对基数定额进行各自的特殊处理  并返回页面需要的树形结构数据和记取位置数据
    const viewData = await this.customizeViewHandler(args, baseDeList);
    this.delUselessFiled(viewData.treeData);
    let result = {
      // 列表基数定额数据
      'treeData': viewData.treeData,
      // 1：对应分部  2：指定措施  3：指定分部
      'constructionMeasureType': CostDeMatchConstants.ZJCS,
      // 默认的记取位置
      'defaultQdName': viewData.qdName,
      'csxmFlag': '1',
      'qdData': {
        [CostDeMatchConstants.ZJCS]: {
          'qdList': await this.qdList({
            constructId,
            singleId,
            unitId,
            optionType: CostDeMatchConstants.ZJCS
          })
        },
        [CostDeMatchConstants.FBFX]: {
          'qdList': await this.qdList({
            constructId,
            singleId,
            unitId,
            optionType: CostDeMatchConstants.FBFX
          })
        },
        [CostDeMatchConstants.DYFBFX]: {
          'qdList': []
        }
      }
    };
    const fxtjCache = await this.getCache(args);
    // 获取缓存，刷新默认的记取位置数据
    if (ObjectUtil.isNotEmpty(fxtjCache)) {
      result.constructionMeasureType = fxtjCache.constructionMeasureType;
      result.csxmFlag = fxtjCache.csxmFlag;
      // 根据历史记取位置数据
      for (const key of Object.keys(fxtjCache.qdData)) {
        result.qdData[key].qdId = fxtjCache.qdData[key];
        const parentNode = this.getNodeByIdAndMeasureType(constructId, result.qdData[key].qdId, result.constructionMeasureType);
        if (ObjectUtil.isNotEmpty(parentNode)) {
          result.qdData[key].qdName = ObjectUtil.isNotEmpty(parentNode.deName) ? parentNode.deName : parentNode.name;
        }
      }
    }
    return result;
  }

  async qdList(args) {
    let { constructId, singleId, unitId, optionType } = args;
    let allData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    let bkjzItem;
    if (optionType == CostDeMatchConstants.ZJCS) {
      allData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);
      bkjzItem = allData.find(item => item.deName == '不可竞争措施项目');
    }
    if (ObjectUtils.isEmpty(allData)) {
      return [];
    }
    let typeList = [DeTypeConstants.DE_TYPE_DEFAULT, DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB, DeTypeConstants.DE_TYPE_DELIST];
    if (optionType == CostDeMatchConstants.ZJCS) {
      allData = allData.filter(item => typeList.includes(item.type) && item.parentId != bkjzItem.deRowId && item.deRowId != bkjzItem.deRowId);
    } else {
      allData = allData.filter(item => typeList.includes(item.type));
    }
    allData = ObjectUtils.cloneDeep(allData);
    allData.sort((a, b) => a.index - b.index);
    this.delUselessFiled(allData);
    return allData;
  }

  getNodeByIdAndMeasureType(constructId, id, measureType) {
    let node = null;
    if (ObjectUtil.isEmpty(id)) {
      return node;
    }
    if (ObjectUtil.isNotEmpty(measureType)) {
      if (measureType == CostDeMatchConstants.ZJCS) {
        node = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(id);
      } else {
        node = ProjectDomain.getDomain(constructId).deDomain.getDeById(id);
      }
    } else {
      node = ProjectDomain.getDomain(constructId).deDomain.getDeById(id);
      if (ObjectUtil.isEmpty(node)) {
        node = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(id);
      }
    }
    return node;
  }

  delUselessFiled(dataArr) {
    if (ObjectUtil.isNotEmpty(dataArr)) {
      dataArr.map(item => {
        delete item.parent;
        delete item.children;
        delete item.prev;
        delete item.next;
      });
    }
  }

  getFirstCgFb(constructId, unitId, deName) {
    const root = ProjectDomain.getDomain(constructId).deDomain.getRoot(unitId);
    if (ObjectUtils.isEmpty(root.children)) {
      return root;
    }
    return this.findCgFb(root, deName);
  }

  findCgFb(fbNode, deName) {
    if (ObjectUtils.isNotEmpty(fbNode) && [DeTypeConstants.DE_TYPE_FB, DeTypeConstants.DE_TYPE_ZFB].includes(fbNode.type) && fbNode.deName == deName) {
      // 先确定这个分部是【超高降效】分部
      if (ObjectUtils.isEmpty(fbNode.children)) {
        return fbNode;
      } else {
        if (fbNode.children[0].type != DeTypeConstants.DE_TYPE_ZFB) {
          return fbNode;
        }
      }
    }
    const sortChildren = [...fbNode.children].sort((a, b) => a.index - b.index);
    for (const child of sortChildren) {
      const findAzFb = this.findCgFb(child, deName);
      if (ObjectUtils.isNotEmpty(findAzFb)) {
        return findAzFb;
      }
    }
  }

  async customizeViewHandler(args, baseDeList) {
    // 留给子类实现  这里不写逻辑
    throw new Error('留给子类实现方法');
  }

  async findBaseDeList(args) {
    const { constructId, singleId, unitId } = args;
    let baseDeList = [];
    const yssDeData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId && this.baseDeTypeArr.includes(item.type));
    const csxmDeData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId && this.baseDeTypeArr.includes(item.type));
    baseDeList = baseDeList.concat(yssDeData);
    baseDeList = baseDeList.concat(csxmDeData);
    if (ObjectUtil.isNotEmpty(baseDeList)) {
      baseDeList = baseDeList.filter(item => {
        if (item.libraryCode == CostDeMatchConstants.LIBRARY_CODE_FGJZ_25 && ObjectUtil.isNotEmpty(item.classlevel02)) {
          for (const str of CostDeMatchConstants.CLASS_LEVEL2_ARR_FGJZ_25) {
            if (item.classlevel02.startsWith(str)) {
              return true;
            }
          }
          return false;
        }
        return false;
      });
    }
    return baseDeList;
  }

  /**
   * 房修土建记取接口
   */
  async costMatch(args) {
    const { constructId, singleId, unitId, feeType, baseDeData, constructionMeasureType, qdId, csxmFlag } = args;
    let changeDeIds = new Set();
    // 设置缓存
    await this.setCache(args);
    // 删除历史的所有房修土建feeType对应的费用定额
    await EE.app.service.gongLiaoJiProject.gljConstructCostMathService.delCostDe(unitId, singleId, constructId, [this.getCostDeValueByFeeType(feeType)]);
    // 筛选出定额  并且是需要记取的
    let baseDeArr = baseDeData.filter(item => item.type == DeTypeConstants.DE_TYPE_DE && item.matchFlag == 1);
    if (ObjectUtil.isNotEmpty(baseDeArr) && feeType == CostDeMatchConstants.CG && csxmFlag != 1) {
      baseDeArr = baseDeArr.filter(item => ObjectUtils.isEmpty(ProjectDomain.getDomain(constructId).csxmDomain.getDeById(item.deRowId)));
    }
    if (ObjectUtil.isEmpty(baseDeArr)) {
      return;
    }

    let parentNodeName = '';
    // 根据基数定额和记取的费用项，获取本次需要添加的费用定额数据 并确认费用定额对应的基数定额和所属的父级
    const newCostDeData = await this.customizeConfirmCostDe(baseDeArr, args);
    // costDeArr是已添加的费用定额的数据    costDeBaseDe是费用定额对应的基数定额关联数据
    const { costDeArr, costDeBaseDe } = newCostDeData;
    if (ObjectUtil.isNotEmpty(costDeArr)) {
      parentNodeName = ObjectUtils.isNotEmpty(costDeArr[0].parent.deName) ? costDeArr[0].parent.deName : costDeArr[0].parent.name;
      if (constructionMeasureType == CostDeMatchConstants.ZJCS || constructionMeasureType == CostDeMatchConstants.FBFX) {
        const fxCache = await this.getCache(args);
        fxCache.qdData[constructionMeasureType] = costDeArr[0].parent.sequenceNbr;
      }
    }
    // 重新计算费用定额的人材机的合计数量
    await this.updateCostDeRcjTotalNumber(costDeArr, costDeBaseDe, args);

    let resStr = '已成功记取至';
    if (constructionMeasureType == CostDeMatchConstants.DYFBFX) {
      resStr = resStr.concat('对应分部');
    } else if (constructionMeasureType == CostDeMatchConstants.ZJCS) {
      resStr = resStr.concat('措施项目');
      if (ObjectUtils.isNotEmpty(parentNodeName)) {
        resStr = resStr.concat('-').concat(parentNodeName);
      }
    } else if (constructionMeasureType == CostDeMatchConstants.FBFX) {
      resStr = resStr.concat('预算书');
      if (ObjectUtils.isNotEmpty(parentNodeName)) {
        resStr = resStr.concat('-').concat(parentNodeName);
      }
    }
    resStr = resStr.concat('中');
    return resStr;
  }


  async updateCostDeRcjTotalNumber(costDeArr, costDeBaseDe, args) {
    let rcjList = ProjectDomain.getDomain(args.constructId).getResourceDomain().getResource(WildcardMap.generateKey(args.unitId) + WildcardMap.WILDCARD);
    let precision = await EE.app.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(args.constructId);
    for (const newCostDe of costDeArr) {
      // 费用定额对应的基数定额数组
      const costBaseDeArr = costDeBaseDe[newCostDe.sequenceNbr];
      let baseValue = 0;
      for (const baseDe of costBaseDeArr) {
        // 人工费定额价合价 + 机械费定额价合价
        baseValue = NumberUtil.addParams(baseValue, NumberUtil.numberScale(baseDe.rdTotalSum, precision.EDIT.DE.rTotalSum), NumberUtil.numberScale(baseDe.jdTotalSum, precision.EDIT.DE.jTotalSum));
      }
      newCostDe.baseNum = { def: baseValue };
      newCostDe.formula = baseValue;
      const costDeRcjList = rcjList.filter(item => item.deId == newCostDe.sequenceNbr);
      if (ObjectUtil.isNotEmpty(costDeRcjList)) {
        for (const costDeRcjItem of costDeRcjList) {
          // 合计数量 = 计算基数*消耗量*定额工程量(默认为1)*%
          costDeRcjItem.totalNumber = NumberUtil.multiplyParams(NumberUtil.numberScale(costDeRcjItem.resQty, precision.DETAIL.RCJ.resQty), NumberUtil.numberScale(newCostDe.quantity, precision.EDIT.DE.quantity), baseValue, 0.01);
        }
        if (ObjectUtils.isNotEmpty(ProjectDomain.getDomain(args.constructId).deDomain.getDeById(newCostDe.sequenceNbr))) {
          await ProjectDomain.getDomain(args.constructId).deDomain.notify(newCostDe, false);
        } else {
          await ProjectDomain.getDomain(args.constructId).csxmDomain.notify(newCostDe, false);
        }
      }
    }
  }

  async customizeConfirmCostDe(baseDeArr, args) {
    // 留给子类实现  这里不写逻辑
    throw new Error('留给子类实现方法');
  }

  getCostDeValueByFeeType(feeType) {
    let costDeValue;
    switch (feeType) {
      // case CostDeMatchConstants.CG:
      //   costDeValue = CostDeMatchConstants.FXTJ_CG;
      //   break;
      // case CostDeMatchConstants.CZYS:
        // costDeValue = CostDeMatchConstants.FXTJ_CZYS;
        // break;
      case CostDeMatchConstants.FGJZ_ZXXJX:
        costDeValue = CostDeMatchConstants.FGJZ_DE_ZXXJX;
        break;
      // case CostDeMatchConstants.ZXXJX:
      //   costDeValue = CostDeMatchConstants.FGJZ_ZXXJX;
      //   break;
      // case CostDeMatchConstants.GCSDF:
      //   costDeValue = CostDeMatchConstants.FXTJ_GCSDF;
      //   break;
    }
    return costDeValue;
  }

  /**
   * 设置房修土建缓存
   */
  async setCache(args) {
    const { constructId, singleId, unitId, feeType, baseDeData, constructionMeasureType, csxmFlag, qdId } = args;
    // 获取历史的缓存
    let oldCache = await this.getCache(args);
    // 构建新的缓存对象
    let cacheObj = {
      // 列表基数定额数据
      'treeData': baseDeData,
      // 分部分项、总价措施、单价措施
      'constructionMeasureType': constructionMeasureType,
      'csxmFlag': csxmFlag,
      // 对应记取位置所选的清单数据记录
      // 例子：{1: qdId, 2: qdId, 3: qdId}，记录的是对应分部、指定措施、指定分部分别选择的清单id
      // 1、2、3 对应的就是constructionMeasureType的值，代表了对应分部、指定措施、指定分部
      'qdData': {
        [constructionMeasureType]: qdId
      }
    };
    if (ObjectUtil.isNotEmpty(oldCache)) {
      // 获取原缓存的清单记取位置数据   并进行复制保留
      cacheObj.qdData = oldCache.qdData;
      // 手动设置缓存中的记取位置对应的qdId
      cacheObj.qdData[constructionMeasureType] = qdId;
      const unitCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FGJZ_COST_MATCH_CACHE)[unitId];
      this.setCacheByFeeType(args, unitCache, cacheObj);
    } else {
      // 没有历史缓存
      let fxtjCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FGJZ_COST_MATCH_CACHE);
      if (ObjectUtil.isEmpty(fxtjCache)) {
        let unitCache = {};
        this.setCacheByFeeType(args, unitCache, cacheObj);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_FGJZ_COST_MATCH_CACHE, { [unitId]: unitCache });
      } else {
        // 有历史缓存   就看有没有这个单位对应的缓存
        const unitCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FGJZ_COST_MATCH_CACHE)[unitId];
        if (ObjectUtil.isNotEmpty(unitCache)) {
          // 如果有这个单位对应的缓存  那么直接给这个单位的缓存对象设置本次的缓存数据
          this.setCacheByFeeType(args, unitCache, cacheObj);
        } else {
          // 没有这个单位对应的缓存对象    那么就给这个单位创建一个对象
          let unitCache = {};
          this.setCacheByFeeType(args, unitCache, cacheObj);
          fxtjCache[unitId] = unitCache;
        }
      }

    }
  }

  setCacheByFeeType(args, unitCache, cacheObj) {
    switch (args.feeType) {
      // case CostDeMatchConstants.CG:
      //   unitCache.cgCache = cacheObj;
      //   break;
      // case CostDeMatchConstants.CZYS:
      //   unitCache.czyxCache = cacheObj;
      //   break;
      case CostDeMatchConstants.FGJZ_ZXXJX:
        unitCache.zxxjxCache = cacheObj;
        break;
      // case CostDeMatchConstants.GCSDF:
      //   unitCache.gcsdfCache = cacheObj;
      //   break;
    }
  }

  /**
   * 获取房修土建缓存
   */
  async getCache(args) {
    const { constructId, singleId, unitId, feeType } = args;
    let fxtjCache = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FGJZ_COST_MATCH_CACHE);
    if (ObjectUtils.isNotEmpty(fxtjCache)) {
      const unitFxtjCache = fxtjCache[unitId];
      switch (feeType) {
      //   case CostDeMatchConstants.CG:
      //     return ObjectUtil.isEmpty(unitFxtjCache) ? null : unitFxtjCache.cgCache;
      //   case CostDeMatchConstants.CZYS:
      //     return ObjectUtil.isEmpty(unitFxtjCache) ? null : unitFxtjCache.czyxCache;
        case CostDeMatchConstants.FGJZ_ZXXJX:
          return ObjectUtil.isEmpty(unitFxtjCache) ? null : unitFxtjCache.zxxjxCache;
        // case CostDeMatchConstants.GCSDF:
        //   return ObjectUtil.isEmpty(unitFxtjCache) ? null : unitFxtjCache.gcsdfCache;
        // default:
        //   return ObjectUtil.isEmpty(unitFxtjCache) ? null : unitFxtjCache.zxxjxCache;
      }
    }
    return null;
  }


  //--------------------------------------------------------------------------------------------------------------------


  async autoCostMatch(args) {
    const { constructId, singleId, unitId, feeType } = args;
    // 获取缓存
    const cacheObj = await this.getCache(args);
    // if (ObjectUtil.isEmpty(cacheObj) && feeType == CostDeMatchConstants.CG) {
    //   return;
    // }

    // 获取本次记取的费用项的费用定额
    let costDeArr = [];
    const yssDeData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
    const csxmDeData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);
    // 因为房修超高也要显示“降”   type字段会被后续逻辑改为“DeTypeConstants.DE_TYPE_ZHUANSHI_FEE”   此处需要加一个类型判断
    let deType = [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_ZHUANSHI_FEE];
    costDeArr = costDeArr.concat(yssDeData.filter(item => deType.includes(item.type)));
    costDeArr = costDeArr.concat(csxmDeData.filter(item => deType.includes(item.type)));
    costDeArr = costDeArr.filter(item => item.isCostDe == this.getCostDeValueByFeeType(feeType));
    if (ObjectUtil.isEmpty(costDeArr)) {
      return;
    }
    // 找出当前的所有基数定额
    let baseDeList = await this.findBaseDeList(args);
    // 中小型机械需要特殊处理
    baseDeList = await this.customizeFilterBaseDe(args, baseDeList);
    // 数据进行克隆  避免后续修改数据造成影响
    baseDeList = ObjectUtil.cloneDeep(baseDeList);
    baseDeList = baseDeList.filter(item => ObjectUtil.isNotEmpty(this.getNodeByIdAndMeasureType(constructId, item.sequenceNbr)));
    if (ObjectUtil.isNotEmpty(cacheObj)) {
      // 如果存在缓存  需要根据缓存数据进行匹配
      for (const newDe of baseDeList) {
        this.findCacheData(newDe, cacheObj.treeData, feeType, constructId, newDe);
      }
    } else {
      for (const newDe of baseDeList) {
        newDe.matchFlag = 1;
      }
    }
    // 过滤出【记取】的基数定额
    baseDeList = baseDeList.filter(item => item.matchFlag == 1);

    let costDeBaseDe = {};
    // 根据已有的费用定额和基数定额，进行匹配  找出每个费用定额对应的基数定额
    let constructionMeasureType = CostDeMatchConstants.ZJCS;
    if (ObjectUtil.isNotEmpty(cacheObj)) {
      constructionMeasureType = cacheObj.constructionMeasureType;
    }
    for (const costDe of costDeArr) {
      // 根据不同的费用项  找到费用定额对应的基数定额
      costDeBaseDe[costDe.sequenceNbr] = await this.customizeFindBaseDeByCostDe(constructionMeasureType, costDe, baseDeList);
    }

    // 重新计算费用定额的人材机的合计数量
    await this.updateCostDeRcjTotalNumber(costDeArr, costDeBaseDe, {
      constructId: constructId,
      unitId: unitId
    });
  }

  findCacheData(current, cacheDataArr, feeType, constructId, newDe) {
    // 先查询当前的在不在缓存中
    const cacheData = cacheDataArr.find(item => item.sequenceNbr == current.sequenceNbr);
    if (ObjectUtils.isNotEmpty(cacheData)) {
      // 在缓存中找到了父级  直接继承父级的数据
      newDe.matchFlag = cacheData.matchFlag;
      if (feeType == CostDeMatchConstants.CG) {
        newDe.cgEavesHighFloors = cacheData.cgEavesHighFloors;
      }
      return;
    }
    // 缓存中没有就找出当前这条数据  就查当前的父级
    const currentParent = this.getNodeByIdAndMeasureType(constructId, current.parentId);
    if (ObjectUtil.isNotEmpty(currentParent)) {
      this.findCacheData(currentParent, cacheDataArr, feeType, constructId, newDe);
    }
  }


  async customizeFilterBaseDe(args, baseDeList) {
    // 留给子类实现  这里不写逻辑
    throw new Error('留给子类实现方法');
  }

  async customizeFindBaseDeByCostDe(constructionMeasureType, costDe, baseDeList) {
    // 留给子类实现  这里不写逻辑
    throw new Error('留给子类实现方法');
  }

}

GljFgjzCostMatchStrategy.toString = () => '[class GljFgjzCostMatchStrategy]';
module.exports = GljFgjzCostMatchStrategy;
