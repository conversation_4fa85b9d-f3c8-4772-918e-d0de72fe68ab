
const { ObjectUtil } = require('../../../common/ObjectUtil');
const AppContext = require('../core/container/APPContext');
const WildcardMap = require('../core/container/WildcardMap');
const { Tree,TreeNode,arrayToTree } = require('../core/tools/ConstructTreeMap');
const BaseDomain = require('./core/BaseDomain');
const DeDomain = require('./DeDomain');
const ResourceDomain  = require('./ResourceDomain');
const ProjectTypeConstants = require('../constants/ProjectTypeConstants');
const DomainContext = require('./core/DomainContext');
const PropertyUtil = require('./utils/PropertyUtil');
const LRUCache = require('../core/tools/cache/LRUCache');
const EE = require("../../../core/ee");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const { Snowflake } = require('../utils/Snowflake');
const CommonConstants = require('../constants/CommonConstants');
const { ObjectUtils } = require('../utils/ObjectUtils');
const ProjectModel = require('./projectProcessor/models/ProjectModel');
const StandardDeModel = require('./deProcessor/models/StandardDeModel');
const ResourceModel = require('./deProcessor/models/ResourceModel');
const LabelConstants = require("../constants/LabelConstants");
const {ConvertUtil} = require("../utils/ConvertUtils");
const { ConstructProject } = require('../models/ConstructProject');
const { json } = require('stream/consumers');

class ProjectDomain extends BaseDomain {

  static KEY_PROJECT_TREE = "ProjectTree";
  static KEY_DE_TREE = "De";
  static KEY_RESOURCES = "Resources";
  static KEY_FUNCTION_DATA_MAP = "FunctionDataMap";

  static recentProject = new LRUCache(20)

  constructId;
  deDomain;
  resourceDomain;
  functionDataMap;

  /**
   *
   * @returns {*}
   */
  static getRecentProject() {
    return ProjectDomain.recentProject.toArray();
  }

  /**
   *
   * @param ProjectModel
   */
  static openProject(ProjectModel) {

  }

  /**
   * 获取domain 实例
   * @param pid
   * @returns {ProjectDomain|unknown}
   */
  static getDomain(pid) {
    let projectDomain;
    if (ObjectUtil.isEmpty(AppContext.getContext(pid))) {
      projectDomain = new ProjectDomain(pid);
    } else {
      projectDomain = AppContext.getContext(pid);
    }
    projectDomain.constructId = pid;
    return projectDomain;
  }
  /**
   * 获取导入项目的id，用来导入时替换为新的 
   * @param {*} JsonData 
   * @returns 
   */
  static async getImportProjectId(JsonData){
    let rootProject = JsonData[ProjectDomain.KEY_PROJECT_TREE].find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT && ObjectUtils.isEmpty(item.parentId));
    return rootProject.sequenceNbr;
  }

  /**
     * 获取导入项目的id，用来导入时替换为新的
     * @param {*} JsonData
     * @returns
     */
    static async getImportProjectRoot(JsonData) {
        let rootProject = JsonData[ProjectDomain.KEY_PROJECT_TREE].find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT && ObjectUtils.isEmpty(item.parentId));
        return rootProject;
    }

  /**
   * 从Json文件 导入工程
   * @param JsonData
   */
  static async importProject(JsonData)
  {
    let rootProject = JsonData[ProjectDomain.KEY_PROJECT_TREE].find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT && ObjectUtils.isEmpty(item.parentId));
    let projectDomain = ProjectDomain.getDomain(rootProject.sequenceNbr);
    
    let arrayProject =  JsonData[ProjectDomain.KEY_PROJECT_TREE];
    if(ObjectUtils.isNotEmpty(arrayProject)){
      // //先处理project排序
      // arrayProject.sort((a,b)=>{
      //   // 首先比较 type
      //   if (a.type < b.type) return -1;
      //   if (a.type > b.type) return 1;
      //   // 如果 type相同，则按照index排序
      //   if (a.index < b.index) return -1;
      //   if (a.index > b.index) return 1;
      //
      //   return 0;
      // });
      // for(const jsonProject of arrayProject)
      // {
      //   let newProject =  new ProjectModel(jsonProject.sequenceNbr,jsonProject.type,jsonProject.parentId);
      //   PropertyUtil.copyProperties(jsonProject,newProject);
      //   projectDomain.ctx.treeProject.addNode(newProject,projectDomain.ctx.treeProject.getNodeById(newProject.parentId));
      // }
      //如果按照上面的排序，则不同层级的子单项排序会错乱，导致二级排在了三级子单项后面，这样调用addNode时二级没有父级就赋值给了root出错

      let project = arrayProject.find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT);
      let newProject = new ProjectModel(project.sequenceNbr, project.type, project.parentId);
      PropertyUtil.copyProperties(project, newProject);
      projectDomain.ctx.treeProject.addNode(newProject, null);
      let singleList = arrayProject.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE);
      let unitList = arrayProject.filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
      if (ObjectUtil.isNotEmpty(singleList)) {
        let singleListC = [];
        this._calSingleSort(project.sequenceNbr, arrayProject, singleListC);
        for (let jsonProject of singleListC) {
          this._calSingleSortAddNode(projectDomain, jsonProject, singleListC);
        }
      }
      if (ObjectUtil.isNotEmpty(unitList)) {
        unitList = unitList.sort((a, b) => a.index - b.index);
        for (const jsonProject of unitList) {
          let newProject = new ProjectModel(jsonProject.sequenceNbr, jsonProject.type, jsonProject.parentId);
          PropertyUtil.copyProperties(jsonProject, newProject);
          projectDomain.ctx.treeProject.addNode(newProject, projectDomain.ctx.treeProject.getNodeById(newProject.parentId));
        }
      }
    }
    //非顺序处理
    let arrayDes =  JsonData[ProjectDomain.KEY_DE_TREE];
    arrayDes = this._buildOrderTree(arrayDes);
    for(let jsonDe of arrayDes){
      this._createDeTree(jsonDe,projectDomain);
    }
    // for(const jsonDe of JsonData[ProjectDomain.KEY_DE_TREE])
    // {

    //   let newDe = new StandardDeModel(jsonDe.constructId,jsonDe.unitId,jsonDe.sequenceNbr,jsonDe.parentId,jsonDe.type);
    //   PropertyUtil.copyProperties(jsonDe,newDe);
    //   //为了保持位置不变，需要判断index
    //   let indexDe = newDe.index;
    //   let parentDe = projectDomain.ctx.deMap.getNodeById(newDe.parentId);
    //   if(parentDe && parentDe.children && indexDe > parentDe.children.length){
    //     indexDe = null;
    //   }
    //   if(ObjectUtils.isEmpty(indexDe)){
    //     projectDomain.ctx.deMap.addNode(newDe,parentDe);
    //   }else{
    //     projectDomain.ctx.deMap.addNodeAt(newDe,parentDe,indexDe);
    //   }
    // }
    for(const jsonResource  of JsonData[ProjectDomain.KEY_RESOURCES])
    {
      let newResource = new ResourceModel(jsonResource.constructId, jsonResource.unitId,jsonResource.sequenceNbr, jsonResource.deRowId, jsonResource.type);
      PropertyUtil.copyProperties(jsonResource,newResource);
      projectDomain.ctx.resourceMap.set(WildcardMap.generateKey(jsonResource.unitId,jsonResource.deRowId,jsonResource.sequenceNbr),newResource);
    }
    //统一处理再次
    projectDomain.functionDataMap = PropertyUtil.newObjectToMapRecursive(JsonData[ProjectDomain.KEY_FUNCTION_DATA_MAP],
      [
        FunctionTypeConstants.TABLE_SETTING_CACHE,
        FunctionTypeConstants.PROJECT_PRECISION_SETTING,
        FunctionTypeConstants.DE_COST_CODE,
        FunctionTypeConstants.DE_SETTING_CACHE,
        FunctionTypeConstants.UNIT_CONVERSION,
      ]
    );
    this.transformFunctionDataMapType(projectDomain.functionDataMap,JsonData[ProjectDomain.KEY_FUNCTION_DATA_MAP]);
    //重新设置dedomain与projectDomain中的funcitonmap关系
    projectDomain.deDomain.functionDataMap = projectDomain.functionDataMap;
    return projectDomain;
  }
  static _createDeTree(jsonDe,projectDomain){
    let newDe = new StandardDeModel(jsonDe.constructId,jsonDe.unitId,jsonDe.sequenceNbr,jsonDe.parentId,jsonDe.type);
    PropertyUtil.copyProperties(jsonDe,newDe,["children"]);
    //为了保持位置不变，需要判断index
    let indexDe = newDe.index;
    let parentDe = projectDomain.ctx.deMap.getNodeById(newDe.parentId);
    if(parentDe && parentDe.children && indexDe > parentDe.children.length){
      indexDe = null;
    }
    if(ObjectUtils.isEmpty(indexDe)){
      projectDomain.ctx.deMap.addNode(newDe,parentDe);
    }else{
      projectDomain.ctx.deMap.addNodeAt(newDe,parentDe,indexDe);
    }
    if(jsonDe.children && jsonDe.children.length>0){
      jsonDe.children.sort((a,b)=>a.index-b.index);//排序数据
      for(let child of jsonDe.children){
        this._createDeTree(child,projectDomain);
      }
    }
  }
  static _buildOrderTree(items) {  
    let tree = [];  
    let itemById = {};  
  
    // 第一步：创建一个根据id索引的映射  
    items.forEach(item => {  
        itemById[item.sequenceNbr] = {...item, children: []};  
    });  
  
    // 第二步：为每个项找到并添加其子项  
    items.forEach(item => {  
        let parentId = item.parentId;  
        if (parentId === 0) {  
            // 如果是根节点，则添加到树中  
            tree.push(itemById[item.sequenceNbr]);  
        } else {  
            // 否则，添加到其父节点的children数组中  
            if (itemById[parentId]) {  
                itemById[parentId].children.push(itemById[item.sequenceNbr]);  
            }  
        }  
    });  
  
    return tree;  
  }  

  static _calSingleSort(parentId, treeList, singleList) {
        let singleList1 = treeList.filter(item => item.parentId === parentId && item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE).sort((a, b) => a.index - b.index);
        if (ObjectUtils.isNotEmpty(singleList1)) {
            singleList.push(...singleList1);
            for (let singleObj of singleList1) {
                this._calSingleSort(singleObj.sequenceNbr, treeList, singleList);
            }
        }
    }


    static _calSingleSortAddNode(projectDomain, jsonProject, singleList) {
        if (ObjectUtil.isNotEmpty(projectDomain.ctx.treeProject.getNodeById(jsonProject.parentId))) {
            let newProject = new ProjectModel(jsonProject.sequenceNbr, jsonProject.type, jsonProject.parentId);
            PropertyUtil.copyProperties(jsonProject, newProject);
            projectDomain.ctx.treeProject.addNode(newProject, projectDomain.ctx.treeProject.getNodeById(newProject.parentId));
        } else {
            let jsonProjectParent = singleList.find(item => item.sequenceNbr == jsonProject.parentId);
            let newProject = new ProjectModel(jsonProjectParent.sequenceNbr, jsonProjectParent.type, jsonProjectParent.parentId);
            PropertyUtil.copyProperties(jsonProjectParent, newProject);
            this._calSingleSortAddNode(projectDomain, jsonProjectParent, singleList);

            let newProject1 = new ProjectModel(jsonProject.sequenceNbr, jsonProject.type, jsonProject.parentId);
            PropertyUtil.copyProperties(jsonProject, newProject1);
            projectDomain.ctx.treeProject.addNode(newProject1, projectDomain.ctx.treeProject.getNodeById(newProject1.parentId));
        }
    }


  static transformFunctionDataMapType(map,json){
    if(ObjectUtils.isEmpty(json)){
        return  null;
    }
    let { service } = EE.app;
    if(ObjectUtil.isEmpty(json[FunctionTypeConstants.PROJECT_USER_RCJ])){
      map.set(FunctionTypeConstants.PROJECT_USER_RCJ,null);
    }
    if(ObjectUtil.isEmpty(json[FunctionTypeConstants.RCJ_MEMORY])){
      map.set(FunctionTypeConstants.RCJ_MEMORY,null);
    }

    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.RCJ_COLLECT])){
     let  rcjMap=  this.buildNextLevelMap(json[FunctionTypeConstants.RCJ_COLLECT]);
      map.set(FunctionTypeConstants.RCJ_COLLECT,rcjMap);
    }
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_USER_RCJ])){
      let  userRcjMap=  this.buildNextLevelMap(json[FunctionTypeConstants.PROJECT_USER_RCJ]);
      map.set(FunctionTypeConstants.PROJECT_USER_RCJ,userRcjMap);
    }
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_GS_ADJUST])){
        let  array =this.buildNextLevelArray(json[FunctionTypeConstants.PROJECT_GS_ADJUST]);
      map.set(FunctionTypeConstants.PROJECT_GS_ADJUST,array);
    }
    // 概算汇总-工程项目
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_GS_SUMMARY])){
      let  array =this.buildNextLevelArray(json[FunctionTypeConstants.PROJECT_GS_SUMMARY]);
      map.set(FunctionTypeConstants.PROJECT_GS_SUMMARY,array);
    }
    // 概算汇总费用代码-工程项目
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_GS_CODE])){
      let  array =this.buildNextLevelArray(json[FunctionTypeConstants.PROJECT_GS_CODE]);
      map.set(FunctionTypeConstants.PROJECT_GS_CODE,array);
    }
    // 建设其他费-工程项目
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_JSQTF])){
      let  array =this.buildNextLevelArray(json[FunctionTypeConstants.PROJECT_JSQTF]);
      map.set(FunctionTypeConstants.PROJECT_JSQTF,array);
    }
    // 建设其他费用代码-工程项目
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_JSQTF_CODE])){
      let  array =this.buildNextLevelArray(json[FunctionTypeConstants.PROJECT_JSQTF_CODE]);
      map.set(FunctionTypeConstants.PROJECT_JSQTF_CODE,array);
    }
    // 建设其他费用计算器-工程项目
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_JSQTF_JSQ])){
      let  array =this.buildNextLevelArray(json[FunctionTypeConstants.PROJECT_JSQTF_JSQ]);
      map.set(FunctionTypeConstants.PROJECT_JSQTF_JSQ,array);
    }
    // 取费表-工程项目
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_QFB])){
      let result = service.PreliminaryEstimate.gsFreeRateService.transProjectQfb(json[FunctionTypeConstants.PROJECT_QFB]);
      map.set(FunctionTypeConstants.PROJECT_QFB, result);
    }
    // 取费表-单位工程
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.UNIT_QFB])){
      let result = service.PreliminaryEstimate.gsFreeRateService.transUnitQfb(json[FunctionTypeConstants.UNIT_QFB]);
      map.set(FunctionTypeConstants.UNIT_QFB, result);
    }
    // 费用查看-工程项目
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_FYCK])){
      let result = service.PreliminaryEstimate.gsFreeViewService.transJson2Fyck(json[FunctionTypeConstants.PROJECT_FYCK]);
      map.set(FunctionTypeConstants.PROJECT_FYCK, result);
    }
    // 费用查看-单项工程
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.SINGLE_FYCK])){
      let result = service.PreliminaryEstimate.gsFreeViewService.transUnitJson2Fyck(json[FunctionTypeConstants.SINGLE_FYCK]);
      map.set(FunctionTypeConstants.SINGLE_FYCK, result);
    }
    // 费用查看-单位工程
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.UNIT_FYCK])){
      let result = service.PreliminaryEstimate.gsFreeViewService.transUnitJson2Fyck(json[FunctionTypeConstants.UNIT_FYCK]);
      map.set(FunctionTypeConstants.UNIT_FYCK, result);
    }
    // 工程量明细
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.UNIT_QUANTITIES])){
      let result = service.PreliminaryEstimate.gsQuantitiesService.transGclmx(json[FunctionTypeConstants.UNIT_QUANTITIES]);
      map.set(FunctionTypeConstants.UNIT_QUANTITIES, result);
    }
    //用户自定义定额
    if(ObjectUtil.isNotEmpty(json[FunctionTypeConstants.PROJECT_USER_DE])){
      map.set(FunctionTypeConstants.PROJECT_USER_DE,json[FunctionTypeConstants.PROJECT_USER_DE]);
    }
    // 人材机主要材料设置
    if (ObjectUtil.isNotEmpty(json[FunctionTypeConstants.MAIN_MATERIAL_SETTING])) {
      let result = service.PreliminaryEstimate.gsRcjCollectService.transUnitMainMaterialSetting(json[FunctionTypeConstants.MAIN_MATERIAL_SETTING]);
      map.set(FunctionTypeConstants.MAIN_MATERIAL_SETTING, result);
    }
  }

  static buildNextLevelArray(json){
    let  newArray = new Array();
    for (const obj in json) {
      newArray.push(json[obj]);
    }
    return newArray;
  }


  static buildNextLevelMap( json ){
    if(ObjectUtil.isEmpty(json)){
      return  null
    }
    let  rcjNewMap = new Map();
    // 遍历对象的所有键
    for (const key in json) {
      rcjNewMap.set(key, json[key]);
      if (key.startsWith("UNIT_COLOR")) {
        let  nextMap =this.buildNextLevelMap(json[key]);
        rcjNewMap.set(key, nextMap);
      }
    }
    return  rcjNewMap;
  }

  static convertPropertiesToMapKeys(cls){
    const map = new Map();
    const keys = Object.keys(cls);
  
    for (const key of keys) {
      if(key.startsWith('JBXX_KEY_TYPE') 
        || key.startsWith('SBGZF_KEY_TYPE')
        || key.startsWith('SEPARATOR')
      ){
        continue;
      }
      // 检查是否为静态属性而不是原型上的属性或其他非静态成员
      if (typeof cls[key] !== 'function') {
          map.set(cls[key],1);
      }
    }
    return map;
  }
  /**
   * 构造函数,初始化父类,并初始化项目容器,定额容器,人材机容器,功能数据容器
   * @param pid
   */
  constructor(pid) {
    super();
    if (ObjectUtil.isEmpty(AppContext.getContext(pid))) {
                                    //项目map     //定额map    //人材机map
      super.ctx = new DomainContext(new Tree(), new Tree(), new WildcardMap())
      this.functionDataMap = new Map();
      this.deDomain = new DeDomain(this.ctx);
      this.deDomain.functionDataMap = this.functionDataMap
      this.resourceDomain = new ResourceDomain(this.ctx);
      this.initData4TopProject(pid);
      AppContext.addContext(pid, this);
    }
  }


  /**
   * 初始化领域对象时 初始化其他周边功能属性
   * @param pid
   */
  initData4TopProject(pid) {
    this.deDomain.initData(pid);
    // 初始化建设其他费
    let { service } = EE.app;
    //建设其他费
    this.functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF, service.PreliminaryEstimate.gsOtherProjectCostService.defaultOtherProjectCost(pid));
    //建设其他费费用代码
    this.functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF_CODE, service.PreliminaryEstimate.gsOtherProjectCostService.defaultOtherProjectCostCode(pid));
    //建设其他费费用计算器
    this.functionDataMap.set(FunctionTypeConstants.PROJECT_JSQTF_JSQ, service.PreliminaryEstimate.gsOtherProjectCostService.defaultOtherProjectCostCalculators(pid));
    //概算汇总
    this.functionDataMap.set(FunctionTypeConstants.PROJECT_GS_SUMMARY, service.PreliminaryEstimate.gsEstimateSummaryService.defaultEstimateSummary(pid));
    //概算费用代码
    this.functionDataMap.set(FunctionTypeConstants.PROJECT_GS_CODE, service.PreliminaryEstimate.gsEstimateCodeService.defaultEstimateCode(pid));
    //工程基本信息
    this.functionDataMap.set(FunctionTypeConstants.JBXX_KEY, new Map());
    //设备购置费信息
    this.functionDataMap.set(FunctionTypeConstants.SBGZF_KEY, new Map().set(service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null,FunctionTypeConstants.SBGZF_KEY_TYPE_GN), service.PreliminaryEstimate.gsEquipmentCostsService.initData(FunctionTypeConstants.SBGZF_KEY_TYPE_GN))
              .set(service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null,FunctionTypeConstants.SBGZF_KEY_TYPE_GW), service.PreliminaryEstimate.gsEquipmentCostsService.initData(FunctionTypeConstants.SBGZF_KEY_TYPE_GW))
              .set(service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null,FunctionTypeConstants.SBGZF_KEY_TYPE_HZ), service.PreliminaryEstimate.gsEquipmentCostsService.initData(FunctionTypeConstants.SBGZF_KEY_TYPE_HZ))
              .set(service.PreliminaryEstimate.gsEquipmentCostsService.getDataMapKey(null,FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_SBF), 0));

    //设备进口计算器
    this.functionDataMap.set(FunctionTypeConstants.SBGZF_KEY_TYPE_HZ_JSQ,new Map());
    //单位独立费
    this.functionDataMap.set(FunctionTypeConstants.UNIT_DLF_KEY,new Map());//独立费
    // 单位费用代码
    this.functionDataMap.set(FunctionTypeConstants.UNIT_COST_CODE,new Map());
    // 单位费用汇总
    this.functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY,new Map());
    // 单位费用汇总合计
    this.functionDataMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY_TOTAL,new Map());


  }

  /**
   * 获得当前工程的定额领域模型
   * @returns {*}
   */
  getDeDomain() {
    return this.deDomain;
  }

  /**
   * 获得当前工程的人材机领域模型
   * @returns {*}
   */
  getResourceDomain() {
    return this.resourceDomain;
  }

  getRoot()
  {
    return this.ctx.treeProject.getAllNodes().find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT && ObjectUtils.isEmpty(item.parentId));
  }
  /**
   *通过自定义function 获得当前项目上下文中的项目
   * @param predicate
   * @returns {unknown}
   */
  getProject(predicate) {
    return this.ctx.treeProject.getAllNodes().filter(predicate);
  }

  /**
   * 通过工程ID 获得工程实例
   * @param projectId
   * @returns {*}
   */
  getProjectById(projectId) {
    return this.ctx.treeProject.getNodeById(projectId);
  }

  /**
   * 获得当前上下问的项目数组
   * @param pid
   * @returns {{displaySign: *, code: *, name: *, sequenceNbr: *, type: *, parentId: *}[]}
   */
  getProjectTree(pid) {
    let nodes = this.ctx.treeProject.getAllNodes();
    nodes.sort((a,b)=>{
      // 首先比较 type  
      if (a.type < b.type) return -1;
      if (a.type > b.type) return 1;
      // 如果 type 相同，则比较 index  
      if (a.index < b.index) return -1;
      if (a.index > b.index) return 1;
      // 如果 type 和 index 都相同，则返回 0  
      return 0;
    });
    return PropertyUtil.shallowCopyAndFilterProperties(nodes, BaseDomain.avoidProperty).map(ProjectDomain.filter4ProjectTree);
  }



  /**
   * 创建项目
   * @param projectModel
   */
  async createProject(projectModel) {
    let parentNode = null;
    switch (projectModel.type) {
      case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
        projectModel.fileCode = CommonConstants.PROJECT_CODE_PREFIX + Snowflake.nextId();//设置fileCode
        projectModel.pricingMethod = CommonConstants.COMMON_YES;// 默认值为按市场价组价
        break;
      case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
        parentNode = this.ctx.treeProject.getNodeById(projectModel.parentId);
        break;
      case ProjectTypeConstants.PROJECT_TYPE_UNIT:
        parentNode = this.ctx.treeProject.getNodeById(projectModel.parentId);
        break;
    }
    projectModel.scopeFlag = true;
    this.ctx.treeProject.addNode(projectModel, parentNode);
    await this.doAfterCreate(projectModel);
  }
  async moveUpAndDown(sequenceNbr,type){
    let node = this.ctx.treeProject.getNodeById(sequenceNbr);
    if(ObjectUtil.isNotEmpty(node) && ObjectUtil.isNotEmpty(node.parentId))
    {
      let parentNode = this.ctx.treeProject.getNodeById(node.parentId);
      if(ObjectUtil.isNotEmpty(parentNode)){
        let changeChildNode = null;
        let childrens = parentNode.children;
        if(ObjectUtils.isEmpty(childrens)){
          childrens = this.getProject(item=>item.parentId === parentNode.sequenceNbr);
          parentNode.children = childrens;
        }

        let curIndex = node.index;
        for(let child of childrens){
          if(child.sequenceNbr === sequenceNbr){
            continue
          }
          if(type === "up" && child.index < curIndex){
            if(changeChildNode != null && changeChildNode.index > child.index){
              continue;
            }
            changeChildNode = child;
          }
          if(type === "down" && child.index > curIndex){
            if(changeChildNode != null && changeChildNode.index < child.index){
              continue;
            }
            changeChildNode = child;
          }
        }
        if(changeChildNode != null){
          let nexIndex = changeChildNode.index;
          node.index = nexIndex;
          changeChildNode.index = curIndex;
        }
        childrens.sort((a,b)=>{
          //比较 index  
          if (a.index < b.index) return -1;
          if (a.index > b.index) return 1;
          // 如果 index 都相同，则返回 0  
          return 0;
        });
      }
    }
  }
  /**
   *可插入 项目创建后 需要进行的业务操作 ,如对不同类型初始化数据: do4Type
   * @param projectModel
   * @returns {Promise<void>}
   */
  async doAfterCreate(projectModel)
  {
    await this.init4Type(projectModel);
  }

  /**
   * 为不同类型初始化数据
   * @param projectModel
   * @returns {Promise<void>}
   */
  async init4Type(projectModel)
  {
    //功能模块初始化
    let { service } = EE.app;
    switch (projectModel.type) {
      case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
        if(ObjectUtil.isEmpty(this.ctx.treeProject.getAllNodes().filter(item => item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE && item.parentId === projectModel.constructId)))
        {
          //创建默认单项工程
          let deFaultSingleProject = new ProjectModel(Snowflake.nextId(),ProjectTypeConstants.PROJECT_TYPE_SINGLE,projectModel.constructId);
          PropertyUtil.copyProperties(deFaultSingleProject,deFaultSingleProject,["sequenceNbr"])
          deFaultSingleProject.name = "单项工程";
          deFaultSingleProject.code = LabelConstants.LABEL_DEFAULT_DE + Snowflake.nextId();
          this.ctx.treeProject.addNode(deFaultSingleProject, projectModel);
        }
        await service.PreliminaryEstimate.gsInitProjectService.init(projectModel, this.constructId);
        break;
      case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
        await service.PreliminaryEstimate.gsInitSingleProjectService.init(projectModel, this.constructId);
        break;
      case ProjectTypeConstants.PROJECT_TYPE_UNIT:
        await service.PreliminaryEstimate.gsInitUnitProjectService.init(projectModel, this.constructId);
        projectModel.defaultDeId = this.deDomain.initDefaultDE(this.constructId,projectModel.sequenceNbr)?.sequenceNbr;
        break;
      default:
    }

  }

  /**
   * 更新项目
   * @param projectModel
   */
  updateProject(projectModel) {
    let project =  this.ctx.treeProject.getNodeById(projectModel.sequenceNbr);
    if(!ObjectUtil.isEmpty(project))
    {
      //同步更新项目基本信息

      if(project.name !== projectModel.name && project.type !== ProjectTypeConstants.PROJECT_TYPE_SINGLE){
        
        let { service } = EE.app;
        let unitId = null;
        let type = FunctionTypeConstants.JBXX_KEY_TYPE_11;
        let nameKey = "项目名称";
        if(project.type === ProjectTypeConstants.PROJECT_TYPE_UNIT){
          unitId = project.sequenceNbr;
          nameKey="工程名称";
        }
        let listKey = service.PreliminaryEstimate.gsOverviewService.getDataMapKey(unitId,type)
        let list = this.functionDataMap.get(FunctionTypeConstants.JBXX_KEY).get(listKey);
        list.forEach(item => {if(item.name === nameKey){item.remark = projectModel.name;}});
        this.functionDataMap.get(FunctionTypeConstants.JBXX_KEY).set(listKey,list);
      }

      console.log("修改前:" + project.name);
      PropertyUtil.copyProperties(projectModel,project,["children"]);

      project.updateValue(project);
      console.log("修改后:" + projectModel.name);
    }
    else {
      throw Error("没有找到工程:"+projectModel.sequenceNbr);
    }
  }

  /**
   * 删除项目
   * @param pid
   */
  async removeProject(pid) {
    let project = this.ctx.treeProject.getNodeById(pid);
    if(ObjectUtil.isEmpty(project)){
      return false;
    }
    let { service } = EE.app;
    switch (project.type) {
      case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
            this.ctx.treeProject.removeNode(pid);
            this.clearAll();
            await service.PreliminaryEstimate.gsInitProjectService.remove(pid, this.constructId);
            break;
      case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
            let {singleList,unitList } = this.getSubProjects(pid);
            for(let unitId of unitList)
            {
              this.removeUnit(unitId);
            }
            for(let singleId of singleList)
            {
              this.ctx.treeProject.removeNode(singleId);
            }
            await service.PreliminaryEstimate.gsInitSingleProjectService.remove(unitList, this.constructId);
            break;
      case ProjectTypeConstants.PROJECT_TYPE_UNIT:
            this.removeUnit(pid);
            await service.PreliminaryEstimate.gsInitUnitProjectService.remove(pid, this.constructId);
            break;
    }
    return true;

  }

  /**
   * 删除所有内存对象
   */
  clearAll()//
  {
    this.ctx.treeProject.clear();
    this.ctx.deMap.clear();
    this.functionDataMap.clear();
    this.deDomain = null;
    this.resourceDomain = null;
    AppContext.removeContext(this.constructId);
    console.log("constructId: " + this.constructId +" has been cleared from AppContext.")
  }

  /**
   * 通过单项工程ID 递归获得下面的所有单位工程
   * @param singleId
   * @returns {{singleList: *[], unitList: *[]}}
   */
  getSubProjects(singleId)
  {
      let unitList = [];
      let singleList =[];
      let project = this.ctx.treeProject.getNodeById(singleId);
      this.findUnit(project,unitList,singleList);
      singleList.push(singleId);
      return {"singleList" : singleList,
              "unitList" : unitList}
  }

  /**
   * 通过单项工程ID 递归获得下面的所有单位工程
   * @param project
   * @param unitList
   * @param singleList
   */
  findUnit(project,unitList,singleList)
  {

    for(let child of project.children) {
      if (child.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
        singleList.push(child.sequenceNbr);
        this.findUnit(child, unitList, singleList);
      } else {
        unitList.push(child.sequenceNbr);
      }
    }

  }

  /**
   * 删除单位工程
   * @param unitId
   */
  removeUnit(unitId){
    this.ctx.treeProject.removeNode(unitId);
    this.notify();
  }

  notify() {
    //Project
    //
    //
    // Calculator
  }


  /**
   * 过滤一些非必要属性
   * @param project
   * @returns {{defaultDeId, displaySign, code, deLibrary, name, constructMajorType, sequenceNbr, type, parentId}}
   */
  static filter4ProjectTree(project) {
    const {sequenceNbr, type,parentId,deLibrary,constructMajorType,displaySign,name, code,defaultDeId,path,scopeFlag} = project; // 解构赋值，只保留需要的属性
    return {sequenceNbr, type,parentId,deLibrary,constructMajorType,displaySign,name, code,defaultDeId,path,scopeFlag}; // 返回一个只包含所需属性的新对象
  }

  findParents(projectModel, parentList, types){

    if(ObjectUtil.isNotEmpty(projectModel.parentId)){
      let parentProject = this.getProjectById(projectModel.parentId);
      if(ObjectUtil.isNotEmpty(parentProject)){
        if(types.includes(parentProject.type)){
          parentList.push(parentProject);
        }
        this.findParents(parentProject,parentList,types);
      }
    }
  }
}
ProjectDomain.toString = () => 'ProjectDomain';
module.exports = ProjectDomain;


