const {Service} = require('../../../core');
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {FreeRateModel} = require("../models/FreeRateModel");
const gsFreeRate = require("../jsonData/gs_free_rate.json");
const WildcardMap = require("../core/container/WildcardMap");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {FreeRateProjectModel} = require("../models/FreeRateProjectModel");
const gsRcjCollectType = require("../jsonData/gs_rcj_collect_type.json");

/**
 * 费率 service
 * @class
 */
class GsFreeRateService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取所有费率
     * @returns {Promise<GsBaseFreeRate[]|Error>}
     */
    async getByFreeRateAll() {
        return await this.gsBaseDeLibraryDao.find({});
    }

    /**
     * 获取费率说明
     * @param args
     * @returns {Promise<*>}
     */
    async feeDescriptionData(args) {
        return gsFreeRate.feeDescriptionData;
    }

    /**
     * 获取费率说明
     * @param args
     * @returns {Promise<*>}
     */
    async getProjectQfbDes(args) {
        let {constructId} = args;
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateProjectModelList = ConvertUtil.deepCopy(gsFreeRate.feeDescriptionData);
        for (let item of freeRateProjectModelList) {
            if (item.field === "projectType") {
                item.default = freeRateProjectModel.projectType
            }
            if (item.field === "payTaxesAreas") {
                item.default = freeRateProjectModel.payTaxesAreas
            }
        }
        return freeRateProjectModelList;
    }

    /**
     * 获取单位工程费率说明
     * @param args
     * @returns {Promise<*>}
     */
    async getUnitQfbDes(args) {
        let {constructId, unitId} = args;
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(constructId, unitId);
        let freeKey = WildcardMap.generateKey(unitId, unitProject.constructMajorType);
        let unitfreeRate =  unitQfbMap.get(freeKey);
        let freeRateProjectModelList = ConvertUtil.deepCopy(gsFreeRate.feeDescriptionData);
        for (let item of freeRateProjectModelList) {
            if (item.field === "projectType") {
                item.default = unitfreeRate.projectType
            }
            if (item.field === "payTaxesAreas") {
                item.default = unitfreeRate.payTaxesAreas
            }
        }
        return freeRateProjectModelList;
    }

    /**
     * 获取费率目录树
     * @param args
     * @returns {Promise<*>}
     */
    async feeCatalogueData(args) {
        return gsFreeRate.feeCatalogueData;
    }

    /**
     * 恢复单位工程费率
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode 定额库编码
     * @param projectType 所属工程专业
     * @param payTaxesAreas 纳税地区
     */
    async recoverUnitFreeRate(args) {
        let {constructId, unitId, libraryCode, projectType, payTaxesAreas} = args;
        let freeRate = await this.getBaseFreeRate(args);
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let freeKey = WildcardMap.generateKey(unitId, freeRate.libraryCode);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRate));
        return freeRate;
    }

    /**
     * 根据条件查询 单位工程费率
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode 定额库编码
     * @param projectType 所属工程专业
     * @param payTaxesAreas 纳税地区
     */
    async getBaseFreeRate(args) {
        let {constructId, unitId, libraryCode, projectType, payTaxesAreas} = args;
        let gsBaseFreeRate = await this.service.PreliminaryEstimate.gsBaseFreeRateService.searchFreeRateInfo(args);
        let freeRate = ObjectUtils.copyProp(gsBaseFreeRate, new FreeRateModel());
        freeRate.init();
        freeRate.constructId = constructId;
        freeRate.unitId = unitId;
        return freeRate;
    }

    /**
     * 修改单位工程费率
     * @param freeRateModel
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode  定额库编码'
     * @param manageFeeRate  企业管理费（%）'
     * @param profitRate  利润（%）'
     * @param taxRate  税金（%）'
     * @param gfRate  规费（%）'
     * @param anwenRate  安全生产、文明施工费（%）'
     */
    async updateUnitFreeRate(freeRateModel) {
        let {constructId, unitId, libraryCode, manageFeeRate, profitRate, taxRate, gfRate, anwenRate} = freeRateModel
        // 获取费率工程项目id
        let freeKey = WildcardMap.generateKey(unitId, libraryCode);
        let freeRate = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB).get(freeKey);
        freeRate.manageFeeRate = ObjectUtils.isEmpty(manageFeeRate)? freeRate.manageFeeRateL: Number(manageFeeRate);
        freeRate.profitRate = ObjectUtils.isEmpty(profitRate)? freeRate.profitRate: Number(profitRate);
        freeRate.taxRate = ObjectUtils.isEmpty(taxRate)? freeRate.taxRate: Number(taxRate);
        freeRate.gfRate = ObjectUtils.isEmpty(gfRate)? freeRate.gfRate: Number(gfRate);
        freeRate.anwenRate = ObjectUtils.isEmpty(anwenRate)? freeRate.anwenRate: Number(anwenRate);
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRate));
        this.service.PreliminaryEstimate.gsUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, freeRate);
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
        return freeRate;
    }

    /**
     * 修改单位工程费率
     * @param freeRateModel
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode  定额库编码'
     * @param manageFeeRate  企业管理费（%）'
     * @param profitRate  利润（%）'
     * @param taxRate  税金（%）'
     * @param gfRate  规费（%）'
     * @param anwenRate  安全生产、文明施工费（%）'
     */
    async updateUnitFree(freeRateModel) {
        let {constructId, unitId} = freeRateModel
        // 获取费率工程项目id
        let freeKey = WildcardMap.generateKey(unitId, freeRateModel.libraryCode);
        let freeRate = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB).get(freeKey);
        let freeRateResult = ObjectUtils.copyProp(freeRate, new FreeRateModel());
        ObjectUtils.copyProp(ObjectUtils.removeUndefined(ConvertUtil.deepCopy(freeRateModel)), freeRateResult);
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRateResult));
    }

    /**
     * 新增费率
     * @param constructId
     * @param unitId
     * @param libraryCode
     * @returns {Promise<void>}
     */
    async addUnitFreeRate(constructId, unitId, libraryCode) {
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(constructId, unitId);
        let freeKey0 = WildcardMap.generateKey(unitId, unitProject.constructMajorType);
        let unitfreeRate =  unitQfbMap.get(freeKey0);
        let payTaxesAreas = unitfreeRate.payTaxesAreas;
        let projectType = unitfreeRate.projectType;
        let args = {constructId, unitId, libraryCode, projectType, payTaxesAreas};
        let freeRate = await this.getBaseFreeRate(args);
        let freeKey = WildcardMap.generateKey(unitId, libraryCode);
        if (ObjectUtils.isEmpty(freeRate.diffFreeRate)) {
            freeRate.diffFreeRate = []
        }
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRate));
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let childFreeRate = freeRateProjectModel.childFreeRate;
        if (ObjectUtils.isNotEmpty(childFreeRate) && ObjectUtils.isEmpty(childFreeRate.get(libraryCode))){
            let projectFreeRate = ObjectUtils.copyProp(freeRate, new FreeRateModel())
            projectFreeRate.unitId = undefined
            childFreeRate.set(libraryCode, projectFreeRate);
        }
        return freeRate;
    }

    /**
     * 修改工程项目费率
     * @param freeRateModel
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode  定额库编码'
     * @param manageFeeRate  企业管理费（%）'
     * @param profitRate  利润（%）'
     * @param taxRate  税金（%）'
     * @param gfRate  规费（%）'
     * @param anwenRate  安全生产、文明施工费（%）'
     */
    async updateProjectFreeRate(freeRateModel) {
        let {constructId, unitId, libraryCode, manageFeeRate, profitRate, taxRate, gfRate, anwenRate} = freeRateModel
        // 获取费率工程项目id
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let cacheFreeProject = freeRateProjectModel.cacheFreeProject?
            ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject):
            ConvertUtil.deepCopy(freeRateProjectModel);
        let freeRate = cacheFreeProject.childFreeRate.get(libraryCode);
        freeRate.manageFeeRate = ObjectUtils.isEmpty(manageFeeRate)? freeRate.manageFeeRateL: Number(manageFeeRate);
        freeRate.profitRate = ObjectUtils.isEmpty(profitRate)? freeRate.profitRate: Number(profitRate);
        freeRate.taxRate = ObjectUtils.isEmpty(taxRate)? freeRate.taxRate: Number(taxRate);
        freeRate.gfRate = ObjectUtils.isEmpty(gfRate)? freeRate.gfRate: Number(gfRate);
        freeRate.anwenRate = ObjectUtils.isEmpty(anwenRate)? freeRate.anwenRate: Number(anwenRate);
        cacheFreeProject.childFreeRate.set(libraryCode, freeRate);

        freeRateProjectModel.cacheFreeProject = cacheFreeProject;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompare(cacheFreeProject, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
        return Array.from(cacheFreeProject.childFreeRate.values());
    }

    /**
     * 修改单位工程费率 工程专业或地区
     * @param freeRateModel
     * libraryCode  定额库编码'
     * projectType  所属工程专业'
     * payTaxesAreas  纳税地区'
     */
    async changeUnitTypeAndAreas(args) {
        let {constructId, unitId, projectType, payTaxesAreas} = args;
        let unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(constructId, unitId);
        let libraryCode = unitProject.constructMajorType;
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let freeRateModelList = ObjectUtils.getMapWithKeysStartingWith2(unitQfbMap, args.unitId);
        for (let item of freeRateModelList) {
            let param = {
                constructId: constructId,
                projectType: projectType,
                payTaxesAreas: payTaxesAreas,
                libraryCode: item.libraryCode,
            }
            let freeRate = await this.getBaseFreeRate(param);
            freeRate.unitId = item.unitId;
            let freeKey = WildcardMap.generateKey(unitId, item.libraryCode);
            unitQfbMap.set(freeKey, freeRate);

            await this.service.PreliminaryEstimate.gsUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, freeRate);
        }
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
    }

    /**
     * 修改单位工程费率 工程专业或地区
     * @param freeRateModel
     * isSub 是否统一应用 到单位工程
     * projectType  所属工程专业'
     * payTaxesAreas  纳税地区'
     */
    async changeProjectTypeAndAreas(args) {
        let {constructId, projectType, payTaxesAreas} = args;
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let cacheFreeProject = freeRateProjectModel.cacheFreeProject?
            ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject):
            ConvertUtil.deepCopy(freeRateProjectModel);
        cacheFreeProject.projectType = projectType;
        cacheFreeProject.payTaxesAreas = payTaxesAreas;

        for (let libraryCode of cacheFreeProject.childFreeRate.keys()) {
            let param = {
                constructId: constructId,
                projectType: projectType,
                payTaxesAreas: payTaxesAreas,
                libraryCode: libraryCode,
            }
            let freeRate = await this.getBaseFreeRate(param);
            cacheFreeProject.childFreeRate.set(libraryCode, freeRate);
        }
        freeRateProjectModel.cacheFreeProject = cacheFreeProject;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompare(cacheFreeProject, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
        return Array.from(cacheFreeProject.childFreeRate.values());
    }

    /**
     * 获取工程项目费率表
     * @param args
     */
    async getProjectQfbList(args) {
        let constructId = args.constructId;
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
        // 获取工程项目取费表
        return Array.from(freeRateProjectModel.childFreeRate.values());
    }

    /**
     * 获取单位工程费率表
     * @param args
     */
    async getUnitQfbList(args) {
        let {constructId, unitId} = args;
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
        // 获取单位工程取费表
        return ObjectUtils.getMapWithKeysStartingWith2(ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB), unitId);
    }

    /**
     * 单位工程和工程项目对应费率 进行比较
     */
    async _updateFreeRateCompare2(constructId) {
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompare(freeRateProjectModel, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
    }

    /**
     * 单位工程和工程项目对应费率 进行比较
     */
    async _updateFreeRateCompare(freeRateProjectModel, freeRateUnitMap) {
        for (let libraryCode of freeRateProjectModel.childFreeRate.keys()) {
            let projectFreeRate = freeRateProjectModel.childFreeRate.get(libraryCode);
            let diffProjectFreeRate = [];
            for (let freeKey of freeRateUnitMap.keys()) {
                let unitfreeRate =  freeRateUnitMap.get(freeKey);
                let diffUnitFreeRate = [];
                if (libraryCode === unitfreeRate.libraryCode) {
                    if (!(projectFreeRate.manageFeeRate === unitfreeRate.manageFeeRate)) {
                        diffUnitFreeRate.push(FreeRateModel.MANAGE_FEE_RATE);
                        diffProjectFreeRate.push(FreeRateModel.MANAGE_FEE_RATE);
                    }
                    if (!(projectFreeRate.profitRate === unitfreeRate.profitRate)) {
                        diffUnitFreeRate.push(FreeRateModel.PROFIT_RATE);
                        diffProjectFreeRate.push(FreeRateModel.PROFIT_RATE);
                    }
                    if (!(projectFreeRate.taxRate === unitfreeRate.taxRate)) {
                        diffUnitFreeRate.push(FreeRateModel.TAX_RATE);
                        diffProjectFreeRate.push(FreeRateModel.TAX_RATE);
                    }
                    if (!(projectFreeRate.gfRate === unitfreeRate.gfRate)) {
                        diffUnitFreeRate.push(FreeRateModel.GF_RATE);
                        diffProjectFreeRate.push(FreeRateModel.GF_RATE);
                    }
                    if (!(projectFreeRate.anwenRate === unitfreeRate.anwenRate)) {
                        diffUnitFreeRate.push(FreeRateModel.ANWEN_RATE);
                        diffProjectFreeRate.push(FreeRateModel.ANWEN_RATE);
                    }
                    unitfreeRate.diffFreeRate = diffUnitFreeRate.filter((value, index, arr) => arr.indexOf(value) === index);
                }
            }
            projectFreeRate.diffFreeRate = diffProjectFreeRate.filter((value, index, arr) => arr.indexOf(value) === index);
        }
    }

    /**
     * 统一应用
     * @param args
     */
    async unifiedApplication(args) {
        let {constructId} = args
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let cacheFreeProject = freeRateProjectModel.cacheFreeProject?
            ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject):
            ConvertUtil.deepCopy(freeRateProjectModel);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, cacheFreeProject);

        let freeRateUnitModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        for (let libraryCode of cacheFreeProject.childFreeRate.keys()) {
            let projectFreeRate = cacheFreeProject.childFreeRate.get(libraryCode);
            for (let freeKey of freeRateUnitModel.keys()) {
                let unitfreeRate =  freeRateUnitModel.get(freeKey);
                if (libraryCode === unitfreeRate.libraryCode) {
                    unitfreeRate.projectType = projectFreeRate.projectType;
                    unitfreeRate.payTaxesAreas = projectFreeRate.payTaxesAreas;
                    unitfreeRate.manageFeeRate = projectFreeRate.manageFeeRate;
                    unitfreeRate.profitRate = projectFreeRate.profitRate;
                    unitfreeRate.taxRate = projectFreeRate.taxRate;
                    unitfreeRate.gfRate = projectFreeRate.gfRate;
                    unitfreeRate.anwenRate = projectFreeRate.anwenRate;
                    let unitId = freeKey.split('--')[0]
                    await this.service.PreliminaryEstimate.gsUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, unitfreeRate);
                }
            }
        }
    }

    /**
     * 导入项目后费率恢复
     * @param constructId
     */
    afterImportFreeInit(constructId) {
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);

        freeRateProjectModel = ObjectUtils.copyProp(freeRateProjectModel, new FreeRateProjectModel());
        freeRateProjectModel.childFreeRate = ObjectUtils.convertObjectToMap(freeRateProjectModel.childFreeRate);
        for (let [key, value] of freeRateProjectModel.childFreeRate) {
            freeRateProjectModel.childFreeRate.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        }
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);

        freeRateUnitMap = ObjectUtils.convertObjectToMap(freeRateUnitMap);
        for (let [key, value] of freeRateUnitMap) {
            freeRateUnitMap.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        }
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, freeRateUnitMap);
    }

    /**
     * 导入项目后，费率统一应用
     * @param constructId
     * @param unitIdList
     * @returns {Promise<void>}
     */
    async afterImportUnifiedApplication(constructId, unitIdList) {
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateUnitModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isEmpty(unitIdList) || unitIdList.length < 1) {
            return;
        }
        for (let libraryCode of freeRateProjectModel.childFreeRate.keys()) {
            let projectFreeRate = freeRateProjectModel.childFreeRate.get(libraryCode);
            for (let unitId of unitIdList) {
                let freeRateUnitModels = ObjectUtils.getMapWithKeysStartingWith(freeRateUnitModel, unitId);
                for (const [key, unitfreeRate] of freeRateUnitModels) {
                    if (ObjectUtils.isNotEmpty(key) && libraryCode === unitfreeRate.libraryCode) {
                        unitfreeRate.projectType = projectFreeRate.projectType;
                        unitfreeRate.payTaxesAreas = projectFreeRate.payTaxesAreas;
                        unitfreeRate.manageFeeRate = projectFreeRate.manageFeeRate;
                        unitfreeRate.profitRate = projectFreeRate.profitRate;
                        unitfreeRate.taxRate = projectFreeRate.taxRate;
                        unitfreeRate.gfRate = projectFreeRate.gfRate;
                        unitfreeRate.anwenRate = projectFreeRate.anwenRate;
                        this.service.PreliminaryEstimate.gsUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, unitfreeRate);
                    }
                }
            }
        }
    }

    /**
     * 更单位新费率表
     * @param params
     * @returns {Promise<void>}
     */
    async refreshUnitFreeRate(params) {
        let {constructId, unitId} = params
        let deList = await ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
        let costFileCodeAll = [...new Set(deList.map(item => item.costFileCode))];
        let unitQfbMap = ProjectDomain.getDomain(this.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        for (let costFileCode of costFileCodeAll) {
            let args = {
                libraryCode: costFileCode,
                projectType: FreeRateModel.DEFAULT_PROJECT_TYPE,
                payTaxesAreas: FreeRateModel.DEFAULT_PAY_TAXES_AREAS
            }
            let gsBaseFreeRate = await this.service.PreliminaryEstimate.gsBaseFreeRateService.searchFreeRateInfo(args);
            let freeKey = WildcardMap.generateKey(unitId, costFileCode);
            unitQfbMap.set(freeKey, gsBaseFreeRate)
        }
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap);
    }


    /**
     * 导入项目后费率恢复
     * @param freeRateProjectModel
     */
    transProjectQfb(freeRateProjectModel) {
        freeRateProjectModel = ObjectUtils.copyProp(freeRateProjectModel, new FreeRateProjectModel());
        freeRateProjectModel.childFreeRate = ObjectUtils.convertObjectToMap(freeRateProjectModel.childFreeRate);
        for (let [key, value] of freeRateProjectModel.childFreeRate) {
            freeRateProjectModel.childFreeRate.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        }
        return freeRateProjectModel;
    }

    /**
     * 导入项目后单位工程费率恢复
     * @param freeRateUnitMap
     */
    transUnitQfb(freeRateUnitMap) {
        freeRateUnitMap = ObjectUtils.convertObjectToMap(freeRateUnitMap);
        for (let [key, value] of freeRateUnitMap) {
            freeRateUnitMap.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        }
        return freeRateUnitMap;
    }


}

GsFreeRateService.toString = () => '[class GljFreeRateService]';
module.exports = GsFreeRateService;
