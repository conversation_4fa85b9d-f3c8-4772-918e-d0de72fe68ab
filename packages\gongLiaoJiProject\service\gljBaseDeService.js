const {Service} = require('../../../core');
const {BaseDe2022} = require("../models/BaseDe2022");
const {GljDe, IS_EXIST_DE} = require("../models/GljDe");
const {ObjectUtils} = require('../utils/ObjectUtils');
const {GljDeRelation} = require("../models/GljDeRelation");
const {SqlUtils} = require("../../../electron/utils/SqlUtils");
const {GljRcj} = require("../models/GljRcj");
const {GljDeRcjRelation} = require("../models/GljDeRcjRelation");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {ObjectUtil} = require("../../../common/ObjectUtil");

/**
 * 定额册 service
 * @class
 */
class GljBaseDeService extends Service{

    constructor(ctx) {
        super(ctx);
        this.baseDe2022Dao = this.app.db.gongLiaoJiProject.manager.getRepository(BaseDe2022);
    }

    /**
     * 根据定额库编码 查询定额
     * @param libraryCode
     * @returns {Promise<GsBaseDe[]|Error>}
     */
    async listDeByLibraryCode(libraryCode) {
        if (null == libraryCode) {
            throw new Error("必传参数定额标准为空");
        }

        return await this.baseDe2022Dao.find({
            where: {libraryCode: libraryCode},
            order: {sortNo: "ASC"}
        });
    }

    /**
     * 获取定额分类目录树
     * @param libraryCode 清单册code
     * @returns {Promise<*[]>}
     */
    async listTreeByLibraryCode(libraryCode) {
        let selectSql = "SELECT\n" +
            "\tsequence_nbr as sequenceNbr,\n" +
            "\tlibrary_code as libraryCode,\n" +
            "\tlibrary_name as libraryName,\n" +
            "\tde_code as deCode,\n" +
            "\tde_name as deName,\n" +
            "\tde_code as deCode,\n" +
            "\tclassify_level1 as classlevel01,\n" +
            "\tclassify_level2 as classlevel02,\n" +
            "\tclassify_level3 as classlevel03,\n" +
            "\tclassify_level4 as classlevel04,\n" +
            "\tclassify_level5 as classlevel05,\n" +
            "\tclassify_level6 as classlevel06,\n" +
            "\tclassify_level7 as classlevel07\n" +
            "FROM\n" +
            "\t\"base_de_2022\" " +
            "WHERE\n" +
            "\tlibrary_code = ? \n" +
            "ORDER BY\n" +
            "\tsort_no ASC";
        let deArrays = this.app.gljSqlite3DataSource.prepare(selectSql).all(libraryCode);
        // 去重取 classify_level1/classify_level2/classify_level3/classify_level4的list
        let level1AllList = this.distinctList(deArrays, "classlevel01");
        let level2AllList = this.distinctList(deArrays, "classlevel01","classlevel02");
        let level3AllList = this.distinctList(deArrays, "classlevel01","classlevel02","classlevel03");
        let level4AllList = this.distinctList(deArrays, "classlevel01","classlevel02","classlevel03","classlevel04");
        let level5AllList = this.distinctList(deArrays, "classlevel01","classlevel02","classlevel03","classlevel04","classlevel05");
        let level6AllList = this.distinctList(deArrays, "classlevel01","classlevel02","classlevel03","classlevel04","classlevel05","classlevel06");
        let level7AllList = this.distinctList(deArrays, "classlevel01","classlevel02","classlevel03","classlevel04","classlevel05","classlevel06","classlevel07");

        let sort = (ll1, ll2)=>ll1.sortNo - ll2.sortNo;
        level1AllList = level1AllList.sort(sort);
        // 给前端作为唯一标识
        let sequenceNbr = 1;
        // level1
        level1AllList.forEach(level1Item => {
            // 给前端统一code和name以及唯一标识
            this.modifyingAttributeValue(level1Item, 1, sequenceNbr++);
            let leve2SubList = level2AllList.filter(level2AllItem => level2AllItem.classlevel01 === level1Item.classlevel01);
            level1Item.childrenList = leve2SubList;
            // level2
            leve2SubList.forEach(level2Item => {
                // 给前端统一code和name以及唯一标识
                this.modifyingAttributeValue(level2Item, 2, sequenceNbr++);
                let leve3SubList = level3AllList.filter(level3AllItem => (level3AllItem.classlevel02 === level2Item.classlevel02 && level3AllItem.classlevel01 === level2Item.classlevel01));
                level2Item.childrenList = leve3SubList;
                // level3
                leve3SubList.forEach(level3Item => {
                    // 给前端统一code和name以及唯一标识
                    this.modifyingAttributeValue(level3Item, 3, sequenceNbr++);
                    // level4
                    let level4SubList = level4AllList.filter(level4AllItem => (level4AllItem.classlevel03 === level3Item.classlevel03 && level4AllItem.classlevel02 === level3Item.classlevel02 && level4AllItem.classlevel01 === level3Item.classlevel01));
                    level3Item.childrenList = level4SubList;
                    level4SubList.forEach(level4Item => {
                        // 给前端统一code和name以及唯一标识
                        this.modifyingAttributeValue(level4Item, 4, sequenceNbr++);
                        // level5
                        let level5SubList = level5AllList.filter(level5AllItem => (level5AllItem.classlevel04 === level4Item.classlevel04 && level5AllItem.classlevel03 === level4Item.classlevel03 && level5AllItem.classlevel02 === level4Item.classlevel02 && level5AllItem.classlevel01 === level4Item.classlevel01));
                        level4Item.childrenList = level5SubList;
                        level5SubList.forEach(level5Item => {
                            // 给前端统一code和name以及唯一标识
                            this.modifyingAttributeValue(level5Item, 5, sequenceNbr++);
                            // level6
                            let level6SubList = level6AllList.filter(level6AllItem => (level6AllItem.classlevel05 === level5Item.classlevel05 && level6AllItem.classlevel04 === level5Item.classlevel04 && level6AllItem.classlevel03 === level5Item.classlevel03 && level6AllItem.classlevel02 === level5Item.classlevel02 && level6AllItem.classlevel01 === level5Item.classlevel01));
                            level5Item.childrenList = level6SubList;
                            level6SubList.forEach(level6Item => {
                                // 给前端统一code和name以及唯一标识
                                this.modifyingAttributeValue(level6Item, 6, sequenceNbr++);
                                // level7
                                let level7SubList = level7AllList.filter(level7AllItem => (level7AllItem.classlevel06 === level6Item.classlevel06 && level7AllItem.classlevel05 === level6Item.classlevel05 && level7AllItem.classlevel04 === level6Item.classlevel04 && level7AllItem.classlevel03 === level6Item.classlevel03 && level7AllItem.classlevel02 === level6Item.classlevel02 && level7AllItem.classlevel01 === level6Item.classlevel01));
                                level6Item.childrenList = level7SubList;
                                level7SubList.forEach(level7Item => {
                                    // 给前端统一code和name以及唯一标识
                                    this.modifyingAttributeValue(level7Item, 7, sequenceNbr++);
                                })
                            })
                        })
                    })
                });
            });
        });

        // 根据libraryCode查定额册名字
        let baseDeLibraryModel = await this.service.gongLiaoJiProject.gljBaseDeLibraryService.getByLibraryCode(libraryCode);
        let topLibraryNode = {sequenceNbr: sequenceNbr++, name: baseDeLibraryModel.libraryName};
        topLibraryNode.childrenList = level1AllList;
        return topLibraryNode;
    }

    /**
     * 根据条件查询定额
     * @param args
     * @returns {Promise<*>}
     */
    async queryListByParam(args) {
        let {libraryCode, name} = args
        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 清单册
        if (libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += "baseList.libraryCode = :libraryCode"
            whereParams.libraryCode = libraryCode;
        }
        // 人材机名称
        if (name) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "( baseList.de_name like :name or " +
                "baseList.de_code like :code )"
            whereParams.name = "%" + name + "%";
            whereParams.code = name + "%";
        }

        if (!whereSql) {
            console.log("error,参数为空");
        }

        let baseLists = await this.baseDe2022Dao
            .createQueryBuilder("baseList")
            .where(whereSql, whereParams)
            .orderBy("baseList.de_name", "ASC")
            .getMany();

        return baseLists;
    }

    /**
     * 查询某条定额
     * @param params
     * @returns {Promise<ResponseData>}
     */
    async queryDeById(params) {
        let {standardId, libraryCode, deCode} = params;
        let querySql = "select a.price_base_journal_tax as price,\n" +
            "a.price_base_journal as baseJournalPrice,\n" +
            "a.price_base_journal_tax as baseJournalTaxPrice,\n" +
            "a.price_market_tax as marketTaxPrice,\n" +
            "a.price_market as marketPrice,\n" +
            "a.classify_level1 as classlevel01,\n" +
            "a.classify_level2 as classlevel02,\n" +
            "a.classify_level3 as classlevel03,\n" +
            "a.classify_level4 as classlevel04,\n" +
            "a.classify_level5 as classlevel05,\n" +
            "a.classify_level6 as classlevel06,\n" +
            "a.classify_level7 as classlevel07,\n" +
            "a.* from base_de_2022 a where sequence_nbr = ?";
        let sqlRes = this.app.gljSqlite3DataSource.prepare(querySql).all(standardId);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        if(convertRes.length !== 0) {
            let gljDe = ObjectUtils.copyProp(convertRes[0], new GljDe());
            gljDe.standardDeId = gljDe.sequenceNbr;
            return gljDe;
        }
        // 二级定额
        if(convertRes.length === 0) {
            querySql = "select a.price_base_journal_tax as price,\n" +
                "a.price_base_journal as baseJournalPrice,\n" +
                "a.price_base_journal_tax as baseJournalTaxPrice,\n" +
                "a.price_market_tax as marketTaxPrice,\n" +
                "a.price_market as marketPrice,\n" +
                "a.classify_level1 as classlevel01,\n" +
                "a.classify_level2 as classlevel02,\n" +
                "a.classify_level3 as classlevel03,\n" +
                "a.classify_level4 as classlevel04,\n" +
                "a.classify_level5 as classlevel05,\n" +
                "a.classify_level6 as classlevel06,\n" +
                "a.classify_level7 as classlevel07,\n" +
                "a.* from base_de_2022 a where de_code = ? and library_code = ?";
            sqlRes = this.app.gljSqlite3DataSource.prepare(querySql).all(deCode, libraryCode);
            convertRes = SqlUtils.convertToModel(sqlRes);
            if(convertRes.length !== 0) {
                let gljDe = ObjectUtils.copyProp(convertRes[0], new GljDe());
                gljDe.standardDeId = gljDe.sequenceNbr;
                return gljDe;
            }
        }
        return []
    }

    /**
     * 根据人材机 查询所有定额
     * @param params
     * @returns {Promise<ResponseData>}
     */
    async queryBaseDeByRcj(params) {
        let { libraryCode, baseRcjId, page = 1, pageSize = 10 } = params;
        // 计算跳过的条数
        const skip = (page - 1) * pageSize;
        // 以 base_de_2022 表为主表
        const queryBuilder = this.baseDe2022Dao.createQueryBuilder('baseDe')
            .innerJoin('base_de_rcj_relation_2022', 'relation',
                'relation.quota_id = baseDe.sequenceNbr AND relation.library_code = baseDe.library_code')
            .where('relation.library_code = :libraryCode', { libraryCode })
            .andWhere('relation.rcj_id = :baseRcjId', { baseRcjId })
            .skip(skip)
            .take(pageSize)
            .orderBy('baseDe.sortNo', 'ASC');
        // 查询数据和总数
        const [data, total] = await queryBuilder.getManyAndCount();
        return {
            data,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    }




    /**
     * 根据定额查询人材机
     * @param standardDeId
     * @returns {Promise<*>}
     */
    async getDeAndRcj(standardDeId){
        if (ObjectUtils.isEmpty(standardDeId)) {
            throw new Error("基础定额standardDeId为空.");
        }

        let baseDe = await this.baseDe2022Dao.findOne({
            where: {sequenceNbr: standardDeId}
        });
        if (ObjectUtils.isEmpty(baseDe)) {
            return null;
        }
        // 复制属性
        let gljDe = ObjectUtils.copyProp(baseDe, new GljDe());
        gljDe.isSubDe = 0;
        gljDe.standardDeId = standardDeId;
        //获取人材机调差
        // let gljDeCompensation = await this.service.gongLiaoJiProject.gljBaseDeCompensationService.getBySequenceNbr(gljDe.sequenceNbr);
        // if (ObjectUtils.isNotEmpty(gljDeCompensation)) {
        //     gljDe.compensation = gljDeCompensation;
        // }
        // 定额人材机关系
        let BaseDeRcjRelationList =  this.getDeRcjRelationNoSub(standardDeId);
        let deRcjRelationList = [];
        BaseDeRcjRelationList.forEach(item=>{deRcjRelationList.push(ObjectUtils.copyProp(item, new GljDeRcjRelation()))});
        gljDe.deRcjRelationList = deRcjRelationList;

        // 人材机
        let baseRcjList =  this.getRcjNoSub(standardDeId);
        let rcjList = [];
        baseRcjList.forEach(item=>{rcjList.push(ObjectUtils.copyProp(item, new GljRcj()))});
        gljDe.rcjList = rcjList;
        // 根据定额id查询人材机
        return gljDe;
    }

    async standardDeFirst(deCode, libraryCode){
        let baseDes = await this.baseDe2022Dao.find({
            where: {deCode: deCode},
            order: {sortNo: "ASC"}
        });
        //存在多个相同编码，，，A001 举例建筑工程
        let baseDe = baseDes.filter(item=>item.libraryCode === libraryCode);
        return baseDe;
    }
    /**
     * 根据定额code查询定额
     * @param constructId
     * @param unitId
     * @param deCode
     * @returns {Promise<*>}
     */
    async getDeAndRcjByDeCode(constructId, unitId, deCode, mainDeLibrary=false) {
        if (ObjectUtils.isEmpty(deCode)) {
            throw new Error("基础定额deCode为空.");
        }
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId)
        let libraryCode = unitProject.constructMajorType;
        //用主定额册查询
        if(mainDeLibrary && unitProject.mainDeLibrary){
            libraryCode = unitProject.mainDeLibrary
        }
        //优先标准定额处理
        let baseDes = await this.standardDeFirst(deCode,libraryCode);
        if (ObjectUtils.isNotEmpty(baseDes)) {
            let gljDes = [];
            for(let baseDe of baseDes){
                // 复制属性
                let gljDe = ObjectUtils.copyProp(baseDe, new GljDe());
                gljDe.isSubDe = 0;
                let standardDeId = gljDe.sequenceNbr;
                // 定额人材机关系
                let BaseDeRcjRelationList = this.getDeRcjRelationNoSub(standardDeId);
                let deRcjRelationList = [];
                BaseDeRcjRelationList.forEach(item=>{deRcjRelationList.push(ObjectUtils.copyProp(item, new GljDeRcjRelation()))});
                gljDe.deRcjRelationList = deRcjRelationList;
                // 人材机
                let baseRcjList = this.getRcjNoSub(standardDeId);
                let rcjList = [];
                baseRcjList.forEach(item=>{rcjList.push(ObjectUtils.copyProp(item, new GljRcj()))});
                gljDe.rcjList = rcjList;
                // 根据定额id查询人材机
                gljDes.push(gljDe);
            }
            return gljDes;
        }

    }

    /**
     * 查询二级定额
     * @param standardDeId
     * @returns {Promise<void>}
     */
    async getDeAndRcjLevel2(standardDeId){
        let baseDeRelation = await this.service.gongLiaoJiProject.gljBaseDeRelationService.getBySequenceNbr(standardDeId);
        //如果是 二级定额
        if (ObjectUtils.isNotEmpty(baseDeRelation)) {
            // 人材机
            let baseRcjList = this.getRcj(baseDeRelation.sequenceNbr);
            let rcjList = [];
            baseRcjList.forEach(item=>{rcjList.push(ObjectUtils.copyProp(item, new GljRcj()))});
            baseDeRelation.rcjList = rcjList;
            baseDeRelation.isSubDe = 1;
        }
        return baseDeRelation;
    }

    /**
     * 获取人材机（有二级定额）
     * @param sequenceNbr
     * @returns {[]|*}
     */
    getRcj(sequenceNbr) {
        let sql = "select b.qts as res_qty,b.qts as init_res_qty,c.* from gs_base_de_relation a\n" +
            "inner join gs_base_de_rcj_relation b\n" +
            "  on a.sequence_nbr = b.quota_id_level2\n" +
            "inner join gs_base_rcj c\n" +
            "  on b.rcj_id = c.sequence_nbr\n" +
            "where a.sequence_nbr = ?";
        let sqlres = this.app.gljSqlite3DataSource.prepare(sql).all(sequenceNbr);
        let convertRes = SqlUtils.convertToModel(sqlres);
        return convertRes;
    }

    /**
     * 获取人材机（有二级定额）
     * @param deId
     * @returns {[]|*}
     */
    getRcjByDeId(deId) {
        let sql = "select a.sequence_nbr as de_rel_id,b.qts as res_qty,b.qts as init_res_qty,c.* from gs_base_de_relation a\n" +
            "inner join gs_base_de_rcj_relation b\n" +
            "  on a.sequence_nbr = b.quota_id_level2\n" +
            "inner join gs_base_rcj c\n" +
            "  on b.rcj_id = c.sequence_nbr\n" +
            "where a.de_id = ?";
        let sqlres = this.app.gljSqlite3DataSource.prepare(sql).all(deId);
        let convertRes = SqlUtils.convertToModel(sqlres);
        return convertRes;
    }

    /**
     * 获取人材机（无二级定额）
     * @param sequenceNbr
     * @returns {[]|*}
     */
    getRcjNoSub(deId) {
        let sql = "select b.res_qty as res_qty,b.res_qty as init_res_qty," +
            "c.price_base_journal_tax as price,\n" +
            "c.price_base_journal as baseJournalPrice,\n" +
            "c.price_base_journal_tax as baseJournalTaxPrice,\n" +
            "c.price_market_tax as marketTaxPrice,\n" +
            "c.price_market as marketPrice," +
            "c.* from base_de_2022 a\n" +
            "inner join base_de_rcj_relation_2022 b\n" +
            "  on a.sequence_nbr = b.quota_id\n" +
            "inner join base_rcj_2022 c\n" +
            "  on b.rcj_id = c.sequence_nbr\n" +
            "where a.sequence_nbr = ?";
        let sqlres = this.app.gljSqlite3DataSource.prepare(sql).all(deId);
        let convertRes = SqlUtils.convertToModel(sqlres);
        return convertRes;
    }

    /**
     * 获取定额人材机（有二级定额）
     * @param sequenceNbr
     * @returns {[]|*}
     */
    getDeRcjRelation(sequenceNbr) {
        let sql = "select b.qts as res_qty,b.qts as init_res_qty,b.* from gs_base_de_relation a\n" +
            "inner join gs_base_de_rcj_relation b\n" +
            "  on a.sequence_nbr = b.quota_id_level2\n" +
            "where a.sequence_nbr = ?";
        let sqlres = this.app.gljSqlite3DataSource.prepare(sql).all(sequenceNbr);
        let convertRes = SqlUtils.convertToModel(sqlres);
        return convertRes;
    }

    /**
     * 获取定额人材机（有二级定额）
     * @param deId
     * @returns {[]|*}
     */
    getDeRcjRelationByDeId(deId) {
        let sql = "select b.qts as res_qty,b.qts as init_res_qty,b.* from gs_base_de_relation a\n" +
            "inner join gs_base_de_rcj_relation b\n" +
            "  on a.sequence_nbr = b.quota_id_level2\n" +
            "where a.de_id = ?";
        let sqlres = this.app.gljSqlite3DataSource.prepare(sql).all(deId);
        let convertRes = SqlUtils.convertToModel(sqlres);
        return convertRes;
    }

    /**
     * 获取定额人材机（无二级定额）
     * @param sequenceNbr
     * @returns {[]|*}
     */
    getDeRcjRelationNoSub(deId) {
        let sql = "select b.res_qty,b.res_qty as init_res_qty,b.* from base_de_2022 a\n" +
            "inner join base_de_rcj_relation_2022 b\n" +
            "on a.sequence_nbr = b.quota_id\n" +
            "where a.sequence_nbr = ?";
        let sqlres = this.app.gljSqlite3DataSource.prepare(sql).all(deId);
        let convertRes = SqlUtils.convertToModel(sqlres);
        return convertRes;
    }

    /**
     * 模糊查定额
     * @param qdDeParam 定额编码、名称、分类名
     * @see QdDeParam
     * @returns {Promise<ObjectLiteral[]>}
     */
    async queryDeByBdCodeAndName(qdDeParam) {
        let baseDe = await this.queryDeById({standardId: qdDeParam.standardId});
        if (ObjectUtils.isNotEmpty(baseDe)) {
            qdDeParam.classlevel01 = baseDe.classlevel01
            qdDeParam.classlevel02 = baseDe.classlevel02
            qdDeParam.classlevel03 = baseDe.classlevel03
            qdDeParam.classlevel04 = baseDe.classlevel04
            qdDeParam.classlevel05 = baseDe.classlevel05
            qdDeParam.classlevel06 = baseDe.classlevel06
            qdDeParam.classlevel07 = baseDe.classlevel07
        }
        let userDes = [];
        if (ObjectUtils.isEmpty(qdDeParam.deCode) && qdDeParam.classlevel01 === "补充定额") {
            userDes = await this.baseDe2022Dao.find({
                where: {
                    libraryCode: qdDeParam.libraryCode,
                    classlevel01: "补充定额"
                },
                order: {sortNo: "ASC"}
            });
            let result = this.paginate(userDes, qdDeParam.page, qdDeParam.limit);
            return {"total":userDes.length,"data":result};
        }
        if (ObjectUtils.isEmpty(qdDeParam.deCode) && qdDeParam.classlevel02 === "补充定额") {
            userDes = await this.baseDe2022Dao.find({
                where: {
                    libraryCode: qdDeParam.libraryCode,
                    classlevel02: "补充定额"
                },
                order: {sortNo: "ASC"}
            });
            let result = this.paginate(userDes, qdDeParam.page, qdDeParam.limit);
            return {"total":userDes.length,"data":result};
        }

        // 选中定额册下的数据情况下，直接返回
        if (((qdDeParam.classlevel01 && qdDeParam.classlevel01 !== "" ) ||
            (qdDeParam.classlevel02 && qdDeParam.classlevel02 !== "") ||
            (qdDeParam.classlevel03 && qdDeParam.classlevel03 !== "") ||
            (qdDeParam.classlevel04 && qdDeParam.classlevel04 !== "") ||
            (qdDeParam.classlevel05 && qdDeParam.classlevel05 !== "") ||
            (qdDeParam.classlevel06 && qdDeParam.classlevel06 !== "") ||
            (qdDeParam.classlevel07 && qdDeParam.classlevel07 !== "") ||
            (qdDeParam.libraryCode && qdDeParam.libraryCode !== "")) && (!qdDeParam.bdName || qdDeParam.bdName === "") ){
            let data = await this.selectDesWhenSelect(qdDeParam);
            let page = qdDeParam.page
            if (ObjectUtils.isNotEmpty(qdDeParam.deName) && ObjectUtils.isNotEmpty(qdDeParam.deCode)) {
                let index = data.findIndex(obj => obj.deName === qdDeParam.deName && obj.deCode === qdDeParam.deCode) + 1;
                page = Math.ceil(index/qdDeParam.limit);
            }
            let length = data.length;
            const startIndex = parseInt((page-1)*qdDeParam.limit);
            const endIndex = startIndex + qdDeParam.limit;
            data = data.slice( startIndex,endIndex);
            return {"total":length,"data":data,"page":page};
        }

        let selectSqlCount = "select COUNT(1) as total from ( select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa," +
            "d.price_base_journal_tax as price,\n" +
            "d.price_base_journal as baseJournalPrice,\n" +
            "d.price_base_journal_tax as baseJournalTaxPrice,\n" +
            "d.price_market_tax as marketTaxPrice,\n" +
            "d.price_market as marketPrice,\n" +
            "d.classify_level1 as classlevel01,\n" +
            "d.classify_level2 as classlevel02,\n" +
            "d.classify_level3 as classlevel03,\n" +
            "d.classify_level4 as classlevel04,\n" +
            "d.classify_level5 as classlevel05,\n" +
            "d.classify_level6 as classlevel06,\n" +
            "d.classify_level7 as classlevel07,\n" +
            " d.*\n" +
            "      from base_de_2022 d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%') and library_code= ?\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc )";

        let selectSql = "select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa," +
            "d.price_base_journal_tax as price,\n" +
            "d.price_base_journal as baseJournalPrice,\n" +
            "d.price_base_journal_tax as baseJournalTaxPrice,\n" +
            "d.price_market_tax as marketTaxPrice,\n" +
            "d.price_market as marketPrice,\n" +
            "d.classify_level1 as classlevel01,\n" +
            "d.classify_level2 as classlevel02,\n" +
            "d.classify_level3 as classlevel03,\n" +
            "d.classify_level4 as classlevel04,\n" +
            "d.classify_level5 as classlevel05,\n" +
            "d.classify_level6 as classlevel06,\n" +
            "d.classify_level7 as classlevel07,\n" +
            " d.*\n" +
            "      from base_de_2022 d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%') and library_code= ?\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc LIMIT ? ,?";
        //查看编码的like里 是否有主定额册的  现在是只要没有精准  就返回两个字段like的 数据  而是否主定额册则是乱的

        let sqlResCount = this.app.gljSqlite3DataSource.prepare(selectSqlCount).all(qdDeParam.bdName,qdDeParam.bdName,qdDeParam.libraryCode,
            qdDeParam.bdName,qdDeParam.bdName,qdDeParam.libraryCode);
        let sqlRes = this.app.gljSqlite3DataSource.prepare(selectSql).all(qdDeParam.bdName,qdDeParam.bdName,qdDeParam.libraryCode,
            qdDeParam.bdName,qdDeParam.bdName,qdDeParam.libraryCode,
            parseInt((qdDeParam.page-1)*qdDeParam.limit),
            qdDeParam.limit);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        return {"total":sqlResCount[0].total,"data":convertRes};
    }

    /**
     * 分页查询函数
     * @param {Array} list 待分页的数据列表
     * @param {Number} page 当前页码,从1开始
     * @param {Number} limit 每页显示条数
     * @returns {Array} 当前页的数据
     */
    paginate(list, page, limit) {
        // 处理页码小于1的情况
        page = Math.max(1, page);
        // 计算起始索引
        const startIndex = (page - 1) * limit;
        // 计算结束索引
        const endIndex = startIndex + limit;
        // 返回当前页数据
        return list.slice(startIndex, endIndex);
    }

    /**
     * list去重
     * @param list 要分组的list
     * @param distinctColumn 分组字段名str
     * @returns {*[]} 新的list
     */
    distinctList(list, ...distinctColumn) {
        if (!Array.isArray(list)) {
            return [];
        }
        let groupArray = [];
        list.forEach(item => {
            // 是否为空
            for (let i = 0 ; i < distinctColumn.length ; ++i) {
                if (ObjectUtils.isEmpty(item[distinctColumn[i]])) {
                    return;
                }
            }
            // 是否存在
            let isExist = groupArray.some(g => {
                let exist = true;
                for (let i = 0 ; i < distinctColumn.length ; ++i) {
                    if (g[distinctColumn[i]] !== item[distinctColumn[i]]) {
                        exist = false;
                        break;
                    }
                }

                return exist;
            });
            // 不存在add
            if (!isExist) {
                groupArray.push(Object.assign({}, item));
            }
        })
        return groupArray;
    }

    /**
     * 修改属性值
     * @param baseDe 定额
     * @param level 清单专业level
     * @param sequenceNbr 重置后的sequenceNbr
     */
    modifyingAttributeValue(baseDe, level, sequenceNbr) {
        /*
         * 1.赋值name
         * 2.置空level下有误的属性值
         * 3.重置sequenceNbr
         */
        baseDe.sequenceNbr = sequenceNbr;
        switch (level) {
            case 1:
                // level1
                baseDe.name = baseDe.classlevel01;
                baseDe.classlevel02 = null;
                baseDe.classlevel03 = null;
                baseDe.classlevel04 = null;
                baseDe.classlevel05 = null;
                baseDe.classlevel06 = null;
                baseDe.classlevel07 = null;
                break;
            case 2:
                // level2
                baseDe.name = baseDe.classlevel02;
                baseDe.classlevel03 = null;
                baseDe.classlevel04 = null;
                baseDe.classlevel05 = null;
                baseDe.classlevel06 = null;
                baseDe.classlevel07 = null;
                break;
            case 3:
                // level3
                baseDe.name = baseDe.classlevel03;
                baseDe.classlevel04 = null;
                baseDe.classlevel05 = null;
                baseDe.classlevel06 = null;
                baseDe.classlevel07 = null;
                break;
            case 4:
                // level4
                baseDe.name = baseDe.classlevel04;
                baseDe.classlevel05 = null;
                baseDe.classlevel06 = null;
                baseDe.classlevel07 = null;
                break;
            case 5:
                // level5
                baseDe.name = baseDe.classlevel05;
                baseDe.classlevel06 = null;
                baseDe.classlevel07 = null;
                break;
            case 6:
                // level6
                baseDe.name = baseDe.classlevel06;
                baseDe.classlevel07 = null;
                break;
            case 7:
                // level7
                baseDe.name = baseDe.classlevel07;
                break;
            default:
                // ...
                break;
        }
    }

    async selectDesWhenSelect(qdDeParam) {
        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 清单册
        if (qdDeParam.libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += "baseDe.libraryCode = :libraryCode"
            whereParams.libraryCode = qdDeParam.libraryCode;
        }
        // 清单名称
        if (qdDeParam.bdName) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "(baseDe.de_code like :bdName OR baseDe.de_name like :bdName)"
            whereParams.bdName = "%" + qdDeParam.bdName + "%";
        }
        // 一级分类
        if (qdDeParam.classlevel01) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classlevel01 = :classlevel01"
            whereParams.classlevel01 = qdDeParam.classlevel01;
        }
        // 二级分类
        if (qdDeParam.classlevel02) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classlevel02 = :classlevel02"
            whereParams.classlevel02 = qdDeParam.classlevel02;
        }
        // 三级分类
        if (qdDeParam.classlevel03) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classlevel03 = :classlevel03"
            whereParams.classlevel03 = qdDeParam.classlevel03;
        }
        // 四级分类
        if (qdDeParam.classlevel04) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classlevel04 = :classlevel04"
            whereParams.classlevel04 = qdDeParam.classlevel04;
        }
        // 五级分类
        if (qdDeParam.classlevel05) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classlevel05 = :classlevel05"
            whereParams.classlevel05 = qdDeParam.classlevel05;
        }
        // 六级分类
        if (qdDeParam.classlevel06) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classlevel06 = :classlevel06"
            whereParams.classlevel06 = qdDeParam.classlevel06;
        }
        // 七级分类
        if (qdDeParam.classlevel07) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe.classlevel07 = :classlevel07"
            whereParams.classlevel07 = qdDeParam.classlevel07;
        }
        if (!whereSql) {
            console.log("error,参数为空");
        }

        let baseDes = await this.baseDe2022Dao
            .createQueryBuilder("baseDe")
            .where(whereSql, whereParams)
            .orderBy("baseDe.sortNo", "ASC")
            .getMany();
        return baseDes;
    }

}

GljBaseDeService.toString = () => '[class GljBaseDeService]';
module.exports = GljBaseDeService;
