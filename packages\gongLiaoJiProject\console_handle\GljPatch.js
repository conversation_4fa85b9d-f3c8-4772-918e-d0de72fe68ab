
class GljPatch {

    /**
     * 补丁
     * @param obj
     * @returns {Promise<void>}
     */
    static async processPatch(obj){
        let resultObj = obj;
        // 20250905 2025-GJXSGC-DEG 替换为 2024-GJXSGC-DEG
        resultObj = await this.processPatch20250905(obj);

        return resultObj;
    }

    /**
     * 2025-GJXSGC-DEG 替换为 2024-GJXSGC-DEG
     * @param obj
     * @returns {Promise<*>}
     */
    static async processPatch20250905(obj){
        let data = JSON.stringify(obj);
        const regex1 = new RegExp('2025-GJXSGC-DEG', 'g');
        data = data.replace(regex1, '2024-GJXSGC-DEG');
        const regex2 = new RegExp('2025-FGJZ-DEG', 'g');
        data = data.replace(regex2, '2024-FGJZ-DEG');
        const regex3 = new RegExp('2025-SZSS-DEX', 'g');
        data = data.replace(regex3, '2024-SZSS-DEX');
        obj = JSON.parse(data)
        return obj;
    }

}

module.exports = GljPatch;