'use strict';

const {SqlUtils} = require("../../../electron/utils/SqlUtils");
const {Service} = require("../../../core");
const { BaseFeeFile2022 } = require('../models/BaseFeeFile2022');
const { BaseFeeFileRelation2022 } = require('../models/BaseFeeFileRelation2022');
const { BaseFeeFileProject2022 } = require('../models/BaseFeeFileProject');
const { orderBy } = require("lodash");
const CommonConstants = require("../../gongLiaoJiProject/constants/CommonConstants");

/**
 * 示例服务
 * @class
 */
class BaseFeeFileService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 查询单个
   * @param qfCode
   * @returns {Promise<BaseFeeFile2022>}
   */
  async getBaseFeeFile(qfCode) {
    return await this.app.db.gongLiaoJiProject.manager.getRepository(BaseFeeFile2022).findOne({
      where: {
        qfCode
      }
    });
  }


   /**
    * 根据qfCode查询取费文件关系
    * @returns {Promise<[]|Error>}
    * @param qfCode
    */
    async getFeeFileRelationByQfCode(qfCode) {
     return await this.app.db.gongLiaoJiProject.manager.getRepository(BaseFeeFileRelation2022).findOne({
        where: {
          qfCode
        }
      });
    }

  /**
   * 查询所有
   * @returns {Promise<BaseFeeFile2022[]>}
   */
  async getAllBaseFeeFile() {
    return await this.app.db.gongLiaoJiProject.manager.getRepository(BaseFeeFile2022).find({
      order: {sortNo: 'ASC'}
    });
  }

  /**
   * 查询单个
   * @param qfCode
   * @returns {Promise<BaseFeeFileProject2022>}
   */
  async getBaseFeeFileProject(qfCode) {
    return await this.app.db.gongLiaoJiProject.manager.getRepository(BaseFeeFileProject2022).findOne({
      where: {
        qfCode
      }
    });
  }

  /**
   * 查询所有
   * @returns {Promise<BaseFeeFileProject2022[]>}
   */
  async queryBaseFeeFileData() {
    let baseFeeFileList = await this.app.db.gongLiaoJiProject.manager.getRepository(BaseFeeFile2022).find({
      order:{sortNo:'ASC'}
    });
    baseFeeFileList = baseFeeFileList.filter(item => item.qfCode !== CommonConstants.SYSTEM_SZGC)
    return baseFeeFileList;
  }

  /**
   * 查询所有
   * @returns {Promise<BaseFeeFileProject2022[]>}
   */
  async getAllBaseFeeFileProject() {
    return await this.app.db.gongLiaoJiProject.manager.getRepository(BaseFeeFileProject2022).find({});
  }


  /**
   * 查询所有
   * @returns {Promise<BaseFeeFileProject2022[]>}
   */
  async getBaseFeeFileProjectByQfCode(args) {
    let {qfMajorTypes} = args;
    // 生成占位符
    const placeholders = qfMajorTypes.map(() => '?').join(', ');
    // 构建查询语句
    const querySql = `SELECT * FROM base_fee_file_project_2022 WHERE qf_code IN (${placeholders})`;
    const baseFeeFileListProjects = this.app.db.gljSqlite3DataSource.prepare(querySql).all(qfMajorTypes);
    return SqlUtils.convertToModel(baseFeeFileListProjects);
  }
}

BaseFeeFileService.toString = () => '[class BaseFeeFileService]';
module.exports = BaseFeeFileService;
