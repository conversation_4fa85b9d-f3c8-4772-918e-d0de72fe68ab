class FunctionTypeConstants {

    /**
     * 单位工程 - 费用汇总 key
     * @type {string}
     */
    static UNIT_COST_SUMMARY = "UNIT_COST_SUMMARY";

    /**
     * 单位工程 - 费用代码 key
     * @type {string}
     */
    static UNIT_COST_CODE = "UNIT_COST_CODE";

    /**
     * 单位工程 - 费用汇总合计 key
     * @type {string}
     */
    static UNIT_COST_SUMMARY_TOTAL = "UNIT_COST_SUMMARY_TOTAL";

    /**
     * 工程项目 - 取费表
     * @type {string}
     */
    static PROJECT_QFB = "PROJECT_QFB";

    /**
     * 单项 - 取费表
     * @type {string}
     */
    static SINGLE_QFB = "SINGLE_QFB";

    /**
     * 单位工程 - 取费表
     * @type {string}
     */
    static UNIT_QFB = "UNIT_QFB";


    /**
     * 工程项目 - 费率说明
     * @type {string}
     */
    static PROJECT_FLSM = "PROJECT_FLSM";

    /**
     * 单项 - 费率说明
     * @type {string}
     */
    static SINGLE_FLSM = "SINGLE_FLSM";

    /**
     * 单位工程 - 费率说明
     * @type {string}
     */
    static UNIT_FLSM = "UNIT_FLSM";

    /**
     * 单位工程 - 工程量明细 Quantities
     * @type {string}
     */
     static UNIT_QUANTITIES = "UNIT_QUANTITIES";


    /**
     * 单位工程 - 说明信息 Content
     * @type {string}
     */
     static UNIT_DE_CONTENT = "UNIT_DE_CONTENT";

    /**
     * 单位工程 - 标准换算 CONVERSION
     * @type {string}
     */
     static UNIT_CONVERSION = "UNIT_CONVERSION";

    /**
     * 工程项目 - 费用查看
     * @type {string}
     */
    static PROJECT_FYCK = "PROJECT_FYCK";

    /**
     * 工程项目 - 费用查看
     * @type {string}
     */
    static SINGLE_FYCK = "SINGLE_FYCK";

    /**
     * 单位工程 - 费用查看
     * @type {string}
     */
    static UNIT_FYCK = "UNIT_FYCK";

    /**
     *    人材机汇总 key
     * @type {string}
     */
    static RCJ_COLLECT = "RCJ_COLLECT";
    /**
     *    人材机汇总 单位菜单 key
     * @type {string}
     */
    static UNIT_MENU = "UNIT_MENU-";
    /**
     *    人材机汇总 工程项目菜单 key
     * @type {string}
     */
    static PROJECT_MENU = "PROJECT_MENU";
    /**
     *    人材机汇总 单项工程菜单 key
     * @type {string}
     */
    static SINGLE_MENU = "SINGLE_MENU";

    /**
     * 暂估人材机汇总 key
     * @type {string}
     */
    static ZG_RCJ_COLLECT = "ZG_RCJ_COLLECT";

    /**
     *  分隔符
     * @type {string}
     */
    static SEPARATOR = "-";

    /**
     *   工程项目数据排序
     * @type {string}
     */
    static PROJECT_DATA_SORT = "PROJECT_DATA_SORT";
    /**
     *   单项数据排序
     * @type {string}
     */
    static SINGLE_DATA_SORT = "SINGLE_DATA_SORT-";
    /**
     *   单位数据排序
     * @type {string}
     */
    static UNIT_DATA_SORT = "UNIT_DATA_SORT-";

    /**
     *  单位工程颜色
     */
    static UNIT_COLOR = "UNIT_COLOR";

    /**
     *  工程基本信息
     */
    static JBXX_KEY = "PROJECT_JBXX";
    /**
     *  工程基本信息-基本
     */
    static JBXX_KEY_TYPE_11 = "11";
    /**
     *  工程基本信息-编制说明
     */
    static JBXX_KEY_TYPE_12 = "12";
    /**
     *  工程基本信息-特征
     */
    static JBXX_KEY_TYPE_13 = "13";

    /**
     *  设备购置费信息-国内
     */
    static SBGZF_KEY_TYPE_GN = "sbgzf00";

    /**
     *  设备购置费信息-国外
     */
    static SBGZF_KEY_TYPE_GW = "sbgzf01";

    /**
     *  设备购置费信息-汇总
     */
    static SBGZF_KEY_TYPE_HZ = "sbgzf02";

    /**
     *  设备购置费信息-汇总金额
     */
    static SBGZF_KEY_TYPE_HZ_SBF = "SBF";

    /**
     *  设备购置费信息-设备进口计算器
     */
    static SBGZF_KEY_TYPE_HZ_JSQ = "JSQ";
    
    /**
     *  单位独立费
     */
    static UNIT_DLF_KEY = "UNIT_DLF";

    /**
     *    人材机汇总 载价 单位
     * @type {string}
     */
    static UNIT_LOAD_PRICE = "UNIT_LOAD_PRICE-";

    /**
     *    人材机汇总 载价 单项
     * @type {string}
     */
    static SINGLE_LOAD_PRICE = "SINGLE_LOAD_PRICE-";

    /**
     *  表格列设置
     */
    static UNIT_BGLSZ = "UNIT_BGLSZ";

    /**
     *    人材机汇总 载价 工程项目
     * @type {string}
     */
    static PROJECT_LOAD_PRICE = "PROJECT_LOAD_PRICE";
    /**
     * 用户定额
     * @type {string}
     */
    static PROJECT_USER_DE = "PROJECT_USER_DE";
    /**
     *   用戶人材機
     * @type {string}
     */
    static PROJECT_USER_RCJ = "PROJECT_USER_RCJ";

    /**
     * 安装费用缓存
     * @type {string}
     */
    static PROJECT_AZ_CACHE = "PROJECT_USER_AZ_CACHE";

    /**
     * 安装费用缓存-预算定额
     * @type {string}
     */
    static PROJECT_AZ_CACHE_YS = "PROJECT_USER_AZ_CACHE_YS";

    /**
     *  工程项目临时数据
     * @type {string}
     */
    static TEMPORARY_DATA = "TEMPORARY_DATA";

    /**
     *    人材机表格列设置
     * @type {string}
     */
    static RCJ_TABLELIST = "RCJ_TABLELIST-";

    /**
     *    预算书表格列设置
     * @type {string}
     */
    static YSH_TABLELIST = "YSH_TABLELIST-";
    /**
     *    措施项目表格列设置
     * @type {string}
     */
    static CSXM_TABLELIST = "CSXM_TABLELIST-";
    /**
         *    预算书表格列设置
         * @type {string}
         */
    static YSH_GCL_EXP_NOTIFY = "YSH_GCL_EXP_NOTIFY";
    /**
     * 单位独立费
     */
    static DLF_TABLELIST = "DLF_TABLELIST-";

    /**
     * 单位三材
     */
    static SC_TABLELIST = "SC_TABLELIST-";

    /**
     * 费用汇总
     */
    static FYHZ_TABLELIST = "FYHZ_TABLELIST-";

    /**
     *    预算书整理子目配置
     * @type {string}
     */
    static YSH_TABLELIST_ARRANGE = "YSH_TABLELIST-ARRANGE-";

    /**
     * 工程项目设置
     * @type {string}
     */
    static PROJECT_SETTING = "PROJECT_SETTING";

    /**
     * 人材机缓存
     * @type {string}
     */
    static RCJ_MEMORY = "RCJ_MEMORY";
    /**
     * 单位 - 人材机缓存
     * @type {string}
     */
    static UNIT_RCJ_MEMORY = "UNIT_RCJ_MEMORY-";
    /**
     *  定额主材设备
     * @type {string}
     */
    static UNIT_DE_MAINMATERIAL = "UNIT_DE_MAINMATERIAL-";

    /**
     * 单位 - 垂直运输费的地上、地下工程量表达式值、装饰超高工程量表达式值、泵送增加费工程量表达式的值 存储
     */
    static UNIT_COST_MATCH_VALUE = 'UNIT_COST_MATCH_VALUE';

    /**
     * 单位 - 垂直运输费记取缓存
     */
    static UNIT_CZYS_COST_MATCH_CACHE = 'UNIT_CZYS_COST_MATCH_CACHE';

    /**
     * 单位 - 装饰超高费记取缓存
     */
    static UNIT_ZSCG_COST_MATCH_CACHE = 'UNIT_ZSCG_COST_MATCH_CACHE';

    /**
     * 单位 - 安装费记取缓存
     */
    static UNIT_AZ_COST_MATCH_CACHE = 'UNIT_AZ_COST_MATCH_CACHE';

    /**
     * 单位 - 泵送增加费记取缓存
     */
    static UNIT_BSZJ_COST_MATCH_CACHE = 'UNIT_BSZJ_COST_MATCH_CACHE';

    /**
     * 单位 - 房修土建费记取缓存
     */
    static UNIT_FXTJ_COST_MATCH_CACHE = 'UNIT_FXTJ_COST_MATCH_CACHE';

    /**
     * 单位 - 仿古记取缓存
     */
    static UNIT_FGJZ_COST_MATCH_CACHE = 'UNIT_FGJZ_COST_MATCH_CACHE';
    /**
     * 单位 - 古建明清记取缓存
     */
    static UNIT_GJMQ_COST_MATCH_CACHE = 'UNIT_GJMQ_COST_MATCH_CACHE';

    /**
     * 单位 - 总价措施记取缓存
     */
    static UNIT_ZJCS_COST_MATCH_CACHE = 'UNIT_ZJCS_COST_MATCH_CACHE';
    /**
     * 是否有定额未参与总价措施记取
     */
    static UNIT_NO_PARTICIPATION_ZJCS_MATCH = 'UNIT_NO_PARTICIPATION_ZJCS_MATCH';

    /**
     *    单位直接分摊
     * @type {string}
     */
    static RCJ_SHARE_COST = "RCJ_SHARE_COST";

    /**
     * 人材机汇总-主要材料设置
     */
    static MAIN_MATERIAL_SETTING = "MAIN_MATERIAL_SETTING-";

    /**
     * 所有页签缓存
     */
    static TABLE_SETTING_CACHE = "TABLE_SETTING_CACHE";

    /**
     * 定额操作缓存
     */
    static DE_SETTING_CACHE = "DE_SETTING_CACHE";

    /**
     * 单位 - 水电费数据
     */
    static UNIT_WATER_ELECTRIC_COST_DATA = 'UNIT_WATER_ELECTRIC_COST_DATA';

    /**
     * 精度设置
     */
    static PROJECT_PRECISION_SETTING = 'PROJECT_PRECISION_SETTING';

    /**
     * 定额费用代码
     */
    static DE_COST_CODE = 'DE_COST_CODE';
    /**
     *  统一调整系数 市场价
     */
    static RCJ_COEFFICIENT_LOCK_PRICE = 'RCJ_COEFFICIENT_LOCK_PRICE';
    static RCJ_COEFFICIENT_LOCK_PRICE_VALUE = 'RCJ_COEFFICIENT_LOCK_PRICE_VALUE';
    /**
     * 统一调整系数 市场价 量
     */
    static RCJ_COEFFICIENT_LOCK_QTY = 'RCJ_COEFFICIENT_LOCK_QTY';
    static RCJ_COEFFICIENT_LOCK_QTY_VALUE = 'RCJ_COEFFICIENT_LOCK_QTY_VALUE';


}
FunctionTypeConstants.toString = () => 'FunctionTypeConstants';
module.exports = FunctionTypeConstants;