const {Service} = require('../../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const gsFreeViewProject = require("../jsonData/gs_free_view_project.json");
const gsFreeViewUnit = require("../jsonData/gs_free_view_unit.json");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {FreeViewItemModel} = require("../models/FreeViewItemModel");
const {FreeViewModel} = require("../models/FreeViewModel");
const WildcardMap = require("../core/container/WildcardMap");
const {NumberUtil} = require("../utils/NumberUtil");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");

/**
 * 费用查看 service
 * @class
 */
class GsFreeViewService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 工程项目-费用查看
     * @returns {Promise<string>}
     */
    async freeViewProject(args) {
        let {constructId, type} = args;

        await this.initFreeView(constructId, type);
        let freeViewModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FYCK);
        let freeViewModelMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FYCK);
        if (ObjectUtils.isEmpty(freeViewModel)) {
            freeViewModel = new FreeViewModel();
            let gsFreeViewList = [];
            for (let item of gsFreeViewProject) {
                let freeViewItem = new FreeViewItemModel()
                gsFreeViewList.push(ObjectUtils.copyProp(item, freeViewItem));
                freeViewItem.value = 0;
            }
            freeViewModel.constructId = constructId;
            freeViewModel.type = type;
            freeViewModel.freeViewItemList = gsFreeViewList;
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_FYCK, freeViewModel);
        }
        if (ObjectUtils.isEmpty(freeViewModelMap)) {
            return freeViewModel;
        }

        let freeKey = WildcardMap.generateKey(constructId);
        let freeViewModelListMap = ObjectUtils.getMapWithKeysStartingWith(freeViewModelMap, freeKey);

        await this.doAddFree(freeViewModel, freeViewModelListMap);
        return freeViewModel;
    }

    /**
     * 工程项目-费用查看
     * @returns {Promise<string>}
     */
    async freeViewSingle(args) {
        let {constructId, singleId, type} = args;

        await this.initFreeView(constructId, type);
        let freeViewSingleMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FYCK);
        let freeViewModelMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FYCK);
        if (ObjectUtils.isEmpty(freeViewSingleMap)) {
            freeViewSingleMap = new Map();
        }
        let freeKey = WildcardMap.generateKey(constructId, singleId);
        let freeViewModel = freeViewSingleMap.get(freeKey);
        if (ObjectUtils.isEmpty(freeViewModel)) {
            freeViewModel = new FreeViewModel();
            let gsFreeViewList = [];
            for (let item of gsFreeViewUnit) {
                let freeViewItem = new FreeViewItemModel()
                gsFreeViewList.push(ObjectUtils.copyProp(item, freeViewItem));
                freeViewItem.value = 0;
            }
            freeViewModel.constructId = constructId;
            freeViewModel.type = type;
            freeViewModel.freeViewItemList = gsFreeViewList;
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_FYCK, freeViewSingleMap.set(freeKey, freeViewModel));
        }
        if (ObjectUtils.isEmpty(freeViewModelMap)) {
            return freeViewModel;
        }
        let {singleList,unitList } = ProjectDomain.getDomain(constructId).getSubProjects(singleId);
        let freeViewModelListMap = new Map();
        for (let singleId of singleList) {
            let singleKey = WildcardMap.generateKey(constructId, singleId);
            let freeViewUnitMap = ObjectUtils.getMapWithKeysStartingWith(freeViewModelMap, singleKey);
            for (const [key, value] of freeViewUnitMap) {
                freeViewModelListMap.set(key, value);
            }
        }
        await this.doAddFree(freeViewModel, freeViewModelListMap);
        return freeViewModel;
    }

    /**
     * 费用查看统一初始化
     * @param constructId
     * @param type
     * @returns {Promise<void>}
     */
    async initFreeView(constructId, type) {
        let unitList = await this.service.PreliminaryEstimate.gsProjectCommonService.getProjectUnitAll(constructId);
        for (let unit of unitList) {
            let args = {
                constructId: constructId,
                singleId: unit.parentId,
                unitId: unit.sequenceNbr,
                type: type
            }
            await this.freeViewUnit(args);
        }
    }


    /**
     * 累加
     * @param freeView
     * @param unitFreeViewMap
     * @returns {Promise<void>}
     */
    async doAddFree(freeView, unitFreeViewMap) {
        let addFreeViewItemList = freeView.freeViewItemList;
        for (let item of addFreeViewItemList) {
            item.value = 0;
            // 遍历原始map
            for (let [key, freeViewModel] of unitFreeViewMap) {
                let unitFreeViewItemList = freeViewModel.freeViewItemList;
                let list = unitFreeViewItemList.filter(item2=>item2.name === item.name);
                if (ObjectUtils.isNotEmpty(list)) {
                    item.value = NumberUtil.accAdd(item.value, list[0].value);
                }
            }
        }
        return freeView;
    }

    /**
     * 单位工程-费用查看
     * @returns {Promise<string>}
     */
    async freeViewUnit(args) {
        let {constructId, singleId, unitId, type} = args;
        let unitProject = await this.service.PreliminaryEstimate.gsProjectCommonService.getUnit(constructId, unitId);

        let freeViewModelMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FYCK);
        if (ObjectUtils.isEmpty(freeViewModelMap)) {
            freeViewModelMap = new Map();
        }
        let freeKey = WildcardMap.generateKey(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(freeViewModelMap.get(freeKey))) {
            let gsFreeViewList = [];
            for (let item of gsFreeViewUnit) {
                let freeViewItem = new FreeViewItemModel()
                gsFreeViewList.push(ObjectUtils.copyProp(item, freeViewItem));
            }
            const freeViewModel = new FreeViewModel();
            freeViewModel.constructId = constructId;
            freeViewModel.singleId = singleId;
            freeViewModel.unitId = unitId;
            freeViewModel.type = type;
            freeViewModel.freeViewItemList = gsFreeViewList;
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_FYCK, freeViewModelMap.set(freeKey, freeViewModel));
        }
        let freeViewModelResult = freeViewModelMap.get(freeKey);
        let freeJz = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ);
        let freeAz = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ);
        freeJz = freeJz? freeJz:[]
        freeAz = freeAz? freeAz:[]
        let free = freeJz.concat(freeAz)
        if (ObjectUtils.isNotEmpty(free)) {
            for (const obj of freeViewModelResult.freeViewItemList) {
                if (obj.name === 'zzj') { //总造价
                    obj.value = free.filter(item => item.name === "工程造价").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'zjf') { //直接费
                    obj.value = free.filter(item => item.name === "直接费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'sbf') { //设备费
                    obj.value = free.filter(item => item.name === "设备费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'qyglf') { //企业管理费
                    obj.value = free.filter(item => item.name === "企业管理费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'gf') { //规费
                    obj.value = free.filter(item => item.name === "规费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'lr') { //利润
                    obj.value = free.filter(item => item.name === "利润").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'rcjhjjc') { //人材机价差合计
                    let unitCostCodePricesJz = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                        .get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZ);
                    let unitCostCodePricesAz = await ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
                        .get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZ);
                    unitCostCodePricesJz = unitCostCodePricesJz? unitCostCodePricesJz:[]
                    unitCostCodePricesAz = unitCostCodePricesAz? unitCostCodePricesAz:[]
                    let unitCostCodePrices = unitCostCodePricesJz.concat(unitCostCodePricesAz)
                    obj.value = unitCostCodePrices.filter(item => item.code === "RCJJC").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'aqwmsgf') { //安全生产、文明施工费
                    obj.value = free.filter(item => item.name === "安全生产、文明施工费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'dlf') { //独立费
                    obj.value = free.filter(item => item.name === "独立费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'sj') { //税金
                    obj.value = free.filter(item => item.name === "税金").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'rgf') { //人工费
                    obj.value = free.filter(item => item.name === "人工费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'clf') { //材料费
                    obj.value = free.filter(item => item.name === "材料费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'jxf') { //机械费
                    obj.value = free.filter(item => item.name === "机械费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
                if (obj.name === 'zcf') { //主材费
                    obj.value = free.filter(item => item.name === "主材费").reduce((sum, item) => NumberUtil.accAdd(sum, item.price), 0);
                }
            }
        }

        return freeViewModelResult;
    }

    /**
     * 工程项目-费用查看
     * @returns {Promise<string>}
     */
    async editFreeViewProject(freeViewModel) {
        ProjectDomain.getDomain(freeViewModel.constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_FYCK, freeViewModel);
        return freeViewModel;
    }

    /**
     * 工程项目-费用查看
     * @returns {Promise<string>}
     */
    async editFreeViewSingle(freeViewModel) {
        let freeViewModelMap = ProjectDomain.getDomain(freeViewModel.constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FYCK);
        let freeKey = WildcardMap.generateKey(freeViewModel.constructId, freeViewModel.singleId);
        ProjectDomain.getDomain(freeViewModel.constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_FYCK, freeViewModelMap.set(freeKey, freeViewModel));
        return freeViewModel;
    }

    /**
     * 单位工程-费用查看
     * @returns {Promise<string>}
     */
    async editFreeViewUnit(freeViewModel) {
        let freeViewModelMap = ProjectDomain.getDomain(freeViewModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FYCK);
        let freeKey = WildcardMap.generateKey(freeViewModel.constructId, freeViewModel.singleId, freeViewModel.unitId);
        ProjectDomain.getDomain(freeViewModel.constructId).functionDataMap.set(FunctionTypeConstants.UNIT_FYCK, freeViewModelMap.set(freeKey, freeViewModel));
        return freeViewModel;
    }

    /**
     * 导入项目后费用查看恢复
     * @param freeViewModel
     */
    transJson2Fyck(freeViewModel) {
        freeViewModel = ObjectUtils.copyProp(freeViewModel, new FreeViewModel());
        let arr = [];
        for (let item of freeViewModel.freeViewItemList) {
            arr.push(ObjectUtils.copyProp(item, new FreeViewItemModel()))
        }
        freeViewModel.freeViewItemList = arr;
        return freeViewModel;
    }

    /**
     * 导入项目后费用查看恢复
     * @param freeViewModelMap
     */
    transUnitJson2Fyck(freeViewModelMap) {
        freeViewModelMap = ObjectUtils.convertObjectToMap(freeViewModelMap);
        for (let [key, value] of freeViewModelMap) {
            freeViewModelMap.set(key, this.transJson2Fyck(value))
        }
        return freeViewModelMap;
    }



}

GsFreeViewService.toString = () => '[class GsFreeViewService]';
module.exports = GsFreeViewService;
